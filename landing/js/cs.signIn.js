document.addEventListener('DOMContentLoaded', function (event) {
    'use strict';
    const user_email = document.getElementById('user-email');
    const user_password = document.getElementById('login-password');
    const authorizationCode = document.getElementById('authorization-code');
    const authorizationCodeWrapper = document.querySelector('.authorization-code-wrapper');
    const signInBtn = document.getElementById('btnSignIn');
    const errorMessage = document.getElementById('errorLogIn');
    const signInFacebook = document.getElementById('signInFacebook');
    const signInGoogle = document.getElementById('signInGoogle');
    const signInSAML = document.getElementById('signInSAML');
    const useEmailElement = document.querySelector('.use-email-message');
    const forgotPassword = document.getElementById('forgotPassword');
    const closeForgotPasswordModal = document.getElementById('hideForgotPasswordModal');
    const forgotPasswordModal = document.getElementById('forgot-password-modal');
    const sendResetPassword = document.getElementById('sendResetPassword');
    const emailForResetPassword = document.getElementById('emailForResetPassword');
    const errorRessetPassword = document.getElementById('errorRessetPassword');
    const forgotPasswordModalFirst = document.getElementById('forgot-password-modal-first');
    const forgotPasswordModalSecond = document.getElementById('forgot-password-modal-second');
    const forgotPasswordFooter = document.querySelector('.forgot-password-modal-footer');
    const errorMessageWrapper = document.querySelector('.errorMessageWrapper');
    const passwordWarapper = document.querySelector('.form__group.password-wrapper');

    // forgot auth code modal
    const forgotAuthCode = document.getElementById('forgotAuthCode');
    const forgotAuthCodeModal = document.getElementById('forgot-authCode-modal');
    const closeForgotAuthCodeModal = document.getElementById('close-forgot-authCode-modal');
    //
    const acceptResetPassword = document.getElementById('acceptResetPassword');
    const ieModal = document.getElementById('ie-modal');
    const closeIeModal = document.getElementById('close-ie-modal');
    let language = document.documentElement.lang;
    if (language === 'uk') language = 'ua';
    const facebookAppId = '305967169564826';
    let tfaCode = null;
    let socialEmail = null;
    let socialCode = null;
    let isVisibleAuthorizationCodeInput = false;
    let isVisibleAuthorizationCodeInputForFacebook = false;
    let isVisibleAuthorizationCodeInputForGoogle = false;
    let usingSAMLLogin = false;
    const apiKey = {
        facebook: { appId: facebookAppId },
        linkedIn: { api_key: '75zhzlvnn4p5on' },
        vk: { apiId: 4592828 },
        google: {
            client_id: '************-eo4qmmi7o6hii0ckmrc004lhkh9m3596.apps.googleusercontent.com',
        },
        outlook: { client_id: '********-454c-4fb7-8ef7-c799b62005c3' },
    };
    const google_url =
        'https://accounts.google.com/o/oauth2/auth' +
        '?client_id=' +
        apiKey.google.client_id +
        '&scope=email%20profile' +
        '&state=/profile' +
        '&redirect_uri=' +
        location.protocol +
        '//' +
        location.hostname +
        '/white.html' +
        '&response_type=code%20token';

    if (window.location.href.includes('userMustLogIn')) {
        let message = '';

        switch (language) {
            case 'en':
                message = 'Log in using the login to which the invitation came, and follow the invitation link again';
                break;
            case 'ru':
                message =
                    'Авторизуйтесь, используя логин, на который пришло приглашение, и перейдите по ссылке приглашения ещё раз';
                break;
            case 'ro':
                message =
                    'Conectați-vă folosind datele de autentificare la care a fost trimisă invitația și faceți clic din nou pe link-ul invitației';
                break;
            case 'pl':
                message =
                    'Zaloguj się przy użyciu loginu, na który otrzymałeś zaproszenie, i ponownie kliknij łącze zaproszenia';
                break;
            default:
                message =
                    'Авторизуйтесь, використовуючи логін, на який прийшло запрошення, і перейдіть за посиланням запрошення ще раз';
        }

        let template = `
            <div class="userMustLogIn-wrapper" style="display: block">
                <span><b>${message}</b></span>
            </div>
        `;
        errorMessageWrapper.insertAdjacentHTML('beforeBegin', template);
    }

    (function checkSAMLDirectLink() {
        const urlParams = new URLSearchParams(window.location.search);
        if (
            urlParams.has('saml') &&
            urlParams.get('saml') === 'true' &&
            urlParams.has('orgId') &&
            urlParams.get('orgId') !== ''
        ) {
            window.location.href = `/RecrutSales/saml/login?orgId=${urlParams.get('orgId')}&lang=${language}`;
        } else if (urlParams.has('errorMessage') && urlParams.get('errorMessage') !== '') {
            const errorMsg = urlParams.get('errorMessage');
            showMainError(errorMsg, errorMsg, errorMsg, errorMsg, errorMsg);
        }
    })();

    //listeners
    user_email.addEventListener('focus', () => {
        user_email.style.borderColor = '#47ab43';
        user_email.style.backgroundColor = '#fff';
        errorMessage.style.opacity = '0';
        errorMessageWrapper.style.display = 'none';
    });
    user_password.addEventListener('focus', () => {
        user_password.style.borderColor = '#47ab43';
        errorMessage.style.opacity = '0';
        errorMessageWrapper.style.display = 'none';
        user_password.style.backgroundColor = '#fff';
    });
    user_email.addEventListener('blur', () => {
        if (!user_email.value.trim() || !emailValidation(user_email.value)) {
            user_email.style.borderColor = '#e75a3a';
            user_email.style.backgroundColor = '#faded8';
        } else {
            user_email.style.backgroundColor = '#fff';
            user_email.style.borderColor = '#d6e0e7';
        }
    });
    user_password.addEventListener('blur', () => {
        if (!user_password.value.trim() || !passwordValidation(user_password.value)) {
            user_password.style.borderColor = '#e75a3a';
            user_password.style.backgroundColor = '#faded8';
        } else {
            user_password.style.backgroundColor = '#fff';
            user_password.style.borderColor = '#d6e0e7';
        }
    });

    authorizationCode.addEventListener('focus', () => {
        authorizationCode.style.borderColor = '#47ab43';
        errorMessage.style.opacity = '0';
        errorMessageWrapper.style.display = 'none';
        authorizationCode.style.backgroundColor = '#fff';
    });
    authorizationCode.addEventListener('blur', () => {
        if (authorizationCode && !authorizationCode.value.trim()) {
            authorizationCode.style.borderColor = '#e75a3a';
            authorizationCode.style.backgroundColor = '#faded8';
        } else {
            authorizationCode.style.backgroundColor = '#fff';
            authorizationCode.style.borderColor = '#d6e0e7';
        }
    });

    signInBtn.addEventListener('click', emailPasswordSignInHandler);
    signInFacebook.addEventListener('click', () => facebookSignIn());
    signInGoogle.addEventListener('click', () => googleSignIn());
    signInSAML.addEventListener('click', (e) => {
        e.preventDefault();

        usingSAMLLogin = true;

        errorMessage.style.opacity = '0';
        errorMessageWrapper.style.display = 'none';
        errorMessage.innerText = '';

        useEmailElement.style.display = 'none';
        signInFacebook.style.display = 'none';
        signInGoogle.style.display = 'none';
        passwordWarapper.style.display = 'none';
    });
    forgotPassword.addEventListener('click', () => showForgotPasswordModal());
    closeForgotPasswordModal.addEventListener('click', () => hideForgotPasswordModal());
    sendResetPassword.addEventListener('click', () => resetPassword());
    emailForResetPassword.addEventListener('input', () => emailResetValidation());
    emailForResetPassword.addEventListener('focus', () => focusEmailResetPassword());
    emailForResetPassword.addEventListener('blur', () => blurEmailResetPassword());
    acceptResetPassword.addEventListener('click', () => hideForgotPasswordModal());
    closeIeModal.addEventListener('click', () => hideIeModal());
    authorizationCode.addEventListener('input', validateNumberInput);
    forgotAuthCode.addEventListener('click', () => showForgotAuthCodeModal());
    closeForgotAuthCodeModal.addEventListener('click', () => hideForgotAuthCodeModal());

    //functions

    function emailPasswordSignIn() {
        tfaCode = authorizationCode && authorizationCode.value;
        if (
            user_email.value.trim() &&
            emailValidation(user_email.value) &&
            user_password.value.trim() &&
            passwordValidation(user_password.value)
        ) {
            if (tfaCode) {
                logIn({ tfaCode });
            } else if (!tfaCode && isVisibleAuthorizationCodeInput) {
                showMainError(
                    'Input authorization code.',
                    'Введите код авторизации.',
                    'Introduceți codul de autorizare.',
                    'Введіть код авторизації.',
                    'Wprowadź kod autoryzacyjny.',
                );
            } else {
                logIn({ tfaCode });
            }
        } else {
            showMainError(
                'Enter correct login and password, please.',
                'Введите корректный логин и пароль, пожалуйста.',
                'Vă rugăm să introduceți numele de utilizator și parola corecte.',
                'Введіть коректний логін і пароль, будь ласка.',
                'Podaj poprawny login i hasło.',
            );
        }
    }

    function logIn(extraData) {
        const payload = extraData
            ? {
                  ...extraData,
                  login: user_email.value,
                  password: user_password.value,
                  lang: language === 'ro' ? 'en' : language,
                  timeZoneOffset: new Date().getTimezoneOffset(),
              }
            : {
                  login: user_email.value,
                  password: user_password.value,
                  lang: language === 'ro' ? 'en' : language,
                  timeZoneOffset: new Date().getTimezoneOffset(),
              };

        postData('/hr/person/auth', 'POST', payload)
            .then((resp) => {
                if (resp.status === 'ok') {
                    successLogIn(resp);
                } else if (resp.code === 'tfaCodeIsEmpty') {
                    showAuthorizationCodeInput();
                } else if (resp.code === 'tfaVerificationError') {
                    errorMessage.style.opacity = '1';
                    authorizationCode.style.borderColor = '#e75a3a';
                    errorMessage.innerText =
                        language === 'en'
                            ? 'Invalid authorization code.'
                            : language === 'ru'
                            ? 'Неправильный код авторизации.'
                            : language === 'ro'
                            ? 'Cod de autorizare incorect.'
                            : language === 'pl'
                            ? 'Nieprawidłowy kod autoryzacji.'
                            : 'Неправильний код авторизації.';
                } else {
                    catchError(resp);
                }
            })
            .catch((error) => {
                errorFieldsStyles();
                console.error(error);
            });
    }

    function showAuthorizationCodeInput() {
        isVisibleAuthorizationCodeInput = true;
        authorizationCodeWrapper.style.display = 'block';
    }

    function showAuthorizationCodeInputForGoogle() {
        hideEmailPasswordFields();

        isVisibleAuthorizationCodeInputForGoogle = true;
        authorizationCodeWrapper.style.display = 'block';

        signInBtn.removeEventListener('click', emailPasswordSignInHandler);
        signInBtn.removeEventListener('click', facebookSignInHandler);
        signInBtn.addEventListener('click', googleSignInHandler);
    }

    function showAuthorizationCodeInputForFacebook() {
        hideEmailPasswordFields();

        isVisibleAuthorizationCodeInputForFacebook = true;
        authorizationCodeWrapper.style.display = 'block';

        signInBtn.removeEventListener('click', emailPasswordSignInHandler);
        signInBtn.removeEventListener('click', googleSignInHandler);
        signInBtn.addEventListener('click', facebookSignInHandler);
    }

    function hideEmailPasswordFields() {
        let loginFieldsWrapper = document.querySelector('.login-fields-wrapper');

        if (loginFieldsWrapper) loginFieldsWrapper.style.display = 'none';

        showMainError('', '', '', '', '');
    }

    function validateNumberInput(event) {
        event.target.value = event.target.value.replace(/\D/g, '').slice(0, 6);
    }

    function showMainError(enText, ruText, roText, uaText, plText) {
        errorMessage.style.opacity = '1';
        errorMessageWrapper.style.display = 'block';
        errorMessage.style.color = '#e75a3a';
        errorMessage.innerText =
            language === 'en'
                ? enText
                : language === 'ru'
                ? ruText
                : language === 'ro'
                ? roText
                : language === 'pl'
                ? plText
                : uaText;
    }

    function successLogIn(resp) {
        if (resp.object && resp.object.personId !== undefined) {
            const initPage = resp.object.personParams.organizerTab;
            localStorage.otherSessionsRemoves = resp.object ? resp.object.otherSessionsRemoves : false;
            if (!isIE()) {
                if (resp.recrutRole == 'client') {
                    window.location.replace('/!#/vacancies');
                } else {
                    if (resp.object.recrutRole === 'admin' || resp.object.recrutRole === 'recruter') {
                        if (initPage === 'dashboard') {
                            window.location.replace('/!#/organizer/dashboard');
                        } else if (initPage === 'events') {
                            window.location.replace('/!#/organizer');
                        } else {
                            window.location.replace('/!#/organizer/dashboard');
                        }
                    } else {
                        window.location.replace('/!#/organizer');
                    }
                }

                successFieldsStyles();
            } else {
                showIeModal();
            }
        } else catchError(resp);
    }

    function catchError(resp) {
        if (resp.status === 'error' && resp.code === 'unknownEmail') {
            errorFieldsStyles();
            language === 'en'
                ? (errorMessage.innerText =
                      'You entered an invalid username or password. Please check your credentials and try again.')
                : language === 'ru'
                ? (errorMessage.innerText =
                      'Вы ввели неверный логин или пароль. Пожалуйста, проверьте свои учетные данные.')
                : language === 'ro'
                ? (errorMessage.innerText =
                      'Ați introdus un nume de utilizator sau o parolă incorectă. Vă rugăm să verificați datele dvs. de identificare')
                : language === 'pl'
                ? (errorMessage.innerText = 'Wpisałeś nieprawidłowy login lub hasło. Sprawdź dane swojego konta.')
                : (errorMessage.innerText =
                      'Ви ввели неправильний логін або пароль. Будь ласка, перевірте дані свого облікового запису.');
        } else if (resp.status === 'error' && resp.code === 'registrationIsNotConfirmed') {
            errorFieldsStyles();
            language === 'en'
                ? (errorMessage.innerText = 'You did not approve the registration')
                : language === 'ru'
                ? (errorMessage.innerText = 'Вы не подтвердили регистрацию')
                : language === 'ro'
                ? (errorMessage.innerText = 'Nu ați confirmat înregistrarea')
                : language === 'pl'
                ? (errorMessage.innerText = 'Nie potwierdziłeś rejestracji')
                : (errorMessage.innerText = 'Ви не підтвердили реєстрацію');
        } else if (resp.code === 'manyAuth') {
            errorFieldsStyles();
            postData(`/hr/person/getAuthBlockTime?login=${user_email.value}`, 'GET').then((resp) => {
                let minutes = '';
                if (resp.status === 'ok' && resp.object) {
                    if (language === 'ru') {
                        if (resp.object === 1) minutes = 'минуту';
                        else if (resp.object > 1 && resp.object < 5) minutes = 'минуты';
                        else minutes = 'минут';
                    } else if (language === 'ro') {
                        if (resp.object === 1) minutes = 'minută';
                        else if (resp.object > 1 && resp.object < 5) minutes = 'minute';
                        else minutes = 'minute';
                    } else if (language === 'ua') {
                        if (resp.object === 1) minutes = 'хвилину';
                        else if (resp.object > 1 && resp.object < 5) minutes = 'хвилини';
                        else minutes = 'хвилин';
                    } else if (language === 'pl') {
                        if (resp.object === 1) minutes = 'minutę';
                        else if (resp.object > 1 && resp.object < 5) minutes = 'minuty';
                        else minutes = 'minut';
                    } else {
                        if (resp.object === 1) {
                            minutes = 'minute';
                        } else minutes = 'minutes';
                    }
                    language === 'en'
                        ? (errorMessage.innerText = `You are trying to log in too often. It looks like you are trying to use the same account for different users. It contradicts our user agreement. Please try again in  ${resp.object}  ${minutes}.`)
                        : language === 'ru'
                        ? (errorMessage.innerText = `Вы пытаетесь войти в систему слишком часто. Похоже, вы пытаетесь использовать одну и ту же учетную запись для разных пользователей, что противоречит нашему пользовательскому соглашению. Повторите попытку через  ${resp.object}  ${minutes}`)
                        : language === 'ro'
                        ? (errorMessage.innerText = `Încercați să vă conectați de prea multe ori. Se pare că încercați să utilizați același cont pentru utilizatori diferiți, ceea ce contravine contractului nostru de utilizare. Încercați din nou prin  ${resp.object}  ${minutes}`)
                        : language === 'pl'
                        ? (errorMessage.innerText = `Zbyt często próbujesz się zalogować. Wygląda na to, że próbujesz użyć tego samego konta dla różnych użytkowników, co jest niezgodne z naszą umową użytkownika. Spróbuj ponownie ${resp.object}  ${minutes}`)
                        : (errorMessage.innerText = `Ви намагаєтесь увійти в систему дуже часто. Схоже, ви намагаєтесь використати той самий аккаунт для різних користувачів, що суперечить нашій користувацькій угоді. Спробуйте ще раз через  ${resp.object}  ${minutes}`);
                } else {
                    language === 'en'
                        ? (errorMessage.innerText = `You are trying to log in too often. It looks like you are trying to use the same account for different users. It contradicts our user agreement. Please try again in 5 minutes.`)
                        : language === 'ru'
                        ? (errorMessage.innerText = `Вы пытаетесь войти в систему слишком часто. Похоже, вы пытаетесь использовать одну и ту же учетную запись для разных пользователей, что противоречит нашему пользовательскому соглашению. Повторите попытку через 5 минут`)
                        : language === 'ro'
                        ? (errorMessage.innerText = `Încercați să vă conectați de prea multe ori. Se pare că încercați să utilizați același cont pentru utilizatori diferiți, ceea ce contravine contractului nostru de utilizare. Încercați din nou în 5 minute`)
                        : language === 'pl'
                        ? (errorMessage.innerText = `Zbyt często próbujesz się zalogować. Wygląda na to, że próbujesz używać tego samego konta dla różnych użytkowników, co jest niezgodne z naszą umową użytkownika. Spróbuj ponownie za 5 minut`)
                        : (errorMessage.innerText = `Ви намагаєтесь увійти в систему дуже часто. Схоже, ви намагаєтесь використати той самий аккаунт для різних користувачів, що суперечить нашій користувацькій угоді. Спробуйте ще раз через 5 хвилин`);
                }
            });
        } else if (resp.code === 'registrationOrInviteIsNotConfirmed') {
            errorFieldsStyles();
            language === 'en'
                ? (errorMessage.innerText = 'Confirm registration or accept the invitation to your account to log in')
                : language === 'ru'
                ? (errorMessage.innerText =
                      'Подтвердите регистрацию или примите приглашение в аккаунт, чтобы авторизоваться')
                : language === 'ro'
                ? (errorMessage.innerText =
                      'Confirmați înregistrarea sau acceptați o invitație la contul dvs. pentru a vă autoriza')
                : language === 'pl'
                ? (errorMessage.innerText =
                      'Potwierdź rejestrację lub zaakceptuj zaproszenie do konta, aby się zalogować')
                : (errorMessage.innerText =
                      'Підтвердіть реєстрацію або прийміть запрошення в аккаунт, щоб авторизуватися');
        } else if (resp.code === 'badCredentials' || resp.code === 'BadCredentials') {
            language === 'en'
                ? (errorMessage.innerText =
                      'You entered an invalid username or password. Please check your credentials and try again.')
                : language === 'ru'
                ? (errorMessage.innerText =
                      'Вы ввели неверный логин или пароль. Пожалуйста, проверьте свои учетные данные.')
                : language === 'ro'
                ? (errorMessage.innerText =
                      'Ați introdus un nume de utilizator sau o parolă incorectă. Vă rugăm să verificați datele dvs. de identificare')
                : language === 'pl'
                ? (errorMessage.innerText = 'Wpisałeś nieprawidłowy login lub hasło. Sprawdź dane swojego konta.')
                : (errorMessage.innerText =
                      'Ви ввели неправильний логін або пароль. Будь ласка, перевірте дані свого облікового запису.');
            errorMessage.style.opacity = '1';
            errorMessageWrapper.style.display = 'block';
            errorMessage.style.color = '#e75a3a';
        } else if (resp.code === 'GoogleError') {
            language === 'en'
                ? (errorMessage.innerText = 'Google access error. Try again, please.')
                : language === 'ru'
                ? (errorMessage.innerText = 'Ошибка доступа через Google. Пожалуйста, попробуйте ещё раз.')
                : language === 'ro'
                ? (errorMessage.innerText = 'Eroare de acces prin Google. Vă rugăm să încercați din nou.')
                : language === 'pl'
                ? (errorMessage.innerText = 'Błąd podczas uzyskiwania dostępu przez Google. Proszę spróbuj ponownie.')
                : (errorMessage.innerText = 'Помилка доступа через Google. Будь ласка, спробуйте ще раз.');
        } else if (resp.code === 'tfaVerificationError') {
            errorMessage.style.opacity = '1';
            errorMessageWrapper.style.display = 'block';
            authorizationCode.style.borderColor = '#e75a3a';
            errorMessage.innerText =
                language === 'en'
                    ? 'Invalid authorization code.'
                    : language === 'ru'
                    ? 'Неправильный код авторизации.'
                    : language === 'ro'
                    ? 'Cod de autorizare incorect.'
                    : language === 'pl'
                    ? 'Nieprawidłowy kod autoryzacji.'
                    : 'Неправильний код авторизації.';
        } else if (resp.code === 'onlySamlLogin') {
            errorFieldsStyles();
            errorMessage.innerText =
                language === 'en'
                    ? 'You must log in using SAML authentication'
                    : language === 'ru'
                    ? 'Вы должны войти с использованием SAML-аутентификации.'
                    : language === 'ro'
                    ? 'Trebuie să vă conectați utilizând autentificarea SAML.'
                    : language === 'pl'
                    ? 'Musisz zalogować się przy użyciu uwierzytelniania SAML.'
                    : 'Ви повинні увійти, використовуючи SAML-аутентифікацію.';
        } else {
            errorFieldsStyles();
            errorMessage.innerText = resp.message;
        }
    }

    function postData(url = '', type = '', data = null) {
        const request = {
            method: type,
            mode: 'cors',
            cache: 'no-cache',
            headers: {
                'Content-Type': 'application/json',
            },
        };
        if (data) request.body = JSON.stringify(data);
        return fetch(url, request).then((response) => response.json());
    }

    function isIE() {
        const ua = window.navigator.userAgent;
        const msie = ua.toLowerCase().indexOf('msie');
        const edge = ua.toLowerCase().indexOf('edge');
        return msie > 0 || edge > 0;
    }

    function errorFieldsStyles() {
        // user_email.style.borderColor = "#e75a3a";
        // user_password.style.borderColor = "#e75a3a";
        errorMessage.style.opacity = '1';
        errorMessageWrapper.style.display = 'block';
        errorMessage.style.color = '#e75a3a';
        // user_email.style.backgroundColor = "#faded8";
        // user_password.style.backgroundColor = "#faded8";
    }

    function successFieldsStyles() {
        user_email.style.borderColor = '#47ab43';
        user_password.style.borderColor = '#47ab43';
        errorMessage.style.opacity = '1';
        errorMessage.style.color = '#47ab43';
        errorMessageWrapper.style.display = 'block';
        language === 'en'
            ? (errorMessage.innerText = 'Redirect...')
            : language === 'ru'
            ? (errorMessage.innerText = 'Переадресация...')
            : language === 'ro'
            ? (errorMessage.innerText = 'Redirecționare...')
            : language === 'pl'
            ? (errorMessage.innerText = 'Przekierowanie...')
            : (errorMessage.innerText = 'Переадресація...');
        user_email.style.backgroundColor = '#fff';
    }

    function emailPasswordSignInHandler(e) {
        e.preventDefault();

        if (usingSAMLLogin) {
            SAMLSignIn();
        } else {
            emailPasswordSignIn();
        }
    }

    function SAMLSignIn() {
        if (user_email.value.trim() && emailValidation(user_email.value)) {
            fetch(`/hr/saml/getAuthOrgId?login=${user_email.value.trim()}`)
                .then((resp) => resp.json())
                .then((data) => {
                    if (data.status === 'error') {
                        errorFieldsStyles();

                        language === 'en'
                            ? (errorMessage.innerText = 'Enter correct login, please')
                            : language === 'ru'
                            ? (errorMessage.innerText = 'Введите корректный логин, пожалуйста')
                            : language === 'ro'
                            ? (errorMessage.innerText = 'Vă rugăm să introduceți datele de autentificare corecte')
                            : language === 'pl'
                            ? (errorMessage.innerText = 'Podaj poprawny login')
                            : (errorMessage.innerText = 'Введіть коректний логін, будь ласка');
                    } else {
                        const respOrgId = data.object;
                        window.location.href = `/RecrutSales/saml/login?orgId=${respOrgId}&lang=${language}`;
                    }
                });
        }
    }

    function facebookSignInHandler(e) {
        e.preventDefault();
        tfaCode = authorizationCode && authorizationCode.value;

        if (!tfaCode && isVisibleAuthorizationCodeInputForFacebook) {
            showMainError(
                'Input authorization code.',
                'Введите код авторизации.',
                'Introduceți codul de autorizare.',
                'Введіть код авторизації.',
                'Wprowadź kod autoryzacyjny.',
            );
        } else if (tfaCode && isVisibleAuthorizationCodeInputForFacebook) {
            loginToSystemFromGoogleOrFacebook('facebook');
        } else {
            facebookSignIn();
        }
    }

    function googleSignInHandler(e) {
        e.preventDefault();

        tfaCode = authorizationCode && authorizationCode.value;

        if (!tfaCode && isVisibleAuthorizationCodeInputForGoogle) {
            showMainError(
                'Input authorization code.',
                'Введите код авторизации.',
                'Introduceți codul de autorizare.',
                'Введіть код авторизації.',
                'Wprowadź kod autoryzacyjny.',
            );
        } else if (tfaCode && isVisibleAuthorizationCodeInputForGoogle) {
            loginToSystemFromGoogleOrFacebook('google');
        } else {
            googleSignIn();
        }
    }

    function facebookSignIn() {
        FB.login(
            function (response) {
                if (response.authResponse) {
                    const access_token = response.authResponse.accessToken;

                    FB.api('/me?fields=email', function (data) {
                        getUserInfoFacebook(access_token, data.email);
                    });
                } else {
                }
            },
            {
                scope: 'email',
            },
        );
    }

    function getUserInfoFacebook(code, email) {
        loginToSystemFromGoogleOrFacebook('facebook', code, email);
    }

    function googleSignIn() {
        const win = window.open(google_url, 'windowname1', getPopupParams());
        const pollTimer = window.setInterval(function () {
            try {
                if (win.document.URL.indexOf(gup(google_url, 'redirect_uri')) !== -1) {
                    window.clearInterval(pollTimer);
                    const url = win.document.URL;
                    const code = gup(url, 'code');
                    const access_token = gup(url, 'access_token');
                    if (access_token !== '') {
                        getUserInfoGoogle(access_token, code);
                    }
                    win.close();
                }
            } catch (e) {}
        }, 500);
    }

    function getUserInfoGoogle(token, code) {
        postData('https://www.googleapis.com/oauth2/v1/userinfo?access_token=' + token, 'GET')
            .then((resp) => {
                loginToSystemFromGoogleOrFacebook('google', code, resp.email);
            })
            .catch(() => {
                errorFieldsStyles();
                language === 'en'
                    ? (errorMessage.innerText = 'Google Services is unavailable')
                    : language === 'ru'
                    ? (errorMessage.innerText = 'Google Сервисы недоступны')
                    : language === 'ro'
                    ? (errorMessage.innerText = 'Serviciile Google nu sunt disponibile')
                    : language === 'pl'
                    ? (errorMessage.innerText = 'Usługi Google są niedostępne')
                    : 'Google Сервіси недоступні';
            });
    }

    function loginToSystemFromGoogleOrFacebook(social, code, email) {
        if (email) socialEmail = email;
        if (code) socialCode = code;

        const payload = {
            timeZoneOffset: new Date().getTimezoneOffset(),
            social: social,
            lang: language === 'ro' ? 'en' : language,
            socialKey: code || socialCode,
            login: email || socialEmail,
        };

        if (tfaCode) {
            payload.tfaCode = tfaCode;
        }

        postData('/hr/person/auth', 'POST', payload)
            .then((resp) => {
                localStorage.otherSessionsRemoves = resp.object ? resp.object.otherSessionsRemoves : false;
                if (resp.status === 'ok') {
                    successLogIn(resp);
                } else {
                    if (resp.code === 'tfaCodeIsEmpty') {
                        if (social === 'google') {
                            showAuthorizationCodeInputForGoogle();
                        } else if (social === 'facebook') {
                            showAuthorizationCodeInputForFacebook();
                        }
                    } else {
                        catchError(resp);
                        resetErrorFields();
                    }
                }
            })
            .catch((error) => {
                errorFieldsStyles();
                console.error(error);
            });
    }

    function emailValidation(email) {
        let regForValidation =
            /^[_]?[a-zA-Z0-9]+([\.\-_][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([\.\-_][a-zA-Z0-9]+)*\.[a-zA-Z]{2,10}$$/;
        return regForValidation.test(email);
    }

    function passwordValidation(pass) {
        return !(!pass || pass.length < 8 || pass.length > 50);
    }

    function getPopupParams() {
        const w = 650;
        const h = 550;
        const left = screen.width / 2 - w / 2;
        const top = screen.height / 2 - h / 2;
        return 'width=' + w + ', height=' + h + ', top=' + top + ', left=' + left;
    }

    function gup(url, name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        const regexS = '[\\?&]' + name + '=([^&#]*)';
        const regex = new RegExp(regexS);
        const results = regex.exec(url);
        if (results === null) return '';
        else return results[1];
    }

    function resetErrorFields() {
        user_password.style.borderColor = '#d6e0e7';
        user_email.style.borderColor = '#d6e0e7';
        user_email.style.backgroundColor = '#fff';
        user_password.style.backgroundColor = '#fff';
    }

    function showForgotPasswordModal() {
        let coverDiv = document.createElement('div');
        coverDiv.id = 'cover-div';
        document.body.style.overflowY = 'hidden';
        document.body.append(coverDiv);
        forgotPasswordModal.style.display = 'block';
    }

    function showForgotAuthCodeModal() {
        const coverDiv = document.createElement('div');
        coverDiv.id = 'cover-div';
        forgotAuthCodeModal.style.display = 'block';
        document.body.style.overflowY = 'hidden';
        document.body.append(coverDiv);
    }

    function showIeModal() {
        let coverDiv = document.createElement('div');
        coverDiv.id = 'cover-div';
        document.body.style.overflowY = 'hidden';
        document.body.append(coverDiv);
        ieModal.style.display = 'flex';
        emailResetValidation();
    }

    function hideIeModal() {
        document.body.removeChild(document.getElementById('cover-div'));
        document.body.style.overflowY = 'auto';
        ieModal.style.display = 'none';
    }

    function hideForgotPasswordModal() {
        forgotPasswordModal.style.display = 'none';
        forgotPasswordModal.style.height = '175px';
        document.body.removeChild(document.getElementById('cover-div'));
        document.body.style.overflowY = 'auto';
        emailForResetPassword.value = '';
        emailForResetPassword.style.borderColor = '#d6e0e7';
        errorRessetPassword.style.opacity = '0';
        forgotPasswordModalFirst.style.display = 'block';
        forgotPasswordModalSecond.style.display = 'none';
        sendResetPassword.disabled = true;
        emailForResetPassword.style.backgroundColor = '#eff2f7';
    }

    function hideForgotAuthCodeModal() {
        forgotAuthCodeModal.style.display = 'none';
        document.body.removeChild(document.getElementById('cover-div'));
        document.body.style.overflowY = 'auto';
    }

    function emailResetValidation() {
        if (
            emailForResetPassword.value &&
            emailForResetPassword.value.trim() &&
            emailValidation(emailForResetPassword.value)
        ) {
            sendResetPassword.disabled = false;
        } else {
            sendResetPassword.disabled = true;
        }
    }

    function focusEmailResetPassword() {
        emailForResetPassword.style.borderColor = '#47ab43';
        errorRessetPassword.style.opacity = '0';
        emailForResetPassword.style.backgroundColor = '#ffffff';
    }

    function blurEmailResetPassword() {
        if (
            !emailForResetPassword.value ||
            !emailForResetPassword.value.trim() ||
            !emailValidation(emailForResetPassword.value)
        ) {
            emailForResetPassword.style.borderColor = '#e75a3a';
            emailForResetPassword.style.backgroundColor = '#faded8';
            errorRessetPassword.style.opacity = '1';
            errorRessetPassword.innerText =
                language === 'en'
                    ? 'Enter correct email, please'
                    : language === 'ru'
                    ? 'Введите корректный email, пожалуйста'
                    : language === 'ro'
                    ? 'Vă rugăm să introduceți o adresă de e-mail validă'
                    : language === 'pl'
                    ? 'Proszę wpisać prawidłowy email'
                    : 'Введіть коректний email, будь ласка.';
        } else {
            emailForResetPassword.style.borderColor = '#d6e0e7';
            emailForResetPassword.style.backgroundColor = '#eff2f7';
        }
    }

    function resetPassword() {
        sendResetPassword.setAttribute('disabled', 'true');
        if (
            emailForResetPassword.value &&
            emailForResetPassword.value.trim() &&
            emailValidation(emailForResetPassword.value)
        ) {
            postData(`/hr/person/forgetPasswordRequest/${language === 'ro' ? 'en' : language}`, 'POST', {
                email: emailForResetPassword.value,
            })
                .then((resp) => {
                    sendResetPassword.setAttribute('disabled', 'false');
                    if (resp.status === 'ok') {
                        emailForResetPassword.value = '';
                        forgotPasswordModalFirst.style.display = 'none';
                        forgotPasswordModalSecond.style.display = 'block';
                    } else if (resp.status === 'forbidden') {
                        emailForResetPassword.style.borderColor = '#e75a3a';
                        emailForResetPassword.style.backgroundColor = '#faded8';
                        errorRessetPassword.style.opacity = '1';
                        errorRessetPassword.style.maxWidth = '380px';
                        forgotPasswordModal.style.height = '245px';
                        forgotPasswordFooter.style.backgroundColor = '#fff';
                        switch (language) {
                            case 'en':
                                errorRessetPassword.innerText =
                                    'You’ve reached a limit of 10 attempts to reset your password. If you did not receive an email with instructions to change your password, please check your Spam folder. You can contact our support team or try again tomorrow.';
                                break;
                            case 'ru':
                                errorRessetPassword.innerText =
                                    'Вы исчерпали лимит в 10 попыток сброса пароля. Если вы не получили письмо с инструкциями по смене пароля, проверьте папку Спам на вашей почте. Вы можете обратиться в нашу службу поддержки или попробовать еще раз завтра.';
                                break;
                            case 'ro':
                                errorRessetPassword.innerText =
                                    'Ați atins limita de 10 încercări de resetare a parolei. Dacă nu ați primit un e-mail cu instrucțiuni despre cum să vă resetați parola, vă rugăm să verificați dosarul Spam din e-mail. Puteți contacta echipa noastră de asistență sau puteți încerca din nou mâine.';
                                break;
                            case 'pl':
                                errorRessetPassword.innerText =
                                    'Osiągnąłeś limit 10 prób zresetowania hasła. Jeśli nie otrzymałeś e-maila z instrukcją zmiany hasła, sprawdź folder Spam. Możesz skontaktować się z naszym zespołem pomocy technicznej lub spróbować ponownie jutro.';
                                break;
                            case 'ua':
                                errorRessetPassword.innerText =
                                    'Ви досягли ліміту в 10 спроб скидання пароля. Якщо ви не отримали лист з інструкціями щодо зміни пароля, перевірте папку Спам у вашій пошті. Ви можете звернутись до нашої служби підтримки чи спробувати ще раз завтра.';
                        }
                    }
                })
                .catch((e) => {
                    console.error(e);
                    errorRessetPassword.style.opacity = '1';
                    language === 'en'
                        ? (errorRessetPassword.innerText = 'Service is unavailable')
                        : language === 'ru'
                        ? (errorRessetPassword.innerText = 'Сервис недоступен')
                        : language === 'ro'
                        ? (errorRessetPassword.innerText = 'Serviciul nu este disponibil')
                        : language === 'pl'
                        ? 'Usługa jest niedostępna'
                        : 'Сервіс недоступний';
                });
        } else {
            emailForResetPassword.style.borderColor = '#e75a3a';
            errorRessetPassword.style.opacity = '1';
            emailForResetPassword.style.backgroundColor = '#faded8';
        }
    }

    //dependencies
    window.fbAsyncInit = function () {
        FB.init({
            appId: apiKey.facebook.appId,
            oauth: true,
            status: true, // check login status
            cookie: true, // enable cookies to allow the server to access the session
            xfbml: true, // parse XFBML
            version: 'v2.9',
        });
    };
    (function (d, s, id) {
        let js,
            fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s);
        js.id = id;
        js.src = '//connect.facebook.net/en_US/sdk.js#xfbml=1&appId=305967169564826&version=v2.9';
        fjs.parentNode.insertBefore(js, fjs);
    })(document, 'script', 'facebook-jssdk');
});
