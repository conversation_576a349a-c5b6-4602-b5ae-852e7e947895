/*
    Updates local storage with current search params or adds them
 */
function updateSearchParamsLS() {
    let existingLSParams = new URLSearchParams();

    if (localStorage.getItem('UTMS')) {
        existingLSParams = new URLSearchParams(localStorage.getItem('UTMS'));
    }

    const currentSearchParams = new URLSearchParams(window.location.search);

    if (!currentSearchParams.size) return;

    if (existingLSParams.size && currentSearchParams.size) {
        for (const [key, value] of currentSearchParams) {
            // If value doesn't exist, add it
            if (!existingLSParams.has(key)) {
                existingLSParams.set(key, value);
            }
            // If value exists, update with new value
            if (existingLSParams.get(key) !== value) {
                existingLSParams.set(key, value);
            }
        }

        return localStorage.setItem('UTMS', existingLSParams.toString());
    }

    // If UTMS doesn't exist in local storage - add them
    localStorage.setItem('UTMS', currentSearchParams.toString());
}

function checkConsentAndSendUtms(event) {
    const permissions = JSON.parse(localStorage.getItem('consentMode'));

    if (!permissions || event.target.tagName !== 'BUTTON') return;

    saveUtmsRequest();
}

function saveUtmsRequest() {
    const DOMAIN = 'https://' + location.hostname + '/hr/';
    const utms = localStorage.getItem('UTMS');

    // Script for get cookie Google Analytics
    let match = document.cookie.match('(?:^|;)\\s*_ga=([^;]*)');
    const raw = match ? decodeURIComponent(match[1]) : null;
    if (raw) {
        match = raw.match(/(\d+\.\d+)$/);
    }
    const gacid = match ? match[1] : null;

    if (!utms || !gacid) return;

    void fetch(DOMAIN + 'public/saveUtms', {
        method: 'POST',
        headers: new Headers({ 'content-type': 'application/json; charset=utf-8' }),
        body: JSON.stringify({
            ga: gacid || null,
            utms,
        }),
    });
}

function onReady() {
    function hideBanner() {
        document.getElementById('cookie-consent-banner').style.display = 'none';
    }

    function setConsentCookies(key, value) {
        document.cookie = key + '=' + value + '; expires=Thu, 18 Dec 2033 12:00:00 UTC; path=/';
    }

    if (localStorage.getItem('consentMode') === null) {
        document.getElementById('btn-accept-all').addEventListener('click', function () {
            setConsent({
                necessary: true,
                analytics: true,
                preferences: true,
                marketing: true,
                personalizedAds: true,
            });
            setConsentCookies('consentCookies', 'yes');
            hideBanner();
        });

        document.getElementById('btn-accept-some').addEventListener('click', function () {
            setConsent({
                necessary: true,
                analytics: document.getElementById('consent-analytics').checked,
                preferences: document.getElementById('consent-preferences').checked,
                marketing: document.getElementById('consent-marketing').checked,
                personalizedAds: document.getElementById('consent-personalized-ads').checked,
            });
            setConsentCookies('consentCookies', 'yes');
            hideBanner();
        });

        document.getElementById('btn-reject-all').addEventListener('click', function () {
            setConsent({
                necessary: false,
                analytics: false,
                preferences: false,
                marketing: false,
                personalizedAds: false,
            });
            hideBanner();
        });

        document.getElementById('cookie-consent-banner').style.display = 'block';
    }

    document.getElementById('cookie-consent-banner').addEventListener('click', checkConsentAndSendUtms);
}

function setConsent(consent) {
    const consentMode = {
        functionality_storage: consent.necessary ? 'granted' : 'denied',
        security_storage: consent.necessary ? 'granted' : 'denied',
        ad_storage: consent.marketing ? 'granted' : 'denied',
        ad_user_data: consent.marketing ? 'granted' : 'denied',
        analytics_storage: consent.analytics ? 'granted' : 'denied',
        personalization_storage: consent.preferences ? 'granted' : 'denied',
        ad_personalization: consent.personalizedAds ? 'granted' : 'denied',
    };
    gtag('consent', 'update', consentMode);
    localStorage.setItem('consentMode', JSON.stringify(consentMode));
}

window.addEventListener('load', onReady);
updateSearchParamsLS();
saveUtmsRequest();
