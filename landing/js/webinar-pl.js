const sendButton = document.getElementById('sendButton');
var DOMAIN = 'https://' + location.hostname + '/hr/';

// add indicator to name input
document.getElementById('meeting-name').addEventListener('input', function (e) {
    if (e.target.value) {
        document.getElementById('form__name-indicator-pl').style.display = 'none';
    } else {
        document.getElementById('form__name-indicator-pl').style.display = 'block';
    }
});

// add indicator to email input
document.getElementById('meeting-email').addEventListener('input', function (e) {
    if (e.target.value) {
        document.getElementById('form__mail-indicator').style.display = 'none';
    } else {
        document.getElementById('form__mail-indicator').style.display = 'block';
    }
});

function onClickSendButtonPl(e) {
    e.preventDefault();
    let name = document.getElementById('meeting-name').value;
    let email = document.getElementById('meeting-email').value;
    let phone = document.getElementById('meeting-phone').value;
    let prefixPhoneField = document.querySelector('[name="prefixPhone"]').value;
    let phoneNumber = `${prefixPhoneField}${phone}`;
    let company = document.getElementById('meeting-company').value;
    let nameError = document.querySelector('#error-name-pl');
    let emailError = document.querySelector('#error-email-pl');
    let errorAfterReg = document.querySelector('.error-after-register1');
    const errors = [];
    let rightRegister = false;
    let regularForEmail = /\S+@\S+\.\S+/;
    let validateEmail = regularForEmail.test(email);
    const locationParam = window.location.pathname;
    let userLang = locationParam.slice(1, 3);

    if (!name || name.length <= 3) {
        nameError.style.display = 'block';
        errors.push({ type: 'name', message: 'Введите имя' });
    } else {
        nameError.style.display = 'none';
    }

    if (!email || !validateEmail) {
        emailError.style.display = 'block';
        errors.push({ type: 'email', message: 'Введите корректный email' });
    } else {
        emailError.style.display = 'none';
    }

    if (userLang === 'me') {
        userLang = 'en';
    }

    if (name.length > 3 && validateEmail) {
        sendButton.disabled = true;
        axios
            .post(`${DOMAIN}webinarRequest`, {
                name,
                email,
                phone: phoneNumber,
                company,
                lang: userLang,
            })
            .then((res) => {
                sendButton.disabled = false;
                if (res.data.status === 'ok') {
                    document.querySelector('.form-wrapper').style.display = 'none';
                    document.querySelector('.finish-reg').style.display = 'flex';
                    document.getElementById('goToForm').style.display = 'none';
                    document.querySelector('.right-register__1').style.display = 'block';
                    document.querySelector('.right-register__2').style.display = 'block';
                    document.querySelector('.right-register__3').style.display = 'block';
                    document.querySelector('.right-register__4').style.display = 'block';
                    errorAfterReg.style.display = 'none';
                } else if (res.data.code === 'suchWebinarRequestAlreadyExist') {
                    errorAfterReg.style.display = 'block';
                    document.querySelector('.form-wrapper').style.display = 'flex';
                    document.querySelector('.finish-reg').style.display = 'none';
                    document.getElementById('goToForm').style.display = 'block';
                    document.querySelector('.right-register__1').style.display = 'none';
                    document.querySelector('.right-register__2').style.display = 'none';
                    document.querySelector('.right-register__3').style.display = 'none';
                    document.querySelector('.right-register__4').style.display = 'none';
                } else {
                    rightRegister = false;
                }
            });
    }
}

sendButton.addEventListener('click', (e) => onClickSendButtonPl(e));
