// Create and add styles for custom dropdown
let useCS = '';
var x, i, j, l, ll, selElmnt, a, b, c;
/* Look for any elements with the class "custom-select": */
x = document.getElementsByClassName('custom-select');
l = x.length;
for (i = 0; i < l; i++) {
    selElmnt = x[i].getElementsByTagName('select')[0];
    ll = selElmnt.length;
    /* For each element, create a new DIV that will act as the selected item: */
    a = document.createElement('DIV');
    a.setAttribute('class', 'select-selected');
    a.innerHTML = selElmnt.options[selElmnt.selectedIndex].innerHTML;
    x[i].appendChild(a);
    /* For each element, create a new DIV that will contain the option list: */
    b = document.createElement('DIV');
    b.setAttribute('class', 'select-items select-hide');
    for (j = 1; j < ll; j++) {
        /* For each option in the original select element,
        create a new DIV that will act as an option item: */
        c = document.createElement('DIV');
        c.innerHTML = selElmnt.options[j].innerHTML;
        c.setAttribute('value', `${selElmnt.options[j].innerHTML}`);
        c.addEventListener('click', function (e) {
            /* When an item is clicked, update the original select box,
            and the selected item: */
            var y, i, k, s, h, sl, yl;
            s = this.parentNode.parentNode.getElementsByTagName('select')[0];
            sl = s.length;
            h = this.parentNode.previousSibling;
            for (i = 0; i < sl; i++) {
                if (s.options[i].innerHTML == this.innerHTML) {
                    useCS = this.innerHTML;
                    s.selectedIndex = i;
                    h.innerHTML = this.innerHTML;
                    y = this.parentNode.getElementsByClassName('same-as-selected');
                    yl = y.length;
                    for (k = 0; k < yl; k++) {
                        y[k].removeAttribute('class');
                    }
                    this.setAttribute('class', 'same-as-selected');
                    break;
                }
            }
            h.click();
            let selectBackground = document.querySelector('.select-selected');
            selectBackground.style.background = 'white';
            selectBackground.style.color = '#312e37';
        });
        b.appendChild(c);
    }
    x[i].appendChild(b);
    a.addEventListener('click', function (e) {
        /* When the select box is clicked, close any other select boxes,
        and open/close the current select box: */
        e.stopPropagation();
        closeAllSelect(this);
        this.nextSibling.classList.toggle('select-hide');
        this.classList.toggle('select-arrow-active');
    });

    // Add click event listener to document to close dropdown when clicking outside
    document.addEventListener('click', function (e) {
        if (!x[0]?.contains(e.target)) {
            b.classList.add('select-hide');
            a.classList.remove('select-arrow-active');
        }
    });
}
function closeAllSelect(elmnt) {
    /* A function that will close all select boxes in the document,
    except the current select box: */
    var x,
        y,
        i,
        xl,
        yl,
        arrNo = [];
    x = document.getElementsByClassName('select-items');
    y = document.getElementsByClassName('select-selected');
    xl = x.length;
    yl = y.length;
    for (i = 0; i < yl; i++) {
        if (elmnt == y[i]) {
            arrNo.push(i);
        } else {
            y[i].classList.remove('select-arrow-active');
        }
    }
    for (i = 0; i < xl; i++) {
        if (arrNo.indexOf(i)) {
            x[i].classList.add('select-hide');
        }
    }
}

const sendButton = document.getElementById('sendButton');
const selectQuestion = document.getElementById('customSelect');
const selectValue = document.getElementById('meeting-quiz');
const selectIndicator = document.getElementById('select-indicator');
var DOMAIN = 'https://' + location.hostname + '/hr/';

// function optionValidator() {
//     let quiz = document.getElementById("meeting-quiz");
//     if (quiz.value.length === 0) {
//         quiz.style.background = '#eff2f7';
//     } else {
//         quiz.style.background = 'white';
//         quiz.style.color = '#312e37';
//     }
// }

// add indicator to name input
document.getElementById('meeting-name').addEventListener('input', function (e) {
    if (e.target.value) {
        document.getElementById('form__name-indicator').style.display = 'none';
    } else {
        document.getElementById('form__name-indicator').style.display = 'block';
    }
});

// add indicator to email input
document.getElementById('meeting-email').addEventListener('input', function (e) {
    if (e.target.value) {
        document.getElementById('form__mail-indicator').style.display = 'none';
    } else {
        document.getElementById('form__mail-indicator').style.display = 'block';
    }
});

function onClickSendButton(e) {
    e.preventDefault();
    let name = document.getElementById('meeting-name').value;
    let email = document.getElementById('meeting-email').value;
    let quiz = document.getElementById('meeting-quiz').value;
    let phone = document.getElementById('meeting-phone').value;
    let prefixPhoneField = document.querySelector('[name="prefixPhone"]').value;
    let phoneNumber = `${prefixPhoneField}${phone}`;
    let company = document.getElementById('meeting-company').value;
    let nameError = document.querySelector('#error-name');
    let emailError = document.querySelector('#error-email');
    let quizError = document.querySelector('#error-quiz');
    let errorAfterReg = document.querySelector('.error-after-register1');
    const errors = [];
    let rightRegister = false;
    let regularForEmail = /\S+@\S+\.\S+/;
    let validateEmail = regularForEmail.test(email);
    const locationParam = window.location.pathname;
    let userLang = locationParam.slice(1, 3);

    if (!name || name.length <= 3) {
        nameError.style.display = 'block';
        errors.push({ type: 'name', message: 'Введите имя' });
    } else {
        nameError.style.display = 'none';
    }

    if (!email || !validateEmail) {
        emailError.style.display = 'block';
        errors.push({ type: 'email', message: 'Введите корректный email' });
    } else {
        emailError.style.display = 'none';
    }

    if (!quiz) {
        quizError.style.display = 'block';
        errors.push({ type: 'quiz', message: 'Выберите один вариант' });
    } else {
        quizError.style.display = 'none';
    }

    if (userLang === 'me') {
        userLang = 'en';
    }

    if (name.length > 3 && validateEmail && quiz) {
        sendButton.disabled = true;
        axios
            .post(`${DOMAIN}webinarRequest`, {
                name,
                email,
                phone: phoneNumber,
                company,
                useCs: quiz,
                lang: userLang,
            })
            .then((res) => {
                sendButton.disabled = false;
                if (res.data.status === 'ok') {
                    // prettier-ignore
                    window.dataLayer.push({
                        'event': 'webinar',
                        'form_name': 'webinar',
                    });

                    document.querySelector('form#form-demo').style.display = 'none';
                    document.querySelector('.finish-reg').style.display = 'flex';
                    errorAfterReg.style.display = 'none';
                } else if (res.data.code === 'suchWebinarRequestAlreadyExist') {
                    errorAfterReg.style.display = 'block';
                    document.querySelector('form#form-demo').style.display = 'initial';
                    document.querySelector('.finish-reg').style.display = 'none';
                } else {
                    rightRegister = false;
                }
            });
    }
}

sendButton.addEventListener('click', (e) => onClickSendButton(e));

function onClickSelect(e) {
    if (selectValue.value) {
        selectIndicator.style.display = 'none';
    }
}

function checkRedirectFromPopup() {
    if (localStorage.getItem('isRedirectedFromPopup')) {
        const userInfo = JSON.parse(localStorage.getItem('isRedirectedFromPopup'));
        userInfo.name && (document.getElementById('meeting-name').value = userInfo.name);
        userInfo.email && (document.getElementById('meeting-email').value = userInfo.email);
        userInfo.company && (document.getElementById('meeting-company').value = userInfo.company);

        if (userInfo.phoneNumber) {
            const codes = {
                '+48': 'Poland',
                48: 'Poland',
                '+380': 'Ukraine',
                380: 'Ukraine',
                '+49': 'Germany',
                49: 'Germany',
            };

            Object.keys(codes).forEach((code) => {
                if (userInfo.phoneNumber.startsWith(code)) {
                    document.querySelector('.choices__list--single span:nth-child(2)').innerText = codes[code];
                    document.querySelector('[name="country"]').value = codes[code];
                    document.querySelector('[name="prefixPhone"]').value = code.includes('+') ? code : `+${code}`;
                    document.querySelector('.user-tel__label').innerText = code.includes('+') ? code : `+${code}`;
                    document.getElementById('meeting-phone').value = userInfo.phoneNumber.slice(code.length);
                }
            });
        }

        document.querySelector('.select-selected').textContent = 'Так, користуємося CleverStaff зараз';
        document.querySelector('#meeting-quiz').value = 'Так, користуємося CleverStaff зараз';
        let selectBackground = document.querySelector('.select-selected');
        selectBackground.style.background = 'white';
        selectBackground.style.color = '#312e37';
        document.getElementById('select-indicator').style.display = 'none';
        document.getElementById('form__name-indicator').style.display = 'none';
        document.getElementById('form__mail-indicator').style.display = 'none';

        localStorage.removeItem('isRedirectedFromPopup');
    }
}

setTimeout(() => checkRedirectFromPopup(), 500);

selectQuestion.addEventListener('click', (e) => onClickSelect(e));
