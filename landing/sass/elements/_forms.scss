/***** Forms *****/
/*** - clear form elements rules */
button,
input,
textarea {
    border-radius: 0;
    box-shadow: none;

    &:focus {
        outline: none;
    }
}

button {
    border: none;
    box-shadow: none;
}

input {
    width: 100%;

    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus,
    &:-webkit-autofill:active {
        -webkit-box-shadow: 0 0 0 30px white inset !important;
        box-shadow: 0 0 0 30px white inset !important;
    }

    &::selection {
        background-color: $color-almost-light;
    }
}

textarea {
    width: 100%;
    resize: none;
}

form {
    fieldset {
        padding: 0;
        margin: 0;
        border: none;
    }
}

/*** - forms */
.form {
    margin-bottom: 40px;
    .fix-font {
        font-family: $font-family-body;
        font-weight: bold;
    }

    fieldset {
        margin-bottom: 20px;
    }

    .blur {
        filter: blur(1px);
        user-select: none;
        pointer-events: none;
    }

    .form__label {
        display: block;
        margin-bottom: 2px;
        font-family: $font-family-helper;
        font-size: $font-size-small;
        line-height: 1.5;
    }

    .form__indicator {
        font-family: 'Helvetica-Normal';
        font-weight: 700;
        color: #ff0000;
    }

    .form__error {
        padding-left: 20px;
        display: block;
        height: 0;
        opacity: 0;
        font-size: $font-size-small;
        &.password-error {
            opacity: 1;
        }
    }

    .form__input {
        padding: 9px 20px 8px;
        font-size: $font-size-small;
        line-height: 1.5;
        color: $color-input-text;
        background: $color-almost-light;
        border: 1px solid $color-input;
        border-radius: 20px;
        box-shadow: none;

        &.invalid {
            color: $color-red;
            border: 1px solid $color-red;
            background: $color-red-light;

            & ~ .form__error {
                opacity: 1;
            }
        }

        @include transition(background $time-fast $easing-base, border-color $time-fast $easing-base);

        &:not(:placeholder-shown) {
            &:not(.invalid) {
                color: $color-dark;
                background: $color-light;
            }
        }

        &:focus {
            color: $color-dark;
            background: $color-light;
            border-color: $color-primary;
        }

        @include input-placeholder {
            color: $color-input-text;
            opacity: 1;
        }

        &[type='password'] {
            letter-spacing: 0.5px;
            color: $color-shade;

            @include input-placeholder {
                letter-spacing: 0;
            }
        }

        & + .form__input {
            width: calc(100% - 30px);
            margin-top: 12px;

            & + .toggle-pass {
                margin-top: 12px;
            }
        }
    }

    .form__select {
        .choices {
            width: 100%;
        }
    }

    .form__area {
        min-height: 120px;
    }

    .form__submit {
        padding-top: 6px;
        margin-bottom: 0;
        text-align: center;

        button[disabled] {
            background: $color-input-text;
            border: 1px solid $color-input-text;
            color: $color-dark;

            &:hover {
                box-shadow: none;
                cursor: not-allowed;
            }
        }
    }

    .one-line-group {
        @include flexbox();

        &__label {
            position: relative;
            width: 100%;
        }
    }

    .user-tel__label {
        color: $color-input-text;
        position: absolute;
        top: 10px;
        left: 10px;
        font-size: 14px;
    }

    .form__description {
        display: block;
        margin-bottom: 12px;
        text-align: center;
        font-size: $font-size-small;
        line-height: 1.5;
    }

    .form__group {
        small {
            display: block;
            margin-top: 6px;
            margin-bottom: 12px;
            font-family: $font-family-helper;
            font-size: 12px;
            line-height: 1.5;
            color: $color-shade;
            opacity: 0.6;
        }

        .form__error {
            color: $color-red;
        }

        &.authorization-code-wrapper {
            display: none;
        }
    }

    .alert-company-icon {
        position: absolute;
        display: none;
        top: -16px;
        left: 456px;
        width: 40px;
        height: 30px;

        background: transparent url('../images/icons/alert-icon.svg') no-repeat;
        color: red;
        background-size: contain;
        cursor: pointer;
    }

    .company__error {
        position: relative;
        top: -20px;
    }

    .tooltip {
        display: none;
        position: absolute;
        box-sizing: border-box;
        padding: 10px;
        top: -90px;
        left: 68px;
        border: 2px solid red;
        border-radius: 5px;
        background: indianred;
        color: white;
        width: 400px;
        z-index: 1070;
        font-family: 'Helvetica-Normal', Arial, Verdana, sans-serif;
        font-style: normal;
        font-weight: normal;
        letter-spacing: normal;
        line-break: auto;
        line-height: 1.42857;
        text-align: start;
        text-decoration: none;
        text-shadow: none;
        text-transform: none;
        white-space: normal;
        word-break: normal;
        word-spacing: normal;
        word-wrap: normal;
        font-size: 12px;
    }

    .form__group-field {
        //margin: 0;
        font-size: 0;

        .form__group {
            display: inline-block;
            width: 50%;
            margin-bottom: 0;

            &:first-child {
                padding-right: 20px;
            }
        }
    }

    .form__group--plain {
        margin-bottom: 0;

        & + .form__submit {
            padding-top: 17px;
        }
    }

    .form__next {
        padding-top: 22px;
        text-align: center;
        font-family: $font-family-helper;
        font-size: 16px;
        line-height: 21px;

        a {
            display: block;
            color: #2f80ed;
        }
    }
}

.form--contact {
    margin-bottom: 0 !important;
}

.toggle-pass {
    position: relative;
    @include flexbox();
    @include align-items(center);
    @include flex-shrink(0);
    width: 18px;
    margin-left: 12px;
    @include transition(color $time-fast $easing-base);

    &:before,
    &:after {
        @include pseudo();
        top: 50%;
        left: 50%;
        width: 20px;
        height: 2px;
        margin-top: -1px;
        @include rotate(45);
        opacity: 1;

        @include transition(opacity $time-fast $easing-base);
    }

    &:before {
        background: $color-light;
        margin-left: -9px;
    }

    &:after {
        background: $color-dark;
        margin-left: -11px;
    }

    &.change {
        color: $color-primary;

        &:before,
        &:after {
            opacity: 0;
        }
    }
}

/*** - select */
.choices {
    width: 152px;
    background-color: #eff2f7;
    border-radius: 20px;
    padding: 9px 16px 5px 15px;
    border: 1px solid #d6e0e7;
    @include flex-shrink(0);
    margin-bottom: 0;
    margin-right: 12px;

    .choices__inner {
        min-height: 0;
        padding: 0;
        background: none;
        border: none;
    }

    .choices__list--single {
        padding: 0;

        .choices__item {
            min-height: 40px;
            height: 40px;
            display: block;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            padding: 8px 25px 0 20px;
            font-size: $font-size-small;
            line-height: 1.5;
            color: $color-input-text;
            background: $color-almost-light;
            border: 1px solid $color-input;
            border-radius: 20px;

            @include transition(all $time-fast $easing-base);
        }

        .choices__item--selectable:not([data-id='1']) {
            color: $color-dark;
            background: $color-light;
            border-color: $color-input;
        }
    }

    &.is-open {
        background: #fff;
        border: 1px solid #47ab43;
        .choices__item {
            border-color: $color-primary !important;
            background: $color-light;
        }
    }

    .choices__item {
        img {
            border: 1px solid $color-input;
            margin-right: 2px;
        }

        img + span {
            position: relative;
            top: 1px;
        }
    }

    .choices__list--dropdown {
        margin: 5px 0;
        left: 0;
        border: 1px solid $color-primary;
        border-radius: 20px;
        width: 310px;

        .choices__input {
            height: 36px;
        }

        .choices__item {
            color: $color-shade;
            background: $color-light;
            padding: 7px 20px;
            border-top: 1px solid $color-almost-light !important;
            @include transition(
                color $time-fast $easing-base,
                background $time-fast $easing-base,
                border-color $time-fast $easing-base
            );

            &:first-child {
                border-top: none !important;
            }

            &[data-id='1'] {
                display: none;
            }
        }
        .undefined {
            color: $color-shade;
            background: $color-light;
            padding: 7px 20px;
            border-top: 1px solid $color-almost-light !important;
            @include transition(
                color $time-fast $easing-base,
                background $time-fast $easing-base,
                border-color $time-fast $easing-base
            );

            &:first-child {
                border-top: none !important;
            }

            &[data-id='1'] {
                display: none;
            }
        }

        .choices__item--selectable.is-highlighted {
            color: $color-dark;
            background: $color-background;
        }
    }

    &[data-type*='select-one'] {
        &:after {
            right: 21px;
            margin-top: -4px;
            width: 5px;
            height: 5px;
            border: none;
            border-bottom: 1px solid $color-dark;
            border-right: 1px solid $color-dark;
            @include rotate(45);
        }

        .choices__inner {
            padding-bottom: 0;
        }
    }

    &[data-type*='select-one'].is-open {
        &:after {
            margin-top: -4px;
            border: none;
            border-bottom: 1px solid $color-dark;
            border-right: 1px solid $color-dark;
        }
    }
}

/*** - checkbox */
.checkbox {
    display: none;

    & + label {
        position: relative;
        display: block;
        padding-left: 24px;
        font-family: $font-family-helper;
        font-size: $font-size-for-fix-letters;
        line-height: 1.5;
        color: $color-shade;
        cursor: pointer;

        a {
            color: #2f80ed;
        }

        &:before {
            @include pseudo();
            top: 1px;
            left: 0;
            width: 16px;
            height: 16px;
            border: 1px solid $color-input;
            border-radius: 3px;

            @include transition(all $time-base $easing-base);
        }

        &:after {
            @include pseudo();
            top: 4px;
            left: 6px;
            width: 4px;
            height: 8px;
            border-bottom: 2px solid $color-light;
            border-right: 2px solid $color-light;

            @include rotate(45);

            opacity: 0;
            @include transition(opacity $time-base $easing-base);
        }

        &:hover:before {
            border-color: $color-primary;
        }
    }

    &:checked + label {
        &:before {
            border-color: $color-primary;
            background: $color-primary;
        }

        &:after {
            opacity: 1;
        }
    }
}

@include media-breakpoint-up(sm) {
    .form {
        .input-wrapper {
            width: calc(50% - 6px);
        }

        .form__input {
            & + .form__input {
                margin-left: 12px;
                margin-top: 0;
                width: 100%;

                & + .toggle-pass {
                    margin-top: 0px;
                }
            }
        }

        .form__next {
            a {
                display: inline;
            }
        }
    }

    .form--long {
        margin-bottom: 51px;
    }
}

@include media-breakpoint-up(lg) {
    .form__next {
        display: none;
    }
}

@media (min-width: 1440px) {
    .form {
    }

    .form--long {
        margin-bottom: 51px;
    }
}

#server_error {
    text-align: center;
    color: $color-red;
}

#errorForRus {
    display: none;
    a {
        color: #0000ee;
    }
}

.animation {
    opacity: 1;

    &.onloading {
        opacity: 0;
    }
}
@media (max-width: 575px) {
    .choices {
        width: 130px;
        height: 40px;
    }
    .choices[data-type*='select-one'].is-open:after,
    .choices[data-type*='select-one']:after {
        margin-top: -6px;
    }
    .form .form__input {
        padding: 9px 10px 8px 20px;
    }
}
