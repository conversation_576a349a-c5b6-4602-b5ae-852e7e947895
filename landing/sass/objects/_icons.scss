//$src_images: '../../images';
$src_images: '../../external/images';

@mixin background($name) {
    background-image: url('#{$src_images}/icons/flags/#{$name}.svg');
}

.icon {
    font-style: normal;
}

.icon--feature {
    fill: #f4bf4f;
}

.flag {
    position: relative;
    top: -1px;
    display: inline-block;
    vertical-align: middle;
    width: 16px;
    height: 16px;
    margin-right: 2px;
    border: 1px solid $color-input;
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: cover;
    //background-image: url('#{$src_images}/icons/flags/ua.svg');
}

.visible {
    &.flag {
        position: relative;
        top: -1px;
        display: inline-block;
        vertical-align: middle;
        width: 16px;
        height: 16px;
        margin-right: 2px;
        border: 1px solid $color-input;
        background-repeat: no-repeat;
        background-position: 50% 50%;
        background-size: cover;

        &--ad {
            @include background('ad');
        }
        &--ae {
            @include background('ae');
        }
        &--af {
            @include background('af');
        }
        &--ag {
            @include background('ag');
        }
        &--ai {
            @include background('ai');
        }
        &--al {
            @include background('al');
        }
        &--am {
            @include background('am');
        }
        &--ao {
            @include background('ao');
        }
        &--aq {
            @include background('aq');
        }
        &--ar {
            @include background('ar');
        }
        &--as {
            @include background('as');
        }
        &--at {
            @include background('at');
        }
        &--au {
            @include background('au');
        }
        &--aw {
            @include background('aw');
        }
        &--ax {
            @include background('ax');
        }
        &--az {
            @include background('az');
        }
        &--ba {
            @include background('ba');
        }
        &--bb {
            @include background('bb');
        }
        &--bd {
            @include background('bd');
        }
        &--be {
            @include background('be');
        }
        &--bf {
            @include background('bf');
        }
        &--bg {
            @include background('bg');
        }
        &--bh {
            @include background('bh');
        }
        &--bi {
            @include background('bi');
        }
        &--bj {
            @include background('bj');
        }
        &--bl {
            @include background('bl');
        }
        &--bm {
            @include background('bm');
        }
        &--bn {
            @include background('bn');
        }
        &--bo {
            @include background('bo');
        }
        &--br {
            @include background('br');
        }
        &--bs {
            @include background('bs');
        }
        &--bt {
            @include background('bt');
        }
        &--bv {
            @include background('bv');
        }
        &--bw {
            @include background('bw');
        }
        &--by {
            @include background('by');
        }
        &--bz {
            @include background('bz');
        }
        &--ca {
            @include background('ca');
        }
        &--cc {
            @include background('cc');
        }
        &--cd {
            @include background('cd');
        }
        &--cf {
            @include background('cf');
        }
        &--cg {
            @include background('cg');
        }
        &--ch {
            @include background('ch');
        }
        &--ci {
            @include background('ci');
        }
        &--ck {
            @include background('ck');
        }
        &--cl {
            @include background('cl');
        }
        &--cm {
            @include background('cm');
        }
        &--cn {
            @include background('cn');
        }
        &--co {
            @include background('co');
        }
        &--cr {
            @include background('cr');
        }
        &--cu {
            @include background('cu');
        }
        &--cv {
            @include background('cv');
        }
        &--cx {
            @include background('cx');
        }
        &--cy {
            @include background('cy');
        }
        &--cz {
            @include background('cz');
        }
        &--de {
            @include background('de');
        }
        &--dj {
            @include background('dj');
        }
        &--dk {
            @include background('dk');
        }
        &--dm {
            @include background('dm');
        }
        &--do {
            @include background('do');
        }
        &--dz {
            @include background('dz');
        }
        &--ec {
            @include background('ec');
        }
        &--ee {
            @include background('ee');
        }
        &--eg {
            @include background('eg');
        }
        &--eh {
            @include background('eh');
        }
        &--er {
            @include background('er');
        }
        &--es {
            @include background('es');
        }
        &--et {
            @include background('et');
        }
        &--fi {
            @include background('fi');
        }
        &--fj {
            @include background('fj');
        }
        &--fk {
            @include background('fk');
        }
        &--fm {
            @include background('fm');
        }
        &--fo {
            @include background('fo');
        }
        &--fr {
            @include background('fr');
        }
        &--ga {
            @include background('ga');
        }
        &--gb {
            @include background('gb');
        }
        &--gd {
            @include background('gd');
        }
        &--ge {
            @include background('ge');
        }
        &--gf {
            @include background('gf');
        }
        &--gg {
            @include background('gg');
        }
        &--gh {
            @include background('gh');
        }
        &--gi {
            @include background('gi');
        }
        &--gl {
            @include background('gl');
        }
        &--gm {
            @include background('gm');
        }
        &--gn {
            @include background('gn');
        }
        &--gp {
            @include background('gp');
        }
        &--gq {
            @include background('gq');
        }
        &--gr {
            @include background('gr');
        }
        &--gs {
            @include background('gs');
        }
        &--gt {
            @include background('gt');
        }
        &--gu {
            @include background('gu');
        }
        &--gw {
            @include background('gw');
        }
        &--gy {
            @include background('gy');
        }
        &--hk {
            @include background('hk');
        }
        &--hm {
            @include background('hm');
        }
        &--hn {
            @include background('hn');
        }
        &--hr {
            @include background('hr');
        }
        &--ht {
            @include background('ht');
        }
        &--hu {
            @include background('hu');
        }
        &--id {
            @include background('id');
        }
        &--ie {
            @include background('ie');
        }
        &--il {
            @include background('il');
        }
        &--im {
            @include background('im');
        }
        &--in {
            @include background('in');
        }
        &--io {
            @include background('io');
        }
        &--iq {
            @include background('iq');
        }
        &--ir {
            @include background('ir');
        }
        &--is {
            @include background('is');
        }
        &--it {
            @include background('it');
        }
        &--je {
            @include background('je');
        }
        &--jm {
            @include background('jm');
        }
        &--jo {
            @include background('jo');
        }
        &--jp {
            @include background('jp');
        }
        &--ke {
            @include background('ke');
        }
        &--kg {
            @include background('kg');
        }
        &--kh {
            @include background('kh');
        }
        &--ki {
            @include background('ki');
        }
        &--km {
            @include background('km');
        }
        &--kn {
            @include background('kn');
        }
        &--kp {
            @include background('kp');
        }
        &--kr {
            @include background('kr');
        }
        &--kw {
            @include background('kw');
        }
        &--kn {
            @include background('kn');
        }
        &--kp {
            @include background('kp');
        }
        &--kr {
            @include background('kr');
        }
        &--kw {
            @include background('kw');
        }
        &--ky {
            @include background('ky');
        }
        &--kz {
            @include background('kz');
        }
        &--la {
            @include background('la');
        }
        &--lb {
            @include background('lb');
        }
        &--lc {
            @include background('lc');
        }
        &--li {
            @include background('li');
        }
        &--lk {
            @include background('lk');
        }
        &--lr {
            @include background('lr');
        }
        &--ls {
            @include background('ls');
        }
        &--lt {
            @include background('lt');
        }
        &--lu {
            @include background('lu');
        }
        &--lv {
            @include background('lv');
        }
        &--ly {
            @include background('ly');
        }
        &--ma {
            @include background('ma');
        }
        &--mc {
            @include background('mc');
        }
        &--md {
            @include background('md');
        }
        &--me {
            @include background('me');
        }
        &--mf {
            @include background('mf');
        }
        &--mg {
            @include background('mg');
        }
        &--mh {
            @include background('mh');
        }
        &--mk {
            @include background('mk');
        }
        &--ml {
            @include background('ml');
        }
        &--mm {
            @include background('mm');
        }
        &--mn {
            @include background('mn');
        }
        &--mo {
            @include background('mo');
        }
        &--mp {
            @include background('mp');
        }
        &--mq {
            @include background('mq');
        }
        &--mr {
            @include background('mr');
        }
        &--ms {
            @include background('ms');
        }
        &--mt {
            @include background('mt');
        }
        &--mu {
            @include background('mu');
        }
        &--mv {
            @include background('mv');
        }
        &--mw {
            @include background('mw');
        }
        &--mx {
            @include background('mx');
        }
        &--my {
            @include background('my');
        }
        &--mz {
            @include background('mz');
        }
        &--na {
            @include background('na');
        }
        &--nc {
            @include background('nc');
        }
        &--ne {
            @include background('ne');
        }
        &--nf {
            @include background('nf');
        }
        &--ng {
            @include background('ng');
        }
        &--ni {
            @include background('ni');
        }
        &--nl {
            @include background('nl');
        }
        &--no {
            @include background('no');
        }
        &--np {
            @include background('np');
        }
        &--nr {
            @include background('nr');
        }
        &--nu {
            @include background('nu');
        }
        &--nz {
            @include background('nz');
        }
        &--om {
            @include background('om');
        }
        &--pa {
            @include background('pa');
        }
        &--pe {
            @include background('pe');
        }
        &--pf {
            @include background('pf');
        }
        &--pg {
            @include background('pg');
        }
        &--ph {
            @include background('ph');
        }
        &--pk {
            @include background('pk');
        }
        &--pl {
            @include background('pl');
        }
        &--pm {
            @include background('pm');
        }
        &--pn {
            @include background('pn');
        }
        &--pr {
            @include background('pr');
        }
        &--ps {
            @include background('ps');
        }
        &--pt {
            @include background('pt');
        }
        &--pw {
            @include background('pw');
        }
        &--py {
            @include background('py');
        }
        &--qa {
            @include background('qa');
        }
        &--re {
            @include background('re');
        }
        &--ro {
            @include background('ro');
        }
        &--rs {
            @include background('rs');
        }
        &--ru {
            @include background('ru');
        }
        &--rw {
            @include background('rw');
        }
        &--sa {
            @include background('sa');
        }
        &--sb {
            @include background('sb');
        }
        &--sc {
            @include background('sc');
        }
        &--sd {
            @include background('sd');
        }
        &--se {
            @include background('se');
        }
        &--sg {
            @include background('sg');
        }
        &--sh {
            @include background('sh');
        }
        &--si {
            @include background('si');
        }
        &--sj {
            @include background('sj');
        }
        &--sk {
            @include background('sk');
        }
        &--sl {
            @include background('sl');
        }
        &--sm {
            @include background('sm');
        }
        &--sn {
            @include background('sn');
        }
        &--so {
            @include background('so');
        }
        &--sr {
            @include background('sr');
        }
        &--st {
            @include background('st');
        }
        &--sv {
            @include background('sv');
        }
        &--sy {
            @include background('sy');
        }
        &--sz {
            @include background('sz');
        }
        &--tc {
            @include background('tc');
        }
        &--td {
            @include background('td');
        }
        &--tf {
            @include background('tf');
        }
        &--tg {
            @include background('tg');
        }
        &--th {
            @include background('th');
        }
        &--tj {
            @include background('tj');
        }
        &--tk {
            @include background('tk');
        }
        &--tl {
            @include background('tl');
        }
        &--tm {
            @include background('tm');
        }
        &--tn {
            @include background('tn');
        }
        &--to {
            @include background('to');
        }
        &--tr {
            @include background('tr');
        }
        &--tt {
            @include background('tt');
        }
        &--tv {
            @include background('tv');
        }
        &--tw {
            @include background('tw');
        }
        &--tz {
            @include background('tz');
        }
        &--ua {
            @include background('ua');
        }
        &--ug {
            @include background('ug');
        }
        &--um {
            @include background('um');
        }
        &--us {
            @include background('us');
        }
        &--uy {
            @include background('uy');
        }
        &--uz {
            @include background('uz');
        }
        &--va {
            @include background('va');
        }
        &--vc {
            @include background('vc');
        }
        &--ve {
            @include background('ve');
        }
        &--vg {
            @include background('vg');
        }
        &--vi {
            @include background('vi');
        }
        &--vn {
            @include background('vn');
        }
        &--vu {
            @include background('vu');
        }
        &--wf {
            @include background('wf');
        }
        &--ws {
            @include background('ws');
        }
        &--ye {
            @include background('ye');
        }
        &--yt {
            @include background('yt');
        }
        &--za {
            @include background('za');
        }
        &--zm {
            @include background('zm');
        }
        &--zw {
            @include background('zw');
        }
    }
}
