@import '../utilities/_extensions.scss';

.modal-details {
    position: fixed;
    top: 0;
    left: 0;
    right: -17px;
    bottom: 0;
    z-index: 101;
    overflow-y: scroll;
    opacity: 0;
    pointer-events: none;
    @include transition(opacity 0ms ease 2 * $time-long);

    &:before {
        @include pseudo();
        position: fixed;
        top: 0;
        left: 0;
        right: 80px;
        bottom: 0;
        z-index: -1;
        background: $color-background;

        border-radius: 0 0 400px 0;
        @include translate(-100%, 0);
        @include transition(all $time-long $easing-base $time-long);
    }

    .modal-details__header {
        padding: 96px 0 72px;
    }

    .modal-details__item {
        margin-bottom: 48px;

        h4 {
            font-size: 22px;
            font-family: $font-family-body;
        }
    }

    .btn-order {
        margin-bottom: 96px;
    }

    .modal-details__content {
        opacity: 0;
        @include transition(opacity $time-long $easing-base);
    }

    &.modal-open {
        opacity: 1;
        pointer-events: auto;
        @include transition(opacity 0ms ease 0ms);

        &:before {
            @include translate(0, 0);
            @include transition(all $time-long $easing-base);
        }

        .modal-details__content,
        .modal-toggle {
            opacity: 1;
            @include transition(opacity $time-long $easing-base $time-long);
        }
    }
}

.modal-toggle {
    position: fixed;
    top: 35px;
    right: 125px;

    opacity: 0;
    @include transition(opacity $time-long $easing-base);
}

.modal-window {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
    white-space: nowrap;
    z-index: 100;
    vertical-align: middle;
    pointer-events: none;
    opacity: 0;
    backface-visibility: hidden;
    transform: translateZ(0) perspective(1px);
    -webkit-font-smoothing: subpixel-antialiased;

    &.active {
        pointer-events: auto;
        display: block;
        opacity: 1;
    }

    &:after {
        content: '';
        display: inline-block;
        width: 0;
        height: 100%;
    }

    &__vertical-inner {
        display: inline-block;
        width: 100%;
        height: auto;
        white-space: normal;
        text-align: center;

        &--content {
            display: inline-block;
            width: 440px;
            background-color: $color-light;
            border-radius: 5px;
            font-size: 12px;
            position: relative;

            img {
                position: absolute;
                right: 8px;
                top: 8px;
                cursor: pointer;
            }

            p {
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                background-color: $color-light;
                padding: 25px 0 5px 0;
                font-size: 22px;
                font-family: $font-family-body;
                line-height: 1.1;
            }

            &__footer {
                padding: 13px 0 11px;
                background-color: $color-light;
                border-bottom-left-radius: 5px;
                border-bottom-right-radius: 5px;

                .re-direct {
                    font-family: $font-family-body;
                    font-size: 12px;
                    padding: 8px 60px;
                    min-width: auto;
                }
            }
        }
    }
}
