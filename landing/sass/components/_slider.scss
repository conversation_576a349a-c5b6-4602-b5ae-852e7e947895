.clients-slider,
.reviews-slider,
.blog-slider {
    padding-bottom: 20px;
    padding-top: 20px;

    .swiper-controls {
        position: absolute;
        //top: auto;
        bottom: 0;
        right: auto;
        left: 50%;
        width: 100%;
        text-align: center;
        z-index: 2;

        @include translate(-50%, 0);
    }

    .swiper-pagination {
        position: static;
        display: inline-block;
        width: auto;

        font-family: $font-family-helper;
        font-size: 12px;
        line-height: 1;
    }

    .swiper-pagination-divider {
        margin-left: 3px;
        margin-right: 2px;
    }

    .swiper-button-prev,
    .swiper-button-next {
        position: relative;
        top: 0;
        left: 0;
        margin: 0;
        display: inline-block;
        width: 68px;
        height: 10px;

        &:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            margin-top: -2px;
            width: 100%;
            height: 5px;
            background: url('../images/assets/arrow-long.svg') no-repeat;
            background-position: 0 50%;
        }

        &:focus {
            outline: none;
        }
    }

    .swiper-button-prev {
        left: 0;
        margin-right: 32px;

        &:after {
            @include rotate(180);
        }
    }

    .swiper-button-next {
        right: 0;
        margin-left: 31px;
    }
}

.reviews-slider {
    padding-bottom: 100px;
    padding-top: 80px;
}

.blog-slider {
    padding: 20px 20px 64px;
}

@include media-breakpoint-up(md) {
    .clients-slider,
    .reviews-slider,
    .blog-slider {
        //padding-top: 70px;
        //padding-bottom: 0px;

        .swiper-controls {
            width: auto;
            //top: 0;
            bottom: 0;
            right: 43px;
            left: auto;

            @include translate(0, 0);
        }

        .swiper-button-prev,
        .swiper-button-next {
            &:after {
                background-position: -52px 50%;
                @include transition(background $time-base $easing-base);
            }

            &:hover:after {
                background-position: 0 50%;
            }
        }

        .swiper-button-prev {
            margin-right: 12px;
        }

        .swiper-button-next {
            margin-left: 11px;
        }
    }
}

//Features-mobile
.features-group {
    padding-bottom: 20px;
    padding-top: 20px;

    .swiper-controls {
        position: absolute;
        //top: auto;
        bottom: 0;
        right: auto;
        left: 50%;
        width: 100%;
        text-align: center;
        z-index: 2;

        @include translate(-50%, 0);
    }

    .swiper-pagination {
        position: static;
        display: inline-block;
        width: auto;

        font-family: $font-family-helper;
        font-size: 12px;
        line-height: 1;
    }

    .swiper-pagination-divider {
        margin-left: 3px;
        margin-right: 2px;
    }

    .swiper-button-prev,
    .swiper-button-next {
        position: relative;
        top: 0;
        left: 0;
        margin: 0;
        display: inline-block;
        width: 68px;
        height: 10px;

        &:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            margin-top: -2px;
            width: 100%;
            height: 5px;
            background: url('../images/assets/arrow-long.svg') no-repeat;
            background-position: 0 50%;
        }

        &:focus {
            outline: none;
        }
    }

    .swiper-button-prev {
        left: 0;
        margin-right: 32px;

        &:after {
            @include rotate(180);
        }
    }

    .swiper-button-next {
        right: 0;
        margin-left: 31px;
    }
}

// Pricing
.pricing-group {
    .swiper-slide {
        @include flexbox();
        @include justify-content(center);
        width: 270px;
        height: auto;
    }

    &.swiper-container-horizontal > .swiper-pagination-bullets,
    .swiper-pagination-custom,
    .swiper-pagination-fraction {
        bottom: -7px;
    }

    .swiper-pagination-bullet {
        width: 8px;
        height: 8px;
        margin: 0 6px !important;
        background: $color-dark;
        opacity: 0.2;

        &:focus {
            outline: none;
        }
    }

    .swiper-pagination-bullet-active {
        opacity: 1;
    }
}

@include media-breakpoint-up(sm) {
    .clients-slider,
    .reviews-slider,
    .blog-slider {
        .swiper-slide {
            width: 340px;
        }
    }
}

@include media-breakpoint-up(xl) {
    .pricing-group {
        .swiper-pagination {
            display: none;
        }
    }
}

.information-accordion__panel[aria-hidden='true'] {
    display: none;
}

.information-accordion {
    margin-bottom: -12px;

    .information-accordion__title {
        margin-bottom: 12px;
    }

    .information-accordion__panel {
        padding: 4px 18px 12px;

        & + .information-accordion__title {
            margin-top: 12px;
        }
    }

    .js-accordion__header {
        position: relative;
        display: block;
        padding: 4px 24px 4px 0;
        font-family: $font-family-helper;
        font-size: 16px;
        line-height: 24px;
        text-align: left;

        &:before {
            @include pseudo();
            top: 50%;
            right: 0;
            width: 8px;
            height: 8px;
            margin-top: -7px;
            border-left: 2px solid $color-dark;
            border-bottom: 2px solid $color-dark;
            @include rotate(-45);
        }
    }
}

[aria-hidden='true'].basic-tabs__content {
    display: none;
}

.basic-tabs {
    .js-tablist__link {
        font-family: $font-family-heading;
    }

    .js-tablist__link[aria-selected='true'] {
        display: inline-block;
        border-bottom: 3px solid #c7c3c3;
    }

    .js-tablist__item {
        display: inline-block;
        width: 24%;
        vertical-align: top;
    }

    .js-tabcontent {
        padding-top: 96px;
        padding-bottom: 96px;
    }

    .tabcontent__item {
        margin-bottom: 48px;
    }

    h4 {
        font-size: 22px;
        font-family: $font-family-body;
    }
}

@include media-breakpoint-up(md) {
    .information-accordion {
        .js-accordion__header {
            font-size: 18px;
            line-height: 27px;
        }
    }
}

@include media-breakpoint-up(xl) {
    .information-accordion {
        .information-accordion__panel {
            & + .information-accordion__title {
                margin-top: 20px;
            }
        }
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .reviews-slider {
        padding-top: 45px;
        padding-bottom: 20px;
    }
}
