@keyframes copied {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

.bf-wrapper {
    @media (max-width: 991px) {
        display: flex;
        flex-direction: column;

        .banner.banner--basic.bf-mode {
            display: flex;

            &:before {
                width: 140%;
                left: auto;
                right: auto;
                bottom: 0;
                border-radius: 0 0 50% 50%;
                margin: 0;

                @media (max-width: 550px) {
                    width: 800px;
                }
            }

            @media (max-width: 360px) {
                padding: 0 15px;
            }
        }
    }
}

.bf-banner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;

    @media (max-width: 991px) {
        margin: 60px 0 25px 0;
    }

    &__header {
        display: flex;
        gap: 10px;
    }
    &__title {
        font-family: 'HelveticaNeueCyr-Medium';

        @media (max-width: 1180px) {
            font-size: 28px;
        }
        @media (max-width: 1080px) {
            font-size: 22px;
        }
        @media (max-width: 991px) {
            font-size: 28px;
        }
        @media (max-width: 460px) {
            font-size: 6vw;
        }
    }
    &__fire-icon {
        height: 33px;
        @media (max-width: 1080px) {
            height: 28px;
        }
    }

    &__content {
        padding: 20px;
        border: 1px solid white;
        border-radius: 30px;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        max-width: 370px;

        &_percent {
            margin-top: 20px;
            font-family: 'HelveticaNeueCyr-Bold';
            font-size: 80px;
            font-style: italic;
            line-height: 1em;
            font-weight: 700;
            color: #71c961;

            @media (max-width: 360px) {
                font-size: 60px;
            }
        }
        &_condition {
            margin-bottom: 25px;
        }
        &_promocode-title {
            color: #bdbdbd;
        }
        &_promocode {
            color: white;
            font-family: 'HelveticaNeueCyr-Medium';
            font-size: 1.4em;

            @media (max-width: 360px) {
                font-size: 20px;
            }
        }
        &_hint {
            font-size: 0.7em;
            margin-top: 5px;
            color: #bdbdbd;

            &-animated {
                animation: copied 0.7s;
            }
        }
    }

    &__footer {
        font-size: 1.2em;
        line-height: 1.2em;
        font-family: 'SF UI Display Light';
        text-align: center;

        @media (max-width: 420px) {
            font-size: 1em;
        }

        &_orange-text {
            color: #f19e38;
        }

        &_arrow-icon {
            display: none;
            margin: 40px auto 0;
            width: 15%;

            @media (max-width: 991px) {
                display: block;
            }
        }
    }
}
