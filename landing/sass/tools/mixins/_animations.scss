@mixin title-animation() {
    transform: translate(0, 20%);
    opacity: 0;
    transition: all 0.7s ease;

    &._visible {
        transform: translate(0, 0);
        opacity: 1;
    }
}

@mixin story-title-animation() {
    transform: translate(0, 40%);
    opacity: 0;
    transition: all 0.7s ease;

    &._visible {
        transform: translate(0, 0);
        opacity: 1;
    }
}

@mixin story-content-animation() {
    transform: translate(0, 40%);
    opacity: 0;
    transition: all 0.7s ease;

    &._visible {
        transform: translate(0, 0);
        opacity: 1;
    }
}
