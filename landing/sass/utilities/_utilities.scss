/***** Utilities *****/
.align-center {
    text-align: center;
}

.state {
    position: relative;
    display: block;
    margin: auto;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: $color-light;

    &:before {
        @include pseudo();
    }
}

.check-state {
    border: 2px solid $color-primary;

    &:before {
        top: 50%;
        left: 50%;
        width: 8px;
        height: 20px;
        margin-left: 0px;
        margin-top: -15px;
        border-bottom: 2px solid $color-primary;
        border-right: 2px solid $color-primary;
        @include rotate(45);
    }
}

.uncheck-state {
    border: 2px solid #e75a3a;

    &:before {
        top: 50%;
        left: 4px;
        width: 12px;
        height: 2px;
        margin-top: -1px;
        background: #e75a3a;
        border-radius: 2px;
    }
}

.modify-date {
    margin-top: -8px;
    display: block;
    margin-bottom: 23px;
}

.background {
    position: relative;
    background: $color-background;

    &:before,
    &:after {
        @include pseudo();
        top: 0;
        width: 1000%;
        height: 100%;
        background: $color-background;
    }

    &:before {
        left: 100%;
    }

    &:after {
        right: 100%;
    }

    &.background--partners {
        padding: 36px 0;
    }
}

//.background--partners {
//    padding: 30px 0 30px 350px;
//
//    h3.heading-partners {
//        margin-right: 10px;
//        text-align: left;
//        font-size: 28px;
//    }
//}

@include media-breakpoint-down(md) {
    .background--partners {
        padding: 30px 0 30px 269px;
    }
}

.background--trust {
    padding: 45px 0 16px;
    margin-bottom: 46px;
}

.background--story {
    padding-bottom: 42px;
}

.background--description {
    padding-bottom: 25px;
}

.background--form {
    padding: 51px 0;
    margin-bottom: 51px;
}

.background--common {
    margin-bottom: 34px;

    &:before,
    &:after {
        z-index: -1;
    }
}

.background--extra {
    padding-bottom: 33px;
    margin-bottom: 51px;
}

.contact-decoration-form {
    display: block;
    padding-top: 22px;
    padding-bottom: 22px;
    max-width: 120px;
    margin-left: auto;
    margin-right: auto;
}

.box {
    position: relative;
    background: $color-background;
}

.bg-area {
    fill: $color-light;
    -webkit-filter: drop-shadow(4px 6px 25px rgba(214, 224, 231, 0.5));
    filter: drop-shadow(4px 6px 25px rgba(214, 224, 231, 0.5));

    pointer-events: none;
}

@include media-breakpoint-up(md) {
    .background {
        &.background--partners {
            //padding: 68px 0;
        }
    }

    .background--story {
        padding-bottom: 68px;
    }

    .background--description {
        padding-bottom: 53px;
    }

    .background--trust {
        padding: 65px 0 32px;
        margin-bottom: 64px;
    }

    .background--extra {
        padding-bottom: 48px;
        margin-bottom: 76px;
    }

    .background--form {
        padding: 68px 0;
        margin-bottom: 76px;
    }

    .contact-decoration-form {
        padding-top: 0px;
        padding-bottom: 30px;
    }
}

@include media-breakpoint-up(lg) {
    .background {
        &.background--partners {
            //padding: 72px 0;
        }
    }

    .contact-decoration-form {
        padding-top: 14px;
        padding-bottom: 0;
    }
}

@include media-breakpoint-up(xl) {
    .background--story {
        padding-bottom: 96px;
    }

    .background--trust {
        padding: 95px 0 52px;
        margin-bottom: 96px;
    }

    .background--extra {
        margin-bottom: 96px;
    }

    .background--form {
        padding: 96px 0;
        margin-bottom: 106px;
    }
}

.caret {
    position: relative;
    display: inline-block;
    width: 4px;
    height: 4px;

    &:before {
        @include pseudo();
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        margin-top: -3px;
        margin-left: -3px;
        border-bottom: 1px solid #000;
        border-right: 1px solid #000;
        @include rotate(45);
    }
}

.decoration {
    width: 332px;
    -webkit-filter: drop-shadow(4px 6px 10px rgba(40, 40, 40, 0.5));
    filter: drop-shadow(4px 6px 10px rgba(40, 40, 40, 0.5));

    path {
        fill: $color-dark;
    }
}

.decoration-wrapper {
    position: absolute;
    z-index: -1;
    display: none;
}

.decoration--first {
    display: block;
    top: 10%;
    right: 30%;
}

.decoration--second {
    top: 25%;
    left: -30%;
    @include rotate(28.19);
}

.decoration--third {
    bottom: 0%;
    right: 0%;
    @include rotate(60);
}

.decoration--fourth {
    bottom: -30%;
    left: -40%;
    @include rotate(10.45);
}

.decoration--fifth {
    top: -25%;
    left: -15%;
    @include rotate(87.96);
    transform: rotate(87.96deg);
}

.decoration--six {
    top: 15%;
    left: 25%;
    @include rotate(28.19);
}

.decoration--seven {
    left: 0%;
    bottom: -15%;
    @include rotate(10.45);
}

@include media-breakpoint-up(lg) {
    .decoration-wrapper {
        display: block;
    }

    .decoration--first {
        top: -10%;
        right: -15%;
    }
}

// Status page
.status-done {
    background: #daeed9;
}

.status-attention {
    background: #f5ddaf;
}

.status-working {
    background: #d4c4f5;
}

.status-almost {
    background: $color-background;
}
