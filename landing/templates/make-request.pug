extends layouts/layout-shrink-make-request.pug


block page
    - var page_name = "Make request"
    - var page_description = "Video-call is a great and quick way to learn more about CleverStaff"
    - var page_type = "forms"
    - var require_assets = ['choices']
    - var darken = true


block main
    section.section--block.active
        .container
            .row
                .col-sm-10.offset-sm-1.col-md-8.offset-md-2.col-lg-7.offset-lg-5
                    h2.page-title.page-title--early
                        span.text-box.text-box--md
                            span Make a request

                    .mobile-text
                        p Describe your needs and we will prepare the best offer for you

            .row.animation.onloading
                .col-sm-10.offset-sm-1.col-md-8.offset-md-2.col-lg-5.offset-lg-6
                    form(name="form-make-request", id="form-make-request", novalidate).form
                        - var input_value = ['firstName', 'text', 'First name', 'Enter your first name', true]
                        - var error = 'Please enter your first name'
                        include elements/input.pug

                        - var input_value = ['lastName', 'text', 'Last name', 'Enter your last name', true]
                        - var error = 'Please enter your last name'
                        include elements/input.pug

                        - var tel_placeholder = ["Enter your phone number", "Phone", true]
                        - var error = 'Please enter your phone number'
                        include elements/input_tel.pug

                        - var input_value = ['email', 'email', 'Email', 'Enter your email', true]
                        - var error = 'Please enter valid email'
                        include elements/input.pug

                        - var input_value = ['companyName', 'text', 'Company', 'Company name', true]
                        - var error = 'Please enter your company name'
                        include elements/input.pug

                        - var input_value = ['requirements', 'text', 'Requirements', 'Tell us about your key requirements', false]
                        include elements/input.pug

                        fieldset.form__submit
                            button(type="submit", disabled=true).btn.btn--primary.btn--request Send a request

        .banner.banner--cta
            .banner__decoration
                .decoration-wrapper.decoration--first
                    include elements/decoration.pug

                .decoration-wrapper.decoration--second
                    include elements/decoration.pug

                .decoration-wrapper.decoration--third
                    include elements/decoration.pug

                .decoration-wrapper.decoration--fourth
                    include elements/decoration.pug

            .banner__content.banner__content--shift
                p.banner__text Describe your needs and we will prepare the best offer for you

    section.section--block
        .container
            .row
                .col-sm-10.offset-sm-1.col-md-8.offset-md-2.col-lg-7.offset-lg-5
                    h2.page-title
                        span.text-box.text-box--md
                            span Thank you!!!

                    figure.page-figure
                        include elements/celebrate.pug

                        p We have begun work on your request
                        br/ and will contact with you :)

                        script.
                            fbq('track', 'CustomizeProduct');

        .banner.banner--cta


