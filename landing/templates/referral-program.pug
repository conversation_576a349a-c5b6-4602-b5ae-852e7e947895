extends layouts/layout-referral-program.pug

block page
	- var page_name = "Referral program | CleverStaff | Professional Software for Applicant Tracking and Recruitment Automation"
	- var page_description = "CleverStaff - system for personnel selection, for recruiting"
	- var page_type = "inner"
	- var require_assets = ['scroll']
	- var src_images = isDevMode ? "/images" : "/external/images"

block main
	.page-heading.page-heading--decorative.page-heading--referral
		.page-heading__inner.d-flex.align-items-center
			.container
				.row
					.col-lg-6
						h1.page-heading__title Referral program
						p.page-heading__info Recommend CleverStaff to your colleagues and friends, 
							br
							| and receive 
							strong 100%&nbsp;
							| of their first payment

						a(href="#", data-destination="#learn-more", data-offset="50").btn.btn--primary.scroll-link Learn more
	
	.page-caption 
		.container
			.row.align-items-center
				.col-md-2
					p 
						strong Win-win!
				.col-md-6.col-xl-5
					.page-caption__item
						p Help your friends or colleagues meet the professional software for recruiting, which will exceed their expectations. 
				
				.col-md-4.offset-xl-1
					.page-caption__item.page-caption__item--last
						p In our turn, CleverStaff will reward you for each new client you lead. 
			

	.container
		section(id="learn-more").content
			h2.heading.heading--center.large-heading
				span.text-box 
					span #[mark How] it works:
				span.text-box 
					span just 5 simple #[mark steps]

			.sequence
				div(data-value="sequence-1").sequence-step
					.row.align-items-center
						.col-10.col-md-6.col-lg-7.col-xl-6.order-md-2
							.sequence-step__holder
								.sequence-step__media
									.media-space
									img(src=src_images+"/content/hiw-1.png", alt="step-1")
						
						.col-2.col-md-1.col-xl-2.order-md-1
							span.sequence-step__count 
								span.sequence-step__number
									span 1
								span.sequence-step__label step
						
						.col-md-4.col-xl-3.order-3
							.sequence-step__info
								h4 Get referral link
								
								p Open referral program page in CleverStaff and find your personal referral link

				div(data-value="sequence-2").sequence-step
					.row.align-items-center
						.col-10.col-md-6.col-lg-7.col-xl-6.order-md-2
							.sequence-step__holder
								.sequence-step__media
									.media-space
									img(src=src_images+"/content/hiw-2.png", alt="step-2")
						
						.col-2.col-md-1.col-xl-2.order-md-1
							span.sequence-step__count 
								span.sequence-step__number
									span 2
								span.sequence-step__label step

						.col-md-4.col-xl-3.order-3
							.sequence-step__info
								h4 Share
								
								p Share this link with your mates or post it anywhere else

				div(data-value="sequence-3").sequence-step
					.row.align-items-center
						.col-10.col-md-6.col-lg-7.col-xl-6.order-md-2
							.sequence-step__holder
								.sequence-step__media
									.media-space
									img(src=src_images+"/content/hiw-3.png", alt="step-3")
						
						.col-2.col-md-1.col-xl-2.order-md-1
							span.sequence-step__count 
								span.sequence-step__number
									span 3
								span.sequence-step__label step
						
						.col-md-4.col-xl-3.order-3
							.sequence-step__info
								h4 Give a reason

								p Referral uses your link to register in CleverStaff and makes 
									strong the first payment 
									| with 
									strong 10% discount

				div(data-value="sequence-4").sequence-step
					.row.align-items-center
						.col-10.col-md-6.col-lg-7.col-xl-6.order-md-2
							.sequence-step__holder
								.sequence-step__media
									.media-space
									img(src=src_images+"/content/hiw-4.png", alt="step-4")

						.col-2.col-md-1.col-xl-2.order-md-1
							span.sequence-step__count 
								span.sequence-step__number
									span 4
								span.sequence-step__label step

						.col-md-4.col-xl-3.order-3
							.sequence-step__info
								h4 Earn

								p You receive up to&nbsp;
									strong 100%
									| &nbsp;of your referral's first payment, but no more than $500

				div(data-value="sequence-5").sequence-step
					.row.align-items-center
						.col-10.col-md-6.col-lg-7.col-xl-6.order-md-2
							.sequence-step__holder
								.sequence-step__media
									.media-space
									img(src=src_images+"/content/hiw-5.png", alt="step-5")

						.col-2.col-md-1.col-xl-2.order-md-1
							span.sequence-step__count 
								span.sequence-step__number
									span 5
								span.sequence-step__label step

						.col-md-4.col-xl-3.order-3
							.sequence-step__info
								h4 Get your rewards

								p You choose the way to get your rewards. You can either transfer money to your bank account or top up your CleverStaff account.

		.row
			.col-xl-10.offset-xl-1
				.cta
					.row.align-items-center
						.col-md-7
							h4.cta__title
								span.text-box.text-box--semi 
									span Start today
							p.cta__text You will be paid for recommending excellent software for those who need it.
						
						.col-md-5.col-xl-4.offset-xl-1
							.cta__action
								.btn-holder
									a(href="#").btn.btn--primary I want to recommend
