header.header.header--line.header--fixed
	.container.container-xl
		- var src = isDevMode ? "/images" : "/external/images"
		a(href="#").menu-toggle
			svg(width="24" height="13" viewBox="0 0 24 13").menu-toggle__icon.icon-open
				rect(width="24" height="1")
				rect(y="6" width="24" height="1")
				rect(y="12" width="24" height="1")

			svg(width="14" height="14" viewBox="0 0 14 14").menu-toggle__icon.icon-close
				path(d="M14 1.41L12.59 0L7 5.59L1.41 0L0 1.41L5.59 7L0 12.59L1.41 14L7 8.41L12.59 14L14 12.59L8.41 7L14 1.41Z")

		.header__inner
			.brand
				a(href="/ro/").logo.logo--md
					.header-logo
						img(src=src_images + "/clever.png", alt="CleverStaff" class="clever")
						img(src=src_images + "/logo-text.png", alt="CleverStaff" class="logo-text")

				include ../../elements/lang/menu-ro.pug

			.controls
				.lang(id="change-lang")
					span.lang__current Ro
						span.caret(id="caret_inner")

					ul.lang__options
						li.lang__option
							a(href="/ua/") Ua
						li.lang__option
							a(href="/") En
						li.lang__option
							a(href="/pl/") Pl
						li.lang__option
							a(href="/ru/") Ru

				a(href="../ro/signin.html").login-link Conectare
					svg(width="16", height="16").icon
						use(xlink:href="#icon-user")

				.btn-group-login.btn-group--basic
					a(href="../ro/request.html", data-name="Demo", data-rename="Demo", style="background-color: white").btn.btn--secondary.btn--demo.btn-passive Demo
					.btn-active
						a(href="../ro/signup.html").btn.btn--primary Încearcă gratuit

