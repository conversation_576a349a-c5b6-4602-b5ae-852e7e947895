doctype html
html(lang='en' style='background-image: url(https://cleverstaff.net/img/header.jpg);')
    head
        meta(http-equiv='Content-Type' content='text/html; charset=UTF-8')
        meta(charset='UTF-8')
        meta(name='description' content='Экспорт кандидатов из Stff в CleverStaff')
        title Экспорт кандидатов из E-St*ff в CleverStaff
        style(type="text/css").
            .screenshot {
                width: 100%
            }
    body(style='font-weight:bold; font-family: Arial; background-color: #ffffff;\
  width: 1120px;\
  text-align: justify;\
  margin: 10px auto;\
  padding: 10px 10px !important;\
  border: 1px solid rgba(0, 0, 0, 0.5) !important;\
  margin-top: 1px;')
        h2(style='text-align: center') Пошаговая инструкция по экспорту кандидатов из E-St*ff в CleverStaff
        div(style='font-weight: normal')
            | Сценарий экспорта предназначен для выгрузки всех или отдельных типов объектов системы E-St*ff (структурных подразделений, кандидатов, вакансий и т.д.) во внутреннем XML-формате.
        div
            br
            | Шаг 1. Для создания карточки настроек нового сценария экспорта откройте раздел Сервис -> Администрирование (1)
        br
        div(style='text-align: center')
            img(src='../../images/estaff2cleverstaff/Step1.png' class='screenshot')
        hr
        br
        div
            | Шаг 2. В администрировании, пункт Справочники–> Интеграция–> Сценарии экспорта и нажмите кнопку Новый элемент (2)
        br
        div(style='text-align: center')
            img(src='../../images/estaff2cleverstaff/Step2.png' class='screenshot')
        hr
        br
        div Шаг 3. Ввести данные для создания сценария
        div(style='font-weight: normal')
            | Откроется новое окно. Введите наименование сценария (3).
            br
            | С помощью кнопки Просмотр укажите путь к папке, в которую будет производиться выгрузка (4).
            br
            span(style='color: #003bb3')
                | Для выгрузки кандидатов вместе с комментариями выберите тип объектов для выгрузки
                span(style='font-weight: bold') "Кандидат, Событие"
                |   (5). Для выбора нескольких типов объектов удерживайте кнопку Ctrl на клавиатуре.
            br
            | Значение поля Начиная с даты определяет начальный момент времени, начиная с которого будут выгружены объекты системы при следующем запуске сценария. После успешного выполнения сценария экспорта данное поле автоматически заполнится текущей датой. При необходимости эту поле можно заполнить вручную требуемой начальной датой (6).
            br
            | После заполнения всех полей нажмите кнопку “Сохранить и закрыть” (7).
        br
        div(style='text-align: center')
            img(src='../../images/estaff2cleverstaff/Step3.png' class='screenshot')
        hr
        br
        div
            | Шаг 4. В ручном режиме сценарий экспорта запускается из главного меню программы командой Сервис –> Экспорт -> [наименование сценария экспорта] (8)
        br
        div(style='text-align: center')
            img(src='../../images/estaff2cleverstaff/Step4.png' class='screenshot')
        hr
        br
        div Шаг 5. Как выглядят файлы кандидатов из E-St*ff
        br
        div(style='text-align: center')
            img(src='../../images/estaff2cleverstaff/Step5.jpg' class='screenshot')
        hr
        br
        div Шаг 6. Создаём ZIP-архив кандидатов из E-St*ff
        br
        div(style='text-align: center')
            img(src='../../images/estaff2cleverstaff/Step6.jpg' class='screenshot')
        hr
        br
        div Шаг 7. Находим страницу импорта кандидатов в CleverStaff
        br
        div(style='text-align: center')
            img(src='../../images/estaff2cleverstaff/Step7.png' class='screenshot')
        hr
        br
        div Шаг 8. Загружаем архив в CleverStaff
        br
        div(style='text-align: center')
            img(src='../../images/estaff2cleverstaff/Step8.png' class='screenshot')
        hr
        br
        div Шаг 9. Обработка файлов в архиве.
        br
        div(style='text-align: center')
            img(src='../../images/estaff2cleverstaff/Step9.png' class='screenshot')
