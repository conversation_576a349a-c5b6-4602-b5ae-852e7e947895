a(href="", style="display: none", id="closeButton", onclick="return CloseMySelf(this);")
script.
    function getParameterByName(name, url) {
        if (!url) {
            url = window.location.href;
        }
        name = name.replace(/[\[\]]/g, "\\$&");
        var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
            results = regex.exec(url);
        if (!results) return null;
        if (!results[2]) return '';
        return decodeURIComponent(results[2].replace(/\+/g, " "));
    }
    function CloseMySelf() {
        try {
            window.opener.HandlePopupResult(getParameterByName('code'));
        }
        catch (err) {}
        window.close();
        return false;
    }
    document.getElementById("closeButton").click();