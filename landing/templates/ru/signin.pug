extends ../layouts/layout-shrink-signin-ru.pug

block page
	- var page_name = "Войти"
	- var page_type = "forms"
	- var require_assets = ['']
	- var language_code = "ru"

block main
	//script.
	//	localStorage.NG_TRANSLATE_LANG_KEY = "ru"
	.container
		.row
			.col-sm-10.offset-sm-1.col-md-8.offset-md-2.offset-lg-0.col-xl-7
				h2.page-title 
					span.text-box.text-box--md 
						span Мы рады вас видеть!

		.row
			.col-sm-10.offset-sm-1.col-md-8.offset-md-2.col-lg-6.offset-lg-1.col-xl-5
				form(id="form-login", action="#", method="post").form
					.login-fields-wrapper
						small.form__description войти через

						.btn-group
							a(href="#", id="signInGoogle").btn.btn--secondary.btn--login.btn--google Google
							a(href="#", id="signInFacebook").btn.btn--secondary.btn--login.btn--facebook Facebook
							button(id="signInSAML" type="button").btn.btn--secondary.btn--login.btn--saml SAML

						small.form__description.use-email-message или использовать почту

						- var input_value = ['user-email', 'email', 'Email', 'Введите ваш email', null, 'email-wrapper']
						include ../elements/input.pug

						- var password_state = "enter"
						- var pass_label = "Пароль"
						- var pass_placeholder = "Введите ваш пароль"
						- var forgot_pass = "Забыли пароль?"
						include ../elements/input_pass.pug

					- var authCode_label = "Код авторизации"
					- var authCode_placeholder = "Введите код из Google Authenticator или из FreeOTP Authenticator"
					- var authCode_helpMessage = "Вы попросили запрашивать 6-значный код для входа, каждый раз, когда вы логинитесь в CleverStaff. Это дополнительная мера для защиты вашего аккаунта. Введите 6-значный код из приложения, которое вы выбрали при активации двухэтапной аутентификации (Google Authenticator или из FreeOTP Authenticator)."
					- var authCode_forgot = "Не можете получить код?"
					include ../elements/input_authCode.pug

					fieldset.form__submit.errorMessageWrapper
						span(id='errorLogIn').errorMessage Сейчас CleverStaff недоступен. Скоро доступ будет восстановлен. Попробуйте, пожалуйста, немного позже.

					fieldset.form__submit
						button(id="btnSignIn", type="button").btn.btn--primary Войти

					.form__next Нет аккаунта CleverStaff? 
						a(href="signup.html") Бесплатная регистрация


	.banner.banner--basic.banner--alt
		.banner__decoration
			.decoration-wrapper.decoration--fifth
				include ../elements/decoration.pug

			.decoration-wrapper.decoration--six
				include ../elements/decoration.pug

			.decoration-wrapper.decoration--seven
				include ../elements/decoration.pug

		.banner__content
			h3.banner__title 
				span.text-box.text-box--md.text-box--helper 
					span Нет аккаунта
				span.text-box.text-box--md.text-box--helper
					span CleverStaff?

			a(href="signup.html").btn.btn--secondary.btn--light Бесплатная регистрация

	.forgot-authCode-modal(id='forgot-authCode-modal')
		.forgot-authCode-modal-body
			span.forgot-authCode-modal-text Если Вы не можете получить код для входа, просьба:
				ul.default-list
					li обратиться к Администратору Вашего аккаунта для отключения  двухэтапной аутентификации;
					li обратиться в службу поддержки CleverStaff:&nbsp;
						a(href="mailto:<EMAIL>").link <EMAIL>
		.forgot-authCode-modal-footer
			button(type="submit", id='close-forgot-authCode-modal').btn.btn--primary Закрыть

	.forgot-password-modal(id='forgot-password-modal')
		.forgot-password-modal-first(id="forgot-password-modal-first")
			.forgot-password-modal-main
				.forgot-password-modal-close(id="hideForgotPasswordModal")
				span.forgot-password-modal-main-title Сбросить пароль
				form.form
					input(id='emailForResetPassword', type='email', placeholder="Введите ваш email").form__input
				div(id='errorRessetPassword') Пользователь с таким email не зарегистрирован в CleverStaff
			.forgot-password-modal-footer
				fieldset.form__submit
					button(type="submit", id='sendResetPassword', disabled=true).btn.btn--primary Отправить
		.forgot-password-modal-first(id="forgot-password-modal-second")
			.forgot-password-modal-main.forgot-password-modal-main-second
				span.forgot-password-modal-main-title Мы отправим ссылку для восстановления пароля на указанный email, если такой email зарегистрирован в CleverStaff
			.forgot-password-modal-footer
				fieldset.form__submit
					button(type="submit", id='acceptResetPassword').btn.btn--primary Oк
	.is-ie-modal(id="ie-modal")
		.is-ie-modal-close(id='close-ie-modal')
		.is-ie-modal-title Internet Explorer не подходит для работы в CleverStaff,перейдите в Chrome или Firefox
		.is-ie-modal-icons
			.is-ie-modal-icons-chrome.is-ie-lazyLoading.chrome-logo
			.is-ie-modal-icons-chrome.is-ie-lazyLoading.firefox-logo

