extends ../layouts/layout-shrink-make-request-ru.pug


block page
    - var page_name = "Сделать запрос"
    - var page_type = "forms"
    - var require_assets = ['choices']
    - var darken = true


block main
    section.section--block.active
        .container
            .row
                .col-sm-10.offset-sm-1.col-md-8.offset-md-2.col-lg-7.offset-lg-5
                    h2.page-title.page-title--early
                        span.text-box.text-box--md
                            span Сделать запрос

                    .mobile-text
                        p Опишите ваши потребности и мы подготовим лучшее предложение для вас

            .row.animation.onloading
                .col-sm-10.offset-sm-1.col-md-8.offset-md-2.col-lg-5.offset-lg-6
                    form(name="form-make-request", id="form-make-request", novalidate).form
                        - var input_value = ['firstName', 'text', 'Имя', 'Введите ваше имя', true]
                        - var error = 'Пожалуйста, введите ваше имя'
                        include ../elements/input.pug

                        - var input_value = ['lastName', 'text', 'Фамилия', 'Введите вашу фамилию', true]
                        - var error = 'Пожалуйста, введите вашу фамилию'
                        include ../elements/input.pug

                        - var tel_placeholder = ["Введите ваш телефон", "Телефон", true]
                        - var error = 'Пожалуйста, введите ваш номер телефона'
                        include ../elements/input_tel.pug

                        - var input_value = ['email', 'email', 'Email', 'Введите ваш email', true]
                        - var error = 'Пожалуйста, введите корректный email'
                        include ../elements/input.pug

                        - var input_value = ['companyName', 'text', 'Компания', 'Компания', true]
                        - var error = 'Пожалуйста, введите название Вашей компании'
                        include ../elements/input.pug

                        - var input_value = ['requirements', 'text', 'Требования', 'Расскажите нам о ваших ключевых требованиях', false]
                        include ../elements/input.pug

                        fieldset.form__submit
                            button(type="submit", disabled=true).btn.btn--primary.btn--request Отправить запрос

        .banner.banner--cta
            .banner__decoration
                .decoration-wrapper.decoration--first
                    include ../elements/decoration.pug

                .decoration-wrapper.decoration--second
                    include ../elements/decoration.pug

                .decoration-wrapper.decoration--third
                    include ../elements/decoration.pug

                .decoration-wrapper.decoration--fourth
                    include ../elements/decoration.pug

            .banner__content.banner__content--shift
                p.banner__text Опишите ваши потребности и мы подготовим лучшее предложение для вас

    section.section--block
        .container
            .row
                .col-sm-10.offset-sm-1.col-md-8.offset-md-2.col-lg-7.offset-lg-5
                    h2.page-title
                        span.text-box.text-box--md
                            span  Спасибо!!!

                    figure.page-figure
                        include ../elements/celebrate.pug

                        p Мы начали работу по вашему запросу
                        br/ и свяжемся с вами в ближайшее время :)

                        script.
                            fbq('track', 'CustomizeProduct');

        .banner.banner--cta
