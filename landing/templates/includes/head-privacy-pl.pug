- var src_images = isDevMode ? "/images" : "/external/images"
// *** General page information ***
<!--[if IE]>
meta(http-equiv="X-UA-Compatible", content="IE=edge")
<![endif]-->

meta(charset="utf-8")
title=page_name

// mobile specific metas
meta(name="viewport", content="width=device-width, initial-scale=1.0")

// website info
meta(name="author", content="")
meta(name="description", content="CleverStaff - system selekcji i rekrutacji personelu")
meta(name="keywords", content="")

// Add Micro Tasks
meta(name="twitter:card" content="summary_large_image")
meta(name="twitter:image" content="https://cleverstaff.net/external/images/previews/pl/privacy_policy.png")
meta(property="og:title", content="Polityka prywatności")
meta(property="og:type", content="website")
meta(property="og:url", content="https://cleverstaff.net/pl/privacy.html")
meta(property="og:image", content="https://cleverstaff.net/external/images/previews/pl/privacy_policy.png")
meta(property="og:site_name", content="CleverStaff")
meta(property="og:description", content="Dane klientów są szczególnie chronione dlatego, że CleverStaff ściśle przestrzega wszystkich postanowień Polityki prywatności")

// *** Fonts ***
// Roboto
link(href="https://fonts.googleapis.com/css?family=Roboto&display=swap" rel="preload")
//favicon
link(rel="apple-touch-icon", sizes="180x180", href=src_images + "/favicon/apple-touch-icon.png")
link(rel="icon", type="image/png", sizes="32x32", href=src_images + "/favicon/favicon-32x32.png")
link(rel="icon", type="image/png", sizes="16x16", href=src_images + "/favicon/favicon-16x16.png")
link(rel="manifest", href=src_images + "/favicon/site.webmanifest")
link(rel="mask-icon", href=src_images + "/favicon/safari-pinned-tab.svg", color="#5bbad5")
link(rel="alternate", hreflang="en" href="https://cleverstaff.net/privacy.html")
link(rel="alternate", hreflang="pl" href="https://cleverstaff.net/pl/privacy.html")
link(rel="alternate", hreflang="uk" href="https://cleverstaff.net/ua/privacy.html")
link(rel="alternate", hreflang="ru" href="https://cleverstaff.net/ru/privacy.html")
link(rel="alternate", hreflang="en-pl" href="https://cleverstaff.net/privacy.html")
link(rel="alternate", hreflang="en-ca" href="https://cleverstaff.net/privacy.html")
link(rel="alternate", hreflang="en-ie" href="https://cleverstaff.net/privacy.html")
link(rel="alternate", hreflang="en-us" href="https://cleverstaff.net/privacy.html")
link(rel="alternate", hreflang="en-uk" href="https://cleverstaff.net/privacy.html")
link(rel="alternate", hreflang="en-nz" href="https://cleverstaff.net/privacy.html")
link(rel="alternate", hreflang="x-default" href="https://cleverstaff.net/privacy.html")

meta(name="msapplication-TileColor", content="#da532c")
meta(name="theme-color", content="#ffffff")


// *** Stylesheets ***
// Bootstap grid system
link(rel="stylesheet", href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap-grid.min.css")

if (require_assets.indexOf('swiper') > -1)
    // Swiper slider
    link(rel="stylesheet", href="https://unpkg.com/swiper@5.4.5/css/swiper.min.css")

if (require_assets.indexOf('choices') > -1)
    // Choices
    link(rel="stylesheet", href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css")

// Primary style file
if (language_code != "ru")
    - var src = isDevMode ? "css" : "/external/css"
    link(rel="stylesheet", href=src + "/style.min.css?v=917")

if (language_code == "ru")
    - var src = isDevMode ? "../css" : "/external/css"
    link(rel="stylesheet", href=src + "/style.min.css?v=917")


// Script for Micro Tasks
script(type="application/ld+json").
    {
       "@context": "https://schema.org",
       "@type": "Organization",
       "url": "https://cleverstaff.net/",
       "name": "CleverStaff",
       "email": "<EMAIL>",
       "logo": "https://cleverstaff.net/external/images/logo.svg",
       "sameAs" : [
       "https://t.me/s/cleverstaff", "https://www.facebook.com/cleverstaff", "https://www.linkedin.com/company/cleverstaff/", "https://www.youtube.com/user/cleverstaff" ]
    }
