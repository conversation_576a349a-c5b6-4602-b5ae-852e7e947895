- var src_images = isDevMode ? "/images" : "/external/images"
// *** General page information ***
<!--[if IE]>
meta(http-equiv="X-UA-Compatible", content="IE=edge")
<![endif]-->

meta(charset="utf-8")
title=page_name

// mobile specific metas
meta(name="viewport", content="width=device-width, initial-scale=1.0")

// website info
meta(name="author", content="")
meta(name="description", content="")
meta(name="keywords", content="")

// Add Micro Tasks
meta(name="twitter:card" content="summary_large_image")
meta(name="twitter:image" content="https://cleverstaff.net/external/images/previews/en/demo.png")
meta(property="og:title", content="Rezervă o demonstrație")
meta(property="og:type", content="website")
meta(property="og:url", content="https://cleverstaff.net/ro/request.html")
meta(property="og:image", content="https://cleverstaff.net/external/images/previews/en/demo.png")
meta(property="og:site_name", content="CleverStaff")
meta(property="og:description", content="Demonstrația video este o modalitate rapidă de a verifica ce poate face sistemul de recrutare CleverStaff. Completează formularul și te vom contacta!")

// *** Fonts ***
// Roboto
link(href="https://fonts.googleapis.com/css?family=Roboto&display=swap" rel="preload")
//favicon
link(rel="apple-touch-icon", sizes="180x180", href=src_images + "/favicon/apple-touch-icon.png")
link(rel="icon", type="image/png", sizes="32x32", href=src_images + "/favicon/favicon-32x32.png")
link(rel="icon", type="image/png", sizes="16x16", href=src_images + "/favicon/favicon-16x16.png")
link(rel="manifest", href=src_images + "/favicon/site.webmanifest")
link(rel="mask-icon", href=src_images + "/favicon/safari-pinned-tab.svg", color="#5bbad5")
link(rel="alternate", hreflang="en" href="https://cleverstaff.net/request.html")
link(rel="alternate", hreflang="pl" href="https://cleverstaff.net/pl/request.html")
link(rel="alternate", hreflang="uk" href="https://cleverstaff.net/ua/request.html")
link(rel="alternate", hreflang="ru" href="https://cleverstaff.net/ru/request.html")
link(rel="alternate", hreflang="ro" href="https://cleverstaff.net/ro/request.html")
link(rel="alternate", hreflang="en-pl" href="https://cleverstaff.net/request.html")
link(rel="alternate", hreflang="en-ca" href="https://cleverstaff.net/request.html")
link(rel="alternate", hreflang="en-ie" href="https://cleverstaff.net/request.html")
link(rel="alternate", hreflang="en-us" href="https://cleverstaff.net/request.html")
link(rel="alternate", hreflang="en-uk" href="https://cleverstaff.net/request.html")
link(rel="alternate", hreflang="en-nz" href="https://cleverstaff.net/request.html")
link(rel="alternate", hreflang="x-default" href="https://cleverstaff.net/request.html")

meta(name="msapplication-TileColor", content="#da532c")
meta(name="theme-color", content="#ffffff")

// *** Stylesheets ***
// Bootstap grid system
link(rel="stylesheet", href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap-grid.min.css")

if (require_assets.indexOf('swiper') > -1)
    // Swiper slider
    link(rel="stylesheet", href="https://unpkg.com/swiper@5.4.5/css/swiper.min.css")

if (require_assets.indexOf('choices') > -1)
    // Choices
    link(rel="stylesheet", href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css")

// Primary style file
if (language_code != "ru")
    - var src = isDevMode ? "css" : "/external/css"
    link(rel="stylesheet", href=src + "/style.min.css?v=917")

if (language_code == "ru")
    - var src = isDevMode ? "../css" : "/external/css"
    link(rel="stylesheet", href=src + "/style.min.css?v=917")


// Start VWO Async SmartCode
link(rel="preconnect", href="https://dev.visualwebsiteoptimizer.com")
script(type="text/javascript", id="vwoCode").
    window._vwo_code || (function () {
        var account_id = 861903,
            version = 2.1,
            settings_tolerance = 2000,
            hide_element = 'body',
            hide_element_style = 'opacity:0 !important;filter:alpha(opacity=0) !important;background:none !important',
            /* DO NOT EDIT BELOW THIS LINE */
            f = false, w = window, d = document, v = d.querySelector('#vwoCode'),
            cK = '_vwo_' + account_id + '_settings', cc = {};
        try {
            var c = JSON.parse(localStorage.getItem('_vwo_' + account_id + '_config'));
            cc = c && typeof c === 'object' ? c : {}
        } catch (e) {
        }
        var stT = cc.stT === 'session' ? w.sessionStorage : w.localStorage;
        code = {
            use_existing_jquery: function () {
                return typeof use_existing_jquery !== 'undefined' ? use_existing_jquery : undefined
            }, library_tolerance: function () {
                return typeof library_tolerance !== 'undefined' ? library_tolerance : undefined
            }, settings_tolerance: function () {
                return cc.sT || settings_tolerance
            }, hide_element_style: function () {
                return '{' + (cc.hES || hide_element_style) + '}'
            }, hide_element: function () {
                if (performance.getEntriesByName('first-contentful-paint')[0]) {
                    return ''
                }
                return typeof cc.hE === 'string' ? cc.hE : hide_element
            }, getVersion: function () {
                return version
            }, finish: function (e) {
                if (!f) {
                    f = true;
                    var t = d.getElementById('_vis_opt_path_hides');
                    if (t) t.parentNode.removeChild(t);
                    if (e) (new Image).src = 'https://dev.visualwebsiteoptimizer.com/ee.gif?a=' + account_id + e
                }
            }, finished: function () {
                return f
            }, addScript: function (e) {
                var t = d.createElement('script');
                t.type = 'text/javascript';
                if (e.src) {
                    t.src = e.src
                } else {
                    t.text = e.text
                }
                d.getElementsByTagName('head')[0].appendChild(t)
            }, load: function (e, t) {
                var i = this.getSettings(), n = d.createElement('script'), r = this;
                t = t || {};
                if (i) {
                    n.textContent = i;
                    d.getElementsByTagName('head')[0].appendChild(n);
                    if (!w.VWO || VWO.caE) {
                        stT.removeItem(cK);
                        r.load(e)
                    }
                } else {
                    var o = new XMLHttpRequest;
                    o.open('GET', e, true);
                    o.withCredentials = !t.dSC;
                    o.responseType = t.responseType || 'text';
                    o.onload = function () {
                        if (t.onloadCb) {
                            return t.onloadCb(o, e)
                        }
                        if (o.status === 200) {
                            _vwo_code.addScript({text: o.responseText})
                        } else {
                            _vwo_code.finish('&e=loading_failure:' + e)
                        }
                    };
                    o.onerror = function () {
                        if (t.onerrorCb) {
                            return t.onerrorCb(e)
                        }
                        _vwo_code.finish('&e=loading_failure:' + e)
                    };
                    o.send()
                }
            }, getSettings: function () {
                try {
                    var e = stT.getItem(cK);
                    if (!e) {
                        return
                    }
                    e = JSON.parse(e);
                    if (Date.now() > e.e) {
                        stT.removeItem(cK);
                        return
                    }
                    return e.s
                } catch (e) {
                    return
                }
            }, init: function () {
                if (d.URL.indexOf('__vwo_disable__') > -1) return;
                var e = this.settings_tolerance();
                w._vwo_settings_timer = setTimeout(function () {
                    _vwo_code.finish();
                    stT.removeItem(cK)
                }, e);
                var t;
                if (this.hide_element() !== 'body') {
                    t = d.createElement('style');
                    var i = this.hide_element(), n = i ? i + this.hide_element_style() : '',
                        r = d.getElementsByTagName('head')[0];
                    t.setAttribute('id', '_vis_opt_path_hides');
                    v && t.setAttribute('nonce', v.nonce);
                    t.setAttribute('type', 'text/css');
                    if (t.styleSheet) t.styleSheet.cssText = n; else t.appendChild(d.createTextNode(n));
                    r.appendChild(t)
                } else {
                    t = d.getElementsByTagName('head')[0];
                    var n = d.createElement('div');
                    n.style.cssText = 'z-index: ********** !important;position: fixed !important;left: 0 !important;top: 0 !important;width: 100% !important;height: 100% !important;background: white !important;';
                    n.setAttribute('id', '_vis_opt_path_hides');
                    n.classList.add('_vis_hide_layer');
                    t.parentNode.insertBefore(n, t.nextSibling)
                }
                var o = 'https://dev.visualwebsiteoptimizer.com/j.php?a=' + account_id + '&u=' + encodeURIComponent(d.URL) + '&vn=' + version;
                if (w.location.search.indexOf('_vwo_xhr') !== -1) {
                    this.addScript({src: o})
                } else {
                    this.load(o + '&x=true')
                }
            }
        };
        w._vwo_code = code;
        code.init();
    })();
// End VWO Async SmartCode

// Script for Micro Tasks
script(type="application/ld+json").
    {
       "@context": "https://schema.org",
       "@type": "Organization",
       "url": "https://cleverstaff.net/",
       "name": "CleverStaff",
       "email": "<EMAIL>",
       "logo": "https://cleverstaff.net/external/images/logo.svg",
       "sameAs" : [
       "https://t.me/s/cleverstaff", "https://www.facebook.com/cleverstaff", "https://www.linkedin.com/company/cleverstaff/", "https://www.youtube.com/user/cleverstaff" ]
    }
