// -----------------------------------------------------------------------------------------------------------|
// - var input_value = ['id', 'type', 'input_label' 'input_placeholder', required, custom-wrapper-class]------|
// --------------------- 0 <USER> <GROUP> ----- 2 ----------- 3 <USER> <GROUP> ----------5------------------------|
//   var error = ['Hello world'] | 'Hello world'

fieldset(class=input_value[5] || '', style={'position': 'relative'}).form__group.email-input
	if (input_value[2])
		label(for=input_value[0]).form__label= input_value[2]
			if (input_value[4])
	input(name=input_value[0], id="meeting-email", type=input_value[1], placeholder=input_value[3]).form__input
	span#form__mail-indicator(style={'position': 'absolute', 'left': '81px', 'top': '8px'}).form__indicator.email-name &nbsp;*
	span#error-email Введіть правильний e-mail
	if (error)
		.form__error #{error}
