// -----------------------------------------------------------------------------------------------------------|
// - var input_value = ['id', 'type', 'input_label' 'input_placeholder', required, custom-wrapper-class]------|
// --------------------- 0 <USER> <GROUP> ----- 2 ----------- 3 <USER> <GROUP> ----------5------------------------|
//   var error = ['Hello world'] | 'Hello world'

fieldset(class=input_value[5] || '').form__group
	if (input_value[2])
		label(for=input_value[0]).form__label= input_value[2]
			if (input_value[4])
				span.form__indicator &nbsp;*
	input(name=input_value[0], id=input_value[0], type=input_value[1], placeholder=input_value[3]).form__input
	if (error)
		.form__error #{error}
