module.exports = {
    env: {
        browser: true,
        es2021: true,
    },
    extends: ['standard-with-typescript', 'plugin:react/recommended', 'plugin:react-hooks/recommended'],
    overrides: [
        {
            env: {
                node: true,
            },
            files: ['.eslintrc.{js,cjs}'],
            parserOptions: {
                sourceType: 'script',
            },
        },
    ],
    parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
    },
    plugins: ['react', 'simple-import-sort'],
    rules: {
        indent: 0,
        'multiline-ternary': 0,
        '@typescript-eslint/semi': 0,
        '@typescript-eslint/indent': 0,
        '@typescript-eslint/prefer-ts-expect-error': 0,
        '@typescript-eslint/ban-ts-ignore': 0,
        '@typescript-eslint/ban-ts-comment': 0,
        '@typescript-eslint/strict-boolean-expressions': 0,
        '@typescript-eslint/member-delimiter-style': 0,
        '@typescript-eslint/comma-dangle': 0,
        '@typescript-eslint/prefer-nullish-coalescing': 0,
        '@typescript-eslint/space-before-function-paren': 0,
        '@typescript-eslint/no-misused-promises': [
            2,
            {
                checksVoidReturn: {
                    attributes: false, // allows using async functions in JSX attributes as event handlers
                },
            },
        ],
        'simple-import-sort/imports': 'error',
        'simple-import-sort/exports': 'error',
    },
    ignorePatterns: ['webpack.config.ts'],
};
