controller.controller('PublicTestController', [
    '$scope',
    '$rootScope',
    'serverAddress',
    'Service',
    'Company',
    'notificationService',
    '$routeParams',
    'Test',
    '$interval',
    '$timeout',
    '$localStorage',
    '$location',
    '$filter',
    '$translate',
    '$window',
    '$stateParams',
    function (
        $scope,
        $rootScope,
        serverAddress,
        Service,
        Company,
        notificationService,
        $routeParams,
        Test,
        $interval,
        $timeout,
        $localStorage,
        $location,
        $filter,
        $translate,
        $window,
        $stateParams,
    ) {
        $rootScope.activePublicController = 'PublicTestController';
        $scope.loaded = false;
        $scope.isNextButtonDisabled = true;
        $scope.isQuestionLoading = false;
        $scope.showStartTest = true;
        $scope.showStartTest2 = true;
        $scope.showFirstTest = false;
        $scope.currentTab = 'start_test';
        $scope.hideTest = true;
        $scope.showEndMessage = false;
        $scope.saveAnswersTest = false;
        $scope.endTestMsg = null;
        $scope.serverAddress = serverAddress;
        $scope.checkPreviousAnswers = false;
        $scope.inHover = function () {
            $scope.showHover = true;
        };
        $scope.outHover = function () {
            $scope.showHover = false;
        };
        $timeout(function () {
            $scope.getTestFunc();
        });

        function getOrgLogo(orgId) {
            Service.getOrgLogoId({ orgId: orgId }, function (resp) {
                if (resp.status && resp.status === 'ok') {
                    $scope.companyInfo.logo = resp.object;
                }
            });
        }

        $scope.getTestFunc = function () {
            Test.openTest(
                {
                    appointmentId: $stateParams.id,
                },
                function (resp) {
                    $scope.checkPreviousAnswers = true;
                    $scope.showFirstTest = true;
                    $scope.loaded = true;
                    if (resp.status == 'ok') {
                        getOrgLogo(resp.object.orgId);
                        if ($localStorage.get('currentTab') == 'first_test') {
                            if (performance.navigation.type == 1) {
                                setTimeout(function () {
                                    $scope.startTestFunc('first_test');
                                }, 500);
                            } else {
                                console.info('This page is not reloaded');
                            }
                        }
                        $scope.showEndMessage = false;
                        $scope.showStartTest = false;
                        $scope.showStartTest2 = false;
                        $rootScope.title = resp.object.testName;
                        $scope.companyInfo = {
                            name: resp.companyName,
                            fb: resp.companyFacebookPage,
                            website: resp.companyWebSite,
                        };
                        $scope.getTestCandidate = resp.object;
                        $scope.currentLang = $translate.use() || validateNavigatorLanguage(window.navigator.language);

                        if (resp.answerCount && resp.status !== 'error') {
                            $scope.startTestFunc('first_test');
                            return;
                        }
                    } else {
                        if (resp.message == 'testErrorAlreadyPassed') {
                            $scope.showStartTest = true;
                            $scope.showStartTest2 = false;
                            $scope.endTestMsg = $filter('translate')(
                                'This test has already been taken by you. Please contact the recruiter who sent you this test link, if you want to try it again.',
                            );
                            $scope.showEndMessage = true;
                        }
                        if (resp.message == 'testErrorTestInactive') {
                            $scope.showStartTest = false;
                            $scope.showStartTest2 = true;
                            $scope.endTestMsg = $filter('translate')(
                                'This test is inactive now. Please contact the recruiter if you want to pass the test',
                            );
                            $scope.showEndMessage = true;
                        }
                        if (resp.message == 'testErrorWrongId') {
                            $scope.showStartTest = false;
                            $scope.showStartTest2 = true;
                            $scope.endTestMsg = $filter('translate')('No such appointmentId.');
                            $scope.showEndMessage = true;
                        }
                        if (resp.message == 'testErrorTimeOver') {
                            $scope.showStartTest = false;
                            $scope.showStartTest2 = true;
                            $scope.endTestMsg = $filter('translate')('Time is over');
                            $scope.showEndMessage = true;
                        }
                        getOrgLogo(resp.object.orgId);
                        $rootScope.title = resp.object.testName;
                        $scope.companyInfo = {
                            name: resp.companyName,
                            fb: resp.companyFacebookPage,
                            website: resp.companyWebSite,
                        };
                        $scope.getTestCandidate = resp.object;
                        $scope.currentLang = $translate.use() || validateNavigatorLanguage(window.navigator.language);
                    }
                },
            );
        };

        var intervalId;

        $scope.counter = 0;

        $scope.countdown = $scope.initialCountdown;
        $scope.timer = function () {
            var startTime = new Date();
            intervalId = $interval(function () {
                var actualTime = new Date();
                $scope.counter = Math.floor((actualTime - startTime) / 1000);
                $scope.countdown = $scope.initialCountdown - $scope.counter;
            }, 500);
        };

        $scope.$watch('countdown', function (countdown) {
            if (countdown === 0 || countdown < 0) {
                $scope.stopTimer();
                $('.publicTest3').hide();
                $scope.hideTest = false;
                $scope.showEndMessage = true;
                $scope.saveAnswersTest = false;
            }
        });
        $scope.stopTimer = function () {
            $interval.cancel(intervalId);
            intervalId = null;
        };
        $scope.startTimer = function () {
            if (intervalId === null) {
                $interval(function () {
                    $scope.countdown--;
                }, 1000);
            }
        };
        $scope.startTestFunc = function (tab) {
            $scope.currentTab = tab || 'start_test';
            Test.startTest(
                {
                    appointmentId: $stateParams.id,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        if (resp.object.answer) setRightAnswer(resp);

                        $scope.firstTestQuestion = resp;
                        if (resp.object.question.imageId != undefined) {
                            $scope.imageId = serverAddress + '/getPublicFile/' + resp.object.question.imageId;
                        }
                        $scope.initialCountdown = $scope.firstTestQuestion.timeLeft;
                        if ($scope.initialCountdown > 0) {
                            $scope.timer();
                        }
                        $scope.firstPage = resp.object.question.num;
                        $scope.checkPreviousAnswers = true;
                    } else {
                        if ((resp.message = 'No such appointmentId.')) {
                            $scope.currentTab = 'first_test';
                            $scope.checkPreviousAnswers = true;
                            $scope.showEndMessage = true;
                            $scope.endTestMsg = $filter('translate')('No such appointmentId.');
                        }
                        // notificationService.error(resp.message);
                    }
                },
            );
        };
        $scope.variantsAnswer = [];
        $scope.checkFewAnswer = function (text, id) {
            if ($scope.variantsAnswer.indexOf(text) === -1) {
                $scope.variantsAnswer.push(text);
            } else {
                $scope.variantsAnswer.splice($scope.variantsAnswer.indexOf(text), 1);
            }
            $scope.isNextButtonDisabled = !$scope.variantsAnswer.length;
        };
        $scope.checkOneAnswer = function (text, id) {
            $scope.checkedId = id;
            $scope.checkAnswerText = text;
            $scope.isNextButtonDisabled = false;
        };
        $scope.textAnswer = function (text) {
            $scope.textAnswers = text;
            $scope.isNextButtonDisabled = !$scope.textAnswers;
        };
        $scope.nextTestQuestion = function (next) {
            $scope.isQuestionLoading = true;
            $scope.isNextButtonDisabled = true;
            if ($scope.textAnswers) {
                Test.saveAnswer(
                    {
                        questionId: $scope.firstTestQuestion.object.question.id,
                        appointmentId: $stateParams.id,
                        text: $scope.textAnswers,
                    },
                    function (resp) {
                        if (next === 'next') {
                            $scope.getNextQuestion();
                        } else if (next === 'prev') {
                            $scope.previousTestQuestion();
                        }
                    },
                );
            } else if ($scope.variantsAnswer?.length > 0 || $scope.checkAnswerText) {
                const variantsArray = $scope.checkAnswerText ? [$scope.checkAnswerText] : $scope.variantsAnswer;
                Test.saveAnswer(
                    {
                        questionId: $scope.firstTestQuestion.object.question.id,
                        appointmentId: $stateParams.id,
                        variantsArray,
                    },
                    function (resp) {
                        if (next === 'next') {
                            $scope.getNextQuestion();
                        } else if (next === 'prev') {
                            $scope.previousTestQuestion();
                        }
                    },
                );
            } else if (next === 'prev') {
                $scope.previousTestQuestion();
            } else {
                if ($scope.firstTestQuestion.object.question.answerType === 'task_question') {
                    $scope.isNextButtonDisabled = true;
                    notificationService.error($filter('translate')('Enter your answer first text'));
                } else {
                    $scope.isNextButtonDisabled = true;
                    notificationService.error($filter('translate')('Enter your answer first'));
                }
            }
            // $scope.isQuestionLoading = false;
        };
        $scope.previousTestQuestion = function () {
            $scope.isQuestionLoading = true;
            Test.getTestsQuestion(
                {
                    testId: $scope.getTestCandidate.id,
                    questionNumber: --$scope.firstPage,
                    appointmentId: $stateParams.id,
                },
                function (resp) {
                    clearAnswers();

                    if ($scope.firstPage <= 0) $scope.firstPage = 1;
                    if (resp.status == 'ok') {
                        $scope.firstTestQuestion = resp;
                        $scope.imageId = serverAddress + '/getPublicFile/' + resp.object.question.imageId;
                        -$scope.firstPage;
                        setRightAnswer(resp);
                        $scope.hideTest = true;
                        $scope.saveAnswersTest = false;
                    } else {
                        $scope.firstPage++;
                        notificationService.error(resp.message);
                    }
                    $scope.isQuestionLoading = false;
                    $scope.isNextButtonDisabled = false;
                },
            );
        };
        $scope.endTestTimeOrSubmit = function (done) {
            $('.publicTest3').hide();
            $scope.hideTest = false;
            $scope.showEndMessage = true;
            $scope.saveAnswersTest = false;
            Test.endAppointment(
                {
                    status: 'partly_done' ? done : 'partly_done',
                    appointmentId: $stateParams.id,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                    } else {
                    }
                },
            );
        };
        $scope.getNextQuestion = function () {
            $scope.isQuestionLoading = true;
            Test.getTestsQuestion(
                {
                    testId: $scope.getTestCandidate.id,
                    questionNumber: ++$scope.firstPage,
                    appointmentId: $stateParams.id,
                },
                function (resp) {
                    clearAnswers();

                    if (resp.message == 'There is no question with such number.') {
                        $scope.hideTest = false;
                        $scope.saveAnswersTest = true;
                    }
                    if (resp.status == 'ok') {
                        $scope.firstTestQuestion = resp;
                        if (resp.object.question?.imageId != undefined) {
                            $scope.imageId = serverAddress + '/getPublicFile/' + resp.object.question.imageId;
                        }
                        +$scope.firstPage;
                        $('#answersText').val('');
                        var rightAnswer = {};
                        angular.copy(resp.object.answer, rightAnswer);
                        if (rightAnswer != null) {
                            if (rightAnswer.variantsArray != undefined) {
                                var answer = {};
                                angular.copy(resp.object.question, answer);
                                if (answer.answerType == 'few_answers') {
                                    $scope.variantsAnswer = [];
                                    angular.forEach(rightAnswer.variantsArray, function (val) {
                                        $scope.variantsAnswer.push(val);
                                        $scope.checkAnswerText = null;
                                        $scope.textAnswers = undefined;
                                    });
                                } else if (answer.answerType == 'one_answer') {
                                    $scope.checkAnswerText = null;
                                    angular.forEach(rightAnswer.variantsArray, function (val) {
                                        $scope.checkAnswerText = val;
                                        $scope.variantsAnswer = undefined;
                                        $scope.textAnswers = undefined;
                                    });
                                }
                                $scope.isNextButtonDisabled = false;
                            } else if (rightAnswer.text != undefined) {
                                $scope.textAnswers = rightAnswer.text;
                                $scope.variantsAnswer = undefined;
                                $scope.checkAnswerText = undefined;
                                $('#answersText').val($scope.textAnswers);
                                $scope.isNextButtonDisabled = false;
                            } else {
                            }
                        } else {
                            $scope.isNextButtonDisabled = true;
                            $scope.checkAnswerText = null;
                        }
                    } else {
                        $scope.firstPage--;
                        notificationService.error(resp.message);
                    }
                    $scope.isQuestionLoading = false;
                },
            );
        };

        function clearAnswers() {
            $scope.variantsAnswer = [];
            $scope.checkAnswerText = null;
            $scope.textAnswers = null;
        }

        function setRightAnswer(resp) {
            var rightAnswer = {};
            angular.copy(resp.object.answer, rightAnswer);
            if (rightAnswer != null) {
                if (rightAnswer.variantsArray != undefined) {
                    $scope.variantsAnswer = [];
                    angular.forEach(rightAnswer.variantsArray, function (val) {
                        var answer = {};
                        angular.copy(resp.object.question, answer);
                        if (answer.answerType == 'few_answers') {
                            $scope.variantsAnswer = [];
                            angular.forEach(rightAnswer.variantsArray, function (val) {
                                $scope.variantsAnswer.push(val);
                                $scope.checkAnswerText = undefined;
                                $scope.textAnswers = undefined;
                            });
                        } else if (answer.answerType == 'one_answer') {
                            $scope.checkAnswerText = [];
                            angular.forEach(rightAnswer.variantsArray, function (val) {
                                $scope.checkAnswerText = val;
                                $scope.variantsAnswer = undefined;
                                $scope.textAnswers = undefined;
                            });
                        }
                    });
                } else if (rightAnswer.text != undefined) {
                    $scope.textAnswers = rightAnswer.text;
                    $('#answersText').val($scope.textAnswers);
                }
            }
        }
        $scope.showModalImage = function () {
            $('#question-modal').removeClass('hidden');
            $('#question-modal').addClass('visible');
        };
        $scope.closeModalImage = function () {
            $('#question-modal').removeClass('visible');
            $('#question-modal').addClass('hidden');
        };

        function validateNavigatorLanguage(language) {
            switch (language) {
                case 'ru-RU':
                    return 'ru';
                default:
                    return language;
            }
        }
    },
]);
/*** Created by вик on 31.05.2017.*/
