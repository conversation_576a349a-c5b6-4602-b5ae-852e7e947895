controller.controller('PublicCandidateController', [
    '$scope',
    'Service',
    '$routeParams',
    '$rootScope',
    'serverAddress',
    '$stateParams',
    'Person',
    '$translate',
    '$http',
    function ($scope, Service, $routeParams, $rootScope, serverAddress, $stateParams, Person, $translate, $http) {
        $rootScope.activePublicController = 'PublicCandidateController';
        $scope.pageObject = {
            loading: true,
            showInformation: true,
        };
        $scope.error = { show: false, notFound: false };

        const showCountry = [
            'ru',
            'ru-RU',
            'uk-UK',
            'uk',
            'be',
            'be-BE',
            'mo',
            'mo-MO',
            'kz',
            'kz-KZ',
            'kk',
            'kk-KK',
            'uz',
            'uz-UZ',
            'ky',
            'ky-Ky',
        ];
        $rootScope.americanStyle = true;

        showCountry.forEach((country) => {
            if (country === navigator.language) {
                $rootScope.americanStyle = false;
            }
        });

        Service.publicCandidate(
            { id: $stateParams.candidateId },
            function (resp) {
                if (resp.status === 'ok') {
                    $scope.pageObject.loading = false;
                    $scope.candidate = resp.object;
                    $scope.displayCenterColumn =
                        $scope.candidate.descr ||
                        $scope.candidate.educationList.length ||
                        $scope.candidate.workExperiences.length;
                    $http
                        .get(serverAddress + '/person/getMe')
                        .then(function ({ data }) {
                            if (data.status === 'ok') {
                                localStorage.setItem('NG_TRANSLATE_LANG_KEY', data.object.personParams.lang);
                                setUserLang(data.object.personParams.lang);
                            }
                        })
                        .catch((error) => console.error(error))
                        .finally(() => getLangs());

                    if (resp.object.candidateSkills !== undefined) {
                        $scope.candidateSkills = resp.object.candidateSkills.map((skillObj) => skillObj.skillName);
                    }

                    if (resp.object.education != undefined) {
                        $('#candidateEducation').html(resp.object.education);
                    }
                    $rootScope.title = resp.object.fullName + ' | CleverStaff';
                    if (
                        resp.object.region != undefined &&
                        resp.object.region.lat != undefined &&
                        resp.object.region.lng != undefined
                    ) {
                        $scope.map.center.latitude = resp.object.region.lat;
                        $scope.map.center.longitude = resp.object.region.lng;

                        $scope.marker.coords.latitude = resp.object.region.lat;
                        $scope.marker.coords.longitude = resp.object.region.lng;
                    }

                    Service.getOrgLogoId({ orgId: resp.object.orgId }, function (logoResp) {
                        if (logoResp.status === 'ok') {
                            $scope.companyLogo = logoResp.object;
                            if (serverAddress === '/hr') {
                                $scope.companyLogo
                                    ? ($scope.logoLink = '/hr/getlogo?id=' + $scope.companyLogo + '')
                                    : null;
                            } else {
                                scope.companyLogo
                                    ? ($scope.logoLink = '/hr/getlogo?id=' + $scope.companyLogo + '')
                                    : ($scope.logoLink = null);
                            }
                        }
                    });
                } else {
                    $scope.isVisible = true;
                    if (resp.code === 'notFound') {
                        $scope.error = {
                            show: true,
                            notFound: true,
                        };
                    }
                    $scope.pageObject.showInformation = false;
                    $scope.pageObject.loading = false;
                }
            },
            function (respError) {
                $scope.pageObject.showInformation = false;
                $scope.pageObject.loading = false;
            },
        );
        $scope.serverAddress = serverAddress;

        $scope.map = {
            center: {
                latitude: 48.379433,
                longitude: 31.165579999999977,
            },
            zoom: 5,
            options: {
                panControl: true,
                zoomControl: true,
                scaleControl: true,
                mapTypeControl: true,
                //mapTypeId: "roadmap"
            },
        };

        $scope.showHideMap = function () {
            $scope.showRegion2Map = !$scope.showRegion2Map;
        };
        $scope.marker = {
            id: 1,
            title: '',
            coords: {
                latitude: null,
                longitude: null,
            },
        };

        function transformLanguages(langs) {
            if (!langs) return;
            return langs.map((lang) => {
                return {
                    ...lang,
                    name: $rootScope.allLanguages[lang.name][$rootScope.currentLang],
                };
            });
        }

        function setUserLang(lang) {
            const defaultLang = localStorage.getItem('NG_TRANSLATE_LANG_KEY') || navigator.language;

            $rootScope.currentLang = lang || defaultLang;
            $translate.use($rootScope.currentLang);
        }

        function getLangs() {
            if (!$rootScope.currentLang) {
                setUserLang();
                translateRegion();
            }
            Service.onGetLanguages().then((resp) => {
                $rootScope.allLanguages = {};
                resp.objects.forEach((item) => {
                    $rootScope.allLanguages[item.key] = {
                        ru: item.translation.russian || null,
                        en: item.translation.english || null,
                        ua: item.translation.ukrainian || null,
                        pl: item.translation.polish || null,
                    };
                });
                $scope.candidate.languages = transformLanguages($scope.candidate.languages);
                $scope.isVisible = true;
                $rootScope.$$phase || $scope.$apply();
            });
        }

        function translateRegion() {
            if (!$scope.candidate.region) return;
            const lang = $rootScope.currentLang[0].toUpperCase() + $rootScope.currentLang.slice(1);
            const country = $scope.candidate.region.googlePlaceId[`country${lang}`];
            const city = $scope.candidate.region.googlePlaceId[`city${lang}`];
            $scope.candidate.region.displayFullName = city ? `${city}, ${country}` : country;
        }
    },
]);
