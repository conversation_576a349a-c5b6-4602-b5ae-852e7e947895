@import 'js/common/styles/global-variables.module';

//Svg Icon Mask
@mixin icon-mask($icon-path, $icon-color, $size: 18px) {
    background-color: $icon-color;
    mask: url($icon-path) no-repeat;
    mask-size: cover;
    width: $size;
    height: $size;
}
//

//Borders
@mixin border-small {
    border: 1px solid $border-grey;
    border-radius: 8px;
}

@mixin border-large {
    border: 1px solid $border-grey;
    border-radius: 12px;
}

@mixin border-error {
    border-color: $red;
    &:hover {
        border-color: $red;
    }
}
//

// Links
@mixin link-blue {
    text-decoration: none;
    color: $blue;
    cursor: pointer;

    &:hover {
        color: $yellow;
    }
}

@mixin link-green {
    text-decoration: none;
    color: $main-green;
    cursor: pointer;

    &:focus {
        color: $main-green;
    }

    &:hover {
        color: $yellow;
    }
}

@mixin link-no-color {
    text-decoration: none;
    color: $main-black;
    cursor: pointer;

    &:hover {
        color: $main-black;
    }
}
//

//Tooltips
@mixin tooltip-light {
    padding: 8px;
    background-color: $main-white;
    border: 1px solid $border-grey;
    border-radius: 4px;
    box-shadow: $main-box-shadow;
}
//

//Dropdowns elements
@mixin control-icon-size {
    width: 18px;
    height: 18px;
}
//
