import * as React from 'react';
import Select, { type Props as SelectProps } from 'react-select';

import MenuList from './MenuList';
import { calcOptionsLength } from './util';

interface WindowedSelectProps extends SelectProps {
    windowThreshold: number;
    isPlaceAbove?: any;
    selectAllText?: string;
}
const WindowedSelectFixed = ({ windowThreshold = 100, ...passedProps }: WindowedSelectProps, ref: any) => {
    const optionsLength = React.useMemo(() => calcOptionsLength(passedProps.options), [passedProps.options]);
    const isWindowed = optionsLength >= windowThreshold;

    return (
        <Select
            {...passedProps}
            components={{
                ...passedProps.components,
                ...(isWindowed ? { MenuList } : {}),
            }}
            ref={ref}
        />
    );
};

export default React.forwardRef(WindowedSelectFixed);
