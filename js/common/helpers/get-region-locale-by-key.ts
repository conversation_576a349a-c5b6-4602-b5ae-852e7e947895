import type { Locales } from 'common/enums/locales';
import { Region } from 'common/types/candidate';

export const getRegionLocaleByKey = (currentLang: Locales, type: string, region: Region): string | undefined => {
    if (type === 'country') {
        switch (currentLang) {
            case 'en':
                return region?.googlePlaceId.countryEn;
            case 'ua':
                return region?.googlePlaceId.countryUa;
            case 'pl':
                return region?.googlePlaceId.countryPl;
            case 'ru':
                return region?.googlePlaceId.countryRu;
        }
    } else {
        switch (currentLang) {
            case 'en':
                return region?.googlePlaceId.cityEn;
            case 'ua':
                return region?.googlePlaceId.cityUa;
            case 'pl':
                return region?.googlePlaceId.cityPl;
            case 'ru':
                return region?.googlePlaceId.cityRu;
        }
    }
};

export const getRegionLocale = (currentLang: Locales, type: string, region: any): string | undefined => {
    if (type === 'country') {
        switch (currentLang) {
            case 'en':
                return region?.countryEn;
            case 'ua':
                return region?.countryUa;
            case 'pl':
                return region?.countryPl;
            case 'ru':
                return region?.countryRu;
        }
    } else {
        switch (currentLang) {
            case 'en':
                return region?.cityEn;
            case 'ua':
                return region?.cityUa;
            case 'pl':
                return region?.cityPl;
            case 'ru':
                return region?.cityRu;
        }
    }
};
