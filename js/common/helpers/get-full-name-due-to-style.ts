import type { AngularRootScope } from 'common/types/angular-types';
// @ts-ignore
import { getService } from 'react-in-angularjs';

/**
 * Returns fullName or fullNameEn depending on $rootScope.useAmericanNameStyle
 * @param { {fullName: string, fullNameEn: string} } personLikeObj
 * @return { string }
 */
export const getFullNameDueToStyle = (personLikeObj: { fullName: string; fullNameEn: string }): string => {
    if (!personLikeObj) return '';
    const { useAmericanNameStyle } = getService('$rootScope') as AngularRootScope;

    return useAmericanNameStyle ? personLikeObj?.fullNameEn : personLikeObj?.fullName;
};
