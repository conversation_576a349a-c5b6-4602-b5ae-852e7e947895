import type React from 'react';
import type { HTMLInputTypeAttribute } from 'react';

interface InputPropsType {
    value: string | undefined;
    onChange?: (e: string, additionalArg?: any) => void;
    onBlur?: (e: React.FocusEvent<Element>, additionalArg?: any) => void;
    checked?: boolean;
    placeholder?: string;
    type?: HTMLInputTypeAttribute;
    min?: string;
    max?: string;
    disabled?: boolean;
    additionalArg?: any;
    isClearable?: boolean;
    isError?: boolean;
    currentLang?: string;
    className?: string;
    iconName?: string;
}

export type { InputPropsType };
