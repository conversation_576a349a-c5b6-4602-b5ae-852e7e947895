class jobSitesIntegrationComponentCtrl {
    constructor(integrationPageService, notificationService, $scope, $rootScope, $localStorage) {
        this.integrationPageService = integrationPageService;
        this.notificationService = notificationService;
        this.$scope = $scope;
        this.$rootScope = $rootScope;
        this.$localStorage = $localStorage;

        this.delucruCountryPermission = ['Ukraine', 'Moldova', 'Romania'].includes(
            this.$rootScope.me.orgParams.registrationCountry,
        );

        window.onstorage = (e) => {
            if (e.newValue === 'workUADisabled') this.$scope.$broadcast('workChanged', false);
            if (e.newValue === 'workUAEnabled') this.$scope.$broadcast('workChanged', true);
            if (e.newValue === 'rabotaUADisabled') this.$scope.$broadcast('rabotaChanged', false);
            if (e.newValue === 'rabotaUAEnabled') this.$scope.$broadcast('rabotaChanged', true);
            if (e.newValue === 'djinniDisabled') this.$scope.$broadcast('djinniChanged', false);
            if (e.newValue === 'djinniEnabled') this.$scope.$broadcast('djinniChanged', true);
            if (e.newValue === 'hhDisabled') this.$scope.$broadcast('hhChanged', false);
            if (e.newValue === 'hhEnabled') this.$scope.$broadcast('hhChanged', true);
            this.$scope.$apply();
        };
    }

    $onInit() {
        this.vacancy = this.parent.vacancy;
        this.integrationPageService
            .checkIntegration()
            .then((resp) => (this.integrationPayload = resp.object))
            .catch((err) => this.notificationService.error(err.message))
            .finally(() => this.$scope.$apply());
    }

    checkIntegrationFromDelucru = () => {
        this.$onInit();
    };

    /**
     * @param message {'vacancy_changed'}
     */
    onDelucruCPChange = (message) => {
        if (message === 'vacancy_changed') {
            this.$rootScope.$emit('eventUpdateVacancyDescriptionData', {});
        }
    };
}

const jobSitesIntegrationDefinition = {
    bindings: {},
    require: {
        parent: '^vacancyDescription',
    },
    templateUrl: 'partials/jobSitesIntegration/jobSitesIntegration.html',
    controller: jobSitesIntegrationComponentCtrl,
    controllerAs: 'vm',
};

component.component('jobSitesIntegrationComponent', jobSitesIntegrationDefinition);
