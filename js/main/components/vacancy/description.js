component.component('vacancyDescription', {
    templateUrl: 'partials/vacancy/description.html',
    require: {
        parent: '^vacancyComponent',
    },
    controller: function (
        Vacancy,
        $rootScope,
        $scope,
        $uibModal,
        $location,
        $stateParams,
        $state,
        $filter,
        $translate,
        localStorageService,
        $timeout,
        $window,
        serverAddress,
        Mail,
        Person,
        notificationService,
        Service,
        Company,
        FileInit,
        $localStorage,
        File,
        Email,
        LIIntegrationService,
        vacancyStages,
        $sce,
        Candidate,
        prioritiesOptions,
    ) {
        const vm = this;

        var frontMode;

        vm.visibility = false;
        $rootScope.fileForSave = [];
        vm.publicDescr = '';
        vm.persons = [];
        vm.personId = $rootScope.me.personId;
        vm.changeStateObject = { status: '', comment: '', placeholder: null };
        vm.visibilityCopyVacancy = false;
        vm.copyCandidate = false;
        vm.shareObj = {};
        vm.meObject = null;
        vm.selectedEmail = {};
        vm.status = [
            { value: 'open', label: 'open', color: '#77B472' },
            { value: 'inwork', label: 'inwork', color: '#77B472' },
            { value: 'onhold', label: 'onhold', color: '#F5C620' },
            { value: 'payment', label: 'payment', color: '#F5C620' },
            { value: 'replacement', label: 'replacement', color: '#F5C620' },
            { value: 'canceled', label: 'canceled', color: '#F5C620' },
            { value: 'recommendation', label: 'recommendation', color: '#F5C620' },
        ];
        vm.typesOfRecruitersForHM = [
            { label: 'recruiter', type: 'recruiter' },
            { label: 'interviewer', type: 'interviewer' },
            { label: 'researcher', type: 'researcher' },
            { label: 'hiringManager', type: 'hiringManager' },
        ];

        vm.priorities = prioritiesOptions;
        vm.isBlurToRemovePromoLogo = false;
        vm.statistickSwitcher = false;
        vm.onChangePriority = function (model) {
            $rootScope.$emit('setVacancyPriority', { model: model });
        };
        vm.statusAssoc = Vacancy.getStatusAssociated();
        vm.customStringValueClassToggle = false;
        $rootScope.statusInter = Vacancy.getInterviewStatus();
        vm.changePriorityListener = $rootScope.$on('setVacancyPriority', (e, data) => {
            $rootScope.loading = true;
            Vacancy.onSetPriority({
                vacancyId: $rootScope.vacancy.vacancyId,
                priority: data.model.value,
            })
                .then((resp) => {
                    if (resp.status === 'ok') {
                        $rootScope.currentVacancyPriority = vm.vacancy.priority;
                        vm.priorityModel = data.model;
                        notificationService.success($filter('translate')('Priority changed'));
                        $rootScope.$$phase || $scope.$apply();
                    }
                })
                .catch((err) => {
                    vm.vacancy.priority = $rootScope.currentVacancyPriority;
                    console.error(err);
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        });
        $scope.$on('$destroy', function () {
            vm.changePriorityListener();
        });

        vm.callbackAddPromoLogo = function (photo) {
            if (photo) {
                $('#owner_photo_wrap').css('width', '13%');
                $rootScope.promoLogo = photo;
                if ($rootScope.promoLogo) {
                    vm.promoLogoLink = $rootScope.promoLogo
                        ? $location.$$protocol +
                          '://' +
                          $location.$$host +
                          vm.serverAddress +
                          '/getlogo?id=' +
                          $rootScope.promoLogo +
                          '&d=true'
                        : null;
                } else {
                    vm.promoLogoLink = 'https://cleverstaff.net/images/sprite/vacancy-new.jpg';
                }
            } else {
                $('#owner_photo_wrap').css('width', '100%');
            }
        };
        vm.addEmailInDescriptionFromLocalStorage = function (email) {
            angular.forEach($rootScope.me.emails, function (resp) {
                if (resp.email === localStorage.emailThatAlreadyUsed) {
                    vm.addEmailFromWhatSendInDescription(resp);
                }
            });
        };
        vm.closeModal = function () {
            vm.modalInstance.close();
        };
        vm.closeCommentVacancyModal = function () {
            vm.closeModal();
            vm.changeStateObject.comment = '';
        };
        vm.closeAddingResponsileForVacancyModal = function () {
            vm.closeModal();
            $rootScope.changeResponsibleInVacancy.comment = '';
        };
        vm.hoverInfoHidden = function (flag) {
            if (flag === 'one') {
                vm.visibility = false;
            } else if (flag === 'two') {
                vm.visibility2 = false;
            }
            vm.visibilityCopyVacancy = false;
        };
        vm.showAllDescription = function () {
            vm.showMoreButton = vm.customStringValueClassToggle ? 'Show' : 'Hide';
            vm.customStringValueClassToggle = !vm.customStringValueClassToggle;
        };
        vm.getParsedCustomStringField = function (value) {
            if (!value) return;
            return value.replace(/(<([^>]+)>)/gi, '');
        };
        vm.hoverInfoShow = function (flag, index) {
            var text = [
                'Use this template to send the interviw invitation & details when you move candidates to job stages with an interview.',
                'Use this template to describe candidates that thay do not meet the vacancy criteria.',
                'Use this template to send your candidates the letter with the vacancy proposal',
                'Use this template on the “Offer accepted” stage to send all the needed details and the date of start to your new employee',
            ];

            if (flag === 'one') {
                vm.visibility = true;
            } else if (flag === 'two') {
                vm.visibility2 = true;
                vm.message = text[index];
            }
            vm.visibilityCopyVacancy = true;
        };
        vm.copyVacancyModal = function () {
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/copy-vacancy-to-vacancy.html',
                size: '',
                resolve: function () {},
            });
        };
        vm.share = function (sourse) {
            var link = $location.$$protocol + '://' + $location.$$host + '/i/vacancy-' + vm.vacancy.localId;
            if (frontMode === 'demo') {
                link = $location.$$protocol + '://' + $location.$$host + '/di#/vacancy-' + vm.vacancy.localId;
            }
            if (sourse === 'linkedin') {
                LIIntegrationService.shareVacancy(vm.vacancy.vacancyId);
            } else if (sourse === 'facebook') {
                if (!FB) return;

                Person.changeUserParam({ name: 'facebookPreviewCachePopup', value: 'N' }, function (resp) {
                    if (resp.status == 'ok') {
                        $rootScope.updateMe(true);
                        vm.closeModal();
                    } else {
                        notificationService.error(resp.message);
                    }
                });
                FB.getLoginStatus(function (response) {
                    var setinterval = setInterval(() => {
                        let frame = document.querySelector('.FB_UI_Dialog');
                        if (frame) {
                            frame.setAttribute('width', '600px');
                            frame.setAttribute('style', 'min-width:600px;');
                            frame.style.minWidth = '600px !important';
                            frame.style.width = '600px !important';
                            clearInterval(setinterval);
                        }
                    }, 1000);
                    if (response.status === 'connected' || response.status === 'unknown') {
                        FB.ui(
                            {
                                method: 'share_open_graph',
                                display: 'popup',
                                action_type: 'og.shares',
                                action_properties: JSON.stringify({
                                    object: {
                                        'og:url': link,
                                        'og:title': $filter('translate')('Vacancy') + ' ' + vm.vacancy.position,
                                        'og:description': $filter('limitTo')(vm.publicDescr, 100, 0),
                                        'og:image': getVacancyPromoLogo(),
                                    },
                                }),
                            },
                            function (response) {
                                if (!response.error_message) {
                                    Vacancy.addPublish(
                                        {
                                            vacancyId: vm.vacancy.vacancyId,
                                            type: 'facebook_page',
                                        },
                                        (resp) =>
                                            resp.status === 'ok'
                                                ? (vm.vacancy.publishingCount = resp.object.publishingCount)
                                                : console.error('SMTHGOWRONG'),
                                    );
                                } else if (response.error_message) {
                                    notificationService.error($filter('translate')('Vacancy was not shared'));
                                }
                            },
                        );
                    } else {
                        FB.login(function (response) {
                            if (response.authResponse) {
                                FB.ui(
                                    {
                                        method: 'share_open_graph',
                                        display: 'popup',
                                        action_type: 'og.shares',
                                        action_properties: JSON.stringify({
                                            object: {
                                                'og:url': link,
                                                'og:title': $filter('translate')('Vacancy') + ' ' + vm.vacancy.position,
                                                'og:description': $filter('limitTo')($scope.publicDescr, 100, 0),
                                                'og:image': getVacancyPromoLogo(),
                                            },
                                        }),
                                    },
                                    function (response) {
                                        if (response.error_message) {
                                            notificationService.error($filter('translate')('Vacancy was not shared'));
                                        }
                                    },
                                );
                            }
                        });
                    }
                });
            }
        };
        vm.removeFile = function (id) {
            Vacancy.removeFile({ vacancyId: vm.vacancy.vacancyId, fileId: id }, function (resp) {
                if (resp.status == 'ok') {
                }
            });
            angular.forEach(vm.vacancy.files, function (val, ind) {
                if (val.fileId === id) {
                    $('#file-vacancy').val('');
                    vm.vacancy.files.splice(ind, 1);
                }
            });
            angular.forEach($rootScope.fileForSave, function (val, ind) {
                if (val.fileId === id) {
                    $rootScope.fileForSave.splice(ind, 1);
                }
            });
        };
        vm.showEditFileNameFunc = function (file) {
            file.showEditFileName = !file.showEditFileName;
            file.showMenuEdDelFile = !file.showMenuEdDelFile;
            vm.showMenuEdDelFile = false;
        };
        vm.onDownloadFile = function (file) {
            file.showMenuEdDelFile = !file.showMenuEdDelFile;
            $rootScope.downloadFile(file.fileId, file.fileName);
        };
        vm.MenuEdDelFile = function (file) {
            file.showMenuEdDelFile = !file.showMenuEdDelFile;
            $('body').mouseup(function (e) {
                if ($('.editFileMenu').has(e.target).length === 0) {
                    // file.showMenuEdDelFile = false;
                    if (!$scope.$$phase) $scope.$apply();
                }
            });
        };

        vm.testCountryFromNumber = function () {
            $scope.personContacts = {};
            $scope.mphoneInCountry = false;
            $scope.phoneWorkInCountry = false;
            $scope.phoneMobInCountry = false;
            if ($rootScope.me.contacts.length > 0) {
                $rootScope.me.contacts.forEach((contact) => {
                    switch (contact.contactType) {
                        case 'mphone':
                            $scope.mphone_four = contact.value.substring(0, 4);
                            $scope.mphone_three = contact.value.substring(0, 3);

                            if (
                                $scope.mphone_four === '+380' ||
                                $scope.mphone_four === '+093' ||
                                $scope.mphone_four === '+073' ||
                                $scope.mphone_four === '+063' ||
                                $scope.mphone_four === '+067' ||
                                $scope.mphone_four === '+066' ||
                                $scope.mphone_four === '+050' ||
                                $scope.mphone_four === '+095' ||
                                $scope.mphone_four === '+099' ||
                                $scope.mphone_four === '+096' ||
                                $scope.mphone_four === '+098' ||
                                $scope.mphone_four === '+097'
                            ) {
                                $scope.personContacts.mphone = $scope.mphone_four;
                            }

                            if (
                                $scope.mphone_three === '380' ||
                                $scope.mphone_three === '093' ||
                                $scope.mphone_three === '073' ||
                                $scope.mphone_three === '063' ||
                                $scope.mphone_three === '067' ||
                                $scope.mphone_three === '066' ||
                                $scope.mphone_three === '050' ||
                                $scope.mphone_three === '095' ||
                                $scope.mphone_three === '099' ||
                                $scope.mphone_three === '096' ||
                                $scope.mphone_three === '098' ||
                                $scope.mphone_three === '097'
                            ) {
                                $scope.personContacts.mphone = $scope.mphone_three;
                            }

                            break;
                        case 'phoneWork':
                            $scope.phoneWork_four = contact.value.substring(0, 4);
                            $scope.phoneWork_three = contact.value.substring(0, 3);

                            if (
                                $scope.phoneWork_four === '+380' ||
                                $scope.phoneWork_four === '+093' ||
                                $scope.phoneWork_four === '+073' ||
                                $scope.phoneWork_four === '+063' ||
                                $scope.phoneWork_four === '+067' ||
                                $scope.phoneWork_four === '+066' ||
                                $scope.phoneWork_four === '+050' ||
                                $scope.phoneWork_four === '+095' ||
                                $scope.phoneWork_four === '+099' ||
                                $scope.phoneWork_four === '+096' ||
                                $scope.phoneWork_four === '+098' ||
                                $scope.phoneWork_four === '+097'
                            ) {
                                $scope.personContacts.phoneWork = $scope.phoneWork_four;
                            }

                            if (
                                $scope.phoneWork_three === '380' ||
                                $scope.phoneWork_three === '093' ||
                                $scope.phoneWork_three === '073' ||
                                $scope.phoneWork_three === '063' ||
                                $scope.phoneWork_three === '067' ||
                                $scope.phoneWork_three === '066' ||
                                $scope.phoneWork_three === '050' ||
                                $scope.phoneWork_three === '095' ||
                                $scope.phoneWork_three === '099' ||
                                $scope.phoneWork_three === '096' ||
                                $scope.phoneWork_three === '098' ||
                                $scope.phoneWork_three === '097'
                            ) {
                                $scope.personContacts.phoneWork = $scope.phoneWork_three;
                            }
                            break;
                        case 'phoneMob':
                            $scope.phoneMob_four = contact.value.substring(0, 4);
                            $scope.phoneMob_three = contact.value.substring(0, 3);

                            if (
                                $scope.phoneMob_four === '+380' ||
                                $scope.phoneMob_four === '+093' ||
                                $scope.phoneMob_four === '+073' ||
                                $scope.phoneMob_four === '+063' ||
                                $scope.phoneMob_four === '+067' ||
                                $scope.phoneMob_four === '+066' ||
                                $scope.phoneMob_four === '+050' ||
                                $scope.phoneMob_four === '+095' ||
                                $scope.phoneMob_four === '+099' ||
                                $scope.phoneMob_four === '+096' ||
                                $scope.phoneMob_four === '+098' ||
                                $scope.phoneMob_four === '+097'
                            ) {
                                $scope.personContacts.phoneWork = $scope.phoneMob_four;
                            }

                            if (
                                $scope.phoneMob_three === '380' ||
                                $scope.phoneMob_three === '093' ||
                                $scope.phoneMob_three === '073' ||
                                $scope.phoneMob_three === '063' ||
                                $scope.phoneMob_three === '067' ||
                                $scope.phoneMob_three === '066' ||
                                $scope.phoneMob_three === '050' ||
                                $scope.phoneMob_three === '095' ||
                                $scope.phoneMob_three === '099' ||
                                $scope.phoneMob_three === '096' ||
                                $scope.phoneMob_three === '098' ||
                                $scope.phoneMob_three === '097'
                            ) {
                                $scope.personContacts.phoneMob = $scope.phoneMob_three;
                            }
                            break;
                    }
                });
            }

            if (
                $scope.personContacts.mphone === '380' ||
                $scope.personContacts.mphone === '+380' ||
                $scope.personContacts.mphone === '093' ||
                $scope.personContacts.mphone === '073' ||
                $scope.personContacts.mphone === '063' ||
                $scope.personContacts.mphone === '067' ||
                $scope.personContacts.mphone === '066' ||
                $scope.personContacts.mphone === '050' ||
                $scope.personContacts.mphone === '095' ||
                $scope.personContacts.mphone === '099' ||
                $scope.personContacts.mphone === '096' ||
                $scope.personContacts.mphone === '098' ||
                $scope.personContacts.mphone === '097' ||
                $scope.personContacts.mphone === '+093' ||
                $scope.personContacts.mphone === '+073' ||
                $scope.personContacts.mphone === '+063' ||
                $scope.personContacts.mphone === '+067' ||
                $scope.personContacts.mphone === '+066' ||
                $scope.personContacts.mphone === '+050' ||
                $scope.personContacts.mphone === '+095' ||
                $scope.personContacts.mphone === '+099' ||
                $scope.personContacts.mphone === '+096' ||
                $scope.personContacts.mphone === '+098' ||
                $scope.personContacts.mphone === '+097'
            ) {
                $scope.mphoneInCountry = true;
            }

            if (
                $scope.personContacts.phoneWork === '380' ||
                $scope.personContacts.phoneWork === '+380' ||
                $scope.personContacts.phoneWork === '093' ||
                $scope.personContacts.phoneWork === '073' ||
                $scope.personContacts.phoneWork === '063' ||
                $scope.personContacts.phoneWork === '067' ||
                $scope.personContacts.phoneWork === '066' ||
                $scope.personContacts.phoneWork === '050' ||
                $scope.personContacts.phoneWork === '095' ||
                $scope.personContacts.phoneWork === '099' ||
                $scope.personContacts.phoneWork === '096' ||
                $scope.personContacts.phoneWork === '098' ||
                $scope.personContacts.phoneWork === '097' ||
                $scope.personContacts.phoneWork === '+093' ||
                $scope.personContacts.phoneWork === '+073' ||
                $scope.personContacts.phoneWork === '+063' ||
                $scope.personContacts.phoneWork === '+067' ||
                $scope.personContacts.phoneWork === '+066' ||
                $scope.personContacts.phoneWork === '+050' ||
                $scope.personContacts.phoneWork === '+095' ||
                $scope.personContacts.phoneWork === '+099' ||
                $scope.personContacts.phoneWork === '+096' ||
                $scope.personContacts.phoneWork === '+098' ||
                $scope.personContacts.phoneWork === '+097'
            ) {
                $scope.phoneWorkInCountry = true;
            }

            if (
                $scope.personContacts.phoneMob === '380' ||
                $scope.personContacts.phoneMob === '+380' ||
                $scope.personContacts.phoneMob === '093' ||
                $scope.personContacts.phoneMob === '073' ||
                $scope.personContacts.phoneMob === '063' ||
                $scope.personContacts.phoneMob === '067' ||
                $scope.personContacts.phoneMob === '066' ||
                $scope.personContacts.phoneMob === '050' ||
                $scope.personContacts.phoneMob === '095' ||
                $scope.personContacts.phoneMob === '099' ||
                $scope.personContacts.phoneMob === '096' ||
                $scope.personContacts.phoneMob === '098' ||
                $scope.personContacts.phoneMob === '097' ||
                $scope.personContacts.phoneMob === '+093' ||
                $scope.personContacts.phoneMob === '+073' ||
                $scope.personContacts.phoneMob === '+063' ||
                $scope.personContacts.phoneMob === '+067' ||
                $scope.personContacts.phoneMob === '+066' ||
                $scope.personContacts.phoneMob === '+050' ||
                $scope.personContacts.phoneMob === '+095' ||
                $scope.personContacts.phoneMob === '+099' ||
                $scope.personContacts.phoneMob === '+096' ||
                $scope.personContacts.phoneMob === '+098' ||
                $scope.personContacts.phoneMob === '+097'
            ) {
                $scope.phoneMobInCountry = true;
            }
        };

        vm.onChangeFileName = (value, file) => {
            file.fileName = value;
            $rootScope.$$phase || $scope.$apply();
        };

        vm.showCustomFieldFullText = (value) => {
            $scope.customFieldFullText = value;

            vm.modalInstance = $uibModal.open({
                animation: true,
                windowClass: 'custom-field-full-text-modal',
                templateUrl: 'partials/modal/custom-field-full-text.html',
                size: '',
                scope: $scope,
            });
        };

        vm.editFileName = function (data) {
            File.changeFileName(
                {
                    fileId: data.fileId,
                    name: data.fileName,
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        data.showEditFileName = false;
                    } else {
                        notificationService.error(resp.message);
                    }
                },
            );
        };
        vm.showModalResume = function (file) {
            Service.showModalResume(file, $scope, $rootScope, $location, $sce, $uibModal);
        };
        vm.goToCompanySettings = function () {
            vm.closeModal();
            $location.url($location.path());
            $location.path('/company/settings/&q1=facebook');
        };
        vm.showShareFbPages = function (tab) {
            if ($rootScope.fbPages.length === 0) {
                vm.modalInstance = $uibModal.open({
                    animation: true,
                    windowClass: 'FB-integrate-modal',
                    templateUrl: '../partials/modal/notHaveIntegrationWith-FB.html',
                    size: '',
                    scope: $scope,
                    resolve: function () {},
                });
            } else {
                vm.modalInstance = $uibModal.open({
                    animation: true,
                    templateUrl: '../partials/modal/haveIntegrationWith-FB.html',
                    size: '',
                    scope: $scope,
                    resolve: function () {},
                });
            }
        };
        vm.blurToRemovePromoLogo = function () {
            vm.isBlurToRemovePromoLogo = true;
        };
        vm.unblurToRemovePromoLogo = function () {
            vm.isBlurToRemovePromoLogo = false;
        };
        vm.removePromoLogo = function () {
            Vacancy.removeImg({ vacancyId: vm.vacancy.vacancyId }, function (resp) {
                if (resp.status == 'ok') {
                    $rootScope.promoLogo = null;
                    notificationService.success(
                        $filter('translate')('Logo of the vacancy') +
                            ' ' +
                            vm.vacancy.position +
                            ' ' +
                            $filter('translate')('was removed'),
                    );
                }
            });
        };
        vm.openPromoLogo = function () {
            vm.imgWidthFuncForOpenLogo();
            $('#cover-picture-modal').removeClass('hidden');
            $('#cover-picture-modal').addClass('visible');
        };
        vm.imgWidthFuncForOpenLogo = function () {
            var img = new Image();
            img.src = vm.vacancy.imageId
                ? $location.$$protocol +
                  '://' +
                  $location.$$host +
                  serverAddress +
                  '/getapp?id=' +
                  vm.vacancy.imageId +
                  '&d=' +
                  $rootScope.me.personId
                : null;
            img.onload = function () {
                var width = this.width;
                var height = this.height;
                var minus = width - height;
                if (height > width && minus < -100) {
                    $('.modal-content').css('width', '20%');
                } else if (width > 400 && height < 400 && minus > 100) {
                    $('.modal-content').css('width', '50%');
                } else if (width < 400 && height < 400) {
                    $('.modal-content').css('width', '30%');
                } else if (width > 400 && height > 400) {
                    $('.modal-content').css('width', '30%');
                } else {
                    $('.modal-content').css('width', '25%');
                }
            };
        };
        vm.closeModalCrop = function () {
            $('#crop-picture-modal').removeClass('visible');
            $('#crop-picture-modal').addClass('hidden');
        };
        vm.closeModalImage = function () {
            $('#cover-picture-modal').removeClass('visible');
            $('#cover-picture-modal').addClass('hidden');
        };
        vm.addPublish = function publish(type) {
            var DNA = true;
            vm.requestToAddPublishCame = true;
            if (vm.vacancy.publish != undefined && vm.vacancy.publish.length > 0) {
                angular.forEach(vm.vacancy.publish, function (val) {
                    if (val.type == type) {
                        DNA = false;
                    }
                });
            }
            if (DNA && vm.requestToAddPublishCame) {
                vm.requestToAddPublishCame = false;
                Vacancy.addPublish({ vacancyId: vm.vacancy.vacancyId, type: type }, function (resp) {
                    if (vm.vacancy.publish == undefined) {
                        vm.vacancy.publish = [];
                    }

                    if (resp.status === 'ok' && resp.object) {
                        vm.vacancy.publishingCount = resp.object.publishingCount;
                    }

                    vm.shareObj[resp.object.type] = true;
                    vm.vacancy.publish.push(resp.object);
                    vm.requestToAddPublishCame = true;
                });
            }
        };

        vm.changeVacancyVisibility = function (event) {
            vm.vacancyHidden = event.target.checked;

            $rootScope.loading = true;
            Vacancy.changeVisibility(vm.vacancyHidden, vm.vacancy.vacancyId).then(
                (resp) => {
                    $rootScope.loading = false;
                    if (resp.status !== 'error') {
                        notificationService.success($filter('translate')('Vacancy visibility changed'));
                    } else {
                        notificationService.error(resp.message);
                    }
                },
                (error) => {
                    $rootScope.loading = false;
                    notificationService.error('Request hideVacancy status code: ' + error.status);
                },
            );

            $rootScope.$$phase || $scope.$apply();
        };
        vm.SendEmailTemplateModal = function () {
            $rootScope.fileForSave = [];
            vm.sendEmailTemplate = {
                toEmails: '',
                vacancyId: vm.vacancy.vacancyId,
                candidateId: null,
                fullName: null,
                email: '',
                date: null,
                lang: $translate.use(),
                template: {},
            };
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/send-vacancy-by-email-from-vacancy.html',
                scope: $scope,
                size: '',
                resolve: function () {},
            });
            vm.modalInstance.opened.then(
                $timeout(() => {
                    getCkEditorTemplate();
                }, 200),
            );
        };
        vm.initCkEditorOptions = function () {
            vm.ckEditorOptions = {
                height: 150,
                enterMode: CKEDITOR.ENTER_P,
                allowedContent: true,
                removeButtons:
                    'Source,Save,Preview,NewPage,Print,Templates,Cut,Copy,Paste,PasteText,PasteFromWord,Replace,Find,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Subscript,Superscript,CopyFormatting,RemoveFormat,BulletedList,NumberedList,Outdent,Indent,CreateDiv,Blockquote,JustifyLeft,JustifyCenter,JustifyRight,JustifyBlock,Language,BidiRtl,BidiLtr,Link,Unlink,Anchor,Table,Flash,Image,HorizontalRule,Smiley,SpecialChar,PageBreak,Iframe,Styles,TextColor,BGColor,ShowBlocks,Maximize,About,Format',
            };
        };

        vm.showScoreCardPreviewPopup = function (scoreCard) {
            vm.modalInstance = $uibModal.open({
                templateUrl: 'partials/modal/scoreCards/scoreCardPreview.modal.html',
                controller: [
                    '$uibModalInstance',
                    '$uibModal',
                    'scoreCardModel',
                    function ($uibModalInstance, $uibModal, scoreCardModel) {
                        this.scoreCardPreviewModel = scoreCardModel;
                        this.closeModal = function () {
                            $uibModalInstance.close();
                        };
                    },
                ],
                controllerAs: 'vm',
                windowClass: 'scoreCardPreview-popup',
                scope: $scope,
                resolve: {
                    scoreCardModel: () => {
                        return scoreCard;
                    },
                },
            });
        };
        vm.showFbMessage = function () {
            if (true) {
                vm.modalInstance = $uibModal.open({
                    animation: true,
                    windowClass: 'secondary-modal',
                    templateUrl: '../partials/modal/facebook-modal-before-share.html',
                    backdrop: 'static',
                    size: '',
                    scope: $scope,
                    resolve: function () {},
                });
            } else {
                var link = $location.$$protocol + '://' + $location.$$host + '/i/vacancy-' + vm.vacancy.localId;
                if (FB) {
                    FB.getLoginStatus(function (response) {
                        var setinterval = setInterval(() => {
                            let frame = document.querySelector('.FB_UI_Dialog');
                            if (frame) {
                                frame.setAttribute('width', '600px');
                                frame.setAttribute('style', 'min-width:600px;');
                                frame.style.minWidth = '600px !important';
                                frame.style.width = '600px !important';
                                clearInterval(setinterval);
                            }
                        }, 1000);
                        if (response.status === 'connected' || response.status === 'unknown') {
                            FB.ui(
                                {
                                    display: 'popup',
                                    method: 'share_open_graph',
                                    action_type: 'og.shares',
                                    action_properties: JSON.stringify({
                                        object: {
                                            'og:url': link,
                                            'og:title': $filter('translate')('Vacancy') + ' ' + vm.vacancy.position,
                                            'og:description': $filter('limitTo')(vm.publicDescr, 100, 0),
                                            'og:image': getVacancyPromoLogo(),
                                        },
                                    }),
                                },
                                function (response) {
                                    if (!response.error_message) {
                                        Vacancy.addPublish(
                                            {
                                                vacancyId: vm.vacancy.vacancyId,
                                                type: 'facebook_page',
                                            },
                                            (resp) =>
                                                resp.status === 'ok'
                                                    ? (vm.vacancy.publishingCount = resp.object.publishingCount)
                                                    : console.error('SMTHGOWRONG'),
                                        );
                                    } else if (response.error_message) {
                                        notificationService.error($filter('translate')('Vacancy was not shared'));
                                    }
                                },
                            );
                        } else {
                            FB.login(function (response) {
                                if (response.authResponse) {
                                    FB.ui(
                                        {
                                            method: 'share_open_graph',
                                            action_type: 'og.shares',
                                            action_properties: JSON.stringify({
                                                object: {
                                                    'og:url': link,
                                                    'og:title':
                                                        $filter('translate')('Vacancy') + ' ' + vm.vacancy.position,
                                                    'og:description': $filter('limitTo')(vm.publicDescr, 100, 0),
                                                    'og:image': getVacancyPromoLogo(),
                                                },
                                            }),
                                        },
                                        function (response) {
                                            if (!response.error_message) {
                                                Vacancy.addPublish(
                                                    {
                                                        vacancyId: vm.vacancy.vacancyId,
                                                        type: 'facebook_page',
                                                    },
                                                    (resp) =>
                                                        resp.status === 'ok'
                                                            ? (vm.vacancy.publishingCount = resp.object.publishingCount)
                                                            : console.error('SMTHGOWRONG'),
                                                );
                                            } else if (response.error_message) {
                                                notificationService.error(
                                                    $filter('translate')('Vacancy was not shared'),
                                                );
                                            }
                                        },
                                    );
                                }
                            });
                        }
                    });
                }
            }
        };

        vm.addPublishX = () => {
            const link = `http://twitter.com/intent/tweet?url=${vm.publicLink}&text=${$filter('translate')(
                'Vacancy',
            )} ${vm.publicName}&hashtags=cleverstaff`;
            window.open(link, '_blank');
            vm.addPublish('twitter');
        };

        vm.showRemoveResponsibleModal = function (user) {
            if (vm.modalIsOpened) return;

            vm.modalIsOpened = true;
            $scope.responsibleToRemove = user;
            $scope.vacancyName = vm.vacancy.position;
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/remove-responsible-from-vacancy.html',
                size: '',
                scope: $scope,
                resolve: {},
            });
            vm.modalInstance.result.then(
                function () {
                    vm.modalIsOpened = false;
                },
                function () {
                    vm.modalIsOpened = false;
                },
            );
        };
        vm.showChangeStatusOfVacancy = function (status) {
            vm.changeStateObject.status = status.value;
            vm.canceledChange = true;
            vm.changeStateObject.status_old = vm.vacancy.status;
            vm.changeStateObject.placeholder = $filter('translate')('Write_a_comment_why_do_you_change_vacancy_status');

            if (status.value === vm.changeStateObject.status_old) {
                notificationService.error($filter('translate')('You cannot change the status to the same'));
                return;
            }

            if (status.value === 'completed') {
                Vacancy.onGetCounts({ vacancyId: vm.vacancy.vacancyId }).then(
                    (resp) => {
                        vm.usersInApprovedStage = resp.objects.filter((el) => el.item === 'approved');
                        if (vm.modalInstance) vm.modalInstance.close();
                        vm.hideCongratulation = resp.objects.length === 1 && resp.objects[0].item === 'approved';
                        const hasApproved = vm.usersInApprovedStage.length > 0;
                        if (!hasApproved) {
                            notificationService.error(
                                $filter('translate')('You must move one of the candidates to status Hired'),
                            );
                            vm.statusForChange = {
                                label: vm.changeStateObject.status_old,
                                value: vm.changeStateObject.status_old,
                                color: ['open', 'inwork', 'completed'].includes(vm.changeStateObject.status_old)
                                    ? '#77B472'
                                    : '#F5C620',
                            };
                            $scope.$apply();
                        } else {
                            vacancyChangeStatusModal();
                        }
                    },
                    (err) => console.error(err),
                );
            } else {
                vm.hideCongratulation = true;
                if (
                    status.value !== 'inwork' ||
                    (status.value === 'inwork' &&
                        vm.vacancy.responsiblesPerson &&
                        vm.vacancy.responsiblesPerson.length > 0)
                ) {
                    vacancyChangeStatusModal();
                } else if (vm.needAutoSetResponsible && status.value == 'inwork') {
                    $rootScope.changeResponsibleInVacancy.id = $rootScope.me.userId;
                    $rootScope.changeResponsibleInVacancy.comment =
                        'Поскольку вы являетесь единственным пользователем Вашей компании, мы назначили Вас ответственным';
                    vm.saveResponsibleUserInVacancy();
                    vacancyChangeStatusModal();
                } else if (status.value === 'inwork' && !vm.needAutoSetResponsible) {
                    notificationService.error($filter('translate')('You must set a responsible') + '!');
                }
            }
        };

        vm.showAddResponsibleUser = function (person, oldPerson) {
            $rootScope.clickedSaveResponsibleInVacancy = false;
            $rootScope.changeResponsibleInVacancy.id = person.userId;
            if ($rootScope.useAmericanNameStyle) {
                $rootScope.changeResponsibleInVacancy.name = person.fullNameEn;
            } else {
                $rootScope.changeResponsibleInVacancy.name = person.fullName;
            }
            $rootScope.changeResponsibleInVacancy.name = person.fullName;
            vm.saveResponsibleUserInVacancy(oldPerson);
        };
        vm.setResponsible = function (resp) {
            vm.vacancy.responsiblesPerson = resp.reverse();
            $rootScope.vacancy.responsiblesPerson = resp;
            vm.getCurrentOptions();
            $rootScope.$$phase || $scope.$apply();
        };
        vm.changeResponsibleRole = function (type, responsible) {
            Vacancy.addResponsible(
                {
                    lang: $translate.use(),
                    vacancyId: vm.vacancy.vacancyId,
                    personId: responsible.personId,
                    responsibleType: type.type,
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        notificationService.success($filter('translate')('vacancy set responsible'));
                        vm.vacancy.responsiblesPerson = resp.object.responsiblesPerson;
                    }
                },
                function (err) {
                    console.error(err);
                },
            );
        };
        vm.onAddNewResonsible = function () {
            if (
                vm.vacancy.responsiblesPerson.length > 0 &&
                !vm.vacancy.responsiblesPerson[vm.vacancy.responsiblesPerson.length - 1].responsible?.cutFullName &&
                vm.vacancy.responsiblesPerson.length !== 0
            )
                return;
            vm.vacancy.responsiblesPerson.push({
                responsible: null,
                type: undefined,
                label: undefined,
                value: undefined,
            });
            $rootScope.$$phase || $scope.$apply();
        };
        vm.saveResponsibleUserInVacancy = function (oldPerson) {
            if (!$rootScope.clickedSaveResponsibleInVacancy) {
                $rootScope.clickedSaveResponsibleInVacancy = true;
                $rootScope.loading = true;
                Vacancy.onAddResponsible({
                    lang: $translate.use(),
                    vacancyId: vm.vacancy.vacancyId,
                    personId: $rootScope.changeResponsibleInVacancy.id,
                    comment: $rootScope.changeResponsibleInVacancy.comment,
                    oldPersonId: oldPerson.personId,
                })
                    .then((resp) => {
                        $rootScope.clickedSaveResponsibleInVacancy = false;
                        notificationService.success($filter('translate')('vacancy set responsible'));
                        vm.setResponsible(resp.object.responsiblesPerson);
                        $rootScope.$$phase || $scope.$apply();
                    })
                    .catch((error) => {
                        console.error(error.message);
                    })
                    .finally((resp) => {
                        // vm.closeModal();
                        $rootScope.changeResponsibleInVacancy.comment = '';
                        $rootScope.changeResponsibleInVacancy.id = '';
                        $rootScope.loading = false;
                        $scope.$apply();
                    });
            }
        };

        vm.getCurrentOptions = function () {
            getUsers();
            setTimeout(() => {
                if (vm.vacancy.responsiblesPerson.length && vm.persons.length) {
                    vm.persons = vm.persons.filter((person) => {
                        let includes = false;
                        vm.vacancy.responsiblesPerson.forEach((responsible) => {
                            responsible.value = responsible;
                            responsible.label = $rootScope.useAmericanNameStyle
                                ? responsible.responsible.fullNameEn
                                : responsible.responsible.fullName;
                            if (person.personId === responsible.personId) {
                                includes = true;
                            }
                        });
                        return !includes;
                    });
                }
            }, 0);
        };
        vm.removeResponsibleUserInVacancy = function (userId, editComment) {
            if (!$rootScope.loading) {
                $rootScope.loading = true;
                Vacancy.removeResponsible(
                    {
                        vacancyId: vm.vacancy.vacancyId,
                        personId: userId,
                        comment: editComment,
                    },
                    function (resp) {
                        if (resp.status === 'ok') {
                            notificationService.success($filter('translate')('vacancy remove responsible'));
                            vm.vacancy.responsiblesPerson = resp.object.responsiblesPerson;
                            vm.vacancy.responsiblesPerson.forEach((person) => {
                                // person.type = {label: person.type, value: person.type};
                            });
                            vm.getCurrentOptions();
                            $rootScope.loading = false;
                            $rootScope.$$phase || $scope.$apply();
                        }
                    },
                    function (err) {
                        console.error(error.message);
                        $rootScope.loading = false;
                    },
                );
                vm.closeModal();
                $rootScope.changeResponsibleInVacancy.comment = '';
                $rootScope.changeResponsibleInVacancy.id = '';
            }
        };
        vm.saveVacancyStatus = function (oldStatus) {
            $rootScope.usersCrystalsMap = [];
            $rootScope.userCrystalsMapped = [];
            $rootScope.loading = true;
            vm.canceledChange = false;
            vm.closeModal();
            Vacancy.changeState(
                {
                    vacancyId: vm.vacancy.vacancyId,
                    comment: vm.changeStateObject.comment,
                    vacancyState: vm.changeStateObject.status,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        $rootScope.completedPoints = resp.object.crystals;
                        $rootScope.closedVacancyName = resp.object.position;
                        $rootScope.getCounts = false;
                        if (resp.object.usersCrystalsMap) {
                            $rootScope.usersCrystalsMap = resp.object.usersCrystalsMap;
                            $rootScope.userCrystalsMapped = Object.entries($rootScope.usersCrystalsMap);
                        }

                        vm.vacancy.status = vm.changeStateObject.status;
                        vm.statusForChange = {
                            label: vm.vacancy.status,
                            value: vm.vacancy.status,
                            color: ['open', 'inwork', 'completed'].includes(vm.vacancy.status) ? '#77B472' : '#F5C620',
                        };
                        $rootScope.vacancy.status = resp.object.status;
                        vm.changeStateObject.comment = '';
                        vm.changeStateObject.status = null;
                        $rootScope.$emit('setVacancyStatus', vm.changeStateObject.status);
                        $rootScope.loading = false;
                        if ($rootScope.completedPoints < 1) {
                            notificationService.success($filter('translate')('vacancy change status'));
                        }
                    } else if (resp.message) {
                        $rootScope.loading = false;
                        notificationService.error(resp.message);
                    }
                    $rootScope.loading = false;
                    $rootScope.clickedSaveVacancyStatus = false;

                    $rootScope.$$phase || $scope.$apply();

                    if (
                        $rootScope.me.recrutRole != 'client' &&
                        vm.changeStateObject.status_old !== 'completed' &&
                        vm.vacancy.status === 'completed' &&
                        window.screen.width > 1099 &&
                        window.screen.height > 600 &&
                        resp.object.usersCrystalsMap !== undefined
                    ) {
                        setTimeout(() => {
                            $rootScope.showCongratulation();
                        }, 1500);
                    }
                },
                function (error) {
                    console.error(error);
                    $rootScope.loading = false;
                    $rootScope.clickedSaveVacancyStatus = false;
                },
            );
            vm.modalInstance.close();
        };
        vm.updateOrgPages = function () {
            Company.orgPages(function (resp) {
                $rootScope.fbPages = resp.objects;
                for (var i = $rootScope.fbPages.length - 1; i >= 0; i--) {
                    if ($rootScope.fbPages[i].status === 'D') {
                        $rootScope.fbPages.splice(i, 1);
                    }
                }
                angular.forEach($rootScope.fbPages, function (val) {
                    if (val.facebookPageId) {
                        angular.forEach(vm.vacancy.publish, function (valStatus) {
                            if (valStatus.pageId == val.facebookPageId) {
                                val.vacancyAdded = true;
                            }
                        });
                    }
                });
            });
        };
        vm.showDeleteFbPages = function (tab) {
            vm.deletedTab = tab;
            vm.modalInstance = $uibModal.open({
                animation: true,
                windowClass: 'secondary-modal',
                templateUrl: '../partials/modal/vacancy-remove-fb-tab.html',
                size: '',
                scope: $scope,
                resolve: function () {},
            });
        };
        vm.deleteVacancyOnFacebook = function () {
            Vacancy.deletePublish(
                {
                    publishId: vm.deletedTab.publishId,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        vm.updateOrgPages();
                        vm.closeModal();
                    } else {
                        notificationService.error(resp.message);
                    }
                },
            );
        };
        vm.sendEmailTemplateFunc = function () {
            vm.sendEmailTemplate.template.filesIdName = Service.filesIdNameToObjectInString($rootScope.fileForSave);
            vm.emptyCandidateEmail = false;

            if (!vm.sendEmailTemplate.toEmails) {
                notificationService.error($filter('translate')('enter_email_candidate'));
                vm.emptyCandidateEmail = true;
                return;
            }
            if (typeof vm.sendEmailTemplate.email === 'object')
                vm.sendEmailTemplate.email = vm.sendEmailTemplate.email.email;
            $rootScope.loading = true;
            Mail.sendMailByTemplateVerified(vm.sendEmailTemplate, function (resp) {
                if (resp.status == 'ok') {
                    $rootScope.loading = false;
                    notificationService.success($filter('translate')('Letter sent'));
                    vm.closeModal();
                } else {
                    $rootScope.loading = false;
                    if (resp.message) {
                        notificationService.error($filter('translate')(resp.message));
                    } else {
                        notificationService.error(
                            $filter('translate')('Error connecting integrate with email. Connect it again'),
                        );
                    }
                }
            });
        };
        vm.addVacancyToFacebook = function (tab) {
            Vacancy.addPublish(
                {
                    vacancyId: vm.vacancy.vacancyId,
                    type: 'facebook_page',
                    pageId: tab.facebookPageId,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        $('.shareFbPagesForVacancy.modal').modal('hide');
                    } else {
                        notificationService.error(resp.message);
                    }
                },
            );
        };
        vm.addEmailFromWhatSendInDescription = function (email) {
            vm.selectedEmail = email;
            vm.sendEmailTemplate.template.email = email.email;
            vm.sendEmailTemplate.email = vm.sendEmailTemplate.template.email;
            vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text.replace(
                /\[\[recruiterEmail\]\]/g,
                vm.sendEmailTemplate.template.email,
            );
        };

        vm.changeLetterSubject = (value) => {
            vm.sendEmailTemplate.template.title = value;
            $scope.$apply();
        };

        vm.changeCandidateEmail = (value) => {
            vm.sendEmailTemplate.toEmails = value;
            vm.emptyCandidateEmail = !value;
            $scope.$apply();
        };

        vm.changeDuplicateName = (value) => {
            vm.newVacancyName = value;
            $scope.$apply();
        };

        vm.selectInterview = (value) => {
            vm.selectedInterview = value;
            $scope.$apply();
        };

        vm.copyVacancyModal = function () {
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/copy-vacancy-to-vacancy.html',
                size: '',
                scope: $scope,
                resolve: function () {
                    return {
                        addCandidatesToCopy: () => {
                            return vm.addCandidatesToCopy;
                        },
                    };
                },
            });
            vm.modalInstance.result.then(
                function () {
                    vm.copyCandidate = false;
                    vm.newVacancyName = null;
                },
                function () {
                    vm.copyCandidate = false;
                    vm.newVacancyName = null;
                },
            );

            vm.selectedInterview = [];
            if (vm.vacancy.interviewStatus) {
                vm.interviewStatus = vm.vacancy.interviewStatus.split(',').filter((status) => status != 'approved');
            } else {
                Person.me().then(
                    (resp) => {
                        if (resp.object.orgParams && resp.object.orgParams.defaultInterviewStates) {
                            vm.interviewStatus = resp.object.orgParams.defaultInterviewStates;
                        }
                    },
                    () => {
                        vm.interviewStatus = null;
                    },
                );
            }

            vm.interviewStatus = vm.interviewStatus.map((stage) => ({ label: stage, value: stage }));
            parseCustomStages(vm.interviewStatus);

            let countCandidatesOnStages = [];
            Vacancy.onGetCounts({ vacancyId: vm.vacancy.vacancyId }).then(
                (resp) => {
                    countCandidatesOnStages = resp.objects;
                },
                (err) => console.error(err),
            );

            vm.addCandidatesToCopy = function () {
                vm.copyCandidate = !vm.copyCandidate;
                $rootScope.$$phase || $scope.$apply();
                if (!vm.copyCandidate) {
                    vm.selectedInterview = [];
                    return;
                }

                const stagesWithCandidates = [];
                vm.interviewStatus.forEach((stage) => {
                    const index = countCandidatesOnStages.findIndex((item) => item.item === stage.value);
                    if (index !== -1) {
                        stagesWithCandidates.push(stage);
                    }
                });

                vm.selectedInterview = [...stagesWithCandidates];
                $rootScope.$$phase || $scope.$apply();
            };
        };
        vm.duplicateVacancy = function (newVacancyName) {
            $rootScope.loading = true;
            if (newVacancyName && newVacancyName.trim().length) {
                Vacancy.onAddCopy({
                    id: $rootScope.vacancy.vacancyId,
                    states: vm.selectedInterview.map((stage) => stage.value),
                })
                    .then((resp) => {
                        if (
                            newVacancyName.toLowerCase().includes('директор') ||
                            newVacancyName.toLowerCase().includes('руководитель') ||
                            newVacancyName.toLowerCase().includes('керівник') ||
                            newVacancyName.toLowerCase().includes('начальник') ||
                            newVacancyName.toLowerCase().includes('director') ||
                            newVacancyName.toLowerCase().includes('head') ||
                            newVacancyName.toLowerCase().includes('chief') ||
                            newVacancyName.toLowerCase().includes('lead')
                        ) {
                            if (resp.object.experience === 'e00_no_experience' || !resp.object.experience) {
                                notificationService.error(
                                    $filter('translate')(
                                        'A specialist of this level should have some work experience. Please specify the required work experience.',
                                    ),
                                );
                                $rootScope.loading = false;
                                $scope.$apply();
                                return;
                            }
                        }
                        resp.object.position = newVacancyName;

                        $rootScope.loading = true;
                        Vacancy.onAddVacancy(resp.object)
                            .then((res) => {
                                if (res.status === 'ok') {
                                    vm.closeModal();
                                    $location.path(`/vacancies/${res.object.localId}`);
                                    $rootScope.redirectToSuggestions = true;
                                    notificationService.success($filter('translate')('Vacancy dublicated'));
                                    if ($rootScope.me.recrutRole !== 'client') {
                                        setTimeout(() => {
                                            $rootScope.showSuccessCreateVacancy();
                                        }, 2000);
                                    }
                                } else {
                                    notificationService.error($filter('translate')('That is an error'));
                                }
                            })
                            .catch((err) => {
                                notificationService.error($filter('translate')(err.message));
                                console.error(err);
                            })
                            .finally(() => {
                                $rootScope.loading = false;
                                $scope.$apply();
                            });
                    })
                    .catch((err) => {
                        notificationService.error($filter('translate')(err.message));
                        console.error(err);
                        $rootScope.loading = false;
                        $scope.$apply();
                    })
                    .finally(() => {
                        if (!vm.copyCandidate) {
                            $rootScope.countCandidates = 0;
                        }
                        $scope.$apply();
                    });
            } else {
                $rootScope.loading = false;
                notificationService.error($filter('translate')('Enter the new vacancy name'));
            }
        };

        vm.checkOverflow = function (index) {
            const element = document.getElementById(`text-${index}`);

            if (element) {
                return element.scrollHeight > element.clientHeight || element.scrollWidth > element.clientWidth;
            }
            return false;
        };

        function parseCustomStages(allStages) {
            $rootScope.customStages.forEach((customStage) => {
                allStages.forEach((stage, i) => {
                    if (customStage.customInterviewStateId === stage.value) {
                        allStages[i]['label'] = customStage.name;
                    }
                });
            });
        }

        function resetHideCongratulationValue() {
            vm.hideCongratulation = true;
        }

        function initDescriptionFileAtach() {
            FileInit.initFileVacancy(vm, 'vacancy', $filter);
            vm.callbackFile = function (resp, name) {
                vm.vacancy.files = vm.vacancy.files || [];
                vm.vacancy.files.push(resp);
            };
        }

        function initFileAtach() {
            FileInit.initVacancyTemplateFileOption(vm, '', '', false, $filter);
            vm.callbackFileForTemplate = function (resp, names) {
                vm.vacancy.files = vm.vacancy.files || [];
                vm.vacancy.files.push({ fileId: resp, fileName: names });
                $rootScope.fileForSave.push({ fileId: resp, fileName: names });
                $('#file').val('');
            };
        }

        function getVacancyPromoLogo() {
            return $rootScope.promoLogo
                ? `${$location.$$protocol}://${$location.$$host}${$rootScope.serverAddress}/getlogo?id=${$rootScope.promoLogo}`
                : 'https://cleverstaff.net/images/sprite/vacancy-new.jpg';
        }

        function getCkEditorTemplate() {
            vm.staticEmailTemplate = {
                candidateName: 'John',
                fullName: 'John Dou',
                vacancyLink: vm.vacancy.position,
                date: new Date().getTime(),
                recruiterName: $rootScope.me.fullName,
                recruiterEmail: $rootScope.me.emails.length > 0 ? $rootScope.me.emails[0].email : $rootScope.me.login,
            };
            $rootScope.me.contacts.forEach((contact) => {
                switch (contact.contactType) {
                    case 'mphone':
                        vm.staticEmailTemplate.mobPhone = contact.value;
                        break;
                    case 'phoneWork':
                        vm.staticEmailTemplate.phoneWork = contact.value;
                        break;
                    case 'skype':
                        vm.staticEmailTemplate.skype = contact.value;
                        break;
                    case 'linkedin':
                        vm.staticEmailTemplate.linkedin = contact.value;
                        break;
                    case 'facebook':
                        vm.staticEmailTemplate.facebook = contact.value;
                }
            });

            Mail.getTemplateVacancy({ vacancyId: vm.vacancy.vacancyId, type: 'seeVacancy' }, function (data) {
                if (!vm.skypeContact) {
                    data.object.text = data.object.text.replace(/Skype:/g, '');
                }
                data.object.text = data.object.text.replace(
                    /\[\[vacancy name\]\]/g,
                    '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                        vm.publicLink +
                        '">' +
                        vm.vacancy.position +
                        '</a>',
                );
                data.object.text = data.object.text.replace(/\[\[vacancy name\]\]/g, vm.vacancy.position);
                data.object.text = data.object.text
                    .replace(/\[\[recruiter&#39;s name\]\]/g, $rootScope.me.fullName)
                    .replace(
                        /\[\[recruiter's name\]\]/g,
                        $rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName,
                    );
                data.object.title = data.object.title.replace(/\[\[vacancy name\]\]/g, vm.vacancy.position);

                data.object.text = data.object.text.replace(
                    /\[\[vacancy name\]\]/g,
                    '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                        vm.publicLink +
                        '">' +
                        vm.vacancy.position +
                        '</a>',
                );
                data.object.text = data.object.text
                    .replace(/\[\[recruiter&#39;s name\]\]/g, $rootScope.me.fullName)
                    .replace(
                        /\[\[recruiter's name\]\]/g,
                        $rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName,
                    );
                data.object.title = data.object.title.replace(
                    /\[\[vacancy name\]\]/g,
                    '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                        vm.publicLink +
                        '">' +
                        vm.vacancy.position +
                        '</a>',
                );
                data.object.title = data.object.title.replace(/\[\[vacancy name\]\]/g, vm.vacancy.position);
                data.object.text = data.object.text.replace(/\[\[vacancy name\]\]/g, vm.vacancy.position);
                if ($rootScope.me.contacts[0] && $rootScope.me.contacts[0].contactType == 'mphone') {
                    if ($rootScope.me.contacts[1].contactType == 'phoneWork') {
                        // mhpone - 1, phoneWork - 1
                        data.object.text = data.object.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                        data.object.text = data.object.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                    // mhpone - 0, phoneWork - nan
                    data.object.text = data.object.text
                        .replace(
                            /\[\[recruiter&#39;s phone\]\]/g,
                            $rootScope.me.contacts[0].contactType == 'mphone' ? $rootScope.me.contacts[0].value : '',
                        )
                        .replace(
                            /\[\[recruiter's phone\]\]/g,
                            $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                        );
                    data.object.text = data.object.text
                        .replace(
                            /\[\[recruiter&#39;s phone 2\]\]/g,
                            $rootScope.me.contacts[0].contactType == 'phoneWork' ? $rootScope.me.contacts[0].value : '',
                        )
                        .replace(
                            /\[\[recruiter's phone 2\]\]/g,
                            $rootScope.me.contacts[0].contactType == 'phoneWork' ? $rootScope.me.contacts[0].value : '',
                        );
                } else {
                    // mhpone - none, phoneWork - 0
                    data.object.text = data.object.text
                        .replace(
                            /\[\[recruiter&#39;s phone\]\]/g,
                            $rootScope.me.contacts[0] && $rootScope.me.contacts[0].contactType == 'mphone'
                                ? $rootScope.me.contacts[0].value
                                : '',
                        )
                        .replace(
                            /\[\[recruiter's phone\]\]/g,
                            $rootScope.me.contacts[0] && $rootScope.me.contacts[0].contactType == 'mphone'
                                ? $rootScope.me.contacts[0].value
                                : '',
                        );
                    data.object.text = data.object.text
                        .replace(
                            /\[\[recruiter&#39;s phone 2\]\]/g,
                            $rootScope.me.contacts[0] && $rootScope.me.contacts[0].contactType == 'phoneWork'
                                ? $rootScope.me.contacts[0].value
                                : '',
                        )
                        .replace(
                            /\[\[recruiter's phone 2\]\]/g,
                            $rootScope.me.contacts[0] && $rootScope.me.contacts[0].contactType == 'phoneWork'
                                ? $rootScope.me.contacts[0].value
                                : '',
                        );
                }

                data.object.text = data.object.text
                    .replace(
                        /\[\[recruiter&#39;s Skype\]\]/g,
                        vm.staticEmailTemplate.skype ? vm.staticEmailTemplate.skype : '',
                    )
                    .replace(
                        /\[\[recruiter's Skype\]\]/g,
                        vm.staticEmailTemplate.skype ? vm.staticEmailTemplate.skype : '',
                    );
                if (!vm.staticEmailTemplate.skype) {
                    data.object.text = data.object.text.replace(/Skype:/g, '');
                }
                if (vm.staticEmailTemplate.facebook) {
                    data.object.text = data.object.text
                        .replace(
                            /\[\[recruiter&#39;s Facebook\]\]/g,
                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                vm.staticEmailTemplate.facebook +
                                '">' +
                                vm.staticEmailTemplate.facebook +
                                '</a>',
                        )
                        .replace(
                            /\[\[recruiter's Facebook\]\]/g,
                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                vm.staticEmailTemplate.facebook +
                                '">' +
                                vm.staticEmailTemplate.facebook +
                                '</a>',
                        );
                } else {
                    data.object.text = data.object.text
                        .replace(/\[\[recruiter&#39;s Facebook\]\]/g, '')
                        .replace(/\[\[recruiter's Facebook\]\]/g, '');
                }
                if (vm.staticEmailTemplate.linkedin) {
                    data.object.text = data.object.text
                        .replace(
                            /\[\[recruiter&#39;s LinkedIn\]\]/g,
                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                vm.staticEmailTemplate.linkedin +
                                '">' +
                                vm.staticEmailTemplate.linkedin +
                                '</a>',
                        )
                        .replace(
                            /\[\[recruiter's LinkedIn\]\]/g,
                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                vm.staticEmailTemplate.linkedin +
                                '">' +
                                vm.staticEmailTemplate.linkedin +
                                '</a>',
                        );
                } else {
                    data.object.text = data.object.text
                        .replace(/\[\[recruiter&#39;s LinkedIn\]\]/g, '')
                        .replace(/\[\[recruiter's LinkedIn\]\]/g, '');
                }
                if ($rootScope.me.emails.length == 1) {
                    data.object.text = data.object.text.replace(
                        /\[\[recruiterEmail\]\]/g,
                        $rootScope.me.emails[0].email,
                    );
                }
                initCkEditorTemplate(data);
            });
        }

        function initCkEditorTemplate(data) {
            vm.sendEmailTemplate.template = data.object;
            if (vm.sendEmailTemplate) vm.sendEmailTemplate.email = '';
            if (Email.getDefaultMailbox() && vm.sendEmailTemplate)
                vm.sendEmailTemplate.email = Email.getDefaultMailbox()[0].email;
            vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text.replace(
                /\[\[candidate name\]\]/g,
                $rootScope.candnotify ? $rootScope.candnotify.fullName : '',
            );

            if ($rootScope.me.contacts.length === 1) {
                if ($rootScope.me.contacts[0].contactType === 'mphone') {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(
                            /\[\[recruiter&#39;s phone\]\]/g,
                            $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                        )
                        .replace(
                            /\[\[recruiter's phone\]\]/g,
                            $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                        );
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s phone\]\]/g, '')
                        .replace(/\[\[recruiter's phone\]\]/g, '');
                }

                if ($rootScope.me.contacts[0].contactType === 'phoneWork') {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(
                            /\[\[recruiter&#39;s phone 2\]\]/g,
                            $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                        )
                        .replace(
                            /\[\[recruiter's phone 2\]\]/g,
                            $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                        );
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s phone 2\]\]/g, '')
                        .replace(/\[\[recruiter's phone 2\]\]/g, '');
                }

                if ($rootScope.me.contacts[0].contactType === 'skype') {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(
                            /\[\[recruiter&#39;s Skype\]\]/g,
                            $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                        )
                        .replace(
                            /\[\[recruiter's Skype\]\]/g,
                            $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                        );
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s Skype\]\]/g, '')
                        .replace(/\[\[recruiter's Skype\]\]/g, '');
                }

                if ($rootScope.me.contacts[0].contactType === 'facebook') {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(
                            /\[\[recruiter&#39;s Facebook\]\]/g,
                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                $rootScope.me.contacts[0].value +
                                '">' +
                                $rootScope.me.contacts[0].value +
                                '</a>',
                        )
                        .replace(
                            /\[\[recruiter's Facebook\]\]/g,
                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                $rootScope.me.contacts[0].value +
                                '">' +
                                $rootScope.me.contacts[0].value +
                                '</a>',
                        );
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s Facebook\]\]/g, '')
                        .replace(/\[\[recruiter's Facebook\]\]/g, '');
                }

                if ($rootScope.me.contacts[0].contactType === 'linkedin') {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(
                            /\[\[recruiter&#39;s LinkedIn\]\]/g,
                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                $rootScope.me.contacts[0].value +
                                '">' +
                                $rootScope.me.contacts[0].value +
                                '</a>',
                        )
                        .replace(
                            /\[\[recruiter's LinkedIn\]\]/g,
                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                $rootScope.me.contacts[0].value +
                                '">' +
                                $rootScope.me.contacts[0].value +
                                '</a>',
                        );
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s LinkedIn\]\]/g, '')
                        .replace(/\[\[recruiter's LinkedIn\]\]/g, '');
                }
            }

            if ($rootScope.me.contacts.length === 2) {
                if (
                    $rootScope.me.contacts[0].contactType === 'mphone' ||
                    $rootScope.me.contacts[1].contactType === 'mphone'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s phone\]\]/g, '')
                        .replace(/\[\[recruiter's phone\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'phoneWork' ||
                    $rootScope.me.contacts[1].contactType === 'phoneWork'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s phone 2\]\]/g, '')
                        .replace(/\[\[recruiter's phone 2\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'skype' ||
                    $rootScope.me.contacts[1].contactType === 'skype'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s Skype\]\]/g, '')
                        .replace(/\[\[recruiter's Skype\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'facebook' ||
                    $rootScope.me.contacts[1].contactType === 'facebook'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s Facebook\]\]/g, '')
                        .replace(/\[\[recruiter's Facebook\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'linkedin' ||
                    $rootScope.me.contacts[1].contactType === 'linkedin'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s LinkedIn\]\]/g, '')
                        .replace(/\[\[recruiter's LinkedIn\]\]/g, '');
                }
            }

            if ($rootScope.me.contacts.length === 3) {
                if (
                    $rootScope.me.contacts[0].contactType === 'mphone' ||
                    $rootScope.me.contacts[1].contactType === 'mphone' ||
                    $rootScope.me.contacts[2].contactType === 'mphone'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s phone\]\]/g, '')
                        .replace(/\[\[recruiter's phone\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'phoneWork' ||
                    $rootScope.me.contacts[1].contactType === 'phoneWork' ||
                    $rootScope.me.contacts[2].contactType === 'phoneWork'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s phone 2\]\]/g, '')
                        .replace(/\[\[recruiter's phone 2\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'skype' ||
                    $rootScope.me.contacts[1].contactType === 'skype' ||
                    $rootScope.me.contacts[2].contactType === 'skype'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s Skype\]\]/g, '')
                        .replace(/\[\[recruiter's Skype\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'facebook' ||
                    $rootScope.me.contacts[1].contactType === 'facebook' ||
                    $rootScope.me.contacts[2].contactType === 'facebook'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[2].value +
                                    '">' +
                                    $rootScope.me.contacts[2].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[2].value +
                                    '">' +
                                    $rootScope.me.contacts[2].value +
                                    '</a>',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s Facebook\]\]/g, '')
                        .replace(/\[\[recruiter's Facebook\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'linkedin' ||
                    $rootScope.me.contacts[1].contactType === 'linkedin' ||
                    $rootScope.me.contacts[2].contactType === 'linkedin'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[2].value +
                                    '">' +
                                    $rootScope.me.contacts[2].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[2].value +
                                    '">' +
                                    $rootScope.me.contacts[2].value +
                                    '</a>',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s LinkedIn\]\]/g, '')
                        .replace(/\[\[recruiter's LinkedIn\]\]/g, '');
                }
            }

            if ($rootScope.me.contacts.length === 4) {
                if (
                    $rootScope.me.contacts[0].contactType === 'mphone' ||
                    $rootScope.me.contacts[1].contactType === 'mphone' ||
                    $rootScope.me.contacts[2].contactType === 'mphone' ||
                    $rootScope.me.contacts[3].contactType === 'mphone'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[3].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[3].value ? $rootScope.me.contacts[3].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[3].value ? $rootScope.me.contacts[3].value : '',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s phone\]\]/g, '')
                        .replace(/\[\[recruiter's phone\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'phoneWork' ||
                    $rootScope.me.contacts[1].contactType === 'phoneWork' ||
                    $rootScope.me.contacts[2].contactType === 'phoneWork' ||
                    $rootScope.me.contacts[3].contactType === 'phoneWork'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[3].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[3].value ? $rootScope.me.contacts[3].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[3].value ? $rootScope.me.contacts[3].value : '',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s phone 2\]\]/g, '')
                        .replace(/\[\[recruiter's phone 2\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'skype' ||
                    $rootScope.me.contacts[1].contactType === 'skype' ||
                    $rootScope.me.contacts[2].contactType === 'skype' ||
                    $rootScope.me.contacts[3].contactType === 'skype'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[3].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[3].value ? $rootScope.me.contacts[3].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[3].value ? $rootScope.me.contacts[3].value : '',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s Skype\]\]/g, '')
                        .replace(/\[\[recruiter's Skype\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'facebook' ||
                    $rootScope.me.contacts[1].contactType === 'facebook' ||
                    $rootScope.me.contacts[2].contactType === 'facebook' ||
                    $rootScope.me.contacts[3].contactType === 'facebook'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[2].value +
                                    '">' +
                                    $rootScope.me.contacts[2].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[2].value +
                                    '">' +
                                    $rootScope.me.contacts[2].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[3].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[3].value +
                                    '">' +
                                    $rootScope.me.contacts[3].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[3].value +
                                    '">' +
                                    $rootScope.me.contacts[3].value +
                                    '</a>',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s Facebook\]\]/g, '')
                        .replace(/\[\[recruiter's Facebook\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'linkedin' ||
                    $rootScope.me.contacts[1].contactType === 'linkedin' ||
                    $rootScope.me.contacts[2].contactType === 'linkedin' ||
                    $rootScope.me.contacts[3].contactType === 'linkedin'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[2].value +
                                    '">' +
                                    $rootScope.me.contacts[2].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[2].value +
                                    '">' +
                                    $rootScope.me.contacts[2].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[3].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[3].value +
                                    '">' +
                                    $rootScope.me.contacts[3].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[3].value +
                                    '">' +
                                    $rootScope.me.contacts[3].value +
                                    '</a>',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s LinkedIn\]\]/g, '')
                        .replace(/\[\[recruiter's LinkedIn\]\]/g, '');
                }
            }

            if ($rootScope.me.contacts.length === 5) {
                if (
                    $rootScope.me.contacts[0].contactType === 'mphone' ||
                    $rootScope.me.contacts[1].contactType === 'mphone' ||
                    $rootScope.me.contacts[2].contactType === 'mphone' ||
                    $rootScope.me.contacts[3].contactType === 'mphone' ||
                    $rootScope.me.contacts[4].contactType === 'mphone'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[3].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[3].value ? $rootScope.me.contacts[3].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[3].value ? $rootScope.me.contacts[3].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[4].contactType === 'mphone') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[4].value ? $rootScope.me.contacts[4].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[4].value ? $rootScope.me.contacts[4].value : '',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s phone\]\]/g, '')
                        .replace(/\[\[recruiter's phone\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'phoneWork' ||
                    $rootScope.me.contacts[1].contactType === 'phoneWork' ||
                    $rootScope.me.contacts[2].contactType === 'phoneWork' ||
                    $rootScope.me.contacts[3].contactType === 'phoneWork' ||
                    $rootScope.me.contacts[4].contactType === 'phoneWork'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[3].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[3].value ? $rootScope.me.contacts[3].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[3].value ? $rootScope.me.contacts[3].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[4].contactType === 'phoneWork') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[4].value ? $rootScope.me.contacts[4].value : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[4].value ? $rootScope.me.contacts[4].value : '',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s phone 2\]\]/g, '')
                        .replace(/\[\[recruiter's phone 2\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'skype' ||
                    $rootScope.me.contacts[1].contactType === 'skype' ||
                    $rootScope.me.contacts[2].contactType === 'skype' ||
                    $rootScope.me.contacts[3].contactType === 'skype' ||
                    $rootScope.me.contacts[4].contactType === 'skype'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[2].value ? $rootScope.me.contacts[2].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[3].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[3].value ? $rootScope.me.contacts[3].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[3].value ? $rootScope.me.contacts[3].value : '',
                            );
                    }
                    if ($rootScope.me.contacts[4].contactType === 'skype') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Skype\]\]/g,
                                $rootScope.me.contacts[4].value ? $rootScope.me.contacts[4].value : '',
                            )
                            .replace(
                                /\[\[recruiter's Skype\]\]/g,
                                $rootScope.me.contacts[4].value ? $rootScope.me.contacts[4].value : '',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s Skype\]\]/g, '')
                        .replace(/\[\[recruiter's Skype\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'facebook' ||
                    $rootScope.me.contacts[1].contactType === 'facebook' ||
                    $rootScope.me.contacts[2].contactType === 'facebook' ||
                    $rootScope.me.contacts[3].contactType === 'facebook' ||
                    $rootScope.me.contacts[4].contactType === 'facebook'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[2].value +
                                    '">' +
                                    $rootScope.me.contacts[2].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[2].value +
                                    '">' +
                                    $rootScope.me.contacts[2].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[3].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[3].value +
                                    '">' +
                                    $rootScope.me.contacts[3].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[3].value +
                                    '">' +
                                    $rootScope.me.contacts[3].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[4].contactType === 'facebook') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[4].value +
                                    '">' +
                                    $rootScope.me.contacts[4].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[4].value +
                                    '">' +
                                    $rootScope.me.contacts[4].value +
                                    '</a>',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s Facebook\]\]/g, '')
                        .replace(/\[\[recruiter's Facebook\]\]/g, '');
                }

                if (
                    $rootScope.me.contacts[0].contactType === 'linkedin' ||
                    $rootScope.me.contacts[1].contactType === 'linkedin' ||
                    $rootScope.me.contacts[2].contactType === 'linkedin' ||
                    $rootScope.me.contacts[3].contactType === 'linkedin' ||
                    $rootScope.me.contacts[4].contactType === 'linkedin'
                ) {
                    if ($rootScope.me.contacts[0].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[0].value +
                                    '">' +
                                    $rootScope.me.contacts[0].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[1].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[1].value +
                                    '">' +
                                    $rootScope.me.contacts[1].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[2].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[2].value +
                                    '">' +
                                    $rootScope.me.contacts[2].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[2].value +
                                    '">' +
                                    $rootScope.me.contacts[2].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[3].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[3].value +
                                    '">' +
                                    $rootScope.me.contacts[3].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[3].value +
                                    '">' +
                                    $rootScope.me.contacts[3].value +
                                    '</a>',
                            );
                    }
                    if ($rootScope.me.contacts[4].contactType === 'linkedin') {
                        vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[4].value +
                                    '">' +
                                    $rootScope.me.contacts[4].value +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.me.contacts[4].value +
                                    '">' +
                                    $rootScope.me.contacts[4].value +
                                    '</a>',
                            );
                    }
                } else {
                    vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text
                        .replace(/\[\[recruiter&#39;s LinkedIn\]\]/g, '')
                        .replace(/\[\[recruiter's LinkedIn\]\]/g, '');
                }
            }

            if ($rootScope.me.emails.length == 1) {
                vm.sendEmailTemplate.template.text = vm.sendEmailTemplate.template.text.replace(
                    /\[\[recruiterEmail\]\]/g,
                    $rootScope.me.emails[0].email,
                );
            }
            vm.addEmailInDescriptionFromLocalStorage();
            if (vm.sendEmailTemplate.template.filesIdName) {
                $rootScope.fileForSave = Service.filesIdNameToArray(vm.sendEmailTemplate.template.filesIdName);
            }
        }

        function setVacanciesForCandidatesAccess(access) {
            if (access === 'publicAccess') {
                vm.accessVacancies = false;
            } else if (access === 'privateAccess') {
                vm.accessVacancies = true;
            }
            $rootScope.$$phase || $scope.$apply();
        }

        vm.hideOrShowPublicVacancy = () => {
            vm.accessVacancies = !vm.accessVacancies;
            Vacancy.requestChangeVacanciesForCandidatesAccess(vm.accessVacancies, vm.vacancy.vacancyId);
            $rootScope.$$phase || $scope.$apply();
        };

        function refreshResponsibleArray() {
            if (!this.meObject) {
                const needle = vm.persons.find(({ personId }) => personId === $rootScope.me.userId);
                this.meObject = {
                    ...needle,
                    fullName: `(${$filter('translate')('Me')}) ${needle.fullName}`,
                };
            }

            let hideMe = false;

            vm.vacancy.responsiblesPerson.forEach((resp) => {
                if (resp.personId === $rootScope.me.userId) {
                    hideMe = true;
                }
                vm.persons = vm.persons.filter((person) => person.personId !== resp.personId);
            });

            if (hideMe) {
                vm.responsibleOptions = vm.persons.filter(
                    (person) => person.status === 'A' && person.personId !== $rootScope.me.userId,
                );
            } else {
                vm.responsibleOptions = [
                    this.meObject,
                    ...vm.persons.filter((person) => person.status === 'A' && person.personId !== $rootScope.me.userId),
                ];
            }
        }

        function getUsers() {
            Person.getUsers(function (resp) {
                vm.persons = [];
                vm.statusPerson = resp;
                vm.associativePerson = resp.object;
                angular.forEach(vm.associativePerson, function (val, key) {
                    if (angular.equals(resp.status, 'ok')) {
                        vm.persons.push(vm.associativePerson[key]);
                    }
                });
                vm.persons = vm.persons.sort((a, b) =>
                    a.fullName > b.fullName ? 1 : b.fullName > a.fullName ? -1 : 0,
                );

                refreshResponsibleArray();

                // $rootScope.persons = vm.persons;
                $rootScope.personsEdit = vm.persons;
                var iUser = null;
                for (var i = 0; i <= vm.persons.length - 1; i++) {
                    if ($rootScope.me.userId == vm.persons[i].userId) {
                        iUser = vm.persons[i];
                        vm.persons.splice(i, 1);
                        break;
                    }
                }
                if (iUser) {
                    vm.persons.unshift(iUser);
                }

                var personsCount = vm.associativePerson.length;

                if (personsCount > 1) {
                    vm.needAutoSetResponsible = false;
                } else if (
                    personsCount == 1 &&
                    (vm.vacancy.responsiblesPerson || vm.vacancy.responsiblesPerson.length == 0)
                ) {
                    vm.needAutoSetResponsible = true;
                } else {
                    vm.needAutoSetResponsible = false;
                }
                vm.test = { value: 'hehelgerg', label: 'Sex' };
                $rootScope.$$phase || $scope.$apply();
            });
        }

        function vacancyChangeStatusModal() {
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/vacancy-change-status-in-description.html',
                backdrop: 'static',
                scope: $scope,
                size: '',
                resolve: function () {},
            });
            vm.modalInstance.closed.then(function () {
                setTimeout(function () {
                    if (vm.canceledChange) {
                        vm.statusForChange = {
                            label: vm.changeStateObject.status_old,
                            value: vm.changeStateObject.status_old,
                            color: ['open', 'inwork', 'completed'].includes(vm.changeStateObject.status_old)
                                ? '#77B472'
                                : '#F5C620',
                        };
                        $rootScope.$$phase || $scope.$apply();
                    }
                }, 0);
            });
        }

        function getTestById(id) {
            return vm.tests.filter((test) => {
                return test.test.id === id;
            })[0];
        }

        function setCompanyInfo() {
            Service.getOrgLogoId({ orgId: vm.vacancy.orgId }, function (logoResp) {
                if (logoResp.status && logoResp.status === 'ok') {
                    vm.companyLogo = logoResp.object;
                    if (vm.companyLogo && vm.companyLogo !== '') {
                        vm.publicImgLink = vm.companyLogo
                            ? $location.$$protocol +
                              '://' +
                              $location.$$host +
                              vm.serverAddress +
                              '/getlogo?id=' +
                              vm.companyLogo
                            : null;
                    } else {
                        vm.publicImgLink = 'https://cleverstaff.net/images/sprite/icon_128_128_png.png';
                    }
                }
            });
        }

        function setPromoLogoLink() {
            $rootScope.promoLogo = vm.vacancy.imageId;
            if ($rootScope.promoLogo) {
                $rootScope.promoLogoLink = $rootScope.promoLogo
                    ? $location.$$protocol +
                      '://' +
                      $location.$$host +
                      vm.serverAddress +
                      '/getlogo?id=' +
                      $rootScope.promoLogo +
                      '&d=true'
                    : null;
            } else {
                $rootScope.promoLogoLink = 'https://cleverstaff.net/images/sprite/vacancy-new.jpg';
            }
        }

        function setDescriptionData() {
            vm.tests = vm.parent.tests;
            vm.noAllowedMails = vm.parent.noAllowedMails;
            vm.objectId = vm.vacancy.vacancyId;
            vm.hideSalary = vm.vacancy.hideSalary;
            vm.statusForChange = {
                label: vm.vacancy.status,
                value: vm.vacancy.status,
                color: ['open', 'inwork', 'completed'].includes(vm.vacancy.status) ? '#77B472' : '#F5C620',
            };

            vm.publicLink = $location.$$protocol + '://' + $location.$$host + '/i/vacancy-' + vm.vacancy.localId;
            vm.publicName = vm.vacancy.position;
            vm.serverAddress = serverAddress;
            vm.staticEmailTemplate = {
                candidateName: 'John Dou',
                vacancyLink: vm.vacancy.position,
                date: 1463749200000,
                recruiterName: $rootScope.me.fullName,
                recruiterEmail: $rootScope.me.emails.length > 0 ? $rootScope.me.emails[0].email : $rootScope.me.login,
            };

            parseDescription();
            initVacancyFiles();
            setGMap();
            setVacancyPublishing();
        }

        function parseDescription() {
            vm.vacancy.descrCopy = angular.copy(vm.vacancy.descr);
            if (vm.vacancy.descrCopy && vm.vacancy.descrCopy[0] !== '<') {
                vm.vacancy.descrCopy = '<p>' + vm.vacancy.descrCopy + '<p>';
            }
            angular.forEach(
                angular.element(vm.vacancy.descrCopy).text().replace('\r\n', '\n').split('\n'),
                function (val) {
                    if (val !== undefined && val !== '') {
                        vm.publicDescr += val + ' ';
                    }
                },
            );
        }

        function initVacancyFiles() {
            if (vm.vacancy.files) {
                if (vm.vacancy.files.length) {
                    angular.forEach(vm.vacancy.files, function (val) {
                        val.fileResolution = Service.getFileResolutionFromName(val.fileName);
                        Service.initDocuments(val);
                    });
                }
            }
        }

        function setGMap() {
            if (!!google) {
                vm.map = {
                    center: {
                        latitude: 48.379433,
                        longitude: 31.165579999999977,
                    },
                    zoom: 5,
                    options: {
                        panControl: true,
                        zoomControl: true,
                        scaleControl: true,
                        mapTypeControl: true,
                        mapTypeId: 'roadmap',
                    },
                };
                vm.marker = {
                    id: 1,
                    title: '',
                    coords: {
                        latitude: null,
                        longitude: null,
                    },
                };
            }
        }

        function setVacancyPublishing() {
            for (let i = vm.vacancy.publish.length - 1; i >= 0; i--) {
                if (vm.vacancy.publish[i].dd) {
                    vm.vacancy.publish.splice(i, 1);
                }
            }

            angular.forEach(vm.vacancy.publish, function (val) {
                vm.shareObj[val.type] = true;
            });
        }

        function updateCustomStages(resp) {
            var array = [];

            vm.custStages = resp.object ? resp.object.interviewStates : [];

            angular.forEach(vm.custStages, function (res) {
                res.value = res.name;
                res.movable = true;
                res.added = false;
                res.count = 0;
                if (res.status == 'A') array.push(res);
            });
            $rootScope.customStages = array;
            $scope.$apply();
        }

        function requestStages() {
            vacancyStages.requestVacancyStages().then((resp) => {
                updateCustomStages(resp);
            });
        }

        function transformVacancyStatistic(applyStatistic = {}, viewStatistic = {}) {
            let sortedStatistic = Object.entries(applyStatistic.byDate || []).reduce((prev, cur) => {
                prev.push({
                    statisticDate: cur[0],
                    applyStatisticValue: cur[1],
                });
                return prev;
            }, []);

            sortedStatistic = Object.entries(viewStatistic).reduce((prev, cur) => {
                prev.push({
                    statisticDate: cur[0],
                    viewStatisticValue: cur[1],
                });
                return prev;
            }, sortedStatistic);

            let foo = _.groupBy(sortedStatistic, 'statisticDate');

            return Object.entries(foo).reduce((prev, cur) => {
                prev.push(Object.assign(...cur[1]));
                return prev;
            }, []);
        }

        function calculateTotalValueOfStatistic(obj) {
            vm.vacancyStatistic.applyStatisticTotal = obj.reduce((prev, cur) => {
                return (prev += cur.applyStatisticValue || 0);
            }, 0);
            vm.vacancyStatistic.viewStatisticTotal = obj.reduce((prev, cur) => {
                return (prev += cur.viewStatisticValue || 0);
            }, 0);
        }

        function translateLanguages() {
            Service.onGetLanguagesCached().then((languages) => {
                angular.forEach(vm.vacancy.languages, function (val) {
                    if (val.name != undefined) {
                        angular.forEach(languages, function (item) {
                            if (item.key == val.name) {
                                val.translatedName = $filter('translateLangs')(item.translation);
                            }
                        });
                    }
                });
            });
        }

        function onSanitizeVacancyFields(vacancy) {
            if (!vacancy) return;

            if (vacancy.descr) vacancy.descr = Service.sanitizeStringFromXSS(vacancy.descr);
            if (vacancy.serviceInformation)
                vacancy.serviceInformation = Service.sanitizeStringFromXSS(vacancy.serviceInformation);
            if (vacancy.customFields)
                vacancy.customFields.forEach((customField) => {
                    if (customField.type === 'string' && customField.fieldValue) {
                        customField.fieldValue.value = Service.sanitizeStringFromXSS(customField.fieldValue.value);

                        if (customField.fieldValue.value) {
                            try {
                                customField.fieldValue.plainTextValue = customField.fieldValue.value.replaceAll(
                                    /<[^>]+>|&nbsp;/g,
                                    '',
                                );
                                customField.fieldValue.plainTextValueLength = $filter('linkify3')(
                                    customField.fieldValue.value,
                                ).replaceAll(/<[^>]+>|&nbsp;/g, '').length;
                            } catch (e) {
                                console.error(e);
                            }
                        }
                    }
                });
        }

        function getVacancyDescriptionData(isEvent) {
            $rootScope.loading = true;
            const localId = vm.parent.vacancy.localId;
            Vacancy.onGetByLocalId({
                localId: localId,
                fieldToGet: [
                    'accessType',
                    'creator',
                    'customFields',
                    'files',
                    'recallsCount',
                    'region',
                    'languages',
                    'publish',
                    'publishingCount',
                    'responsiblesPerson',
                    'visitsCount',
                    'scoreCard',
                    'skills',
                    'recallTemplate',
                ],
            })
                .then((resp) => {
                    vm.vacancy = resp.object;

                    $rootScope.originalVacancyForDelucru = angular.copy(resp.object);

                    if (
                        resp.object.experience == 'e00_no_experience' ||
                        resp.object.experience == 'e01_less_than1year'
                    ) {
                        $rootScope.employmentPostingData = 'work ua no experience required';
                    } else if (resp.object.experience == 'e1_1year') {
                        $rootScope.employmentPostingData = 'work ua more than 1 year';
                    } else if (
                        resp.object.experience == 'e2_2years' ||
                        resp.object.experience == 'e3_3years' ||
                        resp.object.experience == 'e4_4years'
                    ) {
                        $rootScope.employmentPostingData = 'work ua more than 2 years';
                    } else if (resp.object.experience == 'e5_5years' || resp.object.experience == 'e6_10years') {
                        $rootScope.employmentPostingData = 'work ua more than 5 years';
                    }
                    onSanitizeVacancyFields(vm.vacancy);
                    vm.vacancyStatistic = {};
                    if (!vm.vacancy.applyStatistic) vm.vacancy.applyStatistic = {};
                    if (vm.vacancy.category && vm.vacancy.category.category) {
                        vm.vacancy.category = vm.vacancy.category.category;
                    }
                    vm.vacancyHidden = vm.vacancy.hide;
                    if (vm.vacancy.skills && vm.vacancy.skills.length) {
                        vm.vacancy.requiredSkills = vm.vacancy.skills.filter((item) => item.mustHave);
                        vm.vacancy.niceToHaveSkills = vm.vacancy.skills.filter((item) => !item.mustHave);
                    }
                    $rootScope.currentVacancyPriority = vm.vacancy.priority;
                    vm.priorityModel = {
                        label: vm.vacancy.priority,
                        value: vm.vacancy.priority,
                        color:
                            vm.vacancy.priority === 'low'
                                ? '#3392ea'
                                : vm.vacancy.priority === 'medium'
                                ? '#f5c620'
                                : vm.vacancy.priority === 'top'
                                ? '#d67354'
                                : null,
                    };

                    if (isEvent) {
                        vm.parent.vacancy.position = resp.object.position;
                    }
                    if (vm.vacancy && vm.vacancy.applyStatistic && vm.vacancy.viewStatistic) {
                        vm.vacancyStatistic.statistic = transformVacancyStatistic(
                            vm.vacancy.applyStatistic,
                            vm.vacancy.viewStatistic,
                        );
                    }
                    if (vm.vacancyStatistic.statistic) calculateTotalValueOfStatistic(vm.vacancyStatistic.statistic);
                    if (vm.vacancy.customFields && vm.vacancy.customFields.length) {
                        vm.vacancy.customFields.forEach((item) => {
                            if (
                                (item.fieldValue && item.fieldValue.value) ||
                                (item.fieldValue && item.fieldValue.dateTimeValue)
                            )
                                vm.showCustomFields = true;
                        });
                    }
                    vm.isResponsiblePerson = false;
                    if (vm.vacancy.responsiblesPerson !== undefined && vm.vacancy.responsiblesPerson.length > 0) {
                        vm.vacancy.responsiblesPerson.forEach((resp) => {
                            // resp.type = {label: resp.type, value: resp.type};
                            if ($rootScope.me.userId === resp.personId) {
                                vm.isResponsiblePerson = true;
                            }
                        });
                    }
                    setVacanciesForCandidatesAccess(vm.vacancy.vacanciesForCandidatesAccess);
                    initFileAtach();
                    initDescriptionFileAtach();
                    setDescriptionData();
                    getUsers();
                    setCompanyInfo();
                    setPromoLogoLink();
                    vm.updateOrgPages();
                    requestStages();
                    vm.initCkEditorOptions();
                    translateLanguages();
                    vm.getCurrentOptions();
                    vm.testCountryFromNumber();
                })
                .catch((error) => {
                    console.error(error);
                    notificationService.error(error.message);
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        }

        this.$onInit = function () {
            getVacancyDescriptionData();
            $rootScope.$on('eventUpdateVacancyDescriptionData', function () {
                getVacancyDescriptionData(true);
            });

            vm.screenWidth = $window.innerWidth;
        };
    },
    controllerAs: 'vm',
});
