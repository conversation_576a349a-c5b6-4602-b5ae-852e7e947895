component.component('vacanciesSearchComponent', {
    bindings: {
        tableParams: '<',
    },
    templateUrl: 'partials/vacancy/search.html',
    controller: class {
        constructor(
            VacanciesSearchService,
            CustomFieldsSearchService,
            ScopeService,
            CustomField,
            Vacancy,
            Person,
            $scope,
            $rootScope,
            Service,
            $timeout,
            notificationService,
            $filter,
            $location,
            $stateParams,
        ) {
            this.vacanciesSearchService = VacanciesSearchService;
            this.customFieldService = CustomField;
            this.Person = Person;
            this.customFieldsSearchService = CustomFieldsSearchService;
            this.vacancyService = Vacancy;
            this.scopeService = ScopeService;

            this.$scope = $scope;
            this.$rootScope = $rootScope;
            this.$timeout = $timeout;
            this.$filter = $filter;
            this.$location = $location;
            this.notificationService = notificationService;
            this.$stateParams = $stateParams;

            this.translatedVacanciesSortOptions = [];
            this.selectedSortValue = {};
            this.changeSort = this.$scope.$parent.sortTable;

            this.allClientsOptions = [];
            this.clientsList = [];
            this.isHideMenuButton = true;
        }

        $onInit() {
            this._getCustomFields();
            this._initModel();
            this._initSearch();
            this._getSearch();
            this._initOnScopeChange();
            this._setActiveScopeParams();
            this._initEmitEventAdvancedSearch();
            this._getClientsList();
            this.handleOpenVacancies();

            this.$rootScope.$watch('currentLang', (newVal, oldVal) => {
                this.translatedVacanciesSortOptions = this.$scope.$parent.vacanciesSortOptions.map((option) => ({
                    ...option,
                    label: this.$filter('translate')(option.label),
                }));

                this.selectedSortValue = this.getSelectedSortValue();
            });

            this.$rootScope.$watch('vacancySortType', (newVal, oldVal) => {
                this.selectedSortValue = this.getSelectedSortValue();
            });

            if (this.$rootScope.isTabletWidth() && this.$rootScope.me.personParams?.vacanciesPageView === 'table') {
                this.toggleViewType();
            }
        }

        getSelectedSortValue() {
            if (!this.$rootScope.vacancySortType.name) return {};

            const { name: value, sort: direction } = this.$rootScope.vacancySortType;

            const { label } = this.translatedVacanciesSortOptions.find(
                (option) => option.value === this.$rootScope.vacancySortType.name,
            );
            return { label, value, direction };
        }

        goToCreateVacancy() {
            this.$location.path('/vacancy/add');
        }

        changeScopeVisibility(event) {
            if (this.$rootScope.scopeActiveObject) {
                if (element === 'My data' && this.$rootScope.scopeActiveObject.name !== 'region') {
                    this.$scope.changeScope('onlyMy', this.$scope.orgId, null, false);
                } else if (element === 'All data' && this.$rootScope.scopeActiveObject.name !== 'region') {
                    this.$scope.changeScope('company', this.$scope.orgId, null, false);
                } else if (element === 'My data' && this.$rootScope.scopeActiveObject.name === 'region') {
                    this.$scope.changeScope('onlyMy', this.$scope.orgId, null, false);
                }
            }
        }

        changeSearchType = (event) => {
            this.search.fields.matchType.value = event.target.checked;
            this.$rootScope.$$phase || this.$scope.$apply();
        };

        clearVacancySearch() {
            this.vacancyService.search.params = {
                country: null,
                city: null,
                name: null,
                personId: null,
                responsibleIds: null,
                words: null,
                customFields: null,
                clientId: null,
                creator: null,
                id: null,
                ids: null,
                org: null,
                position: null,
                regions: null,
                responsible: null,
                searchFullTextType: null,
                containsWord: null,
                states: null,
            };
        }

        moveSearch() {}

        _getCustomFields() {
            this.customFieldService.getCustomFieldsByType({ objectType: 'vacancy' }).then(
                (resp) => {
                    this.customFields = resp.objects;
                    if (!this.customFieldsSearch) {
                        this._initCustomFields();
                        this._setPreviousValuesForCustomFields();
                    }
                    this._getVacanciesFilterTemplate();
                },
                (error) => {
                    this.notificationService.error(error.message);
                    this.$rootScope.loading = false;
                },
            );
        }

        _setPreviousValuesForCustomFields() {
            if (this.vacancyService.search.customFieldsValues && this.customFieldsSearch) {
                this.customFieldsSearch.fields =
                    angular.copy(this.vacancyService.search.customFieldsValues) || this.customFieldsSearch.fields;
            }
        }

        _initCustomFields() {
            this.customFieldsSearch = new this.customFieldsSearchService();

            this.customFields.forEach((field) => {
                switch (field.type) {
                    case 'select':
                        this.customFieldsSearch.addField({
                            id: field.fieldId,
                            name: field.fieldId,
                            type: field.type,
                            data: field.params,
                            placeholder: field.title,
                            path: {
                                label: 'value.value',
                                value: 'value.value',
                            },
                            state: { isActive: true, isSelected: false },
                        });
                        break;
                    case 'date':
                    case 'datetime':
                        this.customFieldsSearch.addField({
                            id: field.fieldId,
                            name: field.fieldId,
                            type: field.type,
                            placeholder: field.title,
                            state: { isActive: true, isSelected: false },
                        });
                        break;
                    default:
                        this.customFieldsSearch.addField({
                            id: field.fieldId,
                            name: field.fieldId,
                            type: field.type,
                            placeholder: field.title,
                            path: { label: 'value', value: 'value' },
                            state: { isActive: true, isSelected: false },
                        });
                }
            });
        }

        _getVacanciesFilterTemplate() {
            this.vacancyService.onGetVacancyFilterTemplate().then(
                (resp) => {
                    this.selectedFields = resp.object.fields;

                    for (let k in this.search.fields) {
                        if (!this.search.fields[k].value) {
                            this.search.fields[k].state.isSelected = false;
                        }
                    }

                    for (let k in this.customFieldsSearch.fields) {
                        if (!this.customFieldsSearch.fields[k].value) {
                            this.customFieldsSearch.fields[k].state.isSelected = false;
                        }
                    }

                    if (resp.object.fields) {
                        resp.object.fields.forEach((field) => {
                            if (this.search.fields[field]) {
                                this.search.fields[field].state.isSelected = true;
                            }
                            if (this.customFieldsSearch && this.customFieldsSearch.fields[field]) {
                                this.customFieldsSearch.fields[field].state.isSelected = true;
                            }
                        });
                    }
                },
                (error) => {},
            );
        }

        _setActiveScopeParams() {
            this.activeScopeParam = this.scopeService.getActiveScopeObject();
        }

        _initOnScopeChange() {
            this.$scope.$on('scopeChanged', () => {
                this._setSearchParams();
            });
        }

        _initModel() {
            this.model = {
                criteria: {
                    general: true,
                    customFields: true,
                },
            };
        }

        _initSearch() {
            this.$rootScope.testRefusalStatusToChangeStage = [];
            this.search = new this.vacanciesSearchService();

            this.search.addField({
                name: 'boolean',
                placeholder: 'Search by vacancies',
            });
            this.search.addField({
                name: 'country',
                type: 'general',
                data: this.countries,
                placeholder: 'country',
                path: { label: 'value.showName', value: 'value.value' },
            });
            this.search.addField({
                name: 'city',
                type: 'general',
                data: this.filteredCities,
                placeholder: 'city',
                path: { label: 'value.showName', value: 'value.value' },
            });
            this.search.addField({
                name: 'status',
                type: 'general',
                placeholder: 'status',
                path: { label: 'value.name', value: 'value.value' },
            });
            this.search.addField({
                name: 'responsible',
                type: 'general',
                placeholder: 'responsible',
                // path: { label: 'value.fullName', value: 'value.userId' },
            });
            this.search.addField({
                name: 'client',
                type: 'general',
                placeholder: 'client',
                path: { label: 'value.name', value: 'value.id' },
            });
            this.search.addField({ name: 'matchType' });
        }

        setCandidateFilterValues() {
            this.vacancyService
                .onSaveVacancyFilterValues({
                    fields: [
                        ...this.search.getAllSelectedFieldsName(),
                        ...this.customFieldsSearch.getAllSelectedFieldsName(),
                    ],
                    userId: this.$rootScope.me.userId,
                })
                .then(
                    (resp) => {
                        this.notificationService.success(this.$filter('translate')('Template was saved'));
                    },
                    (error) => {},
                );
        }

        _isAnyFilledCustomField() {
            return (
                this.customFieldsSearch &&
                this.customFieldsSearch.fields &&
                Object.entries(this.customFieldsSearch.fields).some(([name, field]) => {
                    return field.getLabelValue();
                })
            );
        }

        _setSearchParams() {
            const fields = this.search.fields;
            let countries = null;
            this.vacancyService.search.resetPagination = true;
            this._setActiveScopeParams();
            let country = this.activeScopeParam.value ? this.activeScopeParam.value.value : fields.country.getValue();
            if (country) {
                if (country.value.length > 1) {
                    countries = country.value.map((val) => (val.id === 'Not specified' ? val.value : val.showName));
                    country = null;
                } else {
                    country =
                        country.value[0]?.id === 'Not specified' ? country.value[0]?.value : country.value[0]?.showName;
                }
            }

            this.vacancyService.search.params = {
                country,
                countries,
                city: fields.city.getValue(),
                name: null,
                personId: this.activeScopeParam.name === 'onlyMy' ? this.$rootScope.me.userId : null,
                responsibleIds: fields.responsible.getValue(),
                words: this.$filter('changeTabToSpace')(fields.boolean.getValue()),
                customFields: this.customFieldsSearch ? this.customFieldsSearch.getValues() : [],
                clientId: fields.client.getValue(),
                creator: null,
                id: null,
                ids: null,
                org: null,
                position: null,
                regions: null,
                responsible: null,
                searchFullTextType: fields.boolean.getValue() ? 'booleanSearch' : null,
                containsWord: this.search.fields.matchType.getValue(),
                states: fields.status.getValue(),
            };

            if (this.search) this.vacancyService.search.searchValues = angular.copy(this.search.fields);
            if (this.customFieldsSearch)
                this.vacancyService.search.customFieldsValues = angular.copy(this.customFieldsSearch.fields);
        }

        clearVacancySearch() {
            this.vacancyService.search.params = {
                country: null,
                city: null,
                name: null,
                personId: null,
                responsibleIds: null,
                words: null,
                customFields: null,
                clientId: null,
                creator: null,
                id: null,
                ids: null,
                org: null,
                position: null,
                regions: null,
                responsible: null,
                searchFullTextType: null,
                containsWord: null,
                states: null,
            };
        }

        _getSearch() {
            if (this.vacancyService.search.searchValues) {
                this.isTriggerSearch = true;
                for (let searchParams in this.vacancyService.search.searchValues) {
                    if (this.vacancyService.search.searchValues.hasOwnProperty(searchParams)) {
                        this.search.fields[searchParams] = angular.copy(
                            this.vacancyService.search.searchValues[searchParams],
                        );
                    }
                }
            }
            this.isTriggerSearchAll =
                (this.isTriggerSearch && this.search.isAnyFilledField()) ||
                this.$rootScope.scopeActiveObject.name === 'region' ||
                this.$rootScope.scopeActiveObject.name === 'onlyMy' ||
                this.search.searchFullTextType;
        }

        _isAnyFilledCustomField() {
            return (
                this.customFieldsSearch &&
                this.customFieldsSearch.fields &&
                Object.entries(this.customFieldsSearch.fields).some(([name, field]) => {
                    return field.getLabelValue();
                })
            );
        }

        _setStaticFields(state) {
            Object.entries(this.search.fields).forEach(([name, field]) => {
                field.state.isSelected = state;

                switch (name) {
                    case 'client':
                        field.reset({
                            callback: this.setClientAutocompleterPlaceholderValue,
                        });
                        break;
                    default:
                        field.reset({});
                }
            });
        }

        _setCustomFields(state) {
            Object.entries(this.customFieldsSearch.fields).forEach(([name, field]) => {
                field.state.isSelected = state;
                if (field.type === 'date' || field.type === 'datetime') {
                    field.state.isActive = true;
                }
                field.reset({ name });
            });
        }

        _resetStaticFields() {
            const state = this.model.criteria.general;

            Object.entries(this.search.fields).forEach(([name, field]) => {
                field.state.isSelected = state;

                switch (name) {
                    case 'country':
                        field.value = [];
                        break;
                    case 'client':
                        field.reset({
                            callback: this.setClientAutocompleterPlaceholderValue,
                        });
                        break;
                    default:
                        field.reset({});
                }
            });

            this.model.criteria.general = true;
        }

        _resetCustomFields() {
            const state = this.model.criteria.customFields;
            Object.entries(this.customFieldsSearch.fields).forEach(([name, field]) => {
                field.state.isSelected = state;
                if (field.type === 'date' || field.type === 'datetime') {
                    field.state.isActive = true;
                }
                field.reset({ name });
            });

            this.model.criteria.customFields = true;
        }

        resetSingleStatusValue(status) {
            this.search.fields.status.value = this.search.fields.status.value.filter((item) => item.value !== status);
        }

        resetSingleResponsibleValue(responsibleId) {
            this.search.fields.responsible.selectedResponsibles =
                this.search.fields.responsible.selectedResponsibles.filter(
                    (item) => item.value.userId !== responsibleId,
                );
        }

        resetFields() {
            this._resetStaticFields();
            this._resetCustomFields();
            this.isTriggerSearchAll = false;
            this.isTriggerSearch = false;
            this.triggerSearch();
        }

        _initEmitEventAdvancedSearch() {
            this.$scope.$on('openAdvancedSearch', () => {
                this.isToggleAdvancedSearch = true;
            });
        }

        toggleViewType() {
            this.$rootScope.me.personParams['vacanciesPageView'] =
                this.$rootScope.me.personParams['vacanciesPageView'] === 'list' ? 'table' : 'list';

            this.vacanciesPageView = this.$rootScope.me.personParams.vacanciesPageView;
            this.$scope.$parent.vacanciesPageView = this.$rootScope.me.personParams.vacanciesPageView;

            this.Person.onChangePageView({
                name: 'vacanciesPageView',
                value: this.$rootScope.me.personParams.vacanciesPageView,
            });
        }

        removeCountry = (index) => {
            this.search.fields.country.value = this.search.fields.country.value.filter((_, i) => i !== index);
            if (!this.search.fields.country.value.length) this.search.fields.country.value = null;
            this.$rootScope.$$phase || this.$scope.$apply();
        };

        triggerSearch() {
            this._setSearchParams();
            this.tableParams.reload();
            this.vacancyService.search.resetPagination = false;
            this.isTriggerSearch = true;
            this.isTriggerSearchAll =
                (this.isTriggerSearch && this.search.isAnyFilledField()) ||
                this.$rootScope.scopeActiveObject.name === 'region' ||
                this.$rootScope.scopeActiveObject.name === 'onlyMy' ||
                this.search.searchFullTextType;
        }

        handleOpenVacancies() {
            if (!this.$stateParams.status) {
                return;
            }

            this.search.fields.status.value = [
                {
                    value: this.$stateParams.status,
                    name: 'vacancy_status_assoc.' + this.$stateParams.status,
                    label: 'vacancy_status_assoc.' + this.$stateParams.status,
                },
            ];

            this.vacancyService.setIsLoadVacancies(true);
            this.triggerSearch();

            this.$rootScope.$$phase || this.$scope.$apply();
        }

        $onDestroy() {
            if (window.location.hash.substring(0, 12) !== '#/vacancies/') {
                this.clearVacancySearch();
            }

            this.vacancyService.search.params.sort = null;
            this.vacancyService.search.params.sortOrder = null;
        }

        _getClientsList() {
            const formData = new FormData();
            formData.append('text', '');

            fetch(`hr/client/autocompleteClients`, {
                method: 'POST',
                body: formData,
            })
                .then((response) => response.json())
                .then(({ objects }) => {
                    this.clientsList = objects;
                    this.$rootScope.$$phase || this.$scope.$apply();
                })
                .then(() => {
                    this.isHideMenuButton =
                        !this.clientsList.length &&
                        (this.$rootScope.me.recrutRole === 'freelancer' ||
                            this.$rootScope.me.recrutRole === 'researcher' ||
                            this.$rootScope.me.recrutRole === 'client');

                    this.$rootScope.$$phase || this.$scope.$apply();
                });
        }
    },
    controllerAs: 'vm',
});
