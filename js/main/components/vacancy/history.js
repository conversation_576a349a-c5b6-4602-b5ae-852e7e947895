component.component('vacancyHistory', {
    templateUrl: 'partials/vacancy/history.html',
    require: {
        parent: '^vacancyComponent',
    },
    controller: function (
        $rootScope,
        $scope,
        $filter,
        $translate,
        $state,
        $location,
        $sce,
        Vacancy,
        Service,
        Action,
        $uibModal,
        notificationService,
        $window,
        CandidatesSlider,
    ) {
        const vm = this;

        vm.actionsOptions = {};
        vm.commentVacancy = { comment: '', loading: false };
        vm.commentRemove = {
            person: {},
            comment: '',
            descr: '',
            actionId: null,
        };
        vm.tests = [];

        vm.isShowMore = false;
        vm.historyLimit = 15;
        vm.defaultPageNumber = 1;
        vm.defaultPageCount = 15;
        vm.pageNumber = 1;
        vm.pageCount = 15;

        vm.historyType = [
            { value: 'all_actions' },
            { value: 'vacancy_changes' },
            { value: 'vacancy_comments' },
            { value: 'candidate_comments' },
        ];

        vm.historyTypeModel = { value: 'all_actions' };

        vm.sliderId = CandidatesSlider.getSliderId();

        vm.closeModal = function () {
            vm.modalInstance.close();
        };

        vm.closeCommentVacancyModal = function () {
            vm.closeModal();
            vm.commentVacancy.comment = '';
        };

        vm.initOptions = (page, count, typeModel) => {
            vm.actionsOptions = {
                vacancyId: vm.vacancy !== undefined ? vm.vacancy.vacancyId : null,
                candidateId: null,
                clientId: null,
                page: { number: page || page === 0 ? page : vm.pageNumber, count: count ? count : vm.pageCount },
                type: typeModel ? typeModel : vm.historyTypeModel.value,
                ignoreType: [
                    'interview_add_from_advice_v1',
                    'candidate_remove_candidate_link',
                    'candidate_autoaction_test_send',
                    'candidate_autoaction_mail_send',
                    'candidate_autoaction_mail_send_failed',
                    'candidate_autoaction_test_send_failed',
                    'alpha_sms_success',
                ],
            };
            $rootScope.$$phase || $scope.$apply();
        };

        function getHistory(page, count, type, typeModel) {
            $rootScope.loading = true;
            vm.initOptions(page, count, typeModel);

            Service.history(vm.actionsOptions, function (response) {
                if (type !== 'more') {
                    vm.history = response['objects'];
                } else {
                    vm.history = vm.history.concat(response['objects']);
                }

                setDevidiers();

                vm.history.forEach((action) => {
                    if (action.type === 'task_change_status') {
                        if (action.stateNew === 'open') action.stateNew = 'inwork';
                        if (action.stateOld === 'open') action.stateOld = 'inwork';
                    }

                    if (action.type === 'set_interview_status') {
                        action.stagesArray = action.stateNew.split(',').map((state) => ({ value: state }));
                    }

                    action.descr = action.descr.replaceAll('&nbsp;', ' ');
                    action.descr = action.descr.replaceAll('rel="nofollow"', '');

                    $rootScope.initTagsStyles(action, false);

                    if (action.type === 'scorecard_assign' || action.type === 'delete_scorecard_assign') {
                        if (action.scoreCardNames) {
                            action.cards = JSON.parse(action.scoreCardNames);
                        } else {
                            action.cards = [action.descr];
                        }
                    }
                });

                vm.history.forEach((action) => {
                    if (action.stateNew || action.stateOld) {
                        $rootScope.customStages.forEach((stage) => {
                            if (action.type === 'set_interview_status') {
                                action.stagesArray.forEach((originalStage) => {
                                    if (originalStage.value === stage.customInterviewStateId) {
                                        originalStage['customInterviewStateId'] = stage.customInterviewStateId;
                                        originalStage.value = stage.name;
                                    }
                                });
                            } else {
                                if (stage.customInterviewStateId === action.stateOld) {
                                    action.customStateOld = action.stateOld;
                                    action.stateOld = stage.type;
                                }
                                if (stage.customInterviewStateId === action.stateNew) {
                                    action.customStateNew = action.stateNew;
                                    action.stateNew = stage.type;
                                }
                            }
                        });
                    }

                    if (action.customInterviewStates) {
                        $rootScope.customStages.forEach((stage) => {
                            action.customInterviewStates.forEach((custom) => {
                                if (stage.customInterviewStateId === custom.customInterviewStateId) {
                                    custom.stateNew = stage.type;
                                }
                            });
                        });
                    }
                });

                vm.allPageCount = response.allPageCount;
                vm.pageNumber = response.number + 1;
                vm.pageCount = response.size;
                vm.objectSize = response['objects'] ? response['total'] : 0;
                vm.showHistory = response.objects != undefined;
                $rootScope.loading = false;
                $rootScope.$$phase || $scope.$apply();
            });
        }

        vm.changePage = (pageNumber) => {
            if (vm.isShowMore) {
                getHistory(vm.previousSearchNumber, vm.pageCount);
                vm.isShowMore = false;
            } else {
                vm.previousSearchNumber = pageNumber - 1;
                getHistory(pageNumber - 1, vm.pageCount);
            }
            $scope.$apply();
        };

        vm.showMore = () => {
            vm.isShowMore = true;
            getHistory(vm.pageNumber, vm.pageCount, 'more');
            $scope.$apply();
        };

        vm.changeAmountOfElements = (amount) => {
            if (vm.pageCount === amount) return;
            getHistory(vm.defaultPageNumber - 1, amount);
            vm.isShowMore = false;
        };

        function setDevidiers() {
            let previouslyDate = null;

            vm.history.forEach((action, index) => {
                const currentDate = new Date(action.dm).toLocaleDateString();

                if (previouslyDate !== null && currentDate !== previouslyDate) {
                    vm.history[!index ? index : index - 1].dateDivider = action.dm;
                }

                previouslyDate = currentDate;
            });
        }

        function editUsersInTags(ckeditor) {
            let text = CKEDITOR.instances[ckeditor].getData();

            vm.deletedUsers.forEach((user, index) => {
                if (!text.includes(`${user.fullname}`)) {
                    if (user.responsible) {
                        vm.repsonsibleUsers.push(user);
                    } else {
                        vm.users.push(user);
                    }
                    vm.afterDelete.push(user.id);

                    vm.users.sort((a, b) => a.fullname.localeCompare(b.fullname));
                }
            });

            vm.afterDelete.forEach((id) => {
                vm.deletedUsers = vm.deletedUsers.filter((user) => user.id !== id);
            });
            vm.afterDelete = [];
        }

        function deleteFromTags(id, type) {
            vm.users.filter((user, index) => {
                if (user[type] == id) {
                    vm.users.splice(index, 1);
                }
            });
            vm.repsonsibleUsers.forEach((user, index) => {
                if (user.userId == id) {
                    vm.repsonsibleUsers.splice(index, 1);
                }
            });
        }

        vm.changeCommentFlag = function (history) {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/vacancy/edit-comment-vacancy.html',
                size: '',
                backdrop: true,
                scope: $scope,
                resolve: {},
            });
            $rootScope.closeModal = function () {
                $rootScope.initTagsStyles(history, true);
                $scope.modalInstance.close();
            };

            vm.users = [];
            vm.repsonsibleUsers = [];

            $rootScope.persons.forEach((i, index) => {
                if ($rootScope.vacancy.responsiblesPerson) {
                    $rootScope.vacancy.responsiblesPerson.forEach((vac) => {
                        if (i.userId === vac.personId && i.recrutRole !== 'researcher') {
                            vm.repsonsibleUsers.push({
                                id: index + 1,
                                fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                                userId: i.userId,
                                responsible: true,
                            });
                        }
                    });
                }
                if (
                    i.status === 'A' &&
                    i.recrutRole !== 'client' &&
                    i.recrutRole !== 'freelancer' &&
                    i.recrutRole !== 'researcher'
                ) {
                    vm.users.push({
                        id: index + 1,
                        fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                        userId: i.userId,
                        responsible: false,
                    });
                }
            });
            vm.users.sort((a, b) => a.fullname.localeCompare(b.fullname));

            $rootScope.ckEditorVacancyEdit = {
                height: 140,
                autoGrow_maxHeight: 340,
                toolbar: [],
                enterMode: CKEDITOR.ENTER_BR,
                shiftEnterMode: CKEDITOR.ENTER_BR,
                extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
                removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
                editorplaceholder: $translate.instant('mention placeholder'),
                mentions: [
                    {
                        feed: dataCallback,
                        minChars: 0,
                        itemTemplate: '<li style="padding: 10px" data-id="{id}">{fullname}</li>',
                        outputTemplate: `<a href="#" style="text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;">@{fullname}</a>&nbsp`,
                        marker: '@',
                        pattern: /@([\p{L}\p{M}]*(?:\s[\p{L}\p{M}]*)?)?$/u,
                    },
                ],
                on: {
                    paste: function (ev) {
                        ev.data.dataValue = '';
                        CKEDITOR.instances['ckEditorVacancyEdit'].insertText(ev.data.dataTransfer._.data.Text);
                        setTimeout(() => {
                            let bodyHeight = ev.editor.document.$.children[0].offsetHeight;
                            if (bodyHeight > ev.editor.config.height) {
                                if (bodyHeight < 605) {
                                    ev.editor.resize(null, bodyHeight);
                                }
                            }
                        }, 0);
                    },
                    afterInsertHtml: function (ev) {
                        let user = ev.editor.data;
                        if (user) {
                            deleteFromTags(user.userId, 'userId');
                            vm.deletedUsers.push(user);
                        }
                    },
                    instanceReady: function (ev) {
                        vm.deletedUsers = [];
                        vm.afterDelete = [];

                        ev.editor.dataProcessor.writer.setRules('p', {
                            indent: false,
                            breakBeforeOpen: false,
                            breakAfterOpen: false,
                            breakBeforeClose: false,
                            breakAfterClose: false,
                        });
                        ev.editor.dataProcessor.writer.setRules('br', {
                            indent: false,
                            breakBeforeOpen: false,
                            breakAfterOpen: false,
                            breakBeforeClose: false,
                            breakAfterClose: false,
                        });
                        $rootScope.convertTagsToSpan(history, 'ckEditorVacancyEdit');
                    },
                    resize: function (event) {
                        $rootScope.adjustCKEditorHeight(event);
                    },
                },
            };

            function dataCallback(opts, callback) {
                opts.query = opts.query.replace(/\u00A0/g, ' ');
                editUsersInTags('ckEditorVacancyEdit');
                let showAll =
                    vm.repsonsibleUsers.length === 1 && vm.repsonsibleUsers[0].userId === $rootScope.me.userId;

                if (opts.query.length === 0 && vm.repsonsibleUsers.length > 0 && !showAll) {
                    setTimeout(function () {
                        callback(
                            vm.repsonsibleUsers.filter(function (item) {
                                return item.fullname;
                            }),
                        );
                    });
                } else {
                    setTimeout(function () {
                        callback(
                            vm.users.filter(function (item) {
                                return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                            }),
                        );
                    });
                }
            }
            history.showAllCandidates = false;

            $rootScope.changeComment = function () {
                $rootScope.loading = true;
                let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorVacancyEdit.getData());
                vm.comment = text.replaceAll('<br />', ' &nbsp<br>&nbsp ');

                Action.onEditAction({ comment: vm.comment, actionId: history.actionId })
                    .then((resp) => {
                        history.descr = resp.object.descr;
                        history.dateEdit = resp.object.dateEdit;
                        $rootScope.initTagsStyles(history);
                    })
                    .catch((error) => {
                        console.error(error.message || error.statusText);
                    })
                    .finally(() => {
                        $scope.modalInstance.close();
                        $rootScope.loading = false;
                    });
            };
        };
        vm.openMenuWithCandidates = function (history) {
            history.showAllCandidatesNotSend = false;
            history.showAllCandidates = !history.showAllCandidates;
            history.editCommentFlag = false;
        };
        vm.openMenuWithCandidatesNotSend = function (history) {
            history.showAllCandidatesNotSend = !history.showAllCandidatesNotSend;
            history.showAllCandidates = false;
            history.editCommentFlag = false;
        };

        vm.changeHistoryType = function (type) {
            if (vm.historyTypeModel.value === type.value) return;
            vm.historyTypeModel = type;
            vm.previousSearchNumber = 0;
            getHistory(vm.defaultPageNumber - 1, vm.defaultPageCount, '', type.value);
            $rootScope.$$phase || $scope.$apply();
        };

        vm.routeOnCandidate = function (event, url, panel) {
            if (panel === 'history') {
                localStorage.setItem('isAddCandidates', false);
            } else if (panel === 'candidate') {
                localStorage.setItem('isAddCandidates', true);
            }
        };

        vm.historyToggleCandidatesToShow = function (history) {
            history.showCandidates = true;
        };

        vm.showModalAddCommentToVacancy = function () {
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/add-comment-vacancy.html',
                size: '',
                scope: $scope,
                resolve: function () {},
            });

            vm.users = [];
            vm.repsonsibleUsers = [];

            $rootScope.persons.forEach((i, index) => {
                if ($rootScope.vacancy.responsiblesPerson) {
                    $rootScope.vacancy.responsiblesPerson.forEach((vac) => {
                        if (i.userId === vac.personId && i.recrutRole !== 'researcher') {
                            vm.repsonsibleUsers.push({
                                id: index + 1,
                                fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                                userId: i.userId,
                                responsible: true,
                            });
                        }
                    });
                }
                if (
                    i.status === 'A' &&
                    i.recrutRole !== 'client' &&
                    i.recrutRole !== 'freelancer' &&
                    i.recrutRole !== 'researcher'
                ) {
                    vm.users.push({
                        id: index + 1,
                        fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                        userId: i.userId,
                        responsible: false,
                    });
                }
            });
            vm.users.sort((a, b) => a.fullname.localeCompare(b.fullname));

            $rootScope.ckEditorVacancyComment = {
                height: 140,
                autoGrow_maxHeight: 340,
                width: '100%',
                toolbar: [],
                enterMode: CKEDITOR.ENTER_BR,
                shiftEnterMode: CKEDITOR.ENTER_BR,
                extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
                removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
                editorplaceholder: $translate.instant('mention placeholder'),
                mentions: [
                    {
                        feed: dataCallback,
                        minChars: 0,
                        itemTemplate: '<li style="padding: 10px" data-id="{id}">{fullname}</li>',
                        outputTemplate: `<a href="#" style="text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;">@{fullname}</a>&nbsp`,
                        marker: '@',
                        pattern: /@([\p{L}\p{M}]*(?:\s[\p{L}\p{M}]*)?)?$/u,
                    },
                ],
                on: {
                    paste: function (ev) {
                        ev.data.dataValue = '';
                        CKEDITOR.instances['ckEditorVacancyComment'].insertText(ev.data.dataTransfer._.data.Text);
                        setTimeout(() => {
                            let bodyHeight = ev.editor.document.$.children[0].offsetHeight;
                            if (bodyHeight > ev.editor.config.height) {
                                if (bodyHeight < 605) {
                                    ev.editor.resize(null, bodyHeight);
                                }
                            }
                        }, 0);
                    },
                    afterInsertHtml: function (ev) {
                        let user = ev.editor.data;
                        if (user) {
                            deleteFromTags(user.userId, 'userId');
                            vm.deletedUsers.push(user);
                        }
                    },
                    instanceReady: function (ev) {
                        vm.deletedUsers = [];
                        vm.afterDelete = [];

                        ev.editor.dataProcessor.writer.setRules('p', {
                            indent: false,
                            breakBeforeOpen: false,
                            breakAfterOpen: false,
                            breakBeforeClose: false,
                            breakAfterClose: false,
                        });
                        ev.editor.dataProcessor.writer.setRules('br', {
                            indent: false,
                            breakBeforeOpen: false,
                            breakAfterOpen: false,
                            breakBeforeClose: false,
                            breakAfterClose: false,
                        });
                    },
                    resize: function (event) {
                        $rootScope.adjustCKEditorHeight(event);
                    },
                },
            };
            function dataCallback(opts, callback) {
                opts.query = opts.query.replace(/\u00A0/g, ' ');
                editUsersInTags('ckEditorVacancyComment');

                let showAll =
                    vm.repsonsibleUsers.length === 1 && vm.repsonsibleUsers[0].userId === $rootScope.me.userId;

                if (opts.query.length === 0 && vm.repsonsibleUsers.length > 0 && !showAll) {
                    setTimeout(function () {
                        callback(
                            vm.repsonsibleUsers.filter(function (item) {
                                return item.fullname;
                            }),
                        );
                    });
                } else {
                    setTimeout(function () {
                        callback(
                            vm.users.filter(function (item) {
                                return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                            }),
                        );
                    });
                }
            }
            $(document)
                .unbind('keydown')
                .keydown(function (e) {
                    if (e.ctrlKey == true && e.which == 13) {
                        vm.addCommentInVacancy();
                    }
                });
        };

        vm.addCommentInVacancy = function () {
            let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorVacancyComment.getData());
            vm.commentVacancy.comment = text.replaceAll('<br />', ' &nbsp<br>&nbsp ');

            if (vm.commentVacancy.comment != undefined && vm.commentVacancy.comment.length > 0) {
                vm.commentVacancy.loading = true;
                $rootScope.loading = true;
                Vacancy.setMessage(
                    {
                        comment: vm.commentVacancy.comment,
                        vacancyId: vm.vacancy.vacancyId,
                    },
                    function (resp) {
                        vm.commentVacancy.loading = false;
                        vm.commentVacancy.comment = null;
                        if (resp.status == 'ok') {
                            getHistory(vm.defaultPageNumber - 1, vm.defaultPageCount);
                            $rootScope.loading = false;
                            vm.closeModal();
                        }
                    },
                    function (error) {
                        vm.commentVacancy.loading = false;
                        $rootScope.loading = false;
                    },
                );
            }
        };

        vm.showDeleteComment = function (resp) {
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/delete-vacancy-comment.html',
                scope: $scope,
                resolve: {
                    items: function () {
                        return $scope.items;
                    },
                },
            });
            vm.commentRemove = resp;
        };

        vm.deleteComment = function () {
            $rootScope.loading = true;
            Action.removeMessageAction(
                {
                    actionId: vm.commentRemove.actionId,
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        notificationService.success($filter('translate')('Comment removed'));
                        getHistory(vm.defaultPageNumber - 1, vm.defaultPageCount);
                        $rootScope.loading = false;
                    } else {
                        notificationService.error(resp.message);
                        $rootScope.loading = false;
                    }
                    vm.closeModal();
                },
            );
        };

        vm.showFilePreview = function (history) {
            const file = {
                fileName: history.descr,
                fileId: JSON.parse(history.data).fileId,
            };
            Service.showModalResume(file, $scope, $rootScope, $location, $sce, $uibModal);
        };

        vm.getTestById = function (id) {
            return vm.tests.filter((test) => {
                return test.test.id === id;
            })[0];
        };

        $scope.toScorecard = function (value) {
            let stateParams = {
                scoreCardId: value.scoreCardId,
                candidateObj: value.candidate,
                id: value.candidate.localId,
                isFromVacancyToCandidate: value.vacancyId,
                isFromVacancyToEvaluate: true,
                sliderDataId: vm.sliderId,
                vacancyName: value.vacancy ? value.vacancy.position : null,
                vacancyId: value.vacancyId,
            };

            let link = $state.href('candidate-slide', stateParams, {
                reload: true,
            });
            $window.open(link, '_blank');
        };

        vm.$onInit = function () {
            this.vacancy = this.parent.vacancy;
            getHistory(vm.defaultPageNumber - 1, vm.defaultPageCount);
        };
    },
    controllerAs: 'vm',
});
