component.component('desiredSalaryBlock', {
    templateUrl: 'partials/dropdowns_and_blocks/desiredSalaryBlock.html',
    bindings: {
        model: '=',
        hasError: '=',
        currency: '=',
        progressUpdate: '=?',
    },
    controller: function ($scope, Service, $rootScope) {
        const vm = this;

        vm.$onInit = function () {
            vm.hasError = false;
            vm.hasErrorLength = false;
            vm.hasErrorValue = false;
            vm.currencies = Service.currency() || [];
        };

        vm.validSalary = function (model) {
            vm.model = model.replace(/[^0-9]/g, '');

            const MAX_SALARY_VALUE = 2147483647;
            const modelNumberValue = +vm.model;

            if (model.length > 10) {
                vm.model = model.slice(0, 10);
                vm.hasErrorLength = true;
                vm.hasErrorValue = false;
            } else if (modelNumberValue > MAX_SALARY_VALUE) {
                vm.hasErrorValue = true;
                vm.hasErrorLength = false;
            } else {
                vm.hasErrorLength = false;
                vm.hasErrorValue = false;
            }

            vm.hasError = vm.hasErrorLength || vm.hasErrorValue;
            $rootScope.$$phase || $scope.$apply();
        };
    },
    controllerAs: 'vm',
});
