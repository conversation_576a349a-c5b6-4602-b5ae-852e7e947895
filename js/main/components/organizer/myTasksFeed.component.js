class myTasksFeedCtrl {
    constructor(
        $scope,
        $rootScope,
        $translate,
        $timeout,
        $location,
        $uibModal,
        Vacancy,
        Candidate,
        Task,
        Service,
        notificationService,
        ScopeService,
        Person,
    ) {
        this.$scope = $scope;
        this.$rootScope = $rootScope;
        this.$translate = $translate;
        this.$timeout = $timeout;
        this.$location = $location;
        this.$uibModal = $uibModal;
        this.Vacancy = Vacancy;
        this.Candidate = Candidate;
        this.Service = Service;
        this.Task = Task;
        this.notificationService = notificationService;
        this.ScopeService = ScopeService;
        this.Person = Person;
    }

    $onInit() {
        this.showNewVersion = false;
        this.$rootScope.hideTasksFeed = true;
        this.versionObj = {};
        this.screenSize = window.innerWidth >= 1383 && window.innerWidth <= 1468;

        this.getNewVersion();

        this.$timeout(() => {
            this.$rootScope.loading = false;
        }, 1000);
    }

    getNewVersion = () => {
        if (this.$rootScope.me.personParams?.showBlogPostNotification) {
            this.versionObj = JSON.parse(this.$rootScope.me.personParams.showBlogPostNotification);
        }

        const saveVersionDate = localStorage.getItem('currentVersionDate');
        const todayDate = new Date()
            .toLocaleString('zh-CN', { year: 'numeric', month: 'numeric', day: 'numeric' })
            .replaceAll('/', '-');
        const todayDateMilliseconds = new Date().getTime();
        const tillDateMilliseconds = new Date(this.versionObj?.tillDate).getTime();

        if (this.versionObj && this.versionObj.tillDate && tillDateMilliseconds >= todayDateMilliseconds) {
            this.showNewVersion = tillDateMilliseconds >= todayDateMilliseconds;
            this.$rootScope.showNewVersion = this.showNewVersion;
            this.$rootScope.hideTasksFeed = false;
        } else {
            this.$rootScope.hideTasksFeed = !!(saveVersionDate && saveVersionDate === todayDate);
            localStorage.setItem('currentVersionDate', todayDate);
        }

        this.$rootScope.checkVersion = () => {
            this.$timeout(() => {
                if (!this.$rootScope.todayEvents && !this.showNewVersion) {
                    this.$rootScope.hideTasksFeed = true;
                }

                if (
                    this.$rootScope.todayEvents &&
                    this.$rootScope.todayEvents.length === 0 &&
                    !this.showNewVersion &&
                    this.$rootScope.me.recrutRole === 'client'
                ) {
                    this.$rootScope.hideTasksFeed = true;
                }
            }, 500);
        };

        this.$rootScope.checkVersion();
    };

    redirectToBlog = (type) => {
        let urlParam = '';
        if (type === 'blog') {
            urlParam = `https://cleverstaff.net/blog/${this.versionObj.urlPart}`;
        } else {
            urlParam = 'https://youtube.com/live/UNkQfuRivyA?feature=share';
        }
        window.open(urlParam);

        this.Person.onChangeUserParam({
            userId: this.$rootScope.me.userId,
            name: 'showBlogPostNotification',
            value: 'false',
        })
            .then(() => {
                this.$rootScope.hideTasksFeed = true;
                this.showNewVersion = false;
                this.$rootScope.$$phase || this.$scope.$apply();
            })
            .catch((err) => notificationService.error(err.message));
    };
}

const myTasksFeedCtrlDefinition = {
    bindings: {},
    templateUrl: 'partials/organizer/myTasksFeed.html',
    controller: myTasksFeedCtrl,
    controllerAs: 'vm',
};

component.component('myTasksFeed', myTasksFeedCtrlDefinition);
