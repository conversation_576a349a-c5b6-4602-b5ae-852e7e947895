const myTasksKanbanCtrlDefinition = {
    bindings: {},
    templateUrl: 'partials/organizer/kanban/myTasksKanban.html',
    controller: function (
        $scope,
        $rootScope,
        $translate,
        $timeout,
        $location,
        $uibModal,
        $filter,
        notificationService,
        Vacancy,
        Task,
        Person,
        Email,
        meetTimeValues,
        Candidate,
    ) {
        $rootScope.taskNewComment = '';
        $rootScope.interviewCommentModel = '';
        $rootScope.isKanbanLoading = true;
        $rootScope.loading = true;
        $rootScope.isEmptyEvents = false;
        $rootScope.eventDateModel = new Date(Date.now());
        $rootScope.vacancy = {};
        $rootScope.meetComment = '';
        $scope.meetDuration = { name: '1 hour', value: 60 };
        $scope.meetTimeValues = meetTimeValues;
        $scope.requiredInputs = ['candidateEmail', 'calendarTitle'];
        $scope.emptyRequiredInputs = [];

        $scope.showAddCommentField = false;
        $rootScope.myTasksLists = [
            {
                label: 'Today',
                events: [],
                allowedTypes: ['tomorrow', 'future', 'expired'],
            },
            {
                label: 'Tomorrow',
                events: [],
                allowedTypes: ['today', 'future', 'expired'],
            },
            {
                label: 'Future',
                events: [],
                allowedTypes: ['today', 'tomorrow', 'expired'],
            },
            {
                label: 'Expired',
                events: [],
                allowedTypes: [],
            },
        ];
        $scope.taskStatusOptions = [
            {
                value: 'inwork',
                name: 'inwork',
                status: 'inwork',
                sendStatus: 'open',
                color: '#77B472',
            },
            {
                value: 'completed',
                name: 'tasksStatuses.completed',
                status: 'completed',
                sendStatus: 'completed',
                color: '#eaa24d',
            },
            {
                value: 'Cancelled',
                name: 'tasksStatuses.Cancelled',
                sendStatus: 'Cancelled',
                status: 'Cancelled',
                color: '#eaa24d',
            },
        ];
        $scope.vacancyChangeInterviewDate = {
            date: null,
            dateOld: null,
            candidate: null,
            interviewObject: null,
            comment: null,
        };
        $scope.changeStatusOfInterviewInVacancy = {
            candidate: '',
            comment: '',
            status: '',
            date: null,
            exportgoogle: false,
        };
        $scope.candidate = {};
        $scope.lastDraggedEvent = null;
        $scope.wasDateOkClick = false;
        $scope.lastDraggedCardHeight = null;
        $scope.cardExpanded = false;
        $scope.taskHistory = [];
        $scope.historyComment = [];
        $scope.taskHistoryLimit = 15;
        $scope.historyAllPageCount = 0;
        $scope.showAllActions = false;
        $scope.googleCalendarIntegration = false;
        $scope.selectedGoogleMeetUsers = [];
        $scope.usersToTagOptions = [];
        $scope.interviewResponsibles = [];
        $scope.interviewComment = '';
        $scope.classesNotToInteract = ['popover', 'arrow', 'kanban-avatar', 'inside-popover'];

        //Watchers
        $rootScope.$$listeners['myEventsSwitcher'] ||
            $rootScope.$on('myEventsSwitcher', () => {
                $onInit();
            });

        $rootScope.$$listeners['selectDate'] ||
            $rootScope.$on('selectDate', (_, args) => {
                $scope.cardExpanded && warnUserAboutDate(Date.parse(args.newDate));
            });

        $rootScope.$$listeners['reloadKanban'] ||
            $rootScope.$on('reloadKanban', () => {
                $onInit();
            });
        //

        function checkForMeetEvent(meetObj) {
            if (meetObj.meetLink) {
                $scope[meetObj.meetType === 'googleMeet' ? 'googleMeet' : 'outlookMeet'] = true;
            }
        }

        function checkGoogleCalendarIntegration() {
            Person.getGoogleCalendar(function (resp) {
                $scope.googleCalendarIntegration = !!resp.object;
            });
        }

        function checkOutlookCalendarIntegration() {
            Person.getOutlookCalendar(function (resp) {
                $scope.outlookLabelText = 'Create teams-skype-meet';
                $scope.outlookTooltipText = '';
                if (resp.object) {
                    $scope.outlookCalendarIntegration = true;
                    $scope.availableOutlookMeetingType = resp.object.onlineMeetingProviderType;
                    switch ($scope.availableOutlookMeetingType) {
                        case 'TEAMS_FOR_BUSINESS':
                            $scope.outlookLabelText = 'Create teams-meet';
                            break;
                        case 'SKYPE_FOR_BUSINES':
                        case 'SKYPE_FOR_CONSUMER':
                            $scope.outlookLabelText = 'Create skype-meet';
                            break;
                        case 'UNKNOWN':
                            $scope.outlookTooltipText = 'Blocked Skype/Teams Meetings';
                            $scope.outlookLabelText = 'Create teams-skype-meet';
                    }
                } else {
                    $scope.outlookCalendarIntegration = false;
                }
            });
        }

        function changeCalendarTitle(interviewObj) {
            $scope.calendarTitle = $filter('translate')('Default title for the calendar', {
                vacancy: interviewObj.vacancyName,
                candidate: interviewObj.candidateId[$rootScope.useAmericanNameStyle ? 'fullNameEn' : 'fullName'],
            });
        }

        $scope.toggleTaskCommentField = () => {
            $scope.showAddCommentField = !$scope.showAddCommentField;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onDragStart = (event, eventItem) => {
            $scope.lastDraggedCardHeight = event.target.offsetHeight;
        };

        $scope.onClickEdit = (eventItem) => {
            if (eventItem.event.type === 'task') {
                $scope.$parent.showModalEditTask(eventItem.event);
            } else if (eventItem.event.type === 'payment_vacancy' || eventItem.event.type === 'finish_vacancy') {
                // $scope.lastDraggedEvent = eventItem;
                // $scope.openPaymentOrFinishDate(eventItem.event);
            } else {
                $rootScope.vacancy['responsiblesPerson'] = eventItem.event.vacancy.responsibles;
                $rootScope.isInterviewMeet = eventItem.event.type.includes('interview');
                $scope.openChangeVacancyInterviewDate({
                    ...eventItem.event.interview,
                    candidateId: eventItem.event.candidate,
                    vacancyName: eventItem.event.vacancy.position,
                });
            }
        };

        function formatInterviewComment(interviewObj, status) {
            if (interviewObj.comment) {
                interviewObj.comment = interviewObj.comment.replaceAll('&nbsp;', ' ');
                interviewObj.descrLength = interviewObj.comment.replace(/(<([^>]+)>)/gi, '').length;
                interviewObj.cutDescr = $filter('cutName')(interviewObj.comment.replace(/(<([^>]+)>)/gi, ''), true, 70);
                let regExp = new RegExp(`([a-zA-Z0-9]{32})`, 'g');
                let save = interviewObj.comment.match(regExp);
                save = [...new Set(save)];
                let getIds = interviewObj.comment.split('@');

                let indexOf = interviewObj.comment.indexOf('@');
                if (indexOf !== -1 && save !== null) {
                    $rootScope.persons.find((person) => {
                        save.forEach((ids, index) => {
                            if (person.userId === ids) {
                                save.splice(index, 1);
                            }
                        });
                    });

                    getIds.forEach((item, index) => {
                        save.forEach((sav) => {
                            if (item.substr(0, 32) === sav) {
                                getIds[index] = item.replaceAll(sav, 'userDeleted');
                            }
                        });
                    });
                    interviewObj.comment = getIds.join('@');
                }

                $rootScope.persons.forEach((user) => {
                    interviewObj.tags = !(
                        interviewObj.comment.indexOf('@') === -1 && interviewObj.comment.length > 200
                    );

                    interviewObj.cutDescr = interviewObj.cutDescr?.replaceAll('&nbsp;', ' ');

                    interviewObj.cutDescr = interviewObj.cutDescr.replaceAll(
                        `@${user.userId}`,
                        `<a href='!#/users/${
                            user.userId
                        }' style='display: inline; text-decoration: none; line-height: 2; background-color: #DAECE1; border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@${
                            $rootScope.useAmericanNameStyle ? user.fullNameEn : user.fullName
                        }</a>`,
                    );

                    if (status) {
                        interviewObj.comment = interviewObj.comment.replaceAll(
                            `<span style="text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;">@${user.userId}</span>`,
                            `<a href='!#/users/${
                                user.userId
                            }' style='display: inline; text-decoration: none; line-height: 2; background-color: #DAECE1; border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@${
                                $rootScope.useAmericanNameStyle ? user.fullNameEn : user.fullName
                            }</a>`,
                        );
                    } else {
                        interviewObj.comment = interviewObj.comment.replaceAll(
                            `<a href='#' style='text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@${
                                $rootScope.useAmericanNameStyle ? user.fullNameEn : user.fullName
                            }</a>&nbsp`,
                            `<a href='!#/users/${
                                user.userId
                            }' style='display: inline; text-decoration: none; line-height: 2; background-color: #DAECE1; border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@${
                                $rootScope.useAmericanNameStyle ? user.fullNameEn : user.fullName
                            }</a>`,
                        );

                        interviewObj.cutDescr = interviewObj.cutDescr.replaceAll(
                            `@${$rootScope.useAmericanNameStyle ? user.fullNameEn : user.fullName}`,
                            `<a href='!#/users/${
                                user.userId
                            }' style='display: inline; text-decoration: none; line-height: 2; background-color: #DAECE1; border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@${
                                $rootScope.useAmericanNameStyle ? user.fullNameEn : user.fullName
                            }</a>`,
                        );
                    }
                });
            }
        }

        $scope.addToMeetArray = (event) => {
            $rootScope.showMeetusers = event;
            $scope.$$phase || $scope.$apply();
        };

        $scope.toggleMeetType = function (meetType) {
            $scope[meetType] = !$scope[meetType];
            $scope[meetType === 'googleMeet' ? 'outlookMeet' : 'googleMeet'] = false;
            if ($scope.createdMeetType) {
                if (meetType === $scope.createdMeetType) {
                    $rootScope.showMeetusers = [...$scope.createdMeetUsers];
                    $scope.meetUsers = [...$scope.createdMeetDropdownUsers];
                } else {
                    getVacancyResponsibles(
                        $scope.vacancyChangeInterviewDate.interviewObject.vacancy,
                        $scope.vacancyChangeInterviewDate.interviewObject.candidateId,
                    );
                }
            }
        };

        function getVacancyResponsibles(vacancyId, candidate) {
            let responsible = [];
            Vacancy.onGetVacancyResponsibles({ vacancyId: vacancyId })
                .then((resp) => {
                    responsible = resp.object.responsibles;
                })
                .then(() => {
                    Candidate.getContacts({ candidateId: candidate.candidateId }, function (resp) {
                        candidate.contacts = resp.objects;
                        $scope.candidateContacts = candidate;
                        initMeetingUsers(responsible, candidate);
                    });
                });
        }

        $scope.openChangeVacancyInterviewDate = function (interviewObject) {
            $scope.createdMeetUsers = [];
            $scope.createdMeetDropdownUsers = [];
            $scope.createdMeetType = '';
            $scope.isCalendarTitleError = false;
            $scope.meetDuration = $scope.meetTimeValues.find(
                (timeObj) => timeObj.value === interviewObject.minutesDuration,
            ) || { name: '1 hour', value: 60 };
            if (interviewObject.summary) $scope.calendarTitle = interviewObject.summary;
            else changeCalendarTitle(interviewObject);
            $scope.vacancyChangeInterviewDate.meetComment = interviewObject.meetComment || '';
            $scope.vacancyChangeInterviewDate.interviewObject = interviewObject;
            $scope.vacancyChangeInterviewDate.date = interviewObject.dateInterview;
            $scope.vacancyChangeInterviewDate.comment = interviewObject.comment;
            $scope.vacancyInterviewState = interviewObject.state;
            $scope.vacancyChangeInterviewDate.candidate = interviewObject.candidateId;
            $scope.vacancyChangeInterviewDate.dateOld = angular.copy(interviewObject.dateInterview);
            if ($scope.vacancyChangeInterviewDate.date) {
                $rootScope.changeVacancyInterviewDatePicker = new Date(angular.copy(interviewObject.dateInterview));
            } else {
                $rootScope.changeVacancyInterviewDatePicker = null;
            }

            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: 'partials/modal/organizer-change-interview-time.html',
                size: '',
                scope: $scope,
                resolve: function () {},
            });

            $scope.modalInstance.opened.then(function () {
                initMeetingUsers($rootScope.vacancy.responsiblesPerson, interviewObject.candidateId);

                checkForMeetEvent(interviewObject);
                $scope.methodOfRequest = !!(!interviewObject.meetLink && interviewObject.dateInterview);

                if (interviewObject.meetLink) {
                    Vacancy.onGetSentInvites({
                        interviewId: interviewObject.interviewId,
                        vacancyId: interviewObject.vacancy,
                    })
                        .then((resp) => {
                            $rootScope.showMeetusers = [];
                            let arrayOfMeetUsers = resp.object.filter(
                                (tag, index, array) =>
                                    array.findIndex((t) => t.personEmail === tag.personEmail) === index,
                            );
                            arrayOfMeetUsers.forEach((user) => {
                                let userObj;

                                $scope.meetUsers.forEach((meetUser) => {
                                    if (meetUser.id === user.personId) {
                                        meetUser.optionDisabled = true;
                                        userObj = { ...meetUser };
                                    }
                                });

                                $rootScope.showMeetusers.push(
                                    userObj
                                        ? { ...userObj, optionDisabled: true }
                                        : {
                                              id: user.personId ? user.personId : 'null',
                                              email: user.personEmail,
                                              fullName: $rootScope.useAmericanNameStyle
                                                  ? user.fullNameEn
                                                  : user.fullName,
                                              optionDisabled: true,
                                              key: `${user.personEmail}-${window.crypto.randomUUID()}`,
                                          },
                                );
                            });
                            $scope.createdMeetType =
                                interviewObject.meetType === 'googleMeet' ? 'googleMeet' : 'outlookMeet';
                            $scope.createdMeetUsers = [...$rootScope.showMeetusers];
                            $scope.createdMeetDropdownUsers = [...$scope.meetUsers];
                        })
                        .finally(() => $rootScope.$$phase || $rootScope.$apply());
                    $scope.changeStatusOfInterviewInVacancy.meetComment = interviewObject.meetDescr;
                }

                if ($scope.emailTemplateInModal) $scope.emailTemplateInModal.email = '';
                if (Email.getDefaultMailbox() && $scope.emailTemplateInModal)
                    $scope.emailTemplateInModal.email = Email.getDefaultMailbox()[0];
                setTimeout(function () {
                    $('.changeVacancyInterviewDatePicker')
                        .datetimepicker({
                            format:
                                $rootScope.currentLang === 'ru' || $rootScope.currentLang === 'ua'
                                    ? 'dd/mm/yyyy hh:ii'
                                    : 'mm/dd/yyyy hh:ii',
                            startView: 2,
                            minView: 0,
                            autoclose: true,
                            weekStart: $rootScope.currentLang === 'ru' || $rootScope.currentLang === 'ua' ? 1 : 7,
                            language: $translate.use(),
                        })
                        .on('changeDate', function (data) {
                            $scope.vacancyChangeInterviewDate.date = data.date;
                        })
                        .on('hide', function () {
                            if ($('.changeVacancyInterviewDatePicker').val() === '') {
                                $scope.vacancyChangeInterviewDate.date = null;
                            }
                            $('.changeVacancyInterviewDatePicker').blur();
                        });
                }, 0);

                $scope.users = [];
                $scope.repsonsibleUsers = [];
                $scope.currentComment = interviewObject.comment;
                $rootScope.persons.forEach((i, index) => {
                    if (i.userId === interviewObject.candidateId.responsibleId && i.recrutRole !== 'researcher') {
                        $scope.repsonsibleUsers.push({
                            id: index + 1,
                            fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                            userId: i.userId,
                            responsible: true,
                        });
                    }
                    if ($rootScope.vacancy.responsiblesPerson) {
                        $rootScope.vacancy.responsiblesPerson.forEach((vac) => {
                            if (i.userId === vac.personId && i.recrutRole !== 'researcher') {
                                $scope.repsonsibleUsers.push({
                                    id: index + 1,
                                    fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                                    userId: i.userId,
                                    responsible: true,
                                });
                            }
                        });
                    }

                    if (
                        i.status === 'A' &&
                        i.recrutRole !== 'client' &&
                        i.recrutRole !== 'freelancer' &&
                        i.recrutRole !== 'researcher'
                    ) {
                        $scope.users.push({
                            id: index + 1,
                            fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                            userId: i.userId,
                            responsible: false,
                        });
                    }
                });
                $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));

                $rootScope.ckEditorEditCandidateCommentChangeDate = {
                    height: 140,
                    width: '100%',
                    toolbar: [],
                    enterMode: CKEDITOR.ENTER_BR,
                    shiftEnterMode: CKEDITOR.ENTER_BR,
                    extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
                    removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
                    editorplaceholder: $translate.instant('mention placeholder'),
                    mentions: [
                        {
                            feed: dataCallback,
                            minChars: 0,
                            itemTemplate: '<li style="padding: 10px" data-id="{id}">{fullname}</li>',
                            outputTemplate: `<a href='#' style='text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@{fullname}</a>&nbsp`,
                            marker: '@',
                            pattern: /@([\p{L}\p{M}]*(?:\s[\p{L}\p{M}]*)?)?$/u,
                        },
                    ],
                    on: {
                        paste: function (ev) {
                            ev.data.dataValue = '';
                            CKEDITOR.instances['ckEditorEditCandidateCommentChangeDate'].insertText(
                                ev.data.dataTransfer._.data.Text,
                            );
                            setTimeout(() => {
                                let bodyHeight = ev.editor.document.$.children[0].offsetHeight;
                                if (bodyHeight > ev.editor.config.height) {
                                    if (bodyHeight < 605) {
                                        ev.editor.resize(null, bodyHeight);
                                    }
                                }
                            }, 0);
                        },
                        afterInsertHtml: function (ev) {
                            let userId = ev.editor.data.userId;
                            $scope.users.filter((user, index) => {
                                if (user.userId === userId) {
                                    $scope.users.splice(index, 1);
                                }
                            });
                            $scope.repsonsibleUsers.forEach((user, index) => {
                                if (user.userId === userId) {
                                    $scope.repsonsibleUsers.splice(index, 1);
                                }
                            });
                            $scope.deletedUsers.push(ev.editor.data);
                        },
                        instanceReady: function (ev) {
                            $scope.deletedUsers = [];
                            $scope.afterDelete = [];

                            ev.editor.dataProcessor.writer.setRules('p', {
                                indent: false,
                                breakBeforeOpen: false,
                                breakAfterOpen: false,
                                breakBeforeClose: false,
                                breakAfterClose: false,
                            });
                            ev.editor.dataProcessor.writer.setRules('br', {
                                indent: false,
                                breakBeforeOpen: false,
                                breakAfterOpen: false,
                                breakBeforeClose: false,
                                breakAfterClose: false,
                            });
                            if (interviewObject.comment !== '') {
                                $rootScope.persons.forEach((user) => {
                                    interviewObject.comment = interviewObject.comment.replaceAll(
                                        `<a href='!#/users/${
                                            user.userId
                                        }' style='display: inline; text-decoration: none; line-height: 2; background-color: #DAECE1; border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@${
                                            $rootScope.useAmericanNameStyle ? user.fullNameEn : user.fullName
                                        }</a>`,
                                        `<a href='#' style='text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@${
                                            $rootScope.useAmericanNameStyle ? user.fullNameEn : user.fullName
                                        }</a>&nbsp`,
                                    );
                                });
                                CKEDITOR.instances.ckEditorEditCandidateCommentChangeDate.setData(
                                    interviewObject.comment,
                                );
                            }
                        },
                    },
                };

                function dataCallback(opts, callback) {
                    opts.query = opts.query.replace(/\u00A0/g, ' ');
                    let text = CKEDITOR.instances['ckEditorEditCandidateCommentChangeDate'].getData();

                    $scope.deletedUsers.forEach((user, index) => {
                        if (!text.includes(`${user.fullname}`)) {
                            if (user.responsible) {
                                $scope.repsonsibleUsers.push(user);
                            } else {
                                $scope.users.push(user);
                            }
                            $scope.afterDelete.push(user.id);
                            $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));
                        }
                    });

                    $scope.afterDelete.forEach((id) => {
                        $scope.deletedUsers = $scope.deletedUsers.filter((user) => user.id !== id);
                    });
                    $scope.afterDelete = [];

                    $scope.repsonsibleUsers = $scope.repsonsibleUsers.filter((thing, index) => {
                        return (
                            index ===
                            $scope.repsonsibleUsers.findIndex((obj) => {
                                return JSON.stringify(obj) === JSON.stringify(thing);
                            })
                        );
                    });

                    let showAll =
                        $scope.repsonsibleUsers.length === 1 &&
                        $scope.repsonsibleUsers[0].userId === $rootScope.me.userId;

                    if (opts.query.length === 0 && $scope.repsonsibleUsers.length > 0 && !showAll) {
                        setTimeout(function () {
                            callback(
                                $scope.repsonsibleUsers.filter(function (item) {
                                    return item.fullname;
                                }),
                            );
                        });
                    } else {
                        setTimeout(function () {
                            callback(
                                $scope.users.filter(function (item) {
                                    return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                                }),
                            );
                        });
                    }
                }
            });

            $scope.modalInstance.closed.then(function () {
                $scope.changeStatusOfInterviewInVacancy.meetComment = '';
                $rootScope.emptyRequiredInputs = [];
                $rootScope.showMeetusers = [];
                $scope.googleMeet = false;
                $scope.outlookMeet = false;
            });
        };

        function createObject(person) {
            return {
                id: person.userId ? person.userId : person.candidateId,
                fullName: person.fullName,
                email: person.login ? person.login : 'no email',
                isUser: !!person.userId,
                key: `${person.login ? person.login : ''}-${window.crypto.randomUUID()}`,
            };
        }

        function initMeetingUsers(responsibles, candidate) {
            $rootScope.showMeetusers = [];

            if (!candidate.contacts) candidate.contacts = [];
            const candidateEmail = candidate.contacts.filter((mail) => mail.type === 'email' && mail.default === true);
            candidate.login = candidateEmail.length ? candidateEmail[0].value : null;

            $scope.meetUsers = $rootScope.persons
                .filter((user) => user.status === 'A' && user.userId !== $rootScope.me.userId)
                .map((user) => createObject(user));

            if (candidate.login) {
                const candidateObj = createObject(candidate);
                $scope.meetUsers.push(candidateObj);
                $rootScope.showMeetusers.push(candidateObj);
            }

            responsibles.forEach((responsible) => {
                const responsibleUser = $scope.meetUsers.find((user) => user.id === responsible.personId);
                responsibleUser && $rootScope.showMeetusers.push(responsibleUser);
            });

            const candidateResponsible = $scope.meetUsers.find(
                (responsible) =>
                    responsible.id === candidate.responsibleId && candidate.responsibleId !== $rootScope.me.userId,
            );
            candidateResponsible && $rootScope.showMeetusers.push(candidateResponsible);

            $rootScope.showMeetusers = $rootScope.showMeetusers.filter((obj, i, array) => {
                return array.findIndex((o) => o['id'] === obj['id']) === i;
            });
        }

        /**
         * @param { 'allActions' | 'comment' } type
         * @param { string } taskId
         * @param { number } count
         */
        const getTaskHistory = (type, taskId, count = 15) => {
            Task.getHistory(
                {
                    taskId,
                    ...(type === 'comment' && { type: 'comment' }),
                    page: { number: 0, count },
                },
                function (resp) {
                    if (resp.status !== 'ok') {
                        notificationService.error(resp.message);
                    }

                    $scope.historyAllPageCount = resp.allPageCount;

                    if (type === 'comment') {
                        $scope.historyComment = resp.objects;
                    } else {
                        $scope.taskHistory = resp.objects;
                    }

                    setDividers();

                    $rootScope.$$phase || $scope.$apply();
                },
            );
        };

        $scope.getMoreHistory = function (number) {
            $scope.taskHistoryLimit += number;
            getTaskHistory('allActions', $scope.editableTask.taskId, $scope.taskHistoryLimit);
        };

        $scope.onClickAllActions = (taskId) => {
            $scope.historyComment = [];
            $scope.taskHistory = [];

            getTaskHistory($scope.showAllActions ? 'comment' : '', taskId, $scope.showAllActions ? 30 : 15);

            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: 'partials/modal/vacancy/kanban-task-actions.html',
                scope: $scope,
                backdrop: true,
            });

            $scope.modalInstance.closed.then(() => {
                $scope.taskHistoryLimit = 15;
            });
        };

        $scope.updateCommentForTask = (taskDetails) => {
            const filteredTask = $rootScope.myTasksLists
                .flatMap((list) => list.events)
                .filter((item) => item.event.task?.taskId === taskDetails.taskId)[0].event.task;

            if ($('#addComment2').val().length > 0) {
                Task.updateComment(
                    {
                        taskId: taskDetails.taskId,
                        comment: taskDetails.comment,
                        taskActionId: taskDetails.taskActionId,
                    },
                    function (resp) {
                        if (resp.status === 'ok') {
                            taskDetails.showTxtArea = false;
                            $scope.taskHistory = resp.object?.messageSearchResult.objects;
                            setDividers();

                            if (filteredTask.lastComment.taskActionId === taskDetails.taskActionId) {
                                filteredTask.lastComment = resp.object?.messageSearchResult.objects.find(
                                    (item) => item.taskActionId === taskDetails.taskActionId,
                                );
                            }
                        } else {
                            notificationService.error(resp.message);
                        }
                    },
                );
            } else {
                notificationService.error($filter('translate')('write_a_comment'));
            }
        };

        $scope.deleteCommentForTask = (taskId, taskActionId) => {
            Task.removeComment(
                {
                    taskId: taskId,
                    taskActionId: taskActionId,
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        //Optimistic update
                        $scope.taskHistory = $scope.taskHistory.filter((item) => item.taskActionId !== taskActionId);
                        $scope.historyComment = $scope.historyComment.filter(
                            (item) => item.taskActionId !== taskActionId,
                        );
                        setDividers();
                        notificationService.success($filter('translate')('Comment removed'));
                        $rootScope.$$phase || $scope.$apply();
                    } else {
                        notificationService.error(resp.message);
                    }
                },
            );
        };

        function setDividers() {
            let previouslyDate = null;
            $scope.taskHistory?.forEach((action, index) => {
                const currentDate = new Date(action.dc).toLocaleDateString();
                if (previouslyDate !== null && currentDate !== previouslyDate) {
                    $scope.taskHistory[!index ? index : index - 1].dateDivider = action.dc;
                }
                previouslyDate = currentDate;
            });
        }

        $scope.onHandleHistorySwitcher = (event, taskId) => {
            $scope.taskHistoryLimit = 15;
            $scope.showAllActions = event.target.checked;
            getTaskHistory($scope.showAllActions ? 'comment' : '', taskId, $scope.showAllActions ? 30 : 15);
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onItemClick = async (event, eventItem) => {
            if (eventItem.expanded) return;

            $scope.cardExpanded = true;
            $rootScope.$$phase || $scope.$apply();

            $scope.eventItemDateModel = new Date(eventItem.event.date);

            if (eventItem.event.type === 'task') {
                $scope.selectedTaskStatus = $scope.taskStatusOptions.find(
                    (item) => item.sendStatus === eventItem.event.task.status,
                );
                $scope.editableTask = eventItem.event.task;
            } else if (eventItem.event.type.includes('interview')) {
                $scope.editableInterview = eventItem.event;
            }

            let { width, height, bottom, x, y } = event.currentTarget.getBoundingClientRect();

            const parentBottom = event.currentTarget.parentElement.getBoundingClientRect().bottom;

            if (bottom && parentBottom && Math.round(bottom) > Math.round(parentBottom)) {
                event.currentTarget.parentElement.scrollBy({ top: bottom - parentBottom + 8, behavior: 'smooth' });
                await $rootScope.waitForCondition(
                    100,
                    50,
                    () => Math.round(event.currentTarget.getBoundingClientRect().bottom) <= Math.round(parentBottom),
                );

                ({ bottom, x, y } = event.currentTarget.getBoundingClientRect());
            }

            const mockCard = document.createElement('li');
            event.currentTarget.insertAdjacentElement('afterEnd', mockCard);
            mockCard.className = 'mock-card';
            mockCard.style.width = `${width}px`;
            mockCard.style.minHeight = `${height}px`;
            event.currentTarget.insertAdjacentElement('beforebegin', mockCard);

            const backdrop = document.createElement('div');
            backdrop.className = 'card-full-backdrop';

            const cardElement = event.currentTarget;
            cardElement.classList.add('item_expanded');

            cardElement.style.width = `${width}px`;
            if (event.clientY > (window.innerHeight / 100) * 60) {
                cardElement.style.bottom = `${window.innerHeight - bottom}px`;
                cardElement.style.top = 'auto';
                cardElement.style.left = `${x}px`;
            } else {
                cardElement.style.top = `${y}px`;
                cardElement.style.bottom = 'auto';
                cardElement.style.left = `${x}px`;
            }

            event.currentTarget.insertAdjacentElement('beforebegin', backdrop);

            setTimeout(() => {
                cardElement.style.width = `${width + 100}px`;
                backdrop.style.opacity = '0.5';

                setTimeout(() => {
                    eventItem['expanded'] = true;
                    $rootScope.$$phase || $scope.$apply();
                }, 100);
            }, 100);

            $scope._foldCardCallBack = () => {
                $scope.cardExpanded = false;

                if (!eventItem.expanded) return;

                $scope.showAddCommentField = false;

                cardElement.style.width = `${width}px`;
                cardElement.style.overflow = 'hidden';
                cardElement.children[0].style.width = `${width + 100 - 24}px`;
                backdrop.style.opacity = '0';

                setTimeout(() => {
                    cardElement.classList.remove('item_expanded');
                    cardElement.style = null;
                    cardElement.children[0].style = null;
                    eventItem['expanded'] = false;
                    backdrop.remove();
                    mockCard.remove();
                    $rootScope.$$phase || $scope.$apply();
                }, 200);

                return backdrop.removeEventListener('click', $scope._foldCardCallBack);
            };

            // Fold event
            backdrop.addEventListener('click', $scope._foldCardCallBack, { once: true });
        };

        /**
         * Decides whether to show notification about outdated time
         * @param {number} timestamp
         */
        const warnUserAboutDate = (timestamp) => {
            if (new Date(timestamp).toDateString() === new Date().toDateString() && timestamp <= Date.now()) {
                notificationService.error($filter('translate')('Please enter the current time'));
            }
        };

        /**
         * If original date is today and the time was outdated
         * returns the force set time +3 hours from now() or 23:59
         * @param {string | number} originalDate
         * @returns {number}
         */
        const getForceIncreasedTime = (originalDate) => {
            if (typeof originalDate !== 'number') originalDate = Date.parse(originalDate);

            if (new Date(originalDate).toDateString() === new Date().toDateString() && originalDate <= Date.now()) {
                if (new Date(Date.now()).getHours() >= 21) {
                    return new Date(Date.now()).setHours(23, 59);
                }
                return new Date(Date.now()).setHours(new Date(Date.now()).getHours() + 3);
            }
            return originalDate;
        };

        /**
         * @param {string} type
         * @param {number} date
         */
        $scope.onCardDatePickerClick = (type, date) => {
            if (
                (type === 'task' || type === 'interview') &&
                new Date().toDateString() === new Date(date).toDateString()
            ) {
                warnUserAboutDate(date);
            }
        };

        $scope.onDrop = (list, eventItem) => {
            $scope.lastDraggedEvent = eventItem;

            let eventDate = new Date(eventItem.event.date);

            switch (list.label) {
                case 'Today':
                    $rootScope.eventDateModel = new Date(Date.now());
                    break;

                case 'Tomorrow':
                    $rootScope.eventDateModel = new Date();
                    $rootScope.eventDateModel.setDate($rootScope.eventDateModel.getDate() + 1);
                    break;

                case 'Future':
                    $rootScope.eventDateModel = new Date(Date.now());
                    $rootScope.eventDateModel.setDate($rootScope.eventDateModel.getDate() + 2);
                    break;

                default:
                    $rootScope.eventDateModel = new Date(eventItem.event.date);
            }
            $rootScope.eventDateModel.setHours(eventDate.getHours(), eventDate.getMinutes());

            $rootScope.$$phase || $scope.$apply();

            const datePickerElement = document.querySelector(
                '#eventDatePicker .md-icon-button.md-button.md-ink-ripple',
            );
            const dateTimePickerElement = document.querySelector(
                '#eventDateTimePicker .md-icon-button.md-button.md-ink-ripple',
            );

            if (eventItem.event.type === 'finish_vacancy' || eventItem.event.type === 'payment_vacancy') {
                datePickerElement.click();
            } else {
                dateTimePickerElement.click();
            }

            if (
                list.label === 'Today' &&
                eventItem.event.type !== 'finish_vacancy' &&
                eventItem.event.type !== 'payment_vacancy'
            ) {
                warnUserAboutDate($rootScope.eventDateModel);
            }

            const datePickerWindow = document.querySelector("md-dialog[role='dialog']");

            const clickOutsideDatePicker = (event) => {
                if (!datePickerWindow) return;

                if (event.target.className.includes('md-dialog-container')) {
                    $scope.onCloseDatePicker();
                    document.removeEventListener('click', clickOutsideDatePicker);
                }
            };

            document.addEventListener('click', clickOutsideDatePicker);

            const dataPickerOkButton = datePickerWindow.querySelector("button[aria-label='Ok']");
            dataPickerOkButton.addEventListener(
                'click',
                () => {
                    $scope.wasDateOkClick = true;

                    if (eventItem.event.type === 'task') {
                        $scope.changeTaskDate(eventItem, getForceIncreasedTime($rootScope.eventDateModel));
                    } else if (
                        eventItem.event.type === 'finish_vacancy' ||
                        eventItem.event.type === 'payment_vacancy'
                    ) {
                        $scope.changePaymentOrFinishDate(eventItem.event, Date.parse($rootScope.eventDateModel));
                    } else {
                        $scope.changeInterviewDate(eventItem, getForceIncreasedTime($rootScope.eventDateModel));
                    }
                },
                { once: true },
            );

            return eventItem;
        };

        $scope.onMoved = (index, originalList) => {
            originalList.splice(index, 1);
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeTaskStatus = (newValue) => {
            if (newValue.sendStatus === $scope.editableTask.status) return;

            $scope.editableTask.status = newValue.status;

            Task.changeState(
                {
                    taskId: $scope.editableTask.taskId,
                    taskState: newValue.sendStatus,
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        $scope._foldCardCallBack && $scope._foldCardCallBack();
                        $onInit();
                    } else {
                        notificationService.error(resp.message);
                    }
                },
            );

            $rootScope.$$phase || $scope.$apply();
        };

        $scope.disableMove = () => {
            $timeout(() => {
                const dndPlaceholder = document.querySelector('.dndPlaceholder');
                if (dndPlaceholder) {
                    dndPlaceholder.style.height = `${$scope.lastDraggedCardHeight}px`;
                }
            }, 10);
        };

        /**
         *
         * @param {string} value
         */
        $scope.onChangeDatePicker = (value) => {
            if ($scope.lastDraggedEvent.event.type === 'task') {
                $scope.changeTaskDate($scope.lastDraggedEvent, value);
            } else if (
                $scope.lastDraggedEvent.event.type === 'finish_vacancy' ||
                $scope.lastDraggedEvent.event.type === 'payment_vacancy'
            ) {
                $scope.changePaymentOrFinishDate($scope.lastDraggedEvent.event, Date.parse(value));
            } else {
                $scope.changeInterviewDate($scope.lastDraggedEvent, value);
            }
        };

        $scope.onCloseDatePicker = () => {
            $onInit();
        };

        $scope.changePaymentOrFinishDate = (event, date, callback) => {
            date = new Date(date).setHours(23, 59, 0);

            $rootScope.loading = true;

            const datePayment = event.type === 'payment_vacancy' ? date : null;
            const dateFinish = event.type === 'finish_vacancy' ? date : null;

            Vacancy.setDatePaymentOrFinish(
                {
                    vacancyId: event.vacancy.vacancyId,
                    datePayment,
                    dateFinish,
                },
                (resp) => {
                    $rootScope.loading = false;
                    $scope._foldCardCallBack && $scope._foldCardCallBack();
                    $onInit();
                },
            );
        };

        /**
         *
         * @param eventItem
         * @param {string} date
         */
        $scope.changeTaskDate = (eventItem, date) => {
            $rootScope.loading = true;

            Task.changeTargetDate(
                {
                    taskId: eventItem.event.task.taskId,
                    date: getForceIncreasedTime(date),
                },
                (resp) => {
                    $rootScope.loading = false;
                    $scope._foldCardCallBack && $scope._foldCardCallBack();
                    $onInit();
                },
            );
        };

        /**
         *
         * @param eventItem
         * @param {string} date
         */
        $scope.changeInterviewDate = (eventItem, date) => {
            $rootScope.loading = true;
            const {
                interviewId,
                minutesDuration = 60,
                meetType,
                summary = '',
                meetComment = '',
                interview: { comment = '' },
            } = eventItem.event;
            const meetParticipants =
                eventItem.event.meetParticipants?.reduce((obj, item) => {
                    obj[item.personEmail] = item.personId;
                    return obj;
                }, {}) || {};

            Vacancy.changeInterviewDate(
                {
                    interviewId,
                    date: getForceIncreasedTime(date),
                    lang: $translate.use(),
                    comment,
                    minutesDuration,
                    meetParticipants,
                    meetComment,
                    summary,
                    ...(meetType
                        ? meetType === 'googleMeet'
                            ? { createGoogleMeet: true }
                            : { createTeams: true }
                        : {}),
                },
                (resp) => {
                    $rootScope.loading = false;
                    eventItem.expanded && $scope._foldCardCallBack();
                    $onInit();
                },
            );
        };

        $scope.openPaymentOrFinishDate = (event) => {
            let eventDate = new Date(event.date);
            $rootScope.eventDateModel = new Date(event.date);
            $rootScope.eventDateModel.setHours(eventDate.getHours(), eventDate.getMinutes());

            $timeout(() => {
                const dataPicker = document.querySelector('#eventDatePicker .md-icon-button.md-button.md-ink-ripple');
                dataPicker.click();

                const datePickerWindow = document.querySelector("md-dialog[role='dialog']");
                document.addEventListener(
                    'click',
                    () => {
                        if (!datePickerWindow) return;

                        if (!datePickerWindow.contains(event.target)) {
                            $scope.onCloseDatePicker();
                        }
                    },
                    { once: true },
                );

                const dataPickerOkButton = datePickerWindow.querySelector("button[aria-label='Ok']");
                dataPickerOkButton.addEventListener(
                    'click',
                    () => {
                        $scope.wasDateOkClick = true;

                        $scope.changePaymentOrFinishDate(event, Date.parse($rootScope.eventDateModel));
                    },
                    { once: true },
                );
            }, 0);
        };

        const clearGoogleMeet = () => {
            $scope.googleMeet = false;
            $scope.outlookMeet = false;
            $scope.showGoogleComment = false;
            $scope.googleMeetUsersOptions = [];
            $scope.selectedGoogleMeetUsers = [];
            $scope.meetParticipants = {};
            $rootScope.meetComment = '';
        };

        function checkRequiredInputs(sendEmail = false) {
            $scope.emptyRequiredInputs = [];
            if (!$scope.calendarTitle && ($scope.googleMeet || $scope.outlookMeet))
                $scope.emptyRequiredInputs.push('calendarTitle');
            $scope.requiredInputs.forEach((inputId) => {
                if ($scope.emptyRequiredInputs.includes(inputId)) {
                    $rootScope.scrollToDomElement(`#${inputId}`, 'smooth');
                    notificationService.error($filter('translate')(inputId));
                }
            });

            return !!$scope.emptyRequiredInputs.length;
        }

        $scope.saveChangedVacancyInterview = (interview) => {
            if (checkRequiredInputs()) return;
            const object = { ...interview.interview };
            const newDate = Date.parse($rootScope.changeVacancyInterviewDatePicker) || null;
            let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorEditCandidateCommentChangeDate.getData());
            object.comment = text?.replaceAll('<br />', ' &nbsp<br>&nbsp ');

            const methodOfRequest = !!(!object.meetLink && object.dateInterview);

            const request = methodOfRequest && $scope.googleMeet ? 'createMeetEvent' : 'changeInterviewDate';
            if ($scope.googleMeet || $scope.outlookMeet) createMapForMeeting();

            if (newDate) {
                Vacancy[request](
                    {
                        interviewId: object.interviewId,
                        date: newDate,
                        comment: object.comment || '',
                        lang: $translate.use(),
                        createGoogleMeet: $scope.googleMeet,
                        createTeams: $scope.outlookMeet,
                        minutesDuration: $scope.meetDuration.value,
                        summary: $scope.calendarTitle,
                        meetComment: $scope.changeStatusOfInterviewInVacancy.meetComment,
                        meetParticipants: $scope.meetParticipants,
                    },
                    function (resp) {
                        if (resp.code === 'googleMeetError') {
                            notificationService.error($filter('translate')('google_meet_error'));
                            $rootScope.loading = false;
                            return;
                        }

                        clearGoogleMeet();
                        $scope.modalInstance.close();
                        $onInit();
                    },
                );
            }
        };

        function createMapForMeeting() {
            $scope.meetParticipants = new Map();
            $rootScope.showMeetusers.forEach((person) => {
                $scope.meetParticipants.set(person.email, person.id && !person.isNew ? person.id : 'null');
            });

            $scope.meetParticipants = Object.fromEntries($scope.meetParticipants.entries());
        }

        $scope.addCommentForInterview = (eventItem, comment) => {
            $rootScope.loading = true;

            Vacancy.onMessageInterview({
                candidateId: eventItem.interview.candidate,
                vacancyId: eventItem.interview.vacancy,
                comment,
            }).then((resp) => {
                if (resp.status !== 'ok') return;

                eventItem.interview = resp.object;
                $scope.showAddCommentField = false;
                $rootScope.interviewCommentModel = '';

                $rootScope.loading = false;
                $rootScope.$$phase || $scope.$apply();
            });
        };

        $scope.addCommentForTask = (eventObj, taskId, comment) => {
            if (!comment) return;

            $rootScope.loading = true;

            Task.taskComment({ taskId, comment }, function (resp) {
                if (resp.status === 'ok') {
                    $scope.showAddCommentField = false;
                    $rootScope.taskNewComment = '';
                    eventObj.task.lastComment = resp.object.messageSearchResult.objects[0];
                    $rootScope.$$phase || $scope.$apply();
                    notificationService.success($filter('translate')('Comment added'));

                    $rootScope.loading = false;
                } else {
                    notificationService.error(resp.message);
                }
            });
        };

        function isToday(date) {
            if (typeof date === 'string' || typeof date === 'number') {
                date = new Date(date);
            }

            const today = new Date();
            return (
                date.getTime() >= today.getTime() &&
                date.getDate() === today.getDate() &&
                date.getMonth() === today.getMonth() &&
                date.getFullYear() === today.getFullYear()
            );
        }

        function isTomorrow(date) {
            if (typeof date === 'string' || typeof date === 'number') {
                date = new Date(date);
            }
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            return tomorrow.toDateString() === date.toDateString();
        }

        function isFutureDate(date) {
            if (typeof date === 'string' || typeof date === 'number') {
                date = new Date(date);
            }

            const afterDays = new Date();
            afterDays.setDate(afterDays.getDate() + 2);
            afterDays.setHours(0, 0, 0);

            return date.getTime() > afterDays.getTime();
        }

        function isExpiredDate(date) {
            if (typeof date === 'string' || typeof date === 'number') {
                date = new Date(date);
            }

            const today = new Date();
            return date.getTime() < today.getTime();
        }

        $scope.onChangeCalendarTitle = (value) => {
            $scope.emptyRequiredInputs = $scope.emptyRequiredInputs.filter((field) => field !== 'calendarTitle');
            $scope.calendarTitle = value;
            $scope.$apply();
        };

        $scope.onChangeMeetDuration = (value) => {
            $scope.meetDuration = value;
            $scope.$apply();
        };

        const $onInit = () => {
            $rootScope.loading = true;

            checkGoogleCalendarIntegration();
            checkOutlookCalendarIntegration();

            Vacancy.onGetEvents({
                city: null,
                country: null,
                personId: $rootScope.upcomingEventsScope ? $rootScope.userId : null,
            })
                .then((resp) => {
                    if (resp.status !== 'ok') return;

                    $rootScope.isEmptyEvents = Boolean(!resp.objects.length);

                    const listsBuffer = [
                        {
                            label: 'Today',
                            events: [],
                            allowedTypes: ['tomorrow', 'future', 'expired'],
                        },
                        {
                            label: 'Tomorrow',
                            events: [],
                            allowedTypes: ['today', 'future', 'expired'],
                        },
                        {
                            label: 'Future',
                            events: [],
                            allowedTypes: ['today', 'tomorrow', 'expired'],
                        },
                        {
                            label: 'Expired',
                            events: [],
                            allowedTypes: [],
                        },
                    ];

                    resp.objects.forEach((event) => {
                        if (event.type === 'task' && event.task.lastComment) {
                            event.task.lastComment['creator'] = $rootScope.persons?.find(
                                ({ personId }) => personId === event.task.lastComment.creatorId,
                            );
                        }

                        if (event.type === 'interview') {
                            formatInterviewComment(event.interview, true);
                        }

                        if (isToday(event.date)) {
                            listsBuffer[0].events.push({ event, type: 'today' });
                        } else if (isTomorrow(event.date, 1)) {
                            listsBuffer[1].events.push({ event, type: 'tomorrow' });
                        } else if (isFutureDate(event.date)) {
                            listsBuffer[2].events.push({ event, type: 'future' });
                        } else if (isExpiredDate(event.date)) {
                            listsBuffer[3].events.push({ event, type: 'expired' });
                        }
                    });

                    $rootScope.myTasksLists = listsBuffer;
                    $rootScope.todayEvents = [];

                    if (!$rootScope.upcomingEventsScope) {
                        $rootScope.myTasksLists[0].events.forEach((event) => {
                            if (
                                event.event.type === 'task' &&
                                event.event.task.responsibleIds.includes($rootScope.me.userId)
                            ) {
                                $rootScope.todayEvents.push(event);
                            } else {
                                event.event?.vacancy?.responsibles?.forEach((person) => {
                                    if (person.personId === $rootScope.me.userId) {
                                        $rootScope.todayEvents.push(event);
                                    }
                                });
                            }
                        });
                    } else {
                        $rootScope.todayEvents = $rootScope.myTasksLists[0].events;
                    }

                    if ($rootScope.me.recrutRole === 'client' && !$rootScope.todayEvents.length) {
                        $rootScope.checkVersion();
                    }

                    $rootScope.$$phase || $scope.$apply();
                })
                .catch((err) => console.error(err))
                .finally(() => {
                    $rootScope.isKanbanLoading = false;

                    $scope.wasDateOkClick = false;
                    Task.task($scope, $rootScope, $location, $translate, $uibModal);

                    $timeout(() => {
                        $rootScope.loading = false;
                    }, 1000);

                    $rootScope.$$phase || $scope.$apply();
                });
        };

        $onInit();
    },
};

component.component('myTasksKanban', myTasksKanbanCtrlDefinition);
