component.component('referralMainComponent', {
    templateUrl: 'partials/referralProgram/main.html',
    controller: class {
        constructor($state, ReferralProgram) {
            const vm = this;
            vm.ReferralProgram = ReferralProgram;

            vm.ReferralProgram.getReferralInfoProcessed().then(
                (response) => {
                    vm.referralData = response;
                    if ($state.current.name === 'referral') $state.go('referral.general');
                },
                (error) => {
                    $state.go('organizer');
                },
            );
        }
    },
    controllerAs: 'vm',
});
