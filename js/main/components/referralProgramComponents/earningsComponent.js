component.component('referralEarningsComponent', {
    templateUrl: 'partials/referralProgram/earnings.html',
    controller: class {
        constructor($rootScope, $scope, $translate, $uibModal, ngTableParams, notificationService, ReferralProgram) {
            const self = this;
            this.ReferralProgram = ReferralProgram;
            this.isShowMore = false;
            this.earnings = null;
            this.withdrawAvailable = false;
            this.$scope = $scope;
            this.notificationService = notificationService;
            this.$translate = $translate;
            this.$uibModal = $uibModal;
            this.$rootScope = $rootScope;
            this.$rootScope.objectSize = 0;
            this.ngTableParams = ngTableParams;
        }

        initTableParams() {
            this.$scope.paginationParams = {
                currentPage: 0,
                totalCount: 16,
            };
            this.withdrawParams = {
                totalWithdrawn: 0,
                earningsForWithdraw: [],
            };
            this.wasWithdrawn = 0;
            this.tableParams = new this.ngTableParams(
                {
                    page: 1,
                    count: 15,
                },
                {
                    total: 0,
                    getData: ($defer, params) => {
                        this.ReferralProgram.getTotalReward((resp) => {
                            if (resp.status !== 'error' && resp.object) this.wasWithdrawn = resp.object.payed;
                        });

                        this.ReferralProgram.getEarningsTableData({
                            number: params.page() - 1,
                            count: params.count(),
                        }).then((tableData) => {
                            this.earnings = tableData.object;
                            this.withdrawAvailable = tableData.withdrawAvailable;
                            this.setChecked();
                            this.$scope.objectSize = tableData.total;

                            params.total(tableData.total);
                            $defer.resolve(this.earnings);
                        });
                    },
                },
            );
        }

        selectEarning(earning) {
            angular.forEach(this.earnings, (oneEarning) => {
                if (oneEarning.id === earning.id) {
                    oneEarning.checked = !oneEarning.checked;
                    this.updateEarningIds(oneEarning);
                }
            });
        }

        updateEarningIds(earning) {
            const withdrawInd = this.withdrawParams.earningsForWithdraw.findIndex(
                (oneWithdraw) => earning.id === oneWithdraw.id,
            );
            if (withdrawInd === -1) {
                this.withdrawParams.earningsForWithdraw.push(earning);
                this.withdrawParams.totalWithdrawn += earning.reward;
            } else {
                this.withdrawParams.totalWithdrawn -= earning.reward;
                this.withdrawParams.earningsForWithdraw.splice(withdrawInd, 1);
            }
        }

        withdrawDialog() {
            const self = this;
            if (this.withdrawParams.earningsForWithdraw.length > 0) {
                this.$uibModal.open({
                    templateUrl: 'partials/modal/referral-program/withdraw-bonus.html',
                    controller: [
                        '$rootScope',
                        '$uibModalInstance',
                        'ReferralProgram',
                        'withdrawParams',
                        'tableParams',
                        withdrawCtrl,
                    ],
                    controllerAs: 'vm',
                    backdrop: 'static',
                    windowClass: 'referral-withdraw-popup',
                    resolve: {
                        ReferralProgram: function () {
                            return self.ReferralProgram;
                        },
                        withdrawParams: function () {
                            return self.withdrawParams;
                        },
                        $rootScope: function () {
                            return self.$rootScope;
                        },
                        tableParams: function () {
                            return self.tableParams;
                        },
                    },
                });
            } else {
                this.notificationService.error(this.$translate.instant('Choose rewards'));
            }

            function withdrawCtrl($rootScope, $uibModalInstance, ReferralProgram, withdrawParams, tableParams) {
                const self = this;
                self.target = 'cs';
                self.email = '';
                self.requestSuccessful = false;
                self.$uibModalInstance = $uibModalInstance;
                self.withdrawParams = withdrawParams;
                const earningIds = withdrawParams.earningsForWithdraw.map((earning) => earning.id);
                self.changeTarget = function (target) {
                    self.target = target;
                };
                self.withdraw = function () {
                    if (self.target === 'cs') {
                        $rootScope.loading = true;
                        ReferralProgram.topUpAccount(
                            { ids: earningIds },
                            (resp) => {
                                if (resp.status !== 'error') {
                                    resetWithdrawParams();
                                    self.$uibModalInstance.close();
                                }
                                $rootScope.loading = false;
                            },
                            (err) => {
                                $rootScope.loading = false;
                            },
                        );
                    } else {
                        if (self.checkMailbox()) {
                            ReferralProgram.withdrawReward(
                                { ids: earningIds, email: self.email },
                                (resp) => {
                                    if (resp.status !== 'error') {
                                        resetWithdrawParams();
                                        self.requestSuccessful = true;
                                    }
                                    $rootScope.loading = false;
                                },
                                (err) => {
                                    $rootScope.loading = false;
                                },
                            );
                        }
                    }

                    function resetWithdrawParams() {
                        tableParams.reload();
                        withdrawParams.totalWithdrawn = 0;
                        withdrawParams.earningsForWithdraw = [];
                    }
                };

                self.checkMailbox = function () {
                    const wrapper = document.getElementById('emailWrapper');
                    if (!self.email || !self.email.trim().length) {
                        wrapper.classList.add('empty');
                        wrapper.classList.remove('not-valid');
                    } else if (!ReferralProgram.emailValidation(self.email)) {
                        wrapper.classList.add('not-valid');
                        wrapper.classList.remove('empty');
                    } else {
                        wrapper.classList.remove('empty');
                        wrapper.classList.remove('not-valid');
                        return true;
                    }
                    return false;
                };
            }
        }

        setChecked() {
            this.earnings.forEach((oneEarning) => {
                this.withdrawParams.earningsForWithdraw.forEach((oneWithdraw) => {
                    if (oneWithdraw.id === oneEarning.id) oneEarning.checked = true;
                });
            });
        }

        $onInit() {
            this.initTableParams();
        }
    },
    controllerAs: 'vm',
});
