component.component('referralHowComponent', {
    templateUrl: 'partials/referralProgram/how.html',
    controller: class How {
        constructor($scope) {
            this.$scope = $scope;
        }

        changeStep(step) {
            this.currentStep = step;
        }

        stepForward() {
            How._hideSliderTip();
            if (this.currentStep < 5) {
                this.currentStep++;
                this.$scope.$$phase || this.$scope.$apply();
            }
        }

        stepBack() {
            How._hideSliderTip();
            if (this.currentStep > 1) {
                this.currentStep--;
                this.$scope.$$phase || this.$scope.$apply();
            }
        }

        static _hideSliderTip() {
            const sliderTipEl = $('.slider-tip');
            if (sliderTipEl.length) sliderTipEl.hide();
        }

        $onInit() {
            this.isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            this.currentStep = 1;
        }
    },
    controllerAs: 'vm',
});
