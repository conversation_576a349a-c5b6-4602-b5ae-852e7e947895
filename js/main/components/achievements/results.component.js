class resultsComponentController {
    constructor($filter, $translate, $scope, $rootScope, Person, serverAddress, Service, Achievements) {
        this.$filter = $filter;
        this.$translate = $translate;
        this.$scope = $scope;
        this.$rootScope = $rootScope;
        this.personService = Person;
        this.serverAddress = serverAddress;
        this.Service = Service;
        this.Achievements = Achievements;
    }
    $onInit() {
        this.$rootScope.loading = true;
        if (this.$rootScope.me.orgParams.gamificationEnabled == 'false') {
            window.location.replace('!#/organizer');
        }
        this.recruiterRace = {
            month: {
                full: false,
            },
            quarter: {
                full: false,
            },
            year: {
                full: false,
            },
        };
        this.isRecruiterRaceEmpty = false;
        this.isRecruiterRaceMonthEmpty = false;
        this.isRecruiterRaceQuerterEmpty = false;
        this.isRecruiterRaceYearEmpty = false;
        this.currentRaceRange = {};
        this.$rootScope.currentRaceRange = {};
        this.$rootScope.$$phase || this.$scope.$apply();
        Promise.all([
            this.personService.onGetAllPersonsForGamification(),
            this.Achievements.onGetCurrentRaceCompetition({ type: 'month' }),
            this.Achievements.onGetCurrentRaceCompetition({ type: 'quarter' }),
            this.Achievements.onGetCurrentRaceCompetition({ type: 'year' }),
            this.Achievements.onGetTotalExperiencePoints(),
            this.Achievements.onGetBestUserResult(),
            this.Achievements.onGetCurrentUserResult(),
            this.personService.getMe(),
        ])
            .then((resp) => {
                $('img').error(function () {
                    $(this).css('border', 'none');
                });
                this.allPersons = Object.values(resp[0]).filter((user) => user.status === 'A');
                if (resp[1].object.length !== 0) {
                    this.recruiterRace.month = resp[1].object;
                    this.currentRaceRange.month = this.returnMonthNameFromNumber(resp[1].object[0].month);
                    this.$rootScope.monthNumber = resp[1].object[0].month;
                    this.$rootScope.currentRaceRange.month = this.returnMonthNameFromNumber(
                        this.$rootScope.monthNumber,
                    );
                    this.recruiterRace.month = this.recruiterRace.month.map((item) => {
                        let user = this.allPersons.find((person) => person.userId === item.userId);
                        return {
                            ...item,
                            ...user,
                        };
                    });
                    this.recruiterRace.month.full = true;
                    this.teamBestResult = 'month';
                }
                if (resp[2].object.length !== 0) {
                    this.recruiterRace.quarter = resp[2].object;
                    this.currentRaceRange.quarter = resp[2].object[0].quarter;
                    this.recruiterRace.quarter = this.recruiterRace.quarter.map((item) => {
                        let user = this.allPersons.find((person) => person.userId === item.userId);
                        return {
                            ...item,
                            ...user,
                        };
                    });
                    this.recruiterRace.quarter.full = true;
                    this.teamBestResult = 'quarter';
                }
                if (resp[3].object.length !== 0) {
                    this.recruiterRace.year = resp[3].object;
                    this.currentRaceRange.year = resp[3].object[0].year;
                    this.recruiterRace.year = this.recruiterRace.year.map((item) => {
                        let user = this.allPersons.find((person) => person.userId === item.userId);
                        return {
                            ...item,
                            ...user,
                        };
                    });
                    this.recruiterRace.year.full = true;
                    this.teamBestResult = 'year';
                }
                if (resp[4].object.length !== 0) {
                    this.totalExperiencePoints = resp[4].object.totalExperiencePoints;
                    this.userLevel = resp[4].object.userLevel;
                    this.totalExperienceForLevel = resp[4].object.nextLevelPoints;
                }
                this.bestResult = {};
                if (resp[5].object.length !== 0) {
                    this.bestResult.monthCount = (resp[5].object.month && resp[5].object.month.split(':')[0]) || 0;
                    this.bestResult.monthDate = resp[5].object.month && resp[5].object.month.split(':')[1];
                    this.bestResult.quarterCount =
                        (resp[5].object.quarter && resp[5].object.quarter.split(':')[0]) || 0;
                    this.bestResult.quarterDate = resp[5].object.quarter && resp[5].object.quarter.split(':')[1];
                    this.bestResult.yearCount = (resp[5].object.year && resp[5].object.year.split(':')[0]) || 0;
                    this.bestResult.yearDate = resp[5].object.year && resp[5].object.year.split(':')[1];
                }

                if (resp[6].object.length !== 0) {
                    this.currentResult = resp[6].object;
                    this.$rootScope.collectionCounts = resp[6].object.new_crystals;
                }
                if (Object.keys(this.recruiterRace.month).length === (1 || 0)) {
                    this.isRecruiterRaceMonthEmpty = true;
                }
                if (Object.keys(this.recruiterRace.quarter).length === (1 || 0)) {
                    this.isRecruiterRaceQuerterEmpty = true;
                }
                if (Object.keys(this.recruiterRace.year).length === (1 || 0)) {
                    this.isRecruiterRaceYearEmpty = true;
                }
                if (
                    Object.keys(this.recruiterRace.month).length === (1 || 0) &&
                    Object.keys(this.recruiterRace.quarter).length === (1 || 0) &&
                    Object.keys(this.recruiterRace.year).length === (1 || 0)
                ) {
                    this.isRecruiterRaceEmpty = true;
                }

                setTimeout(() => {
                    if (resp[7].object.personParams.showAchievementsInstruction === 'true') {
                        this.shouldShowInstruction = true;
                    } else {
                        if (
                            (resp[7].object.orgParams.gamificationEnabled === 'true' ||
                                resp[7].object.orgParams.gamificationEnabled === undefined) &&
                            resp[7].object.personParams.showAchievementsInstruction === undefined
                        ) {
                            this.shouldShowInstruction = true;
                        }
                    }
                }, 500);
            })
            .finally(() => {
                setTimeout(() => {
                    if (this.shouldShowInstruction) {
                        this.$rootScope.onOpenInstructionPopup();
                    }
                }, 1000);

                this.$rootScope.loading = false;
                this.$rootScope.$$phase || this.$scope.$apply();
            });
    }

    returnNameOfLevel = function (level, sex) {
        switch (level) {
            case 1:
                return 'Pathfinder';
            case 2:
                return 'Experienced Pathfinder';
            case 3:
                return 'Superior Pathfinder';
            case 4:
                return sex ? 'Beater' : 'Beater_f';
            case 5:
                return sex ? 'Experienced Beater' : 'Experienced Beater_f';
            case 6:
                return sex ? 'Superior Beater' : 'Superior Beater_f';
            case 7:
                return sex ? 'Hunter' : 'Huntress';
            case 8:
                return sex ? 'Experienced Hunter' : 'Experienced Huntress';
            case 9:
                return sex ? 'Born Hunter' : 'Born Huntress';
            case 10:
                return 'Hunt Master';
            case 11:
                return sex ? 'Hunt Hero' : 'Hunt Heroine';
            case 12:
                return sex ? 'Hunt Champion' : 'Hunt Champion_f';
            case 13:
                return 'Hunt Icon';
            case 14:
                return sex ? 'Star Hunter' : 'Star Huntress';
            case 15:
                return sex ? 'Epic Hunter' : 'Epic Huntress';
            case 16:
                return sex ? 'Legendary Hunter' : 'Legendary Huntress';
            case 17:
                return sex ? 'Master of the Woods' : 'Mistress of the Woods';
            case 18:
                return sex ? 'King of the Hill' : 'Queen of the Mountains';
            case 19:
                return sex ? 'The Chosen One' : 'The Chosen One_f';
            case 20:
                return sex ? 'God of the Hunt' : 'Goddess of the Hunt';
        }
    };

    returnMonthNameFromNumber = function (number) {
        switch (number) {
            case 1:
                return 'january';
            case 2:
                return 'february';
            case 3:
                return 'march';
            case 4:
                return 'april';
            case 5:
                return 'may';
            case 6:
                return 'june';
            case 7:
                return 'july';
            case 8:
                return 'august';
            case 9:
                return 'september';
            case 10:
                return 'october';
            case 11:
                return 'november';
            case 12:
                return 'december';
        }
    };
    declOfNum(number, words) {
        return words[
            number % 100 > 4 && number % 100 < 20 ? 2 : [2, 0, 1, 1, 1, 2][number % 10 < 5 ? Math.abs(number) % 10 : 5]
        ];
    }

    getAchievementsCount(recruiter) {
        return `${this.$translate.instant('Count of achievements as for now')} ${
            recruiter.awardsNum
        } ${this.$translate.instant(
            this.declOfNum(recruiter.awardsNum, ['achievement-1', 'achievement-2', 'achievement-3']),
        )}`;
    }

    randomString = `${Date.now()}`;
}

const resultsComponentDefinition = {
    bindings: {},
    templateUrl: 'partials/achievements/results.html',
    controller: resultsComponentController,
    controllerAs: 'vm',
};
component.component('resultsComponent', resultsComponentDefinition);
