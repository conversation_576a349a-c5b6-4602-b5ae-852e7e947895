class collectionsComponentController {
    constructor($translate, $scope, $rootScope, Person, $uibModal, notificationService, Achievements, Service) {
        this.$translate = $translate;
        this.$scope = $scope;
        this.$rootScope = $rootScope;
        this.personService = Person;
        this.$uibModal = $uibModal;
        this.notificationService = notificationService;
        this.Achievements = Achievements;
        this.Service = Service;
    }

    $onInit() {
        this.updateCrystals();
        if (this.$rootScope.me.orgParams.gamificationEnabled == 'false') {
            window.location.replace('!#/organizer');
        }
    }

    addLoaderToLoadPage() {
        this.$rootScope.loading = true;
        setTimeout(() => {
            this.$rootScope.loading = false;
            this.$rootScope.$$phase || this.$scope.$apply();
            this.mainBlockDiamants = document.querySelector('.collections-right-block');
            this.mainBlockDiamants.style.transition = '2s all';
            this.mainBlockDiamants.style.visibility = 'visible';
        }, 4000);
    }

    // Method for reognarize crystal after reognarazing for achievments - "Collector"
    reognarizeCrystal() {
        this.Achievements.onReorganizeCrystals().then((resp) => {
            this.$rootScope.newCrystalsCount = (resp.object.t_0_new_crystal && resp.object.t_0_new_crystal['1']) || 0;
            this.$rootScope.collectionCounts = this.$rootScope.newCrystalsCount;
            this.Achievements.onCheckNewNotifications();
            this.$rootScope.$$phase || this.$rootScope.$apply();
        });
    }

    updateCrystals() {
        this.$rootScope.loading = true;
        this.Achievements.onGetCrystalCollection()
            .then((resp) => {
                if (Object.keys(resp.object.crystalCollection).length) {
                    this.crystals = {
                        greenCrystals: [
                            {
                                imgSrc: '/images/sprite/achievements/crystals/green/1.png',
                                className: 'collections-right-block-item-crystals-item-picture-1',
                                isFirstGreen: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/green/2.png',
                                className: 'collections-right-block-item-crystals-item-picture-2',
                                isSecondGreen: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/green/3.png',
                                className: 'collections-right-block-item-crystals-item-picture-3',
                                isThirdGreen: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/green/4.png',
                                className: 'collections-right-block-item-crystals-item-picture-4',
                                isFourthGreen: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/green/5.png',
                                className: 'collections-right-block-item-crystals-item-picture-5',
                                isFifthGreen: true,
                            },
                        ],
                        blueCrystals: [
                            {
                                imgSrc: '/images/sprite/achievements/crystals/blue/1.png',
                                className: 'collections-right-block-item-crystals-item-picture-1',
                                isFirstBlue: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/blue/2.png',
                                className: 'collections-right-block-item-crystals-item-picture-2',
                                isSecondBlue: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/blue/3.png',
                                className: 'collections-right-block-item-crystals-item-picture-3',
                                isThirdBlue: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/blue/4.png',
                                className: 'collections-right-block-item-crystals-item-picture-4',
                                isFourthBlue: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/blue/5.png',
                                className: 'collections-right-block-item-crystals-item-picture-5',
                                isFifthBlue: true,
                            },
                        ],
                        purpleCrystals: [
                            {
                                imgSrc: '/images/sprite/achievements/crystals/purple/1.png',
                                className: 'collections-right-block-item-crystals-item-picture-1',
                                isFirstPurple: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/purple/2.png',
                                className: 'collections-right-block-item-crystals-item-picture-2',
                                isSecondPurple: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/purple/3.png',
                                className: 'collections-right-block-item-crystals-item-picture-3',
                                isThirdPurple: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/purple/4.png',
                                className: 'collections-right-block-item-crystals-item-picture-4',
                                isFourthPurple: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/purple/5.png',
                                className: 'collections-right-block-item-crystals-item-picture-5',
                                isFifthPurple: true,
                            },
                        ],
                        redCrystals: [
                            {
                                imgSrc: '/images/sprite/achievements/crystals/red/1.png',
                                className: 'collections-right-block-item-crystals-item-picture-1',
                                isFirstRed: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/red/2.png',
                                className: 'collections-right-block-item-crystals-item-picture-2',
                                isSecondRed: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/red/3.png',
                                className: 'collections-right-block-item-crystals-item-picture-3',
                                isThirdRed: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/red/4.png',
                                className: 'collections-right-block-item-crystals-item-picture-4',
                                isFourthRed: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/red/5.png',
                                className: 'collections-right-block-item-crystals-item-picture-5',
                                isFifthRed: true,
                            },
                        ],
                        yellowCrystals: [
                            {
                                imgSrc: '/images/sprite/achievements/crystals/yellow/1.png',
                                className: 'collections-right-block-item-crystals-item-picture-1',
                                isFirstYellow: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/yellow/2.png',
                                className: 'collections-right-block-item-crystals-item-picture-2',
                                isSecondYellow: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/yellow/3.png',
                                className: 'collections-right-block-item-crystals-item-picture-3',
                                isThirdYellow: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/yellow/4.png',
                                className: 'collections-right-block-item-crystals-item-picture-4',
                                isFourthYellow: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/yellow/5.png',
                                className: 'collections-right-block-item-crystals-item-picture-5',
                                isFifthYellow: true,
                            },
                        ],
                    };
                    this.$rootScope.newCrystalsCount =
                        (resp.object.crystalCollection.t_0_new_crystal &&
                            resp.object.crystalCollection.t_0_new_crystal['1']) ||
                        0;
                    this.$rootScope.collectionCounts = this.$rootScope.newCrystalsCount;
                    delete resp.object.crystalCollection.t_0_new_crystal;
                    this.allCrystalsCount = 0;
                    this.$rootScope.$$phase || this.$rootScope.$apply();
                    Object.keys(resp.object.crystalCollection)
                        .sort()
                        .forEach((key, ind) => {
                            Object.values(resp.object.crystalCollection[key]).forEach((count, index) => {
                                switch (ind) {
                                    case 0: {
                                        this.allCrystalsCount += count;
                                        this.crystals.greenCrystals[index].count = count;
                                        break;
                                    }
                                    case 1: {
                                        this.allCrystalsCount += count;
                                        this.crystals.blueCrystals[index].count = count;
                                        break;
                                    }
                                    case 2: {
                                        this.allCrystalsCount += count;
                                        this.crystals.purpleCrystals[index].count = count;
                                        break;
                                    }
                                    case 3: {
                                        this.allCrystalsCount += count;
                                        this.crystals.redCrystals[index].count = count;
                                        break;
                                    }
                                    case 4: {
                                        this.allCrystalsCount += count;
                                        this.crystals.yellowCrystals[index].count = count;
                                        break;
                                    }
                                }
                            });
                        });

                    Object.keys(resp.object.colorsMap)
                        .sort()
                        .forEach((key, ind) => {
                            Object.values(resp.object.colorsMap[key]).forEach((count, index) => {
                                switch (ind) {
                                    case 1: {
                                        this.crystals.greenCrystals[index].paintedCrystal = count;
                                        break;
                                    }
                                    case 2: {
                                        this.crystals.blueCrystals[index].paintedCrystal = count;
                                        break;
                                    }
                                    case 3: {
                                        this.crystals.purpleCrystals[index].paintedCrystal = count;
                                        break;
                                    }
                                    case 4: {
                                        this.crystals.redCrystals[index].paintedCrystal = count;
                                        break;
                                    }
                                    case 5: {
                                        this.crystals.yellowCrystals[index].paintedCrystal = count;
                                        break;
                                    }
                                }
                            });
                        });
                    this.$rootScope.$$phase || this.$scope.$apply();
                    this.allCrystalsCount += this.$rootScope.newCrystalsCount;
                    this.$rootScope.$$phase || this.$scope.$apply();
                } else {
                    this.allCrystalsCount = 0;
                    this.$rootScope.newCrystalsCount = 0;
                    this.crystals = {
                        greenCrystals: [
                            {
                                imgSrc: '/images/sprite/achievements/crystals/green/green1.svg',
                                className: 'collections-right-block-item-crystals-item-picture-1',
                                isFirstGreen: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/green/green2.svg',
                                className: 'collections-right-block-item-crystals-item-picture-2',
                                isSecondGreen: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/green/green3.svg',
                                className: 'collections-right-block-item-crystals-item-picture-3',
                                isThirdGreen: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/green/green4.svg',
                                className: 'collections-right-block-item-crystals-item-picture-4',
                                isFourthGreen: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/green/green5.svg',
                                className: 'collections-right-block-item-crystals-item-picture-5',
                                isFifthGreen: true,
                            },
                        ],
                        blueCrystals: [
                            {
                                imgSrc: '/images/sprite/achievements/crystals/blue/blue1.svg',
                                className: 'collections-right-block-item-crystals-item-picture-1',
                                isFirstBlue: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/blue/blue2.svg',
                                className: 'collections-right-block-item-crystals-item-picture-2',
                                isSecondBlue: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/blue/blue3.svg',
                                className: 'collections-right-block-item-crystals-item-picture-3',
                                isThirdBlue: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/blue/blue4.svg',
                                className: 'collections-right-block-item-crystals-item-picture-4',
                                isFourthBlue: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/blue/blue5.svg',
                                className: 'collections-right-block-item-crystals-item-picture-5',
                                isFifthBlue: true,
                            },
                        ],
                        purpleCrystals: [
                            {
                                imgSrc: '/images/sprite/achievements/crystals/purple/purple1.svg',
                                className: 'collections-right-block-item-crystals-item-picture-1',
                                isFirstPurple: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/purple/purple2.svg',
                                className: 'collections-right-block-item-crystals-item-picture-2',
                                isSecondPurple: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/purple/purple3.svg',
                                className: 'collections-right-block-item-crystals-item-picture-3',
                                isThirdPurple: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/purple/purple4.svg',
                                className: 'collections-right-block-item-crystals-item-picture-4',
                                isFourthPurple: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/purple/purple5.svg',
                                className: 'collections-right-block-item-crystals-item-picture-5',
                                isFifthPurple: true,
                            },
                        ],
                        redCrystals: [
                            {
                                imgSrc: '/images/sprite/achievements/crystals/red/red1.svg',
                                className: 'collections-right-block-item-crystals-item-picture-1',
                                isFirstRed: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/red/red2.svg',
                                className: 'collections-right-block-item-crystals-item-picture-2',
                                isSecondRed: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/red/red3.svg',
                                className: 'collections-right-block-item-crystals-item-picture-3',
                                isThirdRed: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/red/red4.svg',
                                className: 'collections-right-block-item-crystals-item-picture-4',
                                isFourthRed: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/red/red5.svg',
                                className: 'collections-right-block-item-crystals-item-picture-5',
                                isFifthRed: true,
                            },
                        ],
                        yellowCrystals: [
                            {
                                imgSrc: '/images/sprite/achievements/crystals/yellow/yellow1.svg',
                                className: 'collections-right-block-item-crystals-item-picture-1',
                                isFirstYellow: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/yellow/yellow2.svg',
                                className: 'collections-right-block-item-crystals-item-picture-2',
                                isSecondYellow: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/yellow/yellow3.svg',
                                className: 'collections-right-block-item-crystals-item-picture-3',
                                isThirdYellow: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/yellow/yellow4.svg',
                                className: 'collections-right-block-item-crystals-item-picture-4',
                                isFourthYellow: true,
                            },
                            {
                                imgSrc: '/images/sprite/achievements/crystals/yellow/yellow5.svg',
                                className: 'collections-right-block-item-crystals-item-picture-5',
                                isFifthYellow: true,
                            },
                        ],
                    };
                }
                this.$rootScope.$$phase || this.$scope.$apply();
                // this.addLoaderToLoadPage()
                this.mainBlockDiamants = document.querySelector('.collections-right-block');
                this.mainBlockDiamants.style.transition = '2s all';
                this.mainBlockDiamants.style.visibility = 'visible';
                return this.reognarizeCrystal();
            })
            .catch((err) => {
                console.error(err);
            })
            .finally(() => {
                setTimeout(() => {
                    this.showAllCrystals = document.querySelectorAll('.collections-right-block-item-crystals-item');
                    this.showAllCrystals.forEach((item) => {
                        item.style.opacity = '1';
                        item.style.transition = '0.5s all';
                    });
                    this.$rootScope.loading = false;
                    this.$rootScope.$$phase || this.$scope.$apply();
                }, 900);
            });
    }

    openDistributeModal() {
        class removeSkillsModalController {
            constructor($rootScope, notificationService, $translate, Achievements, $uibModalInstance) {
                this.$rootScope = $rootScope;
                this.notificationService = notificationService;
                this.$translate = $translate;
                this.Achievements = Achievements;
                this.$uibModalInstance = $uibModalInstance;
            }
            $onInit() {
                this.$rootScope.loading = true;
                this.Achievements.onTransformCrystals()
                    .then((resp) => {
                        this.crystals = {
                            greenCrystals: [
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/green/green1.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-1',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/green/green2.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-2',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/green/green3.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-3',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/green/green4.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-4',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/green/green5.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-5',
                                },
                            ],
                            blueCrystals: [
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/blue/blue1.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-1',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/blue/blue2.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-2',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/blue/blue3.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-3',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/blue/blue4.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-4',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/blue/blue5.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-5',
                                },
                            ],
                            purpleCrystals: [
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/purple/purple1.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-1',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/purple/purple2.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-2',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/purple/purple3.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-3',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/purple/purple4.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-4',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/purple/purple5.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-5',
                                },
                            ],
                            redCrystals: [
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/red/red1.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-1',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/red/red2.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-2',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/red/red3.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-3',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/red/red4.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-4',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/red/red5.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-5',
                                },
                            ],
                            yellowCrystals: [
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/yellow/yellow1.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-1',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/yellow/yellow2.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-2',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/yellow/yellow3.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-3',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/yellow/yellow4.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-4',
                                },
                                {
                                    imgSrc: '/images/sprite/achievements/crystals/yellow/yellow5.svg',
                                    className: 'collections-right-block-item-crystals-item-picture-5',
                                },
                            ],
                        };
                        resp.object.forEach((item) => {
                            switch (item.typeOfCrystal) {
                                case 't_1_emerald': {
                                    this.crystals.greenCrystals[item.levelOfCrystal - 1].count = item.count;
                                    break;
                                }
                                case 't_2_sapphire': {
                                    this.crystals.blueCrystals[item.levelOfCrystal - 1].count = item.count;
                                    break;
                                }
                                case 't_3_amethyst': {
                                    this.crystals.purpleCrystals[item.levelOfCrystal - 1].count = item.count;
                                    break;
                                }
                                case 't_4_ruby': {
                                    this.crystals.redCrystals[item.levelOfCrystal - 1].count = item.count;
                                    break;
                                }
                                case 't_5_topaz': {
                                    this.crystals.yellowCrystals[item.levelOfCrystal - 1].count = item.count;
                                    break;
                                }
                            }
                        });
                        this.generatedCrystals = Object.values(this.crystals).reduce((prev, cur) => {
                            cur.forEach((item) => {
                                if (item.count) {
                                    prev.push(item);
                                }
                            });
                            return prev;
                        }, []);
                        this.$rootScope.loader = true;
                        this.$rootScope.$$phase || this.$rootScope.$apply();
                        setTimeout(() => {
                            this.mainBlockDiamants = document.querySelector('.crystals-in-distribute-popup');
                            this.mainBtn = document.querySelector('.gamification-footer-with-one-btn');
                            this.mainBlockDiamants.style.transition = '1s all';
                            this.mainBlockDiamants.style.opacity = '1';
                            this.mainBtn.style.transition = '1s all';
                            this.mainBtn.style.opacity = '1';
                        }, 1000);
                        this.$rootScope.$$phase || this.$rootScope.$apply();
                    })
                    .catch((err) => console.error(err))
                    .finally(() => {
                        setTimeout(() => {
                            this.$rootScope.loading = false;
                            this.$rootScope.loader = false;
                            this.$rootScope.$$phase || this.$rootScope.$apply();
                        }, 800);
                    });
            }
            closeModal() {
                this.$uibModalInstance.close();
            }
        }
        const modalInstance = this.$uibModal.open({
            animation: true,
            templateUrl: 'partials/modal/achievements/distribute.html',
            windowClass:
                'gamification-popup gamification-popup-reload collections-left-block-main-counter-wrapper-popup',
            controller: [
                '$rootScope',
                'notificationService',
                '$translate',
                'Achievements',
                '$uibModalInstance',
                removeSkillsModalController,
            ],
            controllerAs: 'vm',
            resolve: {
                $rootScope: () => this.$rootScope,
                notificationService: () => this.notificationService,
                $translate: () => this.$translate,
                Achievements: () => this.Achievements,
            },
        });
        modalInstance.closed.then(() => {
            this.$rootScope.newCrystalsCount = 0;
            this.Achievements.onGetCrystalCollection()
                .then((resp) => {
                    if (resp.object.crystalCollection) {
                        delete resp.object.crystalCollection.t_0_new_crystal;
                        Object.keys(resp.object.crystalCollection)
                            .sort()
                            .forEach((key, ind) => {
                                Object.values(resp.object.crystalCollection[key]).forEach((count, index) => {
                                    switch (ind) {
                                        case 0: {
                                            if (this.crystals.greenCrystals[index].count !== count)
                                                this.crystals.greenCrystals[index].count = count;
                                            break;
                                        }
                                        case 1: {
                                            this.crystals.blueCrystals[index].count = count;
                                            break;
                                        }
                                        case 2: {
                                            this.crystals.purpleCrystals[index].count = count;
                                            break;
                                        }
                                        case 3: {
                                            this.crystals.redCrystals[index].count = count;
                                            break;
                                        }
                                        case 4: {
                                            this.crystals.yellowCrystals[index].count = count;
                                            break;
                                        }
                                    }
                                });
                            });
                    }
                    Object.keys(resp.object.colorsMap)
                        .sort()
                        .forEach((key, ind) => {
                            Object.values(resp.object.colorsMap[key]).forEach((count, index) => {
                                switch (ind) {
                                    case 1: {
                                        this.crystals.greenCrystals[index].paintedCrystal = count;
                                        break;
                                    }
                                    case 2: {
                                        this.crystals.blueCrystals[index].paintedCrystal = count;
                                        break;
                                    }
                                    case 3: {
                                        this.crystals.purpleCrystals[index].paintedCrystal = count;
                                        break;
                                    }
                                    case 4: {
                                        this.crystals.redCrystals[index].paintedCrystal = count;
                                        break;
                                    }
                                    case 5: {
                                        this.crystals.yellowCrystals[index].paintedCrystal = count;
                                        break;
                                    }
                                }
                            });
                        });
                    this.$rootScope.$$phase || this.$scope.$apply();
                    this.reognarizeCrystal();
                    return this.Achievements.onReorganizeCrystals();
                })
                .then((resp) => {
                    this.Achievements.onCheckNewNotifications();
                    if (resp.object) {
                        delete resp.object.t_0_new_crystal;
                        this.allCrystalsCount = 0;
                        Object.keys(resp.object)
                            .sort()
                            .forEach((key, ind) => {
                                Object.values(resp.object[key]).forEach((count, index) => {
                                    switch (ind) {
                                        case 0: {
                                            this.allCrystalsCount += count;
                                            if (this.crystals.greenCrystals[index].count !== count && index !== 0) {
                                                this.crystals.greenCrystals[index].increased = true;
                                            }
                                            this.crystals.greenCrystals[index].count = count;
                                            break;
                                        }
                                        case 1: {
                                            this.allCrystalsCount += count;
                                            if (this.crystals.blueCrystals[index].count !== count && index !== 0) {
                                                this.crystals.blueCrystals[index].increased = true;
                                            }
                                            this.crystals.blueCrystals[index].count = count;
                                            break;
                                        }
                                        case 2: {
                                            this.allCrystalsCount += count;
                                            if (this.crystals.purpleCrystals[index].count !== count && index !== 0) {
                                                this.crystals.purpleCrystals[index].increased = true;
                                            }
                                            this.crystals.purpleCrystals[index].count = count;
                                            break;
                                        }
                                        case 3: {
                                            this.allCrystalsCount += count;
                                            if (this.crystals.redCrystals[index].count !== count && index !== 0) {
                                                this.crystals.redCrystals[index].increased = true;
                                            }
                                            this.crystals.redCrystals[index].count = count;
                                            break;
                                        }
                                        case 4: {
                                            this.allCrystalsCount += count;
                                            if (this.crystals.yellowCrystals[index].count !== count && index !== 0) {
                                                this.crystals.yellowCrystals[index].increased = true;
                                            }
                                            this.crystals.yellowCrystals[index].count = count;
                                            break;
                                        }
                                    }
                                });
                            });
                    }
                    this.$rootScope.$$phase || this.$scope.$apply();
                    setTimeout(() => {
                        this.crystals.greenCrystals.forEach((item) => (item.increased = false));
                        this.crystals.blueCrystals.forEach((item) => (item.increased = false));
                        this.crystals.purpleCrystals.forEach((item) => (item.increased = false));
                        this.crystals.redCrystals.forEach((item) => (item.increased = false));
                        this.crystals.yellowCrystals.forEach((item) => (item.increased = false));
                        this.$rootScope.$$phase || this.$scope.$apply();
                    }, 4000);
                })
                .catch((err) => {
                    console.error(err);
                })
                .finally(() => {
                    setTimeout(() => {
                        this.$rootScope.loading = false;
                    });
                    this.$rootScope.$$phase || this.$scope.$apply();
                });
        });
    }
}

const collectionsComponentDefinition = {
    bindings: {},
    templateUrl: 'partials/achievements/collections.html',
    controller: collectionsComponentController,
    controllerAs: 'vm',
};
component.component('collectionsComponent', collectionsComponentDefinition);
