component.component('candidatesSourceEfficiencyComponent', {
    bindings: {},
    templateUrl: 'partials/reports/source/candidatesSourceEfficiency.html',
    controller: class SourceEfficiency {
        amountOfEfficientSources = 0;
        constructor($rootScope, $scope, $filter, $translate, Stat, notificationService) {
            this.$rootScope = $rootScope;
            this.$scope = $scope;
            this.$translate = $translate;
            this.$filter = $filter;

            this.statService = Stat;
            this.notificationService = notificationService;
        }

        _setDayStart(date) {
            if (typeof date === 'number') {
                date = new Date(date);
            }
            date.setHours(0, 0, 0, 0);
            return date;
        }

        _setDayEnd(date) {
            if (typeof date === 'number') {
                date = new Date(date);
            }
            date.setHours(23, 59, 59, 999);
            return date;
        }

        _initDatepickers() {
            $('.picker').bind('click', (e) => {
                if (e.target.classList.contains('disabled')) {
                    this.model.settings.timeRange.selectRange = 'customRange';
                    this.selectDateRange(this.model.settings.timeRange.selectRange);
                }
            });
        }

        _initModel() {
            this.model = {
                sourceStat: null,
                settings: {
                    weeklyStats: false,
                    withVacancies: false,
                    timeRange: {
                        selectRange: 'currentMonth',
                        dateRange: [
                            'currentWeek',
                            'previousWeek',
                            'currentMonth',
                            'previousMonth',
                            'currentYear',
                            'previousYear',
                            'customRange',
                        ],
                        startDate: null,
                        endDate: null,
                        disabledTimeRange: true,
                    },
                },
            };

            let currentDateStart = new Date(),
                currentDateFinish = new Date(),
                currentDate = new Date();

            currentDateStart.setDate(1);
            this._setDayStart(currentDateStart);
            currentDateFinish.setDate(currentDate.getDate());

            this.model.settings.timeRange.startDate = +new Date(currentDateStart);
            this.model.settings.timeRange.endDate = +new Date(currentDateFinish);
        }

        newDateSourceEfficiency = new Date();

        activateTimeRange() {
            this.model.settings.timeRange.disabledTimeRange = false;
            this.model.settings.timeRange.selectRange = 'customRange';
            this.$rootScope.$$phase || this.$scope.$apply();
        }

        _getSourceDiagramConfig(data, isLegendAtTheBottom = false) {
            let detach = !(this.model.sourceStat.userStatistics && this.model.sourceStat.userStatistics.length === 1);
            window.customTooltip = customTooltip.bind(this);

            function customTooltip(p) {
                window.customTooltip = customTooltip.bind(this);

                const dataset = zingchart.exec(p.id, 'getdata'),
                    series = dataset.graphset[p.graphindex].series,
                    translatedValue = this.$filter('DeclOfNumbers')('candidates', series[p.plotindex].values[0]);

                return {
                    text:
                        '<div class="circle-diagram-tooltip">' +
                        '<p class="ff-light">' +
                        this.$translate.instant('Source:') +
                        '</p> ' +
                        '<p class="ff-normal">%t</p> ' +
                        '<p class="ff-light title-for-added">' +
                        this.$translate.instant('Added:') +
                        '</p> ' +
                        '<p><span class="ff-medium">%v</span> <span class="ff-normal">' +
                        this.$translate.instant('candidates out of', {
                            declForm: translatedValue,
                        }) +
                        '</span> <span class="ff-medium">%pie-total-value</span></p> ' +
                        '<p><span class="ff-medium">%npv%</span> <span class="ff-normal">' +
                        this.$translate.instant('from hiring') +
                        '</span> </p>' +
                        '</div>',
                };
            }

            const diagramConfig = {
                type: 'ring',
                title: {
                    offsetY: '10px',
                    'font-size': '14px',
                    'font-weight': 'normal',
                    'font-family': 'Helvetica-Medium',
                    text: this.$translate.instant('Number of successful candidates by source'),
                },
                plot: {
                    detach: detach,
                    'legend-marker': {
                        'border-width': 2,
                        type: 'circle',
                    },
                    tooltip: {
                        'html-mode': true,
                        'background-color': 'transparent',
                        border: 'none',
                        padding: '0',

                        'offset-x': '0',
                        'offset-y': '-5px',
                        jsRule: 'customTooltip()',
                    },
                    'hover-state': {
                        'border-width': '5px',
                        'border-color': '#ffe4b2',
                    },
                    'value-box': {
                        decimals: '2',
                        placement: 'out',
                        'font-color': '#333',
                        rules: [
                            {
                                rule: '%npv >= 7',
                                placement: 'in',
                                'font-color': '#fff',
                            },
                            {
                                rule: '%npv < 7',
                                placement: 'out',
                                'font-color': '#333',
                            },
                        ],
                    },
                },
                'background-color': '#fff',
                border: '1px',
                'border-color': '#ccc',
                'border-width': '1px',
                'border-radius': '5px',
                legend: {
                    'toggle-action': 'remove',
                    item: {
                        maxSize: 15,
                        'font-color': '#000',

                        'font-size': '14px',
                    },
                    layout: data.length > 36 ? 'x3' : 'x2',
                    'background-color': 'transparent',
                    'max-items': 52,
                    overflow: 'visible',
                    scroll: {
                        handle: {
                            width: '8px',
                            'background-color': '#a6a6a6',
                            'border-radius': '15px',
                        },
                    },
                    border: 'none',
                    marker: {
                        type: 'circle',
                    },
                    icon: {
                        'line-color': '#9999ff',
                    },
                },
                series: data,
            };

            if (isLegendAtTheBottom) {
                diagramConfig.legend = {
                    ...diagramConfig.legend,
                    align: 'center',
                    'vertical-align': 'bottom',
                };
            } else {
                diagramConfig.plotarea = {
                    'margin-right': '50%',
                    'margin-top': '10%',
                };
                diagramConfig.legend = {
                    ...diagramConfig.legend,
                    x: '55%',
                    y: '15%',
                };
            }

            return diagramConfig;
        }

        _setCircleDiagram() {
            this.zingchartData = this.model.sourceStat
                .filter((stat) => stat.approvedCandidates)
                .map((stat) => {
                    return {
                        values: [stat.approvedCandidates],
                        text: this.$filter('limitToEllipse')(this.$translate.instant(stat.source), 18),
                    };
                });
            this.amountOfEfficientSources = this.zingchartData.length;
            this._renderZingchart();
        }
        _renderZingchart = () => {
            if (!this.model.sourceStat?.totals.approvedCandidates) return;
            this.currentScreenSize = window.innerWidth >= 1400 ? 'large' : window.innerWidth < 768 ? 'small' : 'medium';
            zingchart.render({
                id: 'circle',
                data: this._getSourceDiagramConfig(
                    this.zingchartData,
                    this.currentScreenSize === 'small' || this.currentScreenSize === 'medium',
                ),
                height: 600 + (this.currentScreenSize === 'large' ? 0 : this.amountOfEfficientSources * 10),
                width: '100%',
            });
            this.$rootScope.$$phase || this.$scope.$apply();
        };

        initZingchartLayoutListener() {
            this.eventHandler = () => {
                clearTimeout(this.resizeTimer);
                this.resizeTimer = setTimeout(this._renderZingchart, 500);
            };
            $(window).resize(this.eventHandler);
        }

        removeZingChartLayoutListener() {
            $(window).off('resize', this.eventHandler);
        }

        _getTotalStatParams(statData) {
            const totals = statData.reduce(
                (total, oneSource) => {
                    total.candidatesBySource += oneSource.candidatesBySource;
                    total.approvedCandidates += oneSource.approvedCandidates;
                    return total;
                },
                { candidatesBySource: 0, approvedCandidates: 0 },
            );
            totals.efficiency =
                totals.candidatesBySource && totals.approvedCandidates
                    ? Math.round((totals.approvedCandidates / totals.candidatesBySource) * 10000) / 100
                    : 0;
            return totals;
        }

        selectDateRange(dateRange) {
            let currentDate = new Date(),
                currentDateStart = new Date(),
                currentDateFinish = new Date();
            this.model.settings.timeRange.disabledTimeRange = true;
            this.model.settings.timeRange.selectRange = dateRange;

            switch (dateRange) {
                case 'currentWeek':
                    // fix bug for Sunday day
                    if (currentDate.getDay() === 0) {
                        currentDateStart.setDate(currentDate.getDate() - (currentDate.getDay() + 6));
                    } else {
                        currentDateStart.setDate(currentDate.getDate() - (currentDate.getDay() - 1));
                    }

                    this._setDayStart(currentDateStart);
                    currentDateFinish.setDate(currentDate.getDate());
                    break;
                case 'previousWeek':
                    if (currentDate.getDay() === 0) {
                        currentDateStart.setDate(currentDate.getDate() - (currentDate.getDay() + 13));
                        currentDateFinish.setDate(currentDate.getDate() - (currentDate.getDay() + 7));
                    } else {
                        currentDateStart.setDate(currentDate.getDate() - (currentDate.getDay() - 1) - 7);
                        currentDateFinish.setDate(currentDate.getDate() - (currentDate.getDay() - 1) - 1);
                    }

                    this._setDayStart(currentDateStart);
                    break;
                case 'currentMonth':
                    currentDateStart.setDate(1);
                    this._setDayStart(currentDateStart);
                    currentDateFinish.setDate(currentDate.getDate());
                    break;
                case 'previousMonth':
                    currentDateStart.setMonth(currentDate.getMonth() - 1, 1);
                    this._setDayStart(currentDateStart);
                    currentDateFinish.setMonth(currentDate.getMonth(), 0);
                    break;
                case 'currentYear':
                    currentDateStart.setFullYear(currentDate.getFullYear(), 0, 1);
                    this._setDayStart(currentDateStart);
                    currentDateFinish.setDate(currentDate.getDate());
                    break;
                case 'previousYear':
                    currentDateStart.setFullYear(currentDate.getFullYear() - 1, 0, 1);
                    this._setDayStart(currentDateStart);
                    currentDateFinish.setFullYear(currentDate.getFullYear(), 0, 0);
                    break;
                case 'customRange':
                    this.model.settings.timeRange.disabledTimeRange = false;
                    this.$rootScope.$$phase || this.$scope.$apply();
                    if (this.model.settings.timeRange.startDate) {
                        currentDateStart = this.model.settings.timeRange.startDate;
                    } else {
                        currentDateStart.setDate(currentDate.getDate() - (currentDate.getDay() - 1));
                        if (currentDateStart) this._setDayStart(currentDateStart);
                    }
                    currentDateFinish = new Date();
                    break;
            }

            this.model.settings.timeRange.startDate = +new Date(currentDateStart);
            this.model.settings.timeRange.endDate = +new Date(currentDateFinish);
        }

        getLettersEnd(number) {
            let n = Math.abs(number) % 100,
                n1 = n % 10,
                form = 2;

            if (n > 10 && n < 20) form = 2;
            if (n1 > 1 && n1 < 5) form = 1;
            if (n1 === 1) {
                if (number > 1 && this.$rootScope.currentLang === 'en') {
                    form = 2;
                } else {
                    form = 0;
                }
            }
            if (n > 10 && n < 15 && n1 > 0 && n1 < 5) form = 2;
            return form;
        }

        getCandidatesSourceStatistic(fromLastReport) {
            const settings = this.model.settings;

            if (fromLastReport) {
                this.model.sourceStat = fromLastReport;
                this.model.sourceStat.withVacancies = settings.withVacancies;
                this.model.sourceStat.totals = this._getTotalStatParams(this.model.sourceStat);
                this._setCircleDiagram();

                this.$rootScope.$$phase || this.$scope.$apply();
            } else {
                if (settings.timeRange.startDate && settings.timeRange.endDate) {
                    settings.timeRange.startDate = this._setDayStart(settings.timeRange.startDate).getTime();
                    settings.timeRange.endDate = this._setDayEnd(settings.timeRange.endDate).getTime();
                    this.statService
                        .onGetSourceEfficiencyStatistic({
                            dateFrom: settings.timeRange.startDate,
                            dateTo: settings.timeRange.endDate,
                            dateRangeType: settings.timeRange.selectRange,
                            type: 'sourceReport',
                            withVacancies: settings.withVacancies,
                        })
                        .then(
                            (resp) => {
                                this.model.sourceStat = resp.objects;
                                this.model.sourceStat.withVacancies = settings.withVacancies;
                                this.model.sourceStat.totals = this._getTotalStatParams(this.model.sourceStat);
                                this._setCircleDiagram();

                                this.$rootScope.$$phase || this.$scope.$apply();
                            },
                            (error) => {
                                this.notificationService.error(error.message);
                            },
                        );
                } else {
                    this.notificationService.error(this.$translate.instant('Enter a date to generate report'));
                }
            }
        }

        _getLastSourceStatReport() {
            this.statService.onGetLastSourceEffStatisticReport().then(
                (resp) => {
                    if (resp.objects) {
                        this._setSelectedTimeRange(resp.objects);
                    } else {
                        this.selectDateRange(this.model.settings.timeRange.selectRange);
                    }

                    this.$scope.$apply();
                },
                (error) => {
                    console.error(error);
                },
            );
        }

        _setSelectedTimeRange(obj) {
            const settings = this.model.settings;
            const lastReportSettings = obj[1];
            const lastReportData = obj[0];
            settings.timeRange.selectRange = lastReportSettings.dateRangeType || 'currentMonth';

            if (lastReportSettings.dateRangeType === 'customRange') {
                settings.timeRange.startDate = lastReportSettings.dateFrom;
                settings.timeRange.endDate = lastReportSettings.dateTo;
            }
            settings.withVacancies = lastReportSettings.withVacancies;

            this.selectDateRange(settings.timeRange.selectRange);

            this.getCandidatesSourceStatistic(lastReportData);
        }

        getStyle(sourceStat) {
            let style = {};
            if (
                this.$rootScope.me.personParams.enableDownloadToExcel == 'N' &&
                sourceStat.totals.approvedCandidates > 0
            ) {
                style['justify-content'] = 'flex-start';
                style['margin-right'] = '150px';
            }

            if (sourceStat && !sourceStat.withVacancies) {
                style['justify-content'] = 'flex-start';
                style['margin-right'] = '150px';
            }

            if (sourceStat && sourceStat.totals.approvedCandidates == 0) {
                style['justify-content'] = 'center';
                style['margin-right'] = '0px';
            }

            return style;
        }

        getStyleWithExcel(sourceStat, model) {
            let style = {};

            if (
                this.$rootScope.me.recrutRole === 'admin' &&
                !this.$rootScope.me.personParams.enableDownloadToExcel &&
                sourceStat &&
                sourceStat.totals.approvedCandidates == 0
            ) {
                style['justify-content'] = 'flex-end';
                style['margin-left'] = '150px';
            }
            //
            // if (
            //     this.$rootScope.me.recrutRole === 'admin' &&
            //     !this.$rootScope.me.personParams.enableDownloadToExcel &&
            //     sourceStat &&
            //     sourceStat.totals.approvedCandidates > 0
            // ) {
            //     style['justify-content'] = 'flex-start';
            //     style['margin-right'] = '150px';
            // }

            if (
                this.$rootScope.me.personParams.enableDownloadToExcel == 'N' &&
                sourceStat.totals.approvedCandidates > 0
            ) {
                style['justify-content'] = 'flex-start';
                style['margin-right'] = '150px';
            }

            if (
                this.$rootScope.me.personParams.enableDownloadToExcel == 'N' &&
                sourceStat &&
                !sourceStat.withVacancies
            ) {
                style['justify-content'] = 'flex-start';
                style['margin-right'] = '150px';
            }

            if (
                sourceStat &&
                sourceStat.totals.approvedCandidates == 0 &&
                this.$rootScope.me.personParams.enableDownloadToExcel
            ) {
                style['justify-content'] = 'flex-end';
                style['margin-right'] = '0px';
            }

            if (
                sourceStat &&
                sourceStat.totals.approvedCandidates == 0 &&
                this.$rootScope.me.personParams.enableDownloadToExcel == 'N'
            ) {
                style['justify-content'] = 'center';
                style['margin-right'] = '0px';
            }

            if (
                sourceStat &&
                sourceStat.totals.approvedCandidates == 0 &&
                this.$rootScope.me.personParams.enableDownloadToExcel == 'Y'
            ) {
                style['justify-content'] = 'flex-end';
                style['margin-left'] = '150px';
            }

            return style;
        }

        downloadExcel() {
            this.$rootScope.loading = true;
            this.statService.onGetSourceEfficiencyExcel().then(
                (resp) => {
                    if (resp.status !== 'error') {
                        this.$rootScope.loading = false;
                        this.$scope.$apply();
                        if (resp.object) {
                            this.$rootScope.downloadReport(resp.object, '#downloadReport');
                        }
                    }
                },
                (err) => {
                    this.$rootScope.loading = false;
                    this.notificationService.error(err.message);
                    this.$scope.$apply();
                },
            );
        }

        toggleVacanciesSelect(val) {
            if (this.model && this.model.settings) {
                this.model.settings.withVacancies = val;
            }
        }

        watchBrowserZoom() {
            const self = this;
            $(window).resize(
                function () {
                    this.browserZoom = Math.round(window.devicePixelRatio * 100);
                    this.$rootScope.$$phase || this.$scope.$apply();
                }.bind(self),
            );
        }

        $onInit() {
            this.$rootScope.redirectToReports();
            this._initModel();
            this._initDatepickers();
            this.selectDateRange = this.selectDateRange.bind(this);
            this.browserZoom = Math.round(window.devicePixelRatio * 100);
            this.watchBrowserZoom();
            this.initZingchartLayoutListener();
        }

        $onDestroy() {
            this.removeZingChartLayoutListener();
        }
    },
    controllerAs: 'vm',
});
