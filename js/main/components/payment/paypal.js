component.component('paypal', {
    bindings: {
        accountInfo: '<',
        currencyExchangeRates: '<',
        lastInvoiceData: '<',
    },
    templateUrl: 'partials/paypal.html',
    controller: class {
        constructor(
            $rootScope,
            $scope,
            Service,
            Invoice,
            Person,
            Account,
            Pay,
            $filter,
            $uibModal,
            notificationService,
        ) {
            this.$rootScope = $rootScope;
            this.$scope = $scope;
            this.Service = Service;
            this.Invoice = Invoice;
            this.Person = Person;
            this.Account = Account;
            this.Pay = Pay;
            this.$filter = $filter;
            this.$uibModal = $uibModal;
            this.notificationService = notificationService;
        }

        clickCopy(text) {
            this.Service.copyToClipboard(text);

            if (text === '18EqGDEGMMpVKzVM1tqFaMgYFdudKMzq1A') {
                this.showText = true;

                setTimeout(() => {
                    this.showText = false;
                    this.$rootScope.$$phase || this.$scope.$apply();
                }, 500);
            } else {
                this.showText2 = true;

                setTimeout(() => {
                    this.showText2 = false;
                    this.$rootScope.$$phase || this.$scope.$apply();
                }, 500);
            }
        }

        $onInit() {}
    },
    controllerAs: 'vm',
});
