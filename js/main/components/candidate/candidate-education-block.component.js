class candidateEducationBlockCtrl {
    constructor($uibModal, $translate, $scope, notificationService, Vacancy, $location, $rootScope, $state, $filter) {
        this.$uibModal = $uibModal;
        this.$translate = $translate;
        this.$scope = $scope;
        this.notificationService = notificationService;
        this.$location = $location;
        this.$rootScope = $rootScope;
        this.$state = $state;
        this.$filter = $filter;
        self = this;
    }

    saveModal(self, type) {
        class saveController {
            constructor(parent, $uibModalInstance, $translate, $scope, $uibModal, notificationService, saveChanges) {
                this.parent = parent;
                this.$uibModalInstance = $uibModalInstance;
                this.$translate = $translate;
                this.$scope = $scope;
                this.$uibModal = $uibModal;
                this.notificationService = notificationService;
                this.saveChanges = saveChanges;
                self = this;
            }

            confirmSave() {
                this.saveChanges(type);
                this.closeModal();
            }
            closeModal() {
                this.$uibModalInstance.close();
            }

            $onInit() {}
        }

        const modalInstance = this.$uibModal.open({
            templateUrl: 'partials/modal/education-experience/confirm-save-modal.html',
            windowClass: 'secondary-modal',
            controller: [
                'parent',
                '$uibModalInstance',
                '$translate',
                '$scope',
                '$uibModal',
                'notificationService',
                'saveChanges',
                saveController,
            ],
            controllerAs: 'vm',
            resolve: {
                parent: () => {
                    return self.parent;
                },
                $translate: () => {
                    return self.$translate;
                },
                $uibModal: () => {
                    return self.$uibModal;
                },
                notificationService: () => {
                    return self.notificationService;
                },
                saveChanges: () => {
                    return self.saveChanges;
                },
            },
        });
    }

    removeModal(self, type, index) {
        class removeController {
            constructor(
                parent,
                $uibModalInstance,
                $translate,
                $scope,
                $uibModal,
                notificationService,
                saveChanges,
                openRedact,
                deleteItem,
            ) {
                this.parent = parent;
                this.$uibModalInstance = $uibModalInstance;
                this.$translate = $translate;
                this.$scope = $scope;
                this.$uibModal = $uibModal;
                this.notificationService = notificationService;
                this.saveChanges = saveChanges;
                this.openRedact = openRedact;
                this.deleteItem = deleteItem;
                self = this;
            }

            confirmRemove() {
                if (typeof index === 'number') {
                    this.deleteItem(type, index);
                } else {
                    this.deleteItem(type);
                }
                this.closeModal();
            }
            closeModal() {
                this.$uibModalInstance.close();
            }

            $onInit() {
                if (typeof index === 'number') {
                    this.delete = true;
                }
                this.type = type;
            }
        }

        const modalInstance = this.$uibModal.open({
            templateUrl: 'partials/modal/education-experience/confirm-remove-modal.html',
            controller: [
                'parent',
                '$uibModalInstance',
                '$translate',
                '$scope',
                '$uibModal',
                'notificationService',
                'saveChanges',
                'openRedact',
                'deleteItem',
                removeController,
            ],
            controllerAs: 'vm',
            windowClass: 'secondary-modal',
            resolve: {
                parent: () => {
                    return self.parent;
                },
                $translate: () => {
                    return self.$translate;
                },
                $uibModal: () => {
                    return self.$uibModal;
                },
                notificationService: () => {
                    return self.notificationService;
                },
                saveChanges: () => {
                    return self.saveChanges;
                },
                openRedact: () => {
                    return self.openRedact;
                },
                deleteItem: () => {
                    return self.deleteItem;
                },
            },
        });
    }

    createNewItem(type) {
        let maxIndex = this.candidate.educationList.length
            ? this.candidate.educationList.map((item) => item.index).reduce((acc, i) => (acc > i ? acc : i))
            : 0;
        this.educationEditModel = angular.copy(this.educationEditModelEmpty);
        this.educationEditModel.newFlag = true;
        this.educationEditModel.index = ++maxIndex;
        if (this.candidate.candidateId) this.educationEditModel.cadidateId = this.candidate.candidateId;
        this.educationRedact = true;
        this.scrollTo(type);
    }

    openRedact(index, type) {
        if (this.educationRedact === true) return;
        this.educationEditModel = angular.copy(this.candidate.educationList[index]);
        this.educationRedactIndex = index;
        this.educationRedact = true;
        this.scrollTo(type);
    }

    saveToCntrl() {
        this.candidate.saveFunc(this.candidate);
    }

    resetFields(fields) {
        if (fields && fields.length) {
            fields.forEach((item) => {
                this.educationErrorFields[item] = false;
            });
        }
    }

    validateFieldLength(model, field, maxLength) {
        this.educationErrorFields[field] = model.length >= maxLength;
        return model.slice(0, maxLength);
    }

    scrollTo(id) {
        setTimeout(function () {
            setTimeout(function () {
                let element = document.querySelector('#' + id);
                element && element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 0);
        }, 0);
    }

    allFieldsAreEmpty() {
        return (
            !this.educationEditModel.description &&
            !this.educationEditModel.company &&
            !this.educationEditModel.position &&
            !this.educationEditModel.fromDate &&
            !this.educationEditModel.toDate
        );
    }

    saveChanges() {
        if (this.allFieldsAreEmpty()) {
            this.notificationService.error(this.$filter('translate')('Please fill the fields'));
            return;
        }
        if (Object.values(this.educationErrorFields).includes(true)) return;

        if (!this.educationEditModel.newFlag) {
            this.candidate.educationList[this.educationRedactIndex] = angular.copy(this.educationEditModel);
            this.educationRedact = false;
        } else {
            delete this.educationEditModel.newFlag;
            this.candidate.educationList.push(angular.copy(this.educationEditModel));
            this.educationRedact = false;
        }
        this.educationEditModel = angular.copy(this.educationEditModelEmpty);
        this.scrollTo('education_header');
        this.saveToCntrl();
    }

    deleteItem(type, index) {
        if (typeof index === 'number') {
            this.candidate.educationList.splice(index, 1);
            this.candidate.educationList.forEach((item, i) => {
                item.index = i;
            });
            this.saveToCntrl();
        } else {
            this.resetFields(Object.keys(this.educationErrorFields));
            this.educationRedact = false;
            this.scrollTo('education_header');
        }
    }
    _initModels() {
        this.educationEditModelEmpty = {
            candidateId: '',
            educationId: '',
            description: '',
            fromDate: '',
            toDate: '',
            speciality: '',
            university: '',
            index: '',
        };
        this.educationRedact = false;
        this.educationRedactIndex = null;
        this.educationEditModel = angular.copy(this.educationEditModelEmpty);
    }

    $onInit() {
        this._initModels();
        this.openRedact = this.openRedact.bind(this);
        this.saveChanges = this.saveChanges.bind(this);
        this.deleteItem = this.deleteItem.bind(this);
        if (!this.candidate.educationList) {
            this.candidate.educationList = [];
        }
    }

    onChangeFromDate = (newValue) => {
        this.educationEditModel.fromDate = this.validateFieldLength(newValue, 'fromDate', 50);
        this.$rootScope.$$phase || this.$scope.$apply();
    };

    onBlurFromDate = () => {
        this.educationErrorFields.fromDate = false;
        this.$rootScope.$$phase || this.$scope.$apply();
    };

    onChangeToDate = (newValue) => {
        this.educationEditModel.toDate = this.validateFieldLength(newValue, 'toDate', 50);
        this.$rootScope.$$phase || this.$scope.$apply();
    };

    onBlurToDate = () => {
        this.educationErrorFields.toDate = false;
        this.$rootScope.$$phase || this.$scope.$apply();
    };

    onChangeUniversity = (newValue) => {
        this.educationEditModel.university = this.validateFieldLength(newValue, 'university', 100);
        this.$rootScope.$$phase || this.$scope.$apply();
    };

    onBlurUniversity = () => {
        this.educationErrorFields.university = false;
        this.$rootScope.$$phase || this.$scope.$apply();
    };

    onChangeSpeciality = (newValue) => {
        this.educationEditModel.speciality = this.validateFieldLength(newValue, 'speciality', 100);
        this.$rootScope.$$phase || this.$scope.$apply();
    };

    onBlurSpeciality = () => {
        this.educationErrorFields.speciality = false;
        this.$rootScope.$$phase || this.$scope.$apply();
    };
}

const candidateEducationBlockComponent = {
    bindings: {
        candidate: '=',
        withTitle: '=?',
        redactMode: '=',
        educationErrorFields: '=',
    },
    templateUrl: 'partials/candidate/candidateEducationBlock.html',
    controller: candidateEducationBlockCtrl,
    controllerAs: 'vm',
};

component.component('candidateEducationBlock', candidateEducationBlockComponent);
