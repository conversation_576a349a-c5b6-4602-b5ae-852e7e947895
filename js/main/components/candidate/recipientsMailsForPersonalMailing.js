component.component('recipientsMailsForPersonalMailing', {
    templateUrl: 'partials/candidate/recipientsMailsForPersonalMailing.html',
    bindings: {
        candidate: '=',
        candidatesIds: '<',
        defaultEmail: '=',
        isManyCandidates: '<',
        candidates: '=',
        candidatesWithoutEmails: '=',
        templateRendered: '=',
        filterByVacancy: '<?',
        vacancy: '=',
        stages: '=?',
        currentStage: '=?',
    },
    controller: function ($rootScope, $scope, Vacancy, notificationService, Candidate, Contacts, $filter) {
        const vm = this;
        vm.emailError = false;
        vm.isAddEmailForCandidateOpened = false;

        vm.changeCandidateEmail = (value) => {
            vm.addedEmail = value;
            $scope.$apply();
        };

        vm.setDefaultEmail = (e, email) => {
            vm.defaultEmail = email;
            vm.emailModel = email.value;
            if (vm.editableCandidate) {
                vm.editableCandidate.emails.forEach((item) => {
                    item.default = item.contactId === email.contactId || item.value === email.value;
                });
            } else {
                vm.emails.forEach((item) => {
                    item.default = item.contactId === email.contactId;
                });
            }
            if (!vm.isManyCandidates) {
                Contacts.settingDefaultEmail({
                    contactId: email.contactId,
                    candidateId: vm.editableCandidate ? vm.editableCandidate.candidateId : vm.candidate.candidateId,
                }).then((resp) => {
                    if (resp.status === 'ok' && resp.objects) {
                        if (vm.editableCandidate) {
                            vm.editableCandidate.contacts = resp.objects;
                        } else {
                            vm.candidate.contacts = resp.objects;
                        }
                    } else {
                        notificationService.error(resp.message);
                    }
                });
            }
        };
        vm.editCandidate = (candidate, $event) => {
            vm.clearField('addedEmail');
            vm.hideAddEmailBlock();
            vm.removeError($event);
            vm.editableCandidate = angular.copy(candidate);
        };
        vm.cancelEditingCandidate = function () {
            if (vm.candidatesIds && vm.candidatesIds.length) {
                if (vm.candidatesWithoutEmails > 0) {
                    Candidate.all(
                        {
                            ids: vm.candidatesIds,
                            sort: 'dm',
                            sortOrder: 'DESC',
                            displayContacts: true,
                        },
                        (resp) => {
                            if (resp.objects) {
                                vm.candidates = resp.objects;
                                vm.candidatesWithoutEmails = 0;
                                vm.candidates.forEach((candidate) => {
                                    candidate.emails = getCandidateMails(candidate);
                                    candidate.defaultEmail = vm.getDefaultEmail(candidate);
                                    if (candidate.emails.length === 0) {
                                        vm.candidatesWithoutEmails++;
                                    }
                                });
                                vm.editableCandidate = null;
                                vm.emailError = false;
                            }
                        },
                    );
                } else {
                    vm.editableCandidate = null;
                    vm.emailError = false;
                }
            } else {
                vm.editableCandidate = null;
                vm.emailError = false;
            }
        };

        vm.getParamsForGetCandidatesByStages = function (stageName = 'longlist') {
            return {
                state: stageName,
                page: { number: 0, count: 10000 },
                vacancyId: vm.vacancy.vacancyId,
                withCandidates: true,
                withVacancies: false,
                name: null,
                interviewSortEnum: 'addInVacancyDate',
            };
        };

        vm.onStageChanged = function (stage) {
            vm.currentStage = stage.id ? stage.id : vm.currentStage;
            vm.stageModel = stage;
            $scope.$apply();
            vm.setCandidatesFromStage();
        };

        vm.setCandidatesFromStage = function () {
            $rootScope.loading = true;
            vm.getCandidatesByStages()
                .then((candidates) => {
                    vm.filterCandidatesList = candidates;
                })
                .catch((err) => notificationService.error(err.message))
                .finally(() => {
                    $rootScope.loading = false;
                    $scope.$apply();
                });
        };

        vm.getCandidatesByStages = function () {
            if (!vm.stageModel) vm.stageModel = vm.stages.find((item) => item.value === 'longlist');
            return new Promise((resolve, reject) => {
                const paramsForGetCandidatesByStage = vm.getParamsForGetCandidatesByStages(vm.currentStage);
                if (paramsForGetCandidatesByStage) {
                    Vacancy.onGetCandidatesInStages(paramsForGetCandidatesByStage)
                        .then((resp) => {
                            const candidatesFromStages = resp.objects.map((candidate) => candidate.candidateId) || [];

                            vm.candidates.forEach((candidate) => {
                                candidatesFromStages.forEach((candidateFromSelectedStage, ind) => {
                                    if (candidate.candidateId === candidateFromSelectedStage.candidateId) {
                                        candidatesFromStages.splice(ind, 1);
                                    }
                                });
                            });

                            resolve(candidatesFromStages);
                        })
                        .catch((err) => {
                            reject(err);
                        });
                }
            });
        };

        vm.getAllCandidates = (data, callback) => {
            Candidate.getAutocompleteCandidates({
                name: data.trim(),
                notInIds: (vm.candidatesIdsString = vm.candidatesIds.join(',')),
                withPersonalContacts: true,
            }).then((resp) => {
                callback([...resp.objects]);
            });
            $scope.$$phase || $scope.$apply();
        };

        vm.$onInit = function () {
            vm.emailModel = '';
            if (vm.filterByVacancy) {
                vm.stages = vm.stages.map((stage) => {
                    return {
                        value: stage.value,
                        label: `${$filter('translate')(stage.value)} (${stage.count})`,
                        id: stage.customInterviewStateId ? stage.customInterviewStateId : stage.value,
                        count: stage.count,
                    };
                });
            }
            vm.showRemoveAllRecipientsWithoutEmails = false;

            if (vm.candidatesIds) {
                vm.candidatesIdsString = vm.candidatesIds.join(',');
                Candidate.all(
                    {
                        ids: vm.candidatesIds,
                        sort: 'dm',
                        sortOrder: 'DESC',
                        displayContacts: true,
                    },
                    (resp) => {
                        if (resp.objects) {
                            vm.candidates = resp.objects;
                            vm.candidatesWithoutEmails = 0;
                            vm.candidates.forEach((candidate) => {
                                candidate.emails = getCandidateMails(candidate);
                                candidate.defaultEmail = vm.getDefaultEmail(candidate);
                                if (candidate.emails.length === 0) {
                                    vm.candidatesWithoutEmails++;
                                }
                            });

                            if (vm.filterByVacancy && vm.candidatesIds) {
                                vm.setCandidatesFromStage();
                            }
                        }
                    },
                );
            } else {
                if (vm.candidate && vm.candidate.contacts) {
                    vm.emails = getCandidateMails(vm.candidate);
                    if (vm.emails && !vm.defaultEmail) {
                        vm.defaultEmail = vm.emails.filter((email) => email.default)[0];
                        vm.emailModel = vm.defaultEmail && vm.defaultEmail.value;
                    }
                } else if (vm.candidate.localId) {
                    Candidate.one({ localId: vm.candidate.localId }, function (resp) {
                        if (resp.status === 'ok') {
                            vm.candidate = resp.object;
                            vm.emails = getCandidateMails(vm.candidate);
                            if (vm.emails && !vm.defaultEmail) {
                                vm.defaultEmail = vm.emails.filter((email) => email.default)[0];
                                vm.emailModel = vm.defaultEmail && vm.defaultEmail.value;
                            }
                        }
                    });
                }
            }
        };

        vm.hasCandidateAtLeastOneEmail = (candidate) => {
            if (candidate && candidate.emails) {
                return candidate.emails.length > 0;
            }
        };

        vm.hasCandidateMoreThenOneEmail = (candidate) => {
            if (candidate && candidate.emails) {
                return candidate.emails.length > 1;
            }
        };

        vm.removeCandidate = (candidate) => {
            vm.filterCandidatesList = vm.filterCandidatesList ? vm.filterCandidatesList : [];
            vm.candidates = vm.candidates.filter((item) => item.candidateId !== candidate.candidateId);
            vm.candidatesIds = vm.candidates.map((candidate) => candidate.candidateId);
            vm.candidatesIdsString = vm.candidatesIds.join(',');
            vm.filterCandidatesList.push(candidate);
            if (vm.editableCandidate && vm.editableCandidate.candidateId === candidate.candidateId) {
                vm.cancelEditingCandidate();
            }

            if (!candidate.defaultEmail) {
                vm.candidatesWithoutEmails--;
            }
            if (vm.candidates.length === 1) {
                vm.templateRendered.text = vm.templateRendered.text.replace(
                    /\[\[candidate name\]\]/g,
                    vm.candidates[0].fullName,
                );
            }
        };

        vm.removeAllRecipientsWithoutEmails = function () {
            vm.filterCandidatesList = vm.filterCandidatesList ? vm.filterCandidatesList : [];
            vm.candidates = vm.candidates.filter((candidate) => {
                if (candidate.emails && candidate.emails.length) {
                    vm.candidatesWithoutEmails = 0;
                    return candidate.emails.length > 0;
                } else {
                    vm.filterCandidatesList.push(candidate);
                }
            });
        };

        vm.saveCandidateContacts = function () {
            return new Promise((resolve) => {
                const candidateContactsPayload = {
                    candidateId: vm.editableCandidate ? vm.editableCandidate.candidateId : vm.candidate.candidateId,
                    contacts: vm.editableCandidate
                        ? vm.editableCandidate.contacts
                            ? [...vm.editableCandidate.contacts]
                            : []
                        : vm.candidate.contacts
                        ? [...vm.candidate.contacts]
                        : [],
                };
                $rootScope.loading = true;

                Candidate.onSaveContacts(candidateContactsPayload)
                    .then((resp) => {
                        if (resp.object.length > 0) {
                            resp.object.forEach((item) => {
                                if (item.value === vm.addedEmail) {
                                    if (vm.editableCandidate) {
                                        vm.editableCandidate.emails.push(item);
                                    } else {
                                        vm.emails.push(item);
                                    }

                                    this.addedEmail = '';
                                    $scope.$emit('addEmail');
                                }
                            });
                        }
                    })
                    .catch((err) => {
                        notificationService.error(err.message);
                    })
                    .finally(() => {
                        $rootScope.loading = false;
                        $scope.$apply();
                        resolve();
                    });
            });
        };

        vm.addEmail = (isAddEmailForCandidateOpened) => {
            if (!validateEmail(vm.addedEmail)) {
                vm.emailError = true;
            }
            if (vm.addedEmail && validateEmail(vm.addedEmail)) {
                vm.emailError = false;
                const newEmail = { type: 'email', value: vm.addedEmail };
                if (vm.isManyCandidates) {
                    vm.editableCandidate.contacts = vm.editableCandidate.contacts ? vm.editableCandidate.contacts : [];
                    vm.editableCandidate.emails = vm.editableCandidate.emails ? vm.editableCandidate.emails : [];
                    vm.editableCandidate.contacts.push(newEmail);
                    vm.editableCandidate.emails.push(newEmail);
                    vm.addedEmail = '';
                } else {
                    vm.candidate.contacts = vm.candidate.contacts ? vm.candidate.contacts : [];
                    vm.candidate.contacts.push(newEmail);
                }

                vm.defaultEmail = vm.isManyCandidates
                    ? vm.editableCandidate.contacts[vm.editableCandidate.contacts.length - 1]
                    : vm.candidate.contacts[vm.candidate.contacts.length - 1];
                if (!vm.isManyCandidates) {
                    vm.saveCandidateContacts().then(() => {
                        const newEmail = vm.emails.find((email) => email.value === vm.defaultEmail.value);
                        vm.defaultEmail = newEmail;
                        vm.emailModel = newEmail.value;
                        vm.setDefaultEmail('', newEmail);

                        if (isAddEmailForCandidateOpened) {
                            vm.isAddEmailForCandidateOpened = false;
                            vm.emitEventOnAddEmailForCandidate();
                        }
                    });
                } else {
                    vm.setDefaultEmail(
                        '',
                        vm.editableCandidate.emails.find((email) => email.value === vm.defaultEmail.value),
                    );
                    if (isAddEmailForCandidateOpened) {
                        vm.isAddEmailForCandidateOpened = false;
                        vm.emitEventOnAddEmailForCandidate();
                    }
                }
            }
        };

        vm.removeError = (event) => {
            if (!vm.candidates || (vm.candidates && vm.candidates.length > 0)) {
                event.currentTarget.classList.remove('error');
                vm.emailError = false;
            }
        };

        vm.saveEditableCandidate = function () {
            if (vm.isAddEmailForCandidateOpened && !validateEmail(vm.addedEmail)) {
                vm.emailError = true;
                return;
            } else {
                vm.isAddEmailForCandidateOpened = false;
                vm.emailError = false;
            }

            if (vm.editableCandidate) {
                vm.saveCandidateContacts().then(() => {
                    const candidate = vm.candidates.filter(
                        (item) => item.candidateId === vm.editableCandidate.candidateId,
                    )[0];

                    candidate.emails.forEach(
                        (item) => (item.default = !!(item.contactId === candidate.defaultEmail.contactId)),
                    );

                    candidate.defaultEmail = vm.getDefaultEmail(candidate);

                    vm.cancelEditingCandidate();
                    $scope.$$phase || $scope.$apply();
                });
            } else {
                vm.emailError = true;
            }
        };

        vm.saveEditingCandidate = function () {
            if (vm.newEmail) {
                if (!validateEmail(vm.newEmail)) {
                    vm.emailError = true;
                } else {
                    const candidate = vm.candidates.filter(
                        (item) => item.candidateId === vm.editableCandidate.candidateId,
                    )[0];
                    candidate.contacts.push({
                        type: 'email',
                        value: vm.newEmail,
                    });
                    Candidate.add(candidate, (resp) => {
                        if (resp.status === 'ok') {
                            resp.object.contacts.forEach((contact) => {
                                if (contact.value === vm.newEmail) {
                                    candidate.emails = resp.object.contacts.filter((item) => item.type === 'email');
                                    candidate.defaultEmail = vm.getDefaultEmail(candidate);
                                    vm.newEmail = null;
                                    vm.editableCandidate = null;
                                    vm.candidatesWithoutEmails--;
                                }
                            });
                        } else {
                            if (resp.code === 'incorrectEmail') {
                                vm.emailError = true;
                            }
                        }
                    });
                }
            } else if (vm.editableCandidate && vm.editableCandidate.defaultEmail) {
                const candidate = vm.candidates.filter(
                    (item) => item.candidateId === vm.editableCandidate.candidateId,
                )[0];
                const email = candidate.emails.filter((item) => item.contactId === candidate.defaultEmail.contactId)[0];

                candidate.emails.forEach(
                    (item) => (item.default = !!(item.contactId === candidate.defaultEmail.contactId)),
                );
                candidate.defaultEmail = vm.getDefaultEmail(candidate);
                vm.cancelEditingCandidate();

                Contacts.settingDefaultEmail({
                    contactId: email.contactId,
                    candidateId: candidate.candidateId,
                });
            }
        };

        vm.candidatesMoreEmailsCount = (candidate) => {
            if (candidate && candidate.emails) {
                return candidate.emails.length - 1;
            }
        };

        vm.getDefaultEmail = (candidate) => {
            if (candidate && candidate.emails) {
                return candidate.emails.filter((item) => item.default)[0];
            }
        };

        vm.getDefaultEmailValue = (candidate) => {
            if (candidate && vm.getDefaultEmail(candidate)) {
                return vm.getDefaultEmail(candidate).value;
            }
        };

        vm.clearField = function (value) {
            vm[value] = '';
        };

        vm.hideAddEmailBlock = function () {
            vm.isAddEmailForCandidateOpened = false;
        };

        vm.addEmailForCandidate = function ($event) {
            vm.isAddEmailForCandidateOpened = !vm.isAddEmailForCandidateOpened;
            if (!vm.isAddEmailForCandidateOpened) {
                vm.removeError($event);
                vm.clearField('addedEmail');
            }
            if (!vm.isManyCandidates) {
                vm.emitEventOnAddEmailForCandidate();
            }
        };

        vm.emitEventOnAddEmailForCandidate = function () {
            $scope.$emit('isAddEmailForCandidateOpened', vm.isAddEmailForCandidateOpened);
        };

        vm.onSelectCandidateForMailing = (value) => {
            const { candidateId, localId } = value;
            vm.candidatesIds.unshift(candidateId);
            vm.candidatesIdsString = vm.candidatesIds.join(',');
            Candidate.one({ localId: localId }, function (resp) {
                if (resp.status === 'ok') {
                    const candidate = resp.object;
                    candidate.emails = getCandidateMails(candidate);
                    candidate.defaultEmail = vm.getDefaultEmail(candidate);
                    if (candidate.emails.length === 0) {
                        vm.candidatesWithoutEmails++;
                    }
                    vm.filterCandidatesList = vm.filterCandidatesList.filter(
                        (candidate) => candidate.candidateId !== candidateId,
                    );
                    vm.candidates.unshift(candidate);
                    vm.candidateForAddingString = '';
                }
            });
        };

        $rootScope.$on('personalMailingCandidateAutocompleter', (e, localId) => {
            Candidate.one({ localId: localId }, function (resp) {
                if (resp.status === 'ok') {
                    const candidate = resp.object;
                    vm.candidatesIds.unshift(candidate.candidateId);
                    vm.candidatesIdsString = vm.candidatesIds.join(',');
                    candidate.emails = getCandidateMails(candidate);
                    candidate.defaultEmail = vm.getDefaultEmail(candidate);
                    if (candidate.emails.length === 0) {
                        vm.candidatesWithoutEmails++;
                    }
                    vm.candidates.unshift(candidate);
                    vm.filteredCandidates = [];
                    vm.candidateForAddingString = '';
                }
            });
        });

        $rootScope.$on('Not all recipients have emails', () => {
            vm.showRemoveAllRecipientsWithoutEmails = true;
            vm.candidates.sort((a, b) => {
                return a.emails.length - b.emails.length;
            });
        });

        function getCandidateMails(candidate) {
            if (candidate) {
                if (candidate.contacts) {
                    return candidate.contacts.filter((contact) => contact.type === 'email');
                }
                return [];
            }
        }

        function validateEmail(email) {
            return /(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/i.test(
                email,
            );
        }
    },
    controllerAs: 'vm',
});
