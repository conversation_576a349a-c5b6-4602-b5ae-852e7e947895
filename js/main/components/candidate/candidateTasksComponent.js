class candidateTasksComponentCtrl {
    constructor(
        scoreCardsMainService,
        notificationService,
        $scope,
        $rootScope,
        $translate,
        $uibModal,
        $state,
        $stateParams,
    ) {
        this.scoreCardsMainService = scoreCardsMainService;
        this.notificationService = notificationService;
        this.$scope = $scope;
        this.$rootScope = $rootScope;
        this.$translate = $translate;
        this.$uibModal = $uibModal;
        this.$state = $state;
        this.$stateParams = $stateParams;
    }
    $onInit() {}
}

const candidateTasksComponentDefinition = {
    bindings: {
        candidate: '=',
        candidateTasks: '<',
        customStages: '<',
        vacancy: '<',
        fullDataCandidate: '<',
        linkedCandidate: '<',
        tableParamsCandidateHistory: '=',
        history: '=',
        urlTaskId: '=',
        showMore: '=',
        isShowMore: '=',
        objectSize: '=',
        displayShowMore: '=',
        paginationParams: '=',
    },
    templateUrl: 'partials/candidate/candidateTasks.component.html',
    controller: candidateTasksComponentCtrl,
    controllerAs: 'vm',
};
component.component('candidateTasksComponent', candidateTasksComponentDefinition);
