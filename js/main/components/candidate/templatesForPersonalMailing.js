component.component('templatesForPersonalMailing', {
    templateUrl: 'partials/candidate/templatesForPersonalMailing.html',
    bindings: {
        showTemplates: '<',
        templateModel: '=',
        vacancyId: '=',
    },
    controller: function (
        $rootScope,
        $scope,
        $filter,
        $translate,
        $state,
        $stateParams,
        $location,
        Vacancy,
        Service,
        $uibModal,
        notificationService,
        Mail,
        PersonalMailing,
    ) {
        const vm = this;
        const templateInfo = PersonalMailing.templateInfo;
        vm.$onInit = function () {
            if (vm.showTemplates) getTemplates($scope.vm.vacancyId);
        };

        vm.hoverInfoShow = function (index) {
            vm.visibility2 = true;
            vm.message = templateInfo[index];
            vm.hoverItemIndex = index;
        };

        vm.hoverInfoHidden = function () {
            vm.visibility2 = false;
        };

        vm.setTemplate = (template) => {
            vm.templateModel.text = template.text;
            vm.templateModel.title = template.title;
            vm.templateModelOriginText = template.text;
            vm.currentTemplateType = template.type;
            $rootScope.$broadcast('updateRendered');
        };

        vm.setDefaultTemplate = function () {
            vm.templateModel.text = vm.templateModelOriginText;
        };

        function getTemplates(vacancyId) {
            Mail.getTemplatesVacancy({ vacancyId: vacancyId }, function (data) {
                vm.emailTemplates = data.objects;
            });
        }
    },
    controllerAs: 'vm',
});
