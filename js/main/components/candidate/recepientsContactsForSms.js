component.component('recipientsContactsForSms', {
    templateUrl: 'partials/candidate/recipientsContactsForSms.html',
    bindings: {
        candidate: '=',
        candidatesIds: '<',
        defaultPhone: '=',
        isManyCandidates: '<',
        candidates: '=',
        candidatesWithoutPhones: '=',
        templateRendered: '=',
        filterByVacancy: '<?',
        vacancy: '=',
        stages: '=?',
        currentStage: '=?',
    },
    controller: function ($rootScope, $scope, Vacancy, notificationService, Candidate, Contacts, $filter) {
        const vm = this;
        vm.phoneError = false;
        vm.isAddPhoneForCandidateOpened = false;

        vm.changeCandidatePhone = (value) => {
            vm.addedPhone = value.replace(/[^+0-9]/g, '');
            $scope.$apply();
        };

        vm.setDefaultPhone = (e, phone) => {
            vm.defaultPhone = phone;
            vm.phoneModel = phone.value;
            if (vm.editableCandidate) {
                vm.editableCandidate.phoneNumbers.forEach((item) => {
                    item.default = item.contactId === phone.contactId || item.value === phone.value;
                });
            } else {
                vm.phones.forEach((item) => {
                    item.default = item.contactId === phone.contactId;
                });
            }
            if (!vm.isManyCandidates) {
                Contacts.settingDefaultEmail({
                    contactId: phone.contactId,
                    candidateId: vm.editableCandidate ? vm.editableCandidate.candidateId : vm.candidate.candidateId,
                }).then((resp) => {
                    if (resp.status === 'ok' && resp.objects) {
                        if (vm.editableCandidate) {
                            vm.editableCandidate.contacts = resp.objects;
                        } else {
                            vm.candidate.contacts = resp.objects;
                        }
                    } else {
                        notificationService.error(resp.message);
                    }
                });
            }
        };
        vm.editCandidate = (candidate, $event) => {
            vm.clearField('addedPhone');
            vm.hideAddPhoneBlock();
            vm.removeError($event);
            vm.editableCandidate = angular.copy(candidate);
        };
        vm.cancelEditingCandidate = function () {
            if (vm.candidatesIds && vm.candidatesIds.length) {
                if (vm.candidatesWithoutPhones > 0) {
                    Candidate.all(
                        {
                            ids: vm.candidatesIds,
                            sort: 'dm',
                            sortOrder: 'DESC',
                            displayContacts: true,
                        },
                        (resp) => {
                            if (resp.objects) {
                                vm.candidates = resp.objects.map((candidate) => {
                                    return {
                                        ...candidate,
                                        phoneNumbers: candidate.contacts.filter((contact) => contact.type === 'mphone'),
                                    };
                                });
                                vm.candidatesWithoutPhones = 0;
                                vm.candidates.forEach((candidate) => {
                                    candidate.phones = getCandidatePhones(candidate);
                                    candidate.defaultPhone = vm.getDefaultPhone(candidate);
                                    if (candidate.phoneNumbers.length === 0) {
                                        vm.candidatesWithoutPhones++;
                                    }
                                });
                                vm.editableCandidate = null;
                                vm.phoneError = false;
                            }
                        },
                    );
                } else {
                    vm.editableCandidate = null;
                    vm.phoneError = false;
                }
            } else {
                vm.editableCandidate = null;
                vm.phoneError = false;
            }
        };

        vm.getParamsForGetCandidatesByStages = function (stageName = 'longlist') {
            return {
                state: stageName,
                page: { number: 0, count: 10000 },
                vacancyId: vm.vacancy.vacancyId,
                withCandidates: true,
                withVacancies: false,
                name: null,
                interviewSortEnum: 'addInVacancyDate',
            };
        };

        vm.onStageChanged = function (stage) {
            vm.currentStage = stage.id ? stage.id : vm.currentStage;
            vm.stageModel = stage;
            $scope.$apply();
            vm.setCandidatesFromStage();
        };

        vm.setCandidatesFromStage = function () {
            $rootScope.loading = true;
            vm.getCandidatesByStages()
                .then((candidates) => {
                    vm.filterCandidatesList = candidates;
                })
                .catch((err) => notificationService.error(err.message))
                .finally(() => {
                    $rootScope.loading = false;
                    $scope.$apply();
                });
        };

        vm.getCandidatesByStages = function () {
            if (!vm.stageModel) vm.stageModel = vm.stages.find((item) => item.value === 'longlist');
            return new Promise((resolve, reject) => {
                const paramsForGetCandidatesByStage = vm.getParamsForGetCandidatesByStages(vm.currentStage);
                if (paramsForGetCandidatesByStage) {
                    Vacancy.onGetCandidatesInStages(paramsForGetCandidatesByStage)
                        .then((resp) => {
                            const candidatesFromStages = resp.objects.map((candidate) => candidate.candidateId) || [];

                            vm.candidates.forEach((candidate) => {
                                candidatesFromStages.forEach((candidateFromSelectedStage, ind) => {
                                    if (candidate.candidateId === candidateFromSelectedStage.candidateId) {
                                        candidatesFromStages.splice(ind, 1);
                                    }
                                });
                            });

                            resolve(candidatesFromStages);
                        })
                        .catch((err) => {
                            reject(err);
                        });
                }
            });
        };

        vm.getAllCandidates = (data, callback) => {
            Candidate.getAutocompleteCandidates({
                name: data.trim(),
                notInIds: (vm.candidatesIdsString = vm.candidatesIds.join(',')),
                withPersonalContacts: true,
            }).then((resp) => {
                callback([...resp.objects]);
            });
            $scope.$$phase || $scope.$apply();
        };

        vm.$onInit = function () {
            vm.phoneModel = '';
            if (vm.filterByVacancy) {
                vm.stages = vm.stages.map((stage) => {
                    return {
                        value: stage.value,
                        label: `${$filter('translate')(stage.value)} (${stage.count})`,
                        id: stage.customInterviewStateId ? stage.customInterviewStateId : stage.value,
                        count: stage.count,
                    };
                });
            }
            vm.showRemoveAllRecipientsWithoutPhones = false;

            if (vm.candidates) {
                vm.candidates = vm.candidates.map((candidate) => {
                    return {
                        ...(candidate.contacts ? candidate : candidate.candidateId),
                        phoneNumbers: candidate.contacts
                            ? candidate.contacts.filter((contact) => contact.type === 'mphone')
                            : candidate.candidateId?.contacts?.filter((contact) => contact.type === 'mphone') || [],
                    };
                });
                vm.candidatesWithoutPhones = 0;
                vm.candidates.forEach((candidate) => {
                    candidate.defaultPhone = vm.getDefaultPhone(candidate);
                    if (candidate.phoneNumbers.length === 0) {
                        vm.candidatesWithoutPhones++;
                    }
                });
            } else {
                if (vm.candidate && vm.candidate.contacts) {
                    vm.phones = getCandidatePhones(vm.candidate);
                    if (vm.emails && !vm.defaultPhone) {
                        vm.defaultPhone = vm.emails.filter((email) => email.default)[0];
                        vm.phoneModel = vm.defaultPhone && vm.defaultPhone.value;
                    }
                } else if (vm.candidate.localId) {
                    Candidate.one({ localId: vm.candidate.localId }, function (resp) {
                        if (resp.status === 'ok') {
                            vm.candidate = resp.object;
                            vm.phones = getCandidatePhones(vm.candidate);
                            if (vm.emails && !vm.defaultPhone) {
                                vm.defaultPhone = vm.emails.filter((email) => email.default)[0];
                                vm.phoneModel = vm.defaultPhone && vm.defaultPhone.value;
                            }
                        }
                    });
                }
            }
        };

        vm.hasCandidateAtLeastPhone = (candidate) => {
            if (candidate && candidate.phoneNumbers) {
                return candidate.phoneNumbers.length > 0;
            }
        };

        vm.hasCandidateMoreThenOneEmail = (candidate) => {
            if (candidate && candidate.emails) {
                return candidate.emails.length > 1;
            }
        };

        vm.removeCandidate = (candidate) => {
            vm.filterCandidatesList = vm.filterCandidatesList ? vm.filterCandidatesList : [];
            vm.candidates = vm.candidates.filter((item) => item.candidateId !== candidate.candidateId);
            vm.candidatesIds = vm.candidates.map((candidate) => candidate.candidateId);
            vm.candidatesIdsString = vm.candidatesIds.join(',');
            vm.filterCandidatesList.push(candidate);
            if (vm.editableCandidate && vm.editableCandidate.candidateId === candidate.candidateId) {
                vm.cancelEditingCandidate();
            }

            if (!candidate.defaultPhone) {
                vm.candidatesWithoutPhones--;
            }
            if (vm.candidates.length === 1) {
                vm.templateRendered.text = vm.templateRendered.text?.replace(
                    /\[\[candidate name\]\]/g,
                    vm.candidates[0].fullName,
                );
            }
            $scope.$$phase || $scope.$apply();
        };

        vm.removeAllRecipientsWithoutPhones = function () {
            vm.filterCandidatesList = vm.filterCandidatesList ? vm.filterCandidatesList : [];
            vm.candidates = vm.candidates.filter((candidate) => {
                if (candidate.phoneNumbers && candidate.phoneNumbers.length) {
                    vm.candidatesWithoutPhones = 0;
                    return candidate.phoneNumbers.length > 0;
                } else {
                    vm.filterCandidatesList.push(candidate);
                }
            });
        };

        vm.saveCandidateContacts = function () {
            return new Promise((resolve) => {
                const candidateContactsPayload = {
                    candidateId: vm.editableCandidate ? vm.editableCandidate.candidateId : vm.candidate.candidateId,
                    contacts: vm.editableCandidate
                        ? vm.editableCandidate.contacts
                            ? [...vm.editableCandidate.contacts]
                            : []
                        : vm.candidate.contacts
                        ? [...vm.candidate.contacts]
                        : [],
                };
                $rootScope.loading = true;

                Candidate.onSaveContacts(candidateContactsPayload)
                    .then((resp) => {
                        if (resp.object.length > 0) {
                            resp.object.forEach((item) => {
                                if (item.value === vm.addedPhone) {
                                    if (vm.editableCandidate) {
                                        vm.editableCandidate.phoneNumbers.push(item);
                                    } else {
                                        vm.phones.push(item);
                                    }

                                    this.addedPhone = '';
                                    $scope.$emit('addEmail');
                                }
                            });
                        }
                    })
                    .catch((err) => {
                        notificationService.error(err.message);
                    })
                    .finally(() => {
                        $rootScope.loading = false;
                        $scope.$apply();
                        resolve();
                    });
            });
        };

        vm.addPhone = (isAddPhoneForCandidateOpened) => {
            if (isAddPhoneForCandidateOpened && !vm.addedPhone) {
                return;
            }
            if (!validatePhone(vm.addedPhone)) {
                vm.phoneError = true;
            }
            if (vm.addedPhone && validatePhone(vm.addedPhone)) {
                vm.phoneError = false;
                const newPhone = { type: 'mphone', value: vm.addedPhone };
                if (vm.isManyCandidates) {
                    vm.editableCandidate.contacts = vm.editableCandidate.contacts ? vm.editableCandidate.contacts : [];
                    vm.editableCandidate.phoneNumbers = vm.editableCandidate.phoneNumbers
                        ? vm.editableCandidate.phoneNumbers
                        : [];
                    vm.editableCandidate.contacts.push(newPhone);
                    vm.editableCandidate.phoneNumbers.push(newPhone);
                    vm.addedPhone = '';
                    vm.candidates.forEach((candidate) => {
                        if (candidate.candidateId === vm.editableCandidate.candidateId) {
                            candidate.phoneNumbers.push(newPhone);
                        }
                    });
                } else {
                    vm.candidate.contacts = vm.candidate.contacts ? vm.candidate.contacts : [];
                    vm.candidate.contacts.push(newPhone);
                }

                vm.defaultPhone = vm.isManyCandidates
                    ? vm.editableCandidate.phoneNumbers[vm.editableCandidate.phoneNumbers.length - 1]
                    : vm.candidate.phoneNumbers[vm.candidate.contacts.length - 1];
                if (!vm.isManyCandidates) {
                    vm.saveCandidateContacts().then(() => {
                        const newPhone = vm.phones.find((phone) => phone.value === vm.defaultPhone.value);
                        vm.defaultPhone = newPhone;
                        vm.phoneModel = newPhone.value;
                        vm.setDefaultPhone('', newPhone);

                        if (isAddPhoneForCandidateOpened) {
                            vm.isAddPhoneForCandidateOpened = false;
                            vm.emitEventOnAddEmailForCandidate();
                        }
                    });
                } else {
                    vm.setDefaultPhone('', vm.defaultPhone);
                    if (isAddPhoneForCandidateOpened) {
                        vm.isAddPhoneForCandidateOpened = false;
                        vm.emitEventOnAddEmailForCandidate();
                    }
                }
            }
        };

        vm.removeError = (event) => {
            if (!vm.candidates || (vm.candidates && vm.candidates.length > 0)) {
                event.currentTarget.classList.remove('error');
                vm.phoneError = false;
            }
        };

        vm.saveEditableCandidate = function () {
            if (vm.isAddPhoneForCandidateOpened && !vm.addedPhone) {
                return;
            }
            if (vm.isAddPhoneForCandidateOpened && !validatePhone(vm.addedPhone)) {
                vm.phoneError = true;
                return;
            } else {
                vm.isAddPhoneForCandidateOpened = false;
                vm.phoneError = false;
            }

            if (vm.editableCandidate) {
                vm.saveCandidateContacts().then(() => {
                    vm.candidates.forEach((candidate) => {
                        if (candidate.candidateId === vm.editableCandidate.candidateId) {
                            candidate.phoneNumbers.forEach((phone) => {
                                phone.default = phone.contactId === vm.defaultPhone?.contactId;
                            });
                        }
                    });

                    vm.cancelEditingCandidate();
                    $scope.$$phase || $scope.$apply();
                });
            } else {
                vm.phoneError = true;
            }
        };

        vm.saveEditingCandidate = function () {
            if (vm.newEmail) {
                if (!validatePhone(vm.newEmail)) {
                    vm.phoneError = true;
                } else {
                    const candidate = vm.candidates.filter(
                        (item) => item.candidateId === vm.editableCandidate.candidateId,
                    )[0];
                    candidate.contacts.push({
                        type: 'email',
                        value: vm.newEmail,
                    });
                    Candidate.add(candidate, (resp) => {
                        if (resp.status === 'ok') {
                            resp.object.contacts.forEach((contact) => {
                                if (contact.value === vm.newEmail) {
                                    candidate.emails = resp.object.contacts.filter((item) => item.type === 'email');
                                    candidate.defaultPhone = vm.getDefaultPhone(candidate);
                                    vm.newEmail = null;
                                    vm.editableCandidate = null;
                                    vm.candidatesWithoutEmails--;
                                }
                            });
                        } else {
                            if (resp.code === 'incorrectEmail') {
                                vm.phoneError = true;
                            }
                        }
                    });
                }
            } else if (vm.editableCandidate && vm.editableCandidate.defaultPhone) {
                const candidate = vm.candidates.filter(
                    (item) => item.candidateId === vm.editableCandidate.candidateId,
                )[0];
                const email = candidate.emails.filter((item) => item.contactId === candidate.defaultPhone.contactId)[0];

                candidate.emails.forEach(
                    (item) => (item.default = !!(item.contactId === candidate.defaultPhone.contactId)),
                );
                candidate.defaultPhone = vm.getDefaultPhone(candidate);
                vm.cancelEditingCandidate();

                Contacts.settingDefaultEmail({
                    contactId: email.contactId,
                    candidateId: candidate.candidateId,
                });
            }
        };

        vm.candidatesMorePhonesCount = (candidate) => {
            if (candidate && candidate.phoneNumbers) {
                return candidate.phoneNumbers.length - 1;
            }
        };

        vm.getDefaultPhone = (candidate) => {
            if (!candidate.phoneNumbers.filter((item) => item.default)[0]) {
                if (candidate.phoneNumbers[0]) candidate.phoneNumbers[0].default = true;
                return candidate.phoneNumbers[0];
            }

            if (candidate && candidate.phoneNumbers) {
                return candidate.phoneNumbers.filter((item) => item.default)[0];
            }
        };

        vm.getDefaultPhoneValue = (candidate) => {
            if (candidate && vm.getDefaultPhone(candidate)) {
                return vm.getDefaultPhone(candidate).value;
            }
            $scope.$$phase || $scope.$apply();
        };

        vm.clearField = function (value) {
            vm[value] = '';
        };

        vm.hideAddPhoneBlock = function () {
            vm.isAddPhoneForCandidateOpened = false;
        };

        vm.addPhoneForCandidate = function ($event) {
            vm.isAddPhoneForCandidateOpened = !vm.isAddPhoneForCandidateOpened;
            if (!vm.isAddPhoneForCandidateOpened) {
                vm.removeError($event);
                vm.clearField('addedPhone');
            }
            if (!vm.isManyCandidates) {
                vm.emitEventOnAddEmailForCandidate();
            }
        };

        vm.emitEventOnAddEmailForCandidate = function () {
            $scope.$emit('isAddPhoneForCandidateOpened', vm.isAddPhoneForCandidateOpened);
        };

        vm.onSelectCandidateForMailing = (value) => {
            const { candidateId, localId } = value;
            vm.candidatesIds.unshift(candidateId);
            vm.candidatesIdsString = vm.candidatesIds.join(',');
            Candidate.one({ localId: localId }, function (resp) {
                if (resp.status === 'ok') {
                    const candidate = resp.object;
                    candidate.phones = getCandidatePhones(candidate);
                    candidate.defaultPhone = vm.getDefaultPhone(candidate);
                    if (candidate.emails.length === 0) {
                        vm.candidatesWithoutEmails++;
                    }
                    vm.filterCandidatesList = vm.filterCandidatesList.filter(
                        (candidate) => candidate.candidateId !== candidateId,
                    );
                    vm.candidates.unshift(candidate);
                    vm.candidateForAddingString = '';
                }
            });
        };

        $rootScope.$on('personalMailingCandidateAutocompleter', (e, localId) => {
            Candidate.one({ localId: localId }, function (resp) {
                if (resp.status === 'ok') {
                    const candidate = resp.object;
                    vm.candidatesIds.unshift(candidate.candidateId);
                    vm.candidatesIdsString = vm.candidatesIds.join(',');
                    candidate.phones = getCandidatePhones(candidate);
                    candidate.defaultPhone = vm.getDefaultPhone(candidate);
                    if (candidate.emails.length === 0) {
                        vm.candidatesWithoutEmails++;
                    }
                    vm.candidates.unshift(candidate);
                    vm.filteredCandidates = [];
                    vm.candidateForAddingString = '';
                }
            });
        });

        $rootScope.$on('Not all recipients have emails', () => {
            vm.showRemoveAllRecipientsWithoutPhones = true;
            vm.candidates.sort((a, b) => {
                return a.phoneNumbers.length - b.phoneNumbers.length;
            });
        });

        function getCandidatePhones(candidate) {
            if (candidate) {
                if (candidate.phoneNumbers) {
                    return candidate.phoneNumbers;
                }
                return [];
            }
        }

        function validatePhone(phone) {
            return /^[+0-9]{6,20}$/.test(phone);
        }
    },
    controllerAs: 'vm',
});
