component.component('candidateMainInfo', {
    templateUrl: 'partials/candidate/candidateMainInfo.html',
    bindings: {
        candidate: '=',
        candidateTasks: '<',
        customStages: '<',
        vacancy: '<',
        fullDataCandidate: '<',
        linkedCandidate: '<',
        tableParamsCandidateHistory: '=',
        history: '=',
        urlTaskId: '=',
        showMore: '=',
        isShowMore: '=',
        objectSize: '=',
        displayShowMore: '=',
        paginationParams: '=',
        importedSkills: '=',
        allSkills: '=',
        lastUpdated: '=',
        customFieldsIsExists: '=',
        vacanciesList: '=',
        vacanciesOfCandidate: '=',
    },
    controller: function (
        $rootScope,
        $scope,
        $filter,
        $translate,
        $state,
        $stateParams,
        $location,
        Vacancy,
        Person,
        Service,
        $timeout,
        $uibModal,
        $anchorScroll,
        notificationService,
        Candidate,
        ngTableParams,
        $interval,
        Action,
        Task,
        $sce,
        $localStorage,
        Mailing,
    ) {
        const vm = this;
        vm.toggleDescription = true;
        vm.historyLimit = 15;
        vm.translateLang;
        vm.showFieldsToUser = {
            info: JSON.parse($localStorage.get('showInfoBlockToUser')) !== false,
            skills: JSON.parse($localStorage.get('showSkillsBlockToUser')) !== false,
            workExperience: JSON.parse($localStorage.get('showWorkExperienceBlockToUser')) !== false,
            education: JSON.parse($localStorage.get('showEducationBlockToUser')) !== false,
            customFields: JSON.parse($localStorage.get('showCustomFieldsBlockToUser')) !== false,
            description: JSON.parse($localStorage.get('showDescriptionBlockToUser')) !== false,
            history: JSON.parse($localStorage.get('showHistoryBlockToUser')) !== false,
        };

        vm.toggleHistorySwitcher = (event) => {
            vm.onlyComments = event.target.checked;
            $rootScope.$$phase || $scope.$apply();
        };

        vm.toogleCandidateFields = function (type) {
            vm.showFieldsToUser[type] = !vm.showFieldsToUser[type];
            switch (type) {
                case 'info':
                    $localStorage.set('showInfoBlockToUser', vm.showFieldsToUser[type]);
                    break;
                case 'skills':
                    $localStorage.set('showSkillsBlockToUser', vm.showFieldsToUser[type]);
                    break;
                case 'workExperience':
                    $localStorage.set('showWorkExperienceBlockToUser', vm.showFieldsToUser[type]);
                    break;
                case 'education':
                    $localStorage.set('showEducationBlockToUser', vm.showFieldsToUser[type]);
                    break;
                case 'customFields':
                    $localStorage.set('showCustomFieldsBlockToUser', vm.showFieldsToUser[type]);
                    break;
                case 'description':
                    $localStorage.set('showDescriptionBlockToUser', vm.showFieldsToUser[type]);
                    break;
                case 'history':
                    $localStorage.set('showHistoryBlockToUser', vm.showFieldsToUser[type]);
                    break;
                default:
                    break;
            }
            $rootScope.$$phase || $scope.$apply();
        };
        vm.customStringValueClassToggle = false;
        vm.todayDate = new Date().getTime();
        vm.onlyComments = true;

        vm.$onInit = function () {
            if ($state.$current.name === 'candidate-slide.tasks' || $state.$current.name === 'candidate.tasks') {
                vm.showTasks = true;
            } else vm.showTasks = false;
            if ($state.current.name === 'candidate-slide.tasks' || $state.current.name === 'candidate.tasks')
                vm.showTasks = true;
            else vm.showTasks = false;
            Task.task($scope, $rootScope, $location, $translate, $uibModal);
        };

        vm.clickInRegionMap = function () {
            if (vm.showRegion2Map) vm.showRegion2Map = false;
            else vm.showRegion2Map = true;
            if (vm.candidate.region) {
                vm.map = {
                    center: {
                        latitude: vm.candidate.region.lat,
                        longitude: vm.candidate.region.lng,
                    },
                    zoom: 14,
                };
                vm.options = { scrollwheel: false };
            }
        };
        vm.removeLinkedinConnect = function (resourseType) {
            vm.resourseType = resourseType;
            angular.forEach(vm.linkedCandidate, function (resp) {
                if (resp.linkId === resourseType) {
                    Candidate.removeCandidateLink(
                        {
                            id: resp.linkId,
                        },
                        function (res) {
                            if (res.status === 'error') {
                                notificationService.error(res.message);
                            } else {
                                updateCandidateLinks();
                                vm.linked = false;
                            }
                        },
                    );
                }
            });
        };
        vm.trustedHtml = function (plainText) {
            return $sce.trustAsHtml(plainText);
        };
        vm.toggleDescriptionFunc = function (param) {
            var elem = $('div.content-description');
            var titleElem = $('.candidate-profile-rezume .centerBar .description #description-title');
            if (vm.toggleDescription || param === 'expand') {
                elem.css({ 'max-height': 'none' });
                elem.toggleClass('showAfter');
                vm.toggleDescription = false;
                titleElem.prop('title', $filter('translate')('Hide full description'));
            } else {
                elem.css({ 'max-height': '100px' });
                elem.toggleClass('showAfter');
                vm.toggleDescription = true;
                titleElem.prop('title', $filter('translate')('Show full description'));
            }
        };
        $scope.closeModal = function () {
            if (vm.modalInstance) {
                vm.modalInstance.close();
                $rootScope.modalInstance.close();
            } else {
                $scope.modalInstance.close();
            }
        };
        $scope.closeModalScope = function () {
            $scope.modalInstance.close();
        };
        vm.showModalAddTaskToCandidate = function (size) {
            $rootScope.ckEditorTaskTextArea = {
                height: 228,
                autoGrow_minHeight: 228,
                extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
                removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
                editorplaceholder: $translate.instant('Task text'),
                on: {
                    change: function (ev) {
                        $timeout(() => {
                            if ($rootScope.editableTask && $rootScope.editableTask.text) {
                                $rootScope.editedTaskText($rootScope.editableTask.text);
                            }
                        }, 0);
                    },
                },
            };
            $rootScope.responsiblePersons = [];
            $rootScope.responsiblePersonsEdit = [];
            $rootScope.newTask.targetDate = '';
            $rootScope.typeOfTasksModel = { value: 'Task' };
            $rootScope.newTask.title = '';
            $rootScope.newTask.text = '';

            angular.forEach($rootScope.persons, function (res) {
                res.notShown = false;
            });
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/adding-task.html',
                size: size,
                scope: $scope,
            });
            $scope.modalInstance.closed.then(function () {
                $rootScope.newTask.title = '';
                $rootScope.newTask.text = '';
                $rootScope.newTask.targetDate = '';
                $rootScope.newTask.responsibleIds = [];
                $rootScope.responsiblePersonsEdit = [];
                $rootScope.newTask.type = 'Task';
            });
        };
        vm.showModalAddCommentToCandidate = function () {
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/add-comment-candidate.html',
                size: '',
                scope: $scope,
                resolve: {},
            });
            vm.modalInstance.closed.then(function () {
                $rootScope.commentCandidate.comment = '';
            });
            $(document)
                .unbind('keydown')
                .keydown(function (e) {
                    if (e.ctrlKey == true && e.which == 13) {
                        $rootScope.addCommentInCandidate();
                        $(document).unbind();
                    }
                });
        };
        vm.getFirstLetters = function (str) {
            return Service.firstLetters(str);
        };
        vm.showAllDescription = function () {
            vm.showMoreButton = vm.customStringValueClassToggle ? 'Show' : 'Hide';
            vm.customStringValueClassToggle = !vm.customStringValueClassToggle;
        };
        vm.getParsedCustomStringField = function (value) {
            if (!value) return;
            return value.replace(/(<([^>]+)>)/gi, '');
        };
        vm.showComments = function (isSwitcher = true) {
            vm.historyLimit = 15;
            vm.onlyWithComment = isSwitcher ? true : vm.onlyWithComment;

            Service.history(
                {
                    vacancyId: vm.vacancy ? vm.vacancy.vacancyId : null,
                    page: { number: 0, count: 15 },
                    candidateId: vm.candidate ? vm.candidate.candidateId : null,
                    onlyWithComment: vm.onlyWithComment,
                    ignoreType: [
                        'interview_add_from_advice_v1',
                        'candidate_email_add',
                        'attached_message',
                        'unattached_message',
                    ],
                },
                function (res) {
                    vm.showHistoryForPrint = true;
                    vm.historyLimit = res.size;
                    vm.historyTotal = res.total;
                    vm.history = res.objects;
                    vm.objectSize = res['objects'] ? res['total'] : 0;
                    vm.onlyComments = true;
                    $('.showCommentSwitcher').prop('checked', !vm.onlyComments);
                },
            );
        };

        $rootScope.addHiringToTags = function (vacancy) {
            vm.vacancyWithShowingComment = vacancy;

            $rootScope.filterRoles = vacancy;
            if (vacancy && vacancy.responsiblesLimitiedRoles !== undefined) {
                vacancy.responsiblesLimitiedRoles.forEach((id) => {
                    $rootScope.persons.forEach((person, index) => {
                        if (person.userId === id) {
                            vm.users.push({
                                id: index + 1,
                                fullname: $rootScope.useAmericanNameStyle ? person.fullNameEn : person.fullName,
                                userId: person.userId,
                            });
                        }
                    });
                });
            }

            $rootScope.$$phase || $scope.$apply();
        };

        $rootScope.removeHiringFromTags = function () {
            if ($rootScope.filterRoles.responsiblesLimitiedRoles !== undefined) {
                $rootScope.filterRoles.responsiblesLimitiedRoles.forEach((id) => {
                    vm.users.forEach((person, index) => {
                        if (person.userId === id) {
                            vm.users.splice(index, 1);
                        }
                    });
                });
            }
        };

        function initUsersInTags() {
            vm.users = [];
            vm.repsonsibleUsers = [];
            $rootScope.persons.forEach((i, index) => {
                if (
                    i.userId === vm.candidate.responsibleId &&
                    i.recrutRole !== 'client' &&
                    i.recrutRole !== 'researcher'
                ) {
                    vm.repsonsibleUsers.push({
                        id: index + 1,
                        fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                        userId: i.userId,
                        responsible: true,
                    });
                }
                if (
                    i.status === 'A' &&
                    i.recrutRole !== 'client' &&
                    i.recrutRole !== 'freelancer' &&
                    i.recrutRole !== 'researcher'
                ) {
                    vm.users.push({
                        id: index + 1,
                        fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                        userId: i.userId,
                        responsible: false,
                    });
                }
            });
            vm.users.sort((a, b) => a.fullname.localeCompare(b.fullname));

            $rootScope.persons = Person.getPersonListWithMe($rootScope.persons);
        }

        function deleteFromTags(id, type) {
            vm.users.forEach((user, index) => {
                if (user[type] == id) {
                    vm.users.splice(index, 1);
                }
            });
            vm.repsonsibleUsers.forEach((user, index) => {
                if (user.userId == id) {
                    vm.repsonsibleUsers.splice(index, 1);
                }
            });
        }

        $rootScope.ckEditorCandidateMain = {
            height: 85,
            width: '100%',
            toolbar: [],
            enterMode: CKEDITOR.ENTER_BR,
            shiftEnterMode: CKEDITOR.ENTER_P,
            autoParagraph: false,
            autoGrow_minHeight: 85,
            autoGrow_maxHeight: 340,
            extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
            removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
            editorplaceholder: $translate.instant('mention placeholder'),
            mentions: [
                {
                    feed: dataCallback,
                    minChars: 0,
                    itemTemplate: '<li class="tag-item" style="padding: 10px" data-id="{id}">{fullname}</li>',
                    outputTemplate: `<a style='text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;' href='#'>@{fullname}</a>&nbsp`,
                    marker: '@',
                    pattern: /@([\p{L}\p{M}]*(?:\s[\p{L}\p{M}]*)?)?$/u,
                },
            ],
            on: {
                paste: function (ev) {
                    ev.data.dataValue = '';
                    CKEDITOR.instances['ckEditorCandidateMain'].insertText(ev.data.dataTransfer._.data.Text);
                    setTimeout(() => {
                        let bodyHeight = ev.editor.document.$.children[0].offsetHeight;
                        if (bodyHeight > ev.editor.config.height) {
                            if (bodyHeight < 605) {
                                ev.editor.resize(null, bodyHeight);
                            }
                        }
                    }, 0);
                },
                afterInsertHtml: function (ev) {
                    if (ev.editor.data) {
                        let userId = ev.editor.data;
                        deleteFromTags(userId.userId, 'userId');
                        $scope.deletedUsers.push(userId);
                    }
                },
                contentDom: function (evt) {
                    let editable = evt.editor.editable();

                    editable.attachListener(editable, 'keyup', function (evt) {
                        if (evt.data.getKeystroke() === 1114125) {
                            $scope.addCommentInCandidate();
                        }
                    });
                },
                instanceReady: function (ev) {
                    $scope.deletedUsers = [];
                    $scope.afterDelete = [];
                    ev.editor.dataProcessor.writer.setRules('p', {
                        indent: false,
                        breakBeforeOpen: false,
                        breakAfterOpen: false,
                        breakBeforeClose: false,
                        breakAfterClose: false,
                    });
                    ev.editor.dataProcessor.writer.setRules('br', {
                        indent: false,
                        breakBeforeOpen: false,
                        breakAfterOpen: false,
                        breakBeforeClose: false,
                        breakAfterClose: false,
                    });
                    initUsersInTags();
                },
            },
        };

        function editUsersInTags(ckeditor) {
            let text = CKEDITOR.instances[ckeditor].getData();

            $scope.deletedUsers.forEach((user, index) => {
                if (!text.includes(`${user.fullname}`)) {
                    if (user.responsible) {
                        vm.repsonsibleUsers.push(user);
                    } else {
                        vm.users.push(user);
                    }
                    $scope.afterDelete.push(user.id);
                    vm.users.sort((a, b) => a.fullname.localeCompare(b.fullname));
                }
            });

            $scope.afterDelete.forEach((id) => {
                $scope.deletedUsers = $scope.deletedUsers.filter((user) => user.id !== id);
            });
            $scope.afterDelete = [];
        }

        function dataCallback(opts, callback) {
            opts.query = opts.query.replace(/\u00A0/g, ' ');
            if (vm.vacancyWithShowingComment === null && $rootScope.filterRoles !== undefined) {
                $rootScope.removeHiringFromTags(vm.vacancyWithShowingComment);
            }

            editUsersInTags('ckEditorCandidateMain');
            if (
                opts.query.length === 0 &&
                vm.repsonsibleUsers.length > 0 &&
                vm.repsonsibleUsers[0].userId !== $rootScope.me.userId
            ) {
                setTimeout(function () {
                    callback(
                        vm.repsonsibleUsers.filter(function (item) {
                            return item.fullname;
                        }),
                    );
                });
            } else {
                setTimeout(function () {
                    callback(
                        vm.users.filter(function (item) {
                            return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                        }),
                    );
                });
            }
        }

        vm.showDetails = function () {
            Service.history(
                {
                    page: { number: 0, count: 15 },
                    candidateId: vm.candidate ? vm.candidate.candidateId : null,
                    onlyWithComment: (vm.onlyWithComment = false),
                    ignoreType: [
                        'interview_add_from_advice_v1',
                        'candidate_email_add',
                        'attached_message',
                        'unattached_message',
                    ],
                },
                function (res) {
                    var keepGoing = true;
                    angular.forEach(vm.history, function (val) {
                        if (keepGoing) {
                            if (
                                val.type === 'vacancy_message' ||
                                val.type === 'candidate_message' ||
                                val.type === 'interview_message' ||
                                val.type === 'client_message'
                            ) {
                                vm.showHistoryForPrint = true;
                                keepGoing = false;
                            }
                        }
                    });
                    vm.historyLimit = res.size;
                    vm.historyTotal = res.total;
                    vm.history = res.objects;
                    vm.objectSize = res['objects'] ? res['total'] : 0;
                    vm.onlyComments = false;
                    $('.showCommentSwitcher').prop('checked', !$scope.onlyComments);
                },
            );
        };

        vm.showCommentsSwitch = function () {
            if (vm.onlyComments) {
                vm.showDetails();
            } else {
                vm.showComments();
            }
        };
        $rootScope.editCommentPopup = function (history) {
            initUsersInTags();
            if (history.type === 'candidate_message_for_hm') addVacancyHiringInTags(history.vacancyId);

            $rootScope.ckEditorCandidateEdit = {
                height: 140,
                autoGrow_maxHeight: 340,
                toolbar: [],
                enterMode: CKEDITOR.ENTER_BR,
                shiftEnterMode: CKEDITOR.ENTER_BR,
                extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
                removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
                editorplaceholder: $translate.instant('mention placeholder'),
                mentions: [
                    {
                        feed: dataCallback,
                        minChars: 0,
                        itemTemplate: '<li style="padding: 10px" data-id="{id}">{fullname}</li>',
                        outputTemplate: `<a href='#' style='text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@{fullname}</a>&nbsp`,
                        marker: '@',
                        pattern: /@([\p{L}\p{M}]*(?:\s[\p{L}\p{M}]*)?)?$/u,
                    },
                ],
                on: {
                    paste: function (ev) {
                        ev.data.dataValue = '';
                        CKEDITOR.instances['ckEditorCandidateEdit'].insertText(ev.data.dataTransfer._.data.Text);
                        setTimeout(() => {
                            let bodyHeight = ev.editor.document.$.children[0].offsetHeight;
                            if (bodyHeight > ev.editor.config.height) {
                                if (bodyHeight < 605) {
                                    ev.editor.resize(null, bodyHeight);
                                }
                            }
                        }, 0);
                    },
                    afterInsertHtml: function (ev) {
                        let user = ev.editor.data;
                        if (user) {
                            deleteFromTags(user.userId, 'userId');
                            $scope.deletedUsers.push(user);
                        }
                    },
                    contentDom: function (evt) {
                        let editable = evt.editor.editable();

                        editable.attachListener(editable, 'keyup', function (evt) {
                            if (evt.data.getKeystroke() === 1114125) {
                                $rootScope.changeComment();
                            }
                        });
                    },
                    instanceReady: function (ev) {
                        $scope.deletedUsers = [];
                        $scope.afterDelete = [];

                        ev.editor.dataProcessor.writer.setRules('p', {
                            indent: false,
                            breakBeforeOpen: false,
                            breakAfterOpen: false,
                            breakBeforeClose: false,
                            breakAfterClose: false,
                        });
                        ev.editor.dataProcessor.writer.setRules('br', {
                            indent: false,
                            breakBeforeOpen: false,
                            breakAfterOpen: false,
                            breakBeforeClose: false,
                            breakAfterClose: false,
                        });
                        $rootScope.convertTagsToSpan(history, 'ckEditorCandidateEdit');
                    },
                    resize: function (event) {
                        $rootScope.adjustCKEditorHeight(event);
                    },
                },
            };

            function dataCallback(opts, callback) {
                opts.query = opts.query.replace(/\u00A0/g, ' ');
                editUsersInTags('ckEditorCandidateEdit');

                if (opts.query.length === 0 && vm.repsonsibleUsers.length > 0) {
                    setTimeout(function () {
                        callback(
                            vm.repsonsibleUsers.filter(function (item) {
                                return item.fullname;
                            }),
                        );
                    });
                } else {
                    setTimeout(function () {
                        callback(
                            vm.users.filter(function (item) {
                                return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                            }),
                        );
                    });
                }
            }

            $rootScope.editCommentText = history.descr;
            $rootScope.oldCommentText = history.descr;
            $rootScope.vacancyWithShowingComment = history.vacancy;
            let actionId = '';
            if (typeof history.actionId === 'object') {
                actionId = history.actionId.actionId;
            } else {
                actionId = history.actionId;
            }
            $rootScope.isCandidateInVacancy = false;
            if (
                history.type === 'interview_message' ||
                history.type === 'interview_edit' ||
                history.type === 'interview_add' ||
                history.type === 'candidate_change_state' ||
                history.type === 'interview_edit_date' ||
                history.type === 'interview_add_sent_email' ||
                history.type === 'interview_edit_sent_email'
            ) {
                $rootScope.isCandidateInVacancy = true;
                $rootScope.typeOfAction = history.type;
            }

            class editCommentController {
                constructor($uibModalInstance, $translate, $scope, $uibModal, notificationService, $rootScope) {
                    this.$uibModalInstance = $uibModalInstance;
                    this.$translate = $translate;
                    this.$scope = $scope;
                    this.notificationService = notificationService;
                    this.$rootScope = $rootScope;
                }

                onVacancyChange = (newValue) => {
                    $rootScope.vacancyWithShowingComment = newValue || null;
                    newValue && $rootScope.addHiringToTags(newValue);
                    $rootScope.$$phase || $scope.$apply();
                };

                auto_grow(element) {
                    element.style.height = '5px';
                    element.style.height = element.scrollHeight + 'px';
                }

                $onInit() {
                    this.options = vm.vacanciesOfCandidate;
                }

                closeModal() {
                    this.$uibModalInstance.close();
                }
            }

            $rootScope.changeComment = function () {
                let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorCandidateEdit.getData());
                initUsersInTags();
                $rootScope.editCommentText = text.replaceAll('<br />', ' &nbsp<br>&nbsp ');
                let vacancyId = null;

                if ($rootScope.isCandidateInVacancy) {
                    vm.changeComment(actionId, $rootScope.editCommentText);
                    return;
                }

                let typeOfMessage =
                    $rootScope.vacancyWithShowingComment && $rootScope.vacancyWithShowingComment.vacancyId
                        ? 'candidate_message_for_hm'
                        : 'candidate_message';
                if ($rootScope.vacancyWithShowingComment && $rootScope.vacancyWithShowingComment.vacancyId)
                    vacancyId = $rootScope.vacancyWithShowingComment.vacancyId;
                $rootScope.loading = true;
                Action.editAction(
                    {
                        comment: $rootScope.editCommentText,
                        actionId: actionId,
                        vacancyId,
                        type: typeOfMessage,
                    },
                    function (resp) {
                        if (resp.status && angular.equals(resp.status, 'error')) {
                            notificationService.error(resp.message);
                            $rootScope.loading = false;
                        } else {
                            if ($rootScope.oldCommentText.trim() !== resp.object.descr.trim()) {
                                notificationService.success($filter('translate')('Comment changed'));
                            }
                            $rootScope.$emit('onUpdateCandidateHistory');
                            $rootScope.modalInstance.close();
                            $rootScope.closeModal();
                            $rootScope.loading = false;
                        }
                    },
                );
            };
            $rootScope.modalInstance = $uibModal.open({
                templateUrl: '../partials/modal/edit-comment-candidate.html',
                backdrop: true,
                controller: [
                    '$translate',
                    '$scope',
                    '$uibModal',
                    'notificationService',
                    '$rootScope',
                    editCommentController,
                ],
                controllerAs: 'vm',
                resolve: {
                    $translate: () => $translate,
                    $uibModal: () => $uibModal,
                    notificationService: () => notificationService,
                    $rootScope: () => $rootScope,
                },
            });

            $rootScope.closeCandidateComment = function () {
                $rootScope.modalInstance.close();
                $rootScope.initTagsStyles(history, true);
            };
        };
        vm.changeCommentFlag = function (history) {
            history.editCommentFlag = !history.editCommentFlag;
            vm.editComment = history.descr;
            history.showAllCandidates = false;
        };
        vm.clickInRegionMap = function () {
            if (vm.showRegion2Map) vm.showRegion2Map = false;
            else vm.showRegion2Map = true;
            if (vm.candidate.region) {
                vm.map = {
                    center: {
                        latitude: vm.candidate.region.lat,
                        longitude: vm.candidate.region.lng,
                    },
                    zoom: 14,
                };
                vm.options = { scrollwheel: false };
            }
        };
        $rootScope.showDeleteComment = function (resp) {
            $rootScope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/delete-comment-candidate.html',
                scope: $scope,
                resolve: {
                    items: function () {
                        return vm.items;
                    },
                },
            });
            $scope.commentRemove = resp;
            $rootScope.commentRemoveId = resp.actionId;
        };

        $rootScope.pinComment = function (history, attach) {
            $rootScope.loading = true;
            Action.onAttach({
                actionId: history.actionId,
                attach,
                candidateId: history.candidateId,
            })
                .then((resp) => {
                    if (resp.status === 'ok') {
                        attach
                            ? notificationService.success($filter('translate')('Comment pinned'))
                            : notificationService.success($filter('translate')('Comment unpinned'));
                        $rootScope.$emit('onUpdateCandidateHistory');
                    }
                })
                .catch((err) => {
                    console.error(err);
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        };
        vm.changeComment = function (action, comment) {
            let actionId;
            if (typeof action === 'object') {
                actionId = action.actionId;
            } else {
                actionId = action;
            }
            Action.editAction(
                {
                    comment: comment,
                    actionId: actionId,
                    type: $rootScope.typeOfAction,
                },
                function (resp) {
                    if (resp.status && angular.equals(resp.status, 'error')) {
                        notificationService.error(resp.message);
                    } else {
                        notificationService.success($filter('translate')('Comment changed'));
                        $rootScope.closeModal();
                        $rootScope.$emit('onUpdateCandidateHistory');
                    }
                },
            );
        };
        vm.updateTasks = function () {
            Task.get(
                {
                    candidateId: vm.candidate.candidateId,
                },
                function (resp) {
                    vm.candidateTasks = resp.objects;
                    $rootScope.candidateTasks = resp.objects;

                    $rootScope.$emit('onEventForShearingCandidateTasks', vm.candidateTasks);
                    if (vm.urlTaskId) {
                        $rootScope.responsiblePersonsEdit = [];
                        angular.forEach(vm.candidateTasks, function (resp) {
                            if (resp.taskId == vm.urlTaskId) {
                                $rootScope.editableTask = resp;
                            }
                        });
                        if ($rootScope.editableTask && $location.$$absUrl.indexOf('&task=') == -1) {
                            $location.$$absUrl = $location.$$absUrl + '&task=' + vm.urlTaskId;
                            angular.forEach($rootScope.editableTask.responsiblesPerson, function (resp) {
                                angular.forEach($rootScope.persons, function (res) {
                                    if (resp.responsible.userId == res.userId) {
                                        $rootScope.responsiblePersonsEdit.push(res);
                                        res.notShown = true;
                                    }
                                });
                            });
                            $('.editTaskInCandidate')
                                .modal('setting', {
                                    onHide: function () {
                                        vm.urlTaskId = null;
                                        $location.$$absUrl = $location.$$absUrl.split('&')[0];
                                    },
                                })
                                .modal('show');
                        }
                    }
                },
            );
        };
        $rootScope.updatedTask = vm.updateTasks;
        $rootScope.deleteComment = () => {
            Action.removeMessageAction(
                {
                    actionId: $rootScope.commentRemoveId,
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        $rootScope.$emit('onUpdateCandidateHistory');
                        notificationService.success($filter('translate')('Comment removed'));

                        if (!vm.onlyComments) {
                            vm.tableParamsCandidateHistory.reload();
                        }
                    } else {
                        console.error(resp);
                    }
                    $rootScope.modalInstance.close();
                },
            );
        };
        $rootScope.commentCandidate = {
            comment: '',
            loading: false,
        };

        $rootScope.toSentPreviewer = function (mailing) {
            Mailing.showSentCompaignById(mailing);
        };

        $scope.addCommentInCandidate = function (out) {
            let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorCandidateMain.getData());
            $rootScope.commentCandidate.comment = text.replaceAll('<br />', ' &nbsp<br>&nbsp ');
            initUsersInTags();
            $scope.deletedUsers = [];

            if ($rootScope.commentCandidate.comment != undefined && $rootScope.commentCandidate.comment.length > 0) {
                let typeOfMessage =
                    vm.vacancyWithShowingComment && vm.vacancyWithShowingComment.vacancyId
                        ? 'candidate_message_for_hm'
                        : 'candidate_message';
                if ($rootScope.me.recrutRole === 'client') {
                    typeOfMessage = 'candidate_message_for_hm';
                    vm.vacancyWithShowingComment = null;
                }
                $rootScope.loading = true;
                Candidate.setMessage(
                    {
                        vacancyId: (vm.vacancyWithShowingComment && vm.vacancyWithShowingComment.vacancyId) || null,
                        type: typeOfMessage,
                        comment: $rootScope.commentCandidate.comment,
                        candidateIds: [vm.candidate.candidateId],
                        original_text: $scope.origText,
                    },
                    function (resp) {
                        if (resp.status == 'ok') {
                            vm.vacancyWithShowingComment = null;
                            $rootScope.commentCandidate.comment = null;
                            $rootScope.$emit('onUpdateCandidateHistory');
                            $rootScope.loading = false;
                            CKEDITOR.instances.ckEditorCandidateMain.setData('');
                        }
                        notificationService.success($filter('translate')('Comment added'));
                    },
                    function (error) {
                        $rootScope.commentCandidate.loading = false;
                        notificationService.error(error.message);
                        $rootScope.loading = false;
                    },
                );
            } else {
                notificationService.error($filter('translate')('enter a comment'));
            }
        };

        // translate lang on candidate profile
        function translateLanguages() {
            let lang = $rootScope.currentLang;

            Service.onGetLanguagesCached().then((languages) => {
                angular.forEach(vm.candidate.languages, function (val) {
                    if (val.name !== undefined) {
                        angular.forEach(languages, function (item) {
                            let lowerCountry = val.name.split(' ')[0].toLowerCase();
                            if (
                                lowerCountry === item.translation.ukrainian ||
                                lowerCountry === item.translation.russian ||
                                lowerCountry === item.translation.polish ||
                                lowerCountry === item.translation.english
                            ) {
                                if (lang === 'ua') {
                                    val.name =
                                        item.translation.ukrainian.split('')[0].toUpperCase() +
                                        item.translation.ukrainian.slice(1);
                                } else if (lang === 'ru') {
                                    val.name =
                                        item.translation.russian.split('')[0].toUpperCase() +
                                        item.translation.russian.slice(1);
                                } else if (lang === 'pl') {
                                    val.name =
                                        item.translation.polish.split('')[0].toUpperCase() +
                                        item.translation.polish.slice(1);
                                } else if (lang === 'en') {
                                    val.name =
                                        item.translation.english.split('')[0].toUpperCase() +
                                        item.translation.english.slice(1);
                                }
                            }
                        });
                    }
                });
            });
        }

        translateLanguages();
        $rootScope.$on('$translateChangeSuccess', () =>
            $timeout(() => {
                translateLanguages();
            }, 0),
        );

        function updateCandidateLinks() {
            Candidate.getCandidateLinks(
                {
                    id: vm.candidate.candidateId,
                },
                function (resp) {
                    vm.linkedCandidate = resp.object;
                    angular.forEach(vm.linkedCandidate, function (res) {
                        vm.linkedOneCandidate = res;
                        if (
                            res.resourseType === 'linkedin' ||
                            res.resourseType === 'superJob' ||
                            res.resourseType === 'workua' ||
                            res.resourseType === 'trelloCardId' ||
                            res.resourseType === 'robotaua' ||
                            res.resourseType === 'recruforce' ||
                            res.resourseType === 'hh' ||
                            res.resourseType === 'zoho_id' ||
                            res.resourseType === 'firebird_id' ||
                            res.resourseType === 'linkedin_d' ||
                            res.resourseType === 'cvlv' ||
                            res.resourseType === 'estaffId'
                        ) {
                            vm.linked = true;
                        } else if (res.resourseType === 'linkedinNew') {
                            res.resourseType = 'linkedinNew' ? 'linkedin' : 'linkedin';
                            vm.linked = true;
                        }
                    });
                },
            );
        }

        function addVacancyHiringInTags(vacancyId) {
            vm.vacanciesOfCandidate.forEach((vacancy) => {
                if (vacancy.vacancyId === vacancyId) $rootScope.addHiringToTags(vacancy);
            });
        }
    },
    controllerAs: 'vm',
});
