class linkedinIntegrationComponentCtrl {
    constructor(
        $uibModal,
        integrationPageService,
        $translate,
        $scope,
        notificationService,
        Vacancy,
        $location,
        $rootScope,
        $state,
        $localStorage,
        LIIntegrationService,
    ) {
        this.$uibModal = $uibModal;
        this.integrationPageService = integrationPageService;
        this.$translate = $translate;
        this.$scope = $scope;
        this.notificationService = notificationService;
        this.vacancyService = Vacancy;
        this.$location = $location;
        this.$rootScope = $rootScope;
        this.$state = $state;
        this.$localStorage = $localStorage;
        this.LIIntegrationService = LIIntegrationService;
        this.doIntegrationLinkedin = this.doIntegrationLinkedin.bind(this);
        this.logoutLinkedin = this.logoutLinkedin.bind(this);
        self = this;
    }
    doIntegrationLinkedin(flag) {
        if (flag) this.integrateLinkedin();
        else this.logoutLinkedin(this);
    }
    integrateLinkedin() {
        this.LIIntegrationService.integrate();
    }
    logoutLinkedin(self) {
        class AuthController {
            constructor(
                parent,
                $uibModalInstance,
                $translate,
                $scope,
                $uibModal,
                notificationService,
                integrationPageService,
                LIIntegrationService,
            ) {
                this.parent = parent;
                this.$uibModalInstance = $uibModalInstance;
                this.$translate = $translate;
                this.$scope = $scope;
                this.$uibModal = $uibModal;
                this.notificationService = notificationService;
                this.integrationPageService = integrationPageService;
                this.LIIntegrationService = LIIntegrationService;
            }

            logout() {
                self.$rootScope.loading = true;
                this.LIIntegrationService.onDeactivateLinkedinIntegration()
                    .then((resp) => {
                        this.notificationService.success(
                            this.$translate.instant('You are logged out of your linkedin account'),
                        );
                    })
                    .catch((err) => this.notificationService.error(err.message))
                    .finally(() => {
                        self.$rootScope.loading = false;
                        this.closeModal();
                    });
            }
            closeModal() {
                this.$uibModalInstance.close();
            }

            $onInit() {
                this.portal = 'Linkedin';
            }
        }
        const modalInstance = this.$uibModal.open({
            templateUrl: 'partials/modal/jobSitesIntegration/remove-integration.html',
            windowClass: 'remove-publish-popup secondary-modal',
            controller: [
                'parent',
                '$uibModalInstance',
                '$translate',
                '$scope',
                '$uibModal',
                'notificationService',
                'integrationPageService',
                'LIIntegrationService',
                AuthController,
            ],
            controllerAs: 'vm',
            resolve: {
                parent: () => {
                    return self.parent;
                },
                $translate: () => {
                    return self.$translate;
                },
                $uibModal: () => {
                    return self.$uibModal;
                },
                notificationService: () => {
                    return self.notificationService;
                },
                integrationPageService: () => {
                    return self.integrationPageService;
                },
                LIIntegrationService: () => {
                    return self.LIIntegrationService;
                },
            },
        });
        modalInstance.closed.then(() => {
            this.integrationPageService
                .checkIntegration()
                .then((resp) => {
                    self.parent.integrationPayload = resp.object;
                })
                .catch((err) => this.notificationService.error(err.message))
                .finally(() => this.$scope.$apply());
        });
    }
}

const linkedinIntegrationComponentDefinition = {
    bindings: {
        integrated: '=',
    },
    require: {
        parent: '^integrationPageComponent',
    },
    templateUrl: 'partials/integration-page/linkedinIntegration.component.html',
    controller: linkedinIntegrationComponentCtrl,
    controllerAs: 'vm',
};

component.component('linkedinIntegrationComponent', linkedinIntegrationComponentDefinition);
