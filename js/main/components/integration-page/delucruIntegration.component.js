const delucruIntegrationCtrlDefinition = {
    bindings: {
        integrated: '=',
    },
    templateUrl: 'partials/integration-page/delucruIntegration.component.html',
    controllerAs: 'vm',
    controller: function ($scope, $rootScope) {
        const delucruBC = new BroadcastChannel('delucru');
        delucruBC.onmessage = (event) => {
            if (event.data === 'integrated') {
                $scope.isIntegrated = true;
            } else if (event.data === 'disconnected') {
                $scope.isIntegrated = false;
            }
            $rootScope.$$phase || $scope.$apply();
        };

        this.$onInit = function () {
            $scope.isIntegrated = this.integrated.integrated;
        };

        this.$onDestroy = function () {
            delucruBC.close();
        };

        $scope.onToggle = () => {
            $scope.isIntegrated = !$scope.isIntegrated;
            $scope.showIntegrationModal = true;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onSuccess = (status) => {
            if (status === 'integrated') {
                $scope.isIntegrated = true;
                delucruBC.postMessage('integrated');
            } else if (status === 'disconnected') {
                $scope.isIntegrated = false;
                delucruBC.postMessage('disconnected');
            }
            $scope.showIntegrationModal = false;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onCloseModal = () => {
            $scope.showIntegrationModal = false;
            $rootScope.$$phase || $scope.$apply();
        };
    },
};

component.component('delucruIntegrationComponent', delucruIntegrationCtrlDefinition);
