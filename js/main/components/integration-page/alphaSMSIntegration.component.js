class alphasmsIntegrationComponentCtrl {
    constructor(
        AlphasmsIntegrationService,
        notificationService,
        hhVacancyPostingService,
        integrationPageService,
        $translate,
        $scope,
        $rootScope,
    ) {
        this.AlphasmsIntegrationService = AlphasmsIntegrationService;
        this.notificationService = notificationService;
        this.integrationPageService = integrationPageService;
        this.$translate = $translate;
        this.$scope = $scope;
        this.$rootScope = $rootScope;
        this.errorFields = {
            alphaName: false,
            alphaAPI: false,
        };
    }
    connectAlphasms() {
        if (!this._validateFields()) {
            this.$rootScope.loading = true;
            this.AlphasmsIntegrationService.integrateAccount({
                alphaName: this.alphaName,
                apiKey: this.alphaAPI,
            })
                .then(() => {
                    this.integrated = true;
                    this.isEditingCredentials = false;
                    this.$rootScope.me.orgParams.alphaSms = 'Y';
                    this.notificationService.success(this.$translate.instant('Integration with AlphaSMS connected'));

                    if (error.code === 'errorConnectingToAlphaSms')
                        this.notificationService.error(
                            this.$translate.instant('API Key and/or Alpha Name are not valid'),
                        );
                })
                .catch((error) => {
                    if (error.code === 'errorConnectingToAlphaSms')
                        this.notificationService.error(
                            this.$translate.instant('API Key and/or Alpha Name are not valid'),
                        );
                    console.error(error.message || error.statusText);
                })
                .finally(() => {
                    this.$rootScope.loading = false;
                    this.$rootScope.$$phase || this.$rootScope.$apply();
                });
        } else {
            this.notificationService.error(this.$translate.instant('All fields are required'));
            this.$rootScope.$$phase || this.$rootScope.$apply();
        }
    }

    disconnectAlphasms() {
        if (!this.integrated) return;
        this.$rootScope.loading = true;
        this.AlphasmsIntegrationService.deactivateIntegration()
            .then(() => {
                this.integrated = false;
                this.alphaName = '';
                this.alphaAPI = '';
                this.$rootScope.me.orgParams.alphaSms = 'N';
                Object.keys(this.errorFields).forEach((fieldName) => {
                    this.errorFields[fieldName] = false;
                });
                this.notificationService.success(
                    this.$translate.instant('Integration with _portal successfully removed', {
                        portal: 'AlphaSMS',
                    }),
                );
            })
            .catch((error) => console.error(error.message || error.statusText))
            .finally(() => {
                this.$rootScope.loading = false;
                this.$rootScope.$$phase || this.$rootScope.$apply();
            });
    }

    // handleChange(fieldName) {
    //     this.errorFields[fieldName] && (this.errorFields[fieldName] = false);
    //     if (this.integrated && !this.isEditingBamboo) this.isEditingCredentials = true;
    // }

    _setAlphasmsCredentials() {
        if (!this.integrated) return;
        this.$rootScope.loading = true;
        this.AlphasmsIntegrationService.checkAlphaSmsIntegration()
            .then((resp) => {
                const { alphaName, apiKey } = resp.object;
                this.alphaName = alphaName;
                this.alphaAPI = apiKey;
            })
            .catch((error) => console.error(error.message || error.statusText))
            .finally(() => {
                this.$rootScope.loading = false;
                this.$rootScope.$$phase || this.$rootScope.$apply();
            });
    }

    _validateFields() {
        this.errorFields.alphaName = !this.alphaName;
        this.errorFields.alphaAPI = !this.alphaAPI;
        return this.errorFields.alphaName || this.errorFields.alphaAPI;
    }

    $onInit() {
        this.isAlphasmsIntegrationBlockOpen = !!this.integrated;
        this.disconnectAlphasms = this.disconnectAlphasms.bind(this);
        this._setAlphasmsCredentials();
    }
}

const alphasmsIntegrationComponentDefinition = {
    bindings: {
        integrated: '<',
    },
    templateUrl: 'partials/integration-page/alphasmsIntegration.component.html',
    controller: alphasmsIntegrationComponentCtrl,
    controllerAs: 'vm',
};

component.component('alphasmsIntegrationComponent', alphasmsIntegrationComponentDefinition);
