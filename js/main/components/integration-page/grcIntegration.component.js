class grcIntegrationComponentCtrl {
    constructor(
        grcVacancyPostingService,
        notificationService,
        integrationPageService,
        $translate,
        $uibModal,
        $scope,
        $localStorage,
    ) {
        this.grcVacancyPostingService = grcVacancyPostingService;
        this.notificationService = notificationService;
        this.integrationPageService = integrationPageService;
        this.$translate = $translate;
        this.$uibModal = $uibModal;
        this.$scope = $scope;
        this.$localStorage = $localStorage;
        this.doIntegrationGRC = this.doIntegrationGRC.bind(this);
        this.logoutHH = this.logoutHH.bind(this);
    }

    integrate() {
        this.grcVacancyPostingService
            .authFromIntegrationPage()
            .catch((err) => this.notificationService.error(err.message));
    }

    logoutHH() {
        class LogOutCtrl {
            constructor(
                $uibModalInstance,
                $translate,
                $scope,
                $uibModal,
                notificationService,
                grcVacancyPostingService,
                integrationPageService,
            ) {
                this.$uibModalInstance = $uibModalInstance;
                this.$translate = $translate;
                this.$scope = $scope;
                this.$uibModal = $uibModal;
                this.notificationService = notificationService;
                this.grcVacancyPostingService = grcVacancyPostingService;
                this.integrationPageService = integrationPageService;
            }

            logout() {
                this.grcVacancyPostingService
                    .logout()
                    .then(() => {
                        this.notificationService.success(
                            this.$translate.instant('Integration with _portal successfully removed', { portal: 'GRC' }),
                        );
                        this.closeModal();
                    })
                    .catch((err) => this.notificationService.error(err.message));
            }
            closeModal() {
                this.$uibModalInstance.close();
            }

            $onInit() {
                this.portal = 'GRC';
            }
        }

        const modalInstance = this.$uibModal.open({
            templateUrl: 'partials/modal/jobSitesIntegration/remove-integration.html',
            windowClass: 'remove-publish-popup secondary-modal',
            controller: [
                '$uibModalInstance',
                '$translate',
                '$scope',
                '$uibModal',
                'notificationService',
                'grcVacancyPostingService',
                'integrationPageService',
                LogOutCtrl,
            ],
            controllerAs: 'vm',
            resolve: {
                $translate: () => {
                    return this.$translate;
                },
                $uibModal: () => {
                    return this.$uibModal;
                },
                notificationService: () => {
                    return this.notificationService;
                },
                grcVacancyPostingService: () => {
                    return this.grcVacancyPostingService;
                },
                integrationPageService: () => {
                    return this.integrationPageService;
                },
            },
        });
        modalInstance.closed.then(() => {
            this.integrationPageService
                .checkIntegration()
                .then((resp) => {
                    this.integrationPayload = resp.object;
                    this.integrated = resp.object.grc;
                    if (!resp.object.grc) this.$localStorage.set('grcIntegrated', 'grcDisabled');
                })
                .catch((err) => this.notificationService.error(err.message))
                .finally(() => this.$scope.$apply());
        });
    }

    doIntegrationGRC(flag) {
        if (flag) this.integrate();
        else this.logoutHH(this);
    }

    $onInit() {
        if (this.integrated) this.$localStorage.set('grcIntegrated', 'grcEnabled');
    }
}

const grcIntegrationComponentDefinition = {
    bindings: {
        integrated: '=',
    },
    templateUrl: 'partials/integration-page/grcIntegration.component.html',
    controller: grcIntegrationComponentCtrl,
    controllerAs: 'vm',
};

component.component('grcIntegrationComponent', grcIntegrationComponentDefinition);
