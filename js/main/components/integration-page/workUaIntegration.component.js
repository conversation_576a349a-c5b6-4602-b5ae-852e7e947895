class workUaIntegrationComponentCtrl {
    constructor(
        $uibModal,
        workUAIntegrationService,
        integrationPageService,
        $translate,
        $scope,
        notificationService,
        Vacancy,
        $location,
        $rootScope,
        $state,
        $localStorage,
    ) {
        this.$uibModal = $uibModal;
        this.workUAIntegrationService = workUAIntegrationService;
        this.integrationPageService = integrationPageService;
        this.$translate = $translate;
        this.$scope = $scope;
        this.notificationService = notificationService;
        this.vacancyService = Vacancy;
        this.$location = $location;
        this.$rootScope = $rootScope;
        this.$state = $state;
        this.$localStorage = $localStorage;
        this.doIntegrationWork = this.doIntegrationWork.bind(this);
        this.logoutWork = this.logoutWork.bind(this);
        self = this;
    }

    integrateWork(self) {
        class AuthController {
            constructor(
                parent,
                $uibModalInstance,
                $translate,
                $scope,
                $uibModal,
                notificationService,
                workUAIntegrationService,
                integrationPageService,
                $location,
                $rootScope,
                $state,
            ) {
                this.parent = parent;
                this.$uibModalInstance = $uibModalInstance;
                this.$translate = $translate;
                this.$scope = $scope;
                this.$uibModal = $uibModal;
                this.notificationService = notificationService;
                this.integrationPageService = integrationPageService;
                this.$location = $location;
                this.$rootScope = $rootScope;
                this.$state = $state;
                this.workUAIntegrationService = workUAIntegrationService;
                this.work_login = '';
                this.work_pass = '';
                self = this;
            }

            onChangeLogin = (value) => {
                this.work_login = value;
                this.$rootScope.$$phase || this.$scope.$apply();
            };
            onChangePass = (value) => {
                this.work_pass = value;
                this.$rootScope.$$phase || this.$scope.$apply();
            };

            authWorkUA() {
                this.workUAIntegrationService.onIntegrateAccount(this.work_login, this.work_pass).then(
                    (resp) => {
                        this.closeModal();
                    },
                    (err) => {
                        this.notificationService.error(err.message);
                        this.closeModal();
                    },
                );
            }
            closeModal() {
                this.$uibModalInstance.close();
            }
        }

        const modalInstance = this.$uibModal.open({
            templateUrl: 'partials/modal/jobSitesIntegration/workUA/authorization-work.html',
            windowClass: 'auth-popup-work-ua',
            controller: [
                'parent',
                '$uibModalInstance',
                '$translate',
                '$scope',
                '$uibModal',
                'notificationService',
                'workUAIntegrationService',
                'integrationPageService',
                '$location',
                '$rootScope',
                '$state',
                AuthController,
            ],
            controllerAs: 'vm',
            backdrop: 'static',
            resolve: {
                parent: () => {
                    return self.parent;
                },
                $translate: () => {
                    return self.$translate;
                },
                $uibModal: () => {
                    return self.$uibModal;
                },
                notificationService: () => {
                    return self.notificationService;
                },
                workUAIntegrationService: () => {
                    return self.workUAIntegrationService;
                },
                integrationPageService: () => {
                    return self.integrationPageService;
                },
                $location: () => {
                    return self.$location;
                },
                $rootScope: () => {
                    return self.$rootScope;
                },
                $state: () => {
                    return self.$state;
                },
            },
        });
        modalInstance.closed.then(() => {
            this.integrationPageService
                .checkIntegration()
                .then((resp) => {
                    self.parent.integrationPayload = resp.object;
                    if (resp.object.workua) this.$localStorage.set('workIntegrated', 'workUAEnabled');
                })
                .catch((err) => this.notificationService.error(err.message))
                .finally(() => this.$scope.$apply());
        });
    }

    logoutWork(self) {
        class AuthController {
            constructor(
                parent,
                $uibModalInstance,
                $translate,
                $scope,
                $uibModal,
                notificationService,
                workUAIntegrationService,
                integrationPageService,
            ) {
                this.parent = parent;
                this.$uibModalInstance = $uibModalInstance;
                this.$translate = $translate;
                this.$scope = $scope;
                this.$uibModal = $uibModal;
                this.notificationService = notificationService;
                this.integrationPageService = integrationPageService;
                this.workUAIntegrationService = workUAIntegrationService;
                self = this;
            }

            logout() {
                this.workUAIntegrationService
                    .onDeactivateIntegration()
                    .then((resp) => {
                        this.closeModal();
                    })
                    .catch((err) => this.notificationService.error(err.message));
            }
            closeModal() {
                this.$uibModalInstance.close();
            }

            $onInit() {
                this.portal = 'Work.ua';
            }
        }

        const modalInstance = this.$uibModal.open({
            templateUrl: 'partials/modal/jobSitesIntegration/remove-integration.html',
            windowClass: 'remove-publish-popup',
            controller: [
                'parent',
                '$uibModalInstance',
                '$translate',
                '$scope',
                '$uibModal',
                'notificationService',
                'workUAIntegrationService',
                'integrationPageService',
                AuthController,
            ],
            controllerAs: 'vm',
            resolve: {
                parent: () => {
                    return self.parent;
                },
                $translate: () => {
                    return self.$translate;
                },
                $uibModal: () => {
                    return self.$uibModal;
                },
                notificationService: () => {
                    return self.notificationService;
                },
                workUAIntegrationService: () => {
                    return self.workUAIntegrationService;
                },
                integrationPageService: () => {
                    return self.integrationPageService;
                },
            },
        });
        modalInstance.closed.then(() => {
            this.integrationPageService
                .checkIntegration()
                .then((resp) => {
                    self.parent.integrationPayload = resp.object;
                    if (!resp.object.workua) this.$localStorage.set('workIntegrated', 'workUADisabled');
                })
                .catch((err) => this.notificationService.error(err.message))
                .finally(() => this.$scope.$apply());
        });
    }

    doIntegrationWork(flag) {
        if (flag) this.integrateWork(this);
        else this.logoutWork(this);
    }

    $onInit() {}
}

const workUaIntegrationComponentDefinition = {
    bindings: {
        integrated: '=',
    },
    require: {
        parent: '^integrationPageComponent',
    },
    templateUrl: 'partials/integration-page/workUaIntegration.component.html',
    controller: workUaIntegrationComponentCtrl,
    controllerAs: 'vm',
};

component.component('workUaIntegrationComponent', workUaIntegrationComponentDefinition);
