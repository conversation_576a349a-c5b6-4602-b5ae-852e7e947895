class newsFeedComponentCtrl {
    constructor(notificationService, $scope, $rootScope, $translate, Achievements, $uibModal, $filter, Service) {
        this.notificationService = notificationService;
        this.$scope = $scope;
        this.$rootScope = $rootScope;
        this.$translate = $translate;
        this.achievementsService = Achievements;
        this.$uibModal = $uibModal;
        this.$filter = $filter;
        this.Service = Service;
    }

    $onInit() {
        this.$rootScope.checkedNewNotifications = true;
        this.checkNewNotifications();
    }

    checkNewNotifications() {
        this.achievementsService.onCheckNewNotifications().then((resp) => {
            this.$rootScope.currentNotificationsCount = resp.object;
            this.$rootScope.newNotificationsCount = resp.object;

            if (resp.object == 0) {
                this.$rootScope.showNewMessages = false;
                this.$rootScope.checkedNewNotifications = false;
                if (document.querySelector('.showNewMessages') !== null) {
                    document.querySelector('.showNewMessages').style.display = 'none';
                }
            } else {
                this.$rootScope.showNewMessages = true;
                this.$rootScope.checkedNewNotifications = true;
                if (document.querySelector('.showNewMessages') !== null) {
                    document.querySelector('.showNewMessages').style.display = 'flex';
                }
            }
        });
        this.$rootScope.$$phase || this.$scope.$apply();
    }

    onOpenModal() {
        class newsFeedModal {
            constructor(
                $uibModalInstance,
                $translate,
                $rootScope,
                $scope,
                Achievements,
                $filter,
                notificationService,
                Service,
                $location,
            ) {
                this.$uibModalInstance = $uibModalInstance;
                this.$translate = $translate;
                this.$rootScope = $rootScope;
                this.$scope = $scope;
                this.achievementsService = Achievements;
                this.$filter = $filter;
                this.notificationService = notificationService;
                this.Service = Service;
                this.$location = $location;
            }

            $onInit() {
                this.initModels();
                this.getNotifications();
                this.checkNewNotifications();
                this.getUpdateScroll = this.getUpdateScroll.bind(this);
                this.loadOldNotifications = this.loadOldNotifications.bind(this);
                this.redirectToOnboarding = this.redirectToOnboarding.bind(this);
                this.checker = true;
                this.$rootScope.setSortDate = true;
                this.oldNotificationsEmpty = false;
            }

            closeModal() {
                this.$uibModalInstance.close();
                document.body.onkeydown = function (e) {};
            }

            initModels() {
                this.isVisibleSortPopup = {};
                this.sortOrder = 'DESC';
                this.notificationScrollPage = 0;
            }

            checkNewNotifications() {
                this.achievementsService.onCheckNewNotifications().then((resp) => {
                    this.$rootScope.currentNotificationsCount = resp.object;
                    this.$rootScope.newNotificationsCount = resp.object;
                    if (resp.object == 0) {
                        this.$rootScope.showNewMessages = false;
                        this.$rootScope.checkedNewNotifications = false;
                        document.querySelector('.showNewMessages').style.display = 'none';
                    } else {
                        this.$rootScope.showNewMessages = true;
                        this.$rootScope.checkedNewNotifications = true;
                        document.querySelector('.showNewMessages').style.display = 'flex';
                    }
                });
                this.$rootScope.$$phase || this.$scope.$apply();
            }

            readAllNewNotifications() {
                this.achievementsService
                    .onReadAllNotifications()
                    .then((resp) => {
                        if (resp.status === 'ok') {
                            document.querySelector('.readAllNotifications').style.display = 'none';
                            this.$rootScope.showNewMessages = false;
                            this.$rootScope.checkedNewNotifications = false;
                        }
                    })
                    .finally(() => {
                        this.$rootScope.$$phase || this.$scope.$apply();
                    });
            }

            _declOfNum(number, words) {
                return words[
                    number % 100 > 4 && number % 100 < 20
                        ? 2
                        : [2, 0, 1, 1, 1, 2][number % 10 < 5 ? Math.abs(number) % 10 : 5]
                ];
            }

            _getTooltipIconForRace(raceData) {
                if (raceData.some((item) => !item.dc)) return;

                let lang = this.$rootScope.currentLang;
                const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                let hour;
                if (lang === 'en') {
                    hour = 'h:mm a';
                } else if (lang === 'ru') {
                    hour = 'H:mm';
                } else if (lang === 'ua' || lang === 'pl') {
                    hour = 'H:mm';
                }

                const awardsPlacesTexts = raceData.map((user, index) => {
                    let userName = this.$rootScope.useAmericanNameStyle ? user.fullNameEn : user.fullName;
                    if (userName === 'Вы') {
                        userName = 'Вас';
                    } else if (userName === 'Ви') {
                        userName = 'вас';
                    }

                    let userAwardsText = '';

                    if (lang === 'pl' || lang === 'en') {
                        userAwardsText = `<li>${userName} ${this.$filter('translate')('For2')} ${
                            user.awardsNum
                        } ${this.$filter('translate')(
                            this._declOfNum(user.awardsNum, ['achievement-1', 'achievement-2', 'achievement-3']),
                        )}</li>`;
                    } else {
                        userAwardsText = `<li>${this.$filter('translate')('For2')} ${user.awardsNum} ${this.$filter(
                            'translate',
                        )(
                            this._declOfNum(user.awardsNum, ['achievement-1', 'achievement-2', 'achievement-3']),
                        )} ${this.$filter('translate')('to')} ${userName}</li>`;
                    }

                    return userAwardsText;
                });

                const asOf = this.$filter('translate')('As of');
                const dayOfWeek = this.$filter('translate')(days[new Date(raceData[0].dc).getDay()]);
                const time = this.$filter('date')(raceData[0].dc, hour).replace('дп', 'AM').replace('пп', 'PM');
                awardsPlacesTexts;

                const tooltipText = `${asOf} ${
                    lang === 'en' ? dayOfWeek : dayOfWeek.toLowerCase()
                } ${time}<br><ul>${awardsPlacesTexts.join('')}</ul> `;

                return `<i
                            class="info-icon info-icon__orange"
                            tooltip-placement="top-right"
                            tooltip-class="tooltip-outer tooltip-awards-hint"
                            uib-tooltip-html="'${tooltipText}'"
                        ></i>`;
            }

            getNotificationTextByType({
                type,
                count,
                descr,
                vacancyName,
                earnedExpPoints,
                crystalsForAchievement,
                userId,
                level,
                state,
                creator,
                groupedInformation,
                person,
                descrMap,
                personMap,
                vacancyMap,
                hideAchievement,
            }) {
                if (!type) return '';
                setTimeout(() => {
                    switch (window.location.hash) {
                        case '#/achievements/awards':
                            this.closeNewsFeedLink('.closeAchivment');
                            break;
                        case '#/achievements/results':
                            this.closeNewsFeedLink('.closeStones');
                            break;
                        case `#/users/${userId}`:
                            this.closeNewsFeedLink('.closeUser');
                            break;
                        default:
                            this.closeNewsFeedLink('.closeAll');
                    }
                    document
                        .querySelectorAll('.redirectToOnboarding')
                        .forEach((onboard) => onboard.addEventListener('click', this.redirectToOnboarding));
                    this.keyboardScroll();
                }, 500);

                const prefix =
                    userId === this.$rootScope.me.userId
                        ? 'news_feed_notifications_solo'
                        : 'news_feed_notifications_team';
                const notificationsWithStones = [
                    'new_crystals_0',
                    'candidate_move',
                    'crystals_record_month',
                    'crystals_record_quarter',
                    'crystals_record_year',
                    'user_progress_race_month',
                    'user_progress_race_quarter',
                    'user_progress_race_year',
                ];
                let users = [];
                let crystalOne = this.$filter('translate')('камень');
                let crystalTwo = this.$filter('translate')('камня');
                let crystalThree = this.$filter('translate')('камней');
                let crystals = [];
                let crystalsCounts = [];
                let crystalLang = [];
                let originalAchivment = descr;
                let vacanciesList = [];
                let vacClosed;
                let achGetYou = this.$filter('translate')('received_the_award');
                let achGetHis = this.$filter('translate')('received_the_award_his');
                let achGetShe = this.$filter('translate')('received_the_award_she');
                let vacancyTranslate = this.$filter('translate')('edited_vacancy_short');
                let closedTranslate = this.$filter('translate')('closed');
                let usId = this.$rootScope.me.userId;
                let raceMontResultUsers = [];
                let userAllRaces = [];
                let vacancyLink;
                let sexDeclination;
                let roleAfterHr = this.$filter('translate')(descr);
                let showAllRace = [];
                let translateYourName = this.$filter('translate')('You_newsfeed');

                if (type === 'your_candidate_move') {
                    descr = [];
                    this.vacancyList = [];
                    for (let personKey in vacancyMap) {
                        Object.entries(descrMap).map(([key, value]) => {
                            value.forEach((stat) => {
                                if (stat === personKey) {
                                    vacanciesList.push({
                                        position: vacancyMap[personKey].position,
                                        vacancyId: vacancyMap[personKey].localId,
                                        key: key,
                                    });
                                }
                            });
                        });
                    }
                    vacanciesList.forEach((vac) => {
                        this.vacancyList.push(
                            `<a class="closeAll" href='#/vacancies/${vac.vacancyId}'>${vac.position},&nbsp</a>`,
                        );
                    });
                    if (vacanciesList.length === 1) {
                        this.decCandidate = this.$filter('translate')('кандидата');
                        this.decWho = this.$filter('translate')('которого');
                        this.decVacancy = this.$filter('translate')('вакансию');
                    } else {
                        this.decCandidate = this.$filter('translate')('кандидатов');
                        this.decWho = this.$filter('translate')('которых');
                        this.decVacancy = this.$filter('translate')('вакансии');
                    }

                    this.pointsDeclination = this.getLettersEnd(earnedExpPoints);
                    if (this.pointsDeclination === 0) {
                        earnedExpPoints = `${earnedExpPoints} ${this.$filter('translate')('очко')}`;
                    } else if (this.pointsDeclination === 1) {
                        earnedExpPoints = `${earnedExpPoints} ${this.$filter('translate')('очка')}`;
                    } else {
                        earnedExpPoints = `${earnedExpPoints} ${this.$filter('translate')('очков')}`;
                    }
                    descr = `<span>${this.decCandidate}${this.$filter('translate')(',')} ${this.decWho} ${this.$filter(
                        'translate',
                    )('добавили на')} ${this.decVacancy}</span>`;
                    if (vacanciesList.length > 4) {
                        this.hideVacancies = this.vacancyList.splice(4, 200);
                        this.hideVacancies = this.hideVacancies.join('');
                        this.hideVacancies = this.hideVacancies.split('');
                        this.hideVacancies.splice(-10, 6);
                        this.hideVacancies = this.hideVacancies.join('');
                        this.vacancyList.splice(4, 200);
                        this.vacancyList.push(
                            `<span class="showOtherUsers">...<div class="showHandler move">${this.hideVacancies}</div></span>`,
                        );
                        vacanciesList = this.vacancyList;
                        vacanciesList = vacanciesList.join('');
                    } else {
                        vacanciesList = this.vacancyList;
                        vacanciesList = vacanciesList.join('');
                        vacanciesList = vacanciesList.split('');
                        vacanciesList.splice(-10, 6, '.');
                        vacanciesList = vacanciesList.join('');
                    }
                }

                if (
                    type === 'user_progress_race_month' ||
                    type === 'user_progress_race_quarter' ||
                    type === 'user_progress_race_year'
                ) {
                    descr = '';
                    this.vacancyRace = [];
                    for (let personKey in personMap) {
                        for (let descrKey in descrMap) {
                            if (descrMap[descrKey].userId === personKey) {
                                raceMontResultUsers.push({
                                    place: descrMap[descrKey].place,
                                    fullName: this.$rootScope.useAmericanNameStyle
                                        ? personMap[personKey].fullNameEn
                                        : personMap[personKey].fullName,
                                    fullNameEn: personMap[personKey].fullNameEn,
                                    userId: personMap[personKey].userId,
                                    crystalCount: descrMap[descrKey].crystallsCount,
                                    awardsNum: descrMap[descrKey].awardsNum,
                                    dc: descrMap[descrKey].dc,
                                });
                            }
                        }
                    }
                    raceMontResultUsers.sort((a, b) => a.place - b.place);

                    if (type === 'user_progress_race_month') {
                        this.currentPeriod = this.$filter('translate')('user_progress_race_month');
                    } else if (type === 'user_progress_race_quarter') {
                        this.currentPeriod = this.$filter('translate')('user_progress_race_quarter');
                    } else {
                        this.currentPeriod = this.$filter('translate')('user_progress_race_year');
                    }

                    raceMontResultUsers.forEach((race) => {
                        this.decCount = this.decStones(this.getLettersEnd(race.crystalCount));
                        race.userId === usId ? (race.fullName = translateYourName) : race.fullName;

                        if (race.userId == undefined) {
                            race.userId === usId
                                ? (this.persons = `<a class="removeTagAStyles">${translateYourName}</a>`)
                                : (this.persons = `<a class="removeTagAStyles">${race.fullName}</a>`);
                        } else {
                            race.userId === usId
                                ? (this.persons = `<a className="closeAll" href="!#/users/${usId}">${translateYourName}</a>`)
                                : (this.persons = `<a className="closeAll" href="!#/users/${race.userId}">${race.fullName}</a>`);
                        }
                        this.vacancyRace += `${race.place}. ${this.persons} - <a className='closeStones' href="#/achievements/results">${race.crystalCount} ${this.decCount}</a>.<br>`;
                    });

                    if (raceMontResultUsers.some((user) => user.awardsNum)) {
                        this.tooltipIcon = this._getTooltipIconForRace(raceMontResultUsers);
                    } else this.tooltipIcon = '';

                    descr = `${this.currentPeriod}:${this.tooltipIcon} <br> ${this.vacancyRace}`;
                }

                if (type === 'candidate_move') {
                    type = `${type}_${this.getLettersEnd(earnedExpPoints)}`;
                }

                if (prefix === 'news_feed_notifications_solo') {
                    if (type === 'level_up') {
                        if (this.$rootScope.me.sex) {
                            type = type + `_${count}_m`;
                        } else {
                            type = type + `_${count}_f`;
                        }
                    }
                }

                if (prefix === 'news_feed_notifications_team') {
                    if (type === 'level_up') {
                        if (person.sex) {
                            type = type + `_${count}_m`;
                        } else {
                            type = type + `_${count}_f`;
                        }
                    }
                    if (type === 'vacancy_opened') {
                        if (person.userId !== undefined) {
                            vacancyLink = `<a href='#/vacancies/${descr}'>${vacancyName}</a>`;
                        } else {
                            vacancyLink = vacancyName;
                        }
                    }
                }

                if (prefix === 'news_feed_notifications_team') {
                    switch (type) {
                        case 'vacancy_opened':
                            person.sex
                                ? (sexDeclination = this.$filter('translate')('Открыл'))
                                : (sexDeclination = this.$filter('translate')('Открыла'));
                            break;
                        case 'level_up':
                            person.sex
                                ? (sexDeclination = this.$filter('translate')('Достиг'))
                                : (sexDeclination = this.$filter('translate')('Достигла'));
                            break;
                        case 'new_achievement':
                            person.sex
                                ? (sexDeclination = this.$filter('translate')('get_male'))
                                : (sexDeclination = this.$filter('translate')('get_female'));
                            break;
                        case 'new_secret_achievement':
                            person.sex
                                ? (sexDeclination = this.$filter('translate')('get_male'))
                                : (sexDeclination = this.$filter('translate')('get_female'));
                            break;
                        case 'new_crystals_0':
                            person.sex
                                ? (sexDeclination = this.$filter('translate')('get_male'))
                                : (sexDeclination = this.$filter('translate')('get_female'));
                            break;
                        case 'crystals_record_month':
                            person.sex
                                ? (sexDeclination = this.$filter('translate')('Установил'))
                                : (sexDeclination = this.$filter('translate')('Установила'));
                            break;
                        case 'crystals_record_quarter':
                            person.sex
                                ? (sexDeclination = this.$filter('translate')('Установил'))
                                : (sexDeclination = this.$filter('translate')('Установила'));
                            break;
                        case 'crystals_record_year':
                            person.sex
                                ? (sexDeclination = this.$filter('translate')('Установил'))
                                : (sexDeclination = this.$filter('translate')('Установила'));
                            break;
                    }
                }

                if (groupedInformation) {
                    groupedInformation.map((el) => {
                        if (el.person) {
                            if (el.person.userId !== undefined) {
                                users.push(
                                    `<a href="!#/users/${el.person.userId}">${
                                        this.$rootScope.useAmericanNameStyle ? el.person.fullNameEn : el.person.fullName
                                    }</a>`,
                                );
                            } else {
                                users.push(
                                    this.$rootScope.useAmericanNameStyle ? el.person.fullNameEn : el.person.fullName,
                                );
                            }
                        } else {
                            users.push(`<a href="!#/users/${usId}">${translateYourName}</a>`);
                        }
                    });

                    groupedInformation.forEach((el) => {
                        userAllRaces.push({
                            type: el.type,
                            count: el.count,
                            descr: el.descr,
                            lang: '',
                        });
                    });

                    for (let i = 0; users.length > i; i++) {
                        if (users[i] == translateYourName) {
                            users[i] = users.splice(0, 1, users[i])[0];
                        }
                    }

                    users = users.join(', ').trim();

                    if (
                        type === 'crystals_record_month' ||
                        type === 'crystals_record_quarter' ||
                        type === 'crystals_record_year'
                    ) {
                        userAllRaces = [];
                        this.hideUsers = [];
                        descr = [];
                        users = [];
                        groupedInformation.forEach((el) => {
                            if (el.person) {
                                userAllRaces.push({
                                    count: el.count,
                                    userId: el.person.userId,
                                    fullName: this.$rootScope.useAmericanNameStyle
                                        ? el.person.fullNameEn
                                        : el.person.fullName,
                                    fullNameEn: el.person.fullNameEn,
                                });
                            } else {
                                userAllRaces.push({
                                    count: el.count,
                                    userId: usId,
                                    fullName: translateYourName,
                                });
                            }
                        });

                        userAllRaces.sort((a, b) => b.count - a.count);
                        if (type === 'crystals_record_month') {
                            descr = this.$filter('translate')('user_progress_race_month');
                        } else if (type === 'crystals_record_quarter') {
                            descr = this.$filter('translate')('user_progress_race_quarter');
                        } else {
                            descr = this.$filter('translate')('user_progress_race_year');
                        }

                        this.stones = this.decStones(this.getLettersEnd(userAllRaces.count));
                        userAllRaces.forEach((user) => {
                            if (user.userId === usId) {
                                user.fullName = translateYourName;
                            }
                            if (user.userId == undefined) {
                                this.hideUsers.push(
                                    `<span class="flexStones"><a class="removeTagAStyles">${user.fullName}</a>&nbsp-&nbsp<a class="closeStones" href="#/achievements/results">${user.count} ${this.stones},&nbsp</a></span>`,
                                );
                                users.push(
                                    `<span><a class="removeTagAStyles">${user.fullName}</a> - <a class="closeStones" href="#/achievements/results">${user.count} ${this.stones}</a></span>, `,
                                );
                            } else {
                                this.hideUsers.push(
                                    `<span class="flexStones"><a class="closeStones" href="!#/users/${user.userId}">${user.fullName}</a>&nbsp-&nbsp<a class="closeStones" href="#/achievements/results">${user.count} ${this.stones},&nbsp</a></span>`,
                                );
                                users.push(
                                    `<span><a class="closeStones" href="!#/users/${user.userId}">${user.fullName}</a> - <a class="closeStones" href="#/achievements/results">${user.count} ${this.stones}</a></span>, `,
                                );
                            }
                        });
                        if (users.length > 4) {
                            this.hideUsers = this.hideUsers.splice(4, 200);
                            this.hideUsers = this.hideUsers.join('');
                            this.hideUsers = this.hideUsers.split('');
                            this.hideUsers.splice(-17, 6);
                            this.hideUsers = this.hideUsers.join('');
                            users.splice(4, 200);
                            users.push(
                                `<span class="showOtherUsers">...<div class="showHandler crystall-record">${this.hideUsers}</div></span>`,
                            );
                            users = users.join('');
                        } else {
                            users = users.join('');
                            users = users.split('');
                            users.splice(-2, 2, '.');
                            users = users.join('');
                        }
                    }

                    if (type === 'user_progress_race') {
                        if (person) {
                            users = `<a class="closeAll" href="!#/users/${person.userId}">${
                                this.$rootScope.useAmericanNameStyle ? person.fullNameEn : person.fullName
                            }</a>`;
                        } else {
                            users = `<a class="closeAll" href="!#/users/${usId}">${translateYourName}</a>`;
                        }
                        userAllRaces.reverse();
                        userAllRaces.forEach((i) => {
                            i.lang = `${this.getLettersEnd(i.count)}`;
                            if (i.lang == '0') {
                                i.lang = `${crystalOne}`;
                            } else if (i.lang == '1') {
                                i.lang = `${crystalTwo}`;
                            } else {
                                i.lang = `${crystalThree}`;
                            }
                            showAllRace.push(
                                `<span>${this.$filter('translate')('onempty')} ${i.descr} ${this.$filter('translate')(
                                    'place in the race of the',
                                )} ${this.$filter('translate')(
                                    i.type,
                                )}</span> - <a class="closeStones" href="#/achievements/results">${i.count}${
                                    i.lang
                                }</a>`,
                            );
                        });
                    }

                    showAllRace = showAllRace.join(` ${this.$filter('translate')('and')} `).trim();
                    if (userAllRaces.length === 3) {
                        showAllRace = showAllRace.replace(` ${this.$filter('translate')('and')} `, ', ');
                    }

                    if (type === 'new_achievement') {
                        users = [];
                        groupedInformation.sort((a, b) => {
                            if (a.person === undefined) {
                                return -1;
                            }
                        });

                        groupedInformation.map((el) => {
                            if (el.person) {
                                if (el.person.userId !== undefined) {
                                    users.push(
                                        this.$rootScope.useAmericanNameStyle
                                            ? `<a class="closeStones" href="!#/users/${el.person.userId}">${el.person.fullNameEn},&nbsp</a>`
                                            : `<a class="closeStones" href="!#/users/${el.person.userId}">${el.person.fullName},&nbsp</a>`,
                                    );
                                } else {
                                    users.push(`<a class="removeTagAStyles">${el.person.fullName},&nbsp</a>`);
                                }
                            } else {
                                users.push(
                                    `<a class="closeStones" href="!#/users/${usId}">${translateYourName},&nbsp</a>`,
                                );
                            }
                        });
                        originalAchivment = descr;
                        if (users.length > 4) {
                            this.hideUsers = users.splice(4, 200);
                            this.hideUsers = this.hideUsers.join('');
                            this.hideUsers = this.hideUsers.split(',');
                            this.hideUsers.splice(-1, 6);
                            users.splice(4, 200);
                            users.push(
                                `<span class="showOtherUsers">...<div class="showHandler">${this.hideUsers}</div></span>`,
                            );
                            users = users.join('');
                        } else {
                            users = users.join('');
                            users = users.split('');
                            users.splice(-10, 6);
                            users = users.join('');
                        }

                        type = type + '_group';
                        descr = this.$filter('translate')('achieve_title.' + groupedInformation[0].descr);
                    }

                    if (type === 'new_secret_achievement') {
                        if (
                            this.$rootScope.me.recrutRole === 'recruter' ||
                            this.$rootScope.me.recrutRole === 'freelancer'
                        ) {
                            if (groupedInformation.length === 1) {
                                if (groupedInformation[0].person.sex === false) {
                                    originalAchivment = descr;
                                    type = type + '_solo_f';
                                    descr = this.$filter('translate')('achieve_title.' + groupedInformation[0].descr);
                                } else {
                                    originalAchivment = descr;
                                    type = type + '_solo';
                                    descr = this.$filter('translate')('achieve_title.' + groupedInformation[0].descr);
                                }
                            }
                        } else {
                            originalAchivment = descr;
                            type = type + '_group';
                            descr = this.$filter('translate')('achieve_title.' + groupedInformation[0].descr);
                        }
                    }
                }
                // ПРОВЕРКА НА 3 ТИПА НОТИСОВ, КОМЕНТ В ПРОВЕРКЕ НА 'race_month_result' ПОДХОДЯТ К 3 ПРОВЕРКАМ
                else if (personMap && descrMap && type !== 'vacancy_closed') {
                    // Проверка на нотис, который приходит
                    if (type === 'race_month_result') {
                        // Обнуление переменных
                        crystals = [];
                        crystalsCounts = [];
                        crystalLang = [];
                        raceMontResultUsers = [];
                        users = [];

                        // Формирование обьекта с 2 массивов
                        for (let personKey in personMap) {
                            for (let descrKey in descrMap) {
                                if (descrMap[descrKey].userId === personKey) {
                                    raceMontResultUsers.push({
                                        fullName: personMap[personKey].fullName,
                                        fullNameEn: personMap[personKey].fullNameEn,
                                        userId: descrMap[descrKey].userId,
                                        crystallsCount: descrMap[descrKey].crystallsCount,
                                        awardsNum: descrMap[descrKey].awardsNum,
                                        dc: descrMap[descrKey].dc,
                                        admin: personMap[personKey].userId === undefined ? false : true,
                                    });
                                }
                            }
                        }
                        raceMontResultUsers.sort((a, b) => b.crystallsCount - a.crystallsCount);
                        // Проверка на транслейт слова "Вас"
                        angular.forEach(raceMontResultUsers, function (val) {
                            if (val.userId === usId) {
                                val.fullName = translateYourName;
                            }
                        });

                        // Добавление в формат для транслейта
                        raceMontResultUsers.map((val) => {
                            if (val.admin) {
                                users.push(
                                    `<a class="closeAll" href="!#/users/${val.userId}">${
                                        this.$rootScope.useAmericanNameStyle ? val.fullNameEn : val.fullName
                                    }</a>`,
                                );
                            } else {
                                users.push(
                                    `<a class="removeTagAStyles">${
                                        this.$rootScope.useAmericanNameStyle ? val.fullNameEn : val.fullName
                                    }</a>`,
                                );
                            }
                            crystalsCounts.push(`${this.getLettersEnd(val.crystallsCount)}`);
                            crystals.push(`<span>${val.crystallsCount}</span>`);
                        });

                        // Проверка и добавление склонения
                        crystalsCounts.map((el) => {
                            if (el == '0') {
                                crystalLang.push(`<span>${crystalOne}</span>`);
                            } else if (el == '1') {
                                crystalLang.push(`<span>${crystalTwo}</span>`);
                            } else {
                                crystalLang.push(`<span>${crystalThree}</span>`);
                            }
                        });
                    }
                    if (type === 'race_quarter_result') {
                        crystals = [];
                        crystalsCounts = [];
                        crystalLang = [];
                        raceMontResultUsers = [];
                        users = [];

                        for (let personKey in personMap) {
                            for (let descrKey in descrMap) {
                                if (descrMap[descrKey].userId === personKey) {
                                    raceMontResultUsers.push({
                                        fullName: personMap[personKey].fullName,
                                        fullNameEn: personMap[personKey].fullNameEn,
                                        userId: descrMap[descrKey].userId,
                                        crystallsCount: descrMap[descrKey].crystallsCount,
                                        awardsNum: descrMap[descrKey].awardsNum,
                                        dc: descrMap[descrKey].dc,
                                        admin: personMap[personKey].userId === undefined ? false : true,
                                    });
                                }
                            }
                        }

                        raceMontResultUsers.sort((a, b) => b.crystallsCount - a.crystallsCount);
                        angular.forEach(raceMontResultUsers, function (val) {
                            if (val.userId === usId) {
                                val.fullName = translateYourName;
                            }
                        });

                        raceMontResultUsers.map((val) => {
                            if (val.admin) {
                                users.push(
                                    `<a class="closeAll" href="!#/users/${val.userId}">${
                                        this.$rootScope.useAmericanNameStyle ? val.fullNameEn : val.fullName
                                    }</a>`,
                                );
                            } else {
                                users.push(
                                    `<a class="removeTagAStyles">${
                                        this.$rootScope.useAmericanNameStyle ? val.fullNameEn : val.fullName
                                    }</a>`,
                                );
                            }
                            crystalsCounts.push(`${this.getLettersEnd(val.crystallsCount)}`);
                            crystals.push(`<span>${val.crystallsCount}</span>`);
                        });

                        crystalsCounts.map((el) => {
                            if (el == '0') {
                                crystalLang.push(`<span>${crystalOne}</span>`);
                            } else if (el == '1') {
                                crystalLang.push(`<span>${crystalTwo}</span>`);
                            } else {
                                crystalLang.push(`<span>${crystalThree}</span>`);
                            }
                        });
                    }
                    if (type === 'race_year_result') {
                        crystals = [];
                        crystalsCounts = [];
                        crystalLang = [];
                        raceMontResultUsers = [];
                        users = [];

                        for (let personKey in personMap) {
                            for (let descrKey in descrMap) {
                                if (descrMap[descrKey].userId === personKey) {
                                    raceMontResultUsers.push({
                                        fullName: personMap[personKey].fullName,
                                        fullNameEn: personMap[personKey].fullNameEn,
                                        userId: descrMap[descrKey].userId,
                                        crystallsCount: descrMap[descrKey].crystallsCount,
                                        awardsNum: descrMap[descrKey].awardsNum,
                                        dc: descrMap[descrKey].dc,
                                        admin: personMap[personKey].userId === undefined ? false : true,
                                    });
                                }
                            }
                        }

                        raceMontResultUsers.sort((a, b) => b.crystallsCount - a.crystallsCount);
                        angular.forEach(raceMontResultUsers, function (val) {
                            if (val.userId === usId) {
                                val.fullName = translateYourName;
                            }
                        });

                        raceMontResultUsers.map((val) => {
                            if (val.admin) {
                                users.push(
                                    `<a class="closeAll" href="!#/users/${val.userId}">${
                                        this.$rootScope.useAmericanNameStyle ? val.fullNameEn : val.fullName
                                    }</a>`,
                                );
                            } else {
                                users.push(`<a class="removeTagAStyles">${val.fullName}</a>`);
                            }
                            crystalsCounts.push(`${this.getLettersEnd(val.crystallsCount)}`);
                            crystals.push(`<span>${val.crystallsCount}</span>`);
                        });

                        crystalsCounts.map((el) => {
                            if (el == '0') {
                                crystalLang.push(`<span>${crystalOne}</span>`);
                            } else if (el == '1') {
                                crystalLang.push(`<span>${crystalTwo}</span>`);
                            } else {
                                crystalLang.push(`<span>${crystalThree}</span>`);
                            }
                        });
                    }
                } // ПРОВЕРКА НА НОВУЮ ПРОВЕРКУ НА ЗАКРЫТИЕ ВАКАНСИИ
                else if (personMap && descrMap && type === 'vacancy_closed') {
                    crystals = [];
                    crystalsCounts = [];
                    crystalLang = [];
                    raceMontResultUsers = [];
                    users = [];
                    this.forHideUsers = [];

                    // Формирование обьекта с 2 массивов
                    for (var keyPersMap in personMap) {
                        Object.entries(descrMap).map(([key, value]) => {
                            value.forEach((stat) => {
                                if (stat === keyPersMap) {
                                    raceMontResultUsers.push({
                                        fullName: personMap[keyPersMap].fullName,
                                        fullNameEn: personMap[keyPersMap].fullNameEn,
                                        crystallsCount: key,
                                        userId: personMap[keyPersMap].userId,
                                        sex: personMap[keyPersMap].sex,
                                    });
                                }
                            });
                        });
                    }
                    raceMontResultUsers.sort((a, b) => {
                        let sorting = b.crystallsCount - a.crystallsCount;
                        if (a.userId === usId && a.crystallsCount === b.crystallsCount) {
                            sorting = -1;
                        }
                        return sorting;
                    });

                    // Проверка на транслейт слова "Вас"
                    angular.forEach(raceMontResultUsers, function (val) {
                        if (val.userId === usId) {
                            val.fullName = translateYourName;
                        }
                    });

                    // Добавление в формат для транслейта
                    raceMontResultUsers.map((val) => {
                        if (val.userId) {
                            users.push(
                                `<a class="closeAll" href="!#/users/${val.userId}">${
                                    this.$rootScope.useAmericanNameStyle ? val.fullNameEn : val.fullName
                                }</a>`,
                            );
                        } else {
                            users.push(
                                `<span>${this.$rootScope.useAmericanNameStyle ? val.fullNameEn : val.fullName}</span>`,
                            );
                        }
                        crystalsCounts.push(`${this.getLettersEnd(val.crystallsCount)}`);
                        crystals.push(`<span>${val.crystallsCount}</span>`);
                    });

                    // Проверка и добавление склонения
                    crystalsCounts.map((el) => {
                        if (el == '0') {
                            crystalLang.push(`<span>${crystalOne}</span>`);
                        } else if (el == '1') {
                            crystalLang.push(`<span>${crystalTwo}</span>`);
                        } else {
                            crystalLang.push(`<span>${crystalThree}</span>`);
                        }
                    });

                    // Транслейт ЗАКРЫТИЕ ВАКАНСИИ
                    if (raceMontResultUsers.length === 0) {
                        if (person) {
                            vacClosed = `${vacancyTranslate} ${
                                person.userId !== undefined
                                    ? `<a class="closeAll" href='#/vacancies/${descr}'>${vacancyName}</a>`
                                    : vacancyName
                            } ${closedTranslate}!</br>`;
                        } else {
                            vacClosed = `${vacancyTranslate} <a class="closeAll" href='#/vacancies/${descr}'>${vacancyName}</a> ${closedTranslate}!</br>`;
                        }
                    } else {
                        vacClosed = `${vacancyTranslate} ${
                            raceMontResultUsers[0].userId !== undefined
                                ? `<a class="closeAll" href='#/vacancies/${descr}'>${vacancyName}</a>`
                                : vacancyName
                        } ${closedTranslate}!</br>`;
                    }
                    if (raceMontResultUsers.length === 1 && raceMontResultUsers[0].userId === usId) {
                        vacClosed += `${this.$filter('translate')(
                            'Received_job_solo',
                        )} - <a class='closeStones' href="#/achievements/results">${
                            raceMontResultUsers[0].crystallsCount
                        } ${this.decStones(this.getLettersEnd(raceMontResultUsers[0].crystallsCount))}</a>.`;
                    } else {
                        if (users.length === 1) {
                            if (users[0].slice(-6, -4) === translateYourName) {
                                vacClosed += `<span>${achGetYou}</span> `;
                                vacClosed += `<span>${users[0]} - <a class='closeStones' href="#/achievements/results">${crystals[0]} ${crystalLang[0]}</a></span>, `;
                            } else {
                                vacClosed += `<span>${raceMontResultUsers[0].sex ? achGetHis : achGetShe}</span> `;
                                vacClosed += `<span>${users[0]} - <a class='closeStones' href="#/achievements/results">${crystals[0]} ${crystalLang[0]}</a></span>, `;
                            }
                        } else if (users.length <= 0) {
                        } else {
                            vacClosed += `<span>${achGetYou}</span> `;
                            for (let i = 0; i < users.length; i++) {
                                this.forHideUsers.push(
                                    `${users[i]}&nbsp-&nbsp<a class='closeStones' href="#/achievements/results">${crystals[i]} ${crystalLang[i]},&nbsp</a>`,
                                );
                                vacClosed += `<span>${users[i]} - <a class='closeStones' href="#/achievements/results">${crystals[i]} ${crystalLang[i]}</a></span>, `;
                            }
                        }
                        if (users.length > 4) {
                            vacClosed = vacClosed.split(',');
                            vacClosed.splice(4, 50);
                            this.forHideUsers = this.forHideUsers.splice(4, 50);
                            this.forHideUsers = this.forHideUsers.join('');
                            this.forHideUsers = this.forHideUsers.split('');
                            this.forHideUsers.splice(-10, 6);
                            this.forHideUsers = this.forHideUsers.join('');
                            users = users.join(',');
                            vacClosed.push(
                                `<span class="showOtherUsers"> ...<div class="showHandler vacancy">${this.forHideUsers}</div></span>`,
                            );
                            vacClosed = vacClosed.join(',');
                        } else {
                            vacClosed = vacClosed.split('');
                            vacClosed.splice(-2, 1, '.');
                            vacClosed = vacClosed.join('');
                        }
                    }
                } else {
                    if (notificationsWithStones.includes(type)) {
                        type = `${type}_${this.getLettersEnd(count)}`;
                    }

                    if (type === 'new_achievement') {
                        descr = this.$filter('translate')('achieve_title.' + descr);
                        crystalsForAchievement = crystalsForAchievement;
                    }

                    if (type === 'new_secret_achievement') {
                        descr = this.$filter('translate')('secret_achieve_title.' + descr);
                        crystalsForAchievement = crystalsForAchievement;
                    }
                }

                if (raceMontResultUsers.some((user) => user.awardsNum)) {
                    this.tooltipIcon = this._getTooltipIconForRace(raceMontResultUsers);
                } else this.tooltipIcon = '';

                return this.$filter('translate')(`${prefix}.${type}`, {
                    type,
                    hintIcon: this.tooltipIcon,
                    count,
                    descr,
                    originalAchivment,
                    vacancyName,
                    earnedExpPoints,
                    crystalsForAchievement,
                    level,
                    state,
                    creator,
                    groupedInformation,
                    showAllRace,
                    person,
                    users,
                    crystals,
                    crystalLang,
                    vacClosed,
                    descrMap,
                    roleAfterHr,
                    hideAchievement,
                    sexDeclination,
                    vacancyLink,
                    vacanciesList,
                });
            }
            declOfNum(number, words) {
                return words[
                    number % 100 > 4 && number % 100 < 20
                        ? 2
                        : [2, 0, 1, 1, 1, 2][number % 10 < 5 ? Math.abs(number) % 10 : 5]
                ];
            }

            getLettersEndForCandidates(count) {
                if (count > 1) {
                    return 1;
                }
                return 0;
            }

            transformDate(date) {
                let dateObj = new Date(date);
                let month = `${dateObj.getMonth() + 1}`.padStart(2, '0'); //months from 1-12
                let day = `${dateObj.getDate()}`.padStart(2, '0');
                let year = dateObj.getFullYear();

                return `${day}.${month}.${year}`;
            }

            getNotificationTime(date) {
                // get time value for each notification;
                return `${new Date(date).getHours()}:${this.formatDuring(date)}`;
            }

            decStones(count) {
                if (count === 0) {
                    return this.$filter('translate')('камень');
                } else if (count === 1) {
                    return this.$filter('translate')('камня');
                } else {
                    return this.$filter('translate')('камней');
                }
            }

            formatDuring(time) {
                let minutes = parseInt((time % (1000 * 60 * 60)) / (1000 * 60));

                if (minutes < 10) {
                    minutes = '0' + minutes;
                }

                return minutes;
            }

            onTransformNotificationsByDay(notifications) {
                return notifications.reduce((prev, cur) => {
                    const day = this.transformDate(cur.dc);

                    cur.time = this.getNotificationTime(cur.dc);
                    cur.message = this.getNotificationTextByType(cur);

                    prev[day] = prev[day] ? [...prev[day], cur] : [cur];

                    return prev;
                }, {});
            }

            closeNewsFeedLink(classLink) {
                document.querySelectorAll(classLink).forEach((item) =>
                    item.addEventListener('click', () => {
                        this.closeModal();
                    }),
                );
            }

            keyboardScroll() {
                let wrapper = document.querySelector('#news-feed-modal-content');
                document.body.onkeydown = (e) => {
                    if (e.key === 'ArrowDown') {
                        if (
                            wrapper.scrollHeight === wrapper.offsetHeight &&
                            document.querySelector('#news-feed-modal-content') !== null
                        ) {
                            this.notificationScrollPage++;
                            this.getNotifications();
                        }
                        wrapper.scroll(0, wrapper.scrollTop + 100);
                    } else if (e.key === 'ArrowUp') {
                        wrapper.scroll(0, wrapper.scrollTop - 100);
                    }
                    if (document.querySelector('#news-feed-modal-content') === null) {
                        document.body.onkeydown = function (e) {};
                    }
                };
            }

            loadOldNotifications() {
                const wrapper = document.querySelector('#news-feed-modal-content');
                const scrollHeight = Math.max(wrapper.scrollHeight, wrapper.offsetHeight, wrapper.clientHeight);
                const is_safari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
                let showHandlerWrapper = document.querySelectorAll('.showHandler');
                if (showHandlerWrapper.length > 0) {
                    showHandlerWrapper.forEach((el) => {
                        if (
                            el.offsetParent.offsetTop - wrapper.offsetHeight <= wrapper.scrollTop &&
                            wrapper.scrollTop < el.offsetParent.offsetTop - wrapper.offsetHeight + el.clientHeight
                        ) {
                            el.style.top = `-${el.clientHeight * 7}%`;
                        } else {
                            el.style.top = '35%';
                        }
                    });
                }

                if (this.$rootScope.setSortDate) {
                    if (wrapper.scrollTop + 50 >= scrollHeight - wrapper.offsetHeight) {
                        if (this.checker) {
                            this.notificationScrollPage++;
                            this.getNotifications();
                            this.checker = false;
                        }

                        wrapper.removeEventListener('mousewheel', this.loadOldNotifications);
                        if (is_safari) {
                            setTimeout(() => {
                                wrapper.addEventListener('scroll', this.loadOldNotifications);
                            }, 1000);
                        }
                    }
                }
                this.$rootScope.$$phase || this.$scope.$apply();
            }

            getUpdateScroll() {
                const wrapper = document.querySelector('#news-feed-modal-content');

                if (wrapper.scrollHeight === wrapper.offsetHeight) {
                    wrapper.addEventListener('mousewheel', this.loadOldNotifications);
                    this.checker = true;
                } else {
                    wrapper.addEventListener('scroll', this.loadOldNotifications);
                    this.checker = true;
                }
            }

            getNotifications() {
                this.$rootScope.loading = true;

                const payload = {
                    page: { number: this.notificationScrollPage },
                    sortOrder: this.sortOrder,
                };

                if (!this.oldNotificationsEmpty) {
                    this.achievementsService
                        .onGetNotifications(payload)
                        .then((resp) => {
                            if (resp.objects.length === 0) {
                                this.oldNotificationsEmpty = true;
                            }
                            const allNotifications =
                                this.notifications && this.notificationScrollPage !== 0
                                    ? [...Object.values(this.notifications).flat(1), ...resp.objects]
                                    : resp.objects;
                            this.notificationsCounts = resp.objects.length;
                            this.notifications = this.onTransformNotificationsByDay(allNotifications);
                        })
                        .catch((error) => console.error(error))
                        .finally(() => {
                            this.$rootScope.loading = false;
                            this.checker = true;
                            this.$rootScope.$$phase || this.$scope.$apply();
                        });
                } else {
                    this.$rootScope.loading = false;
                }
            }

            onSetSortPopupVisibility(flag, index) {
                if (flag) this.onCloseAllSortPopup();
                this.isVisibleSortPopup[index] = flag;
            }

            redirectToOnboarding() {
                window.location.replace('!#/onboarding');
            }

            onCloseAllSortPopup() {
                Object.keys(this.isVisibleSortPopup).forEach((index) => (this.isVisibleSortPopup[index] = false));
            }

            openDatePicker() {
                const datePicker = document.querySelector(
                    '#news-feed-modal-date-picker .md-icon-button.md-button.md-ink-ripple',
                );
                setTimeout(() => datePicker.click(), 0);
                this.$rootScope.setSortDate = true;
            }

            getLettersEnd(number) {
                let n = Math.abs(number) % 100,
                    n1 = n % 10,
                    form = 2;

                if (n >= 10 && n < 20) form = 2;
                if (n1 > 1 && n1 < 5) form = 1;
                if (n1 === 1) {
                    if (number > 1 && this.$rootScope.currentLang === 'en') {
                        form = 2;
                    } else {
                        form = 0;
                    }
                }
                if (n >= 10 && n < 19 && n1 > 0 && n1 < 5) form = 2;

                if (n > 19 && n1 > 0 && n1 === 1) form = 0;

                if (n > 19 && n1 > 1 && n1 < 5) form = 1;

                if (n > 19 && n1 > 5 && n1 <= 9) form = 2;

                if (n > 19 && n1 === 0) form = 2;

                return form;
            }

            onSearchByDate(date) {
                this.$rootScope.loading = true;
                const searchDate = Date.parse(date);
                this.achievementsService
                    .onGetNotificationsByDate({ dateFrom: searchDate })
                    .then((resp) => {
                        if (resp.objects && resp.objects.length) {
                            this.notifications = this.onTransformNotificationsByDay(resp.objects);
                            this.$rootScope.setSortDate = false;
                        } else {
                            this.notificationService.success(
                                this.$filter('translate')('There are no results for your search'),
                            );
                        }
                    })
                    .finally(() => {
                        this.$rootScope.loading = false;
                        this.$rootScope.$$phase || this.$scope.$apply();
                    });
            }
        }

        const modalInstance = this.$uibModal.open({
            templateUrl: 'partials/modal/gamification/news-feed-modal.html',
            controller: [
                '$uibModalInstance',
                '$translate',
                '$rootScope',
                '$scope',
                'Achievements',
                '$filter',
                'notificationService',
                'Service',
                newsFeedModal,
            ],
            controllerAs: 'vm',
            windowClass: 'news-feed-modal-wrapper',
            resolve: {},
        });

        modalInstance.result.then(
            () => {
                this.checkNewNotifications();
            },
            () => {
                this.checkNewNotifications();
            },
        );
    }
}

const newsFeedComponentDefinition = {
    templateUrl: 'partials/gamification/news-feed.component.html',
    controller: newsFeedComponentCtrl,
    controllerAs: 'vm',
    bindings: {},
};
component.component('newsFeedComponent', newsFeedComponentDefinition);
