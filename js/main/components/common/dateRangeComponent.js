component.component('dateRangeComponent', {
    bindings: {
        rangeModel: '=',
        rangeOptions: '<',
        rangeMethod: '=',
        disabledTimeRange: '=',
        timeMaxZone: '=?',
        timeMaxZone2: '=?',
    },
    template: `
        <div class="dateRange" id="dateRange">
            <div class="title" translate="Time range"></div>
            <div class="range-dropdown">
                <custom-select-new class="custom-drop-down" model="vm.rangeModel"
                                               options="vm.rangeOptions"
                                               method="vm.rangeMethod">     
                </custom-select-new>
            </div>
            <div class="date-pickers">
                <span class="range-name" translate="from_4"></span>
                <div class="picker" ng-class="{'disabled': vm.disabledTimeRange}">
                                <input placeholder="{{'from3'|translate}}" class="startDate" type="text">
                                <i class="fa fa-calendar" aria-hidden="true"></i>
                </div>
                <span class="range-name" translate="to-2"></span>
                <div class="picker" ng-class="{'disabled': vm.disabledTimeRange}">
                                <input placeholder="{{'to1'|translate}}" class="endDate" type="text">
                                <i class="fa fa-calendar" aria-hidden="true"></i>
                </div>
            </div>
 
        </div>
        <div class="alert alert-danger" ng-show="vm.timeMaxZone || vm.timeMaxZone2">{{"The selected date can not be more than the current date."|translate}}</div>

    `,
    controller: function () {},
    controllerAs: 'vm',
});
