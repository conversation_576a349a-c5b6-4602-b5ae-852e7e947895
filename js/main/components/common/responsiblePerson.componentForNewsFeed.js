class responsiblePersonComponentCtrlForNews {
    constructor(notificationService, $scope, $rootScope, $translate, Service, serverAddress, $location) {
        this.notificationService = notificationService;
        this.$scope = $scope;
        this.$rootScope = $rootScope;
        this.$translate = $translate;
        this.globalService = Service;
        this.serverAddress = serverAddress;
        this.$location = $location;
    }

    $onInit() {
        this.photoLink = this.$rootScope.allPersonsObject[this.userId].photoLink;
    }

    disableClick(e) {
        e.stopPropagation();
        e.preventDefault();

        if (this.userId) this.$location.path(`/users/${this.userId}`);
    }

    getFirstLetters(str) {
        return this.globalService.firstLetters(str);
    }

    returnNameOfLevel(level, sex) {
        switch (level) {
            case 1:
                return 'Pathfinder';
            case 2:
                return 'Experienced Pathfinder';
            case 3:
                return 'Superior Pathfinder';
            case 4:
                return sex ? 'Beater' : 'Beater_f';
            case 5:
                return sex ? 'Experienced Beater' : 'Experienced Beater_f';
            case 6:
                return sex ? 'Superior Beater' : 'Superior Beater_f';
            case 7:
                return sex ? 'Hunter' : 'Huntress';
            case 8:
                return sex ? 'Experienced Hunter' : 'Experienced Huntress';
            case 9:
                return sex ? 'Born <PERSON>' : 'Born Huntress';
            case 10:
                return 'Hunt Master';
            case 11:
                return sex ? 'Hunt Hero' : 'Hunt Heroine';
            case 12:
                return sex ? 'Hunt Champion' : 'Hunt Champion_f';
            case 13:
                return 'Hunt Icon';
            case 14:
                return sex ? 'Star Hunter' : 'Star Huntress';
            case 15:
                return sex ? 'Epic Hunter' : 'Epic Huntress';
            case 16:
                return sex ? 'Legendary Hunter' : 'Legendary Huntress';
            case 17:
                return sex ? 'Master of the Woods' : 'Mistress of the Woods';
            case 18:
                return sex ? 'King of the Hill' : 'Queen of the Mountains';
            case 19:
                return sex ? 'The Chosen One' : 'The Chosen One_f';
            case 20:
                return sex ? 'God of the Hunt' : 'Goddess of the Hunt';
        }
    }
}

const responsiblePersonComponentDefinitionForNews = {
    template: `
            <div style="width: 70px; height: 70px;justify-content: center;z-index: 99999;align-items: center;color: #312E37;border-radius: 50%;margin-bottom: 5px;display: inline-flex" class="responsible-wrapper" ng-click="vm.disableClick($event)">
                <a style="display: inline-block !important; text-decoration: none !important;color: #312E37 !important;font-weight: 600 !important;font-family: 'Helvetica-Normal' !important;" class="responsible-link" href="#/users/{{vm.userId}}" title="{{'Level' | translate}} {{vm.userLevel}} - {{vm.returnNameOfLevel(vm.userLevel, vm.userSex) | translate}}" >
                    <img style="border-radius: 50%;width: 100%;height: 100%; object-fit: cover;" class="responsible-avatar" 
                        ng-if="vm.avatarId"
                        title="{{'Level' | translate}} {{vm.userLevel}} - {{vm.returnNameOfLevel(vm.userLevel, vm.userSex) | translate}}"
                        ng-src="{{vm.serverAddress}}/getapp?id={{vm.avatarId}}&d={{$root.me.personId}}"
                        alt=""> 
                    <img style="border-radius: 50%;width: 100%;height: 100%; object-fit: cover; pointer-events: none" class="responsible-avatar  " 
                        ng-if='!vm.avatarId'
                        title="{{'Level' | translate}} {{vm.userLevel}} - {{vm.returnNameOfLevel(vm.userLevel, vm.userSex) | translate}}"
                        ng-src="{{vm.serverAddress}}/gamification/getLevelImage?userId={{vm.userId}}"
                        alt=""> 
                </a>
            </div>
    `,
    controller: responsiblePersonComponentCtrlForNews,
    controllerAs: 'vm',
    bindings: {
        userId: '<',
        avatarId: '<',
        fullName: '<',
        customClass: '<',
        userLevel: '<',
        userSex: '<',
    },
};
component.component('responsiblePersonForNewsFeed', responsiblePersonComponentDefinitionForNews);
