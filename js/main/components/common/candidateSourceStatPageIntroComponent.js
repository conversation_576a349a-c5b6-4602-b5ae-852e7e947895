component.component('candidateSourceStatPageIntroComponent', {
    bindings: { pageTitle: '<' },
    template: `
        <section class="section__intro" id="intro-section">
            <a class="btn_default back-btn" ng-click="vm.goReports();">
                <svg  class="arrowBack" width="17" height="10" viewBox="0 0 17 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0.646446 3.64645C0.451185 3.84171 0.451185 4.15829 0.646446 4.35355L3.82843 7.53553C4.02369 7.7308 4.34027 7.7308 4.53553 7.53553C4.7308 7.34027 4.7308 7.02369 4.53553 6.82843L1.70711 4L4.53553 1.17157C4.7308 0.976311 4.7308 0.659728 4.53553 0.464466C4.34027 0.269204 4.02369 0.269204 3.82843 0.464466L0.646446 3.64645ZM17 3.5L1 3.5V4.5L17 4.5V3.5Z" fill="black"/>
                    <span   class="back-btn__text" ng-style="($root.currentLang === 'en') ? {'margin-right ':'6px'} : {'margin-right ':'16px'}" >{{'Go back_' | translate}}</span>
            </a>
            <h1 ng-if="vm.pageTitle === 'scope'" class="main-page-title" translate="{{vm.pageTitle}}"></h1>
        </section>
    `,
    controller: function ($window) {
        this.goBack = function () {
            $window.history.back();
        };
        this.goReports = function () {
            $window.location.href = '#/reports/candidates-source';
        };
    },
    controllerAs: 'vm',
});
