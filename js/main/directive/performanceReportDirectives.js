(function () {
    directives.directive('performanceRowToggle', [
        function () {
            return {
                restrict: 'A',
                scope: { row: '=' },
                link: function (scope, element, attrs) {
                    element.bind('click', function (e) {
                        scope.row.collapsed = !scope.row.collapsed;
                        scope.$apply();
                    });
                },
            };
        },
    ]);
    directives.directive('performanceCandidatesModal', [
        function () {
            return {
                restrict: 'EA',
                scope: { candidates: '=' },
                link: function (scope, element, attrs) {
                    scope.selectedCandidates = [];
                    let activeCell = null,
                        _candidatesPreviewsClosing,
                        table = document.getElementById('performance-table');

                    function _getElementOffset(el) {
                        let _x = 0;
                        let _y = 0;
                        while (el && !isNaN(el.offsetLeft) && !isNaN(el.offsetTop)) {
                            _x += el.offsetLeft - el.scrollLeft;
                            _y += el.offsetTop - el.scrollTop;
                            el = el.offsetParent;
                        }
                        return { top: _y, left: _x };
                    }

                    function _getCandidateById(id) {
                        return scope.candidates[id];
                    }

                    function _setSelectedCandidates(candidatesIds) {
                        scope.selectedCandidates = candidatesIds.map((candidateId) => {
                            return _getCandidateById(candidateId);
                        });
                    }

                    function _appendSelectedCandidatesModal(e) {
                        e.stopPropagation();
                        $('#candidate_preview').hide();

                        const modal = document.getElementById('candidates-preview-modal');

                        const top = _getElementOffset(e.target).top;
                        const left = _getElementOffset(e.target).left;

                        modal.classList.remove('hidden');

                        modal.style.top = `${top}px`;
                        modal.style.left =
                            window.screen.width / 2 < left
                                ? `${window.screen.width - (modal.offsetWidth || 380) * 2}px`
                                : `${left + (modal.offsetWidth || 380) / 2}px`;
                    }

                    scope.setCandidatesPreviewPosition = function () {
                        const preview = document.getElementById('candidate_preview'),
                            modal = document.getElementById('candidates-preview-modal');

                        preview.style.top = `${parseInt(preview.style.top) - modal.offsetHeight - 90}px`;
                        preview.addEventListener('click', (e) => e.stopPropagation());
                    };

                    function _setCandidatesPreviewCustomScrollBar() {
                        $('#candidates-preview-modal .candidates').mCustomScrollbar('destroy');

                        $('#candidates-preview-modal .candidates').mCustomScrollbar({
                            theme: 'dark',
                            scrollInertia: 100,
                        });
                    }

                    function _toggleSelectedCellClass(target) {
                        if (activeCell) activeCell.classList.remove('selected');

                        activeCell = target || activeCell;

                        activeCell.classList.toggle('selected');
                    }

                    function _initCandidatesPreviewsClosing() {
                        _candidatesPreviewsClosing = (e) => {
                            const modal = document.getElementById('candidates-preview-modal');

                            if (
                                !modal.classList.contains('hidden') &&
                                !$(event.target).closest('.candidates-preview-modal-wrapper').length
                            ) {
                                modal.classList.add('hidden');
                                activeCell.classList.remove('selected');
                            }
                        };

                        document.querySelector('body').addEventListener('click', _candidatesPreviewsClosing);
                    }

                    function openCandidatesModal(candidatesIds = [], e, target) {
                        if (!candidatesIds.length) return;

                        _setSelectedCandidates(candidatesIds);
                        _appendSelectedCandidatesModal(e);
                        scope.setCandidatesPreviewPosition();
                        _setCandidatesPreviewCustomScrollBar();
                        _toggleSelectedCellClass(target);

                        scope.$apply();
                    }

                    function initModalOpening() {
                        let modalTarget = null;

                        const tableBody = document.getElementById('performance-table-body'),
                            isContainsModalClass = (target) => {
                                modalTarget = null;

                                if (target.classList.contains('modal-target')) {
                                    modalTarget = target;
                                }

                                return modalTarget;
                            };

                        tableBody.addEventListener('click', (e) => {
                            const target =
                                isContainsModalClass(e.target) || isContainsModalClass(e.target.parentElement);
                            if (target) {
                                const data = modalTarget.attributes['data-modal'].value;
                                if (data) openCandidatesModal(JSON.parse(data), e, target);
                            }
                        });
                    }

                    _initCandidatesPreviewsClosing();
                    initModalOpening();

                    table.addEventListener('scroll', _candidatesPreviewsClosing);

                    scope.$on('$destroy', () => {
                        document.querySelector('body').removeEventListener('click', _candidatesPreviewsClosing);
                    });
                },
                template: `
            <div class="candidates-preview-modal-wrapper hidden" id="candidates-preview-modal">
                    <div class="list-title">
                        <span translate="Candidates moved to the stage"></span>
                    </div>
                    <div class="candidates">
                        <div class="candidate" ng-repeat="candidate in selectedCandidates track by $index">
                            <div class="preview" ng-if="candidate">
                                <span data-title="'PREVIEW'" callback="setCandidatesPreviewPosition" priview pageid="candidate_pages" candidate="candidate" class="preview">
                                    <i candidate="user" title="{{'Profile preview'|translate}}" class="fa fa-address-card-o text_change" aria-hidden="true"></i>
                                </span>
                                <a ng-if="$root.useAmericanNameStyle" class="name" target="_blank" href="#/candidates/{{candidate.localId}}">{{candidate.fullNameEn}}</a>
                                <a ng-if="!$root.useAmericanNameStyle" class="name" target="_blank" href="#/candidates/{{candidate.localId}}">{{candidate.fullName}}</a>
                            </div>
                           <div class="preview" ng-if="!candidate">
                                <span translate="You have no access to candidate"></span>
                           </div>
                        </div>
                    </div>
            </div>
            `,
            };
        },
    ]);
    directives.directive('performanceFixedScrolling', [
        function () {
            return {
                restrict: 'A',
                scope: false,
                link: function (scope, element, attrs) {
                    const header = document.getElementById('performance-table-header'),
                        stages = document.getElementById('performance-stages'),
                        table = element[0],
                        body = document.getElementsByTagName('body')[0],
                        tableY = table.getBoundingClientRect().top,
                        tableScroll = document.getElementById('table-top-scroll-wrapper'),
                        headerY = header.getBoundingClientRect().top,
                        tableBody = document.getElementById('performance-table-body'),
                        tableWrapper = document.querySelector('.table-wrapper');

                    let prevTableScrollPos = table.scrollLeft,
                        activeCell;

                    window.onscroll = onWindowScroll;
                    table.onscroll = onTableTableScroll;

                    function onWindowScroll() {
                        const bottom = $(window).height() - $(table).offset().top - $(table).height();

                        if (
                            window.scrollY >= Math.abs(bottom) ||
                            Math.abs(bottom) < 60 ||
                            body.offsetHeight <= window.innerHeight
                        ) {
                            tableScroll.style.display = 'none';
                        } else {
                            tableScroll.style.display = 'block';
                            tableScroll.scrollLeft = table.scrollLeft;
                        }
                    }

                    function onTableTableScroll() {
                        const tableLeftScroll = Math.round(table.scrollLeft);

                        stages.style.left =
                            parseInt(stages.style.left || 0) + prevTableScrollPos - tableLeftScroll + 'px';

                        prevTableScrollPos = tableLeftScroll;
                    }

                    function updateTableWrapper() {
                        setTimeout(() => {
                            tableWrapper.style.width = tableBody.offsetWidth + 'px';
                            tableWrapper.style.maxWidth = '100%';
                        }, 0);
                    }

                    scope.vm.updateTableScroll = function () {
                        onTableTableScroll();
                        onWindowScroll();
                        updateTableWrapper();
                    };
                    scope.$on('$destroy', () => (window.onscroll = null));
                },
            };
        },
    ]);
})();
