var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives.directive('select2CustomField', [
        '$filter',
        'Service',
        'CustomField',
        'notificationService',
        function ($filter, Service, CustomField, notificationService) {
            return {
                restrict: 'EA',
                replace: true,
                link: function ($scope, element, attrs) {
                    var groupNameList = [];
                    var addParam = [];
                    $(element[0])
                        .select2({
                            tags: groupNameList,
                            tokenSeparators: [','],
                            placeholder: $filter('translate')('Enter value for the drop-down list'),
                            minimumInputLength: 1,
                            width: '310px',
                            allowClear: true,
                            formatInputTooShort: function () {
                                return '' + $filter('translate')('Please enter 1 character') + '';
                            },
                        })
                        .on('select2-open', function () {
                            const li = $('.select2-container ul.select2-choices li.select2-search-field');
                            const label = $(
                                '.select2-container ul.select2-choices li.select2-search-field label.select2-offscreen',
                            );
                            const input = $('.select2-container ul.select2-choices li.select2-search-field input');

                            li.removeClass('hidden');
                            input.addClass('block');
                            label.text($filter('translate')('Enter value for the drop-down list'));
                            label.css('display', 'flex');
                        })
                        .on('select2-close', function () {
                            const input = $('.select2-container ul.select2-choices li.select2-search-field input');

                            input.removeClass('block');
                            input.css('display', 'none');
                        })
                        .on('change', function (e) {
                            if (e.added) {
                                $scope.emptyDropDown = false;
                                $scope.selectedTags.push(e.added);
                                $scope.$apply();
                            }
                        });

                    const label = $(
                        '.select2-container ul.select2-choices li.select2-search-field label.select2-offscreen',
                    );
                    const input = $('.select2-container ul.select2-choices li.select2-search-field input');
                    label.text($filter('translate')('Enter value for the drop-down list'));
                    label.css('display', 'flex');
                    input.css('display', 'none');

                    // $('#addFieldParam').click(function () {
                    //     if (
                    //         $scope.newField.type.value !== 'select' ||
                    //         ($scope.selectedField && $scope.selectedField.title)
                    //     )
                    //         return;
                    //
                    //     var newGroupList = $scope.getSelect2Group().split(',');
                    //
                    //     var addParam = [];
                    //     $scope.emptyDropDown = false;
                    //     angular.forEach(newGroupList, function (nval) {
                    //         $scope.emptyDropDown = !nval;
                    //     });
                    //
                    //     if ($scope.emptyDropDown) {
                    //         notificationService.error(
                    //             $filter('translate')('Please enter at least one value for the drop-down list'),
                    //         );
                    //         $scope.$apply();
                    //         return;
                    //     }
                    //
                    //     angular.forEach(newGroupList, function (nval) {
                    //         addParam.push({
                    //             name: 'defaultValue',
                    //             value: nval,
                    //         });
                    //     });
                    //
                    //     $scope.count = 0;
                    //     angular.forEach($scope.allObjCustomField, function (val) {
                    //         if (val.orderIndex != undefined && $scope.count <= val.orderIndex) {
                    //             $scope.count = val.orderIndex;
                    //         }
                    //     });
                    //
                    //     if (!$scope.newField.title) {
                    //         notificationService.error($filter('translate')('Enter the field title'));
                    //         $scope.errorHandler.title = true;
                    //         $scope.$apply();
                    //         return;
                    //     }
                    //
                    //     CustomField.addField(
                    //         {
                    //             objType: $scope.newField.objType,
                    //             type: $scope.newField.type.value,
                    //             title: $scope.newField.title,
                    //             orderIndex: ++$scope.count,
                    //             params: addParam,
                    //             mandatory: $scope.newField.isRequired,
                    //         },
                    //         function (resp) {
                    //             if (resp.status == 'ok') {
                    //                 $scope.objCustomField = resp.object;
                    //                 $scope.fieldTitle = '';
                    //                 $('#customFullField').select2('val', '');
                    //                 $scope.showDropDownSelect = false;
                    //                 $scope.typeCustomField = null;
                    //                 $scope.newField.title = '';
                    //                 $scope.getCustomFieldsByType($scope.newField.objType);
                    //                 $scope.closeModal();
                    //                 notificationService.success($filter('translate')('New field added'));
                    //             } else {
                    //                 //notificationService.error(resp.message);
                    //             }
                    //         },
                    //     );
                    // });

                    $scope.getSelect2Group = function () {
                        var val = $(element[0]).select2('val');
                        return val != null ? val.toString() : null;
                    };
                    $scope.setSelect2Group = function (val) {
                        if (val != undefined) {
                            $(element[0]).select2('val', val);
                        }
                    };
                    $('#s2id_customFullField .select2-search-field input').attr('maxlength', 50);
                },
            };
        },
    ]);
})();
