var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives.directive('clientAutocompleter', [
        '$filter',
        'serverAddress',
        '$rootScope',
        'vacancyStages',
        '$translate',
        '$rootScope',
        function ($filter, serverAddress, $rootScope, vacancyStages, $translate) {
            return {
                restrict: 'EA',
                replace: true,
                link: function ($scope, element, attrs) {
                    if ($scope.vm)
                        $scope.vm.setClientAutocompleterPlaceholderValue = function () {
                            $(element[0]).select2('val', '');
                            $(element[0]).val('').trigger('change');
                        };
                    $scope.setClientAutocompleterValue = function (val, id) {
                        //переимновтаь
                        if (val != undefined) {
                            $(element[0]).select2('data', {
                                id: id,
                                text: val,
                            });
                        }
                    };
                    if ($scope.vm) $scope.vm.setClientAutocompleterValue = $scope.setClientAutocompleterValue;
                    let translatedPositions = false,
                        inputText = null;

                    function translateClient() {
                        let className = document.querySelector('#s2id_clientAutocompleater').children[0].className;
                        let placeholder = document.querySelector('#s2id_clientAutocompleater').children[0].children[0];
                        setTimeout(() => {
                            if (className === 'select2-choice select2-default') {
                                placeholder.innerText = $translate.instant('client');
                            }
                        }, 500);
                    }

                    if (!translatedPositions) {
                        initSelect2();
                    }
                    function initSelect2() {
                        const clientsData = [];

                        const formData = new FormData();
                        formData.append('text', '');

                        fetch(`${serverAddress}/client/autocompleteClients`, {
                            method: 'POST',
                            body: formData,
                        })
                            .then((r) => r.json())
                            .then((data) => {
                                angular.forEach(data['objects'], function (item) {
                                    clientsData.push({
                                        id: item.clientId,
                                        text: item.name,
                                        name: item.name,
                                    });
                                });
                                return clientsData;
                            })
                            .then((clientsData) => {
                                if (clientsData.length > 1 || window.location.hash === '#/vacancies') return;

                                $(element[0]).select2('data', {
                                    id: clientsData[0]?.id,
                                    text: clientsData[0]?.name,
                                    name: clientsData[0]?.name,
                                });
                            });

                        $scope.$watch('$root.currentLang', (current, old) => {
                            if (current !== old) {
                                translateClient();
                            }
                        });
                        translatedPositions = true;
                        if ($(element[0])) {
                            element
                                .select2({
                                    placeholder: $translate.instant(attrs.customPlaceholder || 'client'),
                                    minimumInputLength: 0,
                                    allowClear: true,
                                    data: clientsData,
                                    dropdownCssClass: 'bigdrop',
                                })
                                .on('select2-close', function (e) {
                                    if ($scope.vm && $scope.vm.search)
                                        $scope.vm.search.fields.client.value = $(element[0]).select2('data')
                                            ? $(element[0]).select2('data')
                                            : null;
                                    if ($scope.searchParam)
                                        $scope.searchParam.clientId = $(element[0]).select2('data')
                                            ? $(element[0]).select2('data').text
                                            : null;
                                })
                                .on('change', function (e) {
                                    if ($scope.errorFields) {
                                        $scope.errorFields.clientName = false;
                                    }

                                    translateClient();

                                    if ($scope.vm && $scope.vm.search)
                                        $scope.vm.search.fields.client.value = $(element[0]).select2('data')
                                            ? $(element[0]).select2('data')
                                            : null;
                                    if ($scope.searchParam)
                                        $scope.searchParam.clientId = $(element[0]).select2('data')
                                            ? $(element[0]).select2('data').text
                                            : null;
                                    $rootScope.$$phase || $scope.$apply();
                                })
                                .on('select2-opening', function (e) {
                                    setTimeout(function () {
                                        if (!$('#select2-drop .select2-results .select2-searching')[0]) return;
                                        $('#select2-drop .select2-results .select2-searching')[0].innerText =
                                            $filter('translate')('Searching');
                                    }, 0);
                                });
                        }
                    }
                },
            };
        },
    ]);
})();
