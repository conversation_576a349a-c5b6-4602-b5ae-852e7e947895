var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives.directive('sendEmailToClient', [
        '$filter',
        'serverAddress',
        'Service',
        'CandidateGroup',
        '$rootScope',
        '$translate',
        function ($filter, serverAddress, Service, CandidateGroup, $rootScope, $translate) {
            return {
                restrict: 'EA',
                replace: true,
                link: function ($scope, element, attrs) {
                    function initSelect2() {
                        $(element[0]).val('').trigger('change');
                        let arrayUniqueByKey = [];
                        if ($rootScope.contactEmails && $rootScope.contactEmails.length) {
                            arrayUniqueByKey = [
                                ...new Map(
                                    $rootScope.contactEmails.map((item) => {
                                        return [
                                            item['id'],
                                            {
                                                id: item.id,
                                                text: item.text + ' ' + '<' + item.id + '>',
                                            },
                                        ];
                                    }),
                                ).values(),
                            ];
                        }

                        $(element[0]).select2({
                            tags: arrayUniqueBy<PERSON>ey,
                            tokenSeparators: [','],
                            placeholder: $translate.instant('Please select who you want to send the letter'),
                            formatNoMatches: function (term) {
                                return (
                                    "<div class='select2-result-label' style='cursor: s-resize;'><span class='select2-match'></span>" +
                                    $filter('translate')('No matches found.') +
                                    '</div>'
                                );
                            },
                        });
                    }
                    initSelect2();
                    if ($rootScope.contactEmails && $rootScope.contactEmails.length === 1) {
                        $(element[0]).select2('data', {
                            id: $rootScope.contactEmails[0].id,
                            text: $rootScope.contactEmails[0].text + ' ' + '<' + $rootScope.contactEmails[0].id + '>',
                        });
                        $rootScope.emailToSend = $rootScope.contactEmails[0].id;
                    }
                },
            };
        },
    ]);
})();
