var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives.directive('skillsAutocompleter', [
        '$filter',
        'serverAddress',
        'notificationService',
        function ($filter, serverAddress, notificationService) {
            return {
                restrict: 'EA',
                replace: true,
                link: function ($scope, element, attrs) {
                    $scope.setSkillAutocompleterValue = function (val) {
                        //переимновтаь
                        if (val != undefined) {
                            $(element[0]).select2('data', {
                                id: val,
                                text: val,
                            });
                        }
                    };
                    $scope.getSkillAutocompleterValue = function () {
                        //.переимновтаь
                        var object = $(element[0]).select2('data');
                        return object != null ? object.text : null;
                    };
                    var inputText = '';
                    var source = [
                        'MySQL',
                        'JavaScript',
                        'Linux',
                        'CSS',
                        'HTML',
                        'PHP',
                        'jQuery',
                        'SQL',
                        'Git',
                        'C#',
                        'Java',
                        'XML',
                        'C++',
                        'Python',
                        'OOP/OOD',
                        'PostgreSQL',
                        'MongoDB',
                        'Spring',
                        'Hibernate',
                        'CSS3',
                        'HTML5',
                        'MVC',
                        'Oracle',
                        'ASP.NET',
                        'Android',
                        'Maven',
                        'WPF',
                        'JSON',
                        'WCF',
                        'AJAX',
                        '.NET',
                        'Jira',
                        'Django',
                        'Scrum',
                        'Windows',
                        'JSP',
                        'Yii',
                        'Redis',
                        'JDBC',
                        'SQLite',
                        'Agile',
                        'Ruby',
                        'TDD',
                        'JUnit',
                        'REST API',
                        'STL',
                        'Qt',
                        'Bootstrap',
                        'Tomcat',
                        'ADO.NET',
                        'Delphi',
                        'LINQ',
                        'iOS',
                        'WinForms',
                        'Selenium',
                        'Angular.js',
                        'Design Patterns',
                        'Eclipse',
                        'Apache',
                        'Mercurial',
                        'Frontend',
                        'Entity Framework',
                        'Node.js',
                        'Photoshop',
                        'T-SQL',
                        'JPA',
                        'Objective-C',
                        'Nginx',
                        'Multithreading',
                        'Servlets',
                        'Redmine',
                        'SOAP',
                        'TFS',
                        'UML',
                        'Backbone.js',
                        'Jenkins',
                        'Perl',
                        'Wordpress',
                        'Subversion',
                        'Ant',
                        'Ruby on Rails',
                        'JSF',
                        'CoffeeScript',
                        'PL/SQL',
                        'Joomla',
                        'C/C++',
                        'FreeBSD',
                        'LESS',
                        'SASS',
                        'bash',
                        'Android SDK',
                        'Drupal',
                        'GWT',
                        'EJB',
                        'Scala',
                        'Smarty',
                        'CodeIgniter',
                        'XSLT',
                        'OpenGL',
                        'Quality Assurance (QA)',
                        'Flask',
                        'Symfony',
                        'Swing',
                        'OOD',
                        'Zend Framework (ZF)',
                        'Ubuntu',
                        'Twitter Bootstrap',
                        'Java EE',
                        'Memcached',
                        'Magento',
                        'Silverlight',
                        '\u0421++',
                        'ExtJS',
                        'JMS',
                        'Illustrator',
                        'Boost',
                        'Sphinx',
                        'Patterns',
                        'BDD',
                        'MS SQL Server',
                        'NodeJS',
                        'Unix',
                        'User Interface (UI)',
                        'TCP/IP',
                        'Unity3D',
                        'CI',
                        'Java Core',
                        'JBoss',
                        'RabbitMQ',
                        'NoSQL',
                        'WinAPI',
                        'TeamCity',
                        'Kohana',
                        'TestNG',
                        'Flash',
                        'HAML',
                        'UIKit',
                        'JSTL',
                        'AWS',
                        'NetBeans',
                        'Product management',
                        'RSpec',
                        'C (plain)',
                        'Groovy',
                        'Kanban',
                        'MFC',
                        'Confluence',
                        'Spring MVC',
                        'HTTP',
                        'NHibernate',
                        'XPath',
                        'XAML',
                        'Mantis',
                        'PHPUnit',
                        'Mockito',
                        'Algorithms',
                        'Embedded',
                        'Memcache',
                        'Struts',
                        'Adobe Photoshop',
                        'Cocoa',
                        'CoreData',
                        'Firebird',
                        'Selenium IDE',
                        'IIS',
                        'Visual Studio',
                        'NUnit',
                        'English',
                        'RoR',
                        'Flex',
                        'Lua',
                        'Perforce',
                        'Networking',
                        'SOA',
                        'Knockout',
                        'LAMP',
                        'Xcode',
                        'CakePHP',
                        '\u0421#',
                        'Tornado',
                        'SSIS',
                        'Firebug',
                        'Microsoft SQL Server',
                        'RequireJS',
                        'Highload',
                        'MVVM',
                        'Windows Phone',
                        'SEO',
                        'Doctrine',
                        'COM',
                        'Azure',
                        'Debian',
                        'User Experience (UX)',
                        'IntelliJ IDEA',
                        'JMeter',
                        'Erlang',
                        'Cocoa Touch',
                        'TestComplete',
                        'Windows Forms',
                        'MapKit',
                        'DDD',
                        'MacOS',
                        'Twig',
                        'CVS',
                        'XP',
                        'Foundation',
                        'GUI',
                        'GCD',
                        'Pascal',
                        'Mongo',
                        'Axure',
                        'CentOS',
                        'Prototype',
                        'SoapUI',
                        'Test driven development (TDD)',
                        'SSRS',
                        'Cucumber',
                        'Manual Testing (QA)',
                        'SOLID',
                        'ActionScript',
                        'Grunt',
                        'Solr',
                        'CMS',
                        'Cassandra',
                        'Sinatra',
                        'EntityFramework',
                        'DNS',
                        'Entity',
                        'Hadoop',
                        'Symfony 2',
                        'CRM',
                        'JAXB',
                        'Project Management',
                        'DHCP',
                        'Knockout.js',
                        'regression',
                        'Celery',
                        'TestRail',
                        'CouchDB',
                        'Jetty',
                        'Automation',
                        'DevExpress',
                        'ETL',
                        'Java SE',
                        'Servlet',
                        'ZF',
                        'WebServices',
                        'SCSS',
                        'ASP',
                        'ORM',
                        'Bugzilla',
                        'JAX-RS',
                        'Web Development',
                        'Automated Testing (QA)',
                        'Embedded C',
                        'Stylus',
                        'Selenium WebDriver',
                        'Bitrix',
                        'Shell',
                        'Jade',
                        'iPhone',
                        'Facebook API',
                        'SDLC',
                        'IDEA',
                        'Microsoft Office',
                        'Threads',
                        'Security',
                        'VirtualBox',
                        'Matlab',
                        'DB2',
                        'Vaadin',
                        'Capybara',
                        'Visual Basic (VB)',
                        'Adobe Illustrator',
                        'ARM',
                        'MyBatis',
                        'Waterfall',
                        'RUP',
                        'PHP5',
                        'ActionScript3 (AS3)',
                        'Glassfish',
                        'VMware',
                        'XSD',
                        'Laravel',
                        'Underscore.js',
                        'Gradle',
                        'SharePoint',
                        'SVG',
                        'Grails',
                        'usability',
                        'WinRT',
                        'Zabbix',
                        'Cocos2d',
                        'Nagios',
                        'Assembler',
                        'Objective C',
                        'CoreLocation',
                        'R (language)',
                        'Win32',
                        'Regression Testing',
                        'Cisco',
                        'OpenCV',
                        'JAX-WS',
                        'DirectX',
                        'Continuous Integration',
                        'Haskell',
                        'GIS',
                        'Open Source',
                        'Leaflet',
                    ];
                    var sourceForSelect = [];
                    angular.forEach(source, function (val) {
                        sourceForSelect.push({ id: val, text: val });
                    });
                    $(element[0])
                        .select2({
                            placeholder: $filter('translate')('Enter skill'),
                            minimumInputLength: 2,
                            formatInputTooShort: function () {
                                return '' + $filter('translate')('Please enter 2 characters') + '';
                            },
                            formatNoMatches: function (term) {
                                return (
                                    "<div class='select2-result-label' style='cursor: s-resize;'><span class='select2-match'></span>" +
                                    $filter('translate')('Enter a source of this candidate') +
                                    '</div>'
                                );
                            },
                            createSearchChoice: function (term, data) {
                                if (
                                    $(data).filter(function () {
                                        return this.text.localeCompare(term) === 0;
                                    }).length === 0
                                ) {
                                    inputText = term;
                                    return { id: term, text: term };
                                }
                            },
                            data: sourceForSelect,
                            dropdownCssClass: 'bigdrop',
                        })
                        .on('select2-close', function (e) {
                            function addSkillName(first) {
                                this.name = first;
                            }
                            if ($scope.getSkillAutocompleterValue().length > 1) {
                                if ($scope.candidate.skills.length > 0) {
                                    var noDoublicate = true;
                                    angular.forEach($scope.candidate.skills, function (resp) {
                                        if (
                                            resp.name.toLowerCase() == $scope.getSkillAutocompleterValue().toLowerCase()
                                        ) {
                                            notificationService.error($filter('translate')('Skill is already added'));
                                            noDoublicate = false;
                                        }
                                    });
                                    if (noDoublicate) {
                                        $scope.candidate.skills.push({
                                            name: $scope.getSkillAutocompleterValue(),
                                            level: '0',
                                        });
                                    }
                                } else {
                                    $scope.candidate.skills.push({
                                        name: $scope.getSkillAutocompleterValue(),
                                        level: '0',
                                    });
                                }
                            }
                            $scope.setSkillAutocompleterValue('');
                            $scope.$apply();
                            if (inputText.length > 0) {
                                $(element[0]).select2('data', {
                                    id: inputText,
                                    text: inputText,
                                });
                            }
                        })
                        .on('select2-selecting', function (e) {
                            inputText = '';
                        });
                },
            };
        },
    ]);
})();
