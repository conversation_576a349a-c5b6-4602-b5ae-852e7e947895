var directives = directives || angular.module('RecruitingApp.directives', []);

(function () {
    directives.directive('candidateSelectSkills', [
        '$rootScope',
        '$filter',
        '$translate',
        'serverAddress',
        'notificationService',
        'Service',
        'Vacancy',
        function ($rootScope, $filter, $translate, serverAddress, notificationService, Service, Vacancy) {
            return {
                restrict: 'EA',
                scope: {
                    skill: '=',
                    systemSkillsByCategory: '=',
                    customSkillsByCategory: '=',
                    importedSkillsByCategory: '=',
                    skillsObject: '=',
                    progressUpdate: '=',
                    industry: '=',
                    method: '=?',
                    experience: '=',
                },
                replace: true,
                link: function (scope, element, attrs) {
                    // scope.industry === "IT" ?  initSelect2($rootScope.skills,$rootScope.customSkills):initSelect2([],$rootScope.customSkills);
                    let inputText = '';
                    const plusIcon =
                        '<img src="images/sprite/plus-icon.svg"   style="position: absolute;top: 8px;right: 25px;cursor:pointer;" id="plusIcon">';
                    let textError;
                    $rootScope.currentLang === 'en'
                        ? (textError =
                              '<span id="textError" style="color:red;position: absolute;top:-25px;right:15px">Text must be no more than 50 characters</span>')
                        : $rootScope.currentLang === 'ru'
                        ? (textError =
                              '<span id="textError" style="color:red;position: absolute;top:-25px;right:15px">Текст должен быть до 50 символов</span>')
                        : (textError =
                              '<span id="textError" style="color:red;position: absolute;top:-25px;right:15px">Текст має бути до 50 символів</span>');

                    $(element[0]).on('select2-open', function (e, data) {
                        $('.skills-input-wrapper').children('.select2-search').children('#plusIcon').remove();
                        const autocompleterValue = $(element[0]).select2('data')
                            ? $(element[0]).select2('data').text
                            : '';
                        $('.select2-results li.select2-result-with-children > .select2-result-label').css(
                            'pointer-events',
                            'none',
                        );
                        if ($(element[0]).select2('data')) $('#select2-drop input').val(autocompleterValue);
                    });

                    $(element[0]).on('select2-selecting', function (e) {
                        var $select = $(this);
                        if (e.val == '') {
                            e.preventDefault();
                        }
                        let includes = false;
                        let newSkill = '';
                        let transliterator = new Set(Service.transliterate(e.val.toString()));
                        transliterator.forEach((item) => {
                            scope.allSkills.forEach((skill) => {
                                if (item === skill.skill.toLowerCase()) {
                                    newSkill = skill.skill;
                                    includes = true;
                                }
                            });
                        });
                        if (includes) {
                            e.preventDefault();
                            $(element[0]).select2('data', {
                                id: newSkill,
                                text: newSkill,
                            });
                            $(element[0]).trigger('change');
                            $(element[0]).select2('close');
                        }
                    });

                    function skillErrorHandler(skill) {
                        return {
                            skillErrorLength: skill.length > 50,
                            skillErrorValueOfWords: skill.split(' ').length > 3,
                        };
                    }

                    initSelect2($rootScope.allSkills, $rootScope.customSkills);

                    function initSelect2(
                        systemSkillsByCategory = [],
                        customSkillsByCategory = [],
                        importedSkillsByCategory = [],
                    ) {
                        customSkillsByCategory = systemSkillsByCategory.filter((item) => item.type === 'custom');
                        systemSkillsByCategory = systemSkillsByCategory.filter((item) => item.type === 'general');

                        if (
                            scope.industry === 'IT' &&
                            !systemSkillsByCategory.length &&
                            $rootScope.skills &&
                            $rootScope.skills.length
                        ) {
                            systemSkillsByCategory = $rootScope.skills;
                        }
                        $(element[0])
                            .select2({
                                placeholder: $translate.instant('Skills'),
                                allowClear: true,
                                matcher: matchByTransliteral,
                                formatNoMatches: function (term) {
                                    return `<div class='select2-result-label'>${$filter('translate')(
                                        'You have no skills. You can add them.',
                                    )}</div>`;
                                },
                                query: function (query) {
                                    if (query) {
                                        if (!query.term || !query.term.trim()) {
                                            const useDataByCategory = !(
                                                systemSkillsByCategory.length ||
                                                customSkillsByCategory.length ||
                                                importedSkillsByCategory.length
                                            );
                                            const results = getData(
                                                systemSkillsByCategory,
                                                customSkillsByCategory,
                                                useDataByCategory,
                                                importedSkillsByCategory,
                                            );
                                            if (results && results.length) {
                                                $('.skills-input-wrapper')
                                                    .children('.select2-search')
                                                    .children('#plusIcon')
                                                    .remove();
                                            } else {
                                                $('.skills-input-wrapper')
                                                    .children('.select2-search')
                                                    .children('#plusIcon')
                                                    .remove();
                                                $('.skills-input-wrapper')
                                                    .children('.select2-search')
                                                    .prepend(plusIcon);
                                                $('.select2-search').on('click', (e) => {
                                                    e.stopImmediatePropagation();
                                                    if (
                                                        e.target.id === 'plusIcon' &&
                                                        e.target.parentNode.lastElementChild.value.trim()
                                                    ) {
                                                        let includes = false;
                                                        let newSkill = '';
                                                        let transliterator = new Set(
                                                            scope.transliterate(
                                                                e.target.parentNode.lastElementChild.value,
                                                            ),
                                                        );
                                                        const skillErrors = skillErrorHandler(
                                                            e.target.parentNode.lastElementChild.value.trim(),
                                                        );
                                                        if (skillErrors.skillErrorLength) {
                                                            notificationService.error(
                                                                $filter('translate')(
                                                                    'Custom skill should be no more than 50 characters',
                                                                ),
                                                            );
                                                            return;
                                                        }
                                                        if (skillErrors.skillErrorValueOfWords) {
                                                            notificationService.error(
                                                                $filter('translate')(
                                                                    'Custom skill should be no more than 3 words',
                                                                ),
                                                            );
                                                            return;
                                                        }

                                                        transliterator.forEach((item) => {
                                                            scope.allSkills.forEach((skill) => {
                                                                if (item === skill.skill.toLowerCase()) {
                                                                    newSkill = skill.skill;
                                                                    includes = true;
                                                                }
                                                            });
                                                        });
                                                        if (includes) {
                                                            $(element[0]).select2('data', {
                                                                id: newSkill,
                                                                text: newSkill,
                                                            });
                                                            $(element[0]).trigger('change');
                                                            $(element[0]).select2('close');
                                                        } else {
                                                            $(element[0]).select2('data', {
                                                                id: e.target.parentNode.lastElementChild.value,
                                                                text: removeExtraSpaces(
                                                                    e.target.parentNode.lastElementChild.value,
                                                                ),
                                                            });
                                                            $(element[0]).trigger('change');
                                                            $(element[0]).select2('close');
                                                        }
                                                    }
                                                });
                                            }
                                            query.callback({ results });
                                        } else if (query.term.trim()) {
                                            Vacancy.onGetSkillAutocomplete(query.term).then(
                                                (response) => {
                                                    let results = [];

                                                    if (response && response.objects && response.objects.length) {
                                                        const autocompleteSystemSkills = response.objects.filter(
                                                            (skill) => {
                                                                if (
                                                                    !scope.skillsObject.hasOwnProperty(
                                                                        skill.skill.toLowerCase(),
                                                                    )
                                                                ) {
                                                                    scope.skillsObject[skill.skill.toLowerCase()] =
                                                                        skill.skillId;
                                                                }

                                                                return skill.type === 'general';
                                                            },
                                                        );
                                                        const autocompleteCustomSkills = response.objects.filter(
                                                            (skill) => skill.type === 'custom',
                                                        );
                                                        const autocompleteImportedSkills = response.objects.filter(
                                                            (skill) => skill.type === 'imported',
                                                        );
                                                        results = getData(
                                                            autocompleteSystemSkills,
                                                            autocompleteCustomSkills,
                                                            false,
                                                            autocompleteImportedSkills,
                                                        );
                                                    }
                                                    $('.skills-input-wrapper')
                                                        .children('.select2-search')
                                                        .unbind('click');
                                                    if (results && results.length) {
                                                        $('.skills-input-wrapper')
                                                            .children('.select2-search')
                                                            .children('#plusIcon')
                                                            .remove();
                                                    } else {
                                                        $('.skills-input-wrapper')
                                                            .children('.select2-search')
                                                            .children('#plusIcon')
                                                            .remove();
                                                        $('.skills-input-wrapper')
                                                            .children('.select2-search')
                                                            .prepend(plusIcon);
                                                        $('.skills-input-wrapper')
                                                            .children('.select2-search')
                                                            .on('click', (e) => {
                                                                e.stopImmediatePropagation();
                                                                if (
                                                                    e.target.id === 'plusIcon' &&
                                                                    e.target.parentNode.lastElementChild.value.trim()
                                                                ) {
                                                                    let includes = false;
                                                                    let newSkill = '';
                                                                    let val = scope.transliterate(
                                                                        e.target.parentNode.lastElementChild.value,
                                                                    );
                                                                    let transliterator = new Set(val);
                                                                    const skillErrors = skillErrorHandler(
                                                                        e.target.parentNode.lastElementChild.value.trim(),
                                                                    );
                                                                    if (skillErrors.skillErrorLength) {
                                                                        notificationService.error(
                                                                            $filter('translate')(
                                                                                'Custom skill should be no more than 50 characters',
                                                                            ),
                                                                        );
                                                                        return;
                                                                    }
                                                                    if (skillErrors.skillErrorValueOfWords) {
                                                                        notificationService.error(
                                                                            $filter('translate')(
                                                                                'Custom skill should be no more than 3 words',
                                                                            ),
                                                                        );
                                                                        return;
                                                                    }

                                                                    transliterator.forEach((item) => {
                                                                        scope.allSkills.forEach((skill) => {
                                                                            if (item === skill.skill.toLowerCase()) {
                                                                                newSkill = skill.skill;
                                                                                includes = true;
                                                                            }
                                                                        });
                                                                    });
                                                                    if (includes) {
                                                                        $(element[0]).select2('data', {
                                                                            id: newSkill,
                                                                            text: newSkill,
                                                                        });
                                                                        $(element[0]).trigger('change');
                                                                        $(element[0]).select2('close');
                                                                    } else {
                                                                        $(element[0]).select2('data', {
                                                                            id: e.target.parentNode.lastElementChild
                                                                                .value,
                                                                            text: removeExtraSpaces(
                                                                                e.target.parentNode.lastElementChild
                                                                                    .value,
                                                                            ),
                                                                        });
                                                                        $(element[0]).trigger('change');
                                                                        $(element[0]).select2('close');
                                                                    }
                                                                }
                                                            });
                                                    }
                                                    query.callback({ results });
                                                },
                                                () => {
                                                    query.callback({
                                                        results: [],
                                                    });
                                                },
                                            );
                                        }
                                    }
                                },
                                formatResultCssClass: classFormatter,
                                dropdownCssClass: 'skills-input-wrapper',
                            })
                            .on('change', function (e) {
                                if (e.removed && scope.progressUpdate) scope.progressUpdate();
                                const autocompleterValue = $(element[0]).select2('data')
                                    ? $(element[0]).select2('data').text
                                    : '';
                                if (e.removed) {
                                    scope.experience = 'e00_no_experience';
                                }
                                if (inputText.length > 0) {
                                    $(element[0]).select2('data', {
                                        id: inputText,
                                        text: removeExtraSpaces(inputText),
                                    });
                                }
                                if ($(element[0]).select2('data')) {
                                    $rootScope.$emit('modelOfFieldIsChanged', 'skills');
                                    $(element[0]).select2('data', {
                                        id: inputText,
                                        text: removeExtraSpaces(autocompleterValue),
                                    });

                                    if (scope.method) {
                                        scope.method();
                                    }

                                    scope.candidateSetSkillAutocompleterValue(removeExtraSpaces(autocompleterValue));

                                    if (scope.search)
                                        scope.search.fields.position.value = removeExtraSpaces(autocompleterValue);
                                } else {
                                    if (scope.search)
                                        scope.search.fields.position.value = removeExtraSpaces(autocompleterValue);
                                }
                                if (scope.importedSkills.includes(autocompleterValue.toLowerCase())) {
                                    $rootScope.$broadcast('addNewSkillImported', {
                                        skillName: autocompleterValue,
                                    });
                                }
                                if (
                                    scope.systemSkills &&
                                    !scope.systemSkills.includes(autocompleterValue.toLowerCase()) &&
                                    !scope.importedSkills.includes(autocompleterValue.toLowerCase())
                                ) {
                                    $rootScope.$broadcast('addNewSkill', {
                                        skillName: autocompleterValue,
                                    });
                                }
                                if (scope.systemSkills && scope.systemSkills.includes(autocompleterValue.toLowerCase()))
                                    $rootScope.$broadcast('setTypeSkill', {
                                        skillName: autocompleterValue,
                                    });
                            });
                    }

                    function getData(
                        systemSkills = [],
                        customSkills = [],
                        useDataByCategory = false,
                        importedSkills = [],
                    ) {
                        if (useDataByCategory) {
                            systemSkills = scope.systemSkillsByCategory || [];
                            customSkills = scope.customSkillsByCategory || [];
                            importedSkills = scope.importedSkillsByCategory || [];
                        }
                        $rootScope.$emit('getSkillsToSave');
                        if ($rootScope.skillsToSave && $rootScope.skillsToSave.length) {
                            scope.selectedSkills = $rootScope.skillsToSave
                                .filter((item) => item.skill)
                                .map((skill) => skill.skill);
                        } else scope.selectedSkills = [];
                        let data = [];
                        scope.selectedSkills = scope.selectedSkills.map((skill) => skill.toLowerCase());
                        scope.systemSkills = systemSkills.map((item) => item.skill.toLowerCase());
                        scope.customSkills = customSkills.map((item) => item.skill.toLowerCase());
                        scope.importedSkills = importedSkills.map((item) => item.skill.toLowerCase());

                        scope.allSkills = [...systemSkills, ...customSkills, ...importedSkills];
                        if (customSkills.length) {
                            customSkills = customSkills.filter(
                                (item) => !scope.selectedSkills.includes(item.skill.toLowerCase()),
                            );
                            customSkills = customSkills.map((item) => {
                                return {
                                    id: item.skill,
                                    text: item.skill,
                                    system: false,
                                };
                            });
                        } else customSkills = [];
                        if (importedSkills.length) {
                            importedSkills = importedSkills.filter(
                                (item) => !scope.selectedSkills.includes(item.skill.toLowerCase()),
                            );
                            importedSkills = importedSkills.map((item) => {
                                return {
                                    id: item.skill,
                                    text: item.skill,
                                    system: false,
                                };
                            });
                        } else importedSkills = [];
                        if (systemSkills.length) {
                            systemSkills = systemSkills.filter(
                                (item) => !scope.selectedSkills.includes(item.skill.toLowerCase()),
                            );
                            systemSkills = systemSkills.map((item) => {
                                return {
                                    id: item.skill,
                                    text: item.skill,
                                    system: true,
                                };
                            });
                        } else systemSkills = [];

                        let customSkillsGroup = '';
                        let systemSkillsGroup = '';
                        let importedSkillsGroup = '';
                        let emptyCustomSkillsGroup = '';

                        switch ($rootScope.currentLang) {
                            case 'en':
                                systemSkillsGroup = 'System skills';
                                customSkillsGroup = 'Custom skills';
                                importedSkillsGroup = 'Integrated skills';
                                emptyCustomSkillsGroup = 'You have no one custom skill';
                                break;
                            case 'ua':
                                systemSkillsGroup = 'Системні навички';
                                customSkillsGroup = 'Кастомні навички';
                                importedSkillsGroup = 'Імпортовані навички';
                                emptyCustomSkillsGroup = 'Немає кастомних навичок';
                                break;
                            case 'pl':
                                systemSkillsGroup = 'Zdefiniowane umiejetności';
                                customSkillsGroup = 'Kompetencje konfigurowalne';
                                importedSkillsGroup = 'Kompetencje zaimportowane';
                                emptyCustomSkillsGroup = 'Brak kompetencji konfigurowalnych';
                                break;
                            case 'ru':
                                systemSkillsGroup = 'Системные навыки';
                                customSkillsGroup = 'Кастомные навыки';
                                importedSkillsGroup = 'Импортированные навыки';
                                emptyCustomSkillsGroup = 'Нет кастомных навыков';
                                break;
                        }

                        if (customSkills.length > 0 && systemSkills.length > 0) {
                            if (importedSkills.length > 0) {
                                data = [
                                    {
                                        id: '',
                                        text: systemSkillsGroup,
                                        system: true,
                                        children: [...systemSkills],
                                    },
                                    {
                                        id: '',
                                        text: customSkillsGroup,
                                        system: true,
                                        children: [...customSkills],
                                    },
                                    {
                                        id: '',
                                        text: importedSkillsGroup,
                                        system: true,
                                        children: [...importedSkills],
                                    },
                                ];
                            } else {
                                if (importedSkills.length > 0) {
                                    data = [
                                        {
                                            id: '',
                                            text: systemSkillsGroup,
                                            system: true,
                                            children: [...systemSkills],
                                        },
                                        {
                                            id: '',
                                            text: customSkillsGroup,
                                            system: true,
                                            children: [...customSkills],
                                        },
                                        {
                                            id: '',
                                            text: importedSkillsGroup,
                                            system: true,
                                            children: [...importedSkills],
                                        },
                                    ];
                                } else {
                                    data = [
                                        {
                                            id: '',
                                            text: systemSkillsGroup,
                                            system: true,
                                            children: [...systemSkills],
                                        },
                                        {
                                            id: '',
                                            text: customSkillsGroup,
                                            system: true,
                                            children: [...customSkills],
                                        },
                                    ];
                                }
                            }
                        } else if (customSkills.length === 0 && systemSkills.length > 0) {
                            if (importedSkills.length > 0) {
                                data = [
                                    {
                                        id: '',
                                        text: systemSkillsGroup,
                                        system: true,
                                        children: [...systemSkills],
                                    },
                                    {
                                        id: '',
                                        text: customSkillsGroup,
                                        system: true,
                                        children: [
                                            {
                                                id: '',
                                                text: emptyCustomSkillsGroup,
                                                system: false,
                                            },
                                        ],
                                    },
                                    {
                                        id: '',
                                        text: importedSkillsGroup,
                                        system: true,
                                        children: [...importedSkills],
                                    },
                                ];
                            } else {
                                data = [
                                    {
                                        id: '',
                                        text: systemSkillsGroup,
                                        system: true,
                                        children: [...systemSkills],
                                    },
                                    {
                                        id: '',
                                        text: customSkillsGroup,
                                        system: true,
                                        children: [
                                            {
                                                id: '',
                                                text: emptyCustomSkillsGroup,
                                                system: false,
                                            },
                                        ],
                                    },
                                ];
                            }
                        } else if (customSkills.length === 0 && systemSkills.length === 0) {
                            if (importedSkills.length > 0) {
                                data = [
                                    {
                                        id: '',
                                        text: customSkillsGroup,
                                        system: true,
                                        children: [
                                            {
                                                id: '',
                                                text: emptyCustomSkillsGroup,
                                                system: false,
                                            },
                                        ],
                                    },
                                    {
                                        id: '',
                                        text: importedSkillsGroup,
                                        system: true,
                                        children: [...importedSkills],
                                    },
                                ];
                            } else {
                                data = [
                                    {
                                        id: '',
                                        text: customSkillsGroup,
                                        system: true,
                                        children: [
                                            {
                                                id: '',
                                                text: emptyCustomSkillsGroup,
                                                system: false,
                                            },
                                        ],
                                    },
                                ];
                            }
                        } else {
                            if (importedSkills.length > 0) {
                                data = [
                                    {
                                        id: '',
                                        text: customSkillsGroup,
                                        system: true,
                                        children: [...customSkills],
                                    },
                                    {
                                        id: '',
                                        text: importedSkillsGroup,
                                        system: true,
                                        children: [...importedSkills],
                                    },
                                ];
                            } else {
                                data = [
                                    {
                                        id: '',
                                        text: customSkillsGroup,
                                        system: true,
                                        children: [...customSkills],
                                    },
                                ];
                            }
                        }
                        return data;
                    }

                    function classFormatter(obj) {
                        if (obj.system) return 'select2-system-skill';
                        else return 'select2-custom-skill';
                    }

                    function matchByTransliteral(text, term) {
                        let transliteralValue = Service.transliterate(text);
                        let includes = false;
                        transliteralValue.forEach((item) => {
                            if (term.toLowerCase().indexOf(item) === 0) includes = true;
                        });

                        return term.toLowerCase().indexOf(text.toLowerCase()) === 0 || includes;
                    }

                    function removeExtraSpaces(string = '') {
                        let str = string.split('');
                        for (let i = 0; i < str.length; i++) {
                            if (
                                (str[i] === ' ' && str[i + 1] === ' ' && i !== 0 && i !== str.length - 1) ||
                                (str[i] === ' ' && i === str.length - 1)
                            ) {
                                str.splice(i, 1);
                                i--;
                            }
                        }
                        return str.join('');
                    }

                    String.prototype.replaceAll = function (search, replace) {
                        return this.split(search).join(replace);
                    };

                    scope.getSkillAutocompleterValue = function () {
                        var object = $(element[0]).select2('data');
                        return object != null ? object.text : null;
                    };

                    scope.candidateSetSkillAutocompleterValue = function (val) {
                        $rootScope.initSkillsDirective = true;
                        if (val) {
                            $(element[0]).select2('data', {
                                id: val,
                                text: val,
                            });
                        } else {
                            $(element[0]).select2('data', { id: '', text: '' });
                        }
                    };

                    scope.transliterate = function (text) {
                        text = text.toLowerCase();
                        const arr = [
                            {
                                а: 'a',
                                б: 'b',
                                в: 'v',
                                г: 'g',
                                д: 'd',
                                е: 'e',
                                ё: 'e',
                                ж: 'zh',
                                з: 'z',
                                и: 'i',
                                й: 'i',
                                к: 'k',
                                л: 'l',
                                м: 'm',
                                н: 'n',
                                о: 'o',
                                п: 'p',
                                р: 'r',
                                с: 's',
                                т: 't',
                                у: 'u',
                                ф: 'f',
                                х: 'kh',
                                ц: 'с',
                                ч: 'ch',
                                ш: 'sh',
                                щ: 'shch',
                                ъ: 'ы',
                                yi: 'y',
                                ь: '',
                                э: 'e',
                                ю: 'ju',
                                я: 'ya',
                                ґ: 'g',
                                є: 'ie',
                                і: 'i',
                                ї: 'i',
                                ы: 'yi',
                            },
                            {
                                кс: 'x',
                                зг: 'zgh',
                                льг: 'lg',
                                а: 'a',
                                б: 'b',
                                в: 'w',
                                г: 'g',
                                д: 'd',
                                е: 'e',
                                ё: 'e',
                                ж: 'zh',
                                з: 'z',
                                и: 'i',
                                й: 'j',
                                к: 'k',
                                л: 'l',
                                м: 'm',
                                н: 'n',
                                о: 'o',
                                п: 'p',
                                р: 'r',
                                с: 's',
                                т: 't',
                                у: 'u',
                                ф: 'f',
                                х: 'h',
                                ц: 'тс',
                                ч: 'ch',
                                ш: 'sh',
                                щ: 'shh',
                                ъ: 'ie',
                                ы: 'y',
                                ь: '',
                                э: 'e',
                                ю: 'iu',
                                я: 'ia',
                                ґ: 'g',
                                є: 'ie',
                                і: 'i',
                                ї: 'i',
                            },
                            {
                                кс: 'x',
                                зг: 'zgh',
                                льг: 'lg',
                                дж: 'j',
                                а: 'a',
                                б: 'b',
                                в: 'w',
                                г: 'g',
                                д: 'd',
                                е: 'e',
                                ё: 'e',
                                ж: 'zh',
                                з: 'z',
                                и: 'i',
                                й: 'j',
                                к: 'k',
                                л: 'l',
                                м: 'm',
                                н: 'n',
                                о: 'o',
                                п: 'p',
                                р: 'r',
                                с: 's',
                                т: 't',
                                у: 'u',
                                ф: 'f',
                                х: 'h',
                                ц: 'тс',
                                ч: 'ch',
                                ш: 'sh',
                                щ: 'shh',
                                ъ: 'ie',
                                ы: 'y',
                                ь: '',
                                э: 'e',
                                ю: 'iu',
                                я: 'ia',
                                ґ: 'g',
                                є: 'ie',
                                і: 'i',
                                ї: 'i',
                            },
                            {
                                кс: 'x',
                                зг: 'zgh',
                                льг: 'lg',
                                дж: 'j',
                                а: 'a',
                                б: 'b',
                                в: 'v',
                                г: 'g',
                                д: 'd',
                                е: 'e',
                                ё: 'e',
                                ж: 'zh',
                                з: 'z',
                                и: 'i',
                                й: 'j',
                                к: 'k',
                                л: 'l',
                                м: 'm',
                                н: 'n',
                                о: 'o',
                                п: 'p',
                                р: 'r',
                                с: 's',
                                т: 't',
                                у: 'u',
                                ф: 'f',
                                х: 'h',
                                ц: 'тс',
                                ч: 'ch',
                                ш: 'sh',
                                щ: 'shh',
                                ъ: 'ie',
                                ы: 'y',
                                ь: '',
                                э: 'e',
                                ю: 'iu',
                                я: 'ia',
                                ґ: 'g',
                                є: 'ie',
                                і: 'i',
                                ї: 'i',
                            },
                            {
                                кс: 'x',
                                зг: 'zgh',
                                льг: 'lg',
                                а: 'a',
                                б: 'b',
                                в: 'v',
                                г: 'g',
                                д: 'd',
                                е: 'e',
                                ё: 'e',
                                ж: 'zh',
                                з: 'z',
                                и: 'i',
                                й: 'j',
                                к: 'k',
                                л: 'l',
                                м: 'm',
                                н: 'n',
                                о: 'o',
                                п: 'p',
                                р: 'r',
                                с: 's',
                                т: 't',
                                у: 'u',
                                ф: 'f',
                                х: 'h',
                                ц: 'тs',
                                ч: 'ch',
                                ш: 'sh',
                                щ: 'shh',
                                ъ: 'ie',
                                ы: 'y',
                                ь: '',
                                э: 'e',
                                ю: 'iu',
                                я: 'ia',
                                ґ: 'g',
                                є: 'ie',
                                і: 'i',
                                ї: 'i',
                            },
                            {
                                кс: 'x',
                                зг: 'zgh',
                                а: 'a',
                                б: 'b',
                                в: 'w',
                                г: 'g',
                                д: 'd',
                                е: 'e',
                                ё: 'e',
                                ж: 'zh',
                                з: 'z',
                                и: 'i',
                                й: 'y',
                                к: 'k',
                                л: 'l',
                                м: 'm',
                                н: 'n',
                                о: 'o',
                                п: 'p',
                                р: 'r',
                                с: 's',
                                т: 't',
                                у: 'u',
                                ф: 'f',
                                х: 'h',
                                ц: 'тs',
                                ч: 'ch',
                                ш: 'sh',
                                щ: 'shh',
                                ъ: 'ie',
                                ы: 'y',
                                ь: '',
                                э: 'e',
                                ю: 'iu',
                                я: 'ia',
                                ґ: 'g',
                                є: 'ie',
                                і: 'i',
                                ї: 'i',
                            },
                            {
                                кс: 'h',
                                зг: 'zgh',
                                а: 'a',
                                б: 'b',
                                в: 'v',
                                г: 'g',
                                д: 'd',
                                е: 'e',
                                ё: 'e',
                                ж: 'zh',
                                з: 'z',
                                и: 'i',
                                й: 'y',
                                к: 'k',
                                л: 'l',
                                м: 'm',
                                н: 'n',
                                о: 'o',
                                п: 'p',
                                р: 'r',
                                с: 's',
                                т: 't',
                                у: 'u',
                                ф: 'f',
                                х: 'kh',
                                ц: 'тс',
                                ч: 'ch',
                                ш: 'sh',
                                щ: 'shch',
                                ъ: '',
                                ы: 'y',
                                ь: "'",
                                э: 'e',
                                ю: 'yu',
                                я: 'ja',
                                ґ: 'g',
                                є: 'ie',
                                і: 'i',
                                ї: 'i',
                            },
                            {
                                кс: 'ks',
                                зг: 'zgh',
                                а: 'a',
                                б: 'b',
                                в: 'v',
                                г: 'h',
                                ґ: 'g',
                                д: 'd',
                                е: 'e',
                                ё: 'e',
                                є: 'ie',
                                ж: 'zh',
                                з: 'z',
                                и: 'y',
                                і: 'i',
                                ї: 'i',
                                й: 'y',
                                к: 'k',
                                л: 'l',
                                м: 'm',
                                н: 'n',
                                о: 'o',
                                п: 'p',
                                р: 'r',
                                с: 's',
                                т: 't',
                                у: 'u',
                                ф: 'f',
                                х: 'h',
                                ц: 'c',
                                ч: 'ch',
                                ш: 'sh',
                                ъ: '',
                                ы: 'y',
                                ь: "'",
                                э: 'e',
                                ю: 'yu',
                                я: 'ya',
                                щ: 'sc',
                            },
                            {
                                shch: 'щ',
                                yu: 'ю',
                                ij: 'ій',
                                zh: 'ж',
                                ch: 'ч',
                                sh: 'ш',
                                zgh: 'зг',
                                ya: 'ья',
                                ia: 'я',
                                a: 'а',
                                b: 'б',
                                v: 'в',
                                g: 'г',
                                d: 'д',
                                e: 'е',
                                z: 'з',
                                i: 'і',
                                j: 'й',
                                k: 'к',
                                l: 'л',
                                m: 'м',
                                n: 'н',
                                o: 'о',
                                p: 'п',
                                r: 'р',
                                s: 'с',
                                t: 'т',
                                u: 'у',
                                f: 'ф',
                                kh: 'х',
                                tc: 'ц',
                                y: 'й',
                                x: 'кс',
                                c: 'с',
                                q: 'к',
                                w: 'в',
                                h: 'х',
                                "'": 'ь',
                            },
                            {
                                shch: 'щ',
                                lg: 'льг',
                                yu: 'ю',
                                ij: 'ий',
                                zh: 'ж',
                                ch: 'ч',
                                sh: 'ш',
                                zgh: 'зг',
                                ya: 'я',
                                ia: 'я',
                                a: 'а',
                                b: 'б',
                                v: 'в',
                                g: 'г',
                                d: 'д',
                                e: 'е',
                                z: 'з',
                                i: 'и',
                                j: 'й',
                                k: 'к',
                                l: 'л',
                                m: 'м',
                                n: 'н',
                                o: 'о',
                                p: 'п',
                                r: 'р',
                                s: 'с',
                                t: 'т',
                                u: 'у',
                                f: 'ф',
                                kh: 'х',
                                tc: 'ц',
                                y: 'ы',
                                x: 'кс',
                                c: 'с',
                                q: 'к',
                                w: 'в',
                                h: 'х',
                                "'": 'ь',
                            },
                            {
                                zh: 'ж',
                                yu: 'ю',
                                ch: 'ч',
                                sh: 'ш',
                                zgh: 'зг',
                                ya: 'я',
                                ia: 'я',
                                ts: 'ц',
                                j: 'дж',
                                shch: 'щ',
                                sch: 'ш',
                                tc: 'ц',
                                tz: 'ц',
                                ju: 'ю',
                                a: 'а',
                                b: 'б',
                                g: 'г',
                                d: 'д',
                                e: 'э',
                                z: 'з',
                                i: 'и',
                                q: 'к',
                                x: 'кс',
                                k: 'к',
                                l: 'л',
                                m: 'м',
                                n: 'н',
                                o: 'о',
                                p: 'п',
                                r: 'р',
                                s: 'с',
                                t: 'т',
                                u: 'у',
                                f: 'ф',
                                h: 'х',
                                y: 'й',
                                w: 'в',
                                v: 'в',
                                c: 'к',
                                yi: 'ы',
                                ij: 'ий',
                                "'": 'ь',
                            },
                            {
                                zh: 'ж',
                                yu: 'ю',
                                ch: 'ч',
                                sh: 'ш',
                                zgh: 'зг',
                                ya: 'я',
                                ia: 'я',
                                ts: 'ц',
                                j: 'дж',
                                shch: 'щ',
                                sch: 'ш',
                                tc: 'ц',
                                tz: 'ц',
                                iu: 'ю',
                                a: 'а',
                                b: 'б',
                                g: 'г',
                                d: 'д',
                                e: 'е',
                                yo: 'ё',
                                z: 'з',
                                q: 'к',
                                x: 'кс',
                                yi: 'ы',
                                i: 'и',
                                y: 'й',
                                k: 'к',
                                l: 'л',
                                m: 'м',
                                n: 'н',
                                o: 'о',
                                p: 'п',
                                r: 'р',
                                s: 'с',
                                t: 'т',
                                u: 'у',
                                f: 'ф',
                                h: 'х',
                                w: 'в',
                                v: 'в',
                                c: 'к',
                                "'": 'ь',
                            },
                            {
                                zh: 'ж',
                                ch: 'ч',
                                eh: 'э',
                                sh: 'ш',
                                zgh: 'зг',
                                ya: 'я',
                                ia: 'я',
                                ts: 'ц',
                                j: 'дж',
                                shch: 'щ',
                                sch: 'ш',
                                tc: 'ц',
                                tz: 'ц',
                                iu: 'ю',
                                a: 'а',
                                b: 'б',
                                g: 'г',
                                d: 'д',
                                e: 'ё',
                                z: 'з',
                                q: 'к',
                                x: 'кс',
                                yi: 'ы',
                                i: 'и',
                                y: 'ы',
                                k: 'к',
                                l: 'л',
                                m: 'м',
                                n: 'н',
                                o: 'о',
                                p: 'п',
                                r: 'р',
                                s: 'с',
                                t: 'т',
                                u: 'у',
                                f: 'ф',
                                h: 'х',
                                w: 'в',
                                v: 'в',
                                c: 'ц',
                                "'": 'ь',
                            },
                            {
                                shch: 'щ',
                                yu: 'ю',
                                ij: 'ій',
                                zh: 'ж',
                                ch: 'ч',
                                sh: 'ш',
                                zgh: 'зг',
                                ya: 'ья',
                                ia: 'я',
                                a: 'а',
                                b: 'б',
                                v: 'в',
                                g: 'г',
                                d: 'д',
                                e: 'ё',
                                z: 'з',
                                i: 'і',
                                j: 'й',
                                k: 'к',
                                l: 'л',
                                m: 'м',
                                n: 'н',
                                o: 'о',
                                p: 'п',
                                r: 'р',
                                s: 'с',
                                t: 'т',
                                u: 'у',
                                f: 'ф',
                                kh: 'х',
                                tc: 'ц',
                                y: 'й',
                                x: 'кс',
                                c: 'с',
                                q: 'к',
                                w: 'в',
                                h: 'х',
                                "'": 'ь',
                            },
                        ];
                        let arrResults = [];
                        arr.forEach((item) => {
                            let currText = text;
                            Object.keys(item).forEach((i) => {
                                currText = currText.replaceAll(i, item[i]);
                            });
                            arrResults.push(currText);
                        });

                        return arrResults;
                    };
                    scope.$watch('skill', function (val) {
                        if (val) {
                            let text = val[0].toUpperCase() + val.slice(1);
                            $(element[0]).select2('data', {
                                id: val,
                                text: text,
                            });
                        } else {
                            $(element[0]).select2('val', '');
                        }
                    });

                    $rootScope.$on('putSkills', function (e, data) {
                        initSelect2(data.skills, data.customSkills);
                    });
                },
            };
        },
    ]);
})();
