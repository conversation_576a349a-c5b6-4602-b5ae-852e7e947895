var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives.directive('customFieldAutoCompleter', [
        '$rootScope',
        '$filter',
        '$translate',
        'serverAddress',
        '$timeout',
        function ($rootScope, $filter, $translate, serverAddress, $timeout) {
            return {
                restrict: 'EA',
                scope: {
                    field: '=',
                    onReset: '=',
                },
                replace: true,
                link: function ($scope, element, attrs) {
                    $scope.setCustomFieldAutocompleterValue = function (val) {
                        if (val != undefined) {
                            $(element[0]).select2('data', {
                                id: val,
                                text: val,
                            });
                        } else {
                            $(element[0]).select2('data', { id: '', text: '' });
                        }
                    };
                    $scope.setCustomFieldAutocompleterPlaceholderValue = function () {
                        $(element[0]).select2('val', '');
                        $(element[0]).val('').trigger('change');
                    };
                    $scope.getCustomFieldAutocompleterValue = function () {
                        var object = $(element[0]).select2('data');
                        return object != null ? object.text : null;
                    };

                    var inputText = '';
                    let translatedPositions = false;
                    $rootScope.$on('$translateChangeSuccess', function () {
                        initSelect2();
                    });
                    $rootScope.$on('resetCustomFieldValue', (e, data) => {
                        if (data && data.id === $scope.field.id) {
                            $(element[0]).select2('val', '');
                            $(element[0]).val('').trigger('change');
                        }
                    });

                    if (!translatedPositions) {
                        initSelect2();
                    }

                    function initSelect2() {
                        translatedPositions = true;
                        $(element[0])
                            .select2({
                                placeholder: $scope.field.placeholder,
                                allowClear: true,
                                formatInputTooShort: function () {
                                    return '' + $filter('translate')('Please enter 2 characters') + '';
                                },
                                formatNoMatches: function (term) {
                                    return (
                                        "<div class='select2-result-label' style='cursor: s-resize;'><span class='select2-match'></span>" +
                                        $filter('translate')('Enter a source of this candidate') +
                                        '</div>'
                                    );
                                },
                                createSearchChoice: function (term, data) {
                                    if (
                                        $(data).filter(function () {
                                            return this.text.localeCompare(term) === 0;
                                        }).length === 0
                                    ) {
                                        inputText = term;
                                        return { id: term, text: term };
                                    }
                                },
                                ajax: {
                                    url: serverAddress + '/candidate/autocompleteCustomField',
                                    dataType: 'json',
                                    crossDomain: true,
                                    type: 'POST',
                                    data: function (term, page) {
                                        return {
                                            text: $translate.instant('no_value'),
                                            fieldId: $scope.field.id,
                                        };
                                    },
                                    results: function (data, page) {
                                        var result = [];
                                        result.push({
                                            text: $translate.instant('no_value'),
                                            id: 'no_value',
                                        });
                                        return {
                                            results: result,
                                        };
                                    },
                                },
                                dropdownCssClass: 'bigdrop',
                            })
                            .on('change', function (e) {
                                const autocompleterValue = $(element[0]).select2('data')
                                    ? $(element[0]).select2('data').text
                                    : '';
                                if (inputText.length > 0) {
                                    $(element[0]).select2('data', {
                                        id: inputText,
                                        text: removeExtraSpaces(inputText),
                                    });
                                }
                                if (e.removed) {
                                    if ($rootScope.searchNullCustomFields) {
                                        $rootScope.searchNullCustomFields = $rootScope.searchNullCustomFields.filter(
                                            (item) => item !== $scope.field.placeholder,
                                        );
                                    }
                                    if (
                                        $rootScope.searchNullValues &&
                                        $rootScope.searchNullValues.length &&
                                        $rootScope.searchNullCustomFields &&
                                        $rootScope.searchNullCustomFields.length === 0
                                    ) {
                                        $rootScope.searchNullValues = $rootScope.searchNullValues.filter(
                                            (item) => item !== 'customFields',
                                        );
                                    }
                                }
                                if ($(element[0]).select2('data')) {
                                    $(element[0]).select2('data', {
                                        id: inputText,
                                        text: removeExtraSpaces(autocompleterValue),
                                    });
                                    $scope.field.value = removeExtraSpaces(autocompleterValue);
                                    $scope.setCustomFieldAutocompleterValue($scope.field.value);
                                } else {
                                    $scope.field.value = removeExtraSpaces(autocompleterValue);
                                }
                                $timeout(() => {
                                    if (!$scope.$$phase) {
                                        $scope.$apply();
                                    }
                                });

                                !$scope.$$phase || $scope.$apply();
                            })
                            .on('select2-close', function (e) {
                                if (inputText.length > 0) {
                                    $(element[0]).select2('data', {
                                        id: inputText,
                                        text: removeExtraSpaces(inputText),
                                    });
                                    $scope.field.value = $scope.field.placeholder;
                                    $scope.setCustomFieldAutocompleterPlaceholderValue();
                                }
                            })
                            .on('select2-selecting', function (e) {
                                inputText = '';
                                if (e.object.id === 'no_value') {
                                    if ($rootScope.searchNullValues && $rootScope.searchNullValues.length) {
                                        if (!$rootScope.searchNullValues.includes('customFields')) {
                                            $rootScope.searchNullValues.push('customFields');
                                        }
                                    } else {
                                        $rootScope.searchNullValues = ['customFields'];
                                    }
                                    if (!$rootScope.searchNullCustomFields) {
                                        $rootScope.searchNullCustomFields = [$scope.field.placeholder];
                                    } else $rootScope.searchNullCustomFields.push($scope.field.placeholder);
                                } else {
                                    if ($rootScope.searchNullCustomFields) {
                                        $rootScope.searchNullCustomFields = $rootScope.searchNullCustomFields.filter(
                                            (item) => item !== $scope.field.placeholder,
                                        );
                                    }
                                    if (
                                        $rootScope.searchNullValues &&
                                        $rootScope.searchNullValues.length &&
                                        $rootScope.searchNullCustomFields &&
                                        $rootScope.searchNullCustomFields.length === 0
                                    ) {
                                        $rootScope.searchNullValues = $rootScope.searchNullValues.filter(
                                            (item) => item !== 'customFields',
                                        );
                                    }
                                }
                            })
                            .on('select2-open', function () {
                                if ($(element[0]).select2('data'))
                                    $('#select2-drop input').val($(element[0]).select2('data').text);
                            });
                    }

                    function removeExtraSpaces(string) {
                        let str = string.split('');
                        for (let i = 0; i < str.length; i++) {
                            if (
                                (str[i] === ' ' && str[i + 1] === ' ' && i !== 0 && i !== str.length - 1) ||
                                (str[i] === ' ' && i === str.length - 1)
                            ) {
                                str.splice(i, 1);
                                i--;
                            }
                        }
                        return str.join('');
                    }
                },
            };
        },
    ]);
})();
