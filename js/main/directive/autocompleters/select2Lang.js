var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives.directive('select2Lang', [
        '$filter',
        'serverAddress',
        'Service',
        'notificationService',
        '$window',
        '$rootScope',
        '$timeout',
        function ($filter, serverAddress, Service, notificationService, $window, $rootScope, $timeout) {
            return {
                restrict: 'EA',
                replace: true,
                link: function ($scope, element) {
                    function format(item) {
                        return item.text;
                    }

                    let previousSelectedLanguages = [];
                    $scope.setLangs = function (langs, checked) {
                        $scope.addedLang = $scope.addedLang || [];
                        if (checked) {
                            angular.forEach(checked, function (checkedLang) {
                                angular.forEach(langs, function (lang, index) {
                                    if (checkedLang.name.toLowerCase() === lang.key.toLowerCase()) {
                                        langs.splice(index, 1);
                                    }
                                });
                            });
                            $scope.SaveChecked = checked;
                        }
                        $scope.SaveLangs = langs;
                        $scope.SaveChecked = checked;
                        var results = [];
                        var newarr = [];
                        var unique = {};
                        angular.forEach(langs, function (val) {
                            if (!unique[val.key]) {
                                newarr.push(val);
                                results.push({
                                    id: val.languageId == undefined ? val.key : val.languageId,
                                    text: $filter('translateLangs')(val.translation),
                                    level: 'undefined',
                                });
                                unique[val.key] = { name: val.key };
                            }
                        });
                        $scope.results = results;
                        var myListener = $scope.$on('addedLang', function (event, data) {
                            if (data) {
                                angular.forEach(newarr, function (nval, index) {
                                    angular.forEach(data, function (val) {
                                        if (nval.name === val.text) {
                                            results.splice(index, 1);
                                        }
                                    });
                                });
                                $scope.addedLang = data;
                                $('.addingLangs').show();
                                $scope.$apply();
                            }
                        });
                        $scope.$on('$destroy', myListener);
                        var inputText = '';

                        $(element[0])
                            .select2({
                                tags: results,
                                tokenSeparators: [',', ' '],
                                data: {
                                    results: results,
                                    text: function (item) {
                                        return item.text;
                                    },
                                },
                                createSearchChoice: function (term, data) {
                                    if (
                                        $(data).filter(function () {
                                            return this.text.localeCompare(term) === 0;
                                        }).length === 0
                                    ) {
                                        inputText = term;
                                        var inputElement = $('.select-lang-container .select2-input input');
                                        inputElement.attr('placeholder', $filter('translate')('Choose/add'));
                                        return { id: term, text: term };
                                    }
                                },
                                formatSelection: format,
                                formatResult: format,
                                formatResultCssClass: function (data, container) {
                                    return data.text;
                                },
                            })
                            .on('change.select2', function (e) {
                                if (e.added != undefined) {
                                    let langInList = false;
                                    let allAbsentLangs = $scope.results;
                                    if ($scope.addedLang.length) {
                                        angular.forEach($scope.addedLang, function (prevLang) {
                                            angular.forEach(allAbsentLangs, function (absLang, index) {
                                                if (prevLang.id == absLang.id) {
                                                    allAbsentLangs.splice(index, 1);
                                                }
                                            });
                                        });
                                    }

                                    angular.forEach(allAbsentLangs, function (lang) {
                                        if (e.added.text.toLowerCase() === lang.text.toLowerCase()) {
                                            langInList = true;
                                        }
                                    });
                                    if (!langInList) {
                                        let currLangList = $scope.getSelect2Lang();
                                        let index = currLangList.indexOf(e.added);
                                        currLangList.splice(index, 1);
                                        $scope.setSelect2Lang(currLangList);
                                        angular.forEach(currLangList, function (itemLang) {
                                            if (itemLang.text.toLowerCase() === e.added.text.toLowerCase()) {
                                                notificationService.success(
                                                    $filter('translate')('This language has already been added'),
                                                );
                                            }
                                        });
                                    } else {
                                        addLanguage(e);
                                    }
                                }
                                if (
                                    $scope.type != 'merge' &&
                                    $scope.type != 'Vacancy add' &&
                                    $scope.type != 'Vacancy edit'
                                ) {
                                    $scope.candidate.languages = $scope.getSelect2Lang();
                                }
                                var inputElement = $('.select-lang-container .select2-search-field input');
                                inputElement.attr('placeholder', $filter('translate')('Choose/add'));

                                if (e.removed) {
                                    angular.forEach($scope.addedLang, function (nval) {
                                        if (e.removed.id == nval.id) {
                                            var deleteFromArray = $scope.addedLang.indexOf(nval);
                                            if ($scope.results.indexOf(e.removed) === -1)
                                                $scope.results.push(e.removed);
                                            if (deleteFromArray > -1) {
                                                $scope.addedLang.splice(deleteFromArray, 1);
                                            }
                                            $scope.$broadcast('addedLang', $scope.addedLang);
                                        } else {
                                            let isExist = false;

                                            results.forEach((lang) => {
                                                if (lang.text === e.removed.text) isExist = true;
                                            });

                                            if (!isExist) results.unshift(e.removed);
                                            $scope.$apply();
                                        }
                                    });
                                    previousSelectedLanguages = $scope.addedLang;
                                }
                            })
                            .on('select2-selecting', function (e) {
                                inputText = '';
                            });
                    };
                    $rootScope.$on('$translateChangeSuccess', () =>
                        $timeout(() => {
                            if ($scope.SaveChecked) {
                                $scope.setLangs($scope.SaveLangs, $scope.SaveChecked);
                            } else {
                                $scope.setLangs($scope.SaveLangs);
                            }
                        }, 0),
                    );

                    function addLanguage(event) {
                        previousSelectedLanguages = $scope.getSelect2Lang();
                        $scope.addedLang.push(event.added);
                        $scope.$broadcast('addedLang', $scope.addedLang);
                    }

                    $scope.getSelect2Lang = function () {
                        var val = $(element[0]).select2('data');
                        $scope.addedLang2 = val;
                        return val != null ? val : null;
                    };
                    $scope.setSelect2Lang = function (val) {
                        var inputElement = $('.select-lang-container .select2-search-field input');
                        inputElement.attr('placeholder', $filter('translate')('Choose/add'));
                        inputElement.attr('maxlength', '20');
                        if (val != undefined) {
                            $scope.addedLang = val;
                            $scope.$$phase || $scope.$apply();
                            $(element[0]).select2('data', val);
                            previousSelectedLanguages = val;
                        } else {
                            $(element[0]).select2('data', { id: '', text: '' });
                        }
                    };
                },
            };
        },
    ]);
})();
