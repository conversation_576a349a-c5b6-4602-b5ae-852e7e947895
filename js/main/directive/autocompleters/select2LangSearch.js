var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives.directive('select2LangSearch', [
        '$filter',
        'serverAddress',
        'Service',
        function ($filter, serverAddress, Service) {
            return {
                restrict: 'EA',
                replace: true,
                link: function ($scope, element, attrs) {
                    $scope.setLangs = function (langs) {
                        Array.prototype.removeDuplicates = function () {
                            var temp = [];
                            label: for (i = 0; i < this.length; i++) {
                                for (var j = 0; j < temp.length; j++) {
                                    //check duplicates
                                    if (temp[j] == this[i])
                                        //skip if already present
                                        continue label;
                                }
                                temp[temp.length] = this[i];
                            }
                            return temp;
                        };
                        $scope.fullLangs = langs.concat(Service.lang());
                        $scope.fullLangs2 = $scope.fullLangs.removeDuplicates();
                        //$scope.cutFullLangs = $scope.fullLangs2.slice(0,)

                        $(element[0])
                            .select2({
                                tags: $scope.fullLangs2,
                                tokenSeparators: [','],
                            })
                            .on('change', function (e) {
                                if (e.removed) {
                                } else {
                                }
                            });
                    };
                    $scope.getSelect2Lang = function () {
                        var val = $(element[0]).select2('val');
                        return val != null ? val.toString() : null;
                    };
                    $scope.setSelect2Lang = function (val) {
                        if (val != undefined) {
                            $(element[0]).select2('val', val);
                        }
                    };
                },
            };
        },
    ]);
})();
