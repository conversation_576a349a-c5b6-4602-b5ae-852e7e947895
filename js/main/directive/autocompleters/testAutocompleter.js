var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives.directive('testAutocompleter', [
        '$filter',
        'serverAddress',
        '$rootScope',
        function ($filter, serverAddress, $rootScope) {
            return {
                restrict: 'EA',
                replace: true,
                link: function ($scope, element, attrs) {
                    $scope.addedTest = {};
                    var inputText = '';

                    $(element[0])
                        .select2({
                            placeholder: $filter('translate')('Choose test'),
                            //minimumInputLength: 2,
                            allowClear: true,
                            //formatInputTooShort: function () {
                            //    return ""+ $filter('translate')('Please enter 2 characters') +"";
                            //},
                            formatNoMatches: function (term) {
                                return (
                                    "<div class='select2-result-label' style='cursor: s-resize;'><span class='select2-match'></span>" +
                                    $filter('translate')('Please enter test title') +
                                    '</div>'
                                );
                            },
                            createSearchChoice: function (term, data) {
                                if (
                                    $(data).filter(function () {
                                        return this.text.localeCompare(term) === 0;
                                    }).length === 0
                                ) {
                                    inputText = term;
                                    return { id: data.id, text: term };
                                }
                            },
                            ajax: {
                                url: serverAddress + '/test/autocompleteTest',
                                dataType: 'json',
                                crossDomain: true,
                                type: 'POST',
                                data: function (term, page) {
                                    return {
                                        text: term.trim(),
                                    };
                                },
                                results: function (data, page) {
                                    var result = [];
                                    angular.forEach(data['objects'], function (val) {
                                        if (val.status != 'D') {
                                            result.push({
                                                id: val.id,
                                                text: val.testName,
                                            });
                                        }
                                    });
                                    return {
                                        results: result,
                                    };
                                },
                            },
                            dropdownCssClass: 'bigdrop',
                        })
                        .on('change', function (e) {
                            if (e.added != undefined) {
                                $scope.addedTest = {
                                    id: e.added.id,
                                    text: e.added.text,
                                };
                                if ($scope.vm) {
                                    // $scope.vm.data.test = $scope.addedTest;
                                    $scope.vm.model.test = $scope.addedTest;
                                }
                                $rootScope.$broadcast('addedTest', $scope.addedTest);
                            } else {
                                if ($scope.vm) {
                                    // $scope.vm.data.test = null;
                                    $scope.vm.model.test = null;
                                }
                            }
                        });
                },
            };
        },
    ]);
})();
