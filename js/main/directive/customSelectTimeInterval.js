var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives.directive('customSelectTimeInterval', [
        'Service',
        '$window',
        '$filter',
        '$timeout',
        '$rootScope',
        function (Service, $window, $filter, $timeout, $rootScope) {
            return {
                restrict: 'E',
                scope: {
                    options: '=options',
                    model: '=?model',
                    path: '=path',
                    idPath: '=idPath',
                    placeholder: '=placeholder',
                    disabled: '=disabled',
                    method: '=method',
                    type: '=type',
                    removeMethod: '=removeMethod',
                    addMethod: '=addMethod',
                    trackIndex: '=trackIndex',
                    isNoNeedToTranslateOptions: '=?',
                    thereIsNotDefaultValueExist: '=?',
                    needToAccept: '=?',
                    hidePlaceholderFromOptions: '=?hidePlaceholderFromOptions',
                    isRestrictionForOption: '=?isRestrictionForOption',
                    objectIndex: '=?',
                    trackSelectedOptionById: '=?',
                    updateCurrencyValue: '=?',
                    trackSelectedOptionByField: '=?',
                    customStylesOfLabel: '=customStylesOfLabel',
                    trackSelectedOptionByValue: '=?',
                    customTranslate: '=?',
                    fieldId: '=?',
                    fieldValueId: '=?',
                    notSetModel: '=?',
                    valueToMethod: '=?',
                    customOptStyles: '=?',
                    customStatusStyles: '=?',
                    bigFirstLetter: '=?',
                    resetModel: '=?',
                    notShowCount: '=?',
                    placeStringName: '=?',
                    arrayOfMethodParams: '=?',
                },
                link: function (scope, element, attrs) {
                    scope.isOpenOpt = true;
                    if (scope.path === 'fullName') {
                    }
                    scope.onResetModel = function () {
                        if (scope.hasOwnProperty('model')) scope.model = null;
                    };
                    scope.getCountValue = Service.getObjectPropertyValueByPath;
                    scope.getPropertyValue = function (obj = {}, path = '') {
                        if (scope.path === 'fullName') {
                        }
                        if (
                            obj &&
                            obj.value &&
                            obj.value === 'e00_no_experience' &&
                            scope.placeStringName === 'skillAdvancedSearch'
                        ) {
                            return 'advancedSearchSkills.e00_no_experience';
                        } else if (obj && obj.value && obj.value === 'e00_no_experience') {
                            return 'experience_assoc.e00_no_experience';
                        }

                        if (scope.customTranslate && $rootScope.allLanguages) {
                            const langObj = $rootScope.allLanguages[obj];
                            if (langObj) {
                                return $rootScope.currentLang === 'ru'
                                    ? langObj.russian[0].toUpperCase() + langObj.russian.slice(1)
                                    : $rootScope.currentLang === 'en'
                                    ? langObj.english[0].toUpperCase() + langObj.english.slice(1)
                                    : langObj.ukrainian[0].toUpperCase() + langObj.ukrainian.slice(1);
                            }
                        }
                        if (scope.bigFirstLetter) {
                            if (typeof obj === 'string') {
                                obj = obj[0].toUpperCase() + obj.slice(1).toLowerCase();
                                obj = replaceCategory(obj);
                            }
                        }
                        if (!path) return obj ? (obj.value ? obj.value : obj) : obj;
                        if (!obj) return null;

                        let prevProp = null;

                        path.split('.').forEach((prop) => {
                            prevProp = prevProp ? prevProp[prop] : obj[prop];
                        });
                        return prevProp || prevProp === false ? prevProp : obj;
                    };

                    scope.setPropertyValue = function (obj = {}, value) {
                        const path = scope.path.split('.');
                        for (let i = 0; i < path.length - 1; i++) {
                            let prop = path[i];
                            if (prop in o) {
                                obj = obj[prop];
                            } else {
                                obj[prop] = {};
                                obj = obj[prop];
                            }
                        }

                        obj[path[path.length - 1]] = value;

                        return obj;
                    };
                    for (let i in scope.options) {
                        if (
                            scope.trackSelectedOptionById &&
                            scope.options[i].id &&
                            angular.equals(scope.options[i].id, scope.model)
                        ) {
                            scope.selectedOpt = scope.options[i].value;
                        } else if (
                            scope.trackSelectedOptionByValue &&
                            scope.options[i].value &&
                            scope.model &&
                            angular.equals(scope.options[i].value, scope.model.value)
                        ) {
                            scope.selectedOpt = scope.options[i];
                        } else if (
                            scope.trackSelectedOptionByField &&
                            scope.options[i][scope.trackSelectedOptionByField] &&
                            scope.model &&
                            angular.equals(
                                scope.options[i][scope.trackSelectedOptionByField],
                                scope.model[scope.trackSelectedOptionByField],
                            )
                        ) {
                            scope.selectedOpt = scope.options[i][scope.trackSelectedOptionByField];
                        } else if (angular.equals(scope.options[i], scope.model)) {
                            scope.selectedOpt = scope.options[i];
                        }
                    }
                    // Select an option
                    scope.selectOpt = function (opt, index) {
                        document.querySelector('.select-caret').style.border = '1px solid #ccc';
                        document.querySelector('.select-caret').style.borderLeft = 'none';
                        if (scope.customStatusStyles) {
                            scope.isGreenStatus = opt === 'open' || opt === 'inwork' || opt === 'completed';
                        }
                        if (scope.updateCurrencyValue) {
                            $rootScope.$emit('updateCurrencyValue', opt);
                        }
                        if (scope.needToAccept) {
                            $rootScope.$emit('modelFromCSNwasReplaced', {
                                oldVal: scope.model,
                                newVal: opt,
                            });
                            $rootScope.$on('replaceCSNModelDecision', (evt, data) => {
                                if (data) {
                                    changeModel(opt, index);
                                } else {
                                    optionsDom.removeClass('active');
                                    backdrop.removeClass('active');
                                }
                            });
                        } else {
                            changeModel(opt, index);
                        }
                        const inputText = document.querySelector('custom-select-time-interval .select-text');
                        const arrow = document.querySelector('custom-select-time-interval .arrow-checkboxes');

                        inputText.style.borderBottomLeftRadius = '5px';
                        arrow.style.borderBottomRightRadius = '5px';
                    };

                    function replaceCategory(category) {
                        const categories = {
                            Backend: 'Back End',
                            C: 'C/C++',
                            Big_data: 'Big Data',
                            Data_scientist: 'Data Scientist',
                            Cyber_security: 'Cyber security',
                            Dba: 'DBA',
                            Dev_ops: 'DevOps',
                            Erp_crm: 'ERP/CRM',
                            Frontend: 'Front End',
                            Full_stack: 'Full Stack',
                            Hr: 'HR in IT',
                            Sales: 'Sales in IT',
                            Gamedev: 'GameDev',
                            Ios_mac: 'iOS/macOS',
                            _net: '.Net',
                            Node: 'Node.js',
                            Product_manager: 'Product Manager',
                            Project_manager: 'Project Manager',
                            Qa: 'QA',
                            Seo: 'SEO',
                            System_administrator: 'System Administrator',
                            Technical_writer: 'Technical Writer',
                            Php: 'PHP',
                        };
                        return categories[category] || category;
                    }
                    function changeModel(opt, index) {
                        if (scope.arrayOfMethodParams) {
                            scope.method(opt, ...scope.arrayOfMethodParams);
                            optionsDom.removeClass('active');
                            backdrop.removeClass('active');
                            return;
                        }
                        if (scope.valueToMethod) {
                            scope.method(scope.valueToMethod, opt);
                            optionsDom.removeClass('active');
                            backdrop.removeClass('active');
                            if (scope.hasOwnProperty('model')) scope.model = opt;
                            return;
                        }
                        if (scope.notSetModel) {
                            scope.method(opt);
                            optionsDom.removeClass('active');
                            backdrop.removeClass('active');
                            return;
                        }
                        if (scope.hasOwnProperty('model')) scope.model = opt;
                        if (scope.method && scope.model && scope.fieldId) {
                            scope.method(scope.model.value, scope.fieldId, scope.fieldValueId);
                            optionsDom.removeClass('active');
                            backdrop.removeClass('active');
                            return;
                        }
                        if (scope.method && scope.trackIndex) {
                            scope.method(index);
                        } else if (
                            scope.method && typeof scope.objectIndex === 'number' ? scope.objectIndex.toString() : false
                        ) {
                            scope.method(opt, scope.objectIndex);
                        } else if (scope.method) {
                            scope.method(null, opt, true);
                        }
                        optionsDom.removeClass('active');
                        backdrop.removeClass('active');
                    }

                    scope.removeOpt = function (opt, index) {
                        if (scope.removeMethod && scope.trackIndex) {
                            scope.removeMethod(index);
                        } else if (scope.removeMethod) scope.removeMethod(opt);
                    };

                    scope.addOpt = function () {
                        scope.addMethod();
                    };

                    scope.setPlaceholder = function () {
                        if (scope.hasOwnProperty('model')) scope.model = null;
                        close();
                        if (scope.fieldId) scope.method('', scope.fieldId, scope.fieldValueId);
                    };
                    scope.$watch(
                        'model',
                        function (newVal) {
                            for (let i in scope.options) {
                                if (
                                    scope.trackSelectedOptionById &&
                                    scope.options[i].id &&
                                    angular.equals(scope.options[i].id, newVal)
                                ) {
                                    scope.selectedOpt = scope.options[i].value;
                                } else if (
                                    scope.trackSelectedOptionByValue &&
                                    scope.options[i].value &&
                                    angular.equals(scope.options[i].value, newVal.value)
                                ) {
                                    scope.selectedOpt = scope.options[i];
                                } else if (
                                    scope.trackSelectedOptionByField &&
                                    scope.options[i][scope.trackSelectedOptionByField] &&
                                    scope.model &&
                                    angular.equals(scope.options[i][scope.trackSelectedOptionByField], scope.model)
                                ) {
                                    scope.selectedOpt = scope.options[i][scope.trackSelectedOptionByField];
                                } else if (angular.equals(scope.options[i], newVal)) {
                                    scope.selectedOpt = scope.options[i];
                                }
                            }
                        },
                        true,
                    );

                    var labelDom = element.find('.select-label'),
                        optionsDom = element.find('.select-ops'),
                        backdrop = element.find('.select-backdrop');

                    labelDom.on('click', open);
                    backdrop.on('click', close);

                    if (scope.placeholder && !scope.model) scope.setPlaceholder();

                    function open() {
                        if (!scope.disabled) {
                            optionsDom.toggleClass('active');
                            backdrop.toggleClass('active');
                        }

                        const inputText = document.querySelector('custom-select-time-interval .select-text');
                        const arrow = document.querySelector('custom-select-time-interval .arrow-checkboxes');
                        inputText.style.borderBottomLeftRadius = '0px';
                        arrow.style.borderBottomRightRadius = '0px';

                        scope.isOpenOpt = !scope.isOpenOpt;
                    }
                    $rootScope.removeError = function () {
                        $rootScope.wrongRole = false;
                    };
                    function close() {
                        optionsDom.removeClass('active');
                        backdrop.removeClass('active');
                        labelDom.removeClass('disabled');

                        const inputText = document.querySelector('custom-select-time-interval .select-text');
                        const arrow = document.querySelector('custom-select-time-interval .arrow-checkboxes');

                        inputText.style.borderBottomLeftRadius = '5px';
                        arrow.style.borderBottomRightRadius = '5px';
                    }

                    $(element).bind('open', open);
                    $(element).bind('close', close);

                    scope.$watch('disabled', () => {
                        if (scope.disabled) labelDom.addClass('disabled');
                        else labelDom.removeClass('disabled');
                    });
                },
                template: `<div class="select-label custom-new" tabindex="0">
                        <span style="position:relative" ng-show="model" class="select-label-text select-text" ng-class="{'select-label-text-custom': customStylesOfLabel }" ng-show="!isNoNeedToTranslateOptions  && !customOptStyles && !customStatusStyles">{{getPropertyValue(model || selectedOpt, path) | translate: selectedOpt.translateValues}}<img ng-if="resetModel" ng-click="onResetModel();$event.stopPropagation();" style="height:10px;width:10px;position: absolute;right: 15px;top:11px" src="images/sprite/close-icon.svg" alt=""></span> 
                        <span ng-show="model" class="select-label-text" ng-class="{'greenStatus':selectedOpt === 'open'|| model === 'open' || selectedOpt === 'inwork' || model === 'inwork' || selectedOpt === 'completed' || model === 'completed' || selectedOpt === 'future'|| model === 'future' || selectedOpt === 'in_work'|| model === 'in_work' || selectedOpt === 'all_done'|| model === 'all_done','yellowStatus':!(selectedOpt === 'open'|| model === 'open' || selectedOpt === 'inwork' || model === 'inwork' || selectedOpt === 'completed' || model === 'completed' || selectedOpt === 'future'|| model === 'future' || selectedOpt === 'in_work'|| model === 'in_work' || selectedOpt === 'all_done'|| model === 'all_done')}" ng-if="!isNoNeedToTranslateOptions  && !customOptStyles && customStatusStyles">{{getPropertyValue(selectedOpt || model, path) | translate: selectedOpt.translateValues}}</span> 
                        <span ng-show="model" class="select-label-text" ng-class="{'select-label-text-custom': customStylesOfLabel,'lowPriorityStyles':selectedOpt ==='low' || model ==='low','mediumPriorityStyles':selectedOpt ==='medium' || model ==='medium','highPriorityStyles':selectedOpt ==='top' || model ==='high' }" ng-if="!isNoNeedToTranslateOptions && customOptStyles">{{getPropertyValue(selectedOpt || model, path) | translate: selectedOpt.translateValues}}</span> 
                        <span ng-show="model" class="select-label-text" ng-if="isNoNeedToTranslateOptions && thereIsNotDefaultValueExist && getPropertyValue(selectedOpt || model, path) !== 'Default'">{{getPropertyValue(selectedOpt || model, path)}}</span>
                        <span ng-show="model" class="select-label-text"   ng-if="isNoNeedToTranslateOptions && thereIsNotDefaultValueExist && getPropertyValue(selectedOpt || model, path) === 'Default'">{{getPropertyValue(selectedOpt || model, path) | translate: selectedOpt.translateValues}}</span>
                        <span ng-show="!model" class="select-label-text placeholder requiredExperience" ng-class="{'select-label-text-custom': customStylesOfLabel }" translate="{{placeholder}}"></span>
                        <span class="select-caret arrow-checkboxes">
                            <span></span> 
                        </span>
                    </div>
                    <div class="select-backdrop custom-new"></div>
                    <div style="border-radius: 0px!important;overflow-x: hidden;" class="select-ops custom-new">
                        <div ng-if="placeholder && type !== 'country' && !hidePlaceholderFromOptions" ng-click="setPlaceholder()" translate="{{placeholder}}"></div>
                        <div ng-repeat="o in options track by $index" class="list-ops">
                            <div ng-class="{'greenStatus': o.value === 'approved'}" ng-click="selectOpt(o, $index);" class="list-item-ops" ng-if="!isNoNeedToTranslateOptions && !customOptStyles && !customStatusStyles"><span style="margin-right:5px" ng-if="o.userId===$root.me.userId">({{ "Me"| translate}})</span>{{getPropertyValue(o, path) | translate: o.translateValues}} <span ng-if="idPath">(ID:{{getCountValue(o, idPath)}})</span></div>
                            <div ng-class="{'greenStatus':o === 'open' || o === 'inwork' || o === 'completed' || o === 'future' || o === 'in_work' || o === 'all_done','yellowStatus':o !== 'open' && o !== 'inwork' && o !== 'completed' && o !== 'future' && o !== 'in_work' && o !== 'all_done'}" ng-click="selectOpt(o, $index);" class="list-item-ops"  ng-if="!isNoNeedToTranslateOptions && !customOptStyles && customStatusStyles">{{getPropertyValue(o, path) | translate: o.translateValues}}</div>
                            <div ng-class="{'lowPriorityStyles':o ==='low','mediumPriorityStyles':o ==='medium','highPriorityStyles':o ==='top'}" ng-click="selectOpt(o, $index);" class="list-item-ops"  ng-if="!isNoNeedToTranslateOptions && customOptStyles">{{getPropertyValue(o, path) | translate: o.translateValues}}</div>
                            <div ng-click="selectOpt(o, $index);" class="list-item-ops"  ng-if="isNoNeedToTranslateOptions && thereIsNotDefaultValueExist && getPropertyValue(o, path) !== 'Default'">{{getPropertyValue(o, path)}}</div>
                            <div ng-click="selectOpt(o, $index);" class="list-item-ops"  ng-if="isNoNeedToTranslateOptions && thereIsNotDefaultValueExist && getPropertyValue(o, path) === 'Default'">{{getPropertyValue(o, path) | translate: o.translateValues}}</div>
                            <div ng-show="(o.count || o.count == 0) && !notShowCount" class="white-space-nowrap self-vertical-center">{{o.count}}</div>
                            <img ng-if="removeMethod" class="trash" src="/images/sprite/icons/delete-forever.svg" ng-click="removeOpt(o, $index)" alt />
                        </div>
                       <hr ng-if="$root.testRefusalStatusToChangeStage.length > 0" style="margin-top: 7px;position: relative;left: 4px;margin-bottom: 7px;width: 96%;border-top: 1px solid #b9b4b4;">
                        <div class="list-ops" style="color: #EF5350" ng-if="$root.testRefusalStatusToChangeStage.length > 0" ng-repeat="r in $root.testRefusalStatusToChangeStage track by $index">
                            <div class="list-item-ops" ng-click="selectOpt(r, $index);" class="list-item-ops"  ng-if="!isNoNeedToTranslateOptions && !customOptStyles && !customStatusStyles"><span style="margin-right:5px" ng-if="o.userId===$root.me.userId">({{ "Me"| translate}})</span>{{getPropertyValue(r, path) | translate: r.translateValues}}</div>
                        </div>                        
                        <div ng-if="addMethod" class="list-ops" ng-click="addOpt()">
                            <div class="list-item-ops"><img src="/images/sprite/custom-fields/add.svg" alt/>{{"Add Card" | translate}}</div>
                        </div>
                    </div>`,
            };
        },
    ]);
})();
