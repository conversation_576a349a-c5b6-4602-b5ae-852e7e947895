(function () {
    directives.directive('customPopover', [
        '$rootScope',
        '$timeout',
        '$window',
        function ($rootScope, $timeout, $window) {
            return {
                restrict: 'E',
                scope: {
                    parentScope: '=',
                    candidate: '=',
                    item: '=',
                    turnOffHoverState: '=',
                    popoverTemplate: '@',
                    disabled: '=',
                    popoverHtml: '@',
                    classes: '@',
                    placement: '=',
                },
                transclude: true,
                link: function (scope, element) {
                    function adjustPlacement() {
                        $timeout(function () {
                            const popoverElement = $('.popover');
                            if (popoverElement.length) {
                                const popoverRect = popoverElement[0].getBoundingClientRect();
                                const windowWidth = $window.innerWidth;
                                const windowHeight = $window.innerHeight;

                                let newPlacement = scope.placement;

                                if (popoverRect.right > windowWidth) {
                                    newPlacement = 'top-left';
                                } else if (popoverRect.left < 0) {
                                    newPlacement = 'top-right';
                                }

                                if (popoverRect.bottom > windowHeight) {
                                    newPlacement = 'top-left';
                                } else if (popoverRect.top < 0) {
                                    newPlacement = 'bottom-left';
                                }

                                if (newPlacement !== scope.placement) {
                                    scope.placement = newPlacement;
                                    scope.$digest();
                                }
                            }
                        });
                    }

                    function handleHover() {
                        $(element).bind('mouseover', function () {
                            scope.popoverIsOpen = true;
                            scope.$digest();
                            $timeout(function () {
                                $('.custom-popover').css('opacity', 1.0);
                                adjustPlacement(); // Adjust placement after popover is shown
                            }, 100);
                        });

                        function enter() {
                            $('.popover').on('mouseleave', function () {
                                scope.popoverIsOpen = false;
                                scope.$digest();
                            });
                        }

                        function leave() {
                            if (scope.turnOffHoverState) {
                                scope.popoverIsOpen = false;
                                scope.$digest();
                            } else if ($('.popover').length) {
                                if (!$('.popover').is(':hover')) {
                                    scope.popoverIsOpen = false;
                                    scope.$digest();
                                }
                            }
                        }

                        element.on('mouseenter', enter);
                        element.on('mouseleave', leave);
                    }

                    handleHover();
                },
                template: `<div
                                ng-click="$event.stopPropagation()"
                                uib-popover-template="{{ popoverTemplate }}"
                                popover-class="{{classes}}"
                                uib-popover-html="popoverHtml"
                                popover-append-to-body="true"
                                popover-placement="{{placement ? placement : 'auto'}}"
                                popover-trigger="'none'"
                                popover-is-open="disabled ? false : popoverIsOpen"
                                role="tooltip"
                            >
                                <ng-transclude></ng-transclude>
                            </div>`,
            };
        },
    ]);
})();
