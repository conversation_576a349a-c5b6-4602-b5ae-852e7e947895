(function () {
    directives
        .directive('mailingCandidateAutocompleter', [
            '$filter',
            'serverAddress',
            function ($filter, serverAddress) {
                return {
                    restrict: 'EA',
                    replace: true,
                    link: function (scope, element, attrs) {
                        if ($(element[0])) {
                            element.select2({
                                placeholder: $filter('translate')('select candidate'),
                                minimumInputLength: 2,
                                ajax: {
                                    url: serverAddress + '/candidate/autocomplete',
                                    dataType: 'json',
                                    crossDomain: true,
                                    quietMillis: 500,
                                    type: 'POST',
                                    data: function (term, page) {
                                        return {
                                            name: term.trim(),
                                            withPersonalContacts: true,
                                        };
                                    },
                                    results: function (data, page) {
                                        var results = [];
                                        if (data['objects'] !== undefined) {
                                            angular.forEach(data['objects'], function (item) {
                                                if (
                                                    !scope.$parent.candidatesForMailing.some((candidate) => {
                                                        return candidate.localId === item.localId;
                                                    })
                                                ) {
                                                    results.push({
                                                        candidateId: item.candidateId,
                                                        id: item.localId,
                                                        text: item.fullName,
                                                        fullName: item.fullName,
                                                        firstName: item.firstName,
                                                        lastName: item.lastName,
                                                        contacts: item.contacts,
                                                        editable: false,
                                                    });
                                                }
                                            });
                                        }
                                        return {
                                            results: results,
                                        };
                                    },
                                },
                                dropdownCssClass: 'bigdrop',
                            });
                        }
                    },
                };
            },
        ])
        .directive('mailingVacancyAutocompleter', [
            '$filter',
            '$localStorage',
            'serverAddress',
            '$translate',
            '$rootScope',
            'Vacancy',
            'Mailing',
            'notificationService',
            function (
                $filter,
                $localStorage,
                serverAddress,
                $translate,
                $rootScope,
                Vacancy,
                Mailing,
                notificationService,
            ) {
                return {
                    restrict: 'EA',
                    replace: true,
                    link: function ($scope, element) {
                        let candidatesCount = [];
                        $(element[0])
                            .select2({
                                placeholder: $translate.instant('enter job title'),
                                minimumInputLength: 0,
                                allowClear: true,
                                ajax: {
                                    url: serverAddress + '/vacancy/autocomplete',
                                    dataType: 'json',
                                    crossDomain: true,
                                    quietMillis: 500,
                                    type: 'POST',
                                    data: function (term, page) {
                                        return {
                                            position: term.trim(),
                                        };
                                    },
                                    results: function (data, page) {
                                        var results = [];
                                        if (data['objects'] !== undefined) {
                                            $.each(data['objects'], function (index, item) {
                                                var clientName = '';
                                                if (item.clientId.name.length > 20) {
                                                    clientName = item.clientId.name.substring(0, 20);
                                                } else {
                                                    clientName = item.clientId.name;
                                                }
                                                var inVacancy = false;
                                                var interviewStatus;
                                                if (item.interviewStatus == undefined) {
                                                    item.interviewStatus =
                                                        'longlist,shortlist,interview,approved,notafit,declinedoffer';
                                                }
                                                var extraText = '';
                                                if (item.interviews != null) {
                                                    interviewStatus = item.interviews[0].state;
                                                    angular.forEach($rootScope.customStages, function (stage) {
                                                        if (interviewStatus == stage.customInterviewStateId) {
                                                            interviewStatus = stage.name;
                                                        }
                                                    });
                                                    extraText = ' [ ' + $filter('translate')(interviewStatus) + ' ]';
                                                    inVacancy = true;
                                                }
                                                results.push({
                                                    vacancy: item,
                                                    id: item.vacancyId,
                                                    localId: item.localId,
                                                    status: interviewStatus,
                                                    text:
                                                        item.position +
                                                        ($rootScope.me.personParams.clientAccessLevel === 'hide'
                                                            ? ''
                                                            : ' (' + clientName + ')') +
                                                        extraText,
                                                    interviewStatus: item.interviewStatus,
                                                    inVacancy: inVacancy,
                                                });
                                            });
                                        }
                                        return {
                                            results: results,
                                        };
                                    },
                                },
                                dropdownCssClass: 'bigdrop',
                            })
                            .on('change', function (e) {
                                $scope.vacancy = e.added;
                                $scope.emptyEmails.count = 0;
                                statusListForming($scope.vacancy.id, $scope.vacancy.interviewStatus);
                            });

                        function setSelect2Vacancy() {
                            let recipientsSource = JSON.parse($localStorage.get('mailingRecipientsSource'));
                            if (recipientsSource && recipientsSource.state && recipientsSource.localId) {
                                Mailing.getVacancyParams({
                                    localId: recipientsSource.localId,
                                    fieldToGet: ['interviewStatus'],
                                }).then(
                                    (result) => {
                                        $(element[0]).select2('data', {
                                            id: result.vacancyId,
                                            text: result.position,
                                        });
                                        statusListForming(
                                            result.vacancyId,
                                            result.statuses !== undefined
                                                ? result.statuses
                                                : 'longlist,shortlist,interview,approved,notafit,declinedoffer',
                                        ).then((result) => {
                                            $scope.VacancyStatusFiltered.some((status) => {
                                                if (status.value == recipientsSource.state) {
                                                    recipientsSource.fullState = status;
                                                    return true;
                                                } else {
                                                    return false;
                                                }
                                            });
                                            $('#stageSelect').val(JSON.stringify(recipientsSource.fullState));
                                        });
                                    },
                                    (error) => {
                                        notificationService.error(error);
                                    },
                                );
                            }
                        }
                        setSelect2Vacancy();

                        function statusListForming(vacancyId, statuses) {
                            return new Promise((resolve, reject) => {
                                if (vacancyId) {
                                    Mailing.onGetCounts({
                                        vacancyId: vacancyId,
                                    }).then((resp) => {
                                        candidatesCount = resp.objects;
                                        var sortedStages = [];
                                        var array = statuses ? statuses.split(',') : [];
                                        var VacancyStatus = Vacancy.interviewStatusNew();
                                        var i = 0;
                                        angular.forEach(array, function (resp) {
                                            angular.forEach(VacancyStatus, function (vStatus) {
                                                if (vStatus.used) {
                                                    if (i == 0) {
                                                        angular.forEach($rootScope.customStages, function (res) {
                                                            res.value = res.name;
                                                            res.movable = true;
                                                            res.added = false;
                                                            res.count = 0;
                                                            vStatus.status.push(res);
                                                            i = i + 1;
                                                        });
                                                    }
                                                    angular.forEach(vStatus.status, function (vStatusIn) {
                                                        if (resp == vStatusIn.value) {
                                                            vStatusIn.added = true;
                                                            sortedStages.push(vStatusIn);
                                                        } else if (resp == vStatusIn.customInterviewStateId) {
                                                            vStatusIn.added = true;
                                                            sortedStages.push(vStatusIn);
                                                        }
                                                    });
                                                }
                                            });
                                        });
                                        candidatesCount.forEach((candidateCount) => {
                                            sortedStages.forEach((stage) => {
                                                if (stage.customInterviewStateId) {
                                                    if (stage.customInterviewStateId == candidateCount.item) {
                                                        stage.count = candidateCount.count;
                                                    }
                                                } else {
                                                    if (stage.value == candidateCount.item) {
                                                        stage.count = candidateCount.count;
                                                    }
                                                }
                                            });
                                        });
                                        $scope.VacancyStatusFiltered = sortedStages;
                                        $scope.VacancyStatusValueFiltered = sortedStages.map((stage) => {
                                            // id: (stage.customInterviewStateId)? stage.customInterviewStateId : stage.value
                                            return {
                                                value: stage.value,
                                                id: stage.customInterviewStateId
                                                    ? stage.customInterviewStateId
                                                    : stage.value,
                                                count: stage.count,
                                            };
                                        });

                                        if (!$scope.$$phase && !$rootScope.$$phase) {
                                            $scope.$apply();
                                        }
                                        resolve();
                                    }),
                                        (error) => {
                                            notificationService.error(error.message);
                                            reject();
                                        };
                                }
                            });
                        }
                    },
                };
            },
        ]);
})();
