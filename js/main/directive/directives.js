var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives
        .directive('appVersion', [
            'version',
            function (version) {
                return function (scope, elm, attrs) {
                    elm.text(version);
                };
            },
        ])
        .value('ZeroClipboardPath', '//cdnjs.cloudflare.com/ajax/libs/zeroclipboard/1.1.7/ZeroClipboard.swf')
        .directive('appendHtml', function () {
            return {
                restrict: 'AE',
                scope: {
                    text: '@text',
                },
                link: function (scope, element) {
                    element.append(scope.text);
                },
            };
        })
        .directive('clipCopy', [
            '$window',
            'ZeroClipboardPath',
            function ($window, ZeroClipboardPath) {
                return {
                    scope: {
                        clipCopy: '&',
                        clipClick: '&',
                    },
                    restrict: 'A',
                    link: function (scope, element, attrs) {
                        // Create the clip object
                        var clip = new ZeroClipboard(element, {
                            moviePath: ZeroClipboardPath,
                            trustedDomains: ['*'],
                            allowScriptAccess: 'always',
                        });

                        clip.on('mousedown', function (client) {
                            client.setText(scope.$eval(scope.clipCopy));
                            if (angular.isDefined(attrs.clipClick)) {
                                scope.$apply(scope.clipClick);
                            }
                        });
                    },
                };
            },
        ])
        .directive('pulsar', [
            '$window',
            'Candidate',
            '$rootScope',
            function ($window, Candidate, $rootScope) {
                return {
                    restrict: 'AE',
                    scope: {
                        left: '@',
                        top: '@',
                        typem: '@',
                    },
                    templateUrl: 'partials/pulsar.html',
                    link: function (scope, element, attrs) {
                        element.find('.pulsar').css({
                            left: scope.left,
                            top: scope.top,
                        });
                        element.mouseover(function (event) {
                            var left = element.find('.pulsar').css('left').replace('px', '');
                            var top = element.find('.pulsar').css('top').replace('px', '');
                            if (scope.typem == 'open_cv') {
                                scope.$parent.$parent.showResumeUploadInfoPop = true;
                                if (!scope.$parent.$parent.$$phase) {
                                    scope.$parent.$parent.$apply();
                                }
                            } else if (scope.typem == 'networking') {
                                element.find('.bubble').css({
                                    top: '69px',
                                    left: Number($(this).css('left').replace('px', '')) + 'px',
                                    display: 'block',
                                    visibility: 'visible',
                                });
                                element.find('.bubble-arrow').css({
                                    'background-position': 'right top',
                                    left: '-63px',
                                });
                                element.find('.bubble-info').css({
                                    left: 810 - Number(element.find('.bubble-info').width()) - 900,
                                    display: 'block',
                                    visibility: 'visible',
                                });
                            } else if (scope.typem == 'candidate_add') {
                                element.find('.bubble-arrow').css({
                                    'background-position': 'left bottom',
                                });
                                element.find('.bubble-info').css({
                                    display: 'block',
                                    visibility: 'visible',
                                    top: '0px',
                                });

                                element.find('.bubble').css({
                                    top: Number(top.replace('px', '')) - 10 + 'px',
                                    left: Number(left.replace('px', '')) + 17 + 'px',
                                    display: 'block',
                                    visibility: 'visible',
                                });
                            } else if (scope.typem == 'recommended_candidates') {
                                element.find('.bubble-info').css({
                                    display: 'block',
                                    visibility: 'visible',
                                });

                                element.find('.bubble').css({
                                    top: Number(top.replace('px', '')) - 44 + 'px',
                                    left: Number(left.replace('px', '')) + 17 + 'px',
                                    display: 'block',
                                    visibility: 'visible',
                                });
                            } else if (scope.typem == 'search_vk_ws') {
                                element.find('.bubble-info').css({
                                    display: 'block',
                                    visibility: 'visible',
                                });

                                element.find('.bubble').css({
                                    top: Number(top.replace('px', '')) - 44 + 'px',
                                    left: Number(left.replace('px', '')) + 17 + 'px',
                                    display: 'block',
                                    visibility: 'visible',
                                });
                            } else if (scope.typem == 'scope_descr') {
                                element.find('.bubble-info').css({
                                    display: 'block',
                                    visibility: 'visible',
                                    left: '-192px',
                                    top: '-83px',
                                    height: '215px',
                                });
                                element.find('.bubble-arrow').css({
                                    'background-position': 'right bottom',
                                    left: '171px',
                                    top: '-108px',
                                });

                                element.find('.bubble').css({
                                    top: Number(top.replace('px', '')) + 100 + 'px',
                                    left:
                                        Number(left.replace('px', '')) -
                                        $(this).width() -
                                        element.find('.bubble-arrow').width() -
                                        150 +
                                        'px',
                                    display: 'block',
                                    visibility: 'visible',
                                });
                            } else if (scope.typem == 'invite_user') {
                                element.find('.bubble-arrow').css({
                                    'background-position': 'left bottom',
                                    top: '35px',
                                });
                                element.find('.bubble-info').css({
                                    display: 'block',
                                    visibility: 'visible',
                                    top: '54px',
                                });

                                element.find('.bubble').css({
                                    top: Number(top.replace('px', '')) - 44 + 'px',
                                    left: Number(left.replace('px', '')) + 17 + 'px',
                                    display: 'block',
                                    visibility: 'visible',
                                });
                            }
                        });
                        element.mouseleave(function (event) {
                            element.find('.bubble-info').css({
                                display: 'block',
                                visibility: 'visible',
                            });
                            element.find('.bubble').css({
                                display: 'none',
                                visibility: 'hidden',
                            });
                            if (scope.typem == 'open_cv') {
                                scope.$parent.$parent.showResumeUploadInfoPop = false;
                                if (!scope.$parent.$parent.$$phase) {
                                    scope.$parent.$parent.$apply();
                                }
                            }
                        });
                    },
                };
            },
        ])
        .directive('statisticsDir', [
            'Statistic',
            '$filter',
            '$translate',
            function (Statistic, $filter, $translate) {
                return {
                    restrict: 'AE',
                    scope: {
                        statisticObj: '=',
                    },
                    templateUrl: 'partials/notices/statistics.html',
                    link: function (scope, element, attrs) {
                        element.find('.button_open').click(function () {
                            Statistic.getSalesFunnel(scope.statisticObj.requestObj, function (resp) {
                                scope.hasFunnelChart = false;
                                if (resp['longlist'] != 0) {
                                    scope.hasFunnelChart = true;
                                    var myChart = {};
                                    if (resp.funnelMap) {
                                        var series = [];
                                        var values = [];
                                        var values2 = [];
                                        var values3 = [];
                                        var lastCount = null;
                                        angular.forEach(resp.funnelMap, function (i, s) {
                                            series.push({
                                                values: [i],
                                            });
                                            values.push($filter('translate')('interview_status_assoc' + '.' + s));
                                            values2.push(i.toString());
                                            if (lastCount == null) {
                                                values3.push('100%');
                                            } else {
                                                values3.push((i != 0 ? Math.round((i / lastCount) * 100) : 0) + '%');
                                            }
                                            lastCount = i;
                                        });
                                        myChart = {
                                            type: 'funnel',
                                            series: series,
                                            tooltip: {
                                                visible: true,
                                                shadow: 0,
                                            },

                                            'scale-y': {
                                                values: values,
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': 50,
                                                },
                                            },
                                            'scale-y-2': {
                                                values: values2,
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': -40,
                                                },
                                            },
                                            'scale-y-3': {
                                                values: values3,
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': -10,
                                                },
                                            },
                                            'scale-x': {
                                                values: [''],
                                            },
                                            labels: [
                                                {
                                                    text: $filter('translate')('Relative conversion'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: 670,
                                                    offsetY: 20,
                                                },
                                                {
                                                    text: $filter('translate')('Candidates'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: $translate.use() != 'en' ? 580 : 605,
                                                    offsetY: 20,
                                                },
                                                {
                                                    text: $filter('translate')('status'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: 100,
                                                    offsetY: 20,
                                                },
                                            ],
                                            backgroundColor: 'white',
                                            gui: {
                                                behaviors: [
                                                    {
                                                        id: 'DownloadPDF',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'Reload',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'Print',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'DownloadSVG',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'LogScale',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'About',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'FullScreen',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'BugReport',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'ViewSource',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'FullScreen',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'FullScreen',
                                                        enabled: 'none',
                                                    },
                                                ],
                                            },
                                        };
                                    } else {
                                        myChart = {
                                            type: 'funnel',
                                            width: '610px',
                                            series: [
                                                {
                                                    values: [resp['longlist']],
                                                },
                                                {
                                                    values: [resp['shortlist']],
                                                },
                                                {
                                                    values: [resp['interview']],
                                                },
                                                {
                                                    values: [resp['approved']],
                                                },
                                            ],
                                            tooltip: {
                                                visible: true,
                                            },
                                            'scale-y': {
                                                values: [
                                                    $filter('translate')('long_list'),
                                                    $filter('translate')('short_list'),
                                                    $filter('translate')('interview'),
                                                    $filter('translate')('approved'),
                                                ],
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': 35,
                                                },
                                            },
                                            'scale-y-2': {
                                                values: [
                                                    resp['longlist'] + '',
                                                    resp['shortlist'] + '',
                                                    resp['interview'] + '',
                                                    resp['approved'] + '',
                                                ],
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': 0,
                                                },
                                            },
                                            'scale-y-3': {
                                                values: [
                                                    '100%',
                                                    Math.round((resp['shortlist'] / resp['longlist']) * 100) + '%',
                                                    (resp['shortlist'] != 0
                                                        ? Math.round((resp['interview'] / resp['shortlist']) * 100)
                                                        : 0) + '%',
                                                    (resp['interview'] != 0
                                                        ? Math.round((resp['approved'] / resp['interview']) * 100)
                                                        : 0) + '%',
                                                ],
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': 55,
                                                },
                                            },
                                            'scale-y-4': {
                                                values: [
                                                    '100%',
                                                    Math.round((resp['shortlist'] / resp['longlist']) * 100) + '%',
                                                    (resp['interview'] != 0
                                                        ? Math.round((resp['interview'] / resp['longlist']) * 100)
                                                        : 0) + '%',
                                                    (resp['approved'] != 0
                                                        ? Math.round((resp['approved'] / resp['longlist']) * 100)
                                                        : 0) + '%',
                                                ],
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': 115,
                                                },
                                            },
                                            'scale-x': {
                                                values: [''],
                                            },
                                            labels: [
                                                {
                                                    text: $filter('translate')('Relative conversion'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: 570,
                                                    offsetY: 20,
                                                },
                                                {
                                                    text: $filter('translate')('Absolute conversion'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: 670,
                                                    offsetY: 20,
                                                },
                                                {
                                                    text: $filter('translate')('Candidates'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: $translate.use() != 'en' ? 485 : 505,
                                                    offsetY: 20,
                                                },
                                                {
                                                    text: $filter('translate')('status'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: 80,
                                                    offsetY: 20,
                                                },
                                            ],
                                            backgroundColor: 'white',
                                            gui: {
                                                behaviors: [
                                                    {
                                                        id: 'DownloadPDF',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'Reload',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'Print',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'DownloadSVG',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'LogScale',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'About',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'FullScreen',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'BugReport',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'ViewSource',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'FullScreen',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'FullScreen',
                                                        enabled: 'none',
                                                    },
                                                ],
                                            },
                                        };
                                    }
                                    zingchart.render({
                                        id: 'myChartDiv',
                                        data: myChart,
                                        height: 350,
                                        width: 750,
                                        output: 'html5',
                                    });
                                }
                            });
                            var statPanel = element.find('#statistic_panel');
                            statPanel.css({
                                left: Number(element.find('#buttonPanel').width()) + 14,
                            });
                            if (statPanel.css('display') == 'none') {
                                statPanel.toggle('slide', { direction: 'left' }, 400);
                                $(document).mouseup(function (e) {
                                    var statElement = $('#statistic_panel');
                                    if ($('#statistic_panel').has(e.target) && !$('.button_open').is(e.target)) {
                                        element.find('#statistic_panel').hide();
                                        $(document).off('mouseup');
                                    }
                                });
                            } else {
                                statPanel.hide();
                                $(document).off('mouseup');
                            }
                        });
                    },
                };
            },
        ])
        .directive('compile', [
            '$compile',
            function ($compile) {
                return function (scope, element, attrs) {
                    scope.$watch(
                        function (scope) {
                            // watch the 'compile' expression for changes
                            return scope.$eval(attrs.compile);
                        },
                        function (value) {
                            // when the 'compile' expression changes
                            // assign it into the current DOM
                            element.html(value);

                            // compile the new DOM and link it to the current
                            // scope.
                            // NOTE: we only compile .childNodes so that
                            // we don't get into infinite loop compiling ourselves
                            $compile(element.contents())(scope);
                        },
                    );
                };
            },
        ])
        .directive('userStatisticsDir', [
            'Statistic',
            '$filter',
            '$translate',
            function (Statistic, $filter, $translate) {
                return {
                    restrict: 'AE',
                    scope: {
                        user: '@userId',
                        name: '@userName',
                    },
                    templateUrl: 'partials/notices/userSalesFunnel.html',
                    link: function (scope, element, attrs) {
                        element.find('.button_open').click(function () {
                            var chartId = scope.user + '_chartDiv';
                            Statistic.getSalesFunnel({ creator: scope.user }, function (resp) {
                                scope.hasFunnelChart = false;
                                if (resp['longlist'] != 0) {
                                    scope.hasFunnelChart = true;
                                    var myChart = {};
                                    if (resp.funnelMap) {
                                        var series = [];
                                        var values = [];
                                        var values2 = [];
                                        var values3 = [];
                                        var lastCount = null;
                                        angular.forEach(resp.funnelMap, function (i, s) {
                                            series.push({
                                                values: [i],
                                            });
                                            values.push($filter('translate')('interview_status_assoc' + '.' + s));
                                            values2.push(i.toString());
                                            if (lastCount == null) {
                                                values3.push('100%');
                                            } else {
                                                values3.push((i != 0 ? Math.round((i / lastCount) * 100) : 0) + '%');
                                            }
                                            lastCount = i;
                                        });
                                        myChart = {
                                            type: 'funnel',
                                            series: series,
                                            tooltip: {
                                                visible: true,
                                                shadow: 0,
                                            },

                                            'scale-y': {
                                                values: values,
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': 50,
                                                },
                                            },
                                            'scale-y-2': {
                                                values: values2,
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': -40,
                                                },
                                            },
                                            'scale-y-3': {
                                                values: values3,
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': -10,
                                                },
                                            },
                                            'scale-x': {
                                                values: [''],
                                            },
                                            labels: [
                                                {
                                                    text: $filter('translate')('Conversion'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: 670,
                                                    offsetY: 20,
                                                },
                                                {
                                                    text: $filter('translate')('Count'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: $translate.use() != 'en' ? 580 : 605,
                                                    offsetY: 20,
                                                },
                                                {
                                                    text: $filter('translate')('status'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: 100,
                                                    offsetY: 20,
                                                },
                                            ],
                                            backgroundColor: 'white',
                                            gui: {
                                                behaviors: [
                                                    {
                                                        id: 'DownloadPDF',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'Reload',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'Print',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'DownloadSVG',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'LogScale',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'About',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'FullScreen',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'BugReport',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'ViewSource',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'FullScreen',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'FullScreen',
                                                        enabled: 'none',
                                                    },
                                                ],
                                            },
                                        };
                                    } else {
                                        myChart = {
                                            type: 'funnel',
                                            series: [
                                                {
                                                    values: [resp['longlist']],
                                                },
                                                {
                                                    values: [resp['shortlist']],
                                                },
                                                {
                                                    values: [resp['interview']],
                                                },
                                                {
                                                    values: [resp['approved']],
                                                },
                                            ],
                                            tooltip: {
                                                visible: true,
                                            },
                                            'scale-y': {
                                                values: [
                                                    $filter('translate')('long_list'),
                                                    $filter('translate')('short_list'),
                                                    $filter('translate')('interview'),
                                                    $filter('translate')('approved'),
                                                ],
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': 60,
                                                },
                                            },
                                            'scale-y-2': {
                                                values: [
                                                    resp['longlist'] + '',
                                                    resp['shortlist'] + '',
                                                    resp['interview'] + '',
                                                    resp['approved'] + '',
                                                ],
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': -40,
                                                },
                                            },
                                            'scale-y-3': {
                                                values: [
                                                    '100%',
                                                    Math.round((resp['shortlist'] / resp['longlist']) * 100) + '%',
                                                    (resp['shortlist'] != 0
                                                        ? Math.round((resp['interview'] / resp['shortlist']) * 100)
                                                        : 0) + '%',
                                                    (resp['interview'] != 0
                                                        ? Math.round((resp['approved'] / resp['interview']) * 100)
                                                        : 0) + '%',
                                                ],
                                                item: {
                                                    fontSize: 12,
                                                    'offset-x': -10,
                                                },
                                            },
                                            'scale-x': {
                                                values: [''],
                                            },
                                            labels: [
                                                {
                                                    text: $filter('translate')('Conversion'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: 670,
                                                    offsetY: 20,
                                                },
                                                {
                                                    text: $filter('translate')('Count'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: $translate.use() != 'en' ? 580 : 605,
                                                    offsetY: 20,
                                                },
                                                {
                                                    text: $filter('translate')('status'),
                                                    fontWeight: 500,
                                                    fontSize: 12,
                                                    offsetX: 100,
                                                    offsetY: 20,
                                                },
                                            ],
                                            backgroundColor: 'white',
                                            gui: {
                                                behaviors: [
                                                    {
                                                        id: 'DownloadPDF',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'Reload',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'Print',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'DownloadSVG',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'LogScale',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'About',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'FullScreen',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'BugReport',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'ViewSource',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'FullScreen',
                                                        enabled: 'none',
                                                    },
                                                    {
                                                        id: 'FullScreen',
                                                        enabled: 'none',
                                                    },
                                                ],
                                            },
                                        };
                                    }
                                    zingchart.render({
                                        id: chartId,
                                        data: myChart,
                                        height: 350,
                                        width: 750,
                                        output: 'html5',
                                    });
                                }
                            });
                            var salesFunnelId = '#' + scope.user + '_panel';
                            var statPanel = element.find(salesFunnelId);
                            statPanel.css({
                                left: $(window).width() / 2 - statPanel.width() / 2,
                            });
                            if (statPanel.css('display') == 'none') {
                                statPanel.toggle('slide', { direction: 'left' }, 400);
                                $(document).mouseup(function (e) {
                                    if ($(salesFunnelId).has(e.target) && !$('.button_open').is(e.target)) {
                                        element.find(salesFunnelId).hide();
                                        $(document).off('mouseup');
                                    }
                                });
                            } else {
                                statPanel.hide();
                                $(document).off('mouseup');
                            }
                        });
                    },
                };
            },
        ])
        .directive('noticesDir', [
            '$window',
            'Candidate',
            'CacheCandidates',
            '$rootScope',
            'Person',
            '$location',
            'Service',
            'Notice',
            '$translate',
            '$filter',
            'tmhDynamicLocale',
            '$timeout',
            'CandidatesSlider',
            '$state',
            '$window',
            function (
                $window,
                Candidate,
                CacheCandidates,
                $rootScope,
                Person,
                $location,
                Service,
                Notice,
                $translate,
                $filter,
                tmhDynamicLocale,
                $timeout,
                CandidatesSlider,
                $state,
                $window,
            ) {
                return {
                    scope: {},
                    restrict: 'AE',
                    templateUrl: 'partials/notices/notice.html',
                    link: function (scope, element, attrs) {
                        var sendReadRequest = [];
                        var favicon = new Favico({
                            animation: 'none',
                            position: 'up',
                        });

                        scope.updatedFieldsDict = Service.updatedFieldsDict;

                        scope.sliderId = CandidatesSlider.getSliderId();
                        scope.noticeObj = {
                            tariffPlanType: null,
                            notices: null,
                            read: 0,
                        };
                        scope.sync = { withEmail: false, withGoogle: false };
                        var check = 0;
                        $rootScope.readAll = false;
                        //Candidate.getParseEmailData(function(resp) {
                        //    if (resp.status && resp.status === "ok") {
                        //        angular.forEach(resp.objects, function(val) {
                        //            if (val.validHost) {
                        //                scope.sync.withEmail = "active";
                        //            } else if (scope.sync.withEmail === false) {
                        //                scope.sync.withEmail = "processing";
                        //            }
                        //        });
                        //
                        //    }
                        //}, function(resp) {
                        //});
                        scope.toCandidateAddEmail = function () {
                            closePopUp();
                            $location.path('/candidate/add/email');
                        };

                        scope.toVacancySuggestions = function (event, vacancyId) {
                            $rootScope.redirectToSuggestions = true;
                            event.preventDefault();
                            event.stopPropagation();
                            $location.path('/vacancies/' + vacancyId);
                        };

                        Notice.registerNoticeView(function (id) {
                            $rootScope.newNoticeCount = $rootScope.newNoticeCount - 1;
                            favicon.badge($rootScope.newNoticeCount);
                            angular.forEach(scope.noticeObj.notices, function (val) {
                                if (val.noticeId == id) {
                                    val.read = true;
                                    scope.noticeObj.read++;
                                }
                            });
                        }, 'noticesDir');
                        scope.toVacancyApplication = function (e, localId) {
                            e.preventDefault();
                            closePopUp();
                            $location.path('/vacancy/request').search({ localId: localId });
                        };

                        scope.toVacancy = function (val) {
                            closePopUp();
                            $location.path('/vacancies/' + val);
                        };
                        scope.readNotice = function (n) {
                            if (!n.read && sendReadRequest.indexOf(n.noticeId) === -1) {
                                Notice.updateNoticesView(n.noticeId, 'noticesDir');
                                sendReadRequest.push(n.noticeId);
                                var index = sendReadRequest.indexOf(n.noticeId);
                                n.noticeIds = n.noticeIds.filter((noticeId) => noticeId);
                                Service.readNotice(
                                    n.noticeIds,
                                    function (resp) {
                                        if (resp.status && resp.status == 'ok') {
                                            n.read = true;
                                            scope.noticeObj.read++;
                                            $rootScope.newNoticeCount = $rootScope.newNoticeCount - 1;
                                            favicon.badge($rootScope.newNoticeCount);
                                            document.dispatchEvent(
                                                new CustomEvent('cleverstaffExtensionReloadCountUnreadNotice'),
                                            );
                                        } else if (resp.message) {
                                        }
                                        sendReadRequest.splice(index, 1);
                                    },
                                    function (resp) {
                                        sendReadRequest.splice(index, 1);
                                    },
                                );
                            }
                        };
                        scope.toPage = function (notice) {
                            if (notice.type == 'parserEmailIncorrectPassword') {
                                closePopUp();
                                $location.path('/email-integration');
                            }
                        };
                        scope.toCandidate = function (id) {
                            closePopUp();
                            // $location.path('/candidates/' + id);
                        };
                        scope.toClient = function (id) {
                            closePopUp();
                            $location.path('/clients/' + id);
                        };

                        scope.routeOnCandidates = function (ids) {
                            let data = JSON.parse(ids);
                            let link = $state.href('candidates', { ids: data }, { reload: true });
                            $window.open(link, '_blank');
                        };

                        scope.toScorecard = function (value) {
                            closePopUp();
                            let stateParams = {
                                scoreCardId: value.scoreCardId,
                                candidateObj: value.candidate,
                                id: value.candidate.localId,
                                isFromVacancyToCandidate: value.vacancyId,
                                isFromVacancyToEvaluate: true,
                                sliderDataId: scope.sliderId,
                                vacancyName: value.vacancy ? value.vacancy.position : null,
                                vacancyId: value.vacancyId,
                            };

                            let link = $state.href('candidate-slide', stateParams, {
                                reload: true,
                            });

                            $window.open(link, '_blank');
                        };

                        scope.toSeeLetters = (notice) => {
                            if (
                                $location
                                    .path()
                                    .indexOf(`/candidates/slide/${scope.sliderId}/${notice.candidate.localId}`) > -1
                            ) {
                                $state.go('candidate-slide.personalMailing');
                            } else {
                                $state.go('candidate-slide', {
                                    isFromNotificationsLetters: true,
                                    sliderDataId: scope.sliderId,
                                    id: notice.candidate.localId,
                                    candidateObj: notice.candidate,
                                });
                            }
                            scope.readNotice(notice);
                            closePopUp();
                        };
                        scope.getLettersEnd = function (number) {
                            let n = Math.abs(number) % 100,
                                n1 = n % 10,
                                form = 2;

                            if (n > 10 && n < 20) form = 2;
                            if (n1 > 1 && n1 < 5) form = 1;
                            if (n1 === 1) {
                                if (number > 1 && $rootScope.currentLang === 'en') {
                                    form = 2;
                                } else {
                                    form = 0;
                                }
                            }
                            if (n > 10 && n < 15 && n1 > 0 && n1 < 5) form = 2;
                            return form;
                        };
                        $rootScope.clearNoticeAfterRead = function () {
                            scope.noticeObj.notices = scope.noticeObj.notices.filter((notice) => notice.read != true);
                        };
                        $rootScope.clearNoticesAfterRead = function () {
                            scope.noticeObj.notices = [];
                        };
                        $rootScope.updateNoticesNav = function () {
                            var interval = setInterval(function () {
                                if ($rootScope.globalNotice) {
                                    clearInterval(interval);
                                    if ($rootScope.me !== undefined) {
                                        scope.sync.withGoogle = $rootScope.me.googleMail != undefined;
                                    } else {
                                        scope.sync.withGoogle = false;
                                    }
                                    if (
                                        ($rootScope.globalNotice.notices &&
                                            $rootScope.globalNotice.notices.length > 0) ||
                                        $rootScope.globalNotice.notices.length === 0
                                    ) {
                                        scope.noticeObj.notices = $rootScope.globalNotice.notices;
                                        if ($rootScope.globalNotice.countUnreadNotice > 99) {
                                            $rootScope.newNoticeCount = 99;
                                            $rootScope.currentNoticeCount = $rootScope.globalNotice.countUnreadNotice;
                                        } else {
                                            $rootScope.newNoticeCount = $rootScope.globalNotice.countUnreadNotice;
                                            $rootScope.currentNoticeCount = $rootScope.globalNotice.countUnreadNotice;
                                        }
                                        favicon.badge($rootScope.newNoticeCount);
                                        $rootScope.$apply();
                                    }
                                }
                            }, 500);
                        };
                        $rootScope.updateNoticesNav();

                        function closePopUp() {
                            $('#notices').hide();
                            $('#notice_element_icon').css({
                                'background-color': 'rgba(0, 0, 0, 0)',
                            });
                            $(document).off('mouseup');
                        }

                        scope.toUser = function () {
                            closePopUp();
                            $location.path('/users/' + $rootScope.me.userId);
                        };
                        scope.toCheckout = function () {
                            closePopUp();
                            $location.path('/pay');
                        };
                        scope.toListNotices = function () {
                            closePopUp();
                            $location.path('/notices');
                        };
                        scope.toAddEmail = function () {
                            closePopUp();
                            $location.path('/email-integration');
                        };
                        scope.readAllNoticesLocal = function () {
                            $rootScope.globalNotice.notices.forEach((oneNotice) => {
                                oneNotice.read = true;
                            });
                            $rootScope.newNoticeCount = 0;
                            $rootScope.$$phase || $rootScope.$apply();
                        };

                        scope.checkEverythingRead = function () {
                            Notice.readAll(
                                function (resp) {
                                    if (resp.status == 'ok') {
                                        $rootScope.readAll = true;
                                        scope.readAllNoticesLocal();
                                    }
                                },
                                function (error) {
                                    console.error(error);
                                },
                            );
                        };
                        $rootScope.changeFaviconNumber = function (number) {
                            $rootScope.newNoticeCount = number;
                            favicon.badge($rootScope.newNoticeCount);
                        };
                        scope.changeLanguage = function (key) {
                            $translate.use(key);
                            tmhDynamicLocale.set(key);
                            Person.setLang({ lang: key });
                        };

                        scope.getBrowser = function () {
                            if (navigator.saysWho.indexOf('Chrome') != -1) {
                                return 'Chrome';
                            } else if (navigator.saysWho.indexOf('Firefox') != -1) {
                                return 'Firefox';
                            } else {
                                return $filter('translate')('browser');
                            }
                        };
                        scope.isGoodBrowser = function () {
                            return scope.getBrowser() === 'Chrome' || scope.getBrowser() === 'Firefox';
                        };

                        navigator.saysWho = (function () {
                            var ua = navigator.userAgent,
                                tem,
                                M = ua.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i) || [];
                            if (/trident/i.test(M[1])) {
                                tem = /\brv[ :]+(\d+)/g.exec(ua) || [];
                                return 'IE ' + (tem[1] || '');
                            }
                            if (M[1] === 'Chrome') {
                                tem = ua.match(/\bOPR\/(\d+)/);
                                if (tem != null) return 'Opera ' + tem[1];
                            }
                            M = M[2] ? [M[1], M[2]] : [navigator.appName, navigator.appVersion, '-?'];
                            if ((tem = ua.match(/version\/(\d+)/i)) != null) M.splice(1, 1, tem[1]);
                            return M.join(' ');
                        })();

                        scope.extensionHas = false;
                        if ($rootScope.eventListenerPing) {
                            document.removeEventListener('cleverstaffExtensionPong', $rootScope.eventListenerPing);
                        }
                        $rootScope.eventListenerPing = function (event) {
                            scope.extensionHas = true;
                            $rootScope.extensionHas = true;
                            Service.saveBrowserWithPlugin({
                                browser: scope.getBrowser(),
                            });
                        };
                        document.addEventListener('cleverstaffExtensionPong', $rootScope.eventListenerPing);
                        document.dispatchEvent(new CustomEvent('cleverstaffExtensionPing'));
                    },
                };
            },
        ])
        .directive('helpZip1', [
            '$window',
            'Candidate',
            'CacheCandidates',
            '$rootScope',
            'Person',
            '$location',
            'Service',
            'Notice',
            '$translate',
            '$filter',
            'tmhDynamicLocale',
            'Vacancy',
            function (
                $window,
                Candidate,
                CacheCandidates,
                $rootScope,
                Person,
                Vacancy,
                $location,
                Service,
                Notice,
                $translate,
                $filter,
                tmhDynamicLocale,
            ) {
                return {
                    scope: {},
                    restrict: 'AE',
                    templateUrl: 'partials/notices/helpWindowZip1.html',
                    link: function (scope, element, attrs) {
                        //function closePopUp() {
                        //    $("#noticesQuestion").hide();
                        //    $("#agreedQuestion").css({"background-color": "rgba(0, 0, 0, 0)"})
                        //    $(document).off('mouseup');
                        //}
                    },
                };
            },
        ])
        .directive('ddTextCollapse', [
            '$compile',
            '$translate',
            '$filter',
            '$timeout',
            function ($compile, $translate, $filter, $timeout) {
                return {
                    restrict: 'A',
                    scope: true,
                    link: function (scope, element, attrs) {
                        // start collapsed
                        scope.collapsed = false;
                        scope.less = $filter('translate')('Hide details');
                        scope.more = $filter('translate')('Show more details');
                        let corrected = $('#withoutHttps').attr('href');
                        // create the function to toggle the collapse
                        scope.toggle = function () {
                            $timeout(function () {
                                scope.$apply(function () {
                                    scope.toggleButton = $compile(
                                        '<div class="collapse-text-toggle" ng-click="toggle()">' +
                                            '<span>{{(collapsed ? "Hide details" : "Show more details") | translate}}</span> <img ng-src="images/redesign/svg-icons/chevron-{{collapsed ? \'up\' : \'down\'}}.svg" alt="">' +
                                            '</div>',
                                    )(scope);
                                    scope.collapsed = !scope.collapsed;
                                    if (corrected) {
                                        $('#withoutHttps').attr('href', corrected.replace(/\s+/g, ''));
                                    }
                                    element.empty();
                                    if (corrected) {
                                        $('#withoutHttps').attr('href', corrected.replace(/\s+/g, ''));
                                    }
                                    scope.collapsed
                                        ? element.append(`${scope.firstPart}${scope.secondPart}`)
                                        : element.append(scope.firstPart);
                                    element.append(scope.moreIndicatorSpan);
                                    element.append(scope.toggleButton);
                                }, 0);
                            });
                        };

                        // wait for changes on the text
                        attrs.$observe('ddTextCollapseText', function (text) {
                            function linkify(inputText) {
                                let replacedText, replacePattern1, replacePattern2, replacePattern3;
                                //URLs starting with http://, https://, or ftp://
                                replacePattern1 = /(^|&lt;|\s)\b(https?:\/\/[^\s\(\)\'\"\<\>]+)/gi;
                                replacedText = inputText.replace(
                                    replacePattern1,
                                    "$1<a id='httpsTest' href='$2' target='_blank'>$2</a>",
                                );
                                inputText.replace(/<br[^>]*>/i, '');
                                let httpsTest = $('#httpsTest').attr('href');
                                if (httpsTest) {
                                    $('#httpsTest').attr('href', httpsTest.replace('localhost', ''));
                                }
                                let clearTextInLink = $('#withHttps');
                                if (clearTextInLink.length > 0) {
                                    clearTextInLink[0].firstElementChild.innerHTML = '';
                                }
                                let clearHrefInLink = $('#withHttps').attr('href');
                                if (clearHrefInLink) {
                                    $('#withHttps').attr(
                                        'href',
                                        clearHrefInLink
                                            .replace(/^br*|br*$|(br)br*/g, '$1')
                                            .replace(/^<*|<*$|(<)<*/g, '$1'),
                                    );
                                }

                                //URLs starting with "www." (without // before it, or it'd re-link the ones done above).
                                replacePattern2 = /(^|[^\/])(www\.[\S]+(\b|$))/gim;
                                replacedText = replacedText.replace(
                                    replacePattern2,
                                    '<a style="display: block" id="withoutHttps" href="http://$2" target="_blank">$2</a>',
                                );

                                //Change email addresses to mailto:: links.
                                replacePattern3 = /(([a-zA-Z0-9\-\_\.])+@[a-zA-Z\_]+?(\.[a-zA-Z]{2,6})+)/gim;
                                replacedText = replacedText.replace(replacePattern3, '<a href="mailto:$1">$1</a>');

                                let corrected = $('#withoutHttps').attr('href');
                                if (corrected) {
                                    $('#withoutHttps').attr(
                                        'href',
                                        corrected.replace(/[&\\\#,+()$~%*?<>{}]/g, '').replace(/\s/g, ''),
                                    );
                                }
                                let textFromLink = document.getElementById('withoutHttps');
                                if (textFromLink) {
                                    textFromLink.innerHTML = textFromLink.innerHTML
                                        .replace(/(.+?\.com).*/, '$1')
                                        .replace(/^\s*/, '')
                                        .replace(/(^[\s]+|[\s]+$)/g, '');
                                }
                                return replacedText;
                            }

                            text = linkify(text);
                            // get the length from the attributes
                            var maxLength = attrs.ddTextCollapseMaxLength;

                            if (text.length > maxLength) {
                                scope.firstPart = text.substring(0, maxLength);
                                scope.secondPart = text.substring(maxLength, text.length);
                                scope.moreIndicatorSpan = $compile('<span ng-if="!collapsed">...</span>')(scope);
                                scope.toggleButton = $compile(
                                    '<div class="collapse-text-toggle" ng-click="toggle()">' +
                                        '<span>{{(collapsed ? "Hide details" : "Show more details") | translate}} </span> <img ng-class="{rotate: collapsed}" src="images/redesign/svg-icons/chevron-down.svg" alt="">' +
                                        '</div>',
                                )(scope);
                                element.empty();
                                element.append(scope.firstPart);
                                element.append(scope.moreIndicatorSpan);
                                element.append(scope.toggleButton);
                            } else {
                                element.empty();
                                element.append(text);
                            }
                        });
                    },
                };
            },
        ])
        .directive('helpZip2', [
            '$window',
            'Candidate',
            'CacheCandidates',
            '$rootScope',
            'Person',
            '$location',
            'Service',
            'Notice',
            '$translate',
            '$filter',
            'tmhDynamicLocale',
            'Vacancy',
            function (
                $window,
                Candidate,
                CacheCandidates,
                $rootScope,
                Person,
                Vacancy,
                $location,
                Service,
                Notice,
                $translate,
                $filter,
                tmhDynamicLocale,
            ) {
                return {
                    scope: {},
                    restrict: 'AE',
                    templateUrl: 'partials/notices/helpWindowZip2.html',
                    link: function (scope, element, attrs) {
                        //function closePopUp() {
                        //    $("#noticesQuestion").hide();
                        //    $("#agreedQuestion").css({"background-color": "rgba(0, 0, 0, 0)"})
                        //    $(document).off('mouseup');
                        //}
                    },
                };
            },
        ])
        .directive('helpZip3', [
            '$window',
            'Candidate',
            'CacheCandidates',
            '$rootScope',
            'Person',
            '$location',
            'Service',
            'Notice',
            '$translate',
            '$filter',
            'tmhDynamicLocale',
            'Vacancy',
            function (
                $window,
                Candidate,
                CacheCandidates,
                $rootScope,
                Person,
                Vacancy,
                $location,
                Service,
                Notice,
                $translate,
                $filter,
                tmhDynamicLocale,
            ) {
                return {
                    scope: {},
                    restrict: 'AE',
                    templateUrl: 'partials/notices/helpWindowZip3.html',
                    link: function (scope, element, attrs) {
                        //function closePopUp() {
                        //    $("#noticesQuestion").hide();
                        //    $("#agreedQuestion").css({"background-color": "rgba(0, 0, 0, 0)"})
                        //    $(document).off('mouseup');
                        //}
                    },
                };
            },
        ])
        .directive('priview', [
            'Service',
            '$window',
            'Candidate',
            'CacheCandidates',
            '$rootScope',
            '$location',
            '$sce',
            'Person',
            '$timeout',
            '$filter',
            function (
                Service,
                $window,
                Candidate,
                CacheCandidates,
                $rootScope,
                $location,
                $sce,
                Person,
                $timeout,
                $filter,
            ) {
                return {
                    scope: {
                        candidate: '=',
                        pageid: '@',
                        callback: '=',
                        type: '=',
                    },
                    restrict: 'A',
                    link: function (scope, element, attrs) {
                        var checkShow = false;
                        var isOpen = false;
                        element.click(function (event) {
                            event.stopPropagation();
                            if (!isOpen) {
                                if (scope.type === 'pipeline' && !scope.candidate?.fullName) {
                                    Candidate.onGetCandidateFields({ localId: scope.candidate }).then((resp) => {
                                        $rootScope.candidatePreview = resp.object;
                                        scope.candidate = resp.object;
                                        $rootScope.loadingPriview = true;
                                        timeToOPen(event);
                                        setTimeout(function () {
                                            $rootScope.loadingPriview = false;
                                        }, 0);
                                    });
                                } else {
                                    $rootScope.loadingPriview = true;
                                    timeToOPen(event);
                                    setTimeout(function () {
                                        $rootScope.loadingPriview = false;
                                    }, 0);
                                }
                            }
                        });
                        element.mouseup(function () {
                            checkShow = false;
                            isOpen = false;
                        });

                        function setPosition() {
                            const wrapper = $('#candidate_preview');
                            const windowWidth = window.innerWidth;
                            const windowHeight = window.innerHeight;
                            let percent = (wrapper.width() / 100) * 50 + 4;
                            var pos = findPos(element.find('.text_change')[0]);
                            var preview_top = pos[0] + 5 - $('#candidate_preview').height() / 2;
                            var checkLessThenNull = false;
                            if (preview_top < 0) {
                                checkLessThenNull = true;
                            }

                            $rootScope.closePrevieCandidateModal = () => $('#candidate_preview').hide();
                            $rootScope.closeDescrModal ? $rootScope.closeDescrModal() : null;

                            if (scope.type === 'pipeline') {
                                preview_top += 20;
                                pos[1] = pos[1] + 15;
                            }

                            $('#candidate_preview').css({
                                top: !checkLessThenNull ? preview_top : '41px',
                                left: pos[1] + 50,
                            });

                            $('#arrow_preview').css({
                                top: !checkLessThenNull ? $('#candidate_preview').height() / 2 - 17 : '50px',
                                left: '-12px',
                            });

                            if (scope.type === 'pipeline') {
                                let heightPercent = preview_top + wrapper.height() - windowHeight + 20;
                                setTimeout(() => {
                                    $('.preview-width-without-photo').css({ minWidth: '250px' });
                                    $('.preview-width-without-photo').children().css({ marginRight: '0' });

                                    if (windowWidth < pos[1] + wrapper.width() + 50) {
                                        pos[1] = pos[1] - 30;
                                        percent -= percent + 20 - 20;
                                        $('#candidate_preview').css({
                                            left: `${pos[1] - (wrapper.width() - percent)}px`,
                                        });
                                        $('#arrow_preview').css({
                                            right: '-14px',
                                            left: 'initial',
                                            transform: 'rotate(225deg)',
                                        });

                                        if ($('.candidate-preview-container').hasClass('preview-width-without-photo')) {
                                            $('#arrow_preview').css({
                                                top: !checkLessThenNull
                                                    ? $('#candidate_preview').height() / 2 - 17
                                                    : '50px',
                                            });
                                        }
                                    } else {
                                        $('#arrow_preview').css({
                                            right: 'initial',
                                            transform: 'rotate(45deg)',
                                        });
                                    }

                                    if (
                                        preview_top + wrapper.height() > windowHeight &&
                                        $rootScope.getBrowser() !== 'safari'
                                    ) {
                                        $('#candidate_preview').css({
                                            top: `${preview_top - heightPercent}px`,
                                        });

                                        $('#arrow_preview').css({
                                            top: !checkLessThenNull
                                                ? $('#candidate_preview').height() / 2 - 17 + heightPercent
                                                : '50px',
                                        });
                                    }

                                    $('#' + scope.pageid).click(function () {
                                        $('#candidate_preview').css({
                                            top: 0,
                                            left: 0,
                                        });
                                        $('#candidate_preview').hide();
                                        $('#vacancy').off('click');
                                        $rootScope.candidatePreview = null;
                                        isOpen = false;
                                    });
                                    $('#candidate_preview').show();
                                    $('#candidate_preview').css('opacity', '1');
                                    isOpen = true;

                                    if (windowWidth < pos[1] + $('#candidate_preview')[0].clientWidth + 50) {
                                        $timeout(() => {
                                            $('#candidate_preview').css({
                                                left: `${
                                                    pos[1] - ($('#candidate_preview')[0].clientWidth - percent)
                                                }px`,
                                                top: !checkLessThenNull ? preview_top : '41px',
                                            });
                                        }, 100);
                                    }
                                }, 100);
                            } else {
                                $('#' + scope.pageid).click(function () {
                                    $('#candidate_preview').css({
                                        top: 0,
                                        left: 0,
                                    });
                                    $('#candidate_preview').hide();
                                    $('#vacancy').off('click');
                                    $rootScope.candidatePreview = null;
                                    isOpen = false;
                                });
                                $('#candidate_preview').show();
                                $('#candidate_preview').css('opacity', '1');
                                isOpen = true;
                            }
                        }

                        function timeToOPen(event) {
                            openCandidate(event);
                            if (!checkShow) {
                                checkShow = true;
                                if (checkShow && !isOpen) {
                                    if ($rootScope.candidatePreview) {
                                        $('#candidate_preview').hide();
                                        $('#candidate_preview').css('opacity', '0');
                                        $rootScope.candidatePreview = null;
                                        $rootScope.previewHistory = null;
                                        $rootScope.$apply();
                                        setTimeout(function () {
                                            setPosition();
                                            if (scope.callback) scope.callback();
                                        }, 200);
                                    } else {
                                        setTimeout(function () {
                                            setPosition();
                                            if (scope.callback) scope.callback();
                                        }, 200);
                                    }
                                }
                            }
                        }

                        function findPos(obj) {
                            var obj2 = obj;
                            var curtop = 0;
                            var curleft = 0;
                            if (document.getElementById || document.all) {
                                do {
                                    curleft += obj.offsetLeft - obj.scrollLeft;
                                    curtop += obj.offsetTop - obj.scrollTop;
                                    obj = obj.offsetParent;
                                    obj2 = obj2.parentNode;
                                    while (obj2 != obj) {
                                        curleft -= obj2.scrollLeft;
                                        curtop -= obj2.scrollTop;
                                        obj2 = obj2.parentNode;
                                    }
                                } while (obj.offsetParent);
                            } else if (document.layers) {
                                curtop += obj.y;
                                curleft += obj.x;
                            }
                            return [curtop, curleft];
                        }

                        function translateLanguages() {
                            angular.forEach($rootScope.candidatePreview.languages, (val) => {
                                if (val.name) {
                                    val.translatedName = $filter('translateLangs')($rootScope.allLanguages[val.name]);
                                }
                            });
                        }

                        function open(resp) {
                            $rootScope.setDocCounter();
                            $(document).click(function (e) {
                                var container = $('#candidate_preview');
                                if (!container.is(e.target) && container.has(e.target).length === 0) {
                                    container.hide();
                                    $rootScope.candidatePreview = null;
                                    $rootScope.previewHistory = null;
                                    $rootScope.$apply();
                                    $(document).unbind('click');
                                }
                            });
                            $timeout(() => {
                                $rootScope.candidatePreview = resp;
                                $rootScope.candidatePreviewAdditional = !!(
                                    (resp.contacts && resp.contacts.length) ||
                                    resp.education ||
                                    (resp.languages && resp.languages.length) ||
                                    resp.employmentType ||
                                    resp.readyRelocate
                                );
                                $rootScope.lastCandidatePreview = resp.candidateId;

                                if (
                                    !$rootScope.candidatePreview.responsible &&
                                    $rootScope.candidatePreview.responsibleId &&
                                    $rootScope.persons
                                ) {
                                    $rootScope.candidatePreview.responsible = $rootScope.persons.find(
                                        (person) => person.personId === $rootScope.candidatePreview.responsibleId,
                                    );
                                }

                                translateLanguages();

                                if (!$rootScope.$$phase) {
                                    $rootScope.$apply();
                                }
                            }, 100);
                        }

                        function openCandidate(event) {
                            const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
                            if (isMac && scope.type === 'pipeline') {
                                $timeout(() => open(scope.candidate), 200);
                            } else {
                                $timeout(() => open(scope.candidate), 0);
                            }
                            CacheCandidates.add(scope.candidate);
                        }
                    },
                };
            },
        ])
        .directive('ngEnterSearch', function () {
            return function (scope, element, attrs) {
                element.bind('keypress', function (event) {
                    if (event.which === 13) {
                        scope.clickSearch();
                    }
                });
            };
        })
        .directive('recommendation', function () {
            return {
                restrict: 'EA',
                link: function (scope, element, attr) {
                    var buttonAdd = element.next('.recommendationAdd');
                    element.hover(function () {
                        var buttonAdd = element.next('.recommendationAdd');
                        buttonAdd.removeClass('blockDisplay');
                        var position = getPosition(buttonAdd[0]);
                        if (element[0].getAttribute('isOfset') == undefined) {
                            buttonAdd.offset({
                                top: buttonAdd.offset().top - element[0].scrollHeight / 2 - 13,
                                left: element[0].scrollWidth / 2 - 6,
                            });
                            element[0].setAttribute('isOfset', 'true');
                        }
                    });
                    element.mouseleave(function (event) {
                        var buttonAdd = element.next('.recommendationAdd');
                        buttonAdd.addClass('blockDisplay');
                    });
                },
            };
        })
        .directive('recbutton', function () {
            return {
                restrict: 'EA',
                link: function (scope, element, attr) {
                    element.hover(function () {
                        element.removeClass('blockDisplay');
                    });
                    element.mouseleave(function () {
                        element.addClass('blockDisplay');
                    });
                },
            };
        })
        .directive('modalAddFile', [
            '$filter',
            function ($filter) {
                return {
                    restrict: 'EA',
                    templateUrl: 'partials/modal/add-photo-candidate.html',
                    link: function (scope, element, attr) {
                        scope.loader = true;
                        scope.modalAddPhoto = true;
                        scope.showErrorAddPhotoMessage = false;
                        if (attr.page == 'candidate') {
                            scope.headerFile = $filter('translate')('You can select a photo on your computer');
                            scope.headerLink = $filter('translate')('Or provide a link to photos on the internet');
                        } else if (attr.page == 'client') {
                            scope.headerFile = $filter('translate')('You can select a logo on your computer');
                            scope.headerLink = $filter('translate')('Or provide a link to logo on the internet');
                        }
                        scope.showModalAddPhoto = function (headerText) {
                            scope.modalAddPhotoHeaderText = $filter('translate')(headerText);
                            scope.modalAddPhoto = true;
                            element.children().addClass('active');
                            element.children().removeClass('hide');
                            element.children().children().addClass('active');
                        };
                        scope.hideModalAddPhoto = function () {
                            if (scope.modalAddPhoto) {
                                element.children().addClass('hide');
                                element.children().removeClass('active');
                                element.children().children().removeClass('active');
                                scope.modalAddPhoto = false;
                                scope.photoUrl = '';
                            }
                        };
                    },
                };
            },
        ])
        .directive('statusColorNew', [
            '$compile',
            function ($compile) {
                return {
                    restrict: 'EA',
                    scope: {
                        old: '=',
                        new: '=',
                        icon: '=',
                        statusarr: '@',
                        customClass: '=',
                        customInterviewStateId: '=',
                    },
                    link: function (scope, element) {
                        let firstElements = `${createSpanForInterviewStatusHistory(
                            scope.statusarr,
                            scope.old,
                            null,
                            scope.customClass,
                            scope.customInterviewStateId,
                        )} <i class='fa fa-angle-right' aria-hidden='true' style='color: black;padding-right: 6px;'></i> ${createSpanForInterviewStatusHistory(
                            scope.statusarr,
                            scope.new,
                        )}`;

                        function bindAngularContext(targetElement) {
                            let angularElem;

                            angularElem = angular.element(targetElement);
                            $compile(angularElem)(scope);

                            return angularElem;
                        }

                        if (!scope.statusarr) {
                            scope.statusarr = '';
                        }
                        if (scope.new) {
                            element.append(bindAngularContext(firstElements));
                        } else {
                            scope.$watch('old', function () {
                                element.html(
                                    bindAngularContext(
                                        createSpanForInterviewStatusHistory(
                                            scope.statusarr,
                                            scope.old,
                                            true,
                                            scope.customClass,
                                            scope.customInterviewStateId,
                                        ),
                                    ),
                                );
                                // if (scope.icon) {
                                //     $(element).find("span").append('<i style="margin: 5px 0px 0px 5px;" class="fa fa-angle-down"></i>');
                                // }
                            });
                        }
                    },
                };
            },
        ])
        .directive('statusColorDiv', [
            '$compile',
            function ($compile) {
                return {
                    restrict: 'EA',
                    scope: {
                        old: '=',
                    },
                    link: function (scope, element) {
                        let div = createDivForInterviewStatusHistory(scope.old);
                        let angularElem = angular.element(div);
                        $compile(angularElem)(scope);
                        element.html(angularElem);
                    },
                };
            },
        ])
        .directive('highlights', [
            '$location',
            function ($location) {
                return {
                    restrict: 'EA',
                    scope: {
                        hgobject: '=',
                        hgtablecollspan: '@',
                        hglocationOfOneObject: '=',
                    },
                    link: function (scope, element) {
                        if (scope.hgobject === undefined) {
                            return;
                        }
                        if (scope.hgobject.highlights !== undefined && scope.hgobject.highlights.length > 0) {
                            var reg = /<highlight>|<\/highlight>/g;
                            var withoutHighlitedString = scope.hgobject.highlights[0].replace(reg, '');
                            if (withoutHighlitedString == scope.hgobject.position) {
                                scope.hgobject.position = scope.hgobject.highlights[0];
                            } else if (withoutHighlitedString == scope.hgobject.fullName) {
                                scope.hgobject.fullName = scope.hgobject.highlights[0];
                            } else {
                                $(
                                    "<tr><td style='padding-left: 4%;' colspan='" +
                                        scope.hgtablecollspan +
                                        1 +
                                        "'>" +
                                        scope.hgobject.highlights +
                                        '</td></tr>',
                                )
                                    .insertAfter(element)
                                    .on('click', function () {
                                        $location.path(scope.hglocationOfOneObject + scope.hgobject.localId);
                                        scope.$apply();
                                    });
                            }
                        }
                        if (scope.hgobject.files !== undefined) {
                            angular.forEach(scope.hgobject.files, function (value) {
                                angular.forEach(value.highlights, function (highlight) {
                                    $(
                                        "<tr><td style='padding-left: 4%;' title='" +
                                            value.fileName +
                                            "' colspan='" +
                                            scope.hgtablecollspan +
                                            "'>" +
                                            "<i style='padding-right: 10px' class='fa fa-paperclip'></i>" +
                                            highlight +
                                            '</td></tr>',
                                    )
                                        .insertAfter(element)
                                        .on('click', function () {
                                            $location.path(scope.hglocationOfOneObject + scope.hgobject.localId);
                                            scope.$apply();
                                        });
                                });
                            });
                        }
                    },
                };
            },
        ])
        .directive('ngContacts', function () {
            return function (scope, element, attrs) {
                var contact = scope.contact.contacts;
                if (contact == undefined) return;
                var phone = '';
                var skype = '';
                var email = '';
                angular.forEach(contact, function (val) {
                    if (angular.equals(val.type, 'mphone')) {
                        phone = val.value;
                    }
                    if (angular.equals(val.type, 'email')) {
                        email = '<a href="mailto:' + val.value + '" class="ng-binding">' + val.value + '</a>';
                    }
                    if (angular.equals(val.type, 'skype')) {
                        skype = val.value;
                    }
                });
                element.append('<span>' + email + ' ' + phone + ' ' + skype + '</span>');
            };
        })
        .directive('tofullinformation', [
            '$location',
            '$window',
            'frontMode',
            function ($location, $window, frontMode) {
                return {
                    restrict: 'EA',
                    link: function (scope, element, atrr) {
                        var page = frontMode == 'war' ? '/!#/' : '/!#/';
                        ///home.html#/ /hdemo.html#/
                        $(element.find('.clickable')).mousedown(function (e) {
                            if (e.target.className.includes('preventRedirectToClient')) return;

                            switch (e.which) {
                                case 1:
                                    if (e.target.href) {
                                        let href = e.target.href.split('!#')[1];
                                        $location.path(href);
                                    } else $location.path(atrr.tofullinformation);
                                    scope.$apply();
                                    break;
                                case 2:
                                    if (e.target.href) {
                                        let href = e.target.href.split('!#')[1];
                                        $location.path(href);
                                    } else $window.open($location.$$protocol + '://' + $location.$$host + page + atrr.tofullinformation);
                                    break;
                            }
                        });
                    },
                };
            },
        ])
        .directive('checkbox', function () {
            return {
                restrict: 'EAC',
                link: function (scope, element) {
                    if (element.checkbox) {
                        element.checkbox();
                    }
                },
            };
        })
        .directive('ngSelectColor', function () {
            return {
                restrict: 'EA',
                link: function (scope, element) {
                    if (element.find(':selected')) {
                        $(element).css('color', '#999');
                    } else {
                        $(element).css('color', 'black');
                    }
                    element.on('change', function () {
                        $.each(element.children(), function () {
                            if (!$(this).is(element.find(':first'))) {
                                $(this).css('color', 'black');
                            }
                        });
                        if (element.find(':selected').is(element.find(':first'))) {
                            $(this).css('color', '#999');
                        } else {
                            $(this).css('color', 'black');
                        }
                    });
                },
            };
        })
        .directive('isNumber', function () {
            return {
                require: 'ngModel',
                scope: {
                    model: '=',
                },
                link: function (scope) {
                    scope.$watch('model', function (newValue, oldValue) {
                        var arr = String(newValue).split('');
                        if (arr.length === 0) return;
                        if (arr.length === 1 && (arr[0] == '-' || arr[0] === '.')) return;
                        if (arr.length === 2 && newValue === '-.') return;
                        if (isNaN(newValue)) {
                            scope.model = oldValue;
                        }
                    });
                },
            };
        })
        .directive('optionsClass', [
            '$parse',
            function ($parse) {
                return {
                    require: 'select',
                    link: function (scope, elem, attrs, ngSelect) {
                        var optionsSourceStr = attrs.ngOptions.split(' ').pop(),
                            getOptionsClass = $parse(attrs.optionsClass);

                        scope.$watch(optionsSourceStr, function (items) {
                            angular.forEach(items, function (item, index) {
                                var classes = getOptionsClass(item),
                                    option = elem.find('option[value=' + index + ']');
                                angular.forEach(classes, function (add, className) {
                                    if (add) {
                                        angular.element(option).addClass(className);
                                    }
                                });
                            });
                        });
                    },
                };
            },
        ])
        .directive('googleplace', [
            'Service',
            function (Service) {
                return {
                    require: 'ngModel',
                    link: function (scope, element, attrs, model) {
                        var options = {
                            types: ['(regions)'],
                        };
                        if (!!google) {
                            var gPlace = new google.maps.places.Autocomplete(element[0], options);
                            google.maps.event.addListener(gPlace, 'place_changed', function (val) {
                                var place = gPlace.getPlace();
                                if (place) {
                                    place.formatted_address = $('#pac-input').val();
                                    var fullNameAr = place.formatted_address.split(',');

                                    if (
                                        similar_text(fullNameAr[1], fullNameAr[0]) == fullNameAr[0].length ||
                                        (similar_text(fullNameAr[0], fullNameAr[1]) / fullNameAr[0].length) * 100 > 80
                                    ) {
                                        place.formatted_address = fullNameAr[0] + ',' + fullNameAr[2];
                                    }
                                    var lat = place.geometry.location.k;
                                    var lng = place.geometry.location.D;
                                    if (scope.mapObject != null && google) {
                                        var location = new google.maps.LatLng(lat, lng);
                                        scope.mapObject.marker.setPosition(location);
                                        scope.mapObject.map.setCenter(location);
                                    }
                                    scope.$apply(function () {
                                        scope.region = Service.convertToRegionObject(place, scope);
                                        if (scope.region != undefined && scope.region.country == null) {
                                            scope.region.country =
                                                scope.region.city != null
                                                    ? scope.region.city
                                                    : scope.region.area != null
                                                    ? scope.region.area
                                                    : ' ';
                                            if (scope.region.fullName != undefined) {
                                                scope.region.fullName = scope.region.fullName.replace(',undefined', '');
                                            }
                                        }
                                        model.$setViewValue(place.formatted_address);
                                    });
                                    if (scope.progressUpdate != undefined) {
                                        scope.progressUpdate();
                                    }
                                } else {
                                    scope.regionInputOk = false;
                                    scope.region = null;
                                }
                            });
                        }
                    },
                };
            },
        ])
        .directive('googleplacerelocate', [
            'Service',
            function (Service) {
                return {
                    require: 'ngModel',
                    link: function (scope, element, attrs, model) {
                        var options = {
                            types: ['(regions)'],
                        };
                        if (!!google) {
                            var gPlace = new google.maps.places.Autocomplete(element[0], options);
                            google.maps.event.addListener(gPlace, 'place_changed', function (val) {
                                var place = gPlace.getPlace();
                                place.formatted_address = $('#pac-input2').val();
                                var fullNameAr = place.formatted_address.split(',');
                                if (
                                    similar_text(fullNameAr[1], fullNameAr[0]) == fullNameAr[0].length ||
                                    (similar_text(fullNameAr[0], fullNameAr[1]) / fullNameAr[0].length) * 100 > 80
                                ) {
                                    place.formatted_address = fullNameAr[0] + ',' + fullNameAr[2];
                                }
                                scope.$apply(function () {
                                    scope.regionToRelocate.push(Service.convertToRegionObject(place, scope));
                                    model.$setViewValue('');
                                    $('#pac-input2').val('');
                                });
                            });
                        }
                    },
                };
            },
        ])
        .directive('googleplacezip', [
            'Service',
            function (Service) {
                return {
                    require: 'ngModel',
                    link: function (scope, element, attrs, model) {
                        var options = {
                            types: ['(regions)'],
                        };
                        if (!!google) {
                            var gPlace = new google.maps.places.Autocomplete(element[0], options);
                            google.maps.event.addListener(gPlace, 'place_changed', function (val) {
                                var place = gPlace.getPlace();
                                if (place.address_components) {
                                    place.formatted_address = $('#pac-input3').val();
                                    var fullNameAr = place.formatted_address.split(',');
                                    if (
                                        similar_text(fullNameAr[1], fullNameAr[0]) == fullNameAr[0].length ||
                                        (similar_text(fullNameAr[0], fullNameAr[1]) / fullNameAr[0].length) * 100 > 80
                                    ) {
                                        place.formatted_address = fullNameAr[2];
                                    }
                                    scope.$apply(function () {
                                        scope.regionzip.push(Service.convertToRegionObject(place, scope));
                                        model.$setViewValue('');
                                        $('#pac-input3').val('');
                                    });
                                }
                            });
                        }
                    },
                };
            },
        ])
        .directive('googlePlaces', function () {
            return {
                restrict: 'E',
                replace: true,
                scope: { location: '=' },
                template:
                    '<input id="google_places_ac" name="google_places_ac" type="text" class="input-block-level"/>',
                link: function ($scope, elm, attrs) {
                    if (!!google) {
                        var autocomplete = new google.maps.places.Autocomplete($('#google_places_ac')[0], {});
                        google.maps.event.addListener(autocomplete, 'place_changed', function () {
                            var place = autocomplete.getPlace();
                            $scope.location = place.geometry.location.lat() + ',' + place.geometry.location.lng();
                            $scope.$apply();
                        });
                    }
                },
            };
        })
        .directive('addLogo', [
            '$rootScope',
            'Company',
            'notificationService',
            '$filter',
            function ($rootScope, Company, notificationService, $filter) {
                return {
                    restrict: 'AE',
                    link: function ($scope, elem, attr, ctrl) {
                        elem.on('click', function () {
                            if ($rootScope.me.recrutRole == 'admin') {
                                $('#the-file-input').click();
                            } else {
                                notificationService.error($filter('translate')('Only admin can set logo'));
                            }
                        });

                        var Cropper = window.Cropper;

                        $('#the-file-input')
                            .unbind('change')
                            .change(function () {
                                renderImage(this.files[0]);
                            });

                        renderImage = function (file) {
                            var reader = new FileReader();
                            reader.onload = function (event) {
                                var the_url = event.target.result;
                                var logoImg = new Image();
                                logoImg.src = the_url;
                                logoImg.onload = function () {
                                    $('#logo-button').hide();
                                    if (logoImg.width > 290 && logoImg.height > 290) {
                                        if ($('#cropper-wrap').length == 0) {
                                            $('#owner_photo_wrap').prepend(
                                                '<div id="cropper-wrap"> <div id="img-wrapper"> </div> <button id="close">' +
                                                    $filter('translate')('Close') +
                                                    '</button> <button id="cropp">' +
                                                    $filter('translate')('Accept_1') +
                                                    '</button> <div id="wrapper"></div>  </div> <div id="wrapperForPng"></div>',
                                            );
                                            $('#owner_photo_wrap').find('img').hide();
                                            $('#owner_photo_bubble_wrap').hide();
                                            $('#wrapperForPng').hide();
                                        }
                                        $('#img-wrapper').html("<img id='image' src='" + the_url + "'>");
                                        cropperFunc();
                                    } else if (logoImg.width == 290 && logoImg.height == 290) {
                                        Company.uploadCompanyLogo(the_url).then(
                                            function (data) {
                                                $scope.callbackAddLogo(data.data.objects[0]);
                                                $('#logo-button').show();
                                            },
                                            function (error) {
                                                notificationService.error(error.data.message);
                                                $('#logo-button').show();
                                            },
                                        );
                                    } else {
                                        $('#logo-button').show();
                                        notificationService.error(
                                            $filter('translate')('Please choose image 290 x 290 px or larger'),
                                        );
                                    }
                                };
                            };
                            reader.readAsDataURL(file);
                        };

                        function cropperFunc() {
                            var image = document.getElementById('image');
                            var cropper = new Cropper(image, {
                                aspectRatio: 1 / 1,
                                movable: false,
                                zoomable: false,
                            });

                            $('#cropp').on('click', function () {
                                var canvasImg = image.cropper.getCroppedCanvas();
                                var ctx = canvasImg.getContext('2d');

                                var canvasCopy = document.createElement('canvas');
                                var copyContext = canvasCopy.getContext('2d');
                                canvasCopy.width = 290;
                                canvasCopy.height = 290;
                                copyContext.drawImage(canvasImg, 0, 0, 290, 290);
                                canvasImg.width = 290;
                                canvasImg.height = 290;
                                ctx.drawImage(
                                    canvasCopy,
                                    0,
                                    0,
                                    canvasImg.width,
                                    canvasImg.height,
                                    0,
                                    0,
                                    canvasCopy.width,
                                    canvasCopy.height,
                                );
                                $scope.dataUrl = canvasImg.toDataURL();
                                $('#wrapperForPng').show();
                                $('#wrapperForPng').html(
                                    "<img  src='" +
                                        $scope.dataUrl +
                                        "' > <button id='cancel'>" +
                                        $filter('translate')('cancel') +
                                        "</button><button id='download'>" +
                                        $filter('translate')('save') +
                                        '</button>',
                                );
                                $('#cropper-wrap').hide();
                                $('#cancel').on('click', function () {
                                    $('#cropper-wrap').show();
                                    $('#wrapperForPng').find('img').remove();
                                    $('#cancel').remove();
                                    $('#download').remove();
                                });
                                $('#download').on('click', function () {
                                    Company.uploadCompanyLogo($scope.dataUrl).then(
                                        function (data) {
                                            $scope.callbackAddLogo(data.data.objects[0]);
                                            $('#company-logo').show();
                                            cropper.destroy();
                                            $('#cropper-wrap').remove();
                                            $('#wrapperForPng').remove();
                                            $('#the-file-input').val('');
                                            $('#owner_photo_wrap').find('img').show();
                                            $('#owner_photo_bubble_wrap').show();
                                            $('.block-company .img-section img').prop('href', '$rootScope.logoLink');
                                            $('#logo-button').show();
                                        },
                                        function (error) {
                                            notificationService.error(error.data.message);
                                        },
                                    );
                                });
                            });
                            $('#close').on('click', function () {
                                cropper.destroy();
                                $('#cropper-wrap').remove();
                                $('#wrapperForPng').remove();
                                $('#the-file-input').val('');
                                $('#owner_photo_wrap').find('img').show();
                                $('#owner_photo_bubble_wrap').show();
                                if ($rootScope.companyLogo == undefined) {
                                    $('#logo-button').show();
                                }
                            });
                        }
                    },
                };
            },
        ])
        .directive('addLogoTestCandidateQuestion', [
            '$rootScope',
            'Test',
            'notificationService',
            '$filter',
            function ($rootScope, Test, notificationService, $filter) {
                return {
                    restrict: 'AE',
                    link: function ($scope, elem, attr, ctrl) {
                        elem.on('click', function () {
                            if ($rootScope.me.recrutRole == 'admin') {
                                $('#the-file-input').click();
                            } else {
                                notificationService.error($filter('translate')('Only admin can set logo'));
                            }
                        });

                        var Cropper = window.Cropper;
                        $scope.openFiles = function (index) {
                            $rootScope.questionIndex = index;
                        };
                        $('#the-file-input')
                            .unbind('change')
                            .change(function () {
                                renderImage(this.files[0]);
                            });
                        renderImage = function (file) {
                            var reader = new FileReader();
                            reader.onload = function (event) {
                                var the_url = event.target.result;
                                var logoImg = new Image();
                                logoImg.src = the_url;
                                logoImg.onload = function () {
                                    $('#logo-button' + $rootScope.questionIndex).hide();
                                    if (
                                        (logoImg.width > 960 && logoImg.height > 380) ||
                                        (logoImg.width == 960 && logoImg.height > 380) ||
                                        (logoImg.width > 960 && logoImg.height == 380)
                                    ) {
                                        if ($rootScope.questionIndex != undefined) {
                                            if ($('#cropper-wrap').length == 0) {
                                                $('.owner_photo_wrap' + $rootScope.questionIndex).prepend(
                                                    '<div id="cropper-wrap"> <div id="img-wrapper"> </div> <button id="close">' +
                                                        $filter('translate')('Close') +
                                                        '</button> <button id="cropp">' +
                                                        $filter('translate')('Accept_1') +
                                                        '</button> <div id="wrapper"></div>  </div> <div id="wrapperForPng"></div>',
                                                );
                                                $('.owner_photo_wrap' + $rootScope.questionIndex)
                                                    .find('img')
                                                    .hide();
                                                $('.owner_photo_wrap' + $rootScope.questionIndex)
                                                    .find('.owner_photo_bubble_wrap' + $rootScope.questionIndex)
                                                    .hide();
                                                $('#wrapperForPng').hide();
                                            }
                                            $('#img-wrapper').html("<img id='image' src='" + the_url + "'>");
                                            cropperFunc($rootScope.questionIndex);
                                        }
                                    } else if (logoImg.width == 960 && logoImg.height == 380) {
                                        Test.uploadTestQuestionLogo(the_url).then(
                                            function (data) {
                                                if (data.data.status == 'ok') {
                                                    $scope.callbackTestQuestionLogo(
                                                        data.data.object,
                                                        $rootScope.questionIndex,
                                                    );
                                                    $('#logo-button' + $rootScope.questionIndex).show();
                                                } else {
                                                    notificationService.error(data.data.message);
                                                    $('#logo-button' + $rootScope.questionIndex).show();
                                                }
                                            },
                                            function (error) {
                                                notificationService.error(error.data.message);
                                                $('#logo-button' + $rootScope.questionIndex).show();
                                            },
                                        );
                                    } else {
                                        $('#logo-button' + $rootScope.questionIndex).show();
                                        notificationService.error(
                                            $filter('translate')('Please choose image 960 x 380 px or larger'),
                                        );
                                    }
                                };
                            };
                            reader.readAsDataURL(file);
                        };

                        function cropperFunc(index) {
                            var image = document.getElementById('image');
                            var cropper = new Cropper(image, {
                                aspectRatio: 960 / 380,
                                movable: false,
                                zoomable: false,
                            });

                            $('#cropp').on('click', function () {
                                var canvasImg = image.cropper.getCroppedCanvas();
                                var ctx = canvasImg.getContext('2d');

                                var canvasCopy = document.createElement('canvas');
                                var copyContext = canvasCopy.getContext('2d');
                                canvasCopy.width = 960;
                                canvasCopy.height = 380;
                                copyContext.drawImage(canvasImg, 0, 0, 960, 380);
                                canvasImg.width = 960;
                                canvasImg.height = 380;
                                ctx.drawImage(
                                    canvasCopy,
                                    0,
                                    0,
                                    canvasImg.width,
                                    canvasImg.height,
                                    0,
                                    0,
                                    canvasCopy.width,
                                    canvasCopy.height,
                                );
                                $scope.dataUrl = canvasImg.toDataURL();
                                $('#wrapperForPng').show();
                                $('#wrapperForPng').html(
                                    "<img  src='" +
                                        $scope.dataUrl +
                                        "' > <button id='cancel'>" +
                                        $filter('translate')('cancel') +
                                        "</button><button id='download'>" +
                                        $filter('translate')('save') +
                                        '</button>',
                                );
                                $('#cropper-wrap').hide();
                                $('#cancel').on('click', function () {
                                    $('#cropper-wrap').show();
                                    $('#wrapperForPng').find('img').remove();
                                    $('#cancel').remove();
                                    $('#download').remove();
                                });
                                $('#download').on('click', function () {
                                    Test.uploadTestQuestionLogo($scope.dataUrl).then(
                                        function (data) {
                                            $scope.callbackTestQuestionLogo(data.data.object, $rootScope.questionIndex);
                                            $('#company-logo').show();
                                            cropper.destroy();
                                            $('#cropper-wrap').remove();
                                            $('#wrapperForPng').remove();
                                            $('#the-file-input').val('');
                                            $('.owner_photo_wrap' + index)
                                                .find('img')
                                                .show();
                                            $('.owner_photo_wrap' + index)
                                                .find('.owner_photo_bubble_wrap' + index)
                                                .show();
                                            $('.block-company .img-section img').prop(
                                                'href',
                                                '$rootScope.testQuestionLogoLink',
                                            );
                                            $('#logo-button' + index).show();
                                        },
                                        function (error) {
                                            notificationService.error(error.data.message);
                                        },
                                    );
                                });
                            });
                            $('#close').on('click', function () {
                                cropper.destroy();
                                $('#cropper-wrap').remove();
                                $('#wrapperForPng').remove();
                                $('#the-file-input').val('');
                                $('.owner_photo_wrap' + index)
                                    .find('img')
                                    .hide();
                                $('.owner_photo_wrap' + index)
                                    .find('.owner_photo_bubble_wrap' + index)
                                    .show();
                                $('#logo-button' + index).show();
                            });
                        }
                    },
                };
            },
        ])
        .directive('addPromoLogo', [
            '$rootScope',
            'Vacancy',
            'notificationService',
            '$filter',
            function ($rootScope, Vacancy, notificationService, $filter) {
                return {
                    restrict: 'AE',
                    link: function ($scope, elem, attr, ctrl) {
                        elem.on('click', function () {
                            if ($rootScope.me.recrutRole == 'admin' || $rootScope.me.recrutRole == 'recruter') {
                                $('#the-file-promo-logo').click();
                            } else {
                                notificationService.error($filter('translate')('Only admin or recruiter can set logo'));
                            }
                        });

                        var Cropper = window.Cropper;

                        $('#the-file-promo-logo')
                            .unbind('change')
                            .change(function () {
                                $scope.storeForPromoLogo = $rootScope.promoLogo;
                                $rootScope.promoLogo = null;
                                renderImage(this.files[0]);
                            });

                        $scope.callbackAddPromoLogo = $scope.callbackAddPromoLogo || $scope.vm.callbackAddPromoLogo;

                        renderImage = function (file) {
                            var reader = new FileReader();
                            reader.onload = function (event) {
                                var the_url = event.target.result;
                                var logoImg = new Image();
                                logoImg.src = the_url;
                                logoImg.onload = function () {
                                    setTimeout(() => {
                                        $('.social-vacancy-img__addPhoto').hide();
                                    }, 0);
                                    if (logoImg.width > 290 && logoImg.height > 290) {
                                        if ($('#cropper-wrap').length == 0) {
                                            $('.addPhoto-icon-wrapper').hide();
                                            $('.openPhoto-icon-wrapper').hide();
                                            $('#owner_photo_wrap').prepend(
                                                '<div id="cropper-wrap"> <div id="img-wrapper"> <span>test</span></div> <div class="cropper-button-wrapper"><button style="border-radius:25px" id="close">' +
                                                    $filter('translate')('Close') +
                                                    '</button> <button style="border-radius:25px" id="cropp">' +
                                                    $filter('translate')('Accept_1') +
                                                    '</button></div><div id="wrapper"></div>  </div> <div id="wrapperForPng"></div>',
                                            );
                                            $('#owner_photo_wrap').find('img').hide();
                                            $('#owner_photo_bubble_wrap').hide();
                                            $('#wrapperForPng').hide();
                                        }
                                        $('#img-wrapper').html("<img id='image' src='" + the_url + "'>");
                                        cropperFunc();
                                    } else if (logoImg.width == 290 && logoImg.height == 290) {
                                        Vacancy.uploadPromoLogo(the_url).then(
                                            function (data) {
                                                $scope.callbackAddPromoLogo(data.data.object);
                                                $('#logo-button').show();
                                            },
                                            function (error) {
                                                notificationService.error(error.data.message);
                                                $('#logo-button').show();
                                            },
                                        );
                                    } else {
                                        notificationService.error(
                                            $filter('translate')('Please choose image 290 x 290 px or larger'),
                                        );
                                        $('.social-vacancy-img__addPhoto').show();
                                    }
                                };
                            };
                            reader.readAsDataURL(file);
                        };

                        function cropperFunc() {
                            var image = document.getElementById('image');
                            var cropper = new Cropper(image, {
                                aspectRatio: 1 / 1,
                                movable: false,
                                zoomable: false,
                            });

                            $('#cropp').on('click', function () {
                                var canvasImg = image.cropper.getCroppedCanvas();
                                var ctx = canvasImg.getContext('2d');

                                var canvasCopy = document.createElement('canvas');
                                var copyContext = canvasCopy.getContext('2d');
                                canvasCopy.width = 500;
                                canvasCopy.height = 500;
                                copyContext.drawImage(canvasImg, 0, 0, 500, 500);
                                canvasImg.width = 500;
                                canvasImg.height = 500;
                                ctx.drawImage(
                                    canvasCopy,
                                    0,
                                    0,
                                    canvasImg.width,
                                    canvasImg.height,
                                    0,
                                    0,
                                    canvasCopy.width,
                                    canvasCopy.height,
                                );
                                $scope.dataUrl = canvasImg.toDataURL();
                                $('#cropper-wrap').hide();
                                $rootScope.loading = true;
                                Vacancy.uploadPromoLogo($scope.dataUrl).then(
                                    function (data) {
                                        $scope.callbackAddPromoLogo(data.data.object);
                                        $('#company-logo').show();
                                        cropper.destroy();
                                        $('#cropper-wrap').remove();
                                        $('#wrapperForPng').remove();
                                        $('#the-file-promo-logo').val('');
                                        $('#owner_photo_wrap').find('img').show();
                                        $('#owner_photo_bubble_wrap').show();
                                        $('.block-company .img-section img').prop('href', '$rootScope.promoLogoLink');
                                        $('#logo-button').show();
                                        $rootScope.loading = false;
                                    },
                                    function (error) {
                                        notificationService.error(error.data.message);
                                    },
                                );
                            });
                            $('#close').on('click', function () {
                                cropper.destroy();
                                $rootScope.promoLogo = $scope.storeForPromoLogo ? $scope.storeForPromoLogo : null;
                                $('#cropper-wrap').remove();
                                $('#wrapperForPng').remove();
                                $('#the-file-promo-logo').val('');
                                $('.social-vacancy-img__addPhoto').show();
                                $('.add-photo').show();
                                $rootScope.$apply();
                                $('.blur-wrapper').mouseleave();
                            });
                        }
                    },
                };
            },
        ])
        .directive('clickAnywhereButHere', function ($rootScope, $document) {
            return {
                restrict: 'A',
                link: function (scope, elem, attr, ctrl) {
                    elem.on('click', function (e) {
                        $('.clever-window.hideable').not('.ng-hide').addClass('ng-hide');
                        e.stopPropagation();
                    });
                    $document.on('click', function () {
                        $rootScope.$$phase || scope.$apply(attr.clickAnywhereButHere);
                    });
                },
            };
        })
        .directive('hideOnclickAnywhereButHere', function ($document) {
            return {
                restrict: 'A',
                scope: {
                    show: '=',
                },
                link: function (scope, elem, attr, ctrl) {
                    if (scope.show != false) {
                        elem.on('click', function (e) {
                            var hidden = true;
                            if (!elem.children('.clever-window').hasClass('ng-hide')) {
                                hidden = false;
                            }
                            $('.clever-window.hideable').not('.ng-hide').addClass('ng-hide');
                            if (hidden) {
                                elem.children('.clever-window').removeClass('ng-hide');
                            }
                            e.stopPropagation();
                        });
                        $document.on('click', function () {
                            $('.clever-window.hideable').not('.ng-hide').addClass('ng-hide');
                        });
                    }
                },
            };
        })
        .directive('priviewByLocalId', [
            'Service',
            '$window',
            'Candidate',
            'CacheCandidates',
            '$rootScope',
            '$location',
            '$sce',
            'Person',
            '$timeout',
            '$filter',
            function (
                Service,
                $window,
                Candidate,
                CacheCandidates,
                $rootScope,
                $location,
                $sce,
                Person,
                $timeout,
                $filter,
            ) {
                return {
                    scope: {
                        candidate: '=',
                        pageid: '@',
                        callback: '=',
                    },
                    restrict: 'A',
                    link: function (scope, element, attrs) {
                        var checkShow = false;
                        var isOpen = false;
                        element.click(function (event) {
                            if (!isOpen) {
                                $rootScope.loadingPriview = true;
                                timeToOPen(event);
                                setTimeout(function () {
                                    $rootScope.loadingPriview = false;
                                }, 0);
                            }
                        });
                        element.mouseup(function () {
                            checkShow = false;
                            isOpen = false;
                        });

                        function setPosition() {
                            var pos = findPos(element.find('.text_change')[0]);
                            var preview_top = pos[0] + 5 - $('#candidate_preview').height() / 2;
                            var checkLessThenNull = false;
                            if (preview_top < 0) {
                                checkLessThenNull = true;
                            }
                            $('#candidate_preview').css({
                                top: !checkLessThenNull ? preview_top : '41px',
                                left: pos[1] + 50,
                            });
                            $('#arrow_preview').css({
                                top: !checkLessThenNull ? $('#candidate_preview').height() / 2 - 17 : '50px',
                                left: '-12px',
                            });
                            $('#' + scope.pageid).click(function () {
                                $('#candidate_preview').css({
                                    top: 0,
                                    left: 0,
                                });
                                $('#candidate_preview').hide();
                                $('#vacancy').off('click');
                                $rootScope.candidatePreview = null;
                                isOpen = false;
                            });
                            $('#candidate_preview').show();
                            $('#candidate_preview').css('opacity', '1');
                            isOpen = true;
                        }

                        function timeToOPen(event) {
                            openCandidate(event);
                            if (!checkShow) {
                                checkShow = true;
                                if (checkShow && !isOpen) {
                                    if ($rootScope.candidatePreview) {
                                        $('#candidate_preview').hide();
                                        $rootScope.candidatePreview = null;
                                        $rootScope.previewHistory = null;
                                        $rootScope.$apply();
                                        setTimeout(function () {
                                            setPosition();
                                            if (scope.callback) scope.callback();
                                        }, 50);
                                    } else {
                                        setTimeout(function () {
                                            setPosition();
                                            if (scope.callback) scope.callback();
                                        }, 0);
                                    }
                                }
                            }
                        }

                        function translateLanguages() {
                            angular.forEach($rootScope.candidatePreview.languages, (val) => {
                                if (val.name) {
                                    val.translatedName = $filter('translateLangs')($rootScope.allLanguages[val.name]);
                                }
                            });
                        }

                        function findPos(obj) {
                            var obj2 = obj;
                            var curtop = 0;
                            var curleft = 0;
                            if (document.getElementById || document.all) {
                                do {
                                    curleft += obj.offsetLeft - obj.scrollLeft;
                                    curtop += obj.offsetTop - obj.scrollTop;
                                    obj = obj.offsetParent;
                                    obj2 = obj2.parentNode;
                                    while (obj2 != obj) {
                                        curleft -= obj2.scrollLeft;
                                        curtop -= obj2.scrollTop;
                                        obj2 = obj2.parentNode;
                                    }
                                } while (obj.offsetParent);
                            } else if (document.layers) {
                                curtop += obj.y;
                                curleft += obj.x;
                            }
                            return [curtop, curleft];
                        }

                        function open(resp) {
                            $rootScope.setDocCounter();
                            $(document).click(function (e) {
                                var container = $('#candidate_preview');
                                if (!container.is(e.target) && container.has(e.target).length === 0) {
                                    container.hide();
                                    $rootScope.candidatePreview = null;
                                    $rootScope.previewHistory = null;
                                    $rootScope.$apply();
                                    $(document).unbind('click');
                                }
                            });
                            $timeout(() => {
                                $rootScope.candidatePreview = resp;
                                $rootScope.candidatePreviewAdditional = !!(
                                    resp.contacts.length ||
                                    resp.education ||
                                    resp.languages.length ||
                                    resp.employmentType ||
                                    resp.readyRelocate
                                );
                                $rootScope.lastCandidatePreview = resp.candidateId;

                                if (
                                    !$rootScope.candidatePreview.responsible &&
                                    $rootScope.candidatePreview.responsibleId &&
                                    $rootScope.persons
                                ) {
                                    $rootScope.candidatePreview.responsible = $rootScope.persons.find(
                                        (person) => person.personId === $rootScope.candidatePreview.responsibleId,
                                    );
                                }

                                translateLanguages();

                                if (!$rootScope.$$phase) {
                                    $rootScope.$apply();
                                }
                            });
                        }

                        function openCandidate(event) {
                            Candidate.one({ localId: scope.candidate.localId }, function (resp) {
                                open(resp.object);
                                CacheCandidates.add(resp.object);
                            });
                        }
                    },
                };
            },
        ])
        .directive('descriptionTreatment', [
            '$filter',
            function ($filter) {
                return {
                    restrict: 'EA',
                    scope: {
                        description: '=',
                    },
                    link: function (scope, element, attrs) {
                        function linkify(text) {
                            let pattern =
                                /((?:href|src)=")?(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;\u0400-\u04FF]*[-A-Z0-9+&@#\/%=~_|])/gi;

                            // remove tag 'a' from string to create new links then
                            // let inputText = text.replace(/<a\b[^>]*>|<\/a>/g, '');

                            return text.replace(pattern, function (match, attr) {
                                if (typeof attr != 'undefined') {
                                    return match;
                                }
                                return '<a target="_blank" href="' + match + '">' + match + '</a>';
                            });
                        }

                        scope.$watch('description', function (newval, oldval) {
                            if (newval) {
                                element.html(linkify(scope.description));
                                element.children().each(function () {
                                    if ($(this).html() == '&nbsp;') {
                                        $(this).remove();
                                    } else {
                                        return false;
                                    }
                                });
                            }
                        });
                    },
                };
            },
        ])
        .directive('ngContextMenu', function ($parse) {
            var renderContextMenu = function ($scope, event, options) {
                if (!$) {
                    var $ = angular.element;
                }
                $(event.currentTarget).addClass('context');
                var $contextMenu = $('<div>');
                $contextMenu.addClass('dropdown clearfix');
                var $ul = $('<ul>');
                var body = document.body,
                    html = document.documentElement;

                var height = Math.max(
                    body.scrollHeight,
                    body.offsetHeight,
                    html.clientHeight,
                    html.scrollHeight,
                    html.offsetHeight,
                );
                $ul.addClass('dropdown-menu');
                $ul.attr({ role: 'menu' });
                $ul.css({
                    display: 'block',
                    position: 'absolute',
                    left: event.pageX + 'px',
                    top: event.pageY + 'px',
                });
                angular.forEach(options, function (item, i) {
                    var $li = $('<li>');
                    if (item === null) {
                        $li.addClass('divider');
                    } else {
                        $a = $('<a>');
                        $a.attr({ tabindex: '-1' });
                        $a.text(item[0]);
                        $li.append($a);
                        $li.on('click', function () {
                            $scope.$apply(function () {
                                item[1].call($scope, $scope);
                            });
                        });
                    }
                    $ul.append($li);
                });
                $contextMenu.append($ul);
                $contextMenu.css({
                    width: '100%',
                    height: height,
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    zIndex: 9999999,
                });
                $(document).find('body').append($contextMenu);
                $contextMenu
                    .on('click', function (e) {
                        $(event.currentTarget).removeClass('context');
                        $contextMenu.remove();
                    })
                    .on('contextmenu', function (event) {
                        $(event.currentTarget).removeClass('context');
                        event.preventDefault();
                        $contextMenu.remove();
                    });
            };
            return function ($scope, element, attrs) {
                element.on('contextmenu', function (event) {
                    $scope.$apply(function () {
                        event.preventDefault();
                        var options = $scope.$eval(attrs.ngContextMenu);
                        if (options instanceof Array) {
                            renderContextMenu($scope, event, options);
                        } else {
                            throw '"' + attrs.ngContextMenu + '" not an array';
                        }
                    });
                });
            };
        })
        .directive('dotdotdot', function () {
            return function (scope, element, attrs) {
                $(element).dotdotdot({
                    watch: true,
                    //height:Number(attrs.dotdotdot)
                });
            };
        })
        .directive('dotdotdot2', function () {
            return function (scope, element, attrs) {
                $(element).dotdotdot({
                    watch: true,
                    height: Number(attrs.dotdotdot2),
                });
            };
        })
        .directive('customScrollbarForNotices', [
            '$rootScope',
            function ($rootScope) {
                return {
                    scope: {
                        minWindowWidth: '=',
                        alwaysShowScrollbar: '=',
                    },
                    link: function (scope, element, attrs) {
                        if (!scope.minWindowWidth || window.innerWidth > scope.minWindowWidth) {
                            attrs.$observe('customScrollbarForNotices', function (value) {
                                if (value) {
                                    $(element).mCustomScrollbar({
                                        theme: 'dark',
                                        scrollInertia: 1000,
                                        alwaysShowScrollbar: Number(value) > 2 ? 1 : 0,
                                        live: Number(value) > 2 ? 'on' : 'off',
                                        advanced: {
                                            updateOnBrowserResize: Number(value) > 2 ? true : false,
                                            updateOnContentResize: Number(value) > 2 ? true : false,
                                        },
                                    });
                                }
                            });
                        }
                    },
                };
            },
        ])
        .directive('customScrollbar', function () {
            // http://manos.malihu.gr/jquery-custom-content-scroller/ - documentation
            return {
                scope: {
                    minWindowWidth: '=',
                    alwaysShowScrollbar: '=',
                    onScrollMethod: '=',
                    onTotalScrollMethod: '=',
                },
                link: function (scope, element, attrs) {
                    if (!scope.minWindowWidth || window.innerWidth > scope.minWindowWidth) {
                        $(element).mCustomScrollbar({
                            theme: 'dark',
                            scrollInertia: 1000,
                            mouseWheelPixels: 170,
                            alwaysShowScrollbar: scope.alwaysShowScrollbar ? 1 : 0,
                            advanced: {
                                updateOnBrowserResize: true,
                                updateOnContentResize: true,
                            },
                            callbacks: {
                                onScroll: function () {
                                    scope.onScrollMethod && scope.onScrollMethod(this);
                                },
                                onTotalScroll: function () {
                                    scope.onTotalScrollMethod && scope.onTotalScrollMethod(this);
                                },
                            },
                        });
                    }
                },
            };
        })
        .directive('popup', function () {
            return function ($scope, element, attrs) {
                element.popup({
                    position: 'right center',
                });
            };
        })
        .directive('ngMin', function () {
            return {
                restrict: 'A',
                require: 'ngModel',
                link: function (scope, elem, attr, ctrl) {
                    scope.$watch(attr.ngMin, function () {
                        ctrl.$setViewValue(ctrl.$viewValue);
                    });
                    var minValidator = function (value) {
                        var min = scope.$eval(attr.ngMin) || 0;
                        if (!isEmpty(value) && value < min) {
                            ctrl.$setValidity('ngMin', false);
                            return undefined;
                        } else {
                            ctrl.$setValidity('ngMin', true);
                            return value;
                        }
                    };

                    ctrl.$parsers.push(minValidator);
                    ctrl.$formatters.push(minValidator);
                },
            };
        })
        .directive('ngMax', function () {
            return {
                restrict: 'A',
                require: 'ngModel',
                link: function (scope, elem, attr, ctrl) {
                    scope.$watch(attr.ngMax, function () {
                        ctrl.$setViewValue(ctrl.$viewValue);
                    });
                    var maxValidator = function (value) {
                        var max = scope.$eval(attr.ngMax) || Infinity;
                        if (!isEmpty(value) && value > max) {
                            ctrl.$setValidity('ngMax', false);
                            return undefined;
                        } else {
                            ctrl.$setValidity('ngMax', true);
                            return value;
                        }
                    };

                    ctrl.$parsers.push(maxValidator);
                    ctrl.$formatters.push(maxValidator);
                },
            };
        })
        .directive('showPreviewCandidate', [
            'Service',
            function (Service) {
                return {
                    scope: {
                        candidate: '=',
                    },
                    restrict: 'A',
                    link: function (scope, element, attrs) {
                        let showDocument = false;
                        if (scope.candidate.files) {
                            angular.forEach(scope.candidate.files, function (resp) {
                                Service.initDocuments(resp);
                                if (resp.showFiles) {
                                    showDocument = true;
                                    resp.showDocument = true;
                                }
                            });
                        }
                        if (showDocument) {
                            $(element).addClass('attachment');
                            $(element).click(
                                function () {
                                    $(this).removeClass('attachment');
                                    $(this).addClass('unhide');
                                },
                                function () {
                                    $(this).addClass('attachment');
                                    $(this).removeClass('unhide');
                                },
                            );
                        } else {
                            $(element).addClass('unhide');
                        }
                    },
                };
            },
        ])
        .directive('myEnter', function () {
            return function (scope, element, attrs) {
                element.bind('keydown keypress', function (event) {
                    if (event.which === 13) {
                        scope.$apply(function () {
                            scope.$eval(attrs.myEnter);
                        });

                        event.preventDefault();
                    }
                });
            };
        })
        .directive('clickOnEnter', function () {
            return function (scope, element, attrs) {
                element.bind('keydown keypress', function (event) {
                    if (event.which === 13) {
                        $(attrs.clickOnEnter).click();

                        event.preventDefault();
                    }
                });
            };
        })
        .directive('filterList', function ($timeout) {
            return {
                link: function (scope, element, attrs) {
                    var li = Array.prototype.slice.call(element[0].children);

                    function filterBy(value) {
                        li.forEach(function (el) {
                            if ($(el).attr('role') == 'tab') {
                                el.className =
                                    el.outerText.toLowerCase().indexOf(value.toLowerCase()) !== -1
                                        ? 'panel-heading'
                                        : 'ng-hide';
                                if (el.outerText.toLowerCase().indexOf(value.toLowerCase()) !== -1) {
                                    $(el).parent().css({
                                        display: 'block',
                                        'margin-top': '20px',
                                    });
                                    if (el.className != 'ng-hide') {
                                        $(el).parents('.guideAndWork').find('.mrg-bottom').css('display', 'none');
                                    }
                                    if (scope.searchFaq.length == 0) {
                                        $(el).parents('.guideAndWork').find('.mrg-bottom').css('display', 'block');
                                    }
                                } else {
                                    $(el).parents('.guideAndWork').find('.mrg-bottom').css('display', 'none');
                                    $(el).parent().css('display', 'none');
                                }
                            } else {
                                if (el.textContent.toLowerCase().indexOf(value.toLowerCase()) !== -1) {
                                    $(el).parent().css('display', 'block');
                                    $(el).parent()[0].children[0].className = 'panel-heading';
                                }
                            }
                        });
                    }

                    scope.$watch(attrs.filterList, function (newVal, oldVal) {
                        if (newVal !== oldVal) {
                            filterBy(newVal);
                        }
                    });
                },
            };
        })
        .directive('fileread', [
            function () {
                return {
                    restrict: 'AE',
                    scope: {
                        fileread: '=',
                    },
                    link: function (scope, element, attributes) {
                        element.bind('change', function (changeEvent) {
                            var reader = new FileReader();
                            reader.onload = function (loadEvent) {
                                scope.$apply(function () {
                                    var txt = '';
                                    if ('files' in element[0]) {
                                        for (var i = 0; i < element[0].files.length; i++) {
                                            var file = element[0].files[i];
                                            if ('name' in file) {
                                                txt += file.name;
                                            }
                                        }
                                    }
                                    scope.fileread = txt;
                                });
                            };
                            reader.readAsDataURL(changeEvent.target.files[0]);
                        });
                    },
                };
            },
        ])
        .directive('selectWithScroll', [
            function () {
                return {
                    restrict: 'A',
                    scope: {
                        scrollSize: '=',
                        activeClass: '=',
                    },
                    link: function (scope, element) {
                        element.on({
                            mousedown: () => showOptions(event),
                            change: () => reset(event),
                            blur: () => reset(event),
                            contextmenu: () => disableContextMenu(),
                        });

                        let optionsLength = element.children('option').length;
                        let showUsers = !element.attr('size');
                        let selectedOption = null;

                        function showOptions(e) {
                            if (showUsers && optionsLength > scope.scrollSize - 1) {
                                element.attr('size', scope.scrollSize);
                                element.addClass(scope.activeClass);
                                showUsers = false;
                            }

                            if (
                                e.target === selectedOption &&
                                !showUsers &&
                                e.target.tagName.toLowerCase() !== 'select'
                            ) {
                                reset(e);
                            }

                            if (e.target.tagName.toLowerCase() === 'option') {
                                selectedOption = e.target;
                            }
                        }

                        function reset(e) {
                            element.removeAttr('size');
                            element.removeClass(scope.activeClass);
                            showUsers = true;
                            if (e.target.tagName.toLowerCase() === 'option') selectedOption = e.target;
                            if (e.type === 'change') element.blur();
                        }

                        function disableContextMenu() {
                            selectedOption = null;
                            return false;
                        }
                    },
                };
            },
        ])
        .directive('fixedHeaderTable', [
            '$window',
            '$rootScope',
            '$compile',
            '$filter',
            function ($window, $rootScope, $compile, $filter) {
                return {
                    restrict: 'EA',
                    scope: {
                        columns: '=',
                        secondColumns: '=',
                        vacancies: '=',
                    },
                    link: function (scope, element, attrs) {
                        scope.$watch('vacancies', function (newValue) {
                            if (newValue) setTable();
                        });

                        function setTable() {
                            $(element).after(
                                '<table class="table" id="header-fixed" columns="10" second-columns="8" style="position: fixed;top: 0;display: none;background-color: inherit; width: calc(100% - 30px);"></table>',
                            );
                            // setTimeout(function(){
                            var tableOffset = $('#' + attrs.id).offset().top;
                            /////////////////////////////////////////////////////////////////////////  Id is a must!!!!!!
                            /////////////////////////////////////////////////////////////////////////  ".main-header" is a must!!!!!!
                            var header = $('#' + attrs.id + ' #main-header').clone();
                            var secondHeader = $('#' + attrs.id + ' #second_header').clone();
                            $compile(header)(scope);
                            $compile(secondHeader)(scope);
                            var $fixedHeader = $('#header-fixed').append(header);
                            $fixedHeader = $('#header-fixed').append(secondHeader);
                            setTimeout(function () {
                                // $('#header-fixed #main-header td:nth-child(1)').css('width', '103');
                                // $('#header-fixed #main-header td:nth-child(2)').css('width', '268');
                                // $('#header-fixed #main-header td:nth-child(3)').css('width', '103');
                                // $('#header-fixed #main-header td:nth-child(4)').css('width', '132');
                                // $('#header-fixed #main-header td:nth-child(5)').css('width', '96');
                                // $('#header-fixed #main-header td:nth-child(6)').css('width', '89');
                                // $('#header-fixed #main-header td:nth-child(7)').css('width', '239');
                                // $('#header-fixed #main-header td:nth-child(8)').css('width', '271');
                                // $('#header-fixed #main-header td:nth-child(9)').css('width', '127');
                                // $('#header-fixed #main-header td:nth-child(10)').css('width', '152');
                                //for (var i = 1; i <= scope.columns; i++){
                                //    $("#header-fixed #main-header td:nth-child("+i+")").css('width', $('#'+ attrs.id + " #main-header td:nth-child("+i+")").css('width'));
                                //}
                                if (secondHeader) {
                                    // $('#header-fixed #second_header td:nth-child(1)').css('width', '105');
                                    // $('#header-fixed #second_header td:nth-child(2)').css('width', '119');
                                    // $('#header-fixed #second_header td:nth-child(3)').css('width', '93');
                                    // $('#header-fixed #second_header td:nth-child(4)').css('width', '119');
                                    // $('#header-fixed #second_header td:nth-child(5)').css('width', '56');
                                    // $('#header-fixed #second_header td:nth-child(6)').css('width', '84');
                                    // $('#header-fixed #second_header td:nth-child(7)').css('width', '88');
                                    // $('#header-fixed #second_header td:nth-child(8)').css('width', '84');
                                    //for (var i = 1; i <= scope.secondColumns; i++){
                                    //    $("#header-fixed #second_header td:nth-child("+i+")").css('width', $('#'+ attrs.id + " #second_header td:nth-child("+i+")").css('width'));
                                    //}
                                }
                            }, 1000);

                            $(window).bind('scroll', function () {
                                var offset = $(this).scrollTop();
                                var width =
                                    window.innerWidth ||
                                    document.documentElement.clientWidth ||
                                    document.body.clientWidth;
                                let tr;

                                if (document.querySelector('tr')) {
                                    tr = document.querySelector('tr').offsetWidth;
                                }

                                if (width <= '1106') {
                                    $fixedHeader.css('display', 'inline-table');
                                    $fixedHeader.css('width', '100%');
                                    $fixedHeader.css('left', '0');
                                } else if (tr >= '1530') {
                                    $fixedHeader.css('display', 'inline-table');
                                    $fixedHeader.css('width', 'calc(82% - 37px)');
                                } else {
                                    $fixedHeader.css('display', 'flex');
                                    $fixedHeader.css('width', 'calc(100% - 30px)');
                                    $fixedHeader.css('left', 'unset');
                                }

                                if (offset >= tableOffset && $fixedHeader.is(':hidden')) {
                                    if (window.screen.width >= '1600') {
                                        $fixedHeader.css('display', 'flex');
                                        // $('#header-fixed #main-header td:nth-child(7)').css('width', '226');
                                        // $('#header-fixed #main-header td:nth-child(8)').css('width', '225');
                                        // $('#header-fixed #main-header td:nth-child(9)').css('width', '157');
                                        // $('#header-fixed #main-header td:nth-child(10)').css('width', '183');
                                    } else {
                                        // $fixedHeader.css('display', 'block');
                                        $fixedHeader.css('display', 'flex');
                                    }
                                } else if (offset < tableOffset) {
                                    $fixedHeader.hide();
                                }
                            });
                            // },100)
                        }
                    },
                };
            },
        ])
        .directive('datepickerForTask', [
            '$filter',
            '$rootScope',
            '$translate',
            '$route',
            'Task',
            function ($filter, $rootScope, $translate, $route, Task) {
                return {
                    restrict: 'EA',
                    link: function ($scope, element, attrs) {
                        const updateTasks = $scope.updateTasks || $scope.$parent.vm.updateTasks;
                        const getLastEvent = $scope.updateTasks || $scope.$parent.vm.updateTasks;
                        const updateHistory =
                            $rootScope.getHistoryForAllActions || $scope.$parent.vm.getHistoryForAllActions;
                        $('.editDateTask')
                            .datetimepicker({
                                format: 'dd/mm/yyyy hh:00',
                                startView: 2,
                                minView: 1,
                                autoclose: true,
                                weekStart: $rootScope.currentLang == 'ru' || $rootScope.currentLang == 'ua' ? 1 : 7,
                                language: $translate.use(),
                                initialDate: new Date(),
                                startDate: new Date(),
                            })
                            .on('changeDate', function (data) {
                                if ($rootScope.editableTask)
                                    $rootScope.editableTask.targetDate = $('.editDateTask').datetimepicker('getDate');
                                if ($scope.$parent.vm.editableTask)
                                    $scope.$parent.vm.editableTask.targetDate =
                                        $('.editDateTask').datetimepicker('getDate');
                                if ($scope.roundMinutes) $scope.roundMinutes($rootScope.editableTask.targetDate);
                                Task.changeTargetDate(
                                    {
                                        taskId: $rootScope.editableTask
                                            ? $rootScope.editableTask.taskId
                                            : $scope.$parent.vm.editableTask.taskId,
                                        date: $rootScope.editableTask
                                            ? $rootScope.editableTask.targetDate
                                            : $scope.$parent.vm.editableTask.targetDate,
                                    },
                                    function (resp) {
                                        if ($rootScope.activePage != 'Activity') {
                                            if (updateHistory) updateHistory();
                                            if (updateTasks) updateTasks();
                                            if (getLastEvent) getLastEvent();
                                        } else {
                                            $rootScope.getHistoryForAllActions();
                                            if ($scope.tableParams) $scope.tableParams.reload();
                                        }
                                    },
                                );
                            })
                            .on('hide', function () {
                                if ($('.editDateTask').val() == '') {
                                    if ($rootScope.editableTask) $rootScope.editableTask.date = '';
                                    if ($scope.$parent.vm.editableTask) $scope.$parent.vm.editableTask.date = '';
                                }
                            });
                    },
                };
            },
        ])
        .directive('datepickerForReportOnRequest', [
            '$filter',
            '$rootScope',
            '$translate',
            'Statistic',
            function ($filter, $rootScope, $translate, Statistic) {
                return {
                    restrict: 'EA',
                    link: function ($scope, element, attrs) {
                        let today = new Date();
                        let yesterday = new Date(today);
                        yesterday.setDate(today.getDate() - 1);
                        element
                            .datetimepicker({
                                format: 'dd/mm/yyyy',
                                startView: 2,
                                minView: 2,
                                maxView: 4,
                                autoclose: true,
                                weekStart: $rootScope.currentLang == 'ru' || $rootScope.currentLang == 'ua' ? 1 : 7,
                                language: $translate.use(),
                                initialDate: yesterday,
                                startDate: new Date($rootScope.me.org.dc),
                                endDate: yesterday,
                            })
                            .on('changeDate', function (data) {
                                function datepickerDSTfix(dateFromPicker) {
                                    let currentTimeZone = new Date().getTimezoneOffset();
                                    let pickerTimeZone = dateFromPicker.getTimezoneOffset();
                                    if (pickerTimeZone == currentTimeZone) {
                                        let corrected = new Date(
                                            dateFromPicker -
                                                dateFromPicker.getMinutes() * 60000 -
                                                dateFromPicker.getSeconds() * 1000 +
                                                currentTimeZone * 60000,
                                        );
                                        return corrected - corrected.getHours() * 3600000;
                                    } else {
                                        let timeZoneShift = pickerTimeZone - currentTimeZone;
                                        let corrected = new Date(
                                            dateFromPicker -
                                                dateFromPicker.getMinutes() * 60000 -
                                                dateFromPicker.getSeconds() * 1000 +
                                                currentTimeZone * 60000 +
                                                timeZoneShift * 60000,
                                        );
                                        return corrected - corrected.getHours() * 3600000;
                                    }
                                }

                                if (data.date != undefined) {
                                    Statistic.setParam('requestReportDate', datepickerDSTfix(data.date));
                                }
                            })
                            .on('hide', function () {
                                element.blur();
                            });
                    },
                };
            },
        ])
        .directive('datepickerOfCustomEditTime', [
            '$filter',
            '$rootScope',
            '$translate',
            function ($filter, $rootScope, $translate) {
                return {
                    restrict: 'EA',
                    scope: {
                        fieldValues: '=fieldValues',
                        objType: '=objType',
                        fieldId: '=fieldId',
                        fieldValueId: '=?fieldValueId',
                    },
                    link: function (scope, element, attrs) {
                        element
                            .datetimepicker({
                                format:
                                    $rootScope.currentLang == 'ru' || $rootScope.currentLang == 'ua'
                                        ? 'dd/mm/yyyy hh:00'
                                        : 'mm/dd/yyyy hh:00',
                                startView: 2,
                                minView: 1,
                                autoclose: true,
                                language: $translate.use(),
                                weekStart: $rootScope.currentLang == 'ru' || $rootScope.currentLang == 'ua' ? 1 : 7,
                                initialDate: new Date(),
                                startDate: new Date(-1262304000000),
                            })
                            .on('changeDate', function (val) {
                                var flag = false;

                                function datepickerDSTfix(dateFromPicker) {
                                    let currentTimeZone = new Date().getTimezoneOffset();
                                    let pickerTimeZone = dateFromPicker.getTimezoneOffset();
                                    if (pickerTimeZone === currentTimeZone) {
                                        return (
                                            dateFromPicker -
                                            dateFromPicker.getMinutes() * 60000 -
                                            dateFromPicker.getSeconds() * 1000 +
                                            currentTimeZone * 60000
                                        );
                                    } else {
                                        let timeZoneShift = pickerTimeZone - currentTimeZone;
                                        return (
                                            dateFromPicker -
                                            dateFromPicker.getMinutes() * 60000 -
                                            dateFromPicker.getSeconds() * 1000 +
                                            currentTimeZone * 60000 +
                                            timeZoneShift * 60000
                                        );
                                    }
                                }

                                if (val.date) {
                                    let editCustomValueDate = datepickerDSTfix(val.date);
                                    if (scope.fieldValues && scope.fieldValues.length > 0) {
                                        angular.forEach(scope.fieldValues, function (val) {
                                            if (val.field.fieldId === scope.fieldId) {
                                                val.dateTimeValue = editCustomValueDate;
                                                flag = true;
                                            }
                                        });
                                        if (!flag) {
                                            scope.fieldValues.push({
                                                objType: scope.objType,
                                                dateTimeValue: editCustomValueDate,
                                                fieldValueId: scope.fieldValueId,
                                                field: {
                                                    fieldId: scope.fieldId,
                                                },
                                            });
                                        }
                                    } else {
                                        scope.fieldValues.push({
                                            objType: scope.objType,
                                            dateTimeValue: editCustomValueDate,
                                            fieldValueId: scope.fieldValueId,
                                            field: {
                                                fieldId: scope.fieldId,
                                            },
                                        });
                                    }
                                }
                            })
                            .on('hide', function (val) {
                                if ($(element).name === scope.fieldId) {
                                    angular.forEach(scope.fieldValues, function (nval) {
                                        if ($(element).value !== '') {
                                            if (scope.fieldId === nval.field.fieldId) {
                                                nval.dateTimeValue = '';
                                            }
                                        }
                                    });
                                }
                                $(element).blur();
                            });
                    },
                };
            },
        ])
        .directive('datepickerOfCustomEdit', [
            '$filter',
            '$rootScope',
            '$translate',
            function ($filter, $rootScope, $translate) {
                return {
                    restrict: 'EA',
                    scope: {
                        fieldValues: '=fieldValues',
                        objType: '=objType',
                        fieldId: '=fieldId',
                        fieldValueId: '=?fieldValueId',
                    },
                    link: function (scope, element, attrs) {
                        $(element)
                            .datetimepicker({
                                format:
                                    $rootScope.currentLang == 'ru' || $rootScope.currentLang == 'ua'
                                        ? 'dd/mm/yyyy'
                                        : 'mm/dd/yyyy',
                                startView: 4,
                                minView: 2,
                                autoclose: true,
                                language: $translate.use(),
                                weekStart: $rootScope.currentLang == 'ru' || $rootScope.currentLang == 'ua' ? 1 : 7,
                                initialDate: new Date(),
                                startDate: new Date(-1262304000000),
                            })
                            .on('changeDate', function (val) {
                                var flag = false;
                                if (val.date) {
                                    let editCustomValueDate = val.date.getTime();
                                    if (scope.fieldValues && scope.fieldValues.length > 0) {
                                        angular.forEach(scope.fieldValues, function (val) {
                                            if (val.field.fieldId === scope.fieldId) {
                                                val.dateTimeValue = editCustomValueDate;
                                                flag = true;
                                            }
                                        });

                                        if (!flag) {
                                            scope.fieldValues.push({
                                                objType: scope.objType,
                                                dateTimeValue: editCustomValueDate,
                                                fieldValueId: scope.fieldValueId,
                                                field: {
                                                    fieldId: scope.fieldId,
                                                },
                                            });
                                        }
                                    } else {
                                        // $scope[$scope.objType].fieldValues = [];

                                        scope.fieldValues.push({
                                            objType: scope.objType,
                                            dateTimeValue: editCustomValueDate,
                                            fieldValueId: scope.fieldValueId,
                                            field: {
                                                fieldId: scope.fieldId,
                                            },
                                        });
                                    }
                                }
                            })
                            .on('hide', function () {
                                if ($(element).name === scope.fieldId) {
                                    angular.forEach(scope.fieldValues, function (nval) {
                                        if ($(element).value !== '') {
                                            if (scope.editCustomId === nval.field.fieldId) {
                                                nval.dateTimeValue = '';
                                            }
                                        }
                                    });
                                }
                                $(element).blur();
                            });
                    },
                };
            },
        ])
        .directive('historyBack', [
            '$window',
            function ($window) {
                return {
                    restrict: 'A',
                    link: function (scope, elem, attrs) {
                        if (history && history.length > 1) {
                            elem.on('click', function () {
                                $window.history.back();
                            });
                        } else {
                            $(elem).hide();
                        }
                    },
                };
            },
        ])
        .directive('customScrollbarPagination', function () {
            return function (scope, element, attrs) {
                $(element).mCustomScrollbar({
                    theme: 'dark-3',
                    scrollInertia: 1000,
                });
            };
        })
        .directive('navPagination', [
            function () {
                return {
                    restrict: 'AE',
                    templateUrl: '../partials/pagination.html?1',
                    link: function (scope, element, attributes) {
                        let pagePickerButtons = element.find('.left-block .pager-round-button');
                        let paginationParams = scope.paginationParams
                            ? 'paginationParams'
                            : scope.vm && scope.vm.paginationParams
                            ? 'vm.paginationParams'
                            : 'paginationParams';
                        scope.$watch(paginationParams, function (newValue, oldValue) {
                            if (newValue) scope.totalPagesCount = Math.ceil(newValue.totalCount / scope.params.count());
                            if (pagePickerButtons) {
                                if (scope.totalPagesCount > 9999) {
                                    pagePickerButtons.css({
                                        'min-width': '44px',
                                    });
                                    pagePickerButtons.css({
                                        'min-height': '44px',
                                    });
                                    pagePickerButtons.css({
                                        'padding-top': '12px',
                                    });
                                } else if (scope.totalPagesCount > 999) {
                                    pagePickerButtons.css({
                                        'min-width': '36px',
                                    });
                                    pagePickerButtons.css({
                                        'min-height': '36px',
                                    });
                                    pagePickerButtons.css({
                                        'padding-top': '8px',
                                    });
                                } else {
                                    pagePickerButtons.css({
                                        'min-width': '28px',
                                    });
                                    pagePickerButtons.css({
                                        'min-height': '28px',
                                    });
                                    pagePickerButtons.css({
                                        'padding-top': '5px',
                                    });
                                }
                            }
                        });
                    },
                };
            },
        ])
        .directive('paginationSelect', [
            function () {
                return {
                    restrict: 'AE',
                    link: function (scope, element, attributes) {
                        let startPos = 1;
                        let lastPos = 1;
                        let expanded = false;
                        let heightDropList = 100;
                        let widthDropList = 37;
                        let drListElement = element.find('.pagination-droplist');
                        let elementWrapper = element.find('.pagination-droplist-2');
                        let totalPages;

                        scope.$watch('paginationParams', function (newValue, oldValue) {
                            hideDropdown();
                            if (newValue) totalPages = Math.ceil(newValue.totalCount / scope.params.count());
                            if (totalPages > 5) {
                                startPos = firstPageNumber(totalPages, scope.paginationParams.currentPage + 1);
                                lastPos = lastPageNumber(totalPages, scope.paginationParams.currentPage + 1);
                                formingElement(startPos, lastPos);
                                bindListeners(heightDropList, widthDropList);
                            }
                        });

                        function lastPageNumber(totalPages, currentPage) {
                            if (totalPages == 6) {
                                switch (true) {
                                    case currentPage == 3:
                                        return 5;
                                    case currentPage == 4:
                                        return 3;
                                    default:
                                        return 4;
                                }
                            } else {
                                switch (true) {
                                    case currentPage < 3 || currentPage > totalPages - 2:
                                        return totalPages - 2;
                                    default:
                                        return currentPage - 1;
                                }
                            }
                        }

                        function firstPageNumber(totalPages, currentPage) {
                            if (totalPages == 6) {
                                switch (currentPage) {
                                    case 3:
                                        return 4;
                                    case 4:
                                        return 2;
                                    default:
                                        return 3;
                                }
                            } else {
                                switch (true) {
                                    case currentPage < 3:
                                        return 3;
                                    case currentPage == 3:
                                        return 4;
                                    case currentPage > 3 && currentPage < totalPages - 1:
                                        return 2;
                                    case currentPage >= totalPages - 1:
                                        return 3;
                                }
                            }
                        }

                        function hideDropdown() {
                            if (elementWrapper) {
                                elementWrapper.css({
                                    height: '0',
                                    border: 'none',
                                });
                                expanded = false;
                            }
                        }

                        function bindListeners(height, width) {
                            element.unbind().on('mousedown', (event) => {
                                if (expanded && hideIfNotScrollBar(event)) {
                                    hideDropdown();
                                } else {
                                    expanded = true;

                                    elementWrapper.css({
                                        height: height,
                                        width: width,
                                        border: '1px solid #aaa',
                                    });
                                }
                            });
                            element.find('li').on('mousedown', (event) => {
                                if (event.target.value) {
                                    scope.params.page(event.target.value);
                                    scope.$apply();
                                }
                            });
                            $('body').on('mousedown', (event) => {
                                if (hideIfNotScrollBar(event)) {
                                    if (!$(event.target).is(element)) {
                                        hideDropdown();
                                    }
                                }
                            });
                        }

                        function hideIfNotScrollBar(event) {
                            let classListOfTarget = [];
                            for (let i = event.target.classList.length - 1; i >= 0; i--) {
                                classListOfTarget.push(event.target.classList[i]);
                            }
                            if (
                                classListOfTarget &&
                                (classListOfTarget.indexOf('_mCS_2') != -1 ||
                                    classListOfTarget.indexOf('mCSB_dragger_bar') != -1 ||
                                    classListOfTarget.indexOf('mCSB_dragger') != -1 ||
                                    classListOfTarget.indexOf('mCSB_draggerRail') != -1)
                            ) {
                                return false;
                            } else {
                                return true;
                            }
                        }

                        function formingElement(startPage, lastPage) {
                            let pagesList = '';
                            let elementsCount = lastPage - startPage + 1;
                            if (elementsCount < 200) {
                                for (let i = startPage; i <= lastPage; i++) {
                                    pagesList += '<li value =" ' + i + '">' + i + '</li>';
                                }
                            } else {
                                for (let i = startPage; i <= lastPage && i - startPage <= 100; i++) {
                                    pagesList += '<li value =" ' + i + '">' + i + '</li>';
                                }
                                pagesList += '<li class="ellipsis" value ="...">...</li>';
                                for (let b = lastPage - 100; b <= lastPage; b++) {
                                    pagesList += '<li value =" ' + b + '">' + b + '</li>';
                                }
                            }

                            drListElement.html(pagesList);
                            if (elementsCount < 5) {
                                heightDropList = 20 * elementsCount;
                            } else {
                                heightDropList = 100;
                            }
                            if (lastPage > 999) {
                                widthDropList = 57;
                            } else if (lastPage > 99) {
                                widthDropList = 49;
                            } else {
                                widthDropList = 37;
                            }
                            return drListElement;
                        }
                    },
                };
            },
        ])
        .directive('paginationSecondSelect', [
            function () {
                return {
                    restrict: 'AE',
                    link: function (scope, element, attributes) {
                        let startPos = 1;
                        let lastPos = 1;
                        let expanded = false;
                        let heightDropList = 100;
                        let widthDropList = 37;
                        let drListElement = element.find('.pagination-droplist');
                        let elementWrapper = element.find('.pagination-droplist-2');
                        let totalPages;

                        scope.$watch('paginationParams', function (newValue, oldValue) {
                            hideDropdown();
                            if (newValue) totalPages = Math.ceil(newValue.totalCount / scope.params.count());
                            if (totalPages > 5) {
                                startPos = scope.paginationParams.currentPage + 2;
                                lastPos = totalPages - 1;
                                formingElement(startPos, lastPos);
                                bindListeners(heightDropList, widthDropList);
                            }
                        });

                        function hideDropdown() {
                            if (elementWrapper) {
                                elementWrapper.css({
                                    height: '0',
                                    border: 'none',
                                });
                                expanded = false;
                            }
                        }

                        function bindListeners(height, width) {
                            element.unbind().on('mousedown', (event) => {
                                if (expanded && hideIfNotScrollBar(event)) {
                                    hideDropdown();
                                } else {
                                    expanded = true;
                                    elementWrapper.css({
                                        height: height,
                                        width: width,
                                        border: '1px solid #aaa',
                                    });
                                }
                            });
                            element.find('li').on('mousedown', (event) => {
                                if (event.target.value) {
                                    scope.params.page(event.target.value);
                                    scope.$apply();
                                }
                            });
                            $('body').on('mousedown', (event) => {
                                if (hideIfNotScrollBar(event)) {
                                    if (!$(event.target).is(element)) {
                                        hideDropdown();
                                    }
                                }
                            });
                        }

                        function hideIfNotScrollBar(event) {
                            let classListOfTarget = [];
                            for (let i = event.target.classList.length - 1; i >= 0; i--) {
                                classListOfTarget.push(event.target.classList[i]);
                            }
                            if (
                                classListOfTarget &&
                                (classListOfTarget.indexOf('_mCS_2') != -1 ||
                                    classListOfTarget.indexOf('mCSB_dragger_bar') != -1 ||
                                    classListOfTarget.indexOf('mCSB_dragger') != -1 ||
                                    classListOfTarget.indexOf('mCSB_draggerRail') != -1)
                            ) {
                                return false;
                            } else {
                                return true;
                            }
                        }

                        function formingElement(startPage, lastPage) {
                            let pagesList = '';
                            let elementsCount = lastPage - startPage + 1;
                            if (elementsCount < 200) {
                                for (let i = startPage; i <= lastPage; i++) {
                                    pagesList += '<li value =" ' + i + '">' + i + '</li>';
                                }
                            } else {
                                for (let i = startPage; i <= lastPage && i - startPage <= 100; i++) {
                                    pagesList += '<li value =" ' + i + '">' + i + '</li>';
                                }
                                pagesList += '<li class="ellipsis" value ="...">...</li>';
                                for (let b = lastPage - 100; b <= lastPage; b++) {
                                    pagesList += '<li value =" ' + b + '">' + b + '</li>';
                                }
                            }

                            drListElement.html(pagesList);
                            if (elementsCount < 5) {
                                heightDropList = 20 * elementsCount;
                            } else {
                                heightDropList = 100;
                            }
                            if (lastPage > 999) {
                                widthDropList = 57;
                            } else if (lastPage > 99) {
                                widthDropList = 49;
                            } else {
                                widthDropList = 37;
                            }
                            return drListElement;
                        }
                    },
                };
            },
        ])
        .directive('breadcrumbs', [
            '$compile',
            function ($compile) {
                return {
                    restrict: 'AE',
                    scope: {
                        crumbs: '=',
                    },
                    //crumbs is an array of objects with fields:  href - optional, transl||value.
                    link: function (scope, element, attributes) {
                        let fullCrumbsElement = '<ol class="breadcrumb hidden-xs hidden-sm">';
                        if (scope.crumbs && scope.crumbs.length > 0) {
                            scope.crumbs.forEach((crumb) => {
                                if (crumb.href) {
                                    if (crumb.transl) {
                                        fullCrumbsElement +=
                                            '<li><a href="' +
                                            crumb.href +
                                            '" translate="' +
                                            crumb.transl +
                                            '"></a></li>';
                                    } else {
                                        fullCrumbsElement +=
                                            '<li><a href="' + crumb.href + '">' + crumb.value + '</a></li>';
                                    }
                                } else {
                                    if (crumb.transl) {
                                        fullCrumbsElement += '<li translate="' + crumb.transl + '"></li>';
                                    } else {
                                        fullCrumbsElement += '<li>' + crumb.value + '</li>';
                                    }
                                }
                            });
                            fullCrumbsElement += '</ol>';
                            element.append($compile(fullCrumbsElement)(scope));
                        } else {
                            console.error('Error, "crumbs" directive needs args!');
                        }
                    },
                };
            },
        ])
        .directive('addDirectiveRemovedPerson', function ($compile, $timeout) {
            let restrict = 'EACM';
            return {
                restrict,
                link(scope, element, attrs) {
                    $timeout(() => {
                        let ngInclude = element.find('ng-include')[1],
                            elem;

                        if (ngInclude) {
                            ngInclude.setAttribute('removed-person', '');
                            elem = angular.element(ngInclude);
                            $compile(elem)(scope);
                        }
                    }, 500);
                },
            };
        })
        .directive('customSelectNewContacts', [
            '$window',
            '$filter',
            '$timeout',
            '$rootScope',
            function ($window, $filter, $timeout, $rootScope) {
                return {
                    restrict: 'E',
                    scope: {
                        options: '=options',
                        model: '=?model',
                        path: '=path',
                        placeholder: '=placeholder',
                        disabled: '=disabled',
                        method: '=method',
                        type: '=type',
                        removeMethod: '=removeMethod',
                        addMethod: '=addMethod',
                        trackIndex: '=trackIndex',
                        isNoNeedToTranslateOptions: '=?',
                        thereIsNotDefaultValueExist: '=?',
                        needToAccept: '=?',
                        hidePlaceholderFromOptions: '=?hidePlaceholderFromOptions',
                        isRestrictionForOption: '=?isRestrictionForOption',
                        objectIndex: '=?',
                        trackSelectedOptionById: '=?',
                        updateCurrencyValue: '=?',
                        trackSelectedOptionByField: '=?',
                        customStylesOfLabel: '=customStylesOfLabel',
                        trackSelectedOptionByValue: '=?',
                        customTranslate: '=?',
                        fieldId: '=?',
                        fieldValueId: '=?',
                        notSetModel: '=?',
                        valueToMethod: '=?',
                        customOptStyles: '=?',
                        customStatusStyles: '=?',
                        bigFirstLetter: '=?',
                        notShowCount: '=?',
                    },
                    link: function (scope, element, attrs) {
                        scope.getPropertyValue = function (obj = {}, path = '') {
                            if (obj && obj.value && obj.value === 'e00_no_experience')
                                return 'experience_assoc.e00_no_experience';
                            if (scope.customTranslate && $rootScope.allLanguages) {
                                const langObj = $rootScope.allLanguages[obj];
                                if (langObj) {
                                    return $rootScope.currentLang === 'ru'
                                        ? langObj.russian[0].toUpperCase() + langObj.russian.slice(1)
                                        : $rootScope.currentLang === 'en'
                                        ? langObj.english[0].toUpperCase() + langObj.english.slice(1)
                                        : langObj.ukrainian[0].toUpperCase() + langObj.ukrainian.slice(1);
                                }
                            }
                            if (scope.bigFirstLetter) {
                                if (typeof obj === 'string') {
                                    obj = obj[0].toUpperCase() + obj.slice(1).toLowerCase();
                                    obj = replaceCategory(obj);
                                }
                            }
                            if (!path) return obj ? (obj.value ? obj.value : obj) : obj;
                            if (!obj) return null;

                            let prevProp = null;

                            path.split('.').forEach((prop) => {
                                prevProp = prevProp ? prevProp[prop] : obj[prop];
                            });

                            return prevProp || prevProp === false ? prevProp : obj;
                        };

                        scope.setPropertyValue = function (obj = {}, value) {
                            const path = scope.path.split('.');
                            for (let i = 0; i < path.length - 1; i++) {
                                let prop = path[i];
                                if (prop in o) {
                                    obj = obj[prop];
                                } else {
                                    obj[prop] = {};
                                    obj = obj[prop];
                                }
                            }

                            obj[path[path.length - 1]] = value;

                            return obj;
                        };
                        for (let i in scope.options) {
                            if (
                                scope.trackSelectedOptionById &&
                                scope.options[i].id &&
                                angular.equals(scope.options[i].id, scope.model)
                            ) {
                                scope.selectedOpt = scope.options[i].value;
                            } else if (
                                scope.trackSelectedOptionByValue &&
                                scope.options[i].value &&
                                scope.model &&
                                angular.equals(scope.options[i].value, scope.model.value)
                            ) {
                                scope.selectedOpt = scope.options[i];
                            } else if (
                                scope.trackSelectedOptionByField &&
                                scope.options[i][scope.trackSelectedOptionByField] &&
                                scope.model &&
                                angular.equals(
                                    scope.options[i][scope.trackSelectedOptionByField],
                                    scope.model[scope.trackSelectedOptionByField],
                                )
                            ) {
                                scope.selectedOpt = scope.options[i][scope.trackSelectedOptionByField];
                            } else if (angular.equals(scope.options[i], scope.model)) {
                                scope.selectedOpt = scope.options[i];
                            }
                        }
                        // Select an option
                        scope.selectOpt = function (opt, index) {
                            if (scope.customStatusStyles) {
                                scope.isGreenStatus = opt === 'open' || opt === 'inwork' || opt === 'completed';
                            }
                            if (scope.updateCurrencyValue) {
                                $rootScope.$emit('updateCurrencyValue', opt);
                            }
                            if (scope.needToAccept) {
                                $rootScope.$emit('modelFromCSNwasReplaced', {
                                    oldVal: scope.model,
                                    newVal: opt,
                                });
                                $rootScope.$on('replaceCSNModelDecision', (evt, data) => {
                                    if (data) {
                                        changeModel(opt, index);
                                    } else {
                                        optionsDom.removeClass('active');
                                        backdrop.removeClass('active');
                                    }
                                });
                            } else {
                                changeModel(opt, index);
                            }
                        };

                        function replaceCategory(category) {
                            const categories = {
                                Backend: 'Back End',
                                C: 'C/C++',
                                Big_data: 'Big Data',
                                Data_scientist: 'Data Scientist',
                                Cyber_security: 'Cyber security',
                                Dba: 'DBA',
                                Dev_ops: 'DevOps',
                                Erp_crm: 'ERP/CRM',
                                Frontend: 'Front End',
                                Full_stack: 'Full Stack',
                                Hr: 'HR in IT',
                                Sales: 'Sales in IT',
                                Gamedev: 'GameDev',
                                Ios_mac: 'iOS/macOS',
                                _net: '.Net',
                                Node: 'Node.js',
                                Product_manager: 'Product Manager',
                                Project_manager: 'Project Manager',
                                Qa: 'QA',
                                Seo: 'SEO',
                                System_administrator: 'System Administrator',
                                Technical_writer: 'Technical Writer',
                                Php: 'PHP',
                            };
                            return categories[category] || category;
                        }

                        function changeModel(opt, index) {
                            if (scope.valueToMethod) {
                                scope.method(scope.valueToMethod, opt);
                                optionsDom.removeClass('active');
                                backdrop.removeClass('active');
                                return;
                            }
                            if (scope.notSetModel) {
                                scope.method(opt);
                                optionsDom.removeClass('active');
                                backdrop.removeClass('active');
                                return;
                            }
                            if (scope.hasOwnProperty('model')) scope.model = opt;
                            if (scope.method && scope.model && scope.fieldId) {
                                scope.method(scope.model.value, scope.fieldId, scope.fieldValueId);
                                optionsDom.removeClass('active');
                                backdrop.removeClass('active');
                                return;
                            }
                            if (scope.method && scope.trackIndex) {
                                scope.method(index);
                            } else if (
                                scope.method && typeof scope.objectIndex === 'number'
                                    ? scope.objectIndex.toString()
                                    : false
                            ) {
                                scope.method(opt, scope.objectIndex);
                            } else if (scope.method) {
                                scope.method(opt);
                            }
                            optionsDom.removeClass('active');
                            backdrop.removeClass('active');
                        }

                        scope.removeOpt = function (opt, index) {
                            if (scope.removeMethod && scope.trackIndex) {
                                scope.removeMethod(index);
                            } else if (scope.removeMethod) scope.removeMethod(opt);
                        };

                        scope.addOpt = function () {
                            scope.addMethod();
                        };

                        scope.setPlaceholder = function () {
                            if (scope.hasOwnProperty('model')) scope.model = null;
                            close();
                            if (scope.fieldId) scope.method('', scope.fieldId, scope.fieldValueId);
                        };

                        scope.$watch(
                            'model',
                            function (newVal) {
                                for (let i in scope.options) {
                                    if (
                                        scope.trackSelectedOptionById &&
                                        scope.options[i].id &&
                                        angular.equals(scope.options[i].id, newVal)
                                    ) {
                                        scope.selectedOpt = scope.options[i].value;
                                    } else if (
                                        scope.trackSelectedOptionByValue &&
                                        scope.options[i].value &&
                                        angular.equals(scope.options[i].value, newVal.value)
                                    ) {
                                        scope.selectedOpt = scope.options[i];
                                    } else if (
                                        scope.trackSelectedOptionByField &&
                                        scope.options[i][scope.trackSelectedOptionByField] &&
                                        scope.model &&
                                        angular.equals(scope.options[i][scope.trackSelectedOptionByField], scope.model)
                                    ) {
                                        scope.selectedOpt = scope.options[i][scope.trackSelectedOptionByField];
                                    } else if (angular.equals(scope.options[i], newVal)) {
                                        scope.selectedOpt = scope.options[i];
                                    }
                                }
                            },
                            true,
                        );

                        var labelDom = element.find('.select-label'),
                            optionsDom = element.find('.select-ops'),
                            backdrop = element.find('.select-backdrop');

                        labelDom.on('click', open);
                        backdrop.on('click', close);

                        if (scope.placeholder && !scope.model) scope.setPlaceholder();

                        function open() {
                            if (!scope.disabled) {
                                optionsDom.toggleClass('active');
                                backdrop.toggleClass('active');
                            }
                        }

                        $rootScope.removeError = function () {
                            $rootScope.wrongRole = false;
                        };

                        function close() {
                            optionsDom.removeClass('active');
                            backdrop.removeClass('active');
                            labelDom.removeClass('disabled');
                        }

                        $(element).bind('open', open);
                        $(element).bind('close', close);

                        scope.$watch('disabled', () => {
                            if (scope.disabled) labelDom.addClass('disabled');
                            else labelDom.removeClass('disabled');
                        });
                    },
                    template: `<div class='select-label custom-new' tabindex='0'>
                            <span ng-show='model' class='select-label-text' ng-class="{'select-label-text-custom': customStylesOfLabel }" ng-if='!isNoNeedToTranslateOptions  && !customOptStyles && !customStatusStyles'>{{getPropertyValue(model || selectedOpt, path) | translate: selectedOpt.translateValues}}</span> 
                            <span ng-show='model' class='select-label-text' ng-class="{'greenStatus':selectedOpt === 'open'|| model === 'open' || selectedOpt === 'inwork' || model === 'inwork' || selectedOpt === 'completed' || model === 'completed','yellowStatus':!(selectedOpt === 'open'|| model === 'open' || selectedOpt === 'inwork' || model === 'inwork' || selectedOpt === 'completed' || model === 'completed')}" ng-if='!isNoNeedToTranslateOptions  && !customOptStyles && customStatusStyles'>{{getPropertyValue(selectedOpt || model, path) | translate: selectedOpt.translateValues}}</span> 
                            <span ng-show='model' class='select-label-text' ng-class="{'select-label-text-custom': customStylesOfLabel,'lowPriorityStyles':selectedOpt ==='low' || model ==='low','mediumPriorityStyles':selectedOpt ==='medium' || model ==='medium','highPriorityStyles':selectedOpt ==='top' || model ==='high' }" ng-if='!isNoNeedToTranslateOptions && customOptStyles'>{{getPropertyValue(selectedOpt || model, path) | translate: selectedOpt.translateValues}}</span> 
                            <span ng-show='model' class='select-label-text' ng-if="isNoNeedToTranslateOptions && thereIsNotDefaultValueExist && getPropertyValue(selectedOpt || model, path) !== 'Default'">{{getPropertyValue(selectedOpt || model, path)}}</span>
                            <span ng-show='model' class='select-label-text'   ng-if="isNoNeedToTranslateOptions && thereIsNotDefaultValueExist && getPropertyValue(selectedOpt || model, path) === 'Default'">{{getPropertyValue(selectedOpt || model, path) | translate: selectedOpt.translateValues}}</span>
                            <span ng-show='!model' class='select-label-text placeholder' ng-class="{'select-label-text-custom': customStylesOfLabel }" translate='{{placeholder}}'></span>
                            <span class='select-caret'>
                                <span></span>
                            </span>
                        </div>
                        <div class='select-backdrop custom-new'></div>
                        <div class='select-ops custom-new'>
                            <div ng-repeat='o in options track by $index' class='list-ops'>
                                <div ng-click='selectOpt(o, $index);' style='display:flex;align-items:center;margin-left:10px'>
                                    <div title="{{'E-mail'|translate}}" style='display:flex' ng-if="o === 'E-mail'">
                                       <svg width='16' height='12' viewBox='0 0 16 12' fill='none' xmlns='http://www.w3.org/2000/svg'>
                                            <path d='M1.6 0H14.4C15.28 0 16 0.675 16 1.5V10.5C16 11.325 15.28 12 14.4 12H1.6C0.712 12 0 11.325 0 10.5V1.5C0 0.6675 0.712 0 1.6 0ZM8 5.25L14.4 1.5H1.6L8 5.25ZM1.6 10.5H14.4V3.2775L8 7.02L1.6 3.2775V10.5Z' fill='#838287'/>
                                         </svg>
                                    </div>
                                    <div title="{{'mphone'|translate}}" style='display:flex' ng-if="o === 'mphone'">
                                        <svg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'>
                                            <path d='M13.2222 9.72222C12.2889 9.72222 11.2778 9.56667 10.4222 9.25556C10.3444 9.25556 10.2667 9.25556 10.1889 9.25556C9.95556 9.25556 9.8 9.33333 9.64444 9.48889L7.93333 11.2C5.75556 10.0333 3.88889 8.24444 2.8 6.06667L4.51111 4.35556C4.74444 4.12222 4.82222 3.81111 4.66667 3.57778C4.43333 2.72222 4.27778 1.71111 4.27778 0.777778C4.27778 0.388889 3.88889 0 3.5 0H0.777778C0.388889 0 0 0.388889 0 0.777778C0 8.08889 5.91111 14 13.2222 14C13.6111 14 14 13.6111 14 13.2222V10.5C14 10.1111 13.6111 9.72222 13.2222 9.72222ZM1.55556 1.55556H2.72222C2.8 2.25556 2.95556 2.95556 3.11111 3.57778L2.17778 4.51111C1.86667 3.57778 1.63333 2.56667 1.55556 1.55556ZM12.4444 12.4444C11.4333 12.3667 10.4222 12.1333 9.48889 11.8222L10.4222 10.8889C11.0444 11.0444 11.7444 11.2 12.4444 11.2V12.4444Z' fill='#838287'/>
                                         </svg>
                                    </div>
                                    <div title="{{'linkedin'|translate}}" style='display:flex' ng-if="o === 'linkedin'">
                                         <svg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'>
                                                 <path d='M4.37181 4.42857L4.37174 4.4285L4.3687 4.4316C4.16481 4.63894 3.89186 4.76583 3.60634 4.74768L3.60635 4.74748H3.6C3.32482 4.74748 3.03116 4.63484 2.83131 4.4316L2.83137 4.43153L2.82819 4.42857C2.61154 4.22659 2.50001 3.92928 2.50001 3.62714C2.50001 3.32765 2.60991 3.04783 2.83131 2.82268C3.03116 2.61943 3.30483 2.50679 3.61999 2.50679C3.89336 2.50679 4.16761 2.61819 4.3687 2.82268C4.5901 3.04783 4.7 3.32765 4.7 3.62714C4.7 3.92928 4.58847 4.22659 4.37181 4.42857ZM3.61999 1.9C3.1544 1.9 2.72833 2.07264 2.4087 2.39769L3.6 5.25425L3.6 5.35425H3.6H3.60001H3.60001H3.60001H3.60002H3.60002H3.60002H3.60003H3.60003H3.60004H3.60005H3.60005H3.60006H3.60007H3.60007H3.60008H3.60009H3.6001H3.60011H3.60012H3.60013H3.60014H3.60015H3.60017H3.60018H3.60019H3.6002H3.60022H3.60023H3.60024H3.60026H3.60028H3.60029H3.60031H3.60032H3.60034H3.60036H3.60038H3.60039H3.60041H3.60043H3.60045H3.60047H3.60049H3.60051H3.60053H3.60055H3.60058H3.6006H3.60062H3.60064H3.60067H3.60069H3.60072H3.60074H3.60077H3.60079H3.60082H3.60085H3.60087H3.6009H3.60093H3.60096H3.60098H3.60101H3.60104H3.60107H3.6011H3.60113H3.60116H3.60119H3.60123H3.60126H3.60129H3.60132H3.60136H3.60139H3.60142H3.60146H3.60149H3.60153H3.60156H3.6016H3.60163H3.60167H3.60171H3.60175H3.60178H3.60182H3.60186H3.6019H3.60194H3.60198H3.60202H3.60206H3.6021H3.60214H3.60218H3.60222H3.60227H3.60231H3.60235H3.60239H3.60244H3.60248H3.60249H3.60253H3.60257H3.60262H3.60266H3.60271H3.60275H3.6028H3.60285H3.60289H3.60294H3.60299H3.60304H3.60309H3.60314H3.60319H3.60324H3.60329H3.60334H3.60339H3.60344H3.60349H3.60354H3.60359H3.60365H3.6037H3.60375H3.6038H3.60386H3.60391H3.60397H3.60402H3.60408H3.60413H3.60419H3.60424H3.6043H3.60436H3.60442H3.60447H3.60453H3.60459H3.60465H3.60471H3.60476H3.60482H3.60488H3.60494H3.605H3.60506H3.60513H3.60519H3.60525H3.60531H3.60537H3.60544H3.6055H3.60556H3.60563H3.60569H3.60575H3.60582H3.60588H3.60595H3.60601H3.60608H3.60614H3.60621H3.60628H3.60634H3.60641H3.60648H3.60655H3.60661H3.60668H3.60675H3.60682H3.60689H3.60696H3.60703H3.6071H3.60717H3.60724H3.60731H3.60738H3.60745H3.60753H3.6076H3.60767H3.60774H3.60782H3.60789H3.60796H3.60804H3.60811H3.60818H3.60826H3.60833H3.60841H3.60849H3.60856H3.60864H3.60871H3.60879H3.60887H3.60894H3.60902H3.6091H3.60918H3.60925H3.60933H3.60941H3.60949H3.60957H3.60965H3.60973H3.60981H3.60989H3.60997H3.61005H3.61013H3.61021H3.61029H3.61038H3.61046H3.61054H3.61062H3.61071H3.61079H3.61087H3.61096H3.61104H3.61112H3.61121H3.61129H3.61138H3.61146H3.61155H3.61163H3.61172H3.6118H3.61189H3.61198H3.61206H3.61215H3.61224H3.61232H3.61241H3.6125H3.61259H3.61268H3.61276H3.61285H3.61294H3.61303H3.61312H3.61321H3.6133H3.61339H3.61348H3.61357H3.61366H3.61375H3.61384H3.61393H3.61403H3.61412H3.61421H3.6143H3.61439H3.61449H3.61458H3.61467H3.61477H3.61486H3.61495H3.61505H3.61514H3.61523H3.61533H3.61542H3.61552H3.61561H3.61571H3.6158H3.6159H3.616H3.61609H3.61619H3.61628H3.61638H3.61648H3.61658H3.61667H3.61677H3.61687H3.61697H3.61706H3.61716H3.61726H3.61736H3.61746H3.61756H3.61765H3.61775H3.61785H3.61795H3.61805H3.61815H3.61825H3.61835H3.61845H3.61855H3.61866H3.61876H3.61886H3.61896H3.61906H3.61916H3.61926H3.61937H3.61947H3.61957H3.61967H3.61978H3.61988H3.61998H3.62008H3.62019H3.62029H3.62039H3.6205H3.6206H3.62071H3.62081H3.62091H3.62102H3.62112H3.62123H3.62133H3.62144H3.62154H3.62165H3.62175H3.62186H3.62197H3.62207H3.62218H3.62228H3.62239H3.6225H3.6226H3.62271H3.62282H3.62293H3.62303H3.62314H3.62325H3.62335H3.62346H3.62357H3.62368H3.62379H3.62389H3.624H3.62411H3.62422H3.62433H3.62444H3.62455H3.62466H3.62477H3.62487H3.62498H3.62509H3.6252H3.62531H3.62542H3.62553H3.62564H3.62575H3.62586H3.62598H3.62609H3.6262H3.62631H3.62642H3.62653H3.62664H3.62675H3.62686H3.62697H3.62709H3.6272H3.62731H3.62742H3.62753H3.62765H3.62776H3.62787H3.62798H3.62809H3.62821H3.62832H3.62843H3.62855H3.62866H3.62877H3.62888H3.629H3.62911H3.62922H3.62934H3.62945H3.62956H3.62968H3.62979H3.62991H3.63002H3.63013H3.63025H3.63036H3.63048H3.63059H3.6307H3.63082H3.63093H3.63105H3.63116H3.63128H3.63139H3.63151H3.63162H3.63174H3.63185H3.63197H3.63208H3.6322H3.63231H3.63243H3.63254H3.63266H3.63277H3.63289H3.63301H3.63312H3.63324H3.63335H3.63347H3.63358H3.6337H3.63382H3.63393H3.63405H3.63416H3.63428H3.6344H3.63451H3.63463H3.63475H3.63486H3.63498H3.63509H3.63521H3.63533H3.63544H3.63556H3.63568H3.63579H3.63591H3.63603H3.63614H3.63626H3.63638H3.63649H3.63661H3.63673H3.63685H3.63696H3.63708H3.6372H3.63731H3.63743H3.63755H3.63766H3.63778H3.6379H3.63802H3.63813H3.63825H3.63837H3.63848H3.6386H3.63872H3.63884H3.63895H3.63907H3.63919H3.63931H3.63942H3.63954H3.63966H3.63977H3.63989H3.64001C4.06837 5.35425 4.47361 5.17964 4.79131 4.85656C5.10812 4.53437 5.29937 4.10368 5.3 3.62945C5.32069 3.17206 5.12786 2.7405 4.81243 2.41915C4.49279 2.07309 4.06617 1.9 3.61999 1.9Z' fill='#838287' stroke='#838287' stroke-width='0.2'/>
                                                 <path d='M4.39976 5.56107H2.77977C2.40079 5.56107 2.09977 5.87408 2.09977 6.27124V13.3899C2.09977 13.769 2.42297 14.1001 2.79976 14.1001H4.39976C4.7744 14.1001 5.09976 13.7712 5.09976 13.4103V6.27124C5.09976 5.89216 4.77656 5.56107 4.39976 5.56107ZM4.49975 13.4103C4.49975 13.427 4.4915 13.447 4.47113 13.4649C4.45043 13.4832 4.42353 13.4933 4.39976 13.4933H2.79976C2.78289 13.4933 2.75721 13.4837 2.73357 13.4597C2.71004 13.4357 2.69977 13.4089 2.69977 13.3899V6.27124C2.69977 6.24563 2.71033 6.21746 2.72838 6.19628C2.74624 6.17533 2.76517 6.16786 2.77977 6.16786H4.39976C4.41664 6.16786 4.44232 6.17749 4.46596 6.20153C4.48949 6.22547 4.49975 6.25234 4.49975 6.27124V13.4103Z' fill='#838287' stroke='#838287' stroke-width='0.2'/>
                                                <path d='M10.78 5.35758C10.0608 5.35758 9.37419 5.64081 8.87998 6.10511V6.06775C8.87998 5.93413 8.81525 5.80845 8.72628 5.71798C8.63742 5.62761 8.5131 5.56096 8.37997 5.56096H6.37999C6.2621 5.56096 6.1392 5.61304 6.04618 5.69543C5.95207 5.77879 5.87998 5.90105 5.87998 6.0474V13.6339C5.87998 13.7813 5.95316 13.8995 6.04907 13.9782C6.14357 14.0557 6.26575 14.1 6.37999 14.1H8.57999C8.69423 14.1 8.8164 14.0557 8.9109 13.9782C9.00681 13.8995 9.07999 13.7813 9.07999 13.6339V9.24063C9.07999 8.616 9.5235 8.14062 10.08 8.14062C10.3743 8.14062 10.6482 8.25265 10.8487 8.4565L10.8486 8.4566L10.8525 8.46015C11.022 8.6153 11.1 8.86544 11.1 9.2203V13.5932C11.1 13.7268 11.1647 13.8525 11.2537 13.943C11.3425 14.0334 11.4669 14.1 11.6 14.1H13.6C13.7331 14.1 13.8574 14.0334 13.9463 13.943C14.0352 13.8525 14.1 13.7269 14.1 13.5932V8.34573C14.1 6.66639 12.8382 5.35758 11.2 5.35758H10.78ZM10.1 7.53384L10.0976 7.5339C9.1998 7.55563 8.49998 8.29502 8.49998 9.24066V13.4932H6.49998V6.16775H8.29996V7.36945V7.61377L8.47127 7.43956L9.01126 6.89041L9.01126 6.89041L9.03125 6.87008L9.03704 6.86419L9.04178 6.85744C9.42258 6.3153 10.0917 5.96437 10.8 5.96437H11.22C12.5005 5.96437 13.4999 7.01356 13.4999 8.34575V13.4932H11.7195L11.7 9.22032H11.6L11.7 9.21987L11.7 9.22007C11.6999 8.7157 11.5755 8.32065 11.2913 8.03157C10.9721 7.70698 10.5462 7.53387 10.1 7.53384Z' fill='#838287' stroke='#838287' stroke-width='0.2'/>
                                         </svg>
                                    </div>
                                    <div title="{{'facebook'|translate}}" style='display:flex' ng-if="o === 'facebook'">
                                            <svg xmlns='http://www.w3.org/2000/svg' viewBox='-110 1 511 511.99996' width='15px' height='15px'>
                                                <g><path d='m180 512h-81.992188c-13.695312 0-24.835937-11.140625-24.835937-24.835938v-184.9375h-47.835937c-13.695313 0-24.835938-11.144531-24.835938-24.835937v-79.246094c0-13.695312 11.140625-24.835937 24.835938-24.835937h47.835937v-39.683594c0-39.347656 12.355469-72.824219 35.726563-96.804688 23.476562-24.089843 56.285156-36.820312 94.878906-36.820312l62.53125.101562c13.671875.023438 24.792968 11.164063 24.792968 24.835938v73.578125c0 13.695313-11.136718 24.835937-24.828124 24.835937l-42.101563.015626c-12.839844 0-16.109375 2.574218-16.808594 3.363281-1.152343 1.308593-2.523437 5.007812-2.523437 15.222656v31.351563h58.269531c4.386719 0 8.636719 1.082031 12.289063 3.121093 7.878906 4.402344 12.777343 12.726563 12.777343 21.722657l-.03125 79.246093c0 13.6875-11.140625 24.828125-24.835937 24.828125h-58.46875v184.941406c0 13.695313-11.144532 24.835938-24.839844 24.835938zm-76.8125-30.015625h71.632812v-193.195313c0-9.144531 7.441407-16.582031 16.582032-16.582031h66.726562l.027344-68.882812h-66.757812c-9.140626 0-16.578126-7.4375-16.578126-16.582031v-44.789063c0-11.726563 1.191407-25.0625 10.042969-35.085937 10.695313-12.117188 27.550781-13.515626 39.300781-13.515626l36.921876-.015624v-63.226563l-57.332032-.09375c-62.023437 0-100.566406 39.703125-100.566406 103.609375v53.117188c0 9.140624-7.4375 16.582031-16.578125 16.582031h-56.09375v68.882812h56.09375c9.140625 0 16.578125 7.4375 16.578125 16.582031zm163.0625-451.867187h.003906zm0 0' data-original='#000000' class='active-path' data-old_color='#000000' fill='#838287'/></g>
                                            </svg>
                                    </div>
                                    <div title="{{'telegram'|translate}}" style='display:flex' ng-if="o === 'telegram'">
                                     <svg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'>
                                            <path d='M13.4033 0.635459C13.2908 0.501403 13.1133 0.465457 12.9663 0.533836L0.728101 6.23661C0.592516 6.29985 0.503396 6.44121 0.500095 6.59905C0.496794 6.75662 0.579821 6.90202 0.712867 6.97175L3.63505 8.5015L4.36325 13.1518C4.38686 13.3283 4.51711 13.4702 4.68672 13.4959C4.80859 13.5145 4.92971 13.4689 5.0135 13.377L7.32047 10.9213L10.6126 13.4245C10.7182 13.5045 10.8543 13.5221 10.9741 13.4716C11.0942 13.421 11.1821 13.3089 11.2075 13.174L13.4926 0.984652C13.5152 0.86384 13.486 0.734379 13.4033 0.635459ZM3.93669 7.75582L1.78485 6.62932L10.2861 2.66792L3.93669 7.75582ZM5.12851 8.96097L4.77432 10.8461L4.39219 8.40555L9.06327 4.66254L5.23236 8.75421C5.17853 8.81151 5.14273 8.8861 5.12851 8.96097ZM5.36033 11.861L5.75997 9.73476L6.69206 10.4434L5.36033 11.861ZM10.5758 12.3999L6.08853 8.98826L12.4988 2.14196L10.5758 12.3999Z' fill='#838287'/>
                                    </svg>
                                    </div>
                                    <div title="{{'whatsApp'|translate}}" style='display:flex' ng-if="o === 'whatsApp'">
                                         <svg width='16' height='16' viewBox='0 0 15 16' fill='none' xmlns='http://www.w3.org/2000/svg'>
                                             <path d='M12.8338 2.20708C11.4441 0.776567 9.52316 0 7.56131 0C3.39237 0 0.040872 3.39237 0.0817439 7.52044C0.0817439 8.82834 0.449591 10.0954 1.06267 11.2398L0 15.1226L3.96458 14.1008C5.06812 14.7139 6.29428 15 7.52044 15C11.6485 15 15 11.6076 15 7.47956C15 5.47684 14.2234 3.59673 12.8338 2.20708ZM7.56131 13.733C6.45777 13.733 5.35422 13.4469 4.41417 12.8747L4.16894 12.752L1.79837 13.3651L2.41144 11.0354L2.24796 10.7902C0.449591 7.88828 1.3079 4.04632 4.25068 2.24796C7.19346 0.449591 10.9945 1.3079 12.7929 4.25068C14.5913 7.19346 13.733 10.9946 10.7902 12.7929C9.85014 13.406 8.70572 13.733 7.56131 13.733ZM11.158 9.19619L10.7084 8.99183C10.7084 8.99183 10.0545 8.70572 9.64578 8.50136C9.6049 8.50136 9.56403 8.46049 9.52316 8.46049C9.40054 8.46049 9.3188 8.50136 9.23706 8.54223C9.23706 8.54223 9.19619 8.58311 8.62398 9.23706C8.58311 9.3188 8.50136 9.35967 8.41962 9.35967H8.37875C8.33787 9.35967 8.25613 9.3188 8.21526 9.27793L8.0109 9.19619C7.56131 8.99183 7.15259 8.74659 6.82561 8.41962C6.74387 8.33788 6.62125 8.25613 6.53951 8.17439C6.25341 7.88828 5.9673 7.56131 5.76294 7.19346L5.72207 7.11172C5.6812 7.07084 5.6812 7.02997 5.64033 6.94823C5.64033 6.86649 5.64033 6.78474 5.6812 6.74387C5.6812 6.74387 5.84469 6.53951 5.9673 6.41689C6.04905 6.33515 6.08992 6.21253 6.17166 6.13079C6.25341 6.00817 6.29428 5.84469 6.25341 5.72207C6.21253 5.51771 5.72207 4.41417 5.59945 4.16894C5.51771 4.04632 5.43597 4.00545 5.31335 3.96458H5.19074C5.10899 3.96458 4.98638 3.96458 4.86376 3.96458C4.78202 3.96458 4.70027 4.00545 4.61853 4.00545L4.57766 4.04632C4.49591 4.08719 4.41417 4.16894 4.33243 4.20981C4.25068 4.29155 4.20981 4.3733 4.12807 4.45504C3.84196 4.82289 3.67847 5.27248 3.67847 5.72207C3.67847 6.04905 3.76022 6.37602 3.88283 6.66213L3.92371 6.78474C4.29155 7.56131 4.78202 8.25613 5.43597 8.86921L5.59945 9.0327C5.72207 9.15531 5.84469 9.23706 5.92643 9.35967C6.78474 10.0954 7.76567 10.6267 8.86921 10.9128C8.99183 10.9537 9.15531 10.9537 9.27793 10.9946C9.40055 10.9946 9.56403 10.9946 9.68665 10.9946C9.89101 10.9946 10.1362 10.9128 10.2997 10.8311C10.4223 10.7493 10.5041 10.7493 10.5858 10.6676L10.6676 10.5858C10.7493 10.5041 10.8311 10.4632 10.9128 10.3815C10.9946 10.2997 11.0763 10.218 11.1172 10.1362C11.1989 9.97275 11.2398 9.76839 11.2807 9.56403C11.2807 9.48229 11.2807 9.35967 11.2807 9.27793C11.2807 9.27793 11.2398 9.23706 11.158 9.19619Z' fill='#828282'/>
                                         </svg>
                                    </div>
                                  <div title='Djinni' style='display:flex' ng-if="o === 'djinni'">
                                     <svg width='18' height='16' viewBox='0 0 18 16' fill='none' xmlns='http://www.w3.org/2000/svg'>
                                        <path d='M12.5797 5.21048H10.1061C10.1236 7.26308 10.0534 11.6841 10.0534 12.9998C10.0534 13.684 9.26397 14.7893 8.42188 15.1051C8.94818 15.3682 9.94817 15.263 10.4745 15.263C11.4745 15.263 12.5797 14.1051 12.5797 12.4735V5.21048Z' stroke='#828282' stroke-width='0.5'/>
                                        <path d='M8.10582 12.5261V12.7761H8.35582V12.5261H8.10582ZM8.10582 2.36841H8.35582V2.14464L8.13343 2.11994L8.10582 2.36841ZM6.05322 12.5261L5.81069 12.5868L5.85803 12.7761H6.05322V12.5261ZM5.94797 12.1578L6.18743 12.0859V12.0859L5.94797 12.1578ZM5.8427 11.7893L6.08524 11.7287L5.97426 11.2848L5.66065 11.618L5.8427 11.7893ZM3.89536 12.684L3.8909 12.934H3.89536V12.684ZM1.00067 8.73672L1.25067 8.7401V8.73672L1.00067 8.73672ZM3.89536 5.05258L3.90754 5.30228L3.89536 5.05258ZM5.57954 5.73678L5.38749 5.89682L5.81363 6.40819L5.82947 5.74273L5.57954 5.73678ZM5.63218 3.52629L5.88218 3.53224V3.52629H5.63218ZM5.6856 7.42093H5.9356V7.36191L5.90921 7.30912L5.6856 7.42093ZM5.6856 10.1051L5.8856 10.2551L5.9356 10.1884V10.1051H5.6856ZM4.68562 6.99988V7.24988H4.68562L4.68562 6.99988ZM3.52774 8.7367L3.77774 8.73669L3.52774 8.7367ZM8.35582 12.5261V2.36841H7.85582V12.5261H8.35582ZM6.05322 12.7761H8.10582V12.2761H6.05322V12.7761ZM5.70852 12.2296C5.74855 12.363 5.78609 12.4884 5.81069 12.5868L6.29576 12.4655C6.26773 12.3534 6.22633 12.2156 6.18743 12.0859L5.70852 12.2296ZM5.60016 11.8499C5.6282 11.9621 5.6696 12.0999 5.70852 12.2296L6.18743 12.0859C6.14739 11.9525 6.10984 11.8271 6.08524 11.7287L5.60016 11.8499ZM3.89536 12.934C4.39239 12.934 4.85068 12.767 5.21519 12.5679C5.57957 12.3689 5.86634 12.129 6.02475 11.9607L5.66065 11.618C5.53836 11.7479 5.29356 11.9554 4.97549 12.1291C4.65756 12.3028 4.28253 12.434 3.89536 12.434V12.934ZM0.750692 8.73335C0.728882 10.3473 1.22576 11.3997 1.88866 12.0506C2.54512 12.6952 3.34261 12.9242 3.8909 12.934L3.89983 12.4341C3.46567 12.4263 2.79476 12.2396 2.23897 11.6938C1.6896 11.1544 1.23035 10.2419 1.25065 8.7401L0.750692 8.73335ZM3.88318 4.80287C2.69371 4.8609 1.90459 5.5811 1.42332 6.39951C0.945593 7.21187 0.75067 8.15073 0.750669 8.73672L1.25067 8.73672C1.25067 8.23502 1.42416 7.38443 1.85432 6.65296C2.28092 5.92753 2.93915 5.34952 3.90754 5.30228L3.88318 4.80287ZM5.7716 5.57673C5.60821 5.38066 5.32429 5.18131 5.00059 5.03563C4.67371 4.88853 4.27864 4.78358 3.88318 4.80287L3.90754 5.30228C4.20333 5.28785 4.51878 5.36711 4.79539 5.49159C5.07518 5.6175 5.28773 5.77711 5.38749 5.89682L5.7716 5.57673ZM5.38225 3.52034L5.32962 5.73082L5.82947 5.74273L5.8821 3.53224L5.38225 3.52034ZM8.13343 2.11994C7.37464 2.03563 6.70505 2.189 6.21778 2.45082C5.97455 2.58151 5.77085 2.74247 5.62522 2.9208C5.48106 3.09732 5.38218 3.30606 5.38218 3.52629H5.88218C5.88218 3.45705 5.91487 3.35659 6.01248 3.23706C6.10862 3.11934 6.25689 2.99741 6.45444 2.89126C6.84874 2.6794 7.41598 2.5433 8.07822 2.61688L8.13343 2.11994ZM5.4356 7.42093V10.1051H5.9356V7.42093H5.4356ZM4.68562 7.24988C4.91616 7.24988 5.10362 7.29608 5.23874 7.35845C5.37955 7.42344 5.44356 7.49587 5.46199 7.53273L5.90921 7.30912C5.82238 7.13546 5.64955 6.99737 5.44827 6.90447C5.24128 6.80893 4.98138 6.74988 4.68562 6.74988L4.68562 7.24988ZM3.77774 8.73669C3.77774 8.11638 3.92664 7.74562 4.10175 7.53446C4.27427 7.32642 4.49093 7.24988 4.68562 7.24988V6.74988C4.354 6.74988 3.99171 6.88386 3.71687 7.21529C3.44462 7.54359 3.27774 8.04124 3.27774 8.7367L3.77774 8.73669ZM4.63299 10.434C4.46111 10.434 4.33459 10.3958 4.23779 10.3351C4.1406 10.2742 4.05695 10.1804 3.98767 10.0434C3.84426 9.75988 3.77774 9.32251 3.77774 8.73669L3.27774 8.7367C3.27774 9.33995 3.3428 9.87626 3.54149 10.2691C3.64326 10.4703 3.7833 10.6404 3.97229 10.7588C4.16167 10.8775 4.38383 10.934 4.63299 10.934V10.434ZM5.4856 9.95509C5.33523 10.1556 5.12052 10.434 4.63299 10.434V10.934C5.37475 10.934 5.72018 10.4757 5.8856 10.2551L5.4856 9.95509Z' fill='#828282'/>
                                        <circle cx='11.3688' cy='2.42103' r='1.42103' stroke='#828282' stroke-width='0.5'/>
                                        <circle cx='15.5787' cy='11.0525' r='1.42103' stroke='#828282' stroke-width='0.5'/>
                                     </svg>
                                    </div>
                                    <div title="{{'viber'|translate}}" style='display:flex'  ng-if="o === 'viber'">
                                        <svg width='16' height='16' viewBox='0 0 15 16' fill='none' xmlns='http://www.w3.org/2000/svg'>
                                            <path d='M13.307 1.5425C12.9148 1.18189 11.3269 0.0273327 7.78728 0.0115168C7.78728 0.0115168 3.61504 -0.241538 1.58111 1.62474C0.448691 2.75716 0.0501298 4.41151 0.00900843 6.46758C-0.032113 8.52048 -0.0858873 12.3701 3.62453 13.4139H3.62769L3.62453 15.005C3.62453 15.005 3.60239 15.6503 4.02625 15.78C4.53869 15.9381 4.84235 15.4479 5.33265 14.9196C5.60152 14.6286 5.97161 14.2047 6.25313 13.8789C8.79001 14.0908 10.7417 13.6037 10.9631 13.531C11.4755 13.3633 14.3762 12.9932 14.8475 9.14363C15.3346 5.18332 14.6103 2.67492 13.307 1.5425ZM13.7372 8.86527C13.3387 12.0791 10.9884 12.2815 10.5551 12.4207C10.3716 12.4808 8.65715 12.9078 6.49986 12.7655C6.49986 12.7655 4.89296 14.7045 4.39318 15.2075C4.3141 15.2865 4.22237 15.3182 4.16227 15.3024C4.07686 15.2802 4.05156 15.179 4.05472 15.0303C4.05472 14.8152 4.06737 12.3827 4.06737 12.3827C4.06421 12.3827 4.06421 12.3827 4.06737 12.3827C0.926331 11.5129 1.1098 8.2358 1.14459 6.52135C1.17939 4.8069 1.50203 3.39929 2.46048 2.4535C4.17808 0.897208 7.72401 1.12812 7.72401 1.12812C10.7164 1.14077 12.1493 2.04228 12.4814 2.34278C13.5854 3.29174 14.1484 5.55342 13.7372 8.86527Z' fill='#828282'/>
                                            <path d='M9.24564 6.58148C9.14125 6.58148 9.05268 6.49924 9.04635 6.39169C9.01156 5.71161 8.69208 5.37948 8.04046 5.34468C7.92975 5.33835 7.84435 5.24346 7.85067 5.13275C7.857 5.02204 7.95189 4.93663 8.06261 4.94296C8.92299 4.9904 9.4038 5.48386 9.44808 6.36955C9.45441 6.48026 9.369 6.57516 9.25829 6.57832C9.25196 6.58148 9.2488 6.58148 9.24564 6.58148Z' fill='#828282'/>
                                            <path d='M10.2703 6.92315H10.2672C10.1565 6.91999 10.0679 6.82826 10.071 6.71755C10.0869 6.0343 9.89075 5.48074 9.47321 5.02524C9.05883 4.56974 8.48629 4.32302 7.73029 4.26924C7.61958 4.25975 7.53734 4.16486 7.54683 4.05414C7.55632 3.94343 7.65121 3.86119 7.76192 3.87068C8.61598 3.93394 9.28974 4.23128 9.77055 4.75637C10.2545 5.28462 10.4886 5.94573 10.4728 6.72704C10.4696 6.83775 10.3779 6.92315 10.2703 6.92315Z' fill='#828282'/>
                                            <path d='M11.3206 7.33427C11.2099 7.33427 11.1213 7.2457 11.1213 7.13499C11.1118 5.91084 10.7607 4.9777 10.0458 4.27864C9.34043 3.58906 8.44525 3.23795 7.39191 3.22846C7.2812 3.22846 7.19263 3.13673 7.19263 3.02602C7.19263 2.91531 7.28436 2.82674 7.39191 2.82674C8.5528 2.83623 9.53971 3.2253 10.321 3.99079C11.1055 4.75628 11.5072 5.81278 11.5199 7.12867C11.523 7.24254 11.4345 7.33427 11.3206 7.33427C11.3237 7.33427 11.3237 7.33427 11.3206 7.33427Z' fill='#828282'/>
                                            <path d='M8.0879 9.05825C8.0879 9.05825 8.36942 9.08356 8.52126 8.89693L8.81543 8.52683C8.95778 8.34337 9.30256 8.22633 9.63786 8.41296C9.82449 8.51735 10.1629 8.72612 10.3717 8.88111C10.5963 9.0456 11.0518 9.42518 11.055 9.42834C11.2732 9.61181 11.3238 9.88068 11.1752 10.1685C11.1752 10.1685 11.1752 10.1717 11.1752 10.1749C11.0233 10.4437 10.8177 10.6968 10.5615 10.9309C10.5583 10.9309 10.5583 10.934 10.5552 10.934C10.3433 11.1112 10.1345 11.2124 9.93204 11.2345C9.90673 11.2408 9.87826 11.2408 9.8403 11.2408C9.75173 11.2408 9.66317 11.2282 9.5746 11.1997L9.56827 11.1902C9.25195 11.1017 8.72686 10.8802 7.85382 10.3963C7.28445 10.0831 6.81314 9.76364 6.41141 9.44416C6.19948 9.27651 5.98438 9.08988 5.76296 8.86846C5.75663 8.86213 5.74714 8.85264 5.74082 8.84632C5.73449 8.83999 5.725 8.8305 5.71867 8.82417C5.71235 8.81785 5.70286 8.80836 5.69653 8.80203C5.69021 8.79571 5.68072 8.78622 5.67439 8.77989C5.45613 8.55847 5.26634 8.34337 5.09869 8.13144C4.77921 7.73288 4.45973 7.2584 4.14657 6.68902C3.6626 5.81282 3.44118 5.28773 3.35261 4.97458L3.34312 4.96825C3.31465 4.87968 3.302 4.79111 3.302 4.70254C3.302 4.66459 3.302 4.63612 3.30833 4.61081C3.33363 4.4052 3.43486 4.1996 3.60883 3.98766C3.60883 3.9845 3.61199 3.9845 3.61199 3.98134C3.84607 3.72196 4.09912 3.51951 4.36799 3.36768C4.36799 3.36768 4.37116 3.36768 4.37432 3.36768C4.65901 3.21901 4.92788 3.26962 5.11451 3.48788C5.11451 3.48788 5.49725 3.94654 5.66174 4.17113C5.81673 4.38306 6.0255 4.71836 6.12989 4.90499C6.31652 5.24029 6.19948 5.58507 6.01601 5.72742L5.64592 6.02159C5.45929 6.17343 5.4846 6.45495 5.4846 6.45495C5.4846 6.45495 6.03183 8.53632 8.0879 9.05825Z' fill='#828282'/>
                                        </svg>
                                    </div>
                                    <div title="{{'skype'|translate}}" style='display:flex' ng-if="o === 'skype'">
                                         <svg xmlns='http://www.w3.org/2000/svg' height='16px' viewBox='0 0 512 512' width='16px'>
                                             <g><path d='m498.078125 307.289062c3.582031-16.917968 5.394531-34.144531 5.394531-51.289062 0-66.101562-25.742187-128.246094-72.480468-174.988281-46.742188-46.742188-108.886719-72.484375-174.988282-72.484375-17.113281 0-34.339844 1.8125-51.285156 5.398437-19.136719-9.125-40.304688-13.925781-61.570312-13.925781-78.933594 0-143.148438 64.214844-143.148438 143.144531 0 21.257813 4.800781 42.425781 13.925781 61.574219-3.578125 16.914062-5.390625 34.136719-5.390625 51.28125 0 66.101562 25.742188 128.246094 72.480469 174.988281 46.738281 46.738281 108.882813 72.480469 174.988281 72.480469 17.113282 0 34.335938-1.816406 51.277344-5.394531 19.144531 9.125 40.3125 13.925781 61.578125 13.925781 78.929687 0 143.140625-64.214844 143.140625-143.140625 0-21.257813-4.800781-42.425781-13.921875-61.570313zm-129.21875 174.710938c-18.5625 0-36.269531-4.371094-52.636719-12.996094-2.171875-1.144531-4.574218-1.730468-6.992187-1.730468-1.144531 0-2.292969.132812-3.421875.398437-16.425782 3.847656-33.179688 5.796875-49.804688 5.796875-119.914062 0-217.46875-97.554688-217.46875-217.46875 0-16.65625 1.949219-33.410156 5.796875-49.796875.824219-3.519531.351563-7.21875-1.332031-10.417969-8.628906-16.378906-13-34.089844-13-52.640625 0-62.386719 50.757812-113.144531 113.148438-113.144531 18.5625 0 36.265624 4.371094 52.625 12.992188 3.199218 1.6875 6.898437 2.160156 10.417968 1.335937 16.429688-3.847656 33.1875-5.800781 49.8125-5.800781 119.914063 0 217.46875 97.558594 217.46875 217.472656 0 16.65625-1.949218 33.414062-5.796875 49.804688-.828125 3.519531-.355469 7.21875 1.328125 10.417968 8.625 16.375 12.996094 34.082032 12.996094 52.636719 0 62.386719-50.753906 113.140625-113.140625 113.140625zm0 0' data-original='#000000' class='active-path' data-old_color='#000000' fill='#838287'/><path d='m256 140.035156c44.246094 0 81.632812 23.117188 81.632812 50.480469 0 8.285156 6.714844 15 15 15 8.28125 0 15-6.714844 15-15 0-22.621094-12.394531-43.472656-34.890624-58.71875-20.710938-14.035156-47.964844-21.765625-76.742188-21.765625s-56.03125 7.730469-76.742188 21.765625c-22.5 15.246094-34.890624 36.097656-34.890624 58.71875s12.390624 43.476563 34.890624 58.722656c20.707032 14.035157 47.960938 21.761719 76.742188 21.761719 44.246094 0 81.632812 23.117188 81.632812 50.484375 0 27.363281-37.386718 50.480469-81.632812 50.480469-44.25 0-81.632812-23.117188-81.632812-50.480469 0-8.285156-6.714844-15-15-15-8.28125 0-15 6.714844-15 15 0 22.617187 12.390624 43.472656 34.890624 58.71875 20.710938 14.03125 47.964844 21.761719 76.742188 21.761719s56.03125-7.730469 76.742188-21.761719c22.496093-15.246094 34.890624-36.101563 34.890624-58.71875 0-22.625-12.394531-43.476563-34.890624-58.722656-20.710938-14.03125-47.964844-21.761719-76.742188-21.761719-44.25 0-81.632812-23.117188-81.632812-50.484375 0-27.363281 37.382812-50.480469 81.632812-50.480469zm0 0' data-original='#000000' class='active-path' data-old_color='#000000' fill='#838287'/></g>
                                          </svg>
                                    </div>
                                    <div title="{{'github'|translate}}" style='display:flex' ng-if="o === 'github'">
                                        <svg xmlns='http://www.w3.org/2000/svg' enable-background='new 0 0 24 24' height='15px' viewBox='0 0 24 24' width='15px'>
                                                                <g><path d='m12 .5c-6.63 0-12 5.28-12 11.792 0 5.211 3.438 9.63 8.205 11.188.6.111.82-.254.82-.567 0-.28-.01-1.022-.015-2.005-3.338.711-4.042-1.582-4.042-1.582-.546-1.361-1.335-1.725-1.335-1.725-1.087-.731.084-.716.084-.716 1.205.082 1.838 1.215 1.838 1.215 1.07 1.803 2.809 1.282 3.495.981.108-.763.417-1.282.76-1.577-2.665-.295-5.466-1.309-5.466-5.827 0-1.287.465-2.339 1.235-3.164-.135-.298-.54-1.497.105-3.121 0 0 1.005-.316 3.3 1.209.96-.262 1.98-.392 3-.398 1.02.006 2.04.136 3 .398 2.28-1.525 3.285-1.209 3.285-1.209.645 1.624.24 2.823.12 3.121.765.825 1.23 1.877 1.23 3.164 0 4.53-2.805 5.527-5.475 5.817.42.354.81 1.077.81 2.182 0 1.578-.015 2.846-.015 3.229 0 .309.21.678.825.56 4.801-1.548 8.236-5.97 8.236-11.173 0-6.512-5.373-11.792-12-11.792z' fill='#838287' data-original='#212121' class='active-path' data-old_color='#212121'/></g>
                                         </svg>
                                    </div>
                                    <div title="{{'behance'|translate}}" style='display:flex' ng-if="o === 'behance'">
                                            <svg width='16' height='16' viewBox='0 0 15 10' fill='none' xmlns='http://www.w3.org/2000/svg'>
                                                    <path d='M4.36286 0C4.79582 0 5.19547 0.0333043 5.56182 0.133217C5.92817 0.199826 6.2279 0.333043 6.49434 0.499565C6.76077 0.666086 6.9606 0.899216 7.09382 1.19895C7.22703 1.49869 7.29364 1.86504 7.29364 2.26469C7.29364 2.73095 7.19373 3.1306 6.9606 3.43034C6.76077 3.73008 6.42773 3.99652 6.02808 4.19634C6.59425 4.36286 7.02721 4.6626 7.29364 5.06225C7.56008 5.46191 7.7266 5.96147 7.7266 6.52764C7.7266 6.9939 7.62668 7.39356 7.46016 7.7266C7.29364 8.05964 7.02721 8.35938 6.72747 8.55921C6.42773 8.75903 6.06138 8.92555 5.66173 9.02547C5.26208 9.12538 4.86243 9.19199 4.46278 9.19199H0V0H4.36286ZM4.09643 3.73008C4.46278 3.73008 4.76251 3.63017 4.99565 3.46365C5.22878 3.29713 5.32869 2.99739 5.32869 2.63104C5.32869 2.43121 5.29538 2.23139 5.22877 2.09817C5.16217 1.96495 5.06225 1.86504 4.92904 1.76513C4.79582 1.69852 4.6626 1.63191 4.49608 1.59861C4.32956 1.5653 4.16304 1.5653 3.96321 1.5653H2.03156V3.73008H4.09643ZM4.19634 7.65999C4.39617 7.65999 4.59599 7.62668 4.76252 7.59338C4.92904 7.56008 5.09556 7.49347 5.22877 7.39356C5.36199 7.29364 5.46191 7.19373 5.56182 7.02721C5.62843 6.86069 5.69504 6.66086 5.69504 6.42773C5.69504 5.96147 5.56182 5.62843 5.29538 5.3953C5.02895 5.19547 4.6626 5.09556 4.22965 5.09556H2.03156V7.65999H4.19634ZM10.6241 7.62668C10.8905 7.89312 11.2902 8.02634 11.823 8.02634C12.1894 8.02634 12.5224 7.92642 12.7889 7.7599C13.0553 7.56008 13.2218 7.36025 13.2884 7.16042H14.9203C14.6539 7.95973 14.2542 8.5259 13.7214 8.89225C13.1885 9.22529 12.5557 9.42512 11.7897 9.42512C11.2569 9.42512 10.7906 9.3252 10.3576 9.15868C9.92468 8.99216 9.59164 8.75903 9.2919 8.42599C8.99216 8.12625 8.75903 7.7599 8.62581 7.32695C8.45929 6.89399 8.39268 6.42773 8.39268 5.89486C8.39268 5.3953 8.45929 4.92904 8.62581 4.49608C8.79233 4.06312 9.02547 3.69678 9.3252 3.36373C9.62494 3.064 9.99129 2.79756 10.3909 2.63104C10.8239 2.46452 11.2569 2.36461 11.7897 2.36461C12.3559 2.36461 12.8555 2.46452 13.2884 2.69765C13.7214 2.93078 14.0544 3.19721 14.3208 3.59686C14.5873 3.96321 14.7871 4.39617 14.9203 4.86243C14.9869 5.32869 15.0202 5.79495 14.9869 6.32782H10.1578C10.1578 6.86069 10.3576 7.36025 10.6241 7.62668ZM12.7222 4.12973C12.4891 3.8966 12.1228 3.76339 11.6898 3.76339C11.3901 3.76339 11.1569 3.82999 10.9571 3.92991C10.7573 4.02982 10.6241 4.16304 10.4909 4.29625C10.3576 4.42947 10.291 4.59599 10.2577 4.76252C10.2244 4.92904 10.1911 5.06225 10.1911 5.19547H13.1885C13.1219 4.69591 12.9554 4.36286 12.7222 4.12973ZM9.79146 0.632782H13.5215V1.532H9.79146V0.632782Z' fill='#828282'/>
                                            </svg>
                                    </div>
                                    <div title="{{'homepage'|translate}}" style='display:flex' ng-if="o === 'homepage'">
                                        <svg xmlns='http://www.w3.org/2000/svg' height='15px' viewBox='0 0 512 512' width='15px' class=''>
                                            <g><path d='m437.019531 74.980469c-48.351562-48.351563-112.636719-74.980469-181.015625-74.980469h-.007812c-68.378906 0-132.664063 26.628906-181.015625 74.980469-48.351563 48.351562-74.980469 112.640625-74.980469 181.019531s26.628906 132.667969 74.980469 181.019531c48.351562 48.351563 112.640625 74.980469 181.019531 74.980469s132.667969-26.628906 181.019531-74.980469c48.351563-48.351562 74.980469-112.640625 74.980469-181.019531s-26.628906-132.667969-74.980469-181.019531zm16.722657 290.335937h-74.925782c10.039063-29.414062 16.085938-61.261718 17.496094-94.316406h85.175781c-2.238281 34.03125-12.042969 66.023438-27.746093 94.316406zm-197.742188 114.464844c-31.929688-19-58.410156-48.449219-77.457031-84.464844h154.914062c-19.046875 36.015625-45.527343 65.464844-77.457031 84.464844zm-91.015625-114.464844c-11.03125-28.941406-17.71875-60.898437-19.265625-94.316406h220.5625c-1.546875 33.417969-8.238281 65.375-19.269531 94.316406zm-134.472656-94.316406h85.175781c1.410156 33.054688 7.457031 64.902344 17.496094 94.316406h-74.925782c-15.703124-28.292968-25.507812-60.285156-27.746093-94.316406zm27.746093-124.316406h74.925782c-10.039063 29.414062-16.085938 61.261718-17.496094 94.316406h-85.175781c2.238281-34.03125 12.042969-66.023438 27.746093-94.316406zm197.742188-114.464844c31.929688 19 58.410156 48.449219 77.457031 84.464844h-154.914062c19.046875-36.015625 45.527343-65.464844 77.457031-84.464844zm91.015625 114.464844c11.03125 28.941406 17.71875 60.898437 19.265625 94.316406h-220.5625c1.546875-33.417969 8.238281-65.375 19.269531-94.316406zm134.472656 94.316406h-85.175781c-1.410156-33.054688-7.457031-64.902344-17.496094-94.316406h74.925782c15.703124 28.292968 25.507812 60.285156 27.746093 94.316406zm-114.683593-124.316406c-14.257813-30.796875-33.226563-58.011719-56-79.949219 49.429687 12.359375 92.472656 41.027344 123.03125 79.949219zm-165.609376-79.949219c-22.773437 21.9375-41.742187 49.152344-56 79.949219h-67.03125c30.558594-38.921875 73.601563-67.589844 123.03125-79.949219zm-56 358.582031c14.257813 30.796875 33.226563 58.011719 56 79.949219-49.429687-12.359375-92.472656-41.027344-123.03125-79.949219zm165.609376 79.949219c22.773437-21.9375 41.742187-49.15625 56-79.949219h67.03125c-30.558594 38.921875-73.601563 67.589844-123.03125 79.949219zm0 0' data-original='#000000' class='active-path' data-old_color='#000000' fill='#838287'/></g>
                                        </svg>
                                    </div>
                                    <div title="{{'other'|translate}}" style='display:flex' ng-if="o === 'other'">
                                            <svg width='15' height='15' viewBox='0 0 15 10' fill='none' xmlns='http://www.w3.org/2000/svg'>
                                                <path fill-rule='evenodd' clip-rule='evenodd' d='M0.681818 0H8.86364C9.23864 0 9.54545 0.306818 9.54545 0.681818V8.86364C9.54545 9.23864 9.23864 9.54545 8.86364 9.54545H0.681818C0.306818 9.54545 0 9.23864 0 8.86364V0.681818C0 0.306818 0.306818 0 0.681818 0ZM11.5909 9.54545C11.9659 9.54545 12.2727 9.23864 12.2727 8.86364V0.681818C12.2727 0.306818 11.9659 0 11.5909 0C11.2159 0 10.9091 0.306818 10.9091 0.681818V8.86364C10.9091 9.23864 11.2159 9.54545 11.5909 9.54545ZM13.6364 0.681818V8.86364C13.6364 9.23864 13.9432 9.54545 14.3182 9.54545C14.6932 9.54545 15 9.23864 15 8.86364V0.681818C15 0.306818 14.6932 0 14.3182 0C13.9432 0 13.6364 0.306818 13.6364 0.681818ZM4.77273 1.875C5.61818 1.875 6.30682 2.56364 6.30682 3.40909C6.30682 4.25455 5.61818 4.94318 4.77273 4.94318C3.92727 4.94318 3.23864 4.25455 3.23864 3.40909C3.23864 2.56364 3.92727 1.875 4.77273 1.875ZM1.70455 7.67045V8.18182H7.84091V7.67045C7.84091 6.64773 5.79545 6.13636 4.77273 6.13636C3.75 6.13636 1.70455 6.64773 1.70455 7.67045Z' fill='#828282'/>
                                             </svg>
                                    </div>
                                </div>
                                <div ng-style="(o === 'djinni')? {'padding':'7px 11px'} : null" ng-click='selectOpt(o, $index);' class='list-item-ops'>{{getPropertyValue(o, path) | translate: o.translateValues}}</div>
                            </div>                        
                        </div>`,
                };
            },
        ])
        .directive('customSelectNewScoreCard', [
            '$window',
            '$filter',
            '$timeout',
            '$rootScope',
            function ($window, $filter, $timeout, $rootScope) {
                return {
                    restrict: 'E',
                    scope: {
                        options: '=options',
                        model: '=?model',
                        path: '=path',
                        placeholder: '=placeholder',
                        disabled: '=disabled',
                        method: '=method',
                        type: '=type',
                        removeMethod: '=removeMethod',
                        addMethod: '=addMethod',
                        trackIndex: '=trackIndex',
                        isNoNeedToTranslateOptions: '=?',
                        thereIsNotDefaultValueExist: '=?',
                        needToAccept: '=?',
                        hidePlaceholderFromOptions: '=?hidePlaceholderFromOptions',
                        isRestrictionForOption: '=?isRestrictionForOption',
                        objectIndex: '=?',
                        trackSelectedOptionById: '=?',
                        updateCurrencyValue: '=?',
                        customStylesOfLabel: '=customStylesOfLabel',
                        trackSelectedOptionByValue: '=?',
                        customTranslate: '=?',
                        fieldId: '=?',
                        fieldValueId: '=?',
                        notSetModel: '=?',
                        valueToMethod: '=?',
                        customOptStyles: '=?',
                        customStatusStyles: '=?',
                        bigFirstLetter: '=?',
                    },
                    link: function (scope, element, attrs) {
                        scope.getPropertyValue = function (obj = {}, path = '') {
                            if (obj && obj.value === 'e00_no_experience') return 'experience_assoc.e00_no_experience';
                            if (scope.customTranslate) {
                                const langObj = $rootScope.allLanguages[obj];
                                if (langObj) {
                                    return $rootScope.currentLang === 'ru'
                                        ? langObj.russian[0].toUpperCase() + langObj.russian.slice(1)
                                        : $rootScope.currentLang === 'en'
                                        ? langObj.english[0].toUpperCase() + langObj.english.slice(1)
                                        : langObj.ukrainian[0].toUpperCase() + langObj.ukrainian.slice(1);
                                }
                            }
                            if (scope.bigFirstLetter) {
                                if (typeof obj === 'string') {
                                    obj = obj[0].toUpperCase() + obj.slice(1).toLowerCase();
                                    obj = replaceCategory(obj);
                                }
                            }
                            if (!path) return obj ? (obj.value ? obj.value : obj) : obj;
                            if (!obj) return null;

                            let prevProp = null;

                            path.split('.').forEach((prop) => {
                                prevProp = prevProp ? prevProp[prop] : obj[prop];
                            });

                            return prevProp || prevProp === false ? prevProp : obj;
                        };

                        scope.setPropertyValue = function (obj = {}, value) {
                            const path = scope.path.split('.');
                            for (let i = 0; i < path.length - 1; i++) {
                                let prop = path[i];
                                if (prop in o) {
                                    obj = obj[prop];
                                } else {
                                    obj[prop] = {};
                                    obj = obj[prop];
                                }
                            }

                            obj[path[path.length - 1]] = value;

                            return obj;
                        };
                        for (let i in scope.options) {
                            if (
                                scope.trackSelectedOptionById &&
                                scope.options[i].id &&
                                angular.equals(scope.options[i].id, scope.model)
                            ) {
                                scope.selectedOpt = scope.options[i].value;
                            } else if (
                                scope.trackSelectedOptionByValue &&
                                scope.options[i].value &&
                                scope.model &&
                                angular.equals(scope.options[i].value, scope.model.value)
                            ) {
                                scope.selectedOpt = scope.options[i];
                            } else if (angular.equals(scope.options[i], scope.model)) {
                                scope.selectedOpt = scope.options[i];
                            }
                        }
                        // Select an option
                        scope.selectOpt = function (opt, index) {
                            if (scope.customStatusStyles) {
                                if (opt === 'open' || opt === 'inwork' || opt === 'completed') {
                                    scope.isGreenStatus = true;
                                } else {
                                    scope.isGreenStatus = false;
                                }
                            }
                            if (scope.updateCurrencyValue) {
                                $rootScope.$emit('updateCurrencyValue', opt);
                            }
                            if (scope.needToAccept) {
                                $rootScope.$emit('modelFromCSNwasReplaced', {
                                    oldVal: scope.model,
                                    newVal: opt,
                                });
                                $rootScope.$on('replaceCSNModelDecision', (evt, data) => {
                                    if (data) {
                                        changeModel(opt, index);
                                    } else {
                                        optionsDom.removeClass('active');
                                        backdrop.removeClass('active');
                                    }
                                });
                            } else {
                                changeModel(opt, index);
                            }
                        };

                        $rootScope.$on('attachCreatedScoreCard', (evt, attachedCard) => changeModel(attachedCard));

                        function replaceCategory(category) {
                            const categories = {
                                Backend: 'Back End',
                                Big_data: 'Big Data',
                                C: 'C/C++',
                                Data_scientist: 'Data Scientist',
                                Dba: 'DBA',
                                Dev_ops: 'DevOps',
                                Erp_crm: 'ERP/CRM',
                                Frontend: 'Front End',
                                Full_stack: 'Full Stack',
                                Hr: 'HR in IT',
                                Sales: 'Sales in IT',
                                Gamedev: 'GameDev',
                                Ios_mac: 'iOS/macOS',
                                _net: '.Net',
                                Node: 'Node.js',
                                Product_manager: 'Product Manager',
                                Project_manager: 'Project Manager',
                                Qa: 'QA',
                                Seo: 'SEO',
                                System_administrator: 'System Administrator',
                                Technical_writer: 'Technical Writer',
                                Php: 'PHP',
                            };
                            return categories[category] || category;
                        }

                        function changeModel(opt, index) {
                            if (scope.valueToMethod) {
                                scope.method(scope.valueToMethod, opt);
                                optionsDom.removeClass('active');
                                backdrop.removeClass('active');
                                return;
                            }
                            if (scope.notSetModel) {
                                scope.method(opt);
                                optionsDom.removeClass('active');
                                backdrop.removeClass('active');
                                return;
                            }
                            if (scope.hasOwnProperty('model')) scope.model = opt;
                            if (scope.method && scope.model && scope.fieldId) {
                                scope.method(scope.model.value, scope.fieldId, scope.fieldValueId);
                                optionsDom.removeClass('active');
                                backdrop.removeClass('active');
                                return;
                            }
                            if (scope.method && scope.trackIndex) {
                                scope.method(index);
                            } else if (
                                scope.method && typeof scope.objectIndex === 'number'
                                    ? scope.objectIndex.toString()
                                    : false
                            ) {
                                scope.method(opt, scope.objectIndex);
                            } else if (scope.method) {
                                scope.method(opt);
                            }
                            optionsDom.removeClass('active');
                            backdrop.removeClass('active');
                        }

                        scope.removeOpt = function (opt, index) {
                            if (scope.removeMethod && scope.trackIndex) {
                                scope.removeMethod(index);
                            } else if (scope.removeMethod) scope.removeMethod(opt);
                        };

                        scope.addOpt = function () {
                            scope.addMethod();
                        };

                        scope.setPlaceholder = function () {
                            if (scope.hasOwnProperty('model')) scope.model = null;
                            close();
                            if (scope.fieldId) scope.method('', scope.fieldId, scope.fieldValueId);
                        };

                        scope.$watch(
                            'model',
                            function (newVal) {
                                for (let i in scope.options) {
                                    if (
                                        scope.trackSelectedOptionById &&
                                        scope.options[i].id &&
                                        angular.equals(scope.options[i].id, newVal)
                                    ) {
                                        scope.selectedOpt = scope.options[i].value;
                                    } else if (
                                        scope.trackSelectedOptionByValue &&
                                        scope.options[i].value &&
                                        angular.equals(scope.options[i].value, newVal.value)
                                    ) {
                                        scope.selectedOpt = scope.options[i];
                                    } else if (angular.equals(scope.options[i], newVal)) {
                                        scope.selectedOpt = scope.options[i];
                                    }
                                }
                            },
                            true,
                        );

                        var labelDom = element.find('.select-label'),
                            optionsDom = element.find('.select-ops'),
                            backdrop = element.find('.select-backdrop');

                        labelDom.on('click', open);
                        backdrop.on('click', close);

                        if (scope.placeholder && !scope.model) scope.setPlaceholder();

                        function open() {
                            if (!scope.disabled) {
                                optionsDom.toggleClass('active');
                                backdrop.toggleClass('active');
                            }
                        }

                        $rootScope.removeError = function () {
                            $rootScope.wrongRole = false;
                        };

                        function close() {
                            optionsDom.removeClass('active');
                            backdrop.removeClass('active');
                            labelDom.removeClass('disabled');
                        }

                        $(element).bind('open', open);
                        $(element).bind('close', close);

                        scope.$watch('disabled', () => {
                            if (scope.disabled) labelDom.addClass('disabled');
                            else labelDom.removeClass('disabled');
                        });
                        scope.onResetModel = function () {
                            if (scope.hasOwnProperty('model')) scope.model = null;
                        };
                    },
                    template: `<div class='select-label custom-new' tabindex='0'>
                            <span style='position:relative' ng-show='model' class='select-label-text' ng-class="{'select-label-text-custom': customStylesOfLabel }" ng-if='!isNoNeedToTranslateOptions  && !customOptStyles && !customStatusStyles'><span class='ellipsis'>{{getPropertyValue(model || selectedOpt, path) | translate: selectedOpt.translateValues}}</span> <img ng-if='resetModel' ng-click='onResetModel();$event.stopPropagation();' style='height:10px;width:10px;position: absolute;right: 15px;top:11px' src='images/sprite/close-icon.svg' alt=''></span> 
                            <span ng-show='model' class='select-label-text' ng-class="{'greenStatus':selectedOpt === 'open'|| model === 'open' || selectedOpt === 'inwork' || model === 'inwork' || selectedOpt === 'completed' || model === 'completed','yellowStatus':!(selectedOpt === 'open'|| model === 'open' || selectedOpt === 'inwork' || model === 'inwork' || selectedOpt === 'completed' || model === 'completed')}" ng-if='!isNoNeedToTranslateOptions  && !customOptStyles && customStatusStyles'>{{getPropertyValue(selectedOpt || model, path) | translate: selectedOpt.translateValues}}</span> 
                            <span ng-show='model' class='select-label-text' ng-class="{'select-label-text-custom': customStylesOfLabel,'lowPriorityStyles':selectedOpt ==='low' || model ==='low','mediumPriorityStyles':selectedOpt ==='medium' || model ==='medium','highPriorityStyles':selectedOpt ==='top' || model ==='high' }" ng-if='!isNoNeedToTranslateOptions && customOptStyles'>{{getPropertyValue(selectedOpt || model, path) | translate: selectedOpt.translateValues}}</span> 
                            <span ng-show='model' class='select-label-text' ng-if="isNoNeedToTranslateOptions && thereIsNotDefaultValueExist && getPropertyValue(selectedOpt || model, path) !== 'Default'">{{getPropertyValue(selectedOpt || model, path)}}</span>
                            <span ng-show='model' class='select-label-text'   ng-if="isNoNeedToTranslateOptions && thereIsNotDefaultValueExist && getPropertyValue(selectedOpt || model, path) === 'Default'">{{getPropertyValue(selectedOpt || model, path) | translate: selectedOpt.translateValues}}</span>
                            <span ng-show='!model' class='select-label-text placeholder' ng-class="{'select-label-text-custom': customStylesOfLabel }" translate='{{placeholder}}'></span>
                            <span class='select-caret'>
                                <span></span>
                            </span>
                        </div>
                        <div class='select-backdrop custom-new'></div>
                        <div class='select-ops custom-new'>
                            <div ng-if="placeholder && type !== 'country' && !hidePlaceholderFromOptions" ng-click='setPlaceholder()' translate='{{placeholder}}'></div>
                            <div ng-repeat='o in options track by $index' class='list-ops'>
                                <div ng-click='selectOpt(o, $index);' class='list-item-ops'  ng-if='!isNoNeedToTranslateOptions && !customOptStyles && !customStatusStyles'><span style='margin-right:5px' ng-if='o.userId===$root.me.userId'>({{ "Me"| translate}})</span>{{getPropertyValue(o, path) | translate: o.translateValues}}</div>
                                <div ng-class="{'greenStatus':o === 'open' || o === 'inwork' || o === 'completed' ,'yellowStatus':o !== 'open' && o !== 'inwork' && o !== 'completed'}" ng-click='selectOpt(o, $index);' class='list-item-ops'  ng-if='!isNoNeedToTranslateOptions && !customOptStyles && customStatusStyles'>{{getPropertyValue(o, path) | translate: o.translateValues}}</div>
                                <div ng-class="{'lowPriorityStyles':o ==='low','mediumPriorityStyles':o ==='medium','highPriorityStyles':o ==='top'}" ng-click='selectOpt(o, $index);' class='list-item-ops'  ng-if='!isNoNeedToTranslateOptions && customOptStyles'>{{getPropertyValue(o, path) | translate: o.translateValues}}</div>
                                <div ng-click='selectOpt(o, $index);' class='list-item-ops'  ng-if="isNoNeedToTranslateOptions && thereIsNotDefaultValueExist && getPropertyValue(o, path) !== 'Default'">{{getPropertyValue(o, path)}}</div>
                                <div ng-click='selectOpt(o, $index);' class='list-item-ops'  ng-if="isNoNeedToTranslateOptions && thereIsNotDefaultValueExist && getPropertyValue(o, path) === 'Default'">{{getPropertyValue(o, path) | translate: o.translateValues}}</div>
                                <div ng-show='o.count || o.count == 0' class='white-space-nowrap self-vertical-center'>{{o.count}}</div>
                                <img ng-if='removeMethod' class='trash' src='/images/sprite/icons/delete-forever.svg' ng-click='removeOpt(o, $index)' alt />
                            </div>                        
                            <div ng-if='addMethod' class='list-ops' ng-click='addOpt()'>
                                <div class='list-item-ops'><img src='/images/sprite/custom-fields/add.svg' alt/>{{"Add Card" | translate}}</div>
                            </div>
                        </div>`,
                };
            },
        ])
        .directive('integratedEmailSelect', [
            '$rootScope',
            function ($rootScope) {
                return {
                    restrict: 'EACM',
                    scope: {
                        email: '=?',
                        myEmails: '=?',
                        addEmailMethod: '=?',
                        dropdownbool: '=?',
                    },
                    template: `<div class='email-select-header email-select-show-dropdown' ng-class="{'error-email-sender': myEmails.length === 0}">
                            <span style='position: relative; right: -3px; top: 1px' ng-bind='email' class='email-select-show-dropdown'></span>
                            <span class='chevron-block email-select-show-dropdown' ng-if='myEmails.length > 0'>
                            </span> 
                        </div>
                        <div style='top: 33px' class='dropdown-wrapper'>
                                <div class='integrated-sender-email' ng-repeat='emailItem in myEmails track by $index' ng-show='emailItem.personalMailing && emailItem.email !== email' ng-bind='emailItem.email' ng-click='addEmailMethod(emailItem)'></div>
                                <a class='integrated-sender-email integrate-new' target='_blank' href='!#/email-integration' translate='Add email'>
                                </a>
                        </div>`,
                    link: function (scope, element, attrs) {
                        scope.myEmails = scope.myEmails || $rootScope.me.emails;
                        scope.openDrop = false;
                        scope.addEmailMethod = scope.addEmailMethod || $rootScope.addEmailFromWhatSend;
                        let dropdownPdConsentElement = $('.dropdown-wrapper');
                        $('.modal-dialog ').on('click', ($event) => {
                            if (scope.dropdownbool) {
                                toggleDropdowns($event);
                            }
                        });

                        function toggleDropdowns(event) {
                            scope.openDrop = !scope.openDrop;
                            if ($(event.target).hasClass('email-select-show-dropdown')) {
                                dropdownPdConsentElement.toggle();
                                if (scope.openDrop) {
                                    setTimeout(() => {
                                        document.querySelector('.email-select-header').style.borderBottomLeftRadius =
                                            'inherit';
                                        document.querySelector('.email-select-header').style.borderBottomRightRadius =
                                            'inherit';
                                        document.querySelector('.chevron-block').style.borderBottomRightRadius = '0';
                                    }, 0);
                                } else {
                                    setTimeout(() => {
                                        document.querySelector('.email-select-header').style.borderBottomLeftRadius =
                                            '5px';
                                        document.querySelector('.email-select-header').style.borderBottomRightRadius =
                                            '5px';
                                        document.querySelector('.chevron-block').style.borderBottomRightRadius = '5px';
                                    }, 0);
                                }
                            } else {
                                dropdownPdConsentElement.hide();
                                setTimeout(() => {
                                    document.querySelector('.email-select-header').style.borderBottomLeftRadius = '5px';
                                    document.querySelector('.email-select-header').style.borderBottomRightRadius =
                                        document.querySelector('.chevron-block').style.borderBottomRightRadius = '5px';
                                }, 0);
                                scope.openDrop = false;
                            }
                        }
                    },
                };
            },
        ])
        .directive('firstLetters', [
            '$rootScope',
            function ($rootScope) {
                return {
                    restrict: 'EACM',
                    link(scope, element, attrs) {
                        setTimeout((_) => {
                            element[0].innerHTML = scope.getFirstLetters(element[0].innerHTML);
                        });
                    },
                };
            },
        ])
        .directive('searchCriteriaScroll', [
            '$timeout',
            function ($timeout) {
                return {
                    restrict: 'A',
                    link: function (scope, element, attrs) {
                        $(element).mCustomScrollbar({
                            theme: 'dark',
                            scrollInertia: 300,
                        });

                        const searchCriteria = document.getElementsByClassName('search-criteria')[0];
                        const criteriaFields = document.getElementsByClassName('criteria-search-fields');

                        const searchResults = document.getElementsByClassName(attrs.target + '-result')[0];

                        function resizeSearchCriteria() {
                            $timeout(() => {
                                let max = 0;
                                if (criteriaFields) {
                                    for (let field of criteriaFields) {
                                        max = max > field.offsetHeight ? max : field.offsetHeight;
                                    }
                                }

                                if (searchCriteria) searchCriteria.style.height = max + 'px';
                            });
                        }

                        function resizeSearchResults() {
                            const advancedSearch = document.getElementsByClassName('search-advance active')[0];
                            if (searchResults && advancedSearch)
                                searchResults.style.minHeight = advancedSearch
                                    ? advancedSearch.offsetHeight + 'px'
                                    : '0px';
                        }

                        scope.$watch(
                            'vm.search.fields',
                            function () {
                                resizeSearchCriteria();
                                resizeSearchResults();
                            },
                            true,
                        );

                        $(window).resize(() => {
                            resizeSearchResults();
                        });
                    },
                };
            },
        ])
        .directive('candidatesLanguageSearch', [
            'notificationService',
            'Candidate',
            '$filter',
            function (notificationService, Candidate, $filter) {
                return {
                    restrict: 'E',
                    scope: {
                        languages: '=',
                        selectedLanguages: '=',
                        languagesObj: '=',
                        index: '=',
                    },
                    link: function (scope, element, attr) {
                        var labelDom = element.find('.select-label'),
                            optionsDom = element.find('.select-ops'),
                            backdrop = element.find('.select-backdrop');

                        var preSelected = 0;

                        labelDom.on('click', function () {
                            if (!scope.disabled) {
                                optionsDom.toggleClass('active');
                                backdrop.toggleClass('active');
                            } else {
                                labelDom.addClass('disabled');
                            }
                        });
                        backdrop.on('click', function () {
                            optionsDom.removeClass('active');
                            backdrop.removeClass('active');
                            labelDom.removeClass('disabled');
                        });

                        function updatePreSelection() {
                            optionsDom.children().filter('.preselected').removeClass('preselected');
                            optionsDom.find('div').eq(preSelected).addClass('preselected');
                        }

                        updatePreSelection();

                        scope.getCheckedState = function (language) {
                            return scope.languagesObj.selectedLanguages.some((selectedLang) => {
                                return selectedLang.name === language.name && selectedLang.level === language.level;
                            });
                        };

                        function resetSelectedLanguage() {
                            for (let i = 0; i < scope.languagesObj.selectedLanguages.length; i++) {
                                if (scope.languagesObj.selectedLanguages[i].level !== 'Level is not specified') {
                                    scope.languagesObj.removeField(i);
                                    i--;
                                }
                            }
                        }

                        scope.toggleSelectedLanguages = function (e, language) {
                            e.stopPropagation();

                            if (e.target.checked) {
                                if (language.level === 'Level is not specified') {
                                    resetSelectedLanguage();
                                    scope.languagesObj.addField(language, language.level);
                                    return;
                                }

                                if (scope.languagesObj.selectedLanguages.length === 5) {
                                    e.preventDefault();
                                    notificationService.error($filter('translate')('You can select up to 5 languages'));
                                    return;
                                }
                                scope.languagesObj.addField(language, language.level);
                            } else {
                                scope.languagesObj.selectedLanguages.forEach((lang, index) => {
                                    if (lang.name === language.name && lang.level === language.level) {
                                        scope.languagesObj.removeField(index);
                                    }
                                });
                            }

                            !scope.$$phase || scope.$apply();
                        };
                    },
                    template: `
                    <div class='select-label custom-new' tabindex='0' title='{{selectedOpt.label}}'>
                        <span style='font-size: 14px' class='select-label-text'>{{ languagesObj.selectedLanguages[index].name }} ({{ languagesObj.selectedLanguages[index].level | translate}})</span>
                        <span class='select-caret'>
                            <i style='color:#b6b6b6' class='fa fa-chevron-down' aria-hidden='true'></i>
                        </span>
                    </div>
                    <div class='select-backdrop custom-new'></div>
                    <div class='select-ops custom-new'>
                        <div style='padding:12px;overflow-y:hidden'  ng-repeat='language in languages track by $index'>
                        <input type='checkbox' ng-click='toggleSelectedLanguages($event, language)' ng-checked='getCheckedState(language)' class='custom default' id='{{language.name}}{{$index}}'>
                        <label for='{{language.name}}{{$index}}' ng-click='$event.stopPropagation()'>
                            <span style='position:absolute' class='label-text'>{{ language.name }} ({{ language.level | translate}})</span>
                        </label>
                        </div>
                    </div>
            `,
                };
            },
        ])
        .directive('searchCriteriaToggle', [
            '$timeout',
            function ($timeout) {
                return {
                    restrict: 'A',
                    link: function ($scope, element, attrs) {
                        const fields = $scope.search ? $scope.search.fields : $scope.vm.search.fields;
                        $timeout(() => {
                            $("[data-checkbox='parent']").bind('click', function () {
                                const customFields = $scope.customFieldsSearch
                                    ? $scope.customFieldsSearch.fields
                                    : $scope.vm.customFieldsSearch.fields;
                                const criteriaType = $(this).attr('data-criteria');
                                const checkState = $(this).prop('checked');

                                if (criteriaType === 'customFields') {
                                    Object.entries(customFields).forEach(([name, field]) => {
                                        field.state.isSelected = checkState;

                                        if (!checkState) {
                                            field.reset({
                                                name: field.name,
                                            });
                                        }
                                    });
                                }

                                Object.entries(fields).forEach(([name, field]) => {
                                    if (field.type === criteriaType) {
                                        field.state.isSelected = checkState;

                                        if (!checkState) {
                                            switch (name) {
                                                case 'origin':
                                                    field.reset({
                                                        callback: $scope.setOriginAutocompleterPlaceholderValue,
                                                    });
                                                    break;
                                                case 'industry':
                                                    field.reset({
                                                        callback: $scope.setIndustryAutocompleterPlaceholderValue,
                                                    });
                                                    break;
                                                case 'position':
                                                    field.reset({
                                                        callback: $scope.setPositionAutocompleterPlaceholderValue,
                                                    });
                                                    break;
                                                case 'skills':
                                                    field.reset({
                                                        callback: $scope.setSkillAutocompleterPlaceholderValue,
                                                    });
                                                    break;
                                                case 'candidateGroup':
                                                    field.reset({
                                                        callback: $scope.clearTags,
                                                    });
                                                    break;
                                                default:
                                                    field.reset({});
                                            }
                                        }
                                    }
                                });
                            });
                        });
                    },
                };
            },
        ])
        .directive('customFields', [
            '$rootScope',
            'CustomField',
            '$translate',
            '$timeout',
            function ($rootScope, CustomField, $translate, $timeout) {
                return {
                    restrict: 'EA',
                    scope: {
                        fields: '=',
                    },
                    templateUrl: '/partials/custom-fields-for-search.html',
                    link: function (scope, element, attr) {
                        scope.setDateTimeValue = function (field, ttt) {
                            if (!field.state.isActive) {
                                field.value = 'no_value';
                            } else {
                                field.value = null;
                            }
                            if (field.value === 'no_value') {
                                if ($rootScope.searchNullValues && $rootScope.searchNullValues.length) {
                                    if (!$rootScope.searchNullValues.includes('customFields')) {
                                        $rootScope.searchNullValues.push('customFields');
                                    }
                                } else {
                                    $rootScope.searchNullValues = ['customFields'];
                                }
                                if (!$rootScope.searchNullCustomFields) {
                                    $rootScope.searchNullCustomFields = [field.placeholder];
                                } else $rootScope.searchNullCustomFields.push(field.placeholder);
                            } else {
                                if ($rootScope.searchNullCustomFields) {
                                    $rootScope.searchNullCustomFields = $rootScope.searchNullCustomFields.filter(
                                        (item) => item !== field.placeholder,
                                    );
                                }
                                if (
                                    $rootScope.searchNullValues &&
                                    $rootScope.searchNullValues.length &&
                                    $rootScope.searchNullCustomFields &&
                                    $rootScope.searchNullCustomFields.length === 0
                                ) {
                                    $rootScope.searchNullValues = $rootScope.searchNullValues.filter(
                                        (item) => item !== 'customFields',
                                    );
                                }
                            }
                        };
                        scope.resetField = function (field) {
                            field.value = null;
                            if ($rootScope.searchNullCustomFields) {
                                $rootScope.searchNullCustomFields = $rootScope.searchNullCustomFields.filter(
                                    (item) => item !== field.placeholder,
                                );
                            }
                            if (
                                $rootScope.searchNullValues &&
                                $rootScope.searchNullValues.length &&
                                $rootScope.searchNullCustomFields &&
                                $rootScope.searchNullCustomFields.length === 0
                            ) {
                                $rootScope.searchNullValues = $rootScope.searchNullValues.filter(
                                    (item) => item !== 'customFields',
                                );
                            }
                        };
                    },
                };
            },
        ])
        .directive('customFieldsScrollBar', function () {
            return function (scope, element, attrs) {
                $(element).mCustomScrollbar({
                    theme: 'dark-3',
                    scrollInertia: 1000,
                });
            };
        })
        .directive('fileAttach', [
            'Service',
            function (Service) {
                return {
                    scope: {
                        ioFile: '<',
                        files: '<',
                        removeFile: '&',
                        withIcon: '<',
                        maxFilesCount: '<',
                    },
                    template: `<span class='item attach-button' ng-repeat='file in files track by $index'>
                                <a ng-href='{{window.location.origin}}/hr/getapp/{{ file.fileId}}/{{ file.fileResolution}}' title='{{ file.fileName}}'>
                                    {{file.fileName| fileNameCut:0:30}}
                                </a>
                                <i class='fa fa-times' aria-hidden='true' ng-click='removeFile({fileId: file.fileId})'></i>
                            </span>
                            <input type='file' oi-file='ioFile' id='file' class='hidden'/>
                            <label for='file' class='attach-button' ng-show='!files || files.length < maxFilesCount'>
                                <div class='attach-file-icon'></div>
                                <span translate='Attach file'></span>
                            </label>`,
                    link: function (scope, element, attrs) {
                        if (scope.files) {
                            scope.files = scope.files.map((file) => {
                                file.fileResolution = Service.getFileResolutionFromName(file.fileName);
                            });
                        }
                    },
                };
            },
        ])
        .directive('templateFileAttach', [
            'Service',
            '$rootScope',
            function (Service, $rootScope) {
                return {
                    scope: {
                        ioFile: '<',
                        files: '<',
                        removeFile: '&',
                        withIcon: '<',
                        maxFilesCount: '<',
                    },
                    template: `<div class='file-attach'>
                                    <div class='file-attach-wrapper' ng-if='files'>
                                        <div class='file-attach-wrapper-items' ng-repeat='file in files track by $index'>
                                            <a ng-click='$root.downloadFile(file.fileId, file.fileName)' title='{{ file.fileName}}'>
                                            {{file.fileName| fileNameCut:0:30}}
                                            </a>
                                            <img src='images/redesign/svg-icons/close.svg' alt='' aria-hidden='true' ng-click='removeFile({fileId: file.fileId})' />
                                        </div>
                                    </div>
                                    <div class='file-attach-btn' ng-hide='files.length >= 3'>
                                        <input type='file' oi-file='ioFile' id='file' class='hidden'/>
                                        <label for='file' class='attach-button'>
                                        <img src='images/redesign/svg-icons/attach.svg' alt=''>
                                        <span class='file-attach-btn-attach' translate='Attach file'></span>
                                        </label>
                                    </div>
                                    <div class="file-attach-message">{{'Max. size of 1 file is 5mb' | translate}}</div>
                                </div>`,
                    link: function (scope, element, attrs) {
                        if (scope.files) {
                            scope.files = scope.files.map((file) => {
                                file.fileResolution = Service.getFileResolutionFromName(file.fileName);
                            });
                            if (scope.files[0] === undefined) {
                                scope.files = $rootScope.fileForSave;
                            }
                        }
                    },
                };
            },
        ])
        .directive('vacancyRangeSlider', [
            '$rootScope',
            '$filter',
            function ($rootScope, $filter) {
                return function (scope, element, attrs) {
                    const container = document.getElementById('time-range-value');
                    let current = 2;

                    $rootScope.$on('$translateChangeSuccess', function () {
                        initRangeSlider();
                        $(element).slider('value', current);
                        container.innerText = `${current} ${$filter('translate')('min')}`;
                        // scope.vm.data.timeRange = current;
                        scope.vm.model.timeRange = current;
                    });

                    function initRangeSlider() {
                        $(element).slider({
                            min: 2,
                            max: 120,
                            slide: function (event, ui) {
                                container.innerText = `${ui.value} ${$filter('translate')('min')}`;
                                current = ui.value;
                                // scope.vm.data.timeRange = current;
                                scope.vm.model.timeRange = current;
                            },
                            create: function (event, ui) {
                                container.innerText = `${current} ${$filter('translate')('min')}`;
                                $(element).slider('value', current);
                                // scope.vm.data.timeRange = current;
                                scope.vm.model.timeRange = current;
                            },
                        });
                    }

                    initRangeSlider();
                };
            },
        ])
        .directive('ctrlEnterSubmit', () => {
            return {
                restrict: 'A',
                link: function (scope, elem, attrs) {
                    elem.bind('keydown', function (event) {
                        var code = event.keyCode || event.which;

                        if ((event.ctrlKey || event.metaKey) && (code === 13 || code === 10)) {
                            if (!event.shiftKey) {
                                event.preventDefault();
                                scope.$apply(attrs.ctrlEnterSubmit);
                            }
                        }
                    });
                },
            };
        })
        .directive('horizontalScrollBar', [
            function () {
                return function (scope, element, attrs) {
                    $(element).mCustomScrollbar({
                        axis: 'x',
                        theme: 'dark',
                        scrollInertia: 1,
                        advanced: {
                            updateOnContentResize: true,
                        },
                    });
                };
            },
        ])
        .directive('elastic', [
            '$timeout',
            function ($timeout) {
                return {
                    restrict: 'A',
                    link: function ($scope, element) {
                        $scope.initialHeight = $scope.initialHeight || element[0].style.height;
                        element.on('input change', resize);
                        $timeout(resize, 0);

                        function resize() {
                            element[0].style.height = $scope.initialHeight;
                            element[0].style.height = '' + element[0].scrollHeight + 'px';
                        }
                    },
                };
            },
        ])
        .directive('toggleElementVisibility', [
            '$timeout',
            function ($timeout) {
                return {
                    restrict: 'A',
                    scope: { targetId: '=', backdropId: '=' },
                    link: function (scope, element, attrs) {
                        const target = document.getElementById(scope.targetId);
                        const backdrop = document.getElementById(scope.backdropId);

                        element.bind('click', function (e) {
                            e.stopPropagation();
                            target.classList.toggle('hidden');
                        });

                        backdrop.addEventListener('click', function () {
                            target.classList.add('hidden');
                        });
                    },
                };
            },
        ])
        .directive('horizontalSwipe', [
            function () {
                return {
                    scope: {
                        leftSwipe: '<',
                        rightSwipe: '<',
                        returnListenerRemover: '<',
                        context: '<',
                    },
                    link: function (scope, element, attrs) {
                        document.addEventListener('touchstart', touchStartHandler, false);
                        document.addEventListener('touchend', touchEndHandler, false);
                        if (scope.context) {
                            scope.leftSwipe = scope.leftSwipe.bind(scope.context);
                            scope.rightSwipe = scope.rightSwipe.bind(scope.context);
                        }
                        scope.$on('$destroy', () => {
                            removeListeners();
                        });

                        function triggerSliderBySwipe() {
                            let coordStart = {};
                            return (startPos, endPos) => {
                                if (startPos) coordStart = startPos;
                                else {
                                    const xDiff = coordStart.x - endPos.x;
                                    const yDiff = coordStart.y - endPos.y;
                                    if (Math.abs(xDiff) > 100 && Math.abs(yDiff) < 100) {
                                        if (scope.returnListenerRemover) {
                                            if (xDiff > 0) scope.rightSwipe(removeListeners);
                                            else scope.leftSwipe(removeListeners);
                                        } else {
                                            if (xDiff > 0) scope.rightSwipe();
                                            else scope.leftSwipe();
                                        }
                                    }
                                }
                            };
                        }

                        const swipeSlider = triggerSliderBySwipe();

                        function touchStartHandler(event) {
                            let touches = event.touches;
                            swipeSlider(
                                {
                                    x: touches[0].clientX,
                                    y: touches[0].clientY,
                                },
                                null,
                            );
                        }

                        function touchEndHandler(event) {
                            let touches = event.changedTouches;
                            swipeSlider(null, {
                                x: touches[0].clientX,
                                y: touches[0].clientY,
                            });
                        }

                        function removeListeners() {
                            document.removeEventListener('touchstart', touchStartHandler, false);
                            document.removeEventListener('touchend', touchEndHandler, false);
                        }
                    },
                };
            },
        ])
        .directive('datepickerReferralWithdraw', [
            '$translate',
            '$rootScope',
            '$filter',
            function ($translate, $rootScope, $filter) {
                return {
                    restrict: 'EA',
                    scope: {
                        withdrawalObj: '=datepickerReferralWithdraw',
                    },
                    link: function (scope, element) {
                        $(element)
                            .datetimepicker({
                                format: 'dd.mm.yyyy',
                                startView: 4,
                                minView: 2,
                                autoclose: true,
                                language: $translate.use(),
                                weekStart: 1,
                                initialDate: scope.dateModel ? scope.dateModel : new Date(),
                                startDate: new Date(-1262304000000),
                            })
                            .on('changeDate', function (val) {
                                if (val.date) {
                                    scope.withdrawalObj.confirmDate = val.date.getTime();
                                    scope.withdrawalObj.confirmDateView = scope.withdrawalObj.confirmDate
                                        ? $filter('date')(scope.withdrawalObj.confirmDate, 'dd.MM.yy')
                                        : 'Day/Month/Year';
                                    if (scope.withdrawalObj.confirmDate && scope.withdrawalObj.initStatus !== 'A') {
                                        if (scope.withdrawalObj.status !== 'A') {
                                            scope.withdrawalObj.status = 'A';
                                        }
                                    }
                                    $rootScope.$$phase || $rootScope.$apply();
                                }
                            })
                            .on('hide', function () {
                                $(element).blur();
                            });
                    },
                };
            },
        ])
        .directive('mailingTab', [
            '$location',
            'Person',
            '$uibModal',
            '$rootScope',
            ($location, Person, $uibModal, $rootScope) => {
                return {
                    restrict: 'EA',
                    link: function (scope, element, attr) {
                        scope.closeModal = function () {
                            scope.modalInstance.close();
                        };
                        scope.integrateCorporateEmail = () => $location.path('/email-integration');
                        element.bind('click', () => {
                            $rootScope.loading = true;
                            Person.isIntegratedCorporateEmail().then(
                                (resp) => {
                                    if (resp.object.emails.some((email) => email.permitMailing)) {
                                        scope.emailIsEnabled = true;
                                        $location.path('/mailings/prepared');
                                        scope.$apply();
                                    } else {
                                        scope.emailIsEnabled = false;
                                        onReject();
                                    }
                                    $rootScope.loading = false;
                                },
                                (resp) => {
                                    if (resp) {
                                        scope.noOneIntegratedCorpEmail = true;
                                        onReject();
                                    }
                                    $rootScope.loading = false;
                                },
                            );
                        });

                        function onReject() {
                            scope.modalInstance = $uibModal.open({
                                animation: true,
                                templateUrl: '../partials/modal/not-allowed-mailing.html',
                                controller: [
                                    '$uibModalInstance',
                                    'emailIsEnabled',
                                    'noOneIntegratedCorpEmail',
                                    notAllowedMailing,
                                ],
                                controllerAs: 'vm',
                                windowClass: 'corporate-email-not-integrated-modal secondary-modal',
                                size: '',
                                resolve: {
                                    emailIsEnabled: function () {
                                        return scope.emailIsEnabled;
                                    },
                                    noOneIntegratedCorpEmail: function () {
                                        return scope.noOneIntegratedCorpEmail;
                                    },
                                },
                            });
                            scope.modalInstance.result.then(
                                function () {},
                                function () {},
                            );
                        }

                        function notAllowedMailing($uibModalInstance, emailIsEnabled, noOneIntegratedCorpEmail) {
                            const vm = this;
                            vm.isMassMailing = true;
                            vm.emailIsEnabled = emailIsEnabled;
                            vm.noOneIntegratedCorpEmail = noOneIntegratedCorpEmail;

                            vm.closeModal = function () {
                                $uibModalInstance.close();
                            };
                        }
                    },
                };
            },
        ])
        .directive('heightToLeftColumn', () => {
            return {
                restrict: 'EA',
                link: function (scope, element, attr) {
                    let elem = document.getElementById('candidate-profile-rezume__left-column');
                    if (elem) {
                        element[0].style.maxHeight = elem.offsetHeight - 128 + 'px';
                    }
                },
            };
        })
        .directive('toggleElementVisibilityHover', [
            '$timeout',
            function ($timeout) {
                return {
                    restrict: 'A',
                    scope: { targetId: '=', backdropId: '=' },
                    link: function (scope, element, attrs) {
                        const target = document.getElementById(scope.targetId);
                        const backdrop = document.getElementById(scope.backdropId);

                        element.hover(function (e) {
                            e.stopPropagation();
                            if (attrs.toggleElementVisibilityHover === 'true') {
                                target.classList.toggle('hidden');
                            }
                        });
                    },
                };
            },
        ])
        .directive('testCandidateBlock', function () {
            return {
                restrict: 'A',
                link: function (scope, element, attrs) {
                    scope.$watch(function () {
                        scope.candidatesBlockMaxHeight =
                            $('.showLetter').height() -
                            ($('.autoSelect').height() +
                                parseInt($('.autoSelect').css('margin-bottom')) +
                                $('.testCandidatesBlockEmail').height() +
                                parseInt($('.testCandidatesBlockEmail').css('margin-bottom'))) +
                            'px';
                    });
                },
            };
        })
        .directive('clickTooltip', function () {
            return {
                restrict: 'A',
                link: function (scope, element, attrs) {
                    element[0].addEventListener('click', () => {
                        element[0].children[0].classList.remove('click-tooltip__active');
                    });
                    element[0].addEventListener('mouseenter', () => {
                        element[0].children[0].classList.add('click-tooltip__active');
                    });
                    element[0].addEventListener('mouseleave', () => {
                        element[0].children[0].classList.remove('click-tooltip__active');
                    });
                },
            };
        })
        .directive('accountScopePanel', accountScopePanel)
        .directive('mailTo', ['Service', mailTo])
        .directive('ngMiddleClick', ngMiddleClick)
        .directive('customSelect', setCustomSelect)
        .directive('tooltipMove', tooltipMove)
        .directive('pdConsent', pdConsent)
        .directive('errorPopup', removePopUp)
        .directive('removedPerson', removedPerson)
        .directive('dynamicTranslateDateFormatSimple', dynamicTranslate)
        .directive('dynamicTranslateDateFormatShort', dynamicTranslateDateShort)
        .directive('dynamicTranslateDateShortForStatistic', dynamicTranslateDateShortForStatistic)
        .directive('vacanciesForCandidate', vacanciesForCandidate);

    function vacanciesForCandidate($rootScope, Company, $window, $location) {
        return {
            restrict: 'A',
            link: function (scope, element, attrs) {
                element.bind('click', function () {
                    Company.getParams(function (resp) {
                        scope.companyParams = resp.object;
                        $rootScope.publicLink =
                            $location.$$protocol +
                            '://' +
                            $location.$$host +
                            '/i/' +
                            scope.companyParams.nameAlias +
                            '-vacancies';
                        $window.open($rootScope.publicLink, '_blank');
                    });
                });
            },
        };
    }

    function dynamicTranslate($filter, $rootScope, $timeout) {
        return {
            scope: {
                date: '=',
                item: '=',
            },
            link: function (scope, element, attrs) {
                const dateFilter = $filter('dateFormat7');

                scope.item.parsedDate = dateFilter(scope.date);

                $rootScope.$on('$translateChangeSuccess', () =>
                    $timeout(() => {
                        scope.item.parsedDate = dateFilter(scope.date);
                    }, 0),
                );
            },
        };
    }

    function dynamicTranslateDateShort($filter, $rootScope, $timeout) {
        return {
            scope: {
                date: '=',
                item: '=',
            },
            link: function (scope, element, attrs) {
                const dateFilter = $filter('dateFormatShort');

                scope.$watch('date', function () {
                    if (!scope.item) return;
                    scope.item.parsedDate = dateFilter(scope.date);

                    $rootScope.$on('$translateChangeSuccess', () =>
                        $timeout(() => {
                            scope.item.parsedDate = dateFilter(scope.date);
                        }, 0),
                    );
                });
            },
        };
    }

    function dynamicTranslateDateShortForStatistic($filter, $rootScope, $timeout) {
        return {
            scope: {
                date: '=',
                item: '=',
            },
            link: function (scope, element, attrs) {
                const dateFilter = $filter('dateFormatShort');

                scope.$watch('date', function () {
                    if (!scope.item) return;
                    scope.item.parsedDate = dateFilter(scope.date);

                    $rootScope.$on('$translateChangeSuccess', () =>
                        $timeout(() => {
                            scope.item.parsedDate = dateFilter(scope.date);
                        }, 0),
                    );
                });
            },
        };
    }

    function ngMiddleClick() {
        var directive = {
            restrict: 'A',
            link: function ngMiddleClickLink($scope, element, attrs) {
                var clickExpression = attrs.ngMiddleClick || attrs.ngClick;

                if (clickExpression) {
                    // Using 'auxclick' cause modern browsers no longer trigger 'click' event for middle button
                    // https://w3c.github.io/uievents/#event-type-auxclick
                    var event = 'onauxclick' in document.documentElement ? 'auxclick' : 'mousedown';

                    element.on(event, function (e) {
                        if (e.which === 2) {
                            // Not sure if the 'preventDefault' will always work when using 'mousedown' event
                            if (e.currentTarget.getAttribute('disabled') === 'disabled') {
                                return e.preventDefault();
                            }

                            $scope.$eval(clickExpression, { $event: e });
                            $scope.$$phase || $scope.$apply();
                        }
                    });
                }
            },
        };
        return directive;
    }

    function mailTo(Service) {
        return {
            scope: {
                email: '<',
                subject: '<',
                body: '<',
                toEmails: '<',
                selectEmailId: '@',
                editorId: '@',
                paramsGetter: '&',
            },
            template: `<span class='mailto-elem' ng-click='mailtoClick($event)' title='{{\"Send via default mail client\"|translate}}'  ng-if='$root.me.orgParams.mailto === \"true\"'>
                    <i class="attach-icon mailto-elem-icon mailto-elem-icon__mail"></i>
                    <span class='text'>{{ 'Send via default mail client' | translate  }}</span>
                </span>`,
            link: function (scope, element, attrs) {
                let mailtoRef = document.createElement('a');

                scope.mailtoClick = function (event) {
                    event.preventDefault();
                    let emailSelect = scope.selectEmailId ? $('#' + scope.selectEmailId) : scope.selectEmailId;
                    let email = !scope.email ? '' : scope.email;
                    let text = !scope.body ? ' ' : scope.body;
                    let subject = !scope.subject ? ' ' : scope.subject;
                    if (!email && scope.selectEmailId && emailSelect) {
                        let emails = emailSelect.select2('val');
                        if (typeof emails === 'object') email = emails.join(',');
                    }
                    if (email.isArray) {
                        email = email.join(',');
                    }
                    let getterPromise = scope.paramsGetter({ text: text });
                    if (getterPromise) {
                        getterPromise.then(
                            (resp) => {
                                text = Service.editorHtmlToPlainText(resp);
                                mailtoRef.setAttribute('href', `mailto:${email}?subject=${subject}&body=${text}`);
                                mailtoRef.click();
                            },
                            (err) => {},
                        );
                    } else {
                        text = text ? Service.editorHtmlToPlainText(text) : '';
                        mailtoRef.setAttribute('href', `mailto:${email}?subject=${subject}&body=${text}`);
                        mailtoRef.click();
                    }
                };

                scope.$on('$destroy', () => {
                    mailtoRef.remove();
                });
            },
        };
    }

    function accountScopePanel() {
        return {
            restrict: 'AE',
            templateUrl: 'partials/accountScopePanel.html',
            link: function ($scope, element, attrs) {
                let dropdownRegionsElement = $('.dropdown-regions');
                let dropdownCompanyElement = $('.dropdown-company');
                $('body')
                    .unbind()
                    .on('click', ($event) => {
                        toggleDropdowns($event);
                    });

                function toggleDropdowns(event) {
                    if ($(event.target).hasClass('show-regions-dropdown')) {
                        dropdownRegionsElement.toggle();
                        dropdownCompanyElement.hide();
                    } else {
                        if ($(event.target).hasClass('show-company-dropdown')) {
                            dropdownCompanyElement.toggle();
                        }
                        dropdownRegionsElement.hide();
                    }
                }
            },
        };
    }

    function pdConsent($document) {
        return {
            restrict: 'AE',
            templateUrl: 'partials/pdConsent.html',
            link: function (element, attrs) {
                let dropdownPdConsentElement = $('.pd-consent-dropdown');
                let dropdownChangeStatus = $('.change-statuses');
                $document.unbind().on('click', ($event) => {
                    toggleDropdowns($event);
                });

                function toggleDropdowns(event) {
                    if ($(event.target).hasClass('show-pd-consent-dropdown')) {
                        dropdownPdConsentElement.toggle();
                    } else {
                        if ($(event.target).hasClass('change-gdpr-status')) {
                            dropdownChangeStatus.show();
                        } else {
                            dropdownPdConsentElement.hide();
                            dropdownChangeStatus.hide();
                        }
                    }
                }
            },
        };
    }

    function tooltipMove($filter) {
        let restrict = 'EACM';
        return {
            restrict,
            link(scope, element, attrs) {
                element[0].addEventListener('mouseenter', showToolTip.bind(element[0], $filter, attrs.view));
                element[0].addEventListener('mouseleave', hiddenToolTip);
            },
        };
    }

    function showToolTip($filter, view, event) {
        let text,
            tooltipContent = document.createElement('div');

        if (this.dataset && this.dataset.tooltip) {
            text = this.dataset.tooltip;
            view === 'tooltip-white-hint'
                ? tooltipContent.classList.add('tooltip-white-hint')
                : tooltipContent.classList.add('info-content');
            tooltipContent.classList.add('tooltip-hint');
            tooltipContent.innerHTML = text;
            this.after(tooltipContent);
        }
    }

    function hiddenToolTip() {
        let allToolTips = document.querySelectorAll('.tooltip-hint');
        allToolTips.forEach((tooltip) => tooltip.remove());
    }

    function removedPerson($compile, $timeout) {
        let restrict = 'EACM';
        return {
            restrict,
            link(scope, element, attrs) {
                let user = element.find('.user, a:not(.not-user)'),
                    dataHistory = scope.history,
                    angularElement,
                    compileFn,
                    config = setRemoveConfig(user, dataHistory, element);

                if (!config['isSetRemoveConfig']) return;

                async function appendWithAngularContext(item) {
                    angularElement = angular.element(item.user);
                    compileFn = await $compile(angularElement)(scope);
                    $(element) > $("a:contains('User removed')").addClass('remove-user-in-history');
                }

                config.modifiedElement.forEach((item) => {
                    appendWithAngularContext.apply(null, [item]);
                });
            },
        };
    }

    function setRemoveConfig(user, dataHistory, element) {
        let data = {};
        data.modifiedElement = [];
        data.isSetRemoveConfig = false;

        if (!dataHistory.person.firstName || (dataHistory.person.status && dataHistory.person.status === 'D')) {
            setRemoveConfigForPerson(user[0], dataHistory, data, 'person.firstName');
            data.isSetRemoveConfig = true;
        }

        if (
            dataHistory.targetPerson &&
            (!dataHistory.targetPerson.cutFullName ||
                (dataHistory.targetPerson.status && dataHistory.targetPerson.status === 'D'))
        ) {
            setRemoveConfigForPerson(user[1] || user[0], dataHistory, data, 'targetPerson.fullName');
            setRemoveConfigForPerson(user[1] || user[0], dataHistory, data, 'targetPerson.fullNameEn');
            data.isSetRemoveConfig = true;
        }

        if (data.isSetRemoveConfig) {
            data.modifiedElement.forEach((item) => {
                if (item.user !== undefined) {
                    item.user.classList.add(`${item.class}`);
                    item.user.setAttribute('error-popup', '');
                    item.user.removeAttribute('href');
                }
            });
        }

        return data;
    }

    function setRemoveConfigForPerson(user, dataHistory, data, way) {
        let splitWay = way.split('.'),
            name = dataHistory[splitWay[0]][splitWay[1]];

        if (user !== undefined) {
            user.dataset.error = 'User deleted from account';
        }

        if (!name || dataHistory.type !== 'user_removed') {
            dataHistory[splitWay[0]][splitWay[1]] = 'User removed';
        }
        data.modifiedElement.push({ user, class: 'action-user' });
    }

    function removePopUp(notificationService, $translate) {
        let restrict = 'EACM';
        return {
            restrict,
            link(scope, element, attrs) {
                element[0].onclick = function (event) {
                    notificationService.error($translate.instant(this.dataset.error));
                    event.preventDefault();
                };
            },
        };
    }

    function setCustomSelect($rootScope) {
        let dataOpenSelect = [],
            restrict = 'EAM',
            scope = {
                data: '=',
                model: '=',
                placeholder: '@',
                method: '=',
                $scope: '=',
                event: '=',
                new: '@',
            },
            template = `
        <div class='custom-select' ng-class="{'error-select': $scope.emptySelect}">
            <input type='text' 
            ng-model='model' 
            placeholder='{{placeholder|translate}}' 
            readonly 
            class='form-control col-lg-12 select-input-field'>
            <div class='dropdown-content' style='z-index: -999'>
                <ul>
                    <li class='item-custom-select'
                        ng-repeat='item in data track by $index' 
                        ng-click='method(item, $scope, $event, $index)' 
                        ng-class="{disable: (item.status == 'N')}">{{(item.text || item.name)|translate}} (ID:{{item.accountId}})</li>
                </ul>
            </div>
             <span class='new-label' ng-show='new' style='right: 0px;'>new</span>
             <div class='clearfix'></div>
        </div>`;
        return {
            restrict,
            scope,
            template,
            link(scope, element, attrs) {
                let body = document.querySelector('body'),
                    customSelect = element.find('.custom-select')[0];

                customSelect.addEventListener('click', _actionClickOnTheCustomSelect);

                body.addEventListener('click', (event) => {
                    if (!event.target.classList.contains('select-input-field')) {
                        _hideDropDownContent(customSelect.querySelector('.dropdown-content'));
                    }
                });

                function _actionClickOnTheCustomSelect(event) {
                    let element = event.target;

                    if (element.tagName === 'INPUT') {
                        visibleSelect(element.nextElementSibling, event);
                    } else if (element.classList.contains('custom-select')) {
                        visibleSelect(element.querySelector('.dropdown-content'), event);
                    }
                }

                function visibleSelect(dropdownContent, event) {
                    let isDropDownContentShow = dropdownContent.classList.contains('activeBlock');

                    if (isDropDownContentShow) {
                        _hideDropDownContent(dropdownContent);
                    } else {
                        dataOpenSelect.forEach((item) => item.classList.remove('activeBlock'));
                        dropdownContent.classList.add('activeBlock');
                        dataOpenSelect.push(dropdownContent);
                    }

                    event.stopPropagation();
                }

                function _hideDropDownContent(hiddenElement) {
                    hiddenElement.classList.remove('activeBlock');
                }
            },
        };
    }

    function similar_text(first, second, percent) {
        if (first === null || second === null || typeof first === 'undefined' || typeof second === 'undefined') {
            return 0;
        }
        first += '';
        second += '';
        var pos1 = 0,
            pos2 = 0,
            max = 0,
            firstLength = first.length,
            secondLength = second.length,
            p,
            q,
            l,
            sum;
        max = 0;
        for (p = 0; p < firstLength; p++) {
            for (q = 0; q < secondLength; q++) {
                for (
                    l = 0;
                    p + l < firstLength && q + l < secondLength && first.charAt(p + l) === second.charAt(q + l);
                    l++
                );
                if (l > max) {
                    max = l;
                    pos1 = p;
                    pos2 = q;
                }
            }
        }
        sum = max;
        if (sum) {
            if (pos1 && pos2) {
                sum += similar_text(first.substr(0, pos1), second.substr(0, pos2));
            }
            if (pos1 + max < firstLength && pos2 + max < secondLength) {
                sum += similar_text(
                    first.substr(pos1 + max, firstLength - pos1 - max),
                    second.substr(pos2 + max, secondLength - pos2 - max),
                );
            }
        }
        if (!percent) {
            return sum;
        } else {
            return (sum * 200) / (firstLength + secondLength);
        }
    }

    function getPosition(element) {
        var xPosition = 0;
        var yPosition = 0;
        while (element) {
            xPosition += element.offsetLeft - element.scrollLeft + element.clientLeft;
            yPosition += element.offsetTop - element.scrollTop + element.clientTop;
            element = element.offsetParent;
        }
        return { x: xPosition, y: yPosition };
    }

    function createSpanForInterviewStatusHistory(arrname, status, short, customClass, customInterviewStateId) {
        var span = `<span style='font-weight: normal !important;' class='${customClass}'>`;

        if (customInterviewStateId) {
            return span + status + '</span>';
        }

        switch (status) {
            case 'longlist':
                return span + "{{'interview_status_assoc_full.longlist'|translate}}" + '</span>';
            case 'shortlist':
                return span + "{{'interview_status_assoc_full.shortlist'|translate}}" + '</span>';
            case 'interview':
                return span + "{{'interview_status_assoc_full.interview'|translate}}" + '</span>';
            case 'notafit':
                return span + "{{'interview_status_assoc_full.notafit'|translate}}" + '</span>';
            case 'approved':
                return span + "{{'interview_status_assoc_full.approved'|translate}}" + '</span>';
            case 'declinedoffer':
                return span + "{{'interview_status_assoc_full.declinedoffer'|translate}}" + '</span>';
            case 'offer_declined':
                return span + "{{'interview_status_assoc_full.offer_declined'|translate}}" + '</span>';
            case 'probation_failure':
                return span + "{{'interview_status_assoc_full.probation_failure'|translate}}" + '</span>';
            case 'completed':
                return span + "{{'interview_status_assoc.completed'|translate}}" + '</span>';

            default:
                if (short && status == 'interview_with_the_boss') {
                    return span + "{{'" + status + "'|translate}} </span>";
                } else {
                    if (arrname) {
                        return span + "{{'" + arrname + '.' + status + "'|translate}} </span>";
                    } else {
                        return `<span class='${customClass}' translate='${status}'></span>`;
                    }
                }
        }
    }

    function createDivForInterviewStatusHistory(status) {
        var span = "<div class='grey-hover vacancy-stages'";
        switch (status) {
            case 'longlist':
                return `${span}">{{'longlist'|translate}}<div>`;
            case 'shortlist':
                return `${span}">{{'interview_status_assoc_full.shortlist'|translate}}</div>`;
            case 'interview':
                return `${span}">{{'interview_status_assoc_full.interview'|translate}}</div>`;
            case 'notafit':
                return `${span}">{{'interview_status_assoc_full.notafit'|translate}}</div>`;
            case 'approved':
                return `${span}">{{'interview_status_assoc_full.approved'|translate}}</div>`;
            case 'declinedoffer':
                return `${span}">{{'interview_status_assoc_full.declinedoffer'|translate}}</div>`;
            case 'offer_declined':
                return `${span}">{{'interview_status_assoc_full.offer_declined'|translate}}</div>`;
            case 'probation_failure':
                return `${span}">{{'interview_status_assoc_full.probation_failure'|translate}}</div>`;
            case 'interview_with_the_client':
                return `${span}">{{'interview_status_assoc_full.interview_with_the_client'|translate}}</div>`;
            case 'tech_interview':
                return `${span}">{{'interview_status_assoc_full.tech_interview'|translate}}</div>`;
            case 'interview_with_the_boss':
                return `${span}">{{'interview_status_assoc_full.interview_with_the_boss'|translate}}</div>`;
            case 'accept_offer':
                return `${span}">{{'interview_status_assoc_full.accept_offer'|translate}}</div>`;
            case 'sent_offer':
                return `${span}">{{'interview_status_assoc_full.sent_offer'|translate}}</div>`;
            case 'hr_interview':
                return `${span}">{{'interview_status_assoc_full.hr_interview'|translate}}</div>`;
            case 'no_response':
                return `${span}">{{'interview_status_assoc_full.no_response'|translate}}</div>`;
            case 'tech_screen':
                return `${span}">{{'interview_status_assoc_full.tech_screen'|translate}}</div>`;
            case 'security_check':
                return `${span}">{{'interview_status_assoc_full.security_check'|translate}}</div>`;
            case 'test_task':
                return `${span}">{{'interview_status_assoc_full.test_task'|translate}}</div>`;
            case 'no_contacts':
                return `${span}">{{'interview_status_assoc_full.no_contacts'|translate}}</div>`;
            case 'is_not_looking_for_job':
                return `${span}">{{'interview_status_assoc_full.is_not_looking_for_job'|translate}}</div>`;
            case 'accepted_counter_offer':
                return `${span}">{{'interview_status_assoc_full.accepted_counter_offer'|translate}}</div>`;
            case 'found_another_job':
                return `${span}">{{'interview_status_assoc_full.found_another_job'|translate}}</div>`;
            default:
                return `${span}">${status}</div>`;
        }
    }
})();
