var directives = directives || angular.module('RecruitingApp.directives', []);

(function () {
    directives.directive('releaseNotification', [
        '$http',
        '$location',
        '$rootScope',
        'Person',
        '$uibModal',
        '$timeout',
        function ($http, $location, $rootScope, Person, $uibModal, $timeout) {
            return {
                restrict: 'E',
                template: `<div style="display: block" ng-if="(withDescription && $root.me.personParams.checkVersion === 'N')" class="wrapper">
                            <div class="inner-block">
                                <span ng-if="$root.currentLang === 'en'" class="notice-text" ng-if="release_number">
                                    {{$root.releaseTitles[2]}}</span>
                                <span ng-if="$root.currentLang === 'ru'" class="notice-text" ng-if="release_number">
                                    {{$root.releaseTitles[0]}}</span>
                                <span ng-if="$root.currentLang === 'ua'" class="notice-text" ng-if="release_number">
                                    {{$root.releaseTitles[1]}}
                                </span>
                                <a ng-click="reloadPage()" href="{{$root.changelog_link}}" target="_blank" class="read-and-refresh-page-btn" translate="Read details"></a>
                            </div>
                      </div>`,
                link: function (scope) {
                    scope.outdatedVersion = false;

                    scope.reloadPage = function () {
                        scope.onSetCheckVersionParam();
                        scope.outdatedVersion = false;
                    };

                    scope.onSetCheckVersionParam = function () {
                        Person.onChangeUserParam({
                            userId: $rootScope.me.userId,
                            name: 'checkVersion',
                            value: 'Y',
                        })
                            .then(() => {
                                $rootScope.me.personParams.checkVersion = 'Y';
                            })
                            .catch((err) => notificationService.error(err.message))
                            .finally(() => scope.$$phase || scope.$apply());
                    };

                    getVersion(setInitVersion);

                    setInterval(function () {
                        getVersion(checkVersionsDiff);
                    }, 60000);

                    setTimeout(() => {
                        getVersion();
                    }, 1500);

                    function setInitVersion(version) {
                        scope.currentFrontVersion = version;
                    }

                    function checkVersionsDiff(version) {
                        if (!scope.currentFrontVersion && version) scope.currentFrontVersion = version;

                        scope.outdatedVersion = version > scope.currentFrontVersion;
                        $rootScope.isOutdatedVersion = scope.outdatedVersion;

                        $rootScope.$$phase || $rootScope.$apply();
                    }

                    function getVersion(successCallback) {
                        const getVersion = $http.get('js/Version.json');
                        Promise.all([getVersion])
                            .then((resp) => {
                                if (resp[0] && resp[0].status === 200 && resp[0].data) {
                                    $rootScope.allReleaseLinks = [
                                        resp[0].data.changelog_link_ru,
                                        resp[0].data.changelog_link_ua,
                                        resp[0].data.changelog_link_en,
                                    ];
                                    $rootScope.releaseTitles = [
                                        resp[0].data.title_ru,
                                        resp[0].data.title_ua,
                                        resp[0].data.title_en,
                                    ];
                                    scope.release_number = resp[0].data.release_number;
                                    scope.withDescription = resp[0].data.withDescription;
                                    $rootScope.changelog_link =
                                        $rootScope.currentLang === 'ru'
                                            ? resp[0].data.changelog_link_ru
                                            : $rootScope.currentLang === 'ua'
                                            ? resp[0].data.changelog_link_ua
                                            : resp[0].data.changelog_link_en;
                                    successCallback && successCallback(resp[0].data.version);
                                }
                            })
                            .catch((err) => {
                                console.error('Upd version error:', err);
                            });
                    }
                },
            };
        },
    ]);
})();
