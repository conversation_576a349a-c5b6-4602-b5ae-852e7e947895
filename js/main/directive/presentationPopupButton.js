(function () {
    directives.directive('presentationPopupButton', [
        '$rootScope',
        '$uibModal',
        'notificationData',
        function ($rootScope, $uibModal, notificationData) {
            return {
                restrict: 'E',
                scope: {
                    presentationId: '@',
                },
                transclude: true,
                link: function (scope) {
                    scope.showButton = false; // Hardcoded for now (this new feature is not yet in work)
                    scope.presentationPopupData = notificationData['presentationPopup'][scope.presentationId];

                    scope.openPresentationPopup = function () {
                        $rootScope.presentationPopup = $uibModal.open({
                            animation: true,
                            scope: scope,
                            templateUrl: '../partials/modal/presentation-pop-up.html',
                            controller: 'presentationPopupController',
                            windowClass: 'presentation-pop-up',
                        });
                    };
                },
                template: `<div style="position:relative;">
                                <div ng-if="showButton" ng-click="openPresentationPopup()" class="presentation-pop-up-button">
                                    <span>!</span>
                                </div>
                                <ng-transclude></ng-transclude>
                            </div>`,
            };
        },
    ]);
})();
