(function () {
    directives.directive('tagCustomSelectWithAutocomplete', [
        '$window',
        '$filter',
        '$timeout',
        '$rootScope',
        function ($window, $filter, $timeout, $rootScope) {
            return {
                restrict: 'E',
                scope: {
                    options: '=options',
                    optionsId: '=optionsId',
                    model: '=?model',
                    modelId: '=?modelId',
                    path: '=path',
                    placeholder: '=placeholder',
                    disabled: '=disabled',
                    method: '=method',
                    removeMethod: '=removeMethod',
                    addMethod: '=addMethod',
                    trackIndex: '=trackIndex',
                    autocomplete: '=?autocomplete',
                    hidePlaceholderFromOptions: '=?hidePlaceholderFromOptions',
                    clearModelAfterClick: '=',
                    notSetModel: '=',
                    customTranslate: '=',
                    noTranslate: '=',
                    customSelectOpt: '=',
                    indexToMethod: '=',
                    resetModel: '=',
                    modelLang: '=?modelLang',
                    searchByEnter: '=?searchByEnter',
                    scorecards: '=',
                },
                link: function (scope, element, attrs) {
                    const inputField = document.querySelector('.select-label.custom-new');
                    let modelFromOption = null;

                    if (scope.customSelectOpt && scope.model) {
                        scope.model = scope.model[0].toUpperCase() + scope.model.slice(1);
                    }

                    scope.safari = $rootScope.getBrowser() === 'safari';

                    scope.getPropertyValue = function (obj = {}, path = '') {
                        if (scope.customSelectOpt && obj) {
                            return obj[path][0].toUpperCase() + obj[path].slice(1);
                        }
                        if (!path) return obj;
                        if (!obj) return null;

                        let prevProp = null;

                        path.split('.').forEach((prop) => {
                            prevProp = prevProp ? prevProp[prop] : obj[prop];
                        });

                        return prevProp || prevProp === false ? prevProp : obj;
                    };

                    scope.setPropertyValue = function (obj = {}, value) {
                        const path = scope.path.split('.');
                        for (let i = 0; i < path.length - 1; i++) {
                            let prop = path[i];
                            if (prop in o) {
                                obj = obj[prop];
                            } else {
                                obj[prop] = {};
                                obj = obj[prop];
                            }
                        }

                        obj[path[path.length - 1]] = value;

                        return obj;
                    };

                    for (let i in scope.options) {
                        if (angular.equals(scope.options[i], scope.model)) {
                            scope.selectedOpt = scope.options[i];
                        }
                    }

                    scope.changeInput = function () {
                        return new Promise((resolve, reject) => {
                            if (!scope.disabled) {
                                if (scope.scorecards) {
                                    scope.filterOptions = scope.options;
                                }
                                if (!scope.model || scope.model.length === 0) scope.filterOptions = scope.options;
                                else {
                                    if (!scope.scorecards) {
                                        scope.filterOptions = scope.options;
                                    }
                                }
                            }
                            resolve();
                        });
                    };

                    if (scope.autocomplete)
                        $timeout(() => {
                            scope.changeInput().finally(() => {
                                if (scope.model) scope.setModelForAutocomplete();
                            });
                        }, 0);

                    scope.setModelForAutocomplete = function () {
                        if (!scope.scorecards) {
                            if (scope.model && scope.searchByEnter) {
                                if (!modelFromOption) open();

                                inputField.addEventListener('keypress', callMethodByEnter);
                            } else {
                                if (scope.filterOptions.length > 0 && !scope.customSelectOpt && !scope.notSetModel)
                                    scope.model = scope.filterOptions.map((el) => el[scope.path])[0];
                            }
                        }
                    };
                    // Select an option
                    scope.selectOpt = function (opt, index) {
                        event.stopPropagation();
                        modelFromOption = true;

                        if (scope.scorecards) {
                            $rootScope.dropdownScoreCardId = opt.scoreCardId;
                            scope.inputSearch = '';
                        }

                        if (scope.notSetModel) {
                            scope.method(opt);
                            optionsDom.removeClass('active');
                            backdrop.removeClass('active');
                            return;
                        }
                        if (scope.customSelectOpt) {
                            scope.model = opt[scope.path];
                            scope.model = scope.model[0].toUpperCase() + scope.model.slice(1);
                            scope.method(opt, scope.indexToMethod);
                            optionsDom.removeClass('active');
                            backdrop.removeClass('active');
                            return;
                        }
                        if (scope.hasOwnProperty('model')) {
                            if (scope.hasOwnProperty('clearModelAfterClick') && scope.clearModelAfterClick) {
                                scope.model = '';
                            } else {
                                scope.model = opt[scope.path];
                            }
                        }
                        if (scope.hasOwnProperty('modelId') && scope.hasOwnProperty('optionsId'))
                            scope.modelId = scope.optionsId[opt];

                        if (scope.method && scope.trackIndex) {
                            scope.method(index);
                        } else if (scope.method) scope.method(opt);

                        optionsDom.removeClass('active');
                        backdrop.removeClass('active');
                    };

                    scope.removeOpt = function (opt, index) {
                        if (scope.removeMethod && scope.trackIndex) {
                            scope.removeMethod(index);
                        } else if (scope.removeMethod) scope.removeMethod(opt);
                    };

                    scope.addOpt = function () {
                        scope.addMethod();
                    };

                    scope.setPlaceholder = function () {
                        if (scope.hasOwnProperty('model')) scope.model = null;
                        close();
                    };
                    scope.onResetModel = function (event) {
                        event.stopPropagation();
                        scope.model = null;
                        scope.modelLang = null;
                        scope.method(null);
                    };

                    scope.$watch(
                        'model',
                        function (newVal) {
                            scope.changeInput().then(() => {
                                for (let i in scope.filterOptions) {
                                    if (angular.equals(scope.filterOptions[i], newVal))
                                        scope.selectedOpt = scope.options[i];
                                }
                                if (scope.model) scope.setModelForAutocomplete();
                                if (!scope.model && scope.resetModel) $rootScope.$emit('LangsChanged');
                            });
                        },
                        true,
                    );

                    scope.$watch(
                        'options',
                        () => {
                            if (scope.scorecards && scope.options) {
                                scope.options.map((item) => {
                                    if (item.name === 'Default' || item.name === 'Old Default') {
                                        item.name = $filter('translate')(item.name);
                                    }
                                });
                                scope.listForActive = [scope.options];
                            }
                            scope.changeInput();
                        },
                        true,
                    );

                    var labelDom = element.find('.select-label'),
                        optionsDom = element.find('.select-ops'),
                        backdrop = element.find('.select-backdrop');

                    labelDom.on('click', open);
                    backdrop.on('click', close);

                    if (scope.placeholder && !scope.model) scope.setPlaceholder();

                    function open() {
                        if (!scope.disabled) {
                            optionsDom.addClass('active');
                            backdrop.addClass('active');
                        }

                        if (document.querySelector('#focus-input')) {
                            document.querySelector('#focus-input').focus();
                        }
                    }

                    function close() {
                        optionsDom.removeClass('active');
                        backdrop.removeClass('active');
                        labelDom.removeClass('disabled');

                        if (document.querySelector('#mainTagInput')) {
                            document.querySelector('#mainTagInput').blur();
                        }
                    }

                    function callMethodByEnter(e) {
                        if (e.key === 'Enter') {
                            scope.method(scope.model);

                            modelFromOption = false;

                            inputField.removeEventListener('keypress', callMethodByEnter);
                            close();
                        }
                    }

                    $(element).bind('open', open);
                    $(element).bind('close', close);

                    scope.$watch('disabled', () => {
                        if (scope.disabled) labelDom.addClass('disabled');
                        else labelDom.removeClass('disabled');
                    });
                },
                template: `
                    <form autocomplete="off" class="select-label custom-new" tabindex="0" style="max-height: 34px;">
                        <span ng-show='!scorecards' style="position: relative" class="select-label-text">
                            <input type="text" ng-show="!placeholder" ng-model="model" ng-model-options="{debounce: 300}" style="border: none; width: 100%;"  ng-disabled="disabled"/>
                            <input type="text" ng-change='$root.tagChange(model);' ng-show="placeholder" id="mainTagInput" ng-model="model" placeholder="{{placeholder | translate}}" ng-model-options="{debounce: 300}" style="border: none; width: 95%;background: transparent;text-overflow: ellipsis;overflow: hidden;" ng-disabled="disabled" />
                            <img ng-if="resetModel && model" style="position: absolute;top: 10px;right: 10px;" ng-click="onResetModel($event);$event.stopPropagation();" src="dist/css/select2.svg" alt="">
                        </span>
                        <span ng-show='scorecards' ng-class="{'scorecards-dropdown': scorecards}" style="position: relative" class="select-label-text">
                            <input type="text" ng-show="!placeholder" ng-model="model" ng-model-options="{debounce: 300}" style="border: none; width: 100%;"  disabled/>
                            <input ng-style="safari ? {'color': '#000000'} : null" type="text" ng-show="placeholder" id="mainTagInput" ng-model="model" placeholder="{{placeholder | translate}}" ng-model-options="{debounce: 300}" style="border: none; width: 95%;background: transparent;text-overflow: ellipsis;overflow: hidden;" disabled />
                            <img ng-if="resetModel && model" style="position: absolute;top: 10px;right: 10px;" ng-click="onResetModel($event);$event.stopPropagation();" src="dist/css/select2.svg" alt="">
                        </span>
                        <span class="select-caret">
                           <span></span>
                        </span>
                    </form>
                    <div class="select-backdrop custom-new"></div>
                    <div class="select-ops custom-new" id='tags-select-ops'>
                        <div ng-if="placeholder && !hidePlaceholderFromOptions" ng-click="setPlaceholder()" translate="{{placeholder}}"></div>
                        <div ng-if="filterOptions.length && !scorecards" ng-repeat="o in filterOptions track by $index" ng-click="selectOpt(o);">
                            <div ng-if="!noTranslate" ng-click="selectOpt(o, $index);" class="list-item-ops" ng-class="{'italic': o.country && o.country === 'Remote'}">{{getPropertyValue(o, path) | translate: o.translateValues}}</div>
                            <div ng-if="noTranslate" ng-click="selectOpt(o, $index);" class="list-item-ops" ng-class="{'italic': o.country && o.country === 'Remote'}">{{getPropertyValue(o, path)}}</div>
                            <img ng-if="removeMethod" class="trash" src="/images/sprite/icons/delete-forever.svg" ng-click="removeOpt(o, $index)" alt />
                        </div>
                        <div ng-show='scorecards' class="fixed">
                             <input id="focus-input" style="color:#0e0e0e;border: 1px solid #aaa;border-radius: 0;outline: 0;background: none;box-shadow: none;width: 99%;height: auto !important;min-height: 26px;margin-left:2px" autocomplete="off" type="text" ng-model="inputSearch" ng-change="searchScorecards()" />
                        </div>
                        <div ng-if="filterOptions.length && scorecards && listForActive.length" ng-repeat="o in filterOptions | filter:inputSearch track by $index" ng-click="selectOpt(o);">
                            <div ng-click="selectOpt(o, $index);" class="list-item-ops" ng-class="{'italic': o.country && o.country === 'Remote'}">{{getPropertyValue(o, path)}}</div>
                            <img ng-if="removeMethod" class="trash" src="/images/sprite/icons/delete-forever.svg" ng-click="removeOpt(o, $index)" alt />
                        </div>
                        <div ng-if="!listForActive.length && scorecards">{{'No matches found.'|translate}}</div>
                        <div ng-if="!filterOptions.length">{{'No matches found.'|translate}}</div>
                        <div ng-if="addMethod" class="list-ops" ng-click="addOpt()">
                            <div class="list-item-ops"><img src="/images/sprite/custom-fields/add.svg" alt/>{{"Add Card" | translate}}</div>
                        </div>
                    </div>
                    `,
            };
        },
    ]);
})();
