var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives
        .directive('headHunterCityInput', [
            '$translate',
            'hhVacancyPostingService',
            function cityInput($translate, hhVacancyPostingService) {
                return {
                    link: link,
                    restrict: 'A',
                    scope: {
                        cityInputId: '<?',
                        cities: '=',
                        city: '=?',
                        otherCountries: '=?',
                    },
                    require: 'ngModel',
                };

                function link(scope, element, attrs, ngModel) {
                    scope.$watch('cities', (newVal) => {
                        const citiesObj = hhVacancyPostingService.citiesToSelect2Format(scope.cities);
                        const $element = $(element[0]);
                        scope.cityInputId = scope.cityInputId || 1;
                        const containerId = scope.cityInputId + 'hh_city_container';
                        $element.attr('id', containerId);

                        initSelect2City(citiesObj);
                        function initSelect2City(cities) {
                            $element
                                .select2({
                                    placeholder: scope.otherCountries
                                        ? $translate.instant('Choose the country')
                                        : $translate.instant('Choose the city'),
                                    minimumInputLength: 0,
                                    allowClear: true,
                                    dropdownCssClass: 'hh-cities',
                                    formatNoMatches: function () {
                                        return `<div class='select2-result-label' style='cursor: default'><span class='select2-match'></span>${$translate.instant(
                                            'City not found',
                                        )}</div>`;
                                    },
                                    data: cities,
                                    query: customMatcher,
                                    results: function (data, page) {
                                        return data.id;
                                    },
                                })
                                .off('change')
                                .off('select2-open')
                                .on('change', function (e) {
                                    if (e.added) {
                                        ngModel.$setViewValue(e.added.id);
                                    } else {
                                        ngModel.$setViewValue(null);
                                    }
                                })
                                .off('select2-open', function () {});
                            if (typeof scope.city !== 'object') setCityAutocompleter(scope.city);

                            function setCityAutocompleter(cityId) {
                                const cityObj = hhVacancyPostingService.findArea(cityId, scope.cities);
                                if (cityObj)
                                    $element.select2('data', {
                                        id: cityObj.id,
                                        text: cityObj.name,
                                    });
                            }
                        }

                        function customMatcher(q) {
                            if (this.data) {
                                const pageLength = 15;
                                let results = this.data.filter(function (e) {
                                    if (typeof e.text === 'string') {
                                        q.term = q.term ? q.term : '';
                                        return e.text.toLowerCase().indexOf(q.term.toLowerCase()) === 0;
                                    } else return false;
                                });

                                // Get a page sized slice of data from the results of filtering the data set.
                                const paged = results.slice((q.page - 1) * pageLength, q.page * pageLength);

                                q.callback({
                                    results: paged,
                                    more: results.length >= q.page * pageLength,
                                });
                            }
                        }
                    });
                }
            },
        ])
        .directive('headHunterCountryInput', [
            '$translate',
            'hhVacancyPostingService',
            function countryInput($translate, hhVacancyPostingService) {
                return {
                    link: link,
                    restrict: 'A',
                    scope: {
                        countryInputId: '<?',
                        countries: '=',
                        country: '=?',
                        method: '=?',
                    },
                    require: 'ngModel',
                };

                function link(scope, element, attrs, ngModel) {
                    scope.$watch('countries', (newVal) => {
                        const countriesObj = hhVacancyPostingService.countriesToSelect2Format(scope.countries);
                        const $element = $(element[0]);
                        scope.countryInputId = scope.countryInputId || 1;
                        const containerId = scope.countryInputId + 'hh_country_container';
                        $element.attr('id', containerId);

                        initSelect2Country(countriesObj);
                        function initSelect2Country(countries) {
                            $element
                                .select2({
                                    placeholder: $translate.instant('Choose the country'),
                                    minimumInputLength: 0,
                                    allowClear: true,
                                    dropdownCssClass: 'hh-country',
                                    formatNoMatches: function () {
                                        return `<div class='select2-result-label' style='cursor: default'><span class='select2-match'></span>${$translate.instant(
                                            'Country not found',
                                        )}</div>`;
                                    },
                                    data: countries,
                                    query: customMatcher,
                                    results: function (data, page) {
                                        return data.id;
                                    },
                                })
                                .off('change')
                                .off('select2-open')
                                .on('change', function (e) {
                                    ngModel.$setViewValue(e.added.id);
                                    $('#s2id_1hh_city_container').select2('data', null);
                                    if (scope.method) scope.method();
                                })
                                .off('select2-open', function () {});
                            if (typeof scope.country !== 'object') setCityAutocompleter(scope.country);

                            function setCityAutocompleter(countryId) {
                                const countryObj = hhVacancyPostingService.findArea(countryId, scope.countries);
                                if (countryObj)
                                    $element.select2('data', {
                                        id: countryObj.id,
                                        text: countryObj.name,
                                    });
                            }
                        }

                        function customMatcher(q) {
                            if (this.data) {
                                const pageLength = 15;
                                let results = this.data.filter(function (e) {
                                    if (typeof e.text === 'string') {
                                        q.term = q.term ? q.term : '';
                                        return e.text.toLowerCase().indexOf(q.term.toLowerCase()) === 0;
                                    } else return false;
                                });

                                // Get a page sized slice of data from the results of filtering the data set.
                                const paged = results.slice((q.page - 1) * pageLength, q.page * pageLength);

                                q.callback({
                                    results: paged,
                                    more: results.length >= q.page * pageLength,
                                });
                            }
                        }
                    });
                }
            },
        ]);
})();
