var directives = directives || angular.module('RecruitingApp.directives', []);

(function () {
    directives.directive('showHide', ['$rootScope', showHide]);

    function showHide($rootScope) {
        return {
            restrict: 'A',
            scope: {
                expectedHeight: '=',
                criticalHeight: '=',
                show: '=',
                hide: '=',
            },
            link: function (scope, element, attr) {
                setTimeout(() => {
                    if (element[0].offsetHeight > scope.expectedHeight) {
                        scope.show = true;
                    } else {
                        scope.hide = false;
                        scope.show = false;
                    }
                }, 100);
                $rootScope.updateShowing = function () {
                    setTimeout(() => {
                        if (element[0].scrollHeight < scope.criticalHeight) {
                            scope.hide = false;
                            scope.show = false;
                        }
                        $rootScope.$$phase || scope.$apply();
                    }, 100);
                };
            },
        };
    }
})();
