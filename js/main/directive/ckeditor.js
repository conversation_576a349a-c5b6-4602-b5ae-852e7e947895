var app = angular.module('ckeditor', []).directive('ckeditor', ckeditorDirective);

function ckeditorDirective($rootScope, Service, $translate) {
    return {
        require: '?ngModel',
        scope: {
            options: '=',
            cf: '=',
            withoutMaxHeight: '=',
        },
        link: function (scope, elm, attr, ngModel) {
            function initCKEDITOR() {
                let systemLanguage = $rootScope.currentLang;
                if (systemLanguage === 'ua') {
                    systemLanguage = 'uk';
                }

                let defaultConfig = {
                    enterMode: Number(2),
                    language: systemLanguage,
                    removePlugins: 'elementspath, exportpdf',
                    resize_enabled: false,

                    // prevent CKeditor to encode
                    basicEntities: false,
                    entities: false,
                    entities_greek: false,
                    entities_latin: false,
                    htmlEncodeOutput: false,
                    entities_processNumerical: false,
                    // ------------------------------

                    disableNativeSpellChecker: false, // activate browser spellChacker
                    allowedContent: true,
                    toolbarGroups: [
                        {
                            name: 'document',
                            groups: ['mode', 'document', 'doctools'],
                        },
                        { name: 'clipboard', groups: ['clipboard', 'undo'] },
                        {
                            name: 'editing',
                            groups: ['find', 'selection', 'spellchecker', 'editing'],
                        },
                        { name: 'forms', groups: ['forms'] },
                        { name: 'basicstyles', groups: ['basicstyles', 'cleanup'] },
                        { name: 'colors', groups: ['colors'] },
                        {
                            name: 'paragraph',
                            groups: ['list', 'indent', 'blocks', 'align', 'bidi', 'paragraph'],
                        },
                        { name: 'links', groups: ['links'] },
                        { name: 'insert', groups: ['insert'] },
                        { name: 'styles', groups: ['styles'] },
                        { name: 'tools', groups: ['tools'] },
                        { name: 'others', groups: ['others'] },
                        { name: 'about', groups: ['about'] },
                    ],
                    removeButtons:
                        'Source,Save,NewPage,Preview,Print,Templates,Cut,Copy,Paste,PasteText,PasteFromWord,Find,Replace,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Subscript,Superscript,RemoveFormat,CopyFormatting,Outdent,Indent,Blockquote,CreateDiv,JustifyLeft,JustifyCenter,JustifyRight,JustifyBlock,BidiLtr,BidiRtl,Language,Anchor,Unlink,Link,Image,Flash,Table,HorizontalRule,Smiley,SpecialChar,PageBreak,Iframe,Styles,Format,BGColor,ShowBlocks,Maximize,About',
                };

                if (scope.options) {
                    defaultConfig = {
                        ...defaultConfig,
                        ...scope.options,
                    };
                }

                CKEDITOR.addCss('body { overflow: hidden; }');
                CKEDITOR.addCss('.cke_contents { height: 140px !important; }');
                CKEDITOR.addCss('.cke_reset {width:600px !important; }');
                CKEDITOR.addCss('.cke_editable { margin-top: 16px;margin-bottom: 16px;margin-right: 20px!important; }');
                CKEDITOR.addCss('.cke_editable { font-size: 14px;}');
                CKEDITOR.addCss(
                    '.cke_editable::-webkit-scrollbar { width: 8px!important;height:5px!important;max-height: 5px; }',
                );
                CKEDITOR.addCss('.cke_editable::-webkit-scrollbar-track { background-color: transparent; }');
                CKEDITOR.addCss(
                    '.cke_editable::-webkit-scrollbar-thumb { background-color: #c7c7c7!important;border-radius: 4px;}',
                );
                CKEDITOR.addCss('.cke_editable::-webkit-scrollbar-thumb:hover { background-color: #909090; }');

                if (scope.withoutMaxHeight) {
                    scope.ck = CKEDITOR.replace(elm[0], {
                        ...defaultConfig,
                        extraPlugins: 'autogrow',
                    });
                }
                if (scope.cf) {
                    scope.ck = CKEDITOR.replace(elm[0], {
                        ...defaultConfig,
                        extraPlugins: 'autogrow',
                        autoGrow_minHeight: 53,
                        autoGrow_maxHeight: 133,
                    });
                } else if (!scope.withoutMaxHeight) {
                    scope.ck = CKEDITOR.replace(elm[0], defaultConfig);
                }

                scope.ck.on('key', function (evt) {
                    const selection = scope.ck.getSelection();
                    const range = selection.getRanges()[0];
                    const element = range.startContainer.getAscendant('a', true);
                    const key = evt.data.keyCode;
                    const domEvent = evt.data.domEvent;

                    const LEFT = 37;
                    const RIGHT = 39;
                    const SPACE = 32;
                    const BACKSPACE = 8;
                    const DELETE = 46;
                    const CTRL_BACKSPACE = domEvent && domEvent.getKeystroke() === CKEDITOR.CTRL + BACKSPACE;
                    const CTRL_DELETE = domEvent && domEvent.getKeystroke() === CKEDITOR.CTRL + DELETE;
                    const isCtrl = CTRL_BACKSPACE || CTRL_DELETE;

                    if (element) {
                        if (key === DELETE) {
                            element.removeStyle('background-color');
                        }
                    }

                    if (element && element.getText().startsWith('@')) {
                        if (key === BACKSPACE || key === DELETE || isCtrl) {
                            element.remove();
                            evt.cancel();
                            return;
                        }

                        if (key === SPACE) {
                            const textNode = range.startContainer;

                            if (textNode.type === CKEDITOR.NODE_TEXT) {
                                const offset = range.startOffset;
                                const isAtStart = offset === 0;
                                const isAtEnd = offset === textNode.getLength();

                                if (isAtStart || isAtEnd) return;

                                evt.cancel();
                                return;
                            }
                        }

                        if (key === LEFT || key === RIGHT) {
                            const newRange = new CKEDITOR.dom.range(scope.ck.document);

                            if (key === RIGHT) {
                                newRange.moveToPosition(element, CKEDITOR.POSITION_AFTER_END);
                            } else if (key === LEFT) {
                                newRange.moveToPosition(element, CKEDITOR.POSITION_BEFORE_START);
                            }

                            newRange.collapse(true);
                            scope.ck.getSelection().selectRanges([newRange]);
                            evt.cancel();
                            return;
                        }

                        evt.cancel();
                    }
                });

                scope.ck.on('contentDom', function () {
                    const editor = this;

                    editor.editable().attachListener(editor.editable(), 'click', function (event) {
                        const target = event.data.getTarget();
                        if (target.is('a') && target.getText().startsWith('@')) {
                            const selection = editor.getSelection();
                            const range = editor.createRange();
                            range.selectNodeContents(target);
                            selection.selectRanges([range]);
                        }
                    });
                });

                if (!ngModel) return;

                function updateModel() {
                    const data = scope.ck.getData();
                    const sanitizedData = Service.sanitizeStringFromXSS(data);

                    ngModel.$setViewValue(sanitizedData);
                }

                scope.ck.on('instanceReady', function (evt) {
                    scope.ck.setData(ngModel.$viewValue);
                });
                scope.ck.on('change', updateModel);
                scope.ck.on('key', updateModel);
                scope.ck.on('dataReady', updateModel);
                $rootScope.$$phase || $rootScope.$apply();
                ngModel.$render = function (value) {
                    scope.ck.setData(ngModel.$viewValue);
                };
            }
            initCKEDITOR();

            scope.$watch('$root.currentLang', (current, old) => {
                if (current !== old) {
                    if (scope.ck.name === 'ckEditorCandidateMain')
                        scope.options.editorplaceholder = $translate.instant('mention placeholder');
                    scope.ck.destroy();
                    initCKEDITOR();
                }
            });
        },
    };
    $rootScope.$$phase || $rootScope.$apply();
}
