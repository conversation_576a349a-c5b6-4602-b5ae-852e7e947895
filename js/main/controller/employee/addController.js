function EmployeeAddControllerFunc(
    $rootScope,
    $http,
    $scope,
    $translate,
    FileInit,
    $location,
    Service,
    Candidate,
    notificationService,
    $filter,
    $localStorage,
    $cookies,
    $window,
    serverAddress,
    Employee,
    $uibModal,
    RegionInputService,
) {
    initController();
    $scope.validateEmployeeName = function (name, type) {
        if (!name) return;
        $scope.pageObject.employee.candidateId[type] = Employee.validateName(name);
    };
    $scope.searchPosition = function searchPosition(term) {
        $scope.pageObject.positionListAfterSearch = $filter('filter')(
            $scope.pageObject.positionList,
            term.toLowerCase(),
        );
    };
    $scope.educationLevels = Candidate.getEducationLevel();
    $scope.callbackAddPhoto = function (photo) {
        $rootScope.loading = false;
        $scope.pageObject.employee.candidateId.photo = photo;
        $scope.photoLink = photo ? $scope.serverAddress + '/getapp?id=' + photo + '&d=true' : null;
        $rootScope.photoUrl = '';
        $rootScope.closeModal();
    };
    $scope.addPhotoByReference = function (photoUrl) {
        $rootScope.loading = true;
        FileInit.addPhotoByReference(photoUrl, $scope.callbackAddPhoto);
    };
    $scope.progressUpdate = function () {};
    $scope.updateErrorForPosition = function () {
        const positionElemenet = document.querySelector('.field.position .select2-choice');

        if ($scope.pageObject.employee.position != undefined && $scope.pageObject.employee.position.length > 0) {
            $scope.candidateForm.position.$pristine = true;
            $scope.candidateForm.position.$invalid = false;
            positionElemenet.style.border = '1px solid #ccc';
        } else {
            $scope.candidateForm.position.$pristine = false;
            $scope.candidateForm.position.$invalid = true;
            positionElemenet.style.border = '1px solid red';
        }
    };
    $scope.deleteRegion2ToRelocate = function (index) {
        $scope.regionToRelocate.splice(index, 1);
    };
    $scope.isAddPage = $location.$$url.includes('employee/add');
    $scope.callbackFile = function (resp, names) {
        notificationService.success($filter('translate')('history_info.added_file'));
        $scope.pageObject.variables.fileForSave.push({
            attId: resp,
            fileName: names,
        });
    };

    $scope.removeFile = function (id) {
        angular.forEach($scope.pageObject.variables.fileForSave, function (val, ind) {
            if (val.attId === id) {
                $scope.pageObject.variables.fileForSave.splice(ind, 1);
            }
        });
        $scope.progressUpdate();
    };
    $scope.removeLink = function (id) {
        angular.forEach($scope.linksForSave, function (val, ind) {
            if (val.fileName === id) {
                $scope.linksForSave.splice(ind, 1);
            }
        });
        $scope.progressUpdate();
    };
    $scope.cancel = function () {
        $location.path('/candidates/');
    };
    $scope.addPhoto = function () {
        $('#photoFile').click();
    };
    $scope.callbackErr = function (err) {
        notificationService.error(err);
    };
    $scope.removePhoto = function () {
        $scope.pageObject.employee.candidateId.photo = '';
        $scope.photoLink = undefined;
        $scope.progressUpdate();
    };

    $scope.employmentTypeModel = [];

    $scope.checkSalartInput = function (e) {
        if (e.keyCode === 69 || e.keyCode === 187) {
            e.preventDefault();
        }
    };

    $scope.saveCandidate = function () {
        $scope.errorId = false;
        $scope.checkAllDates();
        if (
            $scope.candidateForm.firstName.$valid &&
            $scope.candidateForm.lastName.$valid &&
            $scope.candidateForm.position.$valid &&
            $scope.candidateForm.workingDate.$valid &&
            !$scope.startSalaryDateError &&
            !$scope.startPositionDateError &&
            !$scope.startDepartmentDateError &&
            !$scope.startWorkingDateError &&
            !$scope.emailError
        ) {
            getFieldWithJQuery(
                $scope.pageObject.employee.candidateId,
                $scope.region,
                $scope.datepickerOfBirth,
                RegionInputService,
            );
            getFullName($scope.pageObject.employee.candidateId);
            $scope.pageObject.employee.currency = returnValue($scope.pageObject.employee.currency);
            $scope.pageObject.employee.candidateId.coreSkills = $scope.pageObject.variables.coreSkills;
            $scope.pageObject.employee.candidateId.descr = $scope.pageObject.variables.coreDescr;
            $scope.pageObject.employee.candidateId.industry = $scope.pageObject.employee.candidateId.industry?.value;
            //expirence - wrong word, correct - experience
            $scope.pageObject.employee.candidateId.expirence = $scope.pageObject.employee.candidateId.expirence?.value;
            $scope.pageObject.employee.candidateId.position = $scope.pageObject.employee.position;
            $scope.pageObject.employee.candidateId.relatedRegions = $scope.regionToRelocate;
            $scope.pageObject.employee.currency =
                $scope.pageObject.employee.currency.value || $scope.pageObject.employee.currency;
            $scope.pageObject.employee.candidateId.currency = $scope.pageObject.employee.currency;
            $scope.pageObject.employee.candidateId.employmentType = $scope.employmentTypeModel.join(',');
            $scope.pageObject.employee.candidateId.photo = $scope.candidate?.photo;
            $scope.pageObject.employee.candidateId.db =
                $scope.pageObject.employee.candidateId.db === 'Invalid date'
                    ? null
                    : $scope.pageObject.employee.candidateId.db;
            $scope.pageObject.employee.candidateId.languages = $scope.addedLang;

            Service.addContactsInCandidateObject($scope);
            Employee.addEmployeeWithCandidate(
                $scope.pageObject.employee,
                function (resp) {
                    if (angular.equals(resp.status, 'ok')) {
                        $scope.pageObject.variables.saveButtonIsPressed = false;
                        notificationService.success($filter('translate')('The employee is saved'));
                        if ($scope.pageObject.variables.fileForSave.length > 0) {
                            angular.forEach($scope.pageObject.variables.fileForSave, function (valI, i) {
                                if (valI.url) {
                                    Candidate.addLink(
                                        {
                                            url: valI.url,
                                            candidateId: resp.object.candidateId.candidateId,
                                            name: valI.fileName,
                                        },
                                        function (resp) {},
                                    );
                                    if ($scope.linksForSave.length - 1 == i) {
                                        $location.path('/company/employees/' + resp.object.employeeId);
                                    }
                                } else {
                                    Candidate.addFile(
                                        {
                                            attId: valI.attId,
                                            candidateId: resp.object.candidateId.candidateId,
                                            fileName: valI.fileName,
                                        },
                                        function (resp) {},
                                    );
                                }
                                if ($scope.pageObject.variables.fileForSave.length - 1 == i) {
                                    $location.path('/company/employees/' + resp.object.employeeId);
                                }
                            });
                        } else {
                            $location.path('/company/employees/' + resp.object.employeeId);
                        }
                    } else {
                        $scope.pageObject.variables.saveButtonIsPressed = false;
                        $scope.pageObject.errorMessage.show = true;
                        $scope.pageObject.errorMessage.message = resp.message;
                        if (resp.code == 'existsEmployeeId') {
                            $scope.errorId = true;
                        }
                    }
                },
                function () {
                    $scope.pageObject.variables.saveButtonIsPressed = false;
                    $cookies.url = $location.$$url;
                    $cookies.cfauth = 'false';
                    $window.location.replace('/');
                },
            );
        } else {
            // $scope.dateChange($scope.candidateForm.workingDate.$modelValue);
            $scope.candidateForm.firstName.$pristine = false;
            $scope.candidateForm.middleName.$pristine = false;
            $('html, body').animate({ scrollTop: 0 }, 'fast');
            $scope.candidateForm.lastName.$pristine = false;
            $scope.candidateForm.salary.$pristine = false;
            $scope.updateErrorForPosition();
        }
    };
    $scope.checkDuplicatesByEmail = function () {
        Candidate.checkDuplicatesByEmail($scope);
    };
    $scope.checkDuplicatesByPhone = function () {
        Candidate.checkDuplicatesByPhone($scope);
    };

    $scope.resetCertainDate = function (dateName) {
        $scope[dateName] = null;
        $scope.checkDateTime(null, dateName);
    };

    $scope.checkAllDates = function () {
        $scope.startWorkingDateError = typeof $scope.startWorkingDate === 'string' || !$scope.startWorkingDate;
        $scope.startDepartmentDateError = typeof $scope.startDepartmentDate === 'string' || !$scope.startDepartmentDate;
        $scope.startPositionDateError = typeof $scope.startPositionDate === 'string' || !$scope.startPositionDate;
        $scope.startSalaryDateError = typeof $scope.startSalaryDate === 'string' || !$scope.startSalaryDate;
    };

    $scope.checkDateTime = function (val, name) {
        switch (name) {
            case 'datepickerOfBirth':
                if (val == null || val == 'null') {
                    $scope.pageObject.employee.candidateId.db = null;
                    $scope.minDateEmployee = null;
                } else {
                    const dateOfBirth = Date.parse(val);
                    $scope.pageObject.employee.candidateId.db = dateOfBirth;
                    $scope.minDateEmployee = val;
                    $scope.minDateEmployee.setFullYear(val.getFullYear() + 14);
                    angular.forEach($scope.startDates, function (date) {
                        if (typeof $scope[date]) {
                            let min = Date.parse($scope.minDateEmployee);
                            if (min > Date.parse($scope[date])) {
                                $scope[date + 'Error'] = true;
                            } else {
                                $scope[date + 'Error'] = false;
                            }
                        }
                    });
                    return ($scope.pageObject.employee.candidateId.db = dateOfBirth);
                }
            case 'startWorkingDate':
                if (val == null || val == 'null') {
                    $scope.pageObject.employee.dateEmployee = null;
                } else {
                    $scope.startWorkingDateError = false;
                    return ($scope.pageObject.employee.dateEmployee = Date.parse($scope.startWorkingDate));
                }
            case 'startDepartmentDate':
                if (val == null || val == 'null') {
                    $scope.pageObject.employee.dateDepartment = null;
                } else {
                    $scope.startDepartmentDateError = false;
                    return ($scope.pageObject.employee.dateDepartment = Date.parse($scope.startDepartmentDate));
                }
            case 'startPositionDate':
                if (val == null || val == 'null') {
                    $scope.pageObject.employee.datePosition = null;
                } else {
                    $scope.startPositionDateError = false;
                    return ($scope.pageObject.employee.datePosition = Date.parse($scope.startPositionDate));
                }
            case 'startSalaryDate':
                if (val == null || val == 'null') {
                    $scope.pageObject.employee.dateSalary = null;
                } else {
                    $scope.startSalaryDateError = false;
                    return ($scope.pageObject.employee.dateSalary = Date.parse($scope.startSalaryDate));
                }
        }
    };
    $scope.showAddLinkFunc = function () {
        $scope.showAddLink = true;
    };
    $scope.closeAddLinkFunc = function () {
        $scope.showAddLink = false;
        $scope.addLinkToCandidate.name = null;
        $scope.addLinkToCandidate.url = null;
        $scope.addLinkErrorShow = false;
    };
    $scope.addLinkInCandidateStart = function () {
        if ($scope.addLinkToCandidate.name && $scope.addLinkToCandidate.url) {
            $scope.pageObject.variables.fileForSave.push({
                url: $scope.addLinkToCandidate.url,
                fileName: $scope.addLinkToCandidate.name,
            });
            $scope.addLinkToCandidate.name = '';
            $scope.addLinkToCandidate.url = '';
            $scope.showAddLink = false;
            notificationService.success($filter('translate')('history_info.added_link'));
        } else {
            $scope.addLinkErrorShow = true;
            if (!$scope.addLinkToCandidate.name && !$scope.addLinkToCandidate.url) {
                notificationService.error($filter('translate')('Please enter a title and URL'));
            }

            if ($scope.addLinkToCandidate.name && !$scope.addLinkToCandidate.url) {
                notificationService.error($filter('translate')('Please enter a URL'));
            }

            if (!$scope.addLinkToCandidate.name && $scope.addLinkToCandidate.url) {
                notificationService.error($filter('translate')('Please enter a title'));
            }
        }
    };
    $scope.changeSex = function (sex) {
        $scope.pageObject.employee.candidateId.sex = sex;
    };

    function setDefaultCurrency() {
        Service.onGetDefaultCurrency({
            defaultCurrencyType: 'employee',
        }).then((resp) => {
            if (resp.status === 'ok') $scope.pageObject.employee.currency = resp.object;
        });
    }

    $scope.validationOfEmailField = function () {
        if ($scope.pageObject.contacts.email) {
            const re =
                /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            $scope.emailError = !re.test(String($scope.pageObject.contacts.email).toLowerCase());
        } else {
            $scope.emailError = false;
        }
    };

    function checkDateError() {
        if (
            typeof $scope.startSalaryDate === 'number' &&
            typeof $scope.startPositionDate === 'number' &&
            typeof $scope.startDepartmentDate === 'number' &&
            typeof $scope.startWorkingDate === 'number'
        ) {
            $scope.errorDateStartWorking = false;
        }
    }

    function returnValue(field) {
        if (field && typeof field === 'object') return field.value;
        else return field;
    }

    function setInitialState() {
        $location.hash('');
        $scope.maxDate = new Date();
        $scope.maxDate.setFullYear($scope.maxDate.getFullYear() - 12);
        $scope.maxDate.setHours(23, 59, 59, 999);
        $scope.startDates = ['startWorkingDate', 'startDepartmentDate', 'startSalaryDate', 'startPositionDate'];
        $scope.candidate = {};
        $scope.swapFields = Service.swapFields;
        $scope.addedLang = [
            {
                name: null,
                level: undefined,
            },
        ];
        $scope.pageObject = {
            employee: {
                candidateId: {
                    status: 'employed',
                    readyRelocate: false,
                    employmentType: null,
                    contacts: [],
                    sex: true,
                },
            },
            contacts: {
                skype: null,
                mphone: null,
                email: null,
                linkedin: null,
                facebook: null,
                googleplus: null,
                homepage: null,
            },
            variables: {
                positionList: [],
                positionListAfterSearch: [],
                currency: Service.currency(),
                industries: Service.getIndustries(),
                experience: Service.experience().slice(1),
                lang: Service.lang(),
                saveButtonIsPressed: false,
                fileForSave: [],
            },
            errorMessage: {
                show: false,
                message: '',
            },
        };
        $scope.addLinkToCandidate = {
            name: '',
            url: '',
        };
        $scope.linksForSave = [];
        $scope.serverAddress = serverAddress;
        $scope.googleMapOption = false;
        $scope.regionToRelocate = [];
        $scope.duplicatesByEmail = [];
        $scope.duplicatesByPhone = [];
        $scope.map = {
            center: {
                latitude: 48.379433,
                longitude: 31.165579999999977,
            },
            zoom: 6,
            options: {
                panControl: true,
                zoomControl: true,
                scaleControl: true,
                mapTypeControl: true,
                mapTypeId: 'roadmap',
            },
        };
        $scope.marker = {
            id: 1,
            title: '',
            coords: {
                latitude: null,
                longitude: null,
            },
        };
        $('.ui.dropdown').dropdown();
        $('.select2-lang-employee')
            .select2({
                tags: $scope.pageObject.variables.lang,
                tokenSeparators: [',', ' '],
            })
            .on('change', function (e) {
                $scope.progressUpdate();
            });
        $('#pac-input').blur(function () {
            if (!$(this).val()) {
                $scope.progressUpdate();
            }
        });
        $scope.employmentType = Service.employmentType();
        $scope.status = Candidate.getStatus();
        setDefaultCurrency();
    }
    function initialRest() {
        Employee.getPositionList(function (resp) {
            $scope.pageObject.positionList = resp.objects;
            $scope.pageObject.positionListAfterSearch = resp.objects;
        });
        FileInit.initCandFileOption($scope, '', '', false);
        initEmploymentType(Service.employmentTypeTwo());
        Candidate.setPhoto($scope);
        Employee.getDepartmentsList(function (resp) {
            $scope.departmentsList = resp.objects;
            angular.forEach($scope.departmentsList, function (val) {
                if (val.deep == 1) {
                    val.name = '-- ' + val.name;
                }
                if (val.deep == 2) {
                    val.name = '---- ' + val.name;
                }
                if (val.deep == 3) {
                    val.name = '------ ' + val.name;
                }
                if (val.deep == 4) {
                    val.name = '-------- ' + val.name;
                }
                if (val.deep == 5) {
                    val.name = '---------- ' + val.name;
                }
                if (val.deep == 6) {
                    val.name = '------------ ' + val.name;
                }
                if (val.deep == 7) {
                    val.name = '-------------- ' + val.name;
                }
                if (val.deep == 8) {
                    val.name = '---------------- ' + val.name;
                }
            });
        });
    }

    function initialListeners() {}

    function initController() {
        setInitialState();
        initialRest();
        initialListeners();
    }

    function initEmploymentType(employmentTArr) {
        $('.select2-employmentType').select2({
            tags: employmentTArr,
            tokenSeparators: [',', ' '],
            initSelection: function (element, callback) {
                var data = { id: element.val(), text: element.val() };
                callback(data);
            },
        });
    }
}

controller.controller('EmployeeAddController', [
    '$rootScope',
    '$http',
    '$scope',
    '$translate',
    'FileInit',
    '$location',
    'Service',
    'Candidate',
    'notificationService',
    '$filter',
    '$localStorage',
    '$cookies',
    '$window',
    'serverAddress',
    'Employee',
    '$uibModal',
    'RegionInputService',
    EmployeeAddControllerFunc,
]);

function getFieldWithJQuery(candidateObject, region2, datepickerOfBirth, RegionInputService) {
    const regionData = RegionInputService.getInstance('1', 'city').getValue();
    if (regionData) {
        candidateObject.region = regionData;
    } else {
        if ($('#pac-input').val().length > 0 && candidateObject.region) {
            candidateObject.region.fromGoogle = true;
        }
    }

    const lang = $('.select2-lang-employee').select2('val'),
        emplType = $('.select2-employmentType').select2('val'),
        db = new Date(datepickerOfBirth);

    candidateObject.languages = lang && lang.length > 0 ? lang.toString() : null;
    candidateObject.employmentType = emplType && emplType.length > 0 ? emplType.toString() : null;
    candidateObject.db = db || null;
}

function getFullName(candidateObject) {
    if (candidateObject.middleName) {
        candidateObject.fullName = `${candidateObject.firstName} ${candidateObject.lastName} ${candidateObject.middleName}`;
    } else {
        candidateObject.fullName = `${candidateObject.firstName} ${candidateObject.lastName}`;
    }
}
