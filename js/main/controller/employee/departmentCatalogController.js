controller.controller('DepartmentCatalogController', [
    '$scope',
    '$rootScope',
    '$stateParams',
    'Service',
    'Person',
    'Company',
    'notificationService',
    '$filter',
    '$translate',
    'Vacancy',
    'Employee',
    '$filter',
    function (
        $scope,
        $rootScope,
        $stateParams,
        Service,
        Person,
        Company,
        notificationService,
        $translate,
        $filter,
        Vacancy,
        Employee,
        $filter,
    ) {
        $scope.showCreateDepartment = false;
        $scope.showEditDepartment = false;
        $scope.departmentOnEdit = {
            name: null,
            parentEmployeeDepartmentId: null,
            editedDepartmentId: null,
        };
        $scope.refreshDepartmentList = function () {
            Employee.getDepartmentsList(function (resp) {
                if (resp.status == 'ok') {
                    $scope.departmentsList = resp.objects;
                } else {
                    $location.path('/organizer');
                }
            });
        };
        $scope.saveDepartmentByKey = function (e) {
            if (e.keyCode === 13) {
                $scope.saveDepartment();
            }
        };
        $scope.refreshDepartmentList();
        $scope.saveDepartment = function () {
            Employee.departmentAdd(
                {
                    name: $scope.newName,
                    parentEmployeeDepartmentId: $scope.departmentOnEdit
                        ? $scope.departmentOnEdit.employeeDepartmentId
                        : null,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        $scope.departmentOnEdit = null;
                        $scope.refreshDepartmentList();
                        notificationService.success($filter('translate')('Department successfully created'));
                    }

                    if (resp.code === 'notEnabledEmployeeFunction' && resp.status === 'error') {
                        notificationService.error(resp.message);
                    }
                },
            );
        };
        $scope.deleteDepartment = function (department) {
            Employee.departmentDelete({ id: department.employeeDepartmentId }, function (resp) {
                if (resp.status == 'ok') {
                    $scope.refreshDepartmentList();
                } else {
                    notificationService.error(resp.message);
                }
            });
        };
        $scope.pasteEditDepartment = function (depart) {
            if ($scope.showCreateDepartment == true) {
                $scope.showCreateDepartment = false;
            }
            $scope.showEditDepartment = true;
            $scope.newName = depart.name;
            $scope.editedDepartmentId = depart.employeeDepartmentId;
            if (depart.parentName) {
                $scope.departmentOnEdit.employeeDepartmentId = depart.parentEmployeeDepartmentId;
                $scope.parentId = depart.parentEmployeeDepartmentId;
            }
        };
        $scope.editDepartment = function () {
            Employee.departmentEdit(
                {
                    employeeDepartmentId: $scope.editedDepartmentId,
                    name: $scope.newName,
                    parentEmployeeDepartmentId: $scope.departmentOnEdit
                        ? $scope.departmentOnEdit.employeeDepartmentId
                        : null,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        $scope.refreshDepartmentList();
                        $scope.showEditDepartment = false;
                        if ($scope.departmentOnEdit) {
                            $scope.departmentOnEdit.employeeDepartmentId = null;
                        }
                        $scope.newName = null;
                    } else {
                        notificationService.error(resp.message);
                    }
                },
            );
        };
    },
]);
