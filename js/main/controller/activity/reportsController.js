controller.controller('reportsController', [
    '$scope',
    '$rootScope',
    '$location',
    '$window',
    'localStorageService',
    'Vacancy',
    'vacancyStages',
    'ngTableParams',
    'Client',
    '$filter',
    'Service',
    'ScopeService',
    'Company',
    'notificationService',
    'serverAddress',
    '$timeout',
    'Person',
    'Statistic',
    '$uibModal',
    '$anchorScroll',
    'Stat',
    'routeData',
    function (
        $scope,
        $rootScope,
        $location,
        $window,
        localStorageService,
        Vacancy,
        vacancyStages,
        ngTableParams,
        Client,
        $filter,
        Service,
        ScopeService,
        Company,
        notificationService,
        serverAddress,
        $timeout,
        Person,
        Statistic,
        $uibModal,
        $anchorScroll,
        Stat,
        routeData,
    ) {
        $rootScope.fullNameForModel;

        if ($location.path() == '/reports/vacancy') {
            initController();
            $rootScope.closeModal = function () {
                $scope.modalInstance.close();
            };
            $scope.deleteSearchByUser = function () {
                $scope.searchParam.personId = null;
                $scope.searchParam.personNameWhoSearching = null;
                $scope.tableParams.reload();
            };

            $scope.updateSearchStatuses = function () {
                $scope.searchParam.status = '';
                if ($scope.chosenStatuses.length != 1) {
                    angular.forEach($scope.chosenStatuses, function (status) {
                        $scope.searchParam.status = $scope.searchParam.status + status + ',';
                    });
                    $scope.searchParam.status = $scope.searchParam.status.replace(/,$/, '');
                } else {
                    $scope.searchParam.status = $scope.chosenStatuses[0];
                }
            };
            $scope.deleteSearchStatus = function (selectedStatus) {
                $scope.chosenStatuses.splice(selectedStatus, 1);
                $scope.currentStatus = 'null';
            };
            $scope.methodForDrop = function (person) {
                $scope.fullNameForModel = person;
                $scope.searchParam.personId = person.personId || person.userId;
            };
            $scope.clickSearch = function () {
                $scope.searchParam.status = $scope.chosenStatuses.join(',');

                if (
                    !$scope.searchParam['status'] &&
                    $scope.searchParam['salaryName'] == 'null' &&
                    $scope.searchParam.states == null &&
                    $scope.searchParam.state == null &&
                    $('#clientAutocompleater').select2('data') == null &&
                    $scope.searchParam.words.length == 0 &&
                    $scope.searchParam.name == null &&
                    $scope.searchParam.position == null &&
                    $scope.searchParam.candidateGroups == null &&
                    $scope.searchParam.regionId == null &&
                    $scope.searchParam.candidateGroupIds == null &&
                    $scope.searchParam.searchFullTextType == null &&
                    $scope.searchParam['responsibleId'] == null &&
                    $scope.searchParam['personId'] == 'null'
                ) {
                    notificationService.error($filter('translate')('Enter the data'));
                } else {
                    if (
                        $scope.searchParam['salaryName'] ||
                        $scope.searchParam['status'] ||
                        $('#clientAutocompleater').select2('data') !== null ||
                        $scope.searchParam['regionId'] ||
                        $scope.searchParam['responsibleId'] ||
                        $scope.searchParam['personId'] ||
                        $scope.searchParam['words']
                    ) {
                        if ($scope.searchParam['salaryName'] === 'null') {
                            $scope.searchParam['salary'] = '';
                        }
                        if ($scope.searchParam['salaryName']) {
                            angular.forEach($scope.salaryObject, function (resp) {
                                if (resp.name == $scope.searchParam.salaryName) {
                                    $scope.searchParam['salary'] = resp;
                                }
                            });
                        }
                        if ($('#clientAutocompleater').select2('data') !== null) {
                            $scope.searchParam['clientId'] = $('#clientAutocompleater').select2('data').id;
                            $scope.searchParam['clientName'] = $('#clientAutocompleater').select2('data').name;
                        }
                        $scope.tableParams.$params.page = 1;
                        $scope.tableParams.reload();
                        $rootScope.searchCheckVacancy = true;
                    } else if ($rootScope.searchCheckVacancy) {
                        $scope.tableParams.$params.page = 1;
                        $scope.tableParams.reload();
                        $rootScope.searchCheckVacancy = false;
                    }
                    $scope.isSearched = true;
                }
            };
            $scope.changeInputPage = function (params, searchNumber) {
                var searchNumber = Math.round(searchNumber);
                var maxValue = $filter('roundUp')(params.settings().total / params.count());
                if (searchNumber) {
                    if (searchNumber >= 1 && searchNumber <= maxValue) {
                        params.page(searchNumber);
                        $scope.a.searchNumber = searchNumber;
                    }
                }
            };
            $scope.toOneObject = function (localId) {
                $location.path('/vacancies/' + localId);
            };
            $scope.toAdd = function () {
                $location.path('/vacancy/add');
            };

            $scope.showAdvancedSearchFunc = function () {
                $scope.showAdvancedSearch = !$scope.showAdvancedSearch;
            };
            $scope.showAdvancedSearchFuncHide = function () {
                $scope.showAdvancedSearch = !$scope.showAdvancedSearch;
            };
            $scope.getFirstLetters = function (str) {
                return Service.firstLetters(str);
            };

            // Method for initilization global variable
            $rootScope.pushedLocalIdVacancies();

            // Method for push OTHER vacancies in Funnel
            $scope.changeVacanciesAdded = function (event, vacancy) {
                if (event.target.tagName === 'A') return;
                vacancy.added = !vacancy.added;
            };

            $scope.pushVacanciesInFunnel = (e, vacancy) => {
                vacancy.added = e.target.checked;

                if (vacancy.added) {
                    if (!$rootScope.pushedVacancies.some((cId) => cId === vacancy.vacancyId)) {
                        $rootScope.pushedVacancies.push(vacancy.vacancyId);

                        $rootScope.pushedVacancies.forEach((cId) => {
                            if (vacancy.vacancyId === cId) vacancy.added = true;
                        });
                    }
                } else {
                    $rootScope.pushedVacancies.splice($rootScope.pushedVacancies.indexOf(vacancy.vacancyId), 1);
                }

                $scope.$$phase || $scope.$apply();
            };

            // Method for push ALL vacancies in Funnel

            $scope.pushAllVacanciesFunnel = (e) => {
                $scope.checkAllVacancy = e.target.checked;
                angular.forEach($scope.vacancies, function (vacancy) {
                    if ($scope.checkAllVacancy) {
                        vacancy.added = true;
                    } else {
                        vacancy.added = false;
                    }
                    $scope.pushVacanciesInFunnel(e, vacancy);
                });

                $scope.$$phase || $scope.$apply();
            };

            $scope.goToVacancyStagesHistogram = function () {
                if ($scope.pushedVacancies.length < 2) {
                    notificationService.error($filter('translate')('Please select two or more vacancies'));
                    return;
                }

                Stat.requestGetGraphicsInfo({
                    vacancyIds: $scope.pushedVacancies,
                }).then(
                    (resp) => {
                        routeData['graphicsInfoResp'] = resp;
                        routeData['vacancyStagesHistogram'] = $scope.pushedVacancies;
                        $window.location.href = '#/reports/vacancy-histogram';
                    },
                    (error) => notificationService.error(error.message),
                );
            };

            // Method for send vacancies marked with a checkbox in request
            $scope.buildAllFunnel = function () {
                Statistic.getVacancyDetailInfo({
                    vacancyIds: $scope.pushedVacancies,
                    withCandidatesHistory: true,
                }).then(
                    (resp) => {
                        routeData['vacanciesDetailedInfoResp'] = resp;
                        $window.location.href = '#/reports/vacancy-funnel';
                    },
                    (error) => notificationService.error(error.message),
                );
            };

            $scope.changeInputPage = function (params, searchNumber) {
                var searchNumber = Math.round(searchNumber);
                var maxValue = $filter('roundUp')(params.settings().total / params.count());
                if (searchNumber) {
                    if (searchNumber >= 1 && searchNumber <= maxValue) {
                        params.page(searchNumber);
                        $scope.a.searchNumber = searchNumber;
                    }
                }
            };

            $scope.showChangeStatusOfVacancy = function (status, vacancy) {
                $scope.oneVacancy = vacancy;
                $rootScope.changeStateObject.status_old = $scope.oneVacancy.status;
                $rootScope.changeStateObject.status = status;
                $rootScope.changeStateObject.placeholder = $filter('translate')(
                    'Write_a_comment_why_do_you_change_vacancy_status',
                );
                if ($rootScope.changeStateObject.status === $rootScope.changeStateObject.status_old) {
                    notificationService.error($filter('translate')('You cannot change the status to the same'));
                    return;
                }
                $scope.numberOfCandidatesInDifferentStates = function () {
                    var totalCount = 0;
                    Vacancy.getCounts(
                        {
                            vacancyId: vacancy.vacancyId,
                        },
                        function (statusesCount) {
                            $scope.statusesCount = statusesCount.objects;
                            angular.forEach($scope.VacancyStatusFiltered, function (val) {
                                val.count = 0;
                            });
                            angular.forEach($scope.statusesCount, function (item) {
                                angular.forEach($scope.VacancyStatusFiltered, function (valS) {
                                    if (valS.name) {
                                        valS.value = valS.name;
                                    }
                                    if (item.item == valS.value) {
                                        valS.count = item.count;
                                        totalCount = totalCount + item.count;
                                    }
                                    if (item.item == valS.customInterviewStateId) {
                                        valS.count = item.count;
                                        totalCount = totalCount + item.count;
                                    }
                                });
                            });
                            $scope.numberAllCandidateInVacancy = totalCount;
                        },
                    );
                };
                if (status == 'completed') {
                    $scope.numberOfCandidatesInDifferentStates();
                    setTimeout(function () {
                        var hasApproved = false;
                        angular.forEach($scope.statusesCount, function (i) {
                            if (i.item == 'approved') {
                                hasApproved = true;
                            }
                        });
                        if (!hasApproved) {
                            notificationService.error(
                                $filter('translate')('You must move one of the candidates to status Hired'),
                            );
                        } else {
                            $scope.modalInstance = $uibModal.open({
                                animation: true,
                                templateUrl: '../partials/modal/vacancy-change-status.html',
                                size: '',
                                resolve: function () {},
                            });
                        }
                    }, 500);
                } else {
                    if (status != 'inwork') {
                        $scope.modalInstance = $uibModal.open({
                            animation: true,
                            templateUrl: '../partials/modal/vacancy-change-status.html',
                            size: '',
                            resolve: function () {},
                        });
                    } else if (
                        status == 'inwork' &&
                        vacancy.responsiblesPerson != undefined &&
                        vacancy.responsiblesPerson.length > 0
                    ) {
                        $scope.modalInstance = $uibModal.open({
                            animation: true,
                            templateUrl: '../partials/modal/vacancy-change-status.html',
                            size: '',
                            resolve: function () {},
                        });
                    } else if ($scope.needAutoSetResponsible && vacancy.status == 'inwork') {
                        $rootScope.changeResponsibleInVacancy.id = $rootScope.me.userId;
                        $rootScope.changeResponsibleInVacancy.comment =
                            'Поскольку вы являетесь единственным пользователем Вашей компании, мы назначили Вас ответственным';
                        $rootScope.saveResponsibleUserInVacancy();
                        $scope.modalInstance = $uibModal.open({
                            animation: true,
                            templateUrl: '../partials/modal/vacancy-change-status.html',
                            size: '',
                            resolve: function () {},
                        });
                    } else if (status == 'inwork' && !$scope.needAutoSetResponsible) {
                        notificationService.error($filter('translate')('You must set a responsible') + '!');
                    }
                }
            };
            $rootScope.saveVacancyStatus = function () {
                if (!$rootScope.clickedSaveVacancyStatus && $rootScope.changeStateObject.status) {
                    $rootScope.clickedSaveVacancyStatus = true;
                    $rootScope.closeModal();
                    Vacancy.changeState(
                        {
                            vacancyId: $scope.oneVacancy.vacancyId,
                            comment: $rootScope.changeStateObject.comment,
                            vacancyState: $rootScope.changeStateObject.status,
                        },
                        function (resp) {
                            if (resp.status == 'ok') {
                                $scope.vacancy.status = $rootScope.changeStateObject.status;
                                $rootScope.changeStateObject.comment = '';
                                notificationService.success($filter('translate')('vacancy change status'));
                                $scope.tableParams.reload();
                            } else if (resp.message) {
                                notificationService.error(resp.message);
                            }
                            $rootScope.clickedSaveVacancyStatus = false;
                        },
                        function (err) {
                            $rootScope.clickedSaveVacancyStatus = false;
                        },
                    );
                }
            };

            function persistentCheckbox(vacancies) {
                vacancies.forEach((vacancy) => {
                    $rootScope.pushedVacancies.forEach((pushedVacancy) => {
                        if (vacancy.vacancyId === pushedVacancy) {
                            vacancy.added = true;
                        }
                    });
                });
            }

            $scope.selectOpt = function () {
                const salaryInput = document.querySelector('.select-salary-input');

                if ($scope.toggleSalaryOpt) {
                    salaryInput.style.borderBottomLeftRadius = '0px';
                    salaryInput.style.borderBottomRightRadius = '0px';
                } else {
                    salaryInput.style.borderBottomLeftRadius = '5px';
                    salaryInput.style.borderBottomRightRadius = '5px';
                }

                $scope.toggleSalaryOpt = !$scope.toggleSalaryOpt;
            };

            $scope.sortTable = function (sortParam) {
                $scope.searchParam['sort'] = sortParam;
                $scope.searchParam['sortOrder'] = $scope.searchParam['sortOrder'] === 'ASC' ? 'DESC' : 'ASC';

                Vacancy.setOptions('sort', $scope.searchParam['sort']);
                Vacancy.setOptions('sortOrder', $scope.searchParam['sortOrder']);

                $rootScope.clientsTableSort = {
                    name: $scope.searchParam['sort'],
                    sort: $scope.searchParam['sortOrder'].toLowerCase(),
                };

                $scope.tableParams.reload();
            };

            function setDefaultCurrency() {
                Service.onGetDefaultCurrency({
                    defaultCurrencyType: 'vacancy_search',
                }).then((resp) => {
                    if (resp.status === 'ok') $scope.currencyValue = resp.object;
                });
            }

            function initController() {
                setDefaultCurrency();
                $rootScope.redirectToReports();
                $scope.vacanciesFound = null;
                $rootScope.searchCheckVacancy =
                    $rootScope.searchCheckVacancy == undefined ? false : $rootScope.searchCheckVacancy;
                $scope.onlyMe = $rootScope.onlyMe;
                $scope.salaryObject = Service.getSalary();
                $scope.previousFlag = true;
                $scope.a = {};
                $scope.a.searchNumber = 1;
                $scope.status = Vacancy.status();
                $scope.statusValues = $scope.status && $scope.status.map((status) => status.value);
                $scope.employmentType = Service.employmentType();
                $scope.regionId = null;
                $scope.loader = false;
                $scope.searchStatus = '';
                $scope.chosenStatuses = [];
                $scope.currentStatus = null;
                $scope.isSearched = false;
                $scope.toggleSalaryOpt = true;
                $rootScope.changeStateObject = {
                    status: '',
                    comment: '',
                    placeholder: null,
                };
                $scope.vacancy = {
                    accessType: 'public',
                    currency: 'USD',
                    dateFinish: null,
                    datePayment: null,
                    descr: '',
                    sex: null,
                    langs: '',
                    status: 'open',
                    clientId: {
                        clientId: $rootScope.addVacancyClientId,
                    },
                };
                $rootScope.clientsTableSort = {
                    name: 'dc',
                    sort: 'desc',
                };
                $rootScope.addVacancyClientId = null;

                if ($rootScope.curentOnlyMenWatch !== undefined) {
                    $rootScope.curentOnlyMenWatch();
                }
                if ($rootScope.curentRegionWatch !== undefined) {
                    $rootScope.curentRegionWatch();
                }

                function scope_update(val) {
                    $scope.tableParams.reload();
                }

                ScopeService.setCurrentControllerUpdateFunc(scope_update);

                $rootScope.statusInter = Vacancy.getInterviewStatus();
                /************************Параметры при загрузке страницы*******************************/
                if (localStorage.countVacancy) {
                    $scope.startPagesShown = localStorage.countVacancy;
                } else {
                    $scope.startPagesShown = 15;
                }
                $scope.searchParam = {
                    searchCs: true,
                    salary: '',
                    currency: Service.currency(),
                    status: '',
                    regionId: null,
                    words: '',
                    personId: 'null',
                    clientId: '',
                    personNameWhoSearching: $rootScope.usernameThatIsSearching,
                    pages: { count: $scope.startPagesShown },
                };

                //This function must be after init searchParam
                if ($rootScope.previousLocation == '/vacancies/{id}') {
                    if ($rootScope.searchParamInVacancies != undefined) {
                        $scope.searchParam = $rootScope.searchParamInVacancies;
                        if ($scope.searchParam.clientId) {
                            $scope.$watch('setClientAutocompleterValue', function (newVal, oldVal) {
                                if (newVal != undefined) {
                                    $scope.setClientAutocompleterValue(
                                        $scope.searchParam.clientName,
                                        $scope.searchParam.clientId,
                                    );
                                }
                            });
                        }
                        $rootScope.searchParamInVacancies = null;
                    }
                } else if ($rootScope.previousLocation == '/clients/{id}' && $rootScope.allClientsVacancies == true) {
                    $scope.searchParam = {
                        searchCs: true,
                        salary: '',
                        status: '',
                        regionId: null,
                        words: '',
                        personId: Vacancy.searchOptions().personId,
                        clientId: '',
                        personNameWhoSearching: $rootScope.usernameThatIsSearching,
                        pages: { count: $scope.startPagesShown },
                    };
                    $scope.searchParam.clientId = $rootScope.searchParamInVacancies.clientId;
                    $scope.searchParam.clientName = $rootScope.searchParamInVacancies.clientName;
                    if ($rootScope.searchParamInVacancies.status != undefined) {
                        $scope.searchParam.status = $rootScope.searchParamInVacancies.status;
                        $scope.chosenStatuses = ['onhold', 'payment', 'completed', 'canceled', 'deleted'];
                    }
                    $scope.$watch('setClientAutocompleterValue', function (newVal, oldVal) {
                        if (newVal != undefined) {
                            $scope.setClientAutocompleterValue(
                                $scope.searchParam.clientName,
                                $scope.searchParam.clientId,
                            );
                        }
                    });
                }
                let currentPage = $scope.searchParam.pages.number;
                $scope.tableParams = new ngTableParams(
                    {
                        page: 1,
                        count: $scope.searchParam.pages.count,
                    },
                    {
                        total: 0,
                        getData: function ($defer, params) {
                            if ($rootScope.previousLocation == '/vacancies/{id}') {
                                if ($scope.previousFlag) {
                                    $scope.tableParams.page($rootScope.previousSearchNumber);
                                    $scope.previousFlag = !$scope.previousFlag;
                                }
                            }

                            function init() {
                                const activeParam = ScopeService.getActiveScopeObject();
                                $scope.activeScopeParam = activeParam;
                                Vacancy.setOptions('page', {
                                    number: params.$params.page - 1,
                                    count: params.$params.count,
                                });
                                localStorage.countVacancy = params.$params.count;
                                $scope.searchParam.pages.count = params.$params.count;
                                $scope.searchParam.personId =
                                    $scope.searchParam.personId == 'null' ? null : $scope.searchParam.personId;
                                Vacancy.setOptions(
                                    'personId',
                                    $scope.searchParam.personId != undefined
                                        ? $scope.searchParam.personId
                                        : activeParam.name == 'onlyMy'
                                        ? $rootScope.userId
                                        : null,
                                );
                                Vacancy.setOptions(
                                    'salaryFrom',
                                    $scope.searchParam['salary'] ? $scope.searchParam['salary'].salaryFrom : null,
                                );
                                Vacancy.setOptions(
                                    'salaryTo',
                                    $scope.searchParam['salary'] ? $scope.searchParam['salary']['salaryTo'] : null,
                                );
                                Vacancy.setOptions(
                                    'state',
                                    Service.isNotBlank($scope.searchParam['status']) &&
                                        $scope.chosenStatuses.length == 1
                                        ? $scope.searchParam['status']
                                        : null,
                                );
                                Vacancy.setOptions(
                                    'states',
                                    $scope.chosenStatuses.length > 1 ? $scope.chosenStatuses : null,
                                );
                                Vacancy.setOptions(
                                    'words',
                                    $scope.searchParam['words'] ? $scope.searchParam['words'] : null,
                                );
                                Vacancy.setOptions(
                                    'clientId',
                                    Service.isNotBlank($scope.searchParam['clientId'])
                                        ? $scope.searchParam['clientId']
                                        : null,
                                );
                                Vacancy.setOptions(
                                    'responsibleId',
                                    Service.isNotBlank($scope.searchParam['responsibleId'])
                                        ? $scope.searchParam['responsibleId']
                                        : null,
                                );

                                Vacancy.setOptions(
                                    'sortOrder',
                                    $scope.searchParam['sortOrder'] ? $scope.searchParam['sortOrder'] : null,
                                );
                                Vacancy.setOptions(
                                    'sort',
                                    $scope.searchParam['sort'] ? $scope.searchParam['sort'] : null,
                                );
                                Vacancy.setOptions(
                                    'country',
                                    $scope.searchParam['regionId'] ? $scope.searchParam['regionId'].country : null,
                                );
                            }

                            if (ScopeService.isInit()) {
                                init();
                            } else {
                                Vacancy.setOptions(
                                    'country',
                                    activeParam.name == 'region' && activeParam.value.type == 'country'
                                        ? activeParam.value.value
                                        : null,
                                );
                                Vacancy.setOptions(
                                    'city',
                                    activeParam.name == 'region' && activeParam.value.type == 'city'
                                        ? activeParam.value.value
                                        : null,
                                );
                            }

                            function getVacancies(page, count) {
                                if (page || count) {
                                    init();
                                    currentPage = page;
                                    Vacancy.setOptions('page', {
                                        number: page,
                                        count: count,
                                    });
                                } else {
                                    $scope.isShowMore = false;
                                    currentPage = Vacancy.searchOptions().page.number;
                                    if (document.getElementById('scrollup'))
                                        document.getElementById('scrollup').style.display = 'none';
                                    $timeout(function () {
                                        $anchorScroll('mainTable');
                                    });
                                }

                                $rootScope.$on('updateCurrencyValue', (e, opt) => {
                                    $scope.currencyValue = opt.value;
                                });
                                $rootScope.loading = true;
                                Vacancy.all(
                                    Object.assign(
                                        {
                                            excludeStates: ['recommendation'],
                                            currency: $scope.currencyValue,
                                        },
                                        Vacancy.searchOptions(),
                                    ),
                                    function (response) {
                                        if (response.status === 'error') {
                                            $scope.sollarError = true;
                                            notificationService.error(response.message);
                                        }
                                        $rootScope.objectSize =
                                            response['objects'] != undefined ? response['total'] : undefined;

                                        $scope.paginationParams = {
                                            currentPage: currentPage,
                                            totalCount: $rootScope.objectSize,
                                            totalPagesCount: response.allPageCount,
                                            amountOfElements: response.size,
                                        };

                                        let pagesCount = Math.ceil(
                                            $scope.paginationParams.totalCount / $scope.paginationParams.currentPage,
                                        );
                                        if (pagesCount == Vacancy.searchOptions().page.number + 1) {
                                            $('#show_more').hide();
                                        } else {
                                            $('#show_more').show();
                                        }
                                        params.total($scope.paginationParams.totalCount);
                                        angular.forEach(response['objects'], function (val) {
                                            if (val.region) {
                                                if (val.region.city) {
                                                    val.regionShort = val.region.displayCity;
                                                } else if (val.region.country)
                                                    val.regionShort = val.region.displayCountry;
                                            }
                                        });

                                        if (page) {
                                            $scope.vacancies = $scope.vacancies.concat(response['objects']);
                                        } else {
                                            $scope.vacancies = response['objects'];
                                        }
                                        persistentCheckbox($scope.vacancies);
                                        $scope.vacanciesFound = $scope.paginationParams.totalCount >= 1;
                                        $defer.resolve($scope.vacancies);
                                        Vacancy.init();
                                        $scope.searchParam.personId =
                                            $scope.searchParam.personId == null ? 'null' : $scope.searchParam.personId;
                                        $rootScope.loading = false;
                                        $scope.displayShowMore =
                                            currentPage < params.total() / $scope.tableParams.count() - 1;

                                        $scope.vacancies.forEach((vacancy) => {
                                            if ($rootScope.pushedVacancies.includes(vacancy.vacancyId)) {
                                                vacancy.added = true;
                                            }
                                        });
                                    },
                                );
                            }

                            getVacancies();
                            $scope.showMore = function () {
                                Vacancy.setOptions(
                                    'sortOrder',
                                    $scope.searchParam['sortOrder'] ? $scope.searchParam['sortOrder'] : null,
                                );
                                Vacancy.setOptions(
                                    'sort',
                                    $scope.searchParam['sort'] ? $scope.searchParam['sort'] : null,
                                );
                                $scope.isShowMore = true;
                                $scope.displayShowMore = Service.dynamicTableLoading(
                                    params.total(),
                                    currentPage,
                                    $scope.tableParams.count(),
                                    getVacancies,
                                );
                            };
                            $rootScope.searchParamInVacancies = $scope.searchParam;
                            $scope.a.searchNumber = $scope.tableParams.page();
                            $rootScope.previousSearchNumber = $scope.a.searchNumber;
                            $rootScope.allClientsVacancies = false;
                        },
                    },
                );
                Client.init();
                if ($rootScope.searchedClientId) {
                    $scope.searchParam['clientId'] = $rootScope.searchedClientId;
                    $scope.tableParams.reload();
                    $rootScope.searchedClientId = '';
                }
                Person.getAllPersons(
                    function (resp) {
                        $scope.persons = [];
                        $rootScope.persons = [];
                        $rootScope.personsNotChanged = [];
                        $scope.associativePerson = resp.object;
                        angular.forEach($scope.associativePerson, function (val, key) {
                            $scope.persons.push($scope.associativePerson[key]);
                            $rootScope.persons.push($scope.associativePerson[key]);
                            $rootScope.personsNotChanged.push($scope.associativePerson[key]);
                        });
                        var iUser = null;
                        for (var i = 0; i <= $scope.persons.length - 1; i++) {
                            if ($rootScope.me.userId == $scope.persons[i].userId) {
                                iUser = $scope.persons[i];
                                $scope.persons.splice(i, 1);
                                break;
                            }
                        }
                        if (iUser) {
                            $scope.persons.unshift(iUser);
                        }
                    },
                    function (error) {},
                );
                setTimeout(() => {
                    Service.saveRegions2(function (resp) {
                        $scope.regions = resp;
                    });
                    $(document).click(function () {
                        if ($('.advancedSearch').css('display') != 'none') {
                            $scope.showAdvancedSearchFuncHide();
                            $scope.$apply();
                        }
                    });
                }, 0);
            }

            $scope.onChangePage = (pageNumber) => {
                if ($scope.isShowMore) {
                    $scope.tableParams.page($rootScope.previousSearchNumber);
                    $scope.tableParams.reload();
                } else {
                    $scope.tableParams.page(pageNumber);
                }

                $scope.$$phase || $scope.$apply();
            };

            $scope.changeAmountOfElements = (amount) => {
                if ($scope.tableParams.count() === amount) return;
                $scope.tableParams.count(amount);
                $scope.tableParams.reload();
            };
        }
    },
]);
