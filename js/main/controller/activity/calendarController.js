controller.controller('ActivityCalendarController', [
    'Person',
    '$scope',
    '$rootScope',
    'Vacancy',
    'frontMode',
    '$sce',
    'googleService',
    'notificationService',
    '$filter',
    '$http',
    '$timeout',
    '$localStorage',
    'outlookService',
    '$uibModal',
    '$translate',
    function (
        Person,
        $scope,
        $rootScope,
        Vacancy,
        frontMode,
        $sce,
        googleService,
        notificationService,
        $filter,
        $http,
        $timeout,
        $localStorage,
        outlookService,
        $uibModal,
        $translate,
    ) {
        $scope.getDisconnectBtnText = (text) => `${$filter('translate')('disconnect_to')} ${text}`;
        $scope.getIntegrationBtnText = (text) =>
            `${$filter('translate')('Turn_on_the_integration')} ${
                $rootScope.currentLang !== 'pl' ? $filter('translate')('with') : ''
            } ${text}`;

        $rootScope.closeModal = function () {
            $scope.modalInstance.close();
        };

        $scope.updateCalendar = (event, selectedCal) => {
            selectedCal.checked = !selectedCal.checked;

            let calendarIndex;
            if (!selectedCal.checked) {
                for (var i = 0; i < $scope.calendarsForGet.length; i++) {
                    if ($scope.calendarsForGet[i].id === selectedCal.id) {
                        $scope.calendarsForGet.splice(i, 1);
                        selectedCal.checked = false;
                        i = 0;
                    }
                }
                calendarIndex = _.findIndex($scope.calendarsForCheck, {
                    id: selectedCal.id,
                });
                $scope.calendarsForCheck[calendarIndex].checked = false;
            } else {
                if (
                    _.indexOf($scope.calendarsForGet, {
                        id: selectedCal.id,
                    }) === -1
                ) {
                    angular.forEach($scope.calendars, function (val) {
                        if (val.id === selectedCal.id) {
                            $scope.calendarsForGet.push(val);
                            selectedCal.checked = true;
                        }
                    });
                }
                calendarIndex = _.findIndex($scope.calendarsForCheck, {
                    id: selectedCal.id,
                });
                $scope.calendarsForCheck[calendarIndex].checked = true;
            }

            $localStorage.set('calendarsss_+' + $rootScope.me.personId, $scope.calendarsForCheck);
            $rootScope.$$phase || $scope.$apply();
            if (!$scope.$$phase) {
                $scope.$apply();
            }

            $rootScope.$$phase || $scope.$apply();
        };

        $scope.getCalendarFrame = function (calendar, calendars) {
            let url = 'https://www.google.com/calendar/embed?mode=MONTH';

            if (frontMode === 'war') {
                // if (calendar) {
                //     url += 'src=' + calendar.id.replace(new RegExp('#', 'g'), '%23');
                //     url += '&ctz=' + jstz.determine().name();
                //     url += '&mode=MONTH';
                // }

                if (calendars) {
                    angular.forEach(calendars, function (value, key) {
                        if (value?.id) {
                            url += '&src=' + value.id.replace(new RegExp('#', 'g'), '%23');
                        }
                    });
                }

                return $sce.trustAsResourceUrl(url);
            } else {
                return $sce.trustAsResourceUrl(
                    'https://www.google.com/calendar/embed?mode=WEEK&src=cgbil05l7c05viqpngev2ou9b4%40group.calendar.google.com&ctz=Europe/Kiev',
                );
            }
        };

        $scope.deleteGoogleCalendarForCs = function () {
            Person.deleteGoogleCalendar(function (resp) {
                if (resp.status === 'ok') {
                    setCalendarShow(false);

                    $scope.showConnectButtom = true;
                    resfeshIframe();

                    $scope.calendars = [];
                    $scope.selectedCalendar = null;
                } else {
                    notificationService.error(resp.message);
                }
            });
        };

        $scope.addOutlookcalendar = function () {
            if ($rootScope.calendarShow) {
                outlookService.getAccessForCalendar();
            } else {
                outlookService.getAccessForCalendar();
            }
        };

        $scope.deleteOutLookCalendar = function () {
            Person.deleteOutlookCalendar(function (resp) {
                if (resp.status === 'ok') {
                    if (resp.object) {
                        $rootScope.me.outlookCalendar = resp.object;
                        $scope.deleteOutlook = true;
                        $scope.deleteOutlookv2 = true;
                    } else {
                        $rootScope.me.outlookCalendar = false;
                    }
                    $rootScope.availableOutlookMeetingType = null;
                    $scope.hideBlockOutlook();
                } else {
                    notificationService.error(resp.message);
                }
            });
        };

        $scope.hideBlockOutlook = function () {
            $rootScope.me.outlookCalendar = false;
        };

        function enlargeImageModalController(uibModalInstance, src) {
            const vm = this;
            vm.closeModal = () => uibModalInstance.close();
            vm.src = src;
        }

        $scope.enlargeImage = (src) => {
            if (window.innerWidth < 575) {
                $uibModal.open({
                    templateUrl: '../partials/modal/enlargeImageModal.html',
                    controllerAs: 'vm',
                    windowClass: 'enlarge-image-modal-wrapper',
                    controller: ['$uibModalInstance', 'src', enlargeImageModalController],
                    resolve: {
                        src: () => src,
                    },
                });
            }
        };

        $scope.addGoogleCalendarForCs = function () {
            Person.getParamsForGoogleCalendar(function (resp) {
                if (resp.status === 'ok' && resp.object) {
                    let clientId = resp.object.clientId;
                    const appName = resp.object.appName;

                    googleService.addCalendar(clientId).then(
                        function (authCode) {
                            if (authCode) {
                                addGoogleCalendarFromServer(authCode, appName);
                            } else {
                                notificationService.error($translate.instant('Try again later'));
                            }
                        },
                        function () {
                            notificationService.error($translate.instant('Try again later'));
                        },
                    );
                } else if (resp.message) {
                    notificationService.error(resp.message);
                }
            });
        };

        $scope.addOnlyCreatedByMeOutlookHandler = function () {
            $rootScope.loading = true;

            $scope.addOnlyCreatedByMeOutlook = !$scope.addOnlyCreatedByMeOutlook;

            const payload = {
                id: $rootScope.me.outlookCalendar.ocId,
                addOnlyCreatedByMe: $scope.addOnlyCreatedByMeOutlook,
                calendarType: 'outlook',
            };

            Person.onUpdateCalendarPreferences(payload)
                .catch((error) => {
                    $scope.addOnlyCreatedByMeOutlook = !$scope.addOnlyCreatedByMeOutlook;
                    console.error(error);
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        };

        $scope.addOnlyCreatedByMeGoogleHandler = function () {
            $rootScope.loading = true;

            $scope.addOnlyCreatedByMeGoogle = !$scope.addOnlyCreatedByMeGoogle;

            const payload = {
                id: $scope.gcId,
                addOnlyCreatedByMe: $scope.addOnlyCreatedByMeGoogle,
                calendarType: 'google',
            };

            Person.onUpdateCalendarPreferences(payload)
                .catch((error) => {
                    $scope.addOnlyCreatedByMeGoogle = !$scope.addOnlyCreatedByMeGoogle;
                    console.error(error);
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        };

        $scope.handleChangeAddToAttendees = async (event) => {
            $rootScope.loading = true;
            const resp = await Person.onUpdateAddToAttendees({
                id: $scope.gcId,
                calendarType: 'google',
                addToAttendees: event.target.checked,
            });
            $rootScope.loading = false;

            if (resp.status === 'ok') {
                $scope.googleCalendarAddToAttendees = resp.object.addToAttendees;
            }

            $rootScope.$$phase || $scope.$apply();
        };

        function addGoogleCalendarFromServer(authCode, appName) {
            $rootScope.loading = true;

            Person.addGoogleCalendar(
                {
                    accessToken: authCode,
                    appName: appName,
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        $scope.addOnlyCreatedByMeGoogle = !!resp.object.addOnlyCreatedByMe;
                        $scope.gcId = resp.object.gcId;

                        Person.getClientIdForGoogleCalendar(
                            { appName },
                            function (resp) {
                                if (resp.status === 'ok' && resp.object) {
                                    getGoogleCalendarAfterAdd();
                                } else {
                                    $rootScope.loading = false;
                                    $scope.deleteGoogleCalendarForCs();
                                    notificationService.error($translate.instant('Try again later'));
                                }
                            },
                            function () {
                                $rootScope.loading = false;
                                $scope.deleteGoogleCalendarForCs();
                                notificationService.error($translate.instant('Try again later'));
                            },
                        );
                    } else {
                        if (resp.code === 'couldNotGetRefreshToken') {
                            $scope.modalInstance = $uibModal.open({
                                animation: true,
                                templateUrl: '../partials/modal/calendar-access.html',
                                resolve: {},
                            });
                        } else {
                            notificationService.error(resp.message);
                        }
                        $rootScope.loading = false;
                    }
                },
            );
        }

        function getGoogleCalendarAfterAdd() {
            googleService.getCalendar().then(
                function (calendarsInfo) {
                    initCalendarAfterAdd(calendarsInfo);
                },
                function () {
                    $rootScope.loading = false;

                    $scope.deleteGoogleCalendarForCs();
                    notificationService.error($translate.instant('Try again later'));

                    if (!$rootScope.$$phase) {
                        $rootScope.$apply();
                    }
                },
            );
        }

        function initUserGoogleCalendars(calendarsInfo) {
            const calendarsForCheckFromLocalStorage = JSON.parse(
                $localStorage.get('calendarsss_+' + $rootScope.me.personId),
            );
            if (calendarsInfo) {
                $scope.calendars = calendarsInfo.calendars;
                $scope.selectedCalendar = calendarsInfo.selectedCalendar;
                if (!$scope.selectedCalendar) {
                    $scope.deleteGoogleCalendarForCs();
                    return;
                }

                $scope.calendarsForCheck = [];

                angular.forEach($scope.calendars, function (val) {
                    $scope.calendarsForCheck.push({
                        id: val.id,
                        checked: true,
                        name: val.summary,
                        isCleverStaffEvent: val.summary === 'CleverStaff events' && val.accessRole === 'owner',
                    });
                });

                if (calendarsForCheckFromLocalStorage) {
                    $scope.calendarsForCheck.forEach((calendarForCheck) => {
                        calendarsForCheckFromLocalStorage.forEach((calendarForCheckFromLocalStorage) => {
                            if (calendarForCheckFromLocalStorage.id === calendarForCheck.id)
                                calendarForCheck.checked = calendarForCheckFromLocalStorage.checked;
                        });
                    });
                }

                $scope.calendarsForGet = angular.copy($scope.calendars);
                for (var i = 0; i < $scope.calendarsForGet.length; i++) {
                    for (var j = 0; j < $scope.calendarsForCheck.length; j++) {
                        if (
                            $scope.calendarsForCheck[j] &&
                            $scope.calendarsForCheck[j].id === $scope.calendarsForGet[i].id
                        ) {
                            if (!$scope.calendarsForCheck[j].checked) {
                                $scope.calendarsForGet.splice(i, 1);
                            }
                        }
                    }
                }

                if ($rootScope.loading) {
                    $timeout(() => {
                        $rootScope.loading = false;
                    }, 0);
                }

                if (!$rootScope.$$phase) {
                    $rootScope.$apply();
                }
            }
        }

        function resfeshIframe() {
            var iFrame = $(document).find('#calendar_iframe');
            iFrame.attr('src', iFrame.attr('src'));
        }

        function initCalendarAfterAdd(calendarsInfo) {
            setCalendarShow();

            initUserGoogleCalendars(calendarsInfo);
            $scope.getCalendarFrame();
        }

        function getGoogleCalendarForUserCs() {
            return new Promise((resolve) => {
                Person.getGoogleCalendar(function (resp) {
                    if (resp.object) {
                        const appName = resp.object.appName;

                        $scope.showConnectButtom = false;

                        $scope.addOnlyCreatedByMeGoogle = !!resp.object.addOnlyCreatedByMe;
                        $scope.googleCalendarAddToAttendees = resp.object.addToAttendees;
                        $scope.gcId = resp.object.gcId;
                        resfeshIframe();

                        resolve(appName);
                    } else {
                        $scope.showConnectButtom = true;
                        setCalendarShow(false);
                    }
                });
            });
        }

        function setCalendarShow(value = true) {
            $rootScope.calendarShow = value;
        }

        function initGoogleCalendar() {
            $rootScope.loading = true;
            getGoogleCalendarForUserCs().then(function (appName) {
                Person.getClientIdForGoogleCalendar({ appName: appName }, function (resp) {
                    if (resp.status === 'ok' && resp.object) {
                        googleService.getCalendar().then(
                            function (calendarsInfo) {
                                setCalendarShow();
                                initUserGoogleCalendars(calendarsInfo);
                                // $rootScope.loading = false;
                            },
                            function (error) {
                                $scope.deleteGoogleCalendarForCs();

                                if (error === 'wrong_account') {
                                    notificationService.error(
                                        $translate.instant('Wrong Google account. Try integrate calendar again'),
                                    );
                                } else {
                                    notificationService.error($translate.instant('Try again later'));
                                }
                                $rootScope.loading = false;
                            },
                        );
                    }
                });
            });
        }

        function initOutlookCalendar() {
            $rootScope.loading = true;
            Person.getOutlookCalendar(function (resp) {
                if (resp.object) {
                    $rootScope.me.outlookCalendar = resp.object;
                    $scope.addOnlyCreatedByMeOutlook = !!$rootScope.me.outlookCalendar.addOnlyCreatedByMe;
                } else {
                    $rootScope.me.outlookCalendar = false;
                }
                $timeout(() => {
                    $rootScope.loading = false;
                }, 0);
            });

            $scope.$watch('outLookWatch', function watch(newValue, oldValue) {
                if (newValue) {
                    Person.addOutlookCalendar(
                        {
                            code: newValue,
                        },
                        function (resp) {
                            if (resp.object) {
                                $rootScope.me.outlookCalendar = resp.object;
                                $scope.addOnlyCreatedByMeOutlook = !!$rootScope.me.outlookCalendar.addOnlyCreatedByMe;
                            } else {
                                $rootScope.me.outlookCalendar = false;
                            }
                        },
                    );
                }
            });
        }

        function init() {
            $scope.srcUrl = null;
            $scope.addOnlyCreatedByMeGoogle = false;
            initGoogleCalendar();
            initOutlookCalendar();
        }

        init();
    },
]);
