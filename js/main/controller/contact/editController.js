controller.controller('ContactEditController', [
    '$scope',
    'Service',
    '$rootScope',
    '$location',
    '$stateParams',
    'Contacts',
    'Client',
    'notificationService',
    '$filter',
    '$uibModal',
    'Candidate',
    '$translate',
    function (
        $scope,
        Service,
        $rootScope,
        $location,
        $stateParams,
        Contacts,
        Client,
        notificationService,
        $filter,
        $uibModal,
        Candidate,
        $translate,
    ) {
        $scope.contactId = $stateParams.contactId;
        $scope.showAddClient = false;
        $scope.pageType = 'edit';
        $scope.contacts = {
            skype: null,
            email: null,
            phone: null,
            mphone: null,
            facebook: null,
            homepage: null,
            linkedin: null,
            googleplus: null,
            vk: null,
            other: null,
        };
        $scope.errorFields = {
            firstName: false,
            lastName: false,
            position: false,
            email: false,
            mphone: false,
            skype: false,
            facebbok: false,
            linkedin: false,
            homepage: false,
        };
        $scope.validationOfEmailField = function (email) {
            if (email) {
                const re =
                    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                $scope.errorFields.email = !re.test(String(email).toLowerCase());
            } else {
                $scope.errorFields.email = false;
            }
        };
        $scope.backToClient = function (id) {
            $location.path('/clients/' + id);
        };
        $scope.validateContactField = function (name, type) {
            if (type === 'mphone') {
                if (name.length >= 20) $scope.errorFields[type] = true;
                else $scope.errorFields[type] = false;
                return name.slice(0, 20);
            } else {
                if (name && name.length >= 100) $scope.errorFields[type] = true;
                else $scope.errorFields[type] = false;
                return Candidate.validatePosition(name);
            }
        };
        $scope.validateCandidateName = function (name, type) {
            if (name.length >= 50) $scope.errorFields[type] = true;
            else $scope.errorFields[type] = false;
            return Candidate.validateName(name);
        };
        $scope.checkErrorName = function (name, type) {
            if (name && name.trim().length > 0) $scope.errorFields[type] = false;
            else $scope.errorFields[type] = true;
        };
        $scope.resetErrorField = function (name, type) {
            $scope.errorFields[type] = false;
        };
        $scope.cancel = function () {
            $location.path('/contacts/' + $stateParams.contactId);
        };
        Client.all(Client.searchOptions(), function (response) {
            $scope.clients = response.objects;
        });
        $rootScope.loading = true;
        Contacts.one(
            { localId: $scope.contactId },
            function (resp) {
                $rootScope.loading = false;
                if (angular.equals(resp.status, 'ok') && angular.equals(resp.object.status, 'A')) {
                    $scope.contact = resp.object;
                    if (resp.object.sex === true) $scope.contact.sex = 'male';
                    if (resp.object.sex === false) $scope.contact.sex = 'female';
                    $scope.client = resp.object.clientId;
                    $location
                        .hash($filter('transliteration')(resp.object.firstName + '_' + resp.object.lastName))
                        .replace();
                    if (resp.object.contacts) {
                        angular.forEach(resp.object.contacts, function (val) {
                            if (angular.equals(val.type, 'email')) {
                                $scope.contacts.email = val.value;
                            } else if (angular.equals(val.type, 'mphone')) {
                                $scope.contacts.mphone = val.value;
                            } else if (angular.equals(val.type, 'skype')) {
                                $scope.contacts.skype = val.value;
                            } else if (angular.equals(val.type, 'facebook')) {
                                $scope.contacts.facebook = val.value;
                            } else if (angular.equals(val.type, 'homepage')) {
                                $scope.contacts.homepage = val.value;
                            } else if (angular.equals(val.type, 'linkedin')) {
                                $scope.contacts.linkedin = val.value;
                            }
                        });
                    }
                } else {
                    notificationService.error($filter('translate')('contact not found'));
                    $location.path('/contacts');
                }
            },
            function (error) {
                console.error(error);
                $rootScope.loading = false;
            },
        );
        $scope.validateFields = function () {
            $scope.errorFields.firstName = !$scope.contact.firstName;
            $scope.errorFields.lastName = !$scope.contact.lastName;
        };
        $scope.save = function () {
            $scope.validateFields();
            let noOneContact = !(
                $scope.contacts.mphone ||
                $scope.contacts.email ||
                $scope.contacts.skype ||
                $scope.contacts.linkedin ||
                $scope.contacts.facebook ||
                $scope.contacts.homepage
            );

            if (!noOneContact && !Object.values($scope.errorFields).includes(true)) {
                var contacts = [];
                if ($scope.contacts.mphone) {
                    contacts.push({
                        type: 'mphone',
                        value: $scope.contacts.mphone,
                    });
                }
                if ($scope.contacts.skype) {
                    contacts.push({
                        type: 'skype',
                        value: $scope.contacts.skype,
                    });
                }
                if ($scope.contacts.email) {
                    contacts.push({
                        type: 'email',
                        value: $scope.contacts.email,
                    });
                }
                if ($scope.contacts.facebook) {
                    contacts.push({
                        type: 'facebook',
                        value: $scope.contacts.facebook,
                    });
                }
                if ($scope.contacts.homepage) {
                    contacts.push({
                        type: 'homepage',
                        value: $scope.contacts.homepage,
                    });
                }
                if ($scope.contacts.linkedin) {
                    contacts.push({
                        type: 'linkedin',
                        value: $scope.contacts.linkedin,
                    });
                }
                $scope.contact.contacts = contacts;
                const payload = { ...$scope.contact };
                if ($scope.contact.sex === 'male') payload.sex = true;
                if ($scope.contact.sex === 'female') payload.sex = false;
                Contacts.onEdit(payload)
                    .then(() => {
                        notificationService.success($filter('translate')('contact save'));
                        $location.path('/contacts/' + $scope.contact.localId);
                    })
                    .catch((err) => {
                        notificationService.error(err.message);
                    })
                    .finally(() => {
                        $scope.$apply();
                    });
            } else {
                if (noOneContact)
                    notificationService.error($translate.instant('Please fill at least one of the contacts fields'));
                else notificationService.error($translate.instant('Contact is filled incorrectly'));
            }
        };

        $rootScope.changeStateInContact = { status: '', fullName: null };
        $scope.delete = function () {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                windowClass: 'secondary-modal',
                templateUrl: '../partials/modal/contact-remove-confirmation.html',
                size: '',
                resolve: function () {},
            });
            $rootScope.changeStateInContact.fullName = $scope.contact.firstName;
        };

        $rootScope.saveStatusOfContact = function () {
            $rootScope.clickedSaveStatusOfContact = true;
            $scope.contact.status = 'R';
            let payload = { ...$scope.contact };
            if (payload.sex === 'male') payload.sex = true;
            if (payload.sex === 'female') payload.sex = false;
            Contacts.onEdit(payload)
                .then(() => {
                    notificationService.success($filter('translate')('Contact has been deleted'));
                    $location.path('/clients/' + $scope.contact.clientId.localId);
                })
                .catch((err) => {
                    notificationService.error(err.message);
                })
                .finally(() => {
                    $rootScope.clickedSaveStatusOfContact = false;
                    $rootScope.closeModal();
                    $scope.$apply();
                });
        };
        $rootScope.closeModal = function () {
            $scope.modalInstance.close();
        };
    },
]);
