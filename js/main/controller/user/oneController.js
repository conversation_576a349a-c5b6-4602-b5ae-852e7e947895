component.component('user', {
    templateUrl: 'partials/user.html',
    controller: function (
        $scope,
        tmhDynamicLocale,
        Person,
        $rootScope,
        $stateParams,
        Vacancy,
        $location,
        $translate,
        Candidate,
        Service,
        notificationService,
        $filter,
        googleService,
        $http,
        serverAddress,
        Client,
        Company,
        vacancyStages,
        Action,
        $sce,
        $uibModal,
        Mailing,
        $timeout,
        ngTableParams,
        $anchorScroll,
        Test,
        RegionInputService,
        CandidatesSlider,
        $state,
        Account,
        $window,
    ) {
        $scope.serverAddress = serverAddress;
        $rootScope.persons = [];
        $scope.showChangePassword = false;
        $scope.isAllPasswordInputsFilled = false;
        $scope.isAllNameInputsFilled = false;
        $scope.isErrorDisplayed = false;
        $scope.passwordError = '';
        $scope.oldPassword = '';
        $scope.newPassword = '';
        $scope.repeatNewPassword = '';
        $scope.showChangeOrgName = false;
        $scope.showChangeRole = false;
        $scope.showChangeRegion = false;
        $scope.showChangeGender = false;
        $scope.showChangeGmail = false;
        $scope.showChangeContacts = false;
        $scope.editNamePermission = true;
        $rootScope.changedFirstName = ' ';
        $rootScope.changedLastName = ' ';
        $scope.contacts = {};
        $scope.allVacancyInUser = [];
        $scope.photoId = null;
        $scope.hideMailingService = false;
        $scope.elementsForGender = ['male', 'female'];
        $scope.blockDeletingCandidates = false;
        $scope.usePhotoLink =
            !$rootScope.me.personParams.dontUsePhotoLink || $rootScope.me.personParams.dontUsePhotoLink === 'false';
        $rootScope.closeModal = function () {
            $scope.modalInstance.close();
        };
        $scope.closeModal = function () {
            $scope.modalInstance.close();
        };

        Service.onGetSAMLSettings().then((data) => {
            if (data.status === 'ok') {
                $scope.editNamePermission = !data.object;
            }
        });

        $scope.showChangeCompanyOfInvitedClient = false;
        vacancyStages.get(
            function (resp) {
                $scope.customStages = resp.object ? resp.object.interviewStates : null;
            },
            (err) => '',
        );
        let oldContacts = {};
        const historyRequestPrams = {};

        $scope.setNewPersonsParam = function (name, value) {
            const req = {
                method: 'POST',
                url: '/hr/person/changePersonParamHm',
                data: { userId: $scope.user.userId, name, value },
            };
            $http(req).then(function (resp) {
                if (!$scope.user.personParams) {
                    $scope.user.personParams = {};
                }
                $scope.user.personParams[name] = value;
                if ($scope.user.login == $rootScope.me.login) {
                    $rootScope.me.personParams[name] = value;
                }
                $scope.tableParams.reload();
            });
        };

        $scope.setPersonParam = function (name, value, callback) {
            Person.onChangeUserParam({
                userId: $scope.user.userId,
                name,
                value,
            })
                .then((resp) => {
                    if (!$scope.user.personParams) {
                        $scope.user.personParams = {};
                    }
                    $scope.user.personParams[name] = value;
                    if (callback != undefined) callback();
                })
                .catch((err) => notificationService.error(err.message))
                .finally(() => $rootScope.$$phase || $scope.$apply());
        };

        $scope.enableViewClients = function (user) {
            if (user.personParams.clientAccessLevel === 'full' || !user.personParams.clientAccessLevel) {
                $scope.setPersonParam('clientAccessLevel', 'hide');
            } else {
                $scope.setPersonParam('clientAccessLevel', 'full');
            }
        };

        $scope.enableViewAttachments = function (user) {
            if (user.personParams.showAttachmentsHm === 'N' || !user.personParams.showAttachmentsHm) {
                $scope.setNewPersonsParam('showAttachmentsHm', 'Y');
            } else {
                $scope.setNewPersonsParam('showAttachmentsHm', 'N');
            }
        };

        $scope.enableViewSalary = function (user) {
            if (user.personParams.showSalaryHm === 'N' || !user.personParams.showSalaryHm) {
                $scope.setNewPersonsParam('showSalaryHm', 'Y');
            } else {
                $scope.setNewPersonsParam('showSalaryHm', 'N');
            }
        };

        $scope.disableViewClients = function () {
            $scope.setPersonParam('clientAccessLevel', 'hide');
        };

        $scope.changeUserClientRole = function (name, clientId) {
            $scope.setPersonParam(name, clientId, function () {
                Person.getPerson({ userId: $stateParams.id }, function (resp) {
                    $scope.user = resp;
                    $scope.showChangeCompanyOfInvitedClient = false;
                });
            });
        };
        $('.ui.dropdown').dropdown();
        Person.getPerson({ userId: $stateParams.id }, function (resp) {
            if (resp.status == 'ok') {
                $rootScope.editEmailTemplatesValue = resp.object.personParams.editDefaultTemplate
                    ? resp.object.personParams.editDefaultTemplate
                    : 'Y';
                $rootScope.editAutoActionValue = resp.object.personParams.manageAutoAction
                    ? resp.object.personParams.manageAutoAction
                    : 'Y';
                $rootScope.editEmailTemplatesName = 'editDefaultTemplate';
                $rootScope.editAutoActionName = 'manageAutoAction';
                $scope.user = resp.object;

                $scope.blockDeletingCandidates =
                    $scope.user.personParams.hasOwnProperty('blockDeletingCandidates') &&
                    $scope.user.personParams?.blockDeletingCandidates === 'Y';

                $scope.elementForGender = !$scope.user.sex;
                $rootScope.genderFlag = $scope.user.sex;
                $rootScope.userRole = resp.object.roles[0].id;
                if ($rootScope.me.personId === $scope.user.personId) {
                    $state.current.data.title = $filter('translate')('My profile in CleverStaff');
                } else $state.current.data.title = $scope.user.fullName + ' ' + $filter('translate')('in CleverStaff');
                getRefererUser();
                getLogoById($scope.user.userId);
                if ($scope.user.recrutRole == 'admin') {
                    $scope.user.personParams.enableDownloadToExcel =
                        !$scope.user.personParams.enableDownloadToExcel ||
                        $scope.user.personParams.enableDownloadToExcel == 'Y'
                            ? true
                            : false;
                } else if ($scope.user.recrutRole == 'recruter') {
                    $scope.user.personParams.enableDownloadToExcel =
                        $scope.user.personParams.enableDownloadToExcel &&
                        $scope.user.personParams.enableDownloadToExcel == 'Y'
                            ? true
                            : false;
                }

                $rootScope.changedFirstName = resp.object.firstName;
                $rootScope.changedLastName = resp.object.lastName;
                $scope.getMyVacancy = function () {
                    Vacancy.all(
                        {
                            page: {
                                number: 0,
                                count: 120,
                            },
                            personId: $scope.user.userId,
                            sortOrder: 'DESC',
                        },
                        function (response) {
                            $scope.allVacancyInUser = response.objects;
                        },
                    );
                };
                $scope.getMyVacancy();
                angular.forEach($scope.user.personParams, function (value, key) {
                    angular.forEach($scope.sendNotificationObj, function (res) {
                        if (res.name == key) {
                            res.value = value;
                        }
                    });
                });
                if ($rootScope.errorMessageType === 'inviteBlockUser') {
                    $rootScope.errorMessageType = null;
                    $scope.errorMessage =
                        $scope.user.fullName +
                        ' (' +
                        $scope.user.login +
                        ') ' +
                        $filter('translate')(
                            'has already been in your account and now he (she) is disabled. Here you can enable access for him (her)',
                        );
                }
                $scope.newRole = resp.object.recrutRole;
                $scope.statisticObj = {
                    requestObj: { creator: resp.userId },
                    objId: resp.userId,
                    objType: 'user',
                };

                if (resp.object.region && resp.object.region.googlePlaceId)
                    $timeout(() => RegionInputService.setInstance('1', resp.object.region.googlePlaceId));

                if (resp.object.contacts) {
                    angular.forEach(resp.object.contacts, function (val) {
                        if (angular.equals(val.contactType, 'mphone')) {
                            $scope.contacts.mphone = val.value;
                        }
                        if (angular.equals(val.contactType, 'phoneWork')) {
                            $scope.contacts.phoneWork = val.value;
                        }
                        if (angular.equals(val.contactType, 'skype')) {
                            $scope.contacts.skype = val.value;
                        }
                        if (angular.equals(val.contactType, 'linkedin')) {
                            $scope.contacts.linkedin = val.value;
                        }
                        if (angular.equals(val.contactType, 'facebook')) {
                            $scope.contacts.facebook = val.value;
                        }
                        // if (angular.equals(val.contactType, 'googleplus')) {
                        //     $scope.contacts.googleplus = val.value;
                        // }
                        if (angular.equals(val.contactType, 'homepage')) {
                            $scope.contacts.homepage = val.value;
                        }
                    });
                    oldContacts = angular.copy($scope.contacts);
                }
            } else if (resp.code == 'notFound') {
                notificationService.error($filter('translate')('User not found'));
                $location.path('company/users');
            } else if (resp.code === 'notAccess') {
                document.querySelector('.controller-view').style.display = 'none';
                const modalInstance = $uibModal.open({
                    animate: true,
                    templateUrl: '../partials/modal/user-not-available.html',
                    windowClass: 'error-handler-for-modal',
                });

                modalInstance.result.then(
                    function () {
                        window.history.back();
                    },
                    function () {
                        window.history.back();
                    },
                );

                $rootScope.closeModal = function () {
                    modalInstance.close();
                };
            }

            $rootScope.$$phase || $scope.$apply();
        });
        let pageNumber = 0;
        let pageCount = 15;
        $scope.tableParams = new ngTableParams(
            {
                page: 1,
                count: 15,
            },
            {
                total: 0,
                getData: function ($defer, params) {
                    function getHistory(page, count) {
                        historyRequestPrams.ignoreType = [
                            'candidate_profile_updated_wandify',
                            'candidate_contacts_updated_wandify',
                            'candidate_profile_not_updated_wandify',
                            'candidate_contacts_not_updated_wandify',
                            'candidate_add_from_jobCannon',
                            'candidate_update_from_jobCannon',
                            'sent_candidate_to_client',
                            'test_sent_after_recall',
                            'candidate_test_passed',
                            'test_result_auto_sent',
                            'removed_test_and_disabled_auto_sending_on_vacancy',
                            'interview_add_from_advice_v1',
                            'candidate_remove_candidate_link',
                            'candidate_autoaction_test_send',
                            'candidate_autoaction_mail_send',
                            'candidate_autoaction_mail_send_failed',
                            'candidate_autoaction_test_send_failed',
                            'gpt_candidate_edit',
                        ];
                        historyRequestPrams.personId = $stateParams.id;
                        if (page || count) {
                            pageNumber = page;
                            pageCount = count;
                            historyRequestPrams.page = {
                                number: pageNumber,
                                count: pageCount,
                            };
                        } else {
                            pageNumber = params.$params.page;
                            pageCount = params.$params.count;
                            historyRequestPrams.page = {
                                number: pageNumber - 1,
                                count: pageCount,
                            };
                            $scope.isShowMore = false;
                            if (document.getElementById('scrollup'))
                                document.getElementById('scrollup').style.display = 'none';
                            $timeout(function () {
                                $anchorScroll('mainTable');
                            });
                        }
                        $rootScope.loading = true;
                        Service.history(
                            historyRequestPrams,
                            function (response) {
                                response.objects.forEach((action) => {
                                    action.descr = action.descr.replaceAll('&nbsp;', ' ');
                                    action.descr = action.descr.replaceAll('rel="nofollow"', '');
                                    $rootScope.initTagsStyles(action);
                                });
                                $rootScope.loading = false;
                                if (page) {
                                    $scope.history = $scope.history.concat(response['objects']);
                                } else {
                                    $scope.history = response['objects'];
                                }
                                $scope.history.forEach((item) => {
                                    if (item.type === 'task_change_status') {
                                        if (item.stateNew === 'open') item.stateNew = 'inwork';
                                        if (item.stateOld === 'open') item.stateOld = 'inwork';
                                    }

                                    if (item.type === 'set_interview_status') {
                                        item.stagesArray = item.stateNew.split(',').map((state) => ({ value: state }));
                                    }

                                    if (
                                        item.type === 'candidate_merge_group' ||
                                        item.type === 'merge_group' ||
                                        item.type === 'edit_group' ||
                                        item.type === 'candidate_edit_group'
                                    )
                                        item.descr = JSON.parse(item.descr);

                                    if (item.type === 'scorecard_assign' || item.type === 'delete_scorecard_assign') {
                                        if (item.scoreCardNames) {
                                            item.cards = JSON.parse(item.scoreCardNames);
                                        } else {
                                            item.cards = [item.descr];
                                        }
                                    }
                                });

                                angular.forEach($scope.history, function (value) {
                                    if (value.stateNew || value.stateOld) {
                                        if ($scope.customStages) {
                                            $scope.customStages.forEach((stage) => {
                                                if (value.type === 'set_interview_status') {
                                                    value.stagesArray.forEach((originalStage) => {
                                                        if (originalStage.value === stage.customInterviewStateId) {
                                                            originalStage['customInterviewStateId'] =
                                                                stage.customInterviewStateId;
                                                            originalStage.value = stage.value;
                                                        }
                                                    });
                                                } else {
                                                    if (stage.customInterviewStateId === value.stateOld) {
                                                        value.customStateOld = value.stateOld;
                                                        value.stateOld = stage.type;
                                                    }
                                                    if (stage.customInterviewStateId === value.stateNew) {
                                                        value.customStateNew = value.stateNew;
                                                        value.stateNew = stage.type;
                                                    }
                                                }
                                            });
                                        }
                                    } else if (value.data && value.type == 'add_custom_interview_state') {
                                        value.newName = JSON.parse(value.data).new_name;
                                    }
                                    if (value.customInterviewStates) {
                                        $scope.customStages.forEach((stage) => {
                                            value.customInterviewStates.forEach((custom) => {
                                                if (stage.customInterviewStateId === custom.customInterviewStateId) {
                                                    custom.stateNew = stage.type;
                                                }
                                            });
                                        });
                                    }
                                });

                                $scope.objectSize = response['objects'] ? response['total'] : 0;
                                params.total(response['total']);
                                $scope.paginationParams = {
                                    currentPage: historyRequestPrams.page.number,
                                    totalCount: $scope.objectSize,
                                    totalPages: response.allPageCount,
                                };
                                if (response['allPageCount'] === historyRequestPrams.page.number + 1) {
                                    $('#show_more').hide();
                                } else {
                                    $('#show_more').show();
                                }
                                $defer.resolve($scope.history);
                                $rootScope.loading = false;
                                $scope.displayShowMore = true;
                            },
                            function (err) {
                                $rootScope.loading = false;
                                notificationService.error(`ActionGet response status: ${err.status}`);
                            },
                        );
                    }
                    setTimeout(() => {
                        getHistory();
                    }, 0);

                    $scope.showMore = function () {
                        $scope.isShowMore = true;
                        $scope.displayShowMore = Service.dynamicTableLoading(
                            params.total(),
                            historyRequestPrams.page.number,
                            historyRequestPrams.page.count,
                            getHistory,
                        );
                    };

                    $scope.previousSearchNumber = $scope.tableParams.page();
                },
            },
        );

        $scope.changePage = (pageNumber) => {
            if ($scope.isShowMore) {
                $scope.tableParams.page($scope.previousSearchNumber);
            } else $scope.tableParams.page(pageNumber);

            $scope.tableParams.reload();
        };

        $scope.changeAmountOfElements = (amount) => {
            if ($scope.tableParams.count() === amount) return;
            $scope.tableParams.count(amount);
            $scope.tableParams.reload();
        };

        $scope.toScorecard = function (value) {
            let stateParams = {
                scoreCardId: value.scoreCardId,
                candidateObj: value.candidate,
                id: value.candidate.localId,
                isFromVacancyToCandidate: value.vacancyId,
                isFromVacancyToEvaluate: true,
                sliderDataId: $scope.sliderId,
                vacancyName: value.vacancy ? value.vacancy.position : null,
                vacancyId: value.vacancyId,
            };

            let link = $state.href('candidate-slide', stateParams, {
                reload: true,
            });
            window.open(link, '_blank');
        };

        $scope.toAllVacancy = function () {
            $rootScope.usernameThatIsSearching = $scope.user.fullName;
            Vacancy.setOptions('personId', $scope.user.userId);
            $location.path('/vacancies');
        };
        $rootScope.$on('$translateChangeSuccess', () => {
            if ($rootScope.me.personId === $scope.user.personId) {
                $state.current.data.title = $filter('translate')('My profile in CleverStaff');
            } else $state.current.data.title = $scope.user.fullName + ' ' + $filter('translate')('in CleverStaff');
        });
        $scope.toAllCandidates = function () {
            $rootScope.usernameThatIsSearching = $scope.user.fullName;
            $rootScope.userIdThatIsSearching = $scope.user.userId;
            Candidate.setOptions('personId', $scope.user.userId);
            $location.path('/candidates');
        };
        $scope.changeLanguage = function (key) {
            $translate.use(key);
            tmhDynamicLocale.set(key);
            Person.setLang({ lang: key });
        };
        $scope.saveNewRole = function (val, confirmed, old) {
            const oldRole = old;
            if (
                (val !== undefined && val !== $scope.user.recrutRole && val != 'client') ||
                (val == 'client' && confirmed) ||
                val != 'client'
            ) {
                $rootScope.loading = true;
                $scope.newRole = val;
                Person.changeUserRole(
                    {
                        personId: $scope.user.personId,
                        userId: $scope.user.userId,
                        role: $scope.newRole,
                    },
                    function (resp) {
                        if (resp.status && angular.equals(resp.status, 'error')) {
                            $rootScope.loading = false;
                            resp.message = $filter('translate')(resp.message);
                            $scope.newRole = oldRole;
                            notificationService.error(resp.message);
                        } else {
                            Person.getPerson({ userId: $stateParams.id }, function (resp) {
                                $rootScope.loading = false;
                                $scope.user = resp.object;
                                if ($scope.newRole == resp.object.recrutRole) {
                                    $scope.user.recrutRole = $scope.newRole;
                                    var roleName =
                                        $scope.newRole == 'admin'
                                            ? 'Admin'
                                            : $scope.newRole == 'client'
                                            ? 'Hiring Manager'
                                            : $scope.newRole == 'freelancer'
                                            ? 'Freelancer'
                                            : $scope.newRole == 'recruter'
                                            ? 'Recruter'
                                            : $scope.newRole == 'researcher'
                                            ? 'Researcher'
                                            : 'Researcher';
                                    var message =
                                        $filter('translate')('You has granted role') +
                                        ' ' +
                                        roleName +
                                        ' ' +
                                        $filter('translate')('_for') +
                                        ' ' +
                                        $scope.user.firstName;
                                    $rootScope.updateMe(true);
                                    notificationService.success(message);
                                    $scope.getLastEvent();
                                    if (
                                        $scope.user.personParams &&
                                        $scope.user.personParams.enableDownloadToExcel === 'Y'
                                    ) {
                                        $scope.user.personParams.enableDownloadToExcel = true;
                                    } else {
                                        $scope.user.personParams.enableDownloadToExcel = false;
                                    }
                                }
                            });
                        }
                    },
                    function (error) {
                        console.error(error);
                    },
                );
                $scope.showChangeRole = false;
            } else if (!confirmed) {
                $scope.modalInstance = $uibModal.open({
                    animation: true,
                    windowClass: 'secondary-modal',
                    templateUrl: '../partials/modal/change-role-warning.html',
                    resolve: {},
                });
            } else {
                $scope.showChangeRole = false;
            }
        };
        $rootScope.saveNewRole = $scope.saveNewRole;
        $scope.inputValue = '';
        $scope.removeRegion = function () {
            Person.changeRegion(
                {
                    personId: $scope.user.personId,
                    lang: $translate.use(),
                },
                () => {
                    $scope.user.region = null;
                    $('#s2id_1_country_container').select2('val', '');
                    $('#s2id_1_city_container').select2('val', '');
                    $('#s2id_1_city_container').select2('destroy');
                    notificationService.success($filter('translate')('Region was removed'));
                },
            );
        };

        $scope.closeChangeRegion = function () {
            if ($scope.user.region?.googlePlaceId) {
                RegionInputService.setInstance('1', $scope.user.region.googlePlaceId);
                if (!$scope.user.region.googlePlaceId.cityEn) {
                    $('#s2id_1_city_container').select2('val', '');
                    $('#s2id_1_city_container').select2('destroy');
                }
            } else {
                $('#s2id_1_country_container').select2('val', '');
                $('#s2id_1_city_container').select2('val', '');
                $('#s2id_1_city_container').select2('destroy');
            }
            $scope.showChangeRegion = false;
        };

        $scope.saveNewRegion = function () {
            const countryData = RegionInputService.getInstance('1', 'country').getValue();
            const countryAndCityData = RegionInputService.getInstance('1', 'city').getValue();

            if (countryAndCityData) {
                $scope.region = countryAndCityData;
            } else if (countryData) {
                $scope.region = { country: countryData.text };
            } else {
                if ($('#pac-input').val().length > 0 && $scope.region) {
                    $scope.region.fromGoogle = true;
                }
            }
            if ($scope.region) {
                Person.changeRegion(
                    {
                        personId: $scope.user.personId,
                        region: $scope.region,
                        lang: $translate.use(),
                    },
                    function (resp) {
                        if (resp.status && angular.equals(resp.status, 'error')) {
                            notificationService.error(resp.message);
                        } else {
                            $scope.user.region = $scope.region;
                            $scope.user.region.displayFullName =
                                $scope.user.region.city + ', ' + $scope.user.region.country;
                            $scope.region = null;

                            RegionInputService.setInstance('1', resp.object.googlePlaceId);
                            $scope.user.region.googlePlaceId = resp.object.googlePlaceId;

                            notificationService.success($filter('translate')('region change'));
                            $rootScope.$$phase || $scope.$apply();
                        }
                    },
                    function (error) {
                        console.error(error);
                    },
                );
                $scope.showChangeRegion = false;
            } else {
                if ($scope.user.region != undefined) {
                    Person.changeRegion(
                        {
                            personId: $scope.user.personId,
                            lang: $translate.use(),
                        },
                        function (resp) {
                            if (resp.status && angular.equals(resp.status, 'error')) {
                                notificationService.error(resp.message);
                            } else {
                                $scope.user.region = undefined;
                                notificationService.success($filter('translate')('region change'));
                            }
                        },
                        function (error) {
                            console.error(error);
                        },
                    );
                }
                $scope.showChangeRegion = false;
            }
        };
        $scope.saveNewGender = function (gender) {
            Person.changeSex(
                { sex: !gender },
                function () {
                    $rootScope.me.sex = !gender;
                    $rootScope.genderFlag = !gender;
                },
                function (error) {
                    console.error(error);
                },
            );
        };

        const displayPasswordError = function (message) {
            $scope.isErrorDisplayed = true;
            $scope.passwordError = message;
            $('.error-password').removeClass('hidden');
            if (message === 'Incorrect current password') {
                $('input[id*="oldPass"]').css({ border: '1px solid red' });
            } else {
                $('input[id*="newPass"]').each((_, pass) => {
                    $(pass).css({ border: '1px solid red' });
                });
            }
        };

        $scope.togglePassword = function (e) {
            const targetEl = $(e.target).closest('.user-toggle-pass');
            targetEl.toggleClass('change');
            if (targetEl.prev().attr('type') === 'password') targetEl.prev().attr('type', 'text');
            else targetEl.prev().attr('type', 'password');
        };

        $scope.checkPasswordFields = function () {
            if ($scope.isErrorDisplayed) {
                $scope.isErrorDisplayed = false;
                $('input[id*="Pass"]').each((_, pass) => {
                    $(pass).css({
                        border: '1px solid #cccccc',
                        'background-color': '#fff',
                    });
                });
            }
            $scope.isAllPasswordInputsFilled = $scope.oldPassword && $scope.newPassword && $scope.repeatNewPassword;
        };

        $scope.closeChangePassword = function () {
            $scope.showChangePassword = false;
            $scope.isErrorDisplayed = false;
            $scope.isAllPasswordInputsFilled = false;
            $scope.oldPassword = '';
            $scope.newPassword = '';
            $scope.repeatNewPassword = '';
            $('input[id*="Pass"]').each((_, pass) => {
                $(pass).css({
                    border: '1px solid #cccccc',
                    'background-color': '#fff',
                });
                $(pass).attr('type', 'password');
            });
            $('.user-toggle-pass').each((_, btn) => {
                $(btn).removeClass('change');
            });
            $('.error-password').addClass('hidden');
        };

        $scope.changePassword = function () {
            const digits = /.*\d.*/;
            const letters = /.*[a-zA-Z].*/;

            if ($scope.newPassword.length < 8) {
                displayPasswordError('Password must be 8-30 characters long');
                return;
            }

            if ($scope.newPassword !== $scope.repeatNewPassword) {
                displayPasswordError("The password doesn't match to previous");
                return;
            }

            if (!digits.test($scope.newPassword)) {
                displayPasswordError('Password should contain at least one number');
                return;
            }

            if (!letters.test($scope.newPassword)) {
                displayPasswordError('Password should contain at least one latin letter');
                return;
            }

            Person.changePassword(
                {
                    oldPass: $scope.oldPassword,
                    newPass: $scope.newPassword,
                },
                function (resp) {
                    if (resp.status && angular.equals(resp.status, 'error')) {
                        displayPasswordError('Incorrect current password');
                    } else {
                        notificationService.success($filter('translate')('password_changed'));
                        $scope.closeChangePassword();
                    }
                },
                function (error) {
                    console.error(error);
                },
            );
        };

        $scope.updateContacts = function () {
            var contacts = [];
            if ($scope.contacts.mphone) {
                contacts.push({
                    contactType: 'mphone',
                    value: $scope.contacts.mphone,
                });
            }
            if ($scope.contacts.phoneWork) {
                contacts.push({
                    contactType: 'phoneWork',
                    value: $scope.contacts.phoneWork,
                });
            }
            if ($scope.contacts.skype) {
                contacts.push({
                    contactType: 'skype',
                    value: $scope.contacts.skype,
                });
            }
            if ($scope.contacts.linkedin) {
                contacts.push({
                    contactType: 'linkedin',
                    value: $scope.contacts.linkedin,
                });
            }
            if ($scope.contacts.facebook) {
                contacts.push({
                    contactType: 'facebook',
                    value: $scope.contacts.facebook,
                });
            }
            // if ($scope.contacts.googleplus) {
            //     contacts.push({
            //         contactType: 'googleplus',
            //         value: $scope.contacts.googleplus,
            //     });
            // }
            if ($scope.contacts.homepage) {
                contacts.push({
                    contactType: 'homepage',
                    value: $scope.contacts.homepage,
                });
            }
            if (
                ($('#phoneNumber').val() == $scope.contacts.mphone ||
                    ($('#phoneNumber').val() == '' && $scope.contacts.mphone == undefined)) &&
                ($('#workNumber').val() == $scope.contacts.phoneWork ||
                    ($('#workNumber').val() == '' && $scope.contacts.phoneWork == undefined))
            ) {
                Person.updateContacts(
                    contacts,
                    function (resp) {
                        if (resp.status && angular.equals(resp.status, 'error')) {
                            notificationService.error(resp.message);
                        } else {
                            notificationService.success($filter('translate')('contacts_saved'));
                            $scope.showChangeContacts = false;
                            $rootScope.me.contacts = contacts;
                            oldContacts = angular.copy($scope.contacts);
                            Person.getMe(function (response) {
                                $rootScope.me = response.object;
                            });
                        }
                    },
                    function (error) {
                        console.error(error);
                    },
                );
            } else {
                notificationService.error($filter('translate')('Incorrect phone number'));
            }
        };

        $scope.cancelUpdateContacts = function () {
            $scope.contacts = angular.copy(oldContacts);
            $scope.showChangeContacts = false;
        };

        $scope.showChangeUserOrgName = function () {
            $scope.showChangeOrgName = true;
            $scope.newOrgName = $rootScope.me.orgName;
        };

        $scope.changeOrgName = function () {
            Person.changeOrgName(
                { orgName: $scope.newOrgName, lang: $translate.use() },
                function (resp) {
                    if (resp.status && angular.equals(resp.status, 'error')) {
                        notificationService.error(resp.message);
                    } else {
                        $rootScope.me.orgName = $scope.newOrgName;
                        $scope.user.orgName = $scope.newOrgName;
                        $scope.showChangeOrgName = false;
                        angular.forEach($rootScope.me.orgs, function (org) {
                            if (org.orgId == $rootScope.me.orgId) {
                                org.orgName = $scope.newOrgName;
                            }
                        });
                        angular.forEach($rootScope.orgs, function (org) {
                            if (org.orgId == $rootScope.me.orgId) {
                                org.orgName = $scope.newOrgName;
                            }
                        });

                        $scope.getLastEvent();
                    }
                },
                function (error) {
                    console.error(error);
                },
            );
        };

        $scope.changeUserStatus = function () {
            if ($scope.user.status === 'A') {
                $scope.disableUser();
            } else if ($scope.user.status === 'N') {
                $scope.enableUser();
            }
        };

        $scope.getLastEvent = function () {
            Service.history(
                {
                    page: { number: 0, count: 1 },
                    personId: $stateParams.id,
                },
                function (res) {
                    if (res.status == 'ok') $scope.history.unshift(res.objects[0]);
                },
                function (error) {},
            );
        };

        $scope.getVacancyById = function (id) {
            return $scope.allVacancyInUser.filter((vacancy) => {
                return vacancy.vacancyId === id;
            })[0];
        };

        $scope.twoFactorAuthorizationHandler = function (isTfaEnable) {
            if (isTfaEnable) {
                $scope.enableTwoFactorAuthorization();
            } else {
                $scope.disableTwoFactorAuthorization();
            }
        };

        $scope.disableTwoFactorAuthorization = function () {
            const isMe = $rootScope.me.userId === $scope.user.userId;

            class disableTwoFactorAuthorization {
                constructor(
                    Service,
                    notificationService,
                    $uibModalInstance,
                    $rootScope,
                    $scope,
                    $uibModal,
                    $translate,
                ) {
                    this.globalService = Service;
                    this.notificationService = notificationService;
                    this.$uibModalInstance = $uibModalInstance;
                    this.$rootScope = $rootScope;
                    this.$scope = $scope;
                    this.$uibModal = $uibModal;
                    this.$translate = $translate;
                }

                closeModal() {
                    this.$uibModalInstance.close();
                }

                cancelChanges() {
                    this.closeModal();
                    $scope.user.tfaEnabled = true;
                }

                disable2fa() {
                    this.$rootScope.loading = true;

                    if (this.isMe) {
                        this.globalService
                            .onDisable2fa()
                            .then(() => {
                                $scope.user.tfaEnabled = false;
                                this.notificationService.success($filter('translate')('Changes are saved'));
                            })
                            .catch((error) => {
                                console.error(error.message || error.statusText);
                            })
                            .finally(() => {
                                this.closeModal();
                                this.$rootScope.loading = false;
                                this.$rootScope.$$phase || this.$scope.$apply();
                            });
                    } else {
                        this.globalService
                            .onDisableForUser({ userId: $scope.user.userId })
                            .then(() => {
                                $scope.user.tfaEnabled = false;
                                this.notificationService.success($filter('translate')('Changes are saved'));
                            })
                            .catch((error) => {
                                console.error(error.message || error.statusText);
                            })
                            .finally(() => {
                                this.closeModal();
                                this.$rootScope.loading = false;
                                this.$rootScope.$$phase || this.$scope.$apply();
                            });
                    }
                }

                $onInit() {
                    this.isMe = isMe;
                }
            }

            const modalInstance = $uibModal.open({
                templateUrl: './partials/modal/two-factor-authorization/disable-two-factor-authorization-modal.html',
                controller: [
                    'Service',
                    'notificationService',
                    '$uibModalInstance',
                    '$rootScope',
                    '$scope',
                    '$uibModal',
                    '$translate',
                    disableTwoFactorAuthorization,
                ],
                controllerAs: 'vm',
                backdrop: 'false',
                resolve: {},
            });
        };

        $scope.enableTwoFactorAuthorization = function () {
            const isMe = $rootScope.me.userId === $scope.user.userId;

            if (isMe) {
                $rootScope.loading = true;
                Service.onGenerate2faQR()
                    .then((resp) => {
                        firstStageTwoFactorAuthorizationModal(resp);
                    })
                    .catch((error) => {
                        console.error(error.message || error.statusText);
                    })
                    .finally(() => {
                        $rootScope.loading = false;
                        $rootScope.$$phase || $scope.$apply();
                    });
            } else {
                notificationService.error($filter('translate')('Access is denied'));
                $scope.user.tfaEnabled = false;
            }
        };

        function firstStageTwoFactorAuthorizationModal({ tfaCode, img }) {
            if (!tfaCode || !img) return;

            class AuthorizationQRCode {
                constructor(Service, notificationService, $uibModalInstance, $scope, $uibModal, $translate) {
                    this.copyToClipboard = Service.copyToClipboard;
                    this.notificationService = notificationService;
                    this.$uibModalInstance = $uibModalInstance;
                    this.$scope = $scope;
                    this.$uibModal = $uibModal;
                    this.$translate = $translate;
                }

                closeModal() {
                    this.$uibModalInstance.close();
                }

                copyCode() {
                    this.copyToClipboard(this.tfaCode);
                    this.notificationService.success(this.$translate.instant('The code has been copied'));
                }

                nextStep() {
                    this.closeModal();

                    confirmStageTwoFactorAuthorizationModal();
                }

                $onInit() {
                    this.tfaCode = tfaCode;
                    this.img = img;
                }
            }

            const modalInstance = $uibModal.open({
                templateUrl:
                    './partials/modal/two-factor-authorization/first-stage-two-factor-authorization-modal.html',
                windowClass: 'two-factor-authorization-wrapper',
                controller: [
                    'Service',
                    'notificationService',
                    '$uibModalInstance',
                    '$scope',
                    '$uibModal',
                    '$translate',
                    AuthorizationQRCode,
                ],
                controllerAs: 'vm',
                backdrop: 'false',
                resolve: {},
            });
        }

        function confirmStageTwoFactorAuthorizationModal() {
            class confirmTwoFactorAuthorization {
                constructor(
                    Service,
                    notificationService,
                    $uibModalInstance,
                    $scope,
                    $uibModal,
                    $translate,
                    $rootScope,
                ) {
                    this.globalService = Service;
                    this.notificationService = notificationService;
                    this.$uibModalInstance = $uibModalInstance;
                    this.$scope = $scope;
                    this.$uibModal = $uibModal;
                    this.$translate = $translate;
                    this.$rootScope = $rootScope;
                }

                closeModal() {
                    this.$uibModalInstance.close();
                }

                searchOnEnter(e) {
                    if (e.keyCode === 13) this.nextStep();
                }

                nextStep() {
                    if (!this.confirmationCode || this.confirmationCode.length < 6) {
                        this.notificationService.error(
                            this.$translate.instant('Enter the verification code from the authentication app'),
                        );
                        return;
                    }

                    this.$rootScope.loading = true;
                    this.globalService
                        .onActivate2fa({ code: this.confirmationCode })
                        .then((resp) => {
                            $scope.user.tfaEnabled = true;

                            this.closeModal();
                            finalStageTwoFactorAuthorizationModal();
                        })
                        .catch((error) => {
                            this.notificationService.error(this.$translate.instant('Incorrect confirm key'));
                        })
                        .finally(() => {
                            this.$rootScope.loading = false;
                            this.$rootScope.$$phase || this.$scope.$apply();
                        });
                }

                $onInit() {
                    this.confirmationCode = null;
                }
            }

            const modalInstance = $uibModal.open({
                templateUrl:
                    './partials/modal/two-factor-authorization/confirm-stage-two-factor-authorization-modal.html',
                controller: [
                    'Service',
                    'notificationService',
                    '$uibModalInstance',
                    '$scope',
                    '$uibModal',
                    '$translate',
                    '$rootScope',
                    confirmTwoFactorAuthorization,
                ],
                controllerAs: 'vm',
                windowClass: 'dark-backdrop',
                backdrop: 'false',
                resolve: {},
            });
        }

        function finalStageTwoFactorAuthorizationModal() {
            class finalStageTwoFactorAuthorization {
                constructor(Service, notificationService, $uibModalInstance, $scope, $uibModal, $translate) {
                    this.copyToClipboard = Service.copyToClipboard;
                    this.notificationService = notificationService;
                    this.$uibModalInstance = $uibModalInstance;
                    this.$scope = $scope;
                    this.$uibModal = $uibModal;
                    this.$translate = $translate;
                }

                closeModal() {
                    this.$uibModalInstance.close();
                }

                $onInit() {}
            }

            const modalInstance = $uibModal.open({
                templateUrl:
                    './partials/modal/two-factor-authorization/final-stage-two-factor-authorization-modal.html',
                controller: [
                    'Service',
                    'notificationService',
                    '$uibModalInstance',
                    '$scope',
                    '$uibModal',
                    '$translate',
                    finalStageTwoFactorAuthorization,
                ],
                controllerAs: 'vm',
                windowClass: 'dark-backdrop',
                backdrop: 'false',
                resolve: {},
            });
        }

        $scope.disableUser = function (user, confirmed) {
            $rootScope.userForDisable = user;
            if (user.status == 'A' && confirmed) {
                Person.disableUser(
                    {
                        personId: $scope.user.personId,
                        userId: $scope.user.userId,
                    },
                    function (resp) {
                        if (resp.status && angular.equals(resp.status, 'error')) {
                            notificationService.error(resp.message);
                        } else if (resp.status === 'delete_limit') {
                            notificationService.error(
                                $translate.instant(
                                    'deleteLimit.You_have_reached_the_daily_limit_for_removing_users_You_can_continue_tomorrow',
                                ),
                            );
                        } else {
                            $scope.user.status = 'N';
                            $scope.getLastEvent();
                            Person.getAllPersons(
                                (resp) => {
                                    angular.forEach(resp.object, function (val) {
                                        if (val.status == 'A') {
                                            Person.setterAllPersons(resp);
                                        }
                                    });
                                },
                                (error) => reject(error),
                            );
                        }
                    },
                    function (error) {
                        console.error(error);
                    },
                );
            } else if (user.status == 'N') {
                Person.enableUser(
                    {
                        personId: $scope.user.personId,
                        userId: $scope.user.userId,
                    },
                    function (resp) {
                        if (resp.status && angular.equals(resp.status, 'error')) {
                            notificationService.error(resp.message);
                        } else {
                            $scope.user.status = 'A';
                            $scope.getLastEvent();
                            Person.getAllPersons(
                                (resp) => {
                                    angular.forEach(resp.object, function (val) {
                                        if (val.status == 'A') {
                                            Person.setterAllPersons(resp);
                                        }
                                    });
                                },
                                (error) => reject(error),
                            );
                        }
                    },
                    function (error) {
                        console.error(error);
                    },
                );
            } else {
                $scope.modalInstance = $uibModal.open({
                    animation: true,
                    scope: $scope,
                    windowClass: 'secondary-modal',
                    templateUrl: '../partials/modal/remove-user-warning.html',
                    resolve: {},
                });
            }
        };
        $rootScope.disableUser = $scope.disableUser;
        $scope.enableUser = function () {
            Person.enableUser(
                { personId: $scope.user.personId, userId: $scope.user.userId },
                function (resp) {
                    if (resp.status && angular.equals(resp.status, 'error')) {
                        notificationService.error(resp.message);
                    } else {
                        $scope.user.status = 'A';
                        $scope.getLastEvent();
                        Person.getAllPersons(
                            (resp) => {
                                angular.forEach(resp.object, function (val) {
                                    if (val.status == 'A') {
                                        Person.setterAllPersons(resp);
                                    }
                                });
                            },
                            (error) => reject(error),
                        );
                    }
                },
                function (error) {
                    console.error(error);
                },
            );
        };

        $scope.setPersonEmploye = function (name, value) {
            const req = {
                method: 'POST',
                url: '/hr/employee/setEmployeeFunctionsEnabled',
                data: { userId: $scope.user.userId, enable: value },
            };

            $http(req).then(function (resp) {
                if (!$scope.user.personParams) {
                    $scope.user.personParams = {};
                }
                $scope.user.personParams[name] = value;
                if ($scope.user.login == $rootScope.me.login) {
                    $rootScope.me.personParams[name] = value;
                }
            });
        };
        $scope.setEditableEmailTemplates = function () {
            if ($rootScope.editEmailTemplatesValue === 'Y') {
                $rootScope.editEmailTemplatesValue = 'N';
            } else {
                $rootScope.editEmailTemplatesValue = 'Y';
            }

            const req = {
                method: 'POST',
                url: '/hr/person/changeUserParam',
                data: {
                    name: $rootScope.editEmailTemplatesName,
                    value: $rootScope.editEmailTemplatesValue,
                    userId: $scope.user.userId,
                },
            };

            $http(req).then(function (resp) {});
        };

        $scope.setEditableAutoAction = function () {
            if ($rootScope.editAutoActionValue === 'Y') {
                $rootScope.editAutoActionValue = 'N';
            } else {
                $rootScope.editAutoActionValue = 'Y';
            }

            const req = {
                method: 'POST',
                url: '/hr/person/changeUserParam',
                data: {
                    name: $rootScope.editAutoActionName,
                    value: $rootScope.editAutoActionValue,
                    userId: $scope.user.userId,
                },
            };

            $http(req).then(function (resp) {});
        };

        $scope.setExcelRecruiter = function (name, value) {
            const req = {
                method: 'POST',
                url: '/hr/employee/setDownloadingToExcelEnable',
                data: { userId: $scope.user.userId, enable: value },
            };

            $http(req).then(function (resp) {
                if (!$scope.user.personParams) {
                    $scope.user.personParams = {};
                }
                $scope.user.personParams[name] = value;
                if ($scope.user.login == $rootScope.me.login) {
                    $rootScope.me.personParams[name] = value;
                }
            });
        };

        $scope.enableViewEmploye = function (user) {
            if (user.personParams.enableEmployee == 'N' || user.personParams.enableEmployee == undefined) {
                $scope.setPersonEmploye('enableEmployee', 'Y');
            } else {
                $scope.setPersonEmploye('enableEmployee', 'N');
            }
        };

        $scope.disableViewEmploye = function () {
            $scope.setPersonEmploye('enableEmployee', 'N');
        };

        $scope.disableExcelRecruiter = function () {
            $scope.setExcelRecruiter('enableEmployee', false);
        };
        $scope.enableExcelRecruiter = function (user) {
            if (user.personParams.enableDownloadToExcel) {
                $scope.setExcelRecruiter('enableDownloadToExcel', false);
            } else {
                $scope.setExcelRecruiter('enableDownloadToExcel', true);
            }
        };

        $scope.toggleInterviewStageChange = function (user) {
            Account.changeRestrictInterviewStageChange({
                userId: user.userId,
                value: user.personParams.restrictInterviewStageChangeHM === 'true' ? 'false' : 'true',
            }).then((resp) => {
                Person.getPerson(
                    { userId: $stateParams.id },
                    function (resp) {
                        $scope.user = resp.object;

                        if (resp.status === 'ok') {
                            notificationService.success($filter('translate')('Changes saved'));
                            $scope.tableParams.reload();
                        }
                    },
                    (error) => console.error(error.message),
                );
            });
        };

        $scope.toggleBlockDeletingCandidates = function () {
            const value = $scope.blockDeletingCandidates ? 'N' : 'Y';
            Person.onSetBlockDeletingCandidates($scope.user.userId, value).then((resp) => {
                if (resp.status === 'ok') {
                    $scope.blockDeletingCandidates = !$scope.blockDeletingCandidates;
                    $rootScope.$$phase || $scope.$apply();
                }
            });
        };

        $scope.enableMailingService = function (user) {
            const userName = $rootScope.useAmericanNameStyle ? user.fullNameEn : user.fullName;

            Mailing.enableMailingService({
                userId: user.userId,
                enableMailing: !($scope.user.personParams.enableMailing === 'true'),
            }).then((resp) => {
                Person.getPerson(
                    { userId: $stateParams.id },
                    function (resp) {
                        $scope.user = resp.object;
                        $scope.user.personParams.enableDownloadToExcel =
                            !$scope.user.personParams.enableDownloadToExcel ||
                            $scope.user.personParams.enableDownloadToExcel == 'Y'
                                ? true
                                : false;

                        if ($scope.user.personParams.enableMailing === 'true') {
                            notificationService.success(
                                $filter('translate')('Mailings are available for the user') + ` ${userName}`,
                            );
                        } else {
                            notificationService.success(
                                $filter('translate')('Mailings are hidden for the user') + ` ${userName}`,
                            );
                        }
                    },
                    (error) => console.error(error.message),
                );
            });
        };

        function deleteFromTags(id, type) {
            $scope.users.filter((user, index) => {
                if (user[type] == id) {
                    $scope.users.splice(index, 1);
                }
            });
            $scope.repsonsibleUsers.forEach((user, index) => {
                if (user.userId == id) {
                    $scope.repsonsibleUsers.splice(index, 1);
                }
            });
        }

        $scope.getCompanyParams = function () {
            Company.getParams(
                function (resp) {
                    $scope.companyParams = resp.object;
                },
                function (error) {
                    console.error(error);
                },
            );
        };
        $scope.getCompanyParams();
        $scope.changeCommentFlag = function (history) {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/edit-comment-user.html',
                size: '',
                backdrop: 'static',
                scope: $scope,
                resolve: {},
            });
            $rootScope.closeModal = function () {
                $scope.modalInstance.close();
                $rootScope.initTagsStyles(history, true);
            };

            $scope.repsonsibleUsers = [];
            $scope.users = [];

            $rootScope.persons.forEach((i, index) => {
                if (history.candidate) {
                    if (i.userId === history.candidate.responsibleId && i.recrutRole !== 'client') {
                        $scope.repsonsibleUsers.push({
                            id: index + 1,
                            fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                            userId: i.userId,
                            responsible: true,
                        });
                    }
                }
                if (
                    i.status === 'A' &&
                    i.recrutRole !== 'client' &&
                    i.recrutRole !== 'freelancer' &&
                    i.recrutRole !== 'researcher' &&
                    $rootScope.checkUserForTagsList(history.type, i.hideClients)
                ) {
                    $scope.users.push({
                        id: index + 1,
                        fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                        userId: i.userId,
                        responsible: false,
                    });
                }
            });
            $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));

            $rootScope.ckEditorUserEditComment = {
                height: 140,
                toolbar: [],
                enterMode: CKEDITOR.ENTER_BR,
                shiftEnterMode: CKEDITOR.ENTER_BR,
                extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
                removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
                editorplaceholder: $translate.instant('mention placeholder'),
                mentions: [
                    {
                        feed: dataCallback,
                        minChars: 0,
                        itemTemplate: '<li style="padding: 10px" data-id="{id}">{fullname}</li>',
                        outputTemplate: `<a href="#" style="text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;">@{fullname}</a>&nbsp`,
                        marker: '@',
                        pattern: /@([\p{L}\p{M}]*(?:\s[\p{L}\p{M}]*)?)?$/u,
                    },
                ],
                on: {
                    paste: function (ev) {
                        ev.data.dataValue = '';
                        CKEDITOR.instances['ckEditorUserEditComment'].insertText(ev.data.dataTransfer._.data.Text);
                        setTimeout(() => {
                            let bodyHeight = ev.editor.document.$.children[0].offsetHeight;
                            if (bodyHeight > ev.editor.config.height) {
                                if (bodyHeight < 605) {
                                    ev.editor.resize(null, bodyHeight);
                                }
                            }
                        }, 0);
                    },
                    afterInsertHtml: function (ev) {
                        let user = ev.editor.data;
                        if (user) {
                            deleteFromTags(user.userId, 'userId');
                            $scope.deletedUsers.push(user);
                        }
                    },
                    instanceReady: function (ev) {
                        $scope.deletedUsers = [];
                        $scope.afterDelete = [];

                        ev.editor.dataProcessor.writer.setRules('p', {
                            indent: false,
                            breakBeforeOpen: false,
                            breakAfterOpen: false,
                            breakBeforeClose: false,
                            breakAfterClose: false,
                        });
                        ev.editor.dataProcessor.writer.setRules('br', {
                            indent: false,
                            breakBeforeOpen: false,
                            breakAfterOpen: false,
                            breakBeforeClose: false,
                            breakAfterClose: false,
                        });
                        $rootScope.convertTagsToSpan(history, 'ckEditorUserEditComment');
                    },
                },
            };

            function dataCallback(opts, callback) {
                opts.query = opts.query.replace(/\u00A0/g, ' ');
                let text = CKEDITOR.instances['ckEditorUserEditComment'].getData();

                $scope.deletedUsers.forEach((user) => {
                    if (!text.includes(`${user.fullname}`)) {
                        if (user.responsible) {
                            $scope.repsonsibleUsers.push(user);
                        } else {
                            $scope.users.push(user);
                        }
                        $scope.afterDelete.push(user.id);

                        $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));
                    }
                });

                $scope.afterDelete.forEach((id) => {
                    $scope.deletedUsers = $scope.deletedUsers.filter((user) => user.id !== id);
                });
                $scope.afterDelete = [];

                if (opts.query.length === 0 && $scope.repsonsibleUsers.length > 0) {
                    setTimeout(function () {
                        callback(
                            $scope.repsonsibleUsers.filter(function (item) {
                                return item.fullname;
                            }),
                        );
                    });
                } else {
                    setTimeout(function () {
                        callback(
                            $scope.users.filter(function (item) {
                                return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                            }),
                        );
                    });
                }
            }

            $rootScope.editUserComment = function () {
                $rootScope.loading = true;
                let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorUserEditComment.getData());
                $scope.actionComment = text.replaceAll('<br />', ' &nbsp<br>&nbsp ');

                Action.editAction(
                    {
                        comment: $scope.actionComment,
                        actionId: history.actionId,
                    },
                    function (resp) {
                        if (resp.status && angular.equals(resp.status, 'error')) {
                            notificationService.error(resp.message);
                        } else {
                            $scope.tableParams.reload();
                            history.showAllCandidates = false;
                            history.descr = resp.object.descr;
                            history.dateEdit = resp.object.dateEdit;
                            $rootScope.loading = false;
                            $rootScope.closeModal();
                        }
                    },
                );
            };
        };
        $scope.openMenuWithCandidatesNotSend = function (history) {
            history.showAllCandidatesNotSend = !history.showAllCandidatesNotSend;
            history.showAllCandidates = false;
            history.editCommentFlag = false;
        };
        $scope.openMenuWithCandidates = function (history) {
            history.showAllCandidatesNotSend = false;
            history.showAllCandidates = !history.showAllCandidates;
            history.editCommentFlag = false;
        };
        $rootScope.openMenuWithCandidatesOnUserProfile = function (history) {
            history.showAllCandidates = !history.showAllCandidates;
            history.editCommentFlag = false;
        };
        $scope.resetComment = function (history) {
            history.editCommentFlag = false;
            history.editedComment = history.descr;
        };
        $scope.changeComment = function (action, comment) {
            Action.editAction({ comment: comment, actionId: action.actionId }, function (resp) {
                if (resp.status && angular.equals(resp.status, 'error')) {
                    notificationService.error(resp.message);
                } else {
                    action.editCommentFlag = false;
                    action.showAllCandidates = false;
                    action.descr = resp.object.descr;
                    action.new_komment = '';
                    action.dateEdit = resp.object.dateEdit;
                    $scope.tableParams.reload();
                }
            });
        };

        $scope.showDeleteComment = function (resp) {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/delete-comment-candidate.html',
                scope: $scope,
                resolve: {
                    items: function () {
                        return $scope.items;
                    },
                },
            });
            $scope.commentRemove = resp;
            $rootScope.commentRemoveId = resp.actionId;
        };

        $scope.toggleCandidates = function (history) {
            $scope.history[$scope.history.indexOf(history)].candidatesToShow =
                !$scope.history[$scope.history.indexOf(history)].candidatesToShow;
        };

        $rootScope.deleteComment = function () {
            Action.removeMessageAction(
                {
                    actionId: $rootScope.commentRemoveId,
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        notificationService.success($filter('translate')('Comment removed'));
                        $scope.tableParams.reload();
                    } else {
                        console.error(resp);
                    }
                    $rootScope.closeModal();
                },
            );
        };
        $scope.showForm = true;
        $scope.focusChangeNameFunc = function () {
            $scope.changedFirstName = $scope.user.firstName;
            $scope.changedLastName = $scope.user.lastName;
            $scope.isAllNameInputsFilled = true;
            $scope.showForm = false;
            setTimeout(function () {
                $('#changeNameInput').focus();
            }, 0);
        };
        $scope.showFormEdit = function () {
            $scope.showForm = false;
            $scope.focusInput = true;
        };
        $scope.checkKeyFunc = function (event) {
            if (event.keyCode === 13) {
                event.preventDefault();
                return false;
            }
        };
        $scope.replaceCharacters = function (name) {
            Company.replaceSpecialCharacters(name, $scope);
            $scope.isAllNameInputsFilled = $scope.changedFirstName && $scope.changedLastName;
            resetNameInputErrors();
        };
        $scope.hideForm = function () {
            $scope.showForm = true;
            $rootScope.changedFirstName = $scope.user.firstName;
            $rootScope.changedLastName = $scope.user.lastName;
            resetNameInputErrors();
        };

        $scope.changeFirst = () => {};

        $scope.changeUserFirstName = function () {
            const firstName = $scope.changedFirstName;
            const lastName = $scope.changedLastName;

            if (
                checkForAllowedSymbols({
                    changeNameInput1: firstName,
                    changeNameInput2: lastName,
                })
            )
                return;

            Person.changeName(
                {
                    firstName,
                    lastName,
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        $scope.showForm = true;
                        Person.getPerson({ userId: $stateParams.id }, function (resp) {
                            $scope.user = resp.object;
                            $rootScope.updateMe(true);
                        });
                        notificationService.success($filter('translate')('Changes saved'));
                    } else {
                        notificationService.error(resp.message);
                    }
                },
            );
        };

        $scope.deleteAvatar = function () {
            $rootScope.loading = true;
            Person.onDeleteAvatar({ creatorId: $scope.user.userId })
                .then(() => {})
                .catch((error) => {
                    console.error(error.message);
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        };

        $scope.deleteCurrentPhotoLink = function () {
            $rootScope.loading = true;
            Person.onDeleteCurrentPhotoLink()
                .then(Person.onGetAllPersons)
                .then(Person.setterAllPersons)
                .catch((error) => console.error(error.message))
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        };

        $scope.usePhotoLinkHandler = function (usePhotoLink) {
            $rootScope.loading = true;

            const payload = {
                userId: $rootScope.me.userId,
                name: 'dontUsePhotoLink',
                value: !usePhotoLink,
            };

            Person.onChangeUserParam(payload)
                .then(() => ($rootScope.me.personParams.dontUsePhotoLink = !usePhotoLink))
                .catch((err) => notificationService.error(err.message))
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        };

        $scope.showEditEmailTemplate = function (template) {
            $scope.activeTemplate = template.type;
            $scope.fileForSave = [];
            $scope.emailTemplate = {
                mailTemplateId: template.mailTemplateId,
                type: template.type,
                name: template.name,
                title: template.title,
                text: template.text,
                vacancyId: $scope.vacancy.vacancyId,
                fileId: template.fileId,
                fileName: template.fileName,
            };
            if ($scope.emailTemplate.fileId && $scope.emailTemplate.fileName) {
                $scope.fileForSave.push({
                    fileId: $scope.emailTemplate.fileId,
                    fileName: $scope.emailTemplate.fileName,
                });
            }
            $scope.emailTemplateForRender.text = $scope.emailTemplate.text;
            $scope.showAddEmailTemplate = true;
            $scope.updateRenderedTitle();
        };

        $(document).mouseup(function (e) {
            if ($('.popover').has(e.target).length === 0) {
                $('.popover').remove('.popover');
            }
        });

        function showModalRemoveUser() {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/remove-candidate-full.html',
                scope: $scope,
                backdrop: 'static',
                keyboard: false,
            });

            $scope.modalInstance.opened.then(function () {
                $rootScope.commentForRemoveUser = '';
            });

            $scope.modalInstance.closed.then(function () {
                $rootScope.commentForRemoveUser = '';
                let decreaseModalWindow = document.querySelector('.modal-dialog');
                if (decreaseModalWindow) decreaseModalWindow.removeAttribute('style');
            });
        }

        function successRemoveCadidate(resp) {
            if (resp.code === 'adminMustBe') {
                notificationService.error(`${$translate.instant(resp.message)}`);
            } else {
                notificationService.success(
                    `${$translate.instant('user')} ${$scope.user.fullName} ${$translate.instant(
                        'has been successfully removed from your account',
                    )}`,
                );
            }
            Person.getAllPersons(
                (resp) => {
                    angular.forEach(resp.object, function (val) {
                        if (val.status == 'A') {
                            Person.setterAllPersons(resp);
                        }
                    });
                },
                (error) => reject(error),
            );
            if ($rootScope.me.userId === $stateParams.id && $rootScope.allPersonsObject.length > 1) {
                $timeout(Person.signOut, 500);
            } else {
                $location.path('/company/users');
            }

            $rootScope.commentForRemoveUser = '';
            $rootScope.loading = false;
            $scope.$apply();
        }

        function removeUser() {
            let dataForRemoveUser = $rootScope.dataForRemoveUser || +localStorage.getItem('dataForRemoveUser'),
                access = accessForRemoveUser(dataForRemoveUser),
                requestData = { userId: $stateParams.id };

            if (!access) return;

            if ($scope.commentForRemoveUser) {
                requestData.comment = $rootScope.commentForRemoveUser;
            }

            if ($scope.vacancyResponsibility) {
                requestData.responsibleForVacancy = $scope.addVacancyId;
            }

            if ($scope.candidatesResponsibility) {
                requestData.responsibleForCandidate = $scope.addCandidateId;
            }

            if ($scope.clientsResponsibility) {
                requestData.responsibleForClient = $scope.addClientsId;
            }

            if ($scope.tasksResponsibility) {
                requestData.responsibleForTask = $scope.addTasksId;
            }

            Person.requestRemoveUser(requestData)
                .then((resp) => {
                    if (resp.status === 'ok') {
                        successRemoveCadidate(resp);
                    } else if (resp.status === 'delete_limit') {
                        notificationService.error(
                            $translate.instant(
                                'deleteLimit.You_have_reached_the_daily_limit_for_removing_users_You_can_continue_tomorrow',
                            ),
                        );
                    } else {
                        notificationService.error(resp.message);
                    }
                })
                .catch((error) => {
                    console.error(error.message);
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $rootScope.$apply();
                });
        }

        function accessForRemoveUser(dataForRemoveUser) {
            if (dataForRemoveUser.count === 1) {
                notificationService.error(
                    $translate.instant(
                        'You are the only user in the account. You can delete the whole account on the account settings page.',
                    ),
                );
                return false;
            } else if (dataForRemoveUser.countsAdminsForRemove === 1 && $rootScope.me.userId === $scope.user.userId) {
                notificationService.error(
                    $translate.instant(
                        'You are now the only administrator in the account. To delete your profile, assign another user as an administrator',
                    ),
                );
                return false;
            } else if (
                dataForRemoveUser.count > 1 &&
                dataForRemoveUser.isAdmin &&
                $rootScope.me.recrutRole !== 'admin'
            ) {
                notificationService.error(
                    'Вы не можете удалить пользователя - обратитесь к пользователю с  ролью Админ',
                );
                return false;
            }

            return true;
        }

        function getLogoById(personId) {
            if (personId) {
                Person.onGetAvatarId({ userId: personId })
                    .then((resp) => {
                        if (resp.status === 'ok') {
                            $scope.photoId = resp.message || null;
                        }
                    })
                    .catch((error) => {
                        console.error(error.message);
                    });
            }
        }

        function getRefererUser() {
            if ($scope.user.refererUserId) {
                Person.getPerson(
                    { userId: $scope.user.refererUserId },
                    (person) => {
                        $scope.referer = person.object;
                    },
                    (error) => {
                        notificationService.error(error.message);
                    },
                );
            }
        }

        function checkForAllowedSymbols(nameObj) {
            let isError = false;
            const regex = /^[A-Za-zА-Яа-яёЁІіЇїЄєҐґĄąĆćĘęŁłŃńÓóŚśŹźŻż''`\- ]{1,50}$/;

            for (let inputId in nameObj) {
                if (!regex.test(nameObj[inputId])) {
                    $(`#${inputId}`).css({ border: '1px solid red' });
                    isError = true;
                }
            }

            if (isError) notificationService.error($translate.instant('notValidName'));

            return isError;
        }

        function resetNameInputErrors() {
            $('input[id*="changeNameInput"]').each((_, input) => {
                $(input).css({
                    border: '1px solid #cccccc',
                });
            });
        }

        $scope.addCandidateId = '';
        $scope.addVacancyId = '';
        $scope.addClientsId = '';
        $scope.addTasksId = '';

        $scope.candidateReassigningResponsibility = function (candidate) {
            if (candidate.id) {
                $scope.candidatesResponsibility = true;
                $scope.addCandidateId = candidate.id;
            } else {
                $scope.candidatesResponsibility = false;
            }
        };
        $scope.vacancyReassigningResponsibility = function (candidate) {
            if (candidate.id) {
                $scope.addVacancyId = candidate.id;
                $scope.vacancyResponsibility = true;
            } else {
                $scope.vacancyResponsibility = false;
            }
        };
        $scope.clientReassigningResponsibility = function (candidate) {
            if (candidate.id) {
                $scope.addClientsId = candidate.id;
                $scope.clientsResponsibility = true;
            } else {
                $scope.clientsResponsibility = false;
            }
        };
        $scope.tasksReassigningResponsibility = function (candidate) {
            if (candidate.id) {
                $scope.addTasksId = candidate.id;
                $scope.tasksResponsibility = true;
            } else {
                $scope.tasksResponsibility = false;
            }
        };

        $scope.initialPersonObj = function () {
            $scope.candidatesResponsibility = false;
            $scope.vacancyResponsibility = false;
            $scope.clientsResponsibility = false;
            $scope.sliderId = CandidatesSlider.getSliderId();
            $scope.tasksResponsibility = false;
            $scope.usersObj = [];
            $scope.clientsUsersObj = [];
            Person.getAllPersons(
                (resp) => {
                    angular.forEach(resp.object, function (val) {
                        $rootScope.persons.push({
                            fullName: val.fullName,
                            userId: val.userId,
                            status: val.status,
                            recrutRole: val.recrutRole,
                            fullNameEn: val.fullNameEn,
                        });
                        $scope.usersObj.push({
                            fullName: val.fullName,
                            fullNameEn: val.fullNameEn,
                            id: val.userId,
                        });
                        if (val.recrutRole === 'admin' || val.recrutRole === 'recruter') {
                            $scope.clientsUsersObj.push({
                                fullName: val.fullName,
                                fullNameEn: val.fullNameEn,
                                id: val.userId,
                            });
                        }
                    });
                },
                (error) => reject(error),
            );
        };

        $scope.initialPersonObj();

        $scope.showFilePreview = function (history) {
            const file = {
                fileName: history.descr,
                fileId: JSON.parse(history.data).fileId,
            };
            Service.showModalResume(file, $scope, $rootScope, $location, $sce, $uibModal);
        };

        $scope.showModalRemoveUser = showModalRemoveUser;
        $scope.removeUser = removeUser;
        $scope.toSentPreview = function (mailing) {
            Mailing.showSentCompaignById(mailing);
        };

        $scope.getVacanciesByIds = function (idsString) {
            const ids = idsString.replace('[', '').replace(']', '').split(', ');

            const vacancies = [];

            $scope.allVacancyInUser.forEach((vacancy) => {
                ids.forEach((id) => {
                    if (vacancy.vacancyId === id) {
                        vacancies.push(vacancy);
                    }
                });
            });

            return vacancies;
        };
        $rootScope.loader = false;
    },
});
