controller.controller('payWay4PayController', [
    '$scope',
    'Person',
    '$rootScope',
    '$stateParams',
    '$location',
    '$translate',
    'Service',
    'notificationService',
    '$filter',
    'Account',
    'Pay',
    'Company',
    '$timeout',
    '$uibModal',
    '$state',
    '$localStorage',
    function (
        $scope,
        Person,
        $rootScope,
        $stateParams,
        $location,
        $translate,
        Service,
        notificationService,
        $filter,
        Account,
        Pay,
        Company,
        $timeout,
        $uibModal,
        $state,
        $localStorage,
    ) {
        $scope.changeColor = function (quantityMonths) {
            return Math.ceil((100 / 12) * quantityMonths);
            switch (quantityMonths) {
                case 1:
                    return 5;
                case 2:
                    return 10;
                case 3:
                    return 17.6;
                case 4:
                    return 23;
                case 5:
                    return 27;
                case 6:
                    return 33;
                case 7:
                    return 35;
                case 8:
                    return 40;
                case 9:
                    return 45;
                case 10:
                    return 54;
                case 11:
                    return 60;
                case 12:
                    return 100; // 66 prev
                case 13:
                    return 70;
                case 14:
                    return 74;
                case 15:
                    return 77;
                case 16:
                    return 80;
                case 17:
                    return 84;
                case 18:
                    return 87;
                case 19:
                    return 91;
                case 20:
                    return 94;
                case 21:
                    return 96;
                case 22:
                    return 97;
                case 23:
                    return 98;
                case 24:
                    return 100;
            }
        };
        $rootScope.closeModal = function () {
            $scope.modalInstance.close();
        };
        $scope.togglePaymentHistory = function ({ payment, transitions }) {
            if (payment && $scope.paymentHistory.payment && $scope.paymentHistory.transitions) {
                $scope.paymentHistory.transitions = false;
            }
            if (transitions && $scope.paymentHistory.payment && $scope.paymentHistory.transitions) {
                $scope.paymentHistory.payment = false;
            }
        };
        $scope.changeMonth2 = function (position) {
            let percent = parseInt(position);
            let oneMonthInPercents = 100 / 12;
            let getOnePercentFromCountMoth = Math.ceil(percent / oneMonthInPercents);

            if (percent >= 84 && percent < 98) {
                $scope.payment.countMonth = 11;
                return;
            }

            if (percent >= 98) {
                $scope.payment.countMonth = 12;
                return;
            }

            if (getOnePercentFromCountMoth === 0) {
                $scope.payment.countMonth = 1;
            } else {
                $scope.payment.countMonth = getOnePercentFromCountMoth;
            }
            return;
        };
        $scope.setSlider = function (event) {
            // set position of slider by click
            function getCoords(elem) {
                // calculate coords
                let box = elem.getBoundingClientRect();
                return {
                    top: box.top + pageYOffset,
                    left: box.left + pageXOffset,
                };
            } // change position of slider
            let progressBar = event.currentTarget;
            let progressBarCoords = getCoords(progressBar);
            let barWidth = progressBar.clientWidth;
            let shiftX = event.pageX - progressBarCoords.left;
            let setPositionSlider = Math.round((100 / barWidth) * shiftX);
            $scope.myWidth = parseInt(setPositionSlider) + '%';
            $scope.changeMonth2($scope.myWidth);
        };
        $rootScope.payWayForPayClick = function () {
            Pay.createPaymentUsage(
                {
                    months: $scope.payment.countMonth,
                    users: $scope.payment.countPeople,
                    type: 'way4pay',
                },
                function (resp) {
                    if (resp.status && angular.equals(resp.status, 'error')) {
                        notificationService.error(resp.message);
                    } else {
                        var form =
                            '<form id="payForm" action="https://secure.wayforpay.com/pay" method="post">' +
                            '<input type="hidden" name="amount" value="' +
                            resp.wayForPayParams.amount +
                            '" />' +
                            '<input type="hidden" name="currency" value="' +
                            resp.wayForPayParams.currency +
                            '" />' +
                            '<input type="hidden" name="merchantAccount" value="' +
                            resp.wayForPayParams.merchantAccount +
                            '" />' +
                            '<input type="hidden" name="merchantDomainName" value="' +
                            resp.wayForPayParams.merchantDomainName +
                            '" />' +
                            '<input type="hidden" name="merchantSignature" value="' +
                            resp.wayForPayParams.merchantSignature +
                            '" />' +
                            '<input type="hidden" name="merchantTransactionSecureType" value="' +
                            resp.wayForPayParams.merchantTransactionSecureType +
                            '" />' +
                            '<input type="hidden" name="merchantTransactionType" value="' +
                            resp.wayForPayParams.merchantTransactionType +
                            '" />' +
                            '<input type="hidden" name="orderDate" value="' +
                            resp.wayForPayParams.orderDate +
                            '" />' +
                            '<input type="hidden" name="orderReference" value="' +
                            resp.wayForPayParams.orderReference +
                            '" />' +
                            '<input type="hidden" name="paymentSystems" value="' +
                            resp.wayForPayParams.paymentSystems +
                            '" />' +
                            '<input type="hidden" name="productCount[]" value="' +
                            resp.wayForPayParams.productCount +
                            '" />' +
                            '<input type="hidden" name="productName[]" value="' +
                            resp.wayForPayParams.productName +
                            '" />' +
                            '<input type="hidden" name="productPrice[]" value="' +
                            resp.wayForPayParams.productPrice +
                            '" />' +
                            '<input type="hidden" name="returnUrl" value="' +
                            resp.wayForPayParams.returnUrl +
                            '" />' +
                            '<input type="hidden" name="serviceUrl" value="' +
                            resp.wayForPayParams.serviceUrl +
                            '" />' +
                            '</form>';
                        $('body').append(form);
                        $('#payForm').submit();
                        $('#payForm').remove();
                    }
                },
                function () {},
            );
        };
        $scope.freePaidUsers = function (pay) {
            $rootScope.payCardOrInvoice = pay;
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/free-paid-users.html',
                windowTopClass: 'free-paid-users-modal',
                size: '',
                resolve: function () {},
            });
        };
        $rootScope.goToInvoice = function () {
            $location.path('/invoice');
            if ($scope.peopleModalShow) $rootScope.closeModal();
        };
        $scope.acceptChangesBilling = function () {
            Company.setParam(
                {
                    name: 'switch2billing',
                    value: 'Y',
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        $rootScope.closeModal();
                        $rootScope.modalInstance = undefined;
                    }
                },
            );
        };
        $scope.scrollTo = function (id) {
            if ($(window).width() <= 768) {
                let element = $('#' + id);
                $('html, body').animate({ scrollTop: element.position().top - 20 }, 'slow');
            }
        };
        $scope.setUpSchedule = function () {
            if (!$scope.selectedRegularPayment) $scope.selectedRegularPayment = $scope.currentPayment;
        };
        $scope.checkPay = function () {
            var string = window.location.hash,
                substring = 'order';
            if (string.indexOf(substring) > -1) {
                $scope.fromPayment = true;
            }
        };
        $scope.changeEditingPaymentsState = function (months, users) {
            $scope.change = !$scope.change;
            if ($localStorage.get('notSavedPayments')) {
                $localStorage.remove('notSavedPayments');
            }
            if (Pay.getOpenPaymentsSettings()) {
                Pay.setOpenPaymentsSettings(false);
            }
            if (months && users) {
                Pay.setIsChangePaymentsSettings(true);
                $rootScope.$emit('onUpdatePayments');
                unblurPageOnEditSettings();
                $scope.payment.countPeople = users;
                $scope.paidUsersForSelect = [];
                for (let i = $scope.paidUsers; i <= $scope.paidUsers + 10; i++) {
                    $scope.paidUsersForSelect.push(i);
                }
                $rootScope.loading = true;
                Pay.onSavePaymentSettings({ months, users })
                    .then(() => {})
                    .catch((e) => {
                        notificationService.error(e.message);
                    })
                    .finally(() => {
                        $scope.isEditingPaymentsActive = false;
                        if (!$scope.showPaymentsSettings) {
                            $scope.showPaymentsSettings = true;
                            setTimeout(() => {
                                let element = $('#payment-settings');
                                $('html, body').animate({ scrollTop: element.position().top - 20 }, 'slow');
                            }, 0);
                        }
                        $rootScope.loading = false;
                        $scope.$apply();
                    });
            } else {
                $scope.isEditingPaymentsActive = true;
                blurPageOnEditSettings();
            }
        };

        function blurPageOnEditSettings() {
            if ($rootScope.me.orgParams.tarif === 'corporate') return;
            $('.first-navbar').css({ 'pointer-events': 'none' });
            $('.first-navbar').css({ opacity: '.6' });
            $('.tabs').css({ 'border-bottom-color': '#7A7A7A' });
            $('.border_show').css({ 'border-top-color': '#7A7A7A' });
            $('.acc-info__item').css({ 'border-right-color': '#7A7A7A' });
            $('.payments-settings__with-bottom-line').css({
                'border-bottom-color': '#7A7A7A',
            });
            $('.payment-history__td').css({ 'border-right-color': '#7A7A7A' });
            $('.payment-history__tr').css({ background: 'transparent' });
            $('.payment-history__td__payment-status').css({ opacity: '.3' });
            $('.payments-settings__container').css({
                background: 'transparent',
            });
            $('.payment-history__show-all-button').css({
                background: '#949595',
            });
            $('.payment').css({ background: '#949595' });
            $('.payment-history__show-all-button__btn').css({ opacity: '.6' });
            $('.btn_success').css({ opacity: '.6' });
            $('.dropdown-wrapper').css({ opacity: '.6' });
            $('.customer-info__form-group input').css({ opacity: '.1' });
            $('.regular-payments__card-marker').css({ opacity: '.6' });
            $('.payment-history__td-amount').css({ opacity: '.6' });
            $('.pay__saving-btn').css({ opacity: '1' });
            $('.crypto-block').css({ opacity: 0.4, background: '#949595' });
            $('.tabs-history').css({ opacity: 0.8 });
            $('.payment__settings').css({ opacity: 0.8 });
            $('.tooltip-wrapper').css({ opacity: 0.4 });
            $('.update-btn').css({ opacity: 0.4 });
            $('.dropdown-wrapper .select-caret').css({ opacity: 0.2 });
            $('.acc-info__item-value').css({ opacity: 0.4 });
        }

        function unblurPageOnEditSettings() {
            $('.first-navbar').css({ 'pointer-events': 'auto' });
            $('.first-navbar').css({ opacity: '1' });
            $('.payments-settings__container').css({ background: '#fff' });
            $('.payment').css({ background: 'none' });
            $('.payment-history__tr').css({ background: '#fff' });
            $('.payment-history__show-all-button').css({ background: '#fff' });
            $('.payment-history__show-all-button__btn').css({ opacity: '1' });
            $('.tabs').css({ 'border-bottom-color': '#CCCCCC' });
            $('.border_show').css({ 'border-top-color': '#CCCCCC' });
            $('.acc-info__item').css({ 'border-right-color': '#CCCCCC' });
            $('.payment-history__td').css({
                'border-right-color': 'rgba(204, 204, 204, 0.3)',
            });
            $('.payments-settings__with-bottom-line').css({
                'border-bottom-color': 'rgba(204, 204, 204, 0.3)',
            });
            $('.payment-history__td-amount').css({ opacity: '1' });
            $('.btn_success').css({ opacity: '1' });
            $('.dropdown-wrapper').css({ opacity: '1' });
            $('.customer-info__form-group input').css({ opacity: '1' });
            $('.regular-payments__card-marker').css({ opacity: '1' });
            $('.pay__saving-btn').css({ opacity: '1' });
            $('.crypto-block').css({ opacity: 1, background: 'inherit' });
            $('.tabs-history').css({ opacity: 1 });
            $('.payment__settings').css({ opacity: 1 });
            $('.tooltip-wrapper').css({ opacity: 1 });
            $('.update-btn').css({ opacity: 1 });
            $('.payment-history__td__payment-status').css({ opacity: 1 });
            $('.select-caret').css({ opacity: 1 });
            $('.acc-info__item-value').css({ opacity: 1 });
        }

        function calculatePaidUsersForSelect(paidUsers, users) {
            $scope.paidUsers = paidUsers;
            $scope.payment.countPeople = users;
            $scope.paidUsersForSelect = [];
            for (let i = $scope.paidUsers; i <= $scope.paidUsers + 10; i++) {
                $scope.paidUsersForSelect.push(i);
            }
            $scope.monthRate = $scope.monthRate || 36;
        }

        function setAccountPaidTillDate() {
            if ($scope.balance.tillDate) {
                const tillDate = $scope.balance.tillDate;
                const day = tillDate.dayOfMonth > 9 ? tillDate.dayOfMonth : '0' + tillDate.dayOfMonth;
                const month = tillDate.monthValue > 9 ? tillDate.monthValue : '0' + tillDate.monthValue;
                return `${day}.${month}.${tillDate.year}`;
            } else {
                return $rootScope.me.orgParams.paidTillDate.split('-').reverse().join('/');
            }
        }

        function openDuplicateCardModal() {
            class OpenDuplicateCardModalCtrl {
                constructor($uibModalInstance, Pay, $rootScope) {
                    this.$uibModalInstance = $uibModalInstance;
                    this.payService = Pay;
                    this.$rootScope = $rootScope;
                }

                closeModal() {
                    this.$uibModalInstance.close();
                }

                connectNewCard() {
                    this.$rootScope.loading = true;
                    this.payService
                        .addNewCard()
                        .catch((err) => notificationService.error(err.message))
                        .finally(() => (this.$rootScope.loading = false));
                }
            }

            $uibModal.open({
                templateUrl: 'partials/modal/duplicate-card.modal.html',
                controller: ['$uibModalInstance', 'Pay', '$rootScope', OpenDuplicateCardModalCtrl],
                controllerAs: 'vm',
                windowClass: 'duplicate-card-modal secondary-modal',
                resolve: {
                    Pay: () => Pay,
                    $rootScope: () => $rootScope,
                },
            });
        }

        function initPayPage() {
            $scope.paymentsHistory = {
                payments: [],
                expenses: [],
            };
            $rootScope.loading = true;
            const requests = [
                Account.accountInfo(),
                Pay.onGetPaymentSettings(),
                Pay.onGetPayments(),
                Account.transactions({ dateFrom: null, dateTo: null }),
                Person.onGetAllPersons(),
            ];

            Promise.all(requests)
                .then(([accInfo, paymentSettings, payments, transactions, users]) => {
                    // accountInfo response start
                    $scope.isOldAccount = accInfo.object.oldAccount;
                    $scope.balance = accInfo.object;
                    $scope.paidTillDate = setAccountPaidTillDate();
                    $scope.isOnBilling = !accInfo.object.monthRate;
                    $scope.tarif = $rootScope.me['orgParams']['tarif'] || accInfo.object.tarif;
                    $scope.tariffValue = $rootScope.me['orgParams']['tariffValue'] || accInfo.object.tariffValue;
                    if (paymentSettings.object) {
                        $scope.customTariffValue = paymentSettings.object.customTariff;
                        $rootScope.customTariffValueTwo = $scope.customTariffValue;
                        $scope.fixedTariff = paymentSettings.object.fixedTariff;
                        $rootScope.fixedTariffVaueTwo = $scope.fixedTariff;
                    }
                    $scope.dailyRate = accInfo.object.dailyRate;
                    $scope.monthRate = accInfo.object.monthRate;
                    // $scope.months = $rootScope.me.orgParams.allowOneMonthPayment !== "true" ? $scope.months.slice(2) : $scope.months;
                    $scope.months = $scope.months;
                    // accountInfo response end

                    // paymentSettings response start
                    if (paymentSettings.object) {
                        $scope.payment.countPeople = paymentSettings.object.users;
                        $scope.payment.countMonth =
                            paymentSettings.object.months > 12 ? 12 : paymentSettings.object.months;
                        $scope.showPaymentsSettings = true;
                        $scope.restrictionForMonths = $rootScope.me.orgParams.allowOneMonthPayment === 'true' ? 1 : 3;
                        let paidUsers = 0;
                        Object.keys(users.object).forEach((key) => {
                            if (users.object[key].recrutRole !== 'client' && users.object[key].status === 'A')
                                paidUsers++;
                        });
                        $scope.paidUsers = paidUsers;
                        calculatePaidUsersForSelect(
                            paidUsers,
                            paymentSettings.object && paymentSettings.object.users
                                ? paymentSettings.object.users
                                : paidUsers,
                        );
                        calculatePaymentData();
                    } else {
                        $scope.showPaymentsSettings = false;
                        Pay.setIsChangePaymentsSettings(false);
                        $scope.restrictionForMonths = 3;
                        Pay.setOpenPaymentsSettings(true);
                        let paidUsers = 0;
                        Object.keys(users.object).forEach((key) => {
                            if (users.object[key].recrutRole !== 'client' && users.object[key].status === 'A')
                                paidUsers++;
                        });
                        $scope.paidUsers = paidUsers;
                        calculatePaidUsersForSelect(
                            paidUsers,
                            paymentSettings.object && paymentSettings.object.users
                                ? paymentSettings.object.users
                                : paidUsers,
                        );
                        calculatePaymentData();
                    }
                    // paymentSettings response end

                    // payments  & transactions responses start
                    $scope.paymentsHistory.payments = payments.objects.map((payment) => {
                        if (payment.cardNumber && payment.type === 'payment') {
                            payment.cardNumber = payment.cardNumber.slice(payment.cardNumber.length - 4);
                        }
                        return payment;
                    });
                    $scope.checkFirstPayment = payments.objects.some(
                        (payment) =>
                            (payment.type === 'adjustment' || payment.type === 'payment') &&
                            payment.status !== 'Refunded',
                    );
                    $scope.paymentsHistory.expenses = transactions.object.reverse();
                    $scope.$apply();

                    // payments  & transactions responses end
                    if ($scope.paidUsers > $scope.payment.countPeople) {
                        $scope.isEditingPaymentsActive = true;
                        $scope.payment.countPeople = $scope.paidUsers;
                        $scope.paidUsersForSelect = [];
                        for (let i = $scope.payment.countPeople; i <= $scope.payment.countPeople + 10; i++) {
                            $scope.paidUsersForSelect.push(i);
                        }
                        $timeout(() => blurPageOnEditSettings(), 0);
                    }
                    if (Pay.getOpenPaymentsSettings()) {
                        if (!Pay.getIsChangePaymentsSettings()) {
                            $scope.isEditingPaymentsActive = true;
                            $timeout(() => blurPageOnEditSettings(), 0);
                        } else if (Pay.getIsChangePaymentsSettings() || $localStorage.get('changePaymentsSettings')) {
                            if ($localStorage.get('changePaymentsSettings'))
                                $localStorage.remove('changePaymentsSettings');

                            $timeout(
                                () =>
                                    $scope.changeEditingPaymentsState(
                                        $scope.payment.countMonth,
                                        $scope.payment.countPeople,
                                    ),
                                0,
                            );
                        }
                    }
                    $rootScope.loading = false;
                })
                .catch((err) => {
                    notificationService.error(err.message);
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $scope.$apply();
                });
        }

        function calculatePaymentData() {
            $scope.colors.zero = '#312E37';
            if ($scope.payment.countMonth === 12) {
                if ($scope.isOldAccount) {
                    $rootScope.payMentTariff = $scope.payment.countPeople <= 10 ? 38 : 35;
                } else {
                    $rootScope.payMentTariff = $scope.payment.countPeople <= 10 ? 40 : 38;
                }
                $scope.myWidth = $scope.changeColor($scope.payment.countMonth) + '%';
                $scope.colors.twenty = '#312E37';
                $scope.colors.fifteen = '#312E37';
                $scope.colors.ten = '#312E37';
                $scope.colors.five = '#312E37';
            } else if ($scope.payment.countMonth > 5 && $scope.payment.countMonth <= 11) {
                if ($scope.isOldAccount) {
                    $rootScope.payMentTariff = $scope.payment.countPeople <= 10 ? 40 : 38;
                } else {
                    $rootScope.payMentTariff = $scope.payment.countPeople <= 10 ? 45 : 42;
                }
                $scope.myWidth = $scope.changeColor($scope.payment.countMonth) + '%';
                $scope.colors.twenty = '';
                $scope.colors.fifteen = '';
                $scope.colors.ten = '#312E37';
                $scope.colors.five = '#312E37';
            } else if ($scope.payment.countMonth > 2 && $scope.payment.countMonth <= 5) {
                if ($scope.isOldAccount) {
                    $rootScope.payMentTariff = $scope.payment.countPeople <= 10 ? 45 : 42;
                } else {
                    $rootScope.payMentTariff = $scope.payment.countPeople <= 10 ? 55 : 50;
                }
                $scope.myWidth = $scope.changeColor($scope.payment.countMonth) + '%';
                $scope.colors.twenty = '';
                $scope.colors.fifteen = '';
                $scope.colors.ten = '';
                $scope.colors.five = '#312E37';
            } else if ($scope.payment.countMonth > 0 && $scope.payment.countMonth <= 2) {
                if ($scope.isOldAccount) {
                    $rootScope.payMentTariff = $scope.payment.countPeople <= 10 ? 50 : 47;
                } else {
                    $rootScope.payMentTariff = $scope.payment.countPeople <= 10 ? 60 : 55;
                }
                $scope.myWidth = $scope.changeColor($scope.payment.countMonth) + '%';
                $scope.colors.twenty = '';
                $scope.colors.fifteen = '';
                $scope.colors.ten = '';
                $scope.colors.five = '';
            } else if ($scope.payment.countMonth === 0) {
                $rootScope.payMentTariff = 0;
                $scope.colors.zero = '#312E37';
                $scope.colors.twenty = '';
                $scope.colors.fifteen = '';
                $scope.colors.ten = '';
                $scope.colors.five = '';
                $scope.myWidth = $scope.changeColor($scope.payment.countMonth) + '%';
            }

            if ($scope.tarif2 == 'standard90' && $scope.payment.countMonth === 12) {
                $rootScope.payMentTariff = 80;
                $scope.myWidth = $scope.changeColor($scope.payment.countMonth) + '%';
                $scope.colors.twenty = '#312E37';
                $scope.colors.fifteen = '#312E37';
                $scope.colors.ten = '#312E37';
                $scope.colors.five = '#312E37';
            } else if (
                $scope.tarif2 == 'standard90' &&
                $scope.payment.countMonth > 2 &&
                $scope.payment.countMonth <= 11
            ) {
                $rootScope.payMentTariff = 90;
                $scope.myWidth = $scope.changeColor($scope.payment.countMonth) + '%';
                $scope.colors.twenty = '';
                $scope.colors.fifteen = '';
                $scope.colors.ten = '#312E37';
                $scope.colors.five = '#312E37';
            } else if (
                $scope.tarif2 == 'standard90' &&
                $scope.payment.countMonth > 0 &&
                $scope.payment.countMonth <= 2
            ) {
                $rootScope.payMentTariff = 100;
                $scope.myWidth = $scope.changeColor($scope.payment.countMonth) + '%';
                $scope.colors.twenty = '';
                $scope.colors.fifteen = '';
                $scope.colors.ten = '';
                $scope.colors.five = '#312E37';
            } else if ($scope.tarif2 == 'standard90' && $scope.payment.countMonth === 0) {
                $rootScope.payMentTariff = 0;
                $scope.colors.zero = '#312E37';
                $scope.colors.twenty = '';
                $scope.colors.fifteen = '';
                $scope.colors.ten = '';
                $scope.colors.five = '';
                $scope.myWidth = $scope.changeColor($scope.payment.countMonth) + '%';
            }

            if ($scope.tarif === 'corporate') {
                $scope.monthRate = 750;
                $scope.payment.countPeople = 25;

                const price = Math.floor($scope.monthRate * $scope.payment.countMonth);
                $scope.bonusAmount = Math.floor((price / 100) * $scope.bonus);
                if ($scope.isReferralDiscount) $scope.price = price - price * 0.1 - $scope.bonusAmount;
                else $scope.price = price - $scope.bonusAmount;
            } else {
                if ($rootScope.customTariffValueTwo && $scope.fixedTariff) {
                    $rootScope.payMentTariff = $rootScope.customTariffValueTwo;
                }

                $scope.payment.countPeople = $scope.payment.countPeople || $scope.paidUsersForSelect.length;
                $scope.monthRate = $scope.monthRate || 36;
                $scope.price =
                    Math.floor($scope.payment.countMonth * $rootScope.payMentTariff) * $scope.payment.countPeople;
                $scope.bonusAmount = Math.floor(($scope.price / 100) * $scope.bonus);
                $scope.priceWithBonus = $scope.price + $scope.bonusAmount;
                $rootScope.price = $scope.price;
            }

            $scope.limitedMonthRange = $scope.payment.countPeople === 1;

            $scope.hintPayment = $rootScope.me.orgParams.allowOneMonthPayment !== 'true';

            Pay.paymentInfo.countPeople = $scope.payment.countPeople;
            Pay.paymentInfo.countMonths = $scope.payment.countMonth;
            $scope.payment.countPeople < $scope.paidUsers
                ? ($scope.peopleModalShow = true)
                : ($scope.peopleModalShow = false);

            $scope.$$phase || $scope.$apply();
        }

        function initController() {
            if ($state.current.name === 'blocked-account' && $rootScope.me.orgParams.block !== 'Y') {
                $state.go('organizer');
            }
            if ($rootScope.me.orgParams.tarif === 'corporate') $location.path('/pay-corporate');
            if (Service.getUrlVars($location.$$absUrl).duplicate === 'true') {
                openDuplicateCardModal();
            }
            $rootScope.blockUser = $rootScope.blockUser || false;
            $scope.paidUsersForSelect = [];
            $scope.isEditingPaymentsActive = false;
            $scope.isOnBilling = false;
            $scope.hintPayment = false;
            $scope.bonuce = 10;
            const refDiscountTillDate = $rootScope.me['orgParams']['refDiscountTillDate'];
            $scope.isReferralDiscount = (() => refDiscountTillDate && new Date() < new Date(refDiscountTillDate))();
            $scope.itsClear = false;
            $scope.peopleModalShow = false;
            $rootScope.payCardOrInvoice = '';
            $scope.paymentHistory = { payment: false, transitions: false };
            $scope.showFreeTariffPayment = false;
            $scope.myWidth = '';
            $scope.getReferralDiscountEndDate = (() => $filter('dateFormatSimple')(new Date(refDiscountTillDate)))();
            $scope.months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
            $scope.payment = {
                countMonth: 3,
                countPeople: 0,
            };
            $scope.colors = {
                zero: '00B549',
                five: '',
                ten: '',
                fifteen: '',
                twenty: '',
            };
            $scope.$watch(
                'payment',
                () => {
                    calculatePaymentData();
                },
                true,
            );
            initPayPage();
            $scope.regularPaySumSelect = ['$50', '$100', '$300', '$500', '$1000'];
            $scope.checkPay();
        }

        initController();
    },
]);
