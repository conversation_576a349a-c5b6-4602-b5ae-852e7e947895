controller.controller('testsAndForms', [
    '$scope',
    'Test',
    'Service',
    'notificationService',
    '$filter',
    '$rootScope',
    '$uibModal',
    '$window',
    '$stateParams',
    '$location',
    'FileInit',
    'serverAddress',
    'Vacancy',
    '$localStorage',
    'Person',
    'Mail',
    '$translate',
    'Email',
    '$timeout',
    'Candidate',
    '$state',
    function (
        $scope,
        Test,
        Service,
        notificationService,
        $filter,
        $rootScope,
        $uibModal,
        $window,
        $stateParams,
        $location,
        FileInit,
        serverAddress,
        Vacancy,
        $localStorage,
        Person,
        Mail,
        $translate,
        Email,
        $timeout,
        Candidate,
        $state,
    ) {
        initController();
        $scope.candidatesList = [];
        $scope.groupNameList = [];
        $scope.showCandidatesList = false;
        $scope.inputSearchThreshold = 2;

        $scope.callbackFileForTemplate = function (resp, names) {
            $scope.fileForSave.push({ fileId: resp, fileName: names });
            $('#file').val('');
        };
        $scope.callbackTestQuestionLogo = function (photo, index) {
            $rootScope.testQuestionLogo[index] = photo;
            $rootScope.testQuestionLogoLink = photo ? $scope.serverAddress + '/getapp?id=' + photo + '&d=true' : null;
        };
        $scope.changeTab = function (tab, TestId) {
            var toEdit = function () {
                $scope.editTestId = TestId;
                Test.getTest(
                    { id: TestId },
                    function (resp) {
                        if (resp.status == 'ok') {
                            var editTestParam = {};
                            angular.copy(resp.object, editTestParam);
                            $scope.testQuestion = resp.object.questions;
                            newTestParamInit();
                            angular.forEach(resp.object.questions, function (question, questId) {
                                editTestParam.questions[questId] = {};
                                $scope.numMenu = question.num;
                                editTestParam.questions[questId].num = question.num;
                                editTestParam.questions[questId].id = question.id;
                                editTestParam.questions[questId].text = question.text;
                                editTestParam.questions[questId].points = question.points;
                                editTestParam.questions[questId].imageId = question.imageId;
                                $rootScope.testQuestionLogo.push(question.imageId);
                                editTestParam.questions[questId].variantsArray = [];
                                editTestParam.questions[questId].rightAnswersArray = [];
                                editTestParam.questions[questId].answerType = !(
                                    question.answerType == 'few_answers' || question.answerType == 'one_answer'
                                );
                                if (questId)
                                    $scope.errorFields.newTestParam.questions.push({
                                        questionText: false,
                                        questionPoints: false,
                                        answers: [{ answerText: false }],
                                    });
                                angular.forEach(question.variantsArray, function (variant, index) {
                                    var isCorrectAnsw = false;
                                    if (question.rightAnswersArray.includes(variant)) {
                                        isCorrectAnsw = true;
                                    }
                                    var variantFOrPush = {
                                        value: variant,
                                        isCorrect: isCorrectAnsw,
                                    };
                                    editTestParam.questions[questId].variantsArray.push(variantFOrPush);
                                    $scope.errorFields.newTestParam.questions[questId].answers.push({
                                        answerText: false,
                                    });
                                });
                            });
                            $scope.newTestParam.testName = editTestParam.testName ? editTestParam.testName : null;
                            $scope.newTestParam.timeLimit = editTestParam.timeLimit ? editTestParam.timeLimit : null;
                            $scope.newTestParam.questions = editTestParam.questions ? editTestParam.questions : null;
                            $scope.newTestParam.norms = editTestParam.norms ? editTestParam.norms : null;
                            $scope.newTestParam.description = editTestParam.description
                                ? editTestParam.description
                                : null;
                            $scope.newTestParam.id = editTestParam.id;
                            $scope.timeLimit.hh = Math.floor(editTestParam.timeLimit / 3600);
                            $scope.timeLimit.mm = Math.floor(
                                (editTestParam.timeLimit - $scope.timeLimit.hh * 3600) / 60,
                            );
                        } else {
                            $location.path('/organizer');
                            notificationService.error(resp.message);
                        }
                    },
                    function (err) {
                        notificationService.error(err.message);
                    },
                );
            };
            $scope.optionTab = tab;
            switch (tab) {
                case 'show':
                    getAllTests();
                    newTestParamInit();
                    $scope.fieldCheck = false;
                    $rootScope.testQuestionLogo = [];
                    break;
                case 'edit':
                    $scope.fieldCheck = false;
                    toEdit();
                    break;
                case 'template':
                    $scope.optionTab = 'template';
                    break;
                default:
                    if ($scope.optionTab == 'add') {
                        $rootScope.testQuestionLogo = [];
                        newTestParamInit();
                    }
            }
        };
        $scope.errorFields = {
            newTestParam: {
                testName: false,
                testTimeHour: false,
                testTimeMinute: false,
                questions: [{ questionText: false, questionPoints: false, answers: [{ answerText: false }] }],
            },
        };

        $scope.changeAnswerType = function ($event, questInd) {
            $scope.newTestParam.questions[questInd].answerType = !$scope.newTestParam.questions[questInd].answerType;

            if ($scope.newTestParam.questions[questInd].answerType) {
                $scope.newTestParam.questions[questInd].variantsArray = [];
                $scope.newTestParam.questions[questInd].rightAnswersArray = [];
                $($event.currentTarget).find('#cmn-toggle-9').prop('checked', true);
            } else {
                $scope.newTestParam.questions[questInd].variantsArray.push({
                    value: null,
                    isCorrect: true,
                });
                $($event.currentTarget).find('#cmn-toggle-9').prop('checked', false);
            }

            $scope.$$phase || $scope.$apply();
        };
        $scope.changeRightAnswer = function ($event, { questIndex, answerIndex }) {
            if ($scope.newTestParam.questions[questIndex].variantsArray[answerIndex].isCorrect) {
                $scope.newTestParam.questions[questIndex].variantsArray[answerIndex].isCorrect = false;
                $scope.newTestParam.questions[questIndex].rightAnswersArray.splice(answerIndex, 1);
            } else {
                $scope.newTestParam.questions[questIndex].variantsArray[answerIndex].isCorrect = true;
                $scope.newTestParam.questions[questIndex].rightAnswersArray.push(answerIndex);
            }
            $scope.$$phase || $scope.$apply();
        };
        $scope.answerAdd = function (questInd) {
            $scope.newTestParam.questions[questInd].variantsArray.push({
                value: null,
                isCorrect: false,
            });

            $scope.errorFields.newTestParam.questions[questInd].answers.push({ answerText: false });
        };
        $scope.questAdd = function () {
            $scope.newTestParam.questions.push({
                text: null,
                points: null,
                answerType: false,
                variantsArray: [{ value: null, isCorrect: true }],
                rightAnswersArray: [],
                num: null,
                imageId: null,
            });
            $scope.errorFields.newTestParam.questions.push({
                questionText: false,
                questionPoints: false,
                answers: [{ answerText: false }],
            });
        };
        $scope.deleteQuestion = function (questIndex) {
            $scope.newTestParam.questions.splice(questIndex, 1);
        };
        $scope.deleteAnswer = function (questIndex, answIndex) {
            $scope.newTestParam.questions[questIndex].variantsArray.splice(answIndex, 1);
        };
        $scope.removeObligatory = function () {
            $('.obligatory').each(function () {
                if ($(this)[0].value == '' || $(this)[0].value === null) {
                    $(this).removeClass('empty');
                }
            });
        };
        $scope.removeLogoTestCandidateQuestion = function (questIndex, question, e) {
            $scope.questIndex = questIndex;
            if (question.imageId == null) {
                $(e.target).offsetParent().hide();
                $(e.target)
                    .offsetParent()
                    .offsetParent()
                    .find('.img_wrap' + questIndex)
                    .hide();
                $rootScope.testQuestionLogo[questIndex] = undefined;
                $('#logo-button' + questIndex).show();
            } else {
            }
            angular.forEach($scope.testQuestion, function (val) {
                if (val.id === question.id) {
                    $rootScope.testQuestionLogo[questIndex] = undefined;
                    $('.owner_photo_wrap' + questIndex)
                        .find('img')
                        .hide();
                    $('.owner_photo_wrap' + questIndex)
                        .find('#owner_photo_bubble_wrap')
                        .hide();
                }
            });
        };
        $scope.selectCorrectAnswer = function (question, answers) {
            if (answers.isCorrect) question.noCorrectAnswerInQuestion = false;
        };
        $scope.updateVacancyAutoTestResponse = function (autoTestResponse) {
            $scope.autoTestResponse = angular.copy(autoTestResponse);
        };
        $scope.saveTest = function () {
            let questionPointError = false;

            var emptyQuestion = false;
            var firstNoAnswerIndex = null;
            $scope.noAnswerIndex = null;
            $scope.fieldCheck = false;
            $scope.noCorrectAnswerInQuestion = false;
            $scope.errorFields.newTestParam.testTimeHour = false;
            $scope.errorFields.newTestParam.testTimeMinute = false;

            if (!$scope.newTestParam.testName) {
                $scope.errorFields.newTestParam.testName = true;
            }

            angular.forEach($scope.newTestParam.questions, function (question, index) {
                if (
                    (question.text === '' ||
                        question.text === null ||
                        !question.points ||
                        question.points === '' ||
                        question.points < 0) &&
                    !question.answerType
                ) {
                    emptyQuestion = true;
                }

                if ((question.text === '' || question.text === null) && !question.answerType) {
                    $scope.errorFields.newTestParam.questions[index].questionText = true;
                }

                if ((!question.points || question.points === '' || question.points < 0) && !question.answerType) {
                    $scope.errorFields.newTestParam.questions[index].questionPoints = true;
                }

                if (question.points < 0 || question.points > 1000) {
                    questionPointError = true;
                }

                let checkForCorrectAnswer = question.variantsArray.every(function (variant) {
                    return !variant.isCorrect;
                });

                if (
                    (checkForCorrectAnswer &&
                        !$scope.newTestParam.questions[$scope.newTestParam.questions.indexOf(question)].answerType) ||
                    ($scope.newTestParam.questions[$scope.newTestParam.questions.indexOf(question)].answerType &&
                        !question.text)
                ) {
                    question.noCorrectAnswerInQuestion = true;
                    $scope.noCorrectAnswerInQuestion = true;
                    $scope.noAnswerIndex = index;
                } else {
                    question.noCorrectAnswerInQuestion = false;
                }

                if (question.noCorrectAnswerInQuestion && firstNoAnswerIndex === null) {
                    firstNoAnswerIndex = index;
                }

                angular.forEach(question.variantsArray, function (variant, answerIndex) {
                    if (variant.value == '' || variant.value == null) {
                        emptyQuestion = true;
                        $scope.errorFields.newTestParam.questions[index].answers[answerIndex].answerText = true;
                    }
                });
            });

            if (
                $scope.newTestParam.testName !== null &&
                $scope.newTestParam.testName !== '' &&
                !emptyQuestion &&
                !$scope.noCorrectAnswerInQuestion
            ) {
                var testForSend = {};
                angular.copy($scope.newTestParam, testForSend);
                angular.forEach($scope.newTestParam.questions, function (quest, key) {
                    testForSend.questions[key].num = key + 1;
                    testForSend.questions[key].rightAnswersArray = [];
                    testForSend.questions[key].imageId = $rootScope.testQuestionLogo[key];
                    angular.forEach(quest.variantsArray, function (variant, index) {
                        if (variant.isCorrect) {
                            if (variant.value) testForSend.questions[key].rightAnswersArray.push(variant.value);
                        }
                    });
                });
                angular.forEach($scope.newTestParam.questions, function (quest, key) {
                    testForSend.questions[key].variantsArray = [];
                    angular.forEach(quest.variantsArray, function (answ, ind) {
                        if (answ.value) {
                            testForSend.questions[key].variantsArray.push(answ.value);
                        }
                    });
                    if (quest.answerType) {
                        testForSend.questions[key].answerType = 'task_question';
                    } else {
                        if (testForSend.questions[key].rightAnswersArray.length == 1) {
                            testForSend.questions[key].answerType = 'one_answer';
                        } else {
                            testForSend.questions[key].answerType = 'few_answers';
                        }
                    }
                });
                let timeFlag = false;
                if ($scope.timeLimit.hh <= 24 && $scope.timeLimit.mm <= 60) {
                    testForSend.timeLimit = ($scope.timeLimit.hh * 60 + Number($scope.timeLimit.mm)) * 60;
                    timeFlag = true;
                }

                if ($scope.timeLimit.hh < 0) {
                    timeFlag = false;
                    $scope.errorFields.newTestParam.testTimeHour = true;
                }

                if ($scope.timeLimit.mm < 0) {
                    timeFlag = false;
                    $scope.errorFields.newTestParam.testTimeMinute = true;
                }

                if ($scope.timeLimit.hh == 0 && $scope.timeLimit.mm == 0) {
                    timeFlag = false;
                    $scope.errorFields.newTestParam.testTimeHour = true;
                    $scope.errorFields.newTestParam.testTimeMinute = true;
                }

                testForSend.status = 'A';
                if (timeFlag) {
                    $rootScope.loading = true;
                    Test.saveTest(
                        testForSend,
                        function (resp) {
                            if (resp.status == 'ok') {
                                $rootScope.loading = false;
                                if ($scope.optionTab == 'add') {
                                    notificationService.success(
                                        $filter('translate')('You created the new test:') + ' ' + testForSend.testName,
                                    );
                                    if ($scope.autoTestResponse.enabled && parseInt($scope.autoTestResponse.value)) {
                                        $scope.autoTestResponse.onSave(resp.object);
                                        $scope.autoTestResponse = {};
                                    }
                                    $('.show-tab').css({ display: 'block' });
                                } else {
                                    notificationService.success(
                                        $filter('translate')('Changes are saved') + ' ' + testForSend.testName,
                                    );
                                    if (parseInt($scope.autoTestResponse.value)) {
                                        $scope.autoTestResponse.onSave(resp.object);
                                        $scope.autoTestResponse = {};
                                    }
                                }
                                $scope.changeTab('show');
                                $location.path('/candidate/tests');
                            } else {
                                $rootScope.loading = false;
                                notificationService.error(resp.message);
                            }
                        },
                        function (err) {
                            $rootScope.loading = false;
                            notificationService.error(err.message);
                        },
                    );
                } else {
                    notificationService.error($filter('translate')('incorrect time for the test'));
                }
            } else {
                let emptyFilesError =
                    $scope.newTestParam.testName === null || $scope.newTestParam.testName === '' || emptyQuestion;

                $('.obligatory').each(function () {
                    if ($(this)[0].value == '' || $(this)[0].value === null) {
                        $(this).addClass('empty');
                    }
                });

                if (emptyFilesError && !questionPointError) {
                    notificationService.error($filter('translate')('You should fill all obligatory fields.'));
                } else if (questionPointError) {
                    notificationService.error($filter('translate')('Enter a numeric value from 0 to 1000.'));
                } else if (!emptyFilesError && $scope.noCorrectAnswerInQuestion) {
                    let element = $('#question-' + firstNoAnswerIndex);
                    $('html, body').animate(
                        {
                            scrollTop: element.position().top + element.height(),
                        },
                        'slow',
                    );
                    return;
                }
            }
        };
        $scope.showDeleteTest = function (testId, testName) {
            removeTestModal();
            $rootScope.testForDelete = { testId: testId, testName: testName };
        };
        $rootScope.closeModal = function () {
            $scope.modalInstance.close();
        };
        $rootScope.deleteTest = function (testId) {
            Test.deleteTest(
                { id: testId },
                function () {
                    notificationService.success($filter('translate')('The test was deleted'));
                    $scope.changeTab('show');
                    $location.path('/candidate/tests');
                },
                function (err) {
                    notificationService.error(err.message);
                },
            );
            $rootScope.closeModal();
        };
        $rootScope.changeSearchType = function (param) {
            $window.location.replace('/!#/candidates');
            $rootScope.changeSearchTypeNotFromCandidates = param;
        };
        $scope.getTestFunc = function () {
            Test.getTest(
                {
                    id: $stateParams.id,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        $rootScope.sendOneTest = resp.object;
                        $rootScope.linkTest =
                            $location.$$protocol +
                            '://' +
                            $location.$$host +
                            '/i/pass_the_test/' +
                            $rootScope.sendOneTest.id;
                        getTestTemplates();
                        if ($scope.testTemplate) {
                            $scope.textEmailTestCandidate = $scope.testTemplate.text;
                            $scope.emailTestCandidate = $scope.testTemplate.title;
                            $scope.$$phase || $scope.$apply();
                        }
                    } else {
                        notificationService.error(resp.message);
                    }
                },
            );
        };
        $scope.deleteCandidate = function (candidate) {
            angular.forEach($scope.groupNameList, function (nval, ind) {
                if (nval.id === candidate.id) {
                    $scope.groupNameList.splice(ind, 1);
                    angular.forEach($('.select2-search-choice'), function (val) {
                        if ($(val).find('div')[0].innerText == nval.text) {
                            document.getElementById('testCandidate').setAttribute('value', $scope.groupNameList);
                            $(val).remove();
                        }
                    });
                }
            });
            $scope.sendTestRequest = [];
        };
        $rootScope.changesEmail = function (text, id) {
            $scope.emailCandidateId = id;
            $rootScope.emailCandidate = text;
            $scope.$$phase || $scope.$apply();
        };

        $scope.changeEmailTestCandidate = function (groupList, candidate) {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/change-email-test-candidate.html',
                scope: $scope,
                resolve: {},
                controller: [
                    '$scope',
                    function ($scope) {
                        $scope.onChangeUserEmail = (value) => {
                            $rootScope.emailCandidate = value;
                            $scope.$$phase || $scope.$apply();
                        };
                    },
                ],
            });

            if (candidate != undefined) {
                $rootScope.groupNameList = groupList;

                angular.forEach(groupList.contacts, function (val) {
                    if (val.type == 'email' && val.default) {
                        $rootScope.emailCandidate = val.value.split(' ')[0].replace(/,/g, '');
                        $scope.oneCandidateEmail = [
                            { candidateId: val.personId, email: val.value.split(' ')[0].replace(/,/g, '') },
                        ];
                    }
                });
            } else {
                $rootScope.groupNameList = groupList;
                if (!$rootScope.groupNameList.email) {
                    $rootScope.emailCandidate = '';
                } else {
                    $rootScope.emailCandidate = $rootScope.groupNameList.email[0].value.split(' ')[0].replace(/,/g, '');
                }
            }
        };
        $rootScope.saveChangeEmailTestCandidate = function (candidateId) {
            if ($rootScope.emailCandidate.indexOf('@') > 0) {
                // email.css('border', '2px solid #61B452');
                if ($state.$current.name === 'candidate-send-test-from-candidate') {
                    if (!$rootScope.groupNameList.contacts) {
                        $rootScope.groupNameList.contacts = [];
                    }

                    if (!$rootScope.groupNameList.contacts.some((item) => item.type === 'email')) {
                        $rootScope.groupNameList.contacts.push({
                            type: 'email',
                            value: $rootScope.emailCandidate,
                            default: true,
                        });
                    } else {
                        angular.forEach($rootScope.groupNameList.contacts, function (val) {
                            if (val.type === 'email') {
                                if (val.default) {
                                    val.value = $rootScope.emailCandidate;
                                } else if (
                                    !$rootScope.groupNameList.contacts.some(
                                        (item) => val.type === 'email' && item.default,
                                    )
                                ) {
                                    val.default = true;
                                }
                            }
                        });
                    }
                } else {
                    $rootScope.groupNameList.email = [{ value: $rootScope.emailCandidate }];
                    // $rootScope.groupNameList.email[0].value = $rootScope.emailCandidate;
                }
                $scope.sendTestRequest.push({
                    candidateId,
                    email: $rootScope.emailCandidate,
                });

                function removeDuplicates(arr, prop) {
                    var new_arr = [];
                    var lookup = {};

                    for (var i in arr) {
                        lookup[arr[i][prop]] = arr[i];
                    }

                    for (i in lookup) {
                        new_arr.push(lookup[i]);
                    }
                    return new_arr;
                }

                var uniqueArray = removeDuplicates($scope.sendTestRequest, 'candidateId');
                $scope.sendTestRequest = uniqueArray;
                $scope.oneCandidateEmail = $scope.oneCandidateEmail
                    ? [{ candidateId, email: $rootScope.emailCandidate }]
                    : null;

                $rootScope.closeModal();
            } else {
                // email.css({
                //     border: '2px solid #C62828',
                //     'background-color': '#FFF6F7',
                // });
                // email.focus();
                $('#error-password').removeClass('hidden');
                $('#error-password').html('Кажется, вы ввели неверный email. Пожалуйста, попробуйте ещё раз.');
                setTimeout(function () {
                    $('#error-password').hide();
                }, 5000);
            }
        };
        $scope.updateSendingData = function () {
            if ($scope.groupNameList) {
                if (Array.isArray($scope.groupNameList)) {
                    angular.forEach($scope.groupNameList, function (nval, ind) {
                        if (nval !== undefined) {
                            $scope.candidate = nval;
                            if (nval.email) {
                                $rootScope.emailCandidate = nval.email[0].value;
                            }
                            $scope.groupNameList.every((candidate) => {
                                if (candidate.email) {
                                    return candidate.email;
                                }
                                return false;
                            })
                                ? $scope.sendTestRequest.push({
                                      candidateId: $scope.candidate.id,
                                      email: $rootScope.emailCandidate,
                                  })
                                : ($scope.sendTestRequest = []);
                        }
                    });
                } else {
                    $scope.candidate = $scope.groupNameList;

                    for (let i in $scope.candidate.contacts) {
                        if ($scope.candidate.contacts[i].type === 'email' && $scope.candidate.contacts[i].default) {
                            $scope.sendTestRequest.push({
                                candidateId: $scope.candidate.candidateId,
                                email: $scope.candidate.contacts[i].value,
                            });
                        }
                    }
                }
            }
        };
        $scope.sendTestToCandidate = function () {
            if (($rootScope.me.emails.length === 0 && !$scope.noAllowedMails) || $scope.emailsConnectionLost) return;

            const candidate = ($rootScope.fromCandidate && $rootScope.fromCandidate[0]) || null;
            const emailContacts =
                candidate && candidate.contacts && candidate.contacts.filter((contact) => contact.type === 'email');

            $scope.updateSendingData();

            if (emailContacts && !emailContacts.length) {
                notificationService.error(
                    $filter('translate')('Please add an email before sending a test to this candidate'),
                );
                return;
            } else if (Test.checkTestLink($scope.textEmailTestCandidate)) {
                let uniqueSendTestRequest = [
                    ...new Map($scope.sendTestRequest.map((obj) => [JSON.stringify(obj), obj])).values(),
                ];

                if ($scope.oneCandidateEmail) {
                    $scope.oneCandidateEmail = [$scope.oneCandidateEmail[0]];
                }

                $rootScope.loading = true;
                Test.sendTest(
                    {
                        emailFrom: $rootScope.emailFrom,
                        testId: $rootScope.sendOneTest.id,
                        appointments: $scope.oneCandidateEmail ? $scope.oneCandidateEmail : uniqueSendTestRequest,
                        template: {
                            type: 'testForCandidate',
                            title: $scope.emailTestCandidate,
                            text: $scope.textEmailTestCandidate,
                            fileId: $scope.fileForSave.length > 0 ? $scope.fileForSave[0].fileId : null,
                            fileName: $scope.fileForSave.length > 0 ? $scope.fileForSave[0].fileName : null,
                        },
                    },
                    function (resp) {
                        $rootScope.loading = false;
                        if (resp.status == 'ok') {
                            notificationService.success(
                                $filter('translate')('The email with a link to the test was successfully sent'),
                            );
                            if ($rootScope.activePage == 'Send test candidate to email') {
                                $location.path('/candidate/tests');
                            } else if ($scope.itsFromPipeline) {
                                $location.path('/vacancies/' + $scope.vacancyFromPipeline.localId);
                            } else if ($rootScope.activePage == 'Send test candidate to email from vacancy') {
                                $location.path('/vacancies/' + $rootScope.candidatesInStages[0].vacancyId.localId);
                            } else if ($rootScope.activePage == 'Send test candidate to email from candidate') {
                                $location.path('/candidates/' + $rootScope.candidateToTest.localId);
                            }
                        } else {
                            notificationService.error(resp.message);
                        }
                    },
                    function (error) {
                        notificationService.error(`Send test response status: ${error.status}`);
                        $rootScope.loading = false;
                    },
                );
            }
        };
        $scope.getParamsForMailTo = function (text, candidateId, candidateName, candidateEmail) {
            return Test.insertTestLink(
                {
                    testId: $rootScope.sendOneTest.id,
                    candidateId: candidateId,
                    email: candidateEmail,
                    name: candidateName,
                },
                text,
            );
        };
        $rootScope.setDocCounter = function () {
            $scope.currentDocPreviewPage = 0;
        };
        $scope.prevDoc = function () {
            $scope.currentDocPreviewPage -= 1;
        };
        $scope.nextDoc = function () {
            $scope.currentDocPreviewPage += 1;
        };
        $scope.checkEmailsOfCandidate = function (candidate) {
            return (
                candidate.candidateId.contacts &&
                candidate.candidateId.contacts.some((contact) => contact.type === 'email')
            );
        };

        $scope.addCandidateLinkToText = (event, objCandidate) => {
            objCandidate.checked = event.target.checked;

            if (objCandidate.checked) {
                if ($scope.checkEmailsOfCandidate(objCandidate)) {
                    $scope.objCandiateEmail.push({
                        candidateId: objCandidate.candidateId.candidateId,
                        email: objCandidate.candidateId.contacts.filter(
                            (contact) => contact.type === 'email' && contact.default,
                        )[0].value,
                    });
                } else {
                    notificationService.error(
                        $filter('translate')('Please add an email before sending a test to this candidate'),
                    );
                }
            } else {
                angular.forEach($scope.objCandiateEmail, function (val, ind) {
                    if (val.candidateId === objCandidate.candidate) {
                        $scope.objCandiateEmail.splice(ind, 1);
                    }
                });
            }

            $scope.sendTestRequest = [];
            $scope.sendTestRequest = $scope.objCandiateEmail;

            $scope.$$phase || $scope.$apply();
        };

        $scope.removeFile = function (fileId) {
            angular.forEach($scope.fileForSave, function (val, ind) {
                if (val.fileId === fileId) {
                    $scope.fileForSave.splice(ind, 1);
                }
            });
        };
        $scope.goBack = function () {
            history.back();
        };
        $scope.addEmailFromWhatSend = function (email) {
            $scope.selectedEmail.emailFrom.email = email.email;
            $scope.textEmailTestCandidateEmailTestCandidate = Email.insertSignatures(
                $scope.textEmailTestCandidate,
                email.signature,
            );
        };

        $scope.updateTestTitle = function (value) {
            $scope.newTestParam.testName = value;
            $scope.errorFields.newTestParam.testName = false;
            $scope.$$phase || $scope.$apply();
        };

        $scope.updateTestDescription = function (value) {
            $scope.newTestParam.description = value;
            $scope.$$phase || $scope.$apply();
        };

        $scope.changeAnswersText = function (value, { questIndex, answerIndex }) {
            $scope.newTestParam.questions[questIndex].variantsArray[answerIndex].value = value;
            $scope.errorFields.newTestParam.questions[questIndex].answers[answerIndex].answerText = false;
            $scope.$$phase || $scope.$apply();
        };

        $scope.changeQuestionText = function (value, questIndex) {
            $scope.newTestParam.questions[questIndex].text = value;
            $scope.errorFields.newTestParam.questions[questIndex].questionText = false;
            $scope.$$phase || $scope.$apply();
        };

        $scope.changeQuestionPoints = function (value, questIndex) {
            $scope.newTestParam.questions[questIndex].points = value;
            $scope.errorFields.newTestParam.questions[questIndex].questionPoints = false;
            $scope.$$phase || $scope.$apply();
        };

        $scope.updateTimeLimitHours = function (value) {
            $scope.timeLimit.hh = value;
            $scope.$$phase || $scope.$apply();
        };

        $scope.updateTimeLimitMinutes = function (value) {
            $scope.timeLimit.mm = value;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onSelectCandidates = (value) => {
            $scope.groupNameList = value;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeEmailTitle = (value) => {
            $scope.emailTestCandidate = value;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.getCandidates = function (data, callback) {
            let results = [];

            Candidate.getAllCandidates({
                name: data.trim(),
                displayContacts: true,
                displayLanguages: true,
                displayRelocatedRegion: true,
            }).then((response) => {
                if (response.objects !== undefined) {
                    angular.forEach(response.objects, function (item) {
                        let email;

                        if (item.contacts !== undefined) {
                            const contactEmail = item.contacts.filter(function (contact) {
                                return contact.type === 'email';
                            });

                            if (contactEmail.length > 0) {
                                email = contactEmail;
                            }
                        }

                        results.push({
                            ...item,
                            id: item.candidateId,
                            label: $rootScope.useAmericanNameStyle
                                ? item.fullNameEn +
                                  (item.position && item.position.trim() !== '' ? `, ${item.position}` : '')
                                : item.fullName +
                                  (item.position && item.position.trim() !== '' ? `, ${item.position}` : ''),
                            fullName: $rootScope.useAmericanNameStyle ? item.fullNameEn : item.fullName,
                            email: email,
                            short: item.firstName + ' ' + item.lastName,
                        });
                    });
                }

                $scope.candidatesList = results;
                callback($scope.candidatesList);
            });
            $scope.$$phase || $scope.$apply();
        };

        function getTestTemplates() {
            if ($scope.emptyTestTemplate) {
                setTemplatesParams();
                return;
            }

            Mail.getTestTemplate().then(
                (resp) => {
                    if (resp.object.fileId && resp.object.fileName) {
                        $scope.fileForSave = [];
                        $scope.fileForSave.push({
                            fileId: resp.object.fileId,
                            fileName: resp.object.fileName,
                        });
                    }
                    $scope.emptyTestTemplate = resp.object;
                    if ($rootScope.sendOneTest.testName && !$rootScope.loading) {
                        $scope.getTestFunc();
                    }
                    setTemplatesParams();
                },
                (error) => {
                    console.error(error);
                },
            );

            function setTemplatesParams() {
                const testName = $rootScope.sendOneTest.testName || $scope.testName;
                let text = $scope.emptyTestTemplate.text
                        .replace('[[recruiter&#39;s name]]', $rootScope.me.fullName)
                        .replace(
                            "[[recruiter's name]]",
                            $rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName,
                        ),
                    title = $scope.emptyTestTemplate.title.replace('{test_name}', testName);

                if ($rootScope.activePage == 'Send test candidate to email from candidate') {
                    if ($rootScope.fromCandidate)
                        text = text
                            .replace(/{candidate_name}/gi, $rootScope.fromCandidate[0].firstName)
                            .replace(
                                /{full_name}/gi,
                                $rootScope.useAmericanNameStyle
                                    ? $rootScope.fromCandidate[0].fullNameEn
                                    : $rootScope.fromCandidate[0].fullName,
                            );
                }
                text = text.replace('{test_name}', testName);

                $scope.testTemplate = { text, title };
            }
        }

        function getAllVacancies() {
            Vacancy.all(Vacancy.searchOptions(), function (response) {
                $scope.vacancies = response.objects;
            });
        }

        function getVacanciesByIds(ids) {
            const vacancies = [];

            $scope.vacancies.forEach((vacancy) => {
                ids.forEach((id) => {
                    if (vacancy.vacancyId === id) {
                        vacancies.push(vacancy);
                    }
                });
            });

            return vacancies;
        }

        function removeTestEnabledInVacancyModal() {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/removeTestEnabledInVacancy.html',
                size: '',
                scope: $scope,
                resolve: function () {},
            });
        }

        function removeTestModal() {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                windowClass: 'secondary-modal',
                templateUrl: '../partials/modal/test-delete.html',
                size: '',
                resolve: function () {},
            });
        }

        function newTestParamInit() {
            $scope.timeLimit = { hh: null, mm: null };
            $scope.errorFields = {
                newTestParam: {
                    testName: false,
                    testTimeHour: false,
                    testTimeMinute: false,
                    questions: [{ questionText: false, questionPoints: false, answers: [{ answerText: false }] }],
                },
            };
            $scope.newTestParam = {
                testName: null,
                description: null,
                timeLimit: null,
                questions: [
                    {
                        text: null,
                        points: null,
                        answerType: false,
                        variantsArray: [{ value: null, isCorrect: true }],
                        rightAnswersArray: [],
                        num: 1,
                        imageId: null,
                    },
                ],
            };
            $scope.testPreview = null;
        }

        function getAllTests() {
            Test.getTests(
                function (resp) {
                    if (resp.status == 'ok') {
                        $scope.allTests = resp.objects;
                        if (!$scope.allTests || $scope.allTests.length === 0) {
                            $('.show-tab').css({ display: 'none' });
                            $scope.changeTab('add');
                        }
                    } else {
                        notificationService.error(resp.message);
                    }
                },
                function (err) {
                    notificationService.error(err.message);
                },
            );
        }

        function initController() {
            $scope.optionTab = 'show';
            $scope.textType = false;
            $scope.fieldCheck = false;
            $scope.fileForSave = [];
            $scope.timeLimit = { hh: null, mm: null };
            $rootScope.sendOneTest = {
                id: null,
            };
            $scope.selectedEmail = { emailFrom: null };
            $scope.autoTestResponse = {};
            $scope.addedTest = {};
            FileInit.initVacancyTemplateFileOption($scope, '', '', false, $filter);
            $scope.templateVariables = [
                { name: 'Candidate name', id: '{candidate_name}' },
                { name: 'Candidate full name', id: '{full_name}' },
                { name: 'Test name', id: '{test_name}' },
                {
                    name: 'Link to test',
                    id:
                        $rootScope.currentLang === 'en'
                            ? '{Here will be an individual link to the test for each candidate}'
                            : $rootScope.currentLang === 'ru'
                            ? '{Здесь будет отдельная ссылка на тест для каждого кандидата}'
                            : '{Тут буде окреме посилання на тест для кожного кандидата}',
                },
            ];
            $scope.allTests = [];
            $rootScope.testQuestionLogo = [];
            newTestParamInit();
            getAllTests();
            $scope.sendTestRequest = [];
            initByPageType();
            $scope.objCandiateEmail = [];
            setTimeout(function () {
                $scope.$$phase || $scope.$apply();
                if ($location.path().includes('add')) $scope.changeTab('add');
                if ($location.path().includes('edit')) {
                    $scope.changeTab('edit', $location.path().match(/(?<=edit\/).+/)[0]);
                }
                var myListener = $scope.$on('addedTest', function (event, data) {
                    $scope.testName = data.text;
                    if (data != undefined) {
                        $('.select2-choice').find('abbr').css('margin-top', '-2px');
                        $('#testCandidateFromVacancyId').show();
                        $('.testCandidateFromCandidate').show();
                        $('.showLetter').show();
                        getTestTemplates();
                        $rootScope.sendOneTest.id = data.id;
                        if ($scope.testTemplate) {
                            $scope.textEmailTestCandidate = $scope.testTemplate.text;
                            $scope.textEmailTestCandidate = Email.setDefaultSignature(
                                Email.getDefaultMailbox(),
                                $scope.textEmailTestCandidate,
                            );
                            $scope.emailTestCandidate = $scope.testTemplate.title;
                            $scope.$$phase || $scope.$apply();
                        }
                    }
                });
                $scope.$on('$destroy', myListener);
            }, 20);
            $scope.$on('groupNameList', function (event, groupNameList) {
                $scope.sendTestRequest = [];
                $scope.groupNameList = groupNameList;
                angular.forEach($scope.groupNameList, function (nval, ind) {
                    if (nval !== undefined) {
                        $scope.candidate = nval;
                        if (nval.email) {
                            $rootScope.emailCandidate = nval.email[0].value;
                        }

                        $scope.groupNameList.every((candidate) => {
                            if (candidate.email) {
                                return candidate.email;
                            }
                            return false;
                        })
                            ? $scope.sendTestRequest.push({
                                  candidateId: $scope.candidate.id,
                                  email: $rootScope.emailCandidate,
                              })
                            : ($scope.sendTestRequest = []);
                    }
                });
            });
            $('#testCandidate').on('change', function (e) {
                $('#testCandidate').select2('val', '');
            });
        }

        const _fetchTests = $rootScope.makeDebounce((inputText, callback) => {
            if (inputText?.length && inputText?.length < $scope.inputSearchThreshold) return callback();

            fetch('hr/test/autocompleteTest', {
                method: 'POST',
                body: $rootScope._objectToFormData({ text: inputText }),
            })
                .then((resp) => resp.json())
                .then((data) => {
                    if (data.status !== 'ok') {
                        notificationService.error(data.message);
                    }

                    callback([
                        ...data.objects.map((option) => ({ ...option, label: option.testName, value: option.id })),
                    ]);
                });
        }, 300);

        $scope.getTests = (inputText, callback) => {
            if (inputText?.length && inputText?.length < $scope.inputSearchThreshold) return callback();
            _fetchTests(inputText, callback);
        };

        $scope.onChangeTest = (newValue) => {
            $scope.addedTest = newValue;
            $scope.testName = newValue.label;
            getTestTemplates();
            $rootScope.sendOneTest.id = newValue.id;

            if (newValue) $scope.showCandidatesList = true;

            if ($scope.testTemplate) {
                $scope.textEmailTestCandidate = $scope.testTemplate.text;
                $scope.textEmailTestCandidate = Email.setDefaultSignature(
                    Email.getDefaultMailbox(),
                    $scope.textEmailTestCandidate,
                );
                $scope.emailTestCandidate = $scope.testTemplate.title;
                $scope.$$phase || $scope.$apply();
            }

            $scope.$$phase || $scope.$apply();
        };

        function initByPageType() {
            if ($rootScope.activePage === 'Test page') {
                $scope.testPreview = {};
                Test.getTest(
                    {
                        id: $stateParams.id,
                    },
                    function (resp) {
                        if (resp.status == 'ok') {
                            angular.copy(resp.object, $scope.testPreview);
                            angular.forEach(resp.object.questions, function (question, questInd) {
                                $scope.testPreview.questions[questInd].variantsArray = [];
                                angular.forEach(question.variantsArray, function (variant, variantInd) {
                                    if (question.rightAnswersArray.includes(variant)) {
                                        $scope.testPreview.questions[questInd].variantsArray.push({
                                            value: variant,
                                            isCorrect: true,
                                        });
                                    } else {
                                        $scope.testPreview.questions[questInd].variantsArray.push({
                                            value: variant,
                                            isCorrect: false,
                                        });
                                    }
                                });
                            });
                        } else {
                            notificationService.error(resp.message);
                        }
                    },
                );
            }
            if ($rootScope.activePage === 'Send test candidate to email') {
                $timeout(() => {
                    Service.onGetLanguagesObjectCached();
                    getTestTemplates();
                    $scope.getTestFunc();
                    $scope.$$phase || $scope.$apply();
                }, 500);
            }
            if ($rootScope.activePage === 'Send test candidate to email from candidate') {
                getTestTemplates();
                setTimeout(() => {
                    if (!$rootScope.candidateToTest) {
                        $rootScope.candidateToTest = JSON.parse($localStorage.get('candidateForTest'));
                        $rootScope.fromCandidate = [$rootScope.candidateToTest];
                        $rootScope.emailCandidateId = $rootScope.candidateToTest.candidateId;
                        if ($rootScope.candidateToTest.contacts.length > 0) {
                            angular.forEach($rootScope.candidateToTest.contacts, function (nval) {
                                if (nval.type === 'email' && nval.default) {
                                    delete $rootScope.emailCandidate;
                                    var email = nval.value.split(' ')[0];
                                    $rootScope.emailCandidate = email.replace(/,/g, '');
                                }
                            });
                        } else {
                            notificationService.error(
                                $filter('translate')('Please add an email before sending a test to this candidate'),
                            );
                        }
                    }

                    $scope.sendTestRequest.push({
                        candidateId: $rootScope.emailCandidateId,
                        email: $rootScope.emailCandidate,
                    });

                    $scope.oneCandidateEmail = $scope.sendTestRequest;
                }, 0);
            }
            if ($rootScope.activePage === 'Send test candidate to email from vacancy') {
                getTestTemplates();
                setTimeout(function () {
                    $rootScope.candidatesInStages = JSON.parse($localStorage.get('vacancyForTest'));
                    $scope.vacancyFromPipeline = JSON.parse($localStorage.get('pipelineVacancy'));
                    if ($rootScope.candidatesInStages.length > 0) {
                        $scope.itsFromPipeline = $rootScope.candidatesInStages[0].itsFromPipeline;
                    }
                    $rootScope.activeCustomStageName = $localStorage.get('activeCustomStageName');
                    $scope.activeCustomStageId = $localStorage.get('activeCustomStageId');
                    $scope.groupNameList = [];
                }, 0);
            }
        }

        getAllVacancies();
        (function getPersonEmails() {
            Email.getMailboxes().then(
                (resp) => {
                    resp.forEach((response) => {
                        if (response.personalMailing === true) {
                            $rootScope.emailFrom = response.email;
                        }
                    });
                    let connectionLostEmails = resp.filter(
                        ({ status }) => !['ok', 'office365', 'gmail', 'exchange'].includes(status),
                    );
                    let isPermittedEmail = resp.filter((email) => email.personalMailing);
                    $rootScope.me.emails = isPermittedEmail;
                    Email.setDefaultMailbox(isPermittedEmail);
                    if ($rootScope.me.emails.length)
                        $scope.selectedEmail.emailFrom = Email.getDefaultMailbox()
                            ? Email.getDefaultMailbox()
                            : $rootScope.me.emails[0];
                    $scope.textEmailTestCandidate = Email.setDefaultSignature(
                        Email.getDefaultMailbox(),
                        $scope.textEmailTestCandidate,
                    );
                    if (!isPermittedEmail && resp.length) $scope.noAllowedMails = true;
                    if (resp.length && resp.length === connectionLostEmails.length) $scope.emailsConnectionLost = true;
                },
                (error) => notificationService.error(error),
            );
        })();
    },
]);
