controller.controller('CandidateAddController', [
    '$rootScope',
    '$http',
    '$httpParamSerializer',
    '$scope',
    '$translate',
    'FileInit',
    '$location',
    'Service',
    'GoogleMaps',
    'Candidate',
    'notificationService',
    '$filter',
    '$localStorage',
    '$cookies',
    '$window',
    '$timeout',
    'serverAddress',
    '$stateParams',
    '$uibModal',
    'CustomField',
    'RegionInputService',
    'CandidateResumeFromLink',
    'Person',
    '$routeParams',
    'allRolesList',
    function (
        $rootScope,
        $http,
        $httpParamSerializer,
        $scope,
        $translate,
        FileInit,
        $location,
        Service,
        GoogleMaps,
        Candidate,
        notificationService,
        $filter,
        $localStorage,
        $cookies,
        $window,
        $timeout,
        serverAddress,
        $stateParams,
        $uibModal,
        CustomField,
        RegionInputService,
        CandidateResumeFromLink,
        Person,
        $routeParams,
        allRolesList,
    ) {
        Service.toAddCandidate('/candidates/');
        $scope.tooMuchCharacters = {
            linkedin: false,
            facebook: false,
            skype: false,
        };
        $scope.ckEditorDescriptionOptions = {
            toolbarGroups: [
                { name: 'document', groups: ['mode', 'document', 'doctools'] },
                { name: 'clipboard', groups: ['clipboard', 'undo'] },
                {
                    name: 'editing',
                    groups: ['find', 'selection', 'spellchecker', 'editing'],
                },
                { name: 'forms', groups: ['forms'] },
                { name: 'basicstyles', groups: ['basicstyles', 'cleanup'] },
                { name: 'colors', groups: ['colors'] },
                {
                    name: 'paragraph',
                    groups: ['list', 'indent', 'blocks', 'align', 'bidi', 'paragraph'],
                },
                { name: 'links', groups: ['links'] },
                { name: 'insert', groups: ['insert'] },
                { name: 'styles', groups: ['styles'] },
                { name: 'tools', groups: ['tools'] },
                { name: 'others', groups: ['others'] },
                { name: 'about', groups: ['about'] },
            ],
            removeButtons:
                'Source,Save,NewPage,Preview,Print,Templates,Cut,Copy,Paste,PasteText,PasteFromWord,Find,Replace,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Subscript,Superscript,RemoveFormat,CopyFormatting,Outdent,Indent,Blockquote,CreateDiv,JustifyBlock,BidiLtr,BidiRtl,Language,Anchor,Unlink,Link,Image,Flash,Table,Smiley,SpecialChar,PageBreak,Iframe,ShowBlocks,Maximize,About',
        };
        $scope.employmentType = [];

        $scope.industryModel = null;

        $scope.skillsOptions = [];
        $scope.skillExperienceOptions = Service.skillCandidateExperience();

        $scope.countriesForRelocateOptions = [];

        $scope.showGoogleLocationPicker = false;
        $scope.initialGooglePlacesInput = '';

        $scope.showGoogleLocationPickerRelocate = false;
        $scope.initialRelocateGooglePlacesInput = '';

        $scope.languagesOptions = [];
        $scope.languageLevelOptions = Service.languagesLevel();
        $scope.languageLevelOptions.unshift({
            label: 'No_experience',
            value: 'no_experience',
        });

        $scope.skillsModel = [];
        $scope.languagesModel = [];

        $scope.inputSearchThreshold = 2;
        $scope.inputGoogleSearchThreshold = 1;

        $scope.candidateGroupsOptions = [];

        $scope.mockContactType = {};

        $scope.contactTypes = [
            { label: 'mphone', value: 'mphone', pathToIcon: 'phone' },
            { label: 'E-mail', value: 'E-mail', pathToIcon: 'mail' },
            { label: 'linkedin', value: 'linkedin', pathToIcon: 'linkedin' },
            { label: 'facebook', value: 'facebook', pathToIcon: 'facebook' },
            { label: 'telegram', value: 'telegram', pathToIcon: 'telegram' },
            { label: 'whatsApp', value: 'whatsApp', pathToIcon: 'whatsApp' },
            { label: 'viber', value: 'viber', pathToIcon: 'viber' },
            { label: 'skype', value: 'skype', pathToIcon: 'skype' },
            { label: 'github', value: 'github', pathToIcon: 'github' },
            { label: 'behance', value: 'behance', pathToIcon: 'behance' },
            { label: 'djinni', value: 'djinni', pathToIcon: 'djinni' },
            { label: 'homepage', value: 'homepage', pathToIcon: 'homepage' },
            { label: 'other', value: 'other', pathToIcon: 'other' },
        ].map((item) => ({ ...item, pathToIcon: '/images/redesign/candidate/' + item.pathToIcon + '.svg' }));

        $scope.addContact = (opt) => {
            if (opt.value === 'E-mail') opt.value = 'email';
            $scope.addInputField(opt.value, $scope.contacts);
            $scope.mockContactType = null;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.positionModel = {};
        $scope.allRoles = allRolesList.map((role) => ({ label: role, value: role }));
        $scope.serverAddress = serverAddress;
        $scope.type = 'add';
        $scope.objType = 'candidate';
        $scope.candidate = {};
        $scope.addedFromResume = false;
        $scope.addLinkErrorShow = false;
        $scope.showAddedLinks = false;
        $scope.showAddedFiles = false;
        $scope.showAddLink = false;
        $scope.currency = Service.currency();
        $scope.industries = Service.getIndustries();
        $scope.experience = Service.experience();
        $scope.googleMapOption = false;
        // $rootScope.errorRegion = false;
        $scope.btnToAddPhone = true;
        $scope.btnToAddEmail = true;
        $scope.btnToAddSkype = true;
        $scope.btnToAddLinkedin = true;
        $scope.btnToAddTelegram = true;
        $scope.btnToAddFacebook = true;
        $scope.btnToAddGoogleplus = true;
        $scope.btnToAddGithub = true;
        $scope.btnToAddHome = true;
        $scope.emailError = true;
        $scope.emailError2 = true;
        $scope.emailError3 = true;
        $scope.linkedinError = true;
        $scope.linkedinError2 = true;
        $scope.showAllContacts = false;
        $scope.linkedinError3 = true;
        $location.hash('');
        $scope.duplicatesByEmail = [];
        $scope.duplicatesByPhone = [];
        $scope.duplicatesBySkype = [];
        $scope.duplicatesByLinkedin = [];
        $scope.regionsToRelocate = [];
        $scope.maxDate = new Date();
        $scope.phoneError = { 0: false, 1: false, 2: false };
        $scope.maxDate.setFullYear($scope.maxDate.getFullYear() - 12);
        $scope.maxDate.setHours(23, 59, 59, 999);
        $scope.contacts = Candidate.candidateContacts();
        $scope.fastCandResumeLinkSite = '';
        $scope.selectedGoogleRegionToRelocate = '';
        $scope.workErrorFields = {
            fromDate: false,
            toDate: false,
            position: false,
            company: false,
            description: false,
        };
        $scope.educationErrorFields = {
            fromDate: false,
            toDate: false,
            speciality: false,
            university: false,
            description: false,
        };

        $scope.candidate = {
            customFields: [
                {
                    fieldValue: {
                        objType: 'candidate',
                        fieldValueId: '',
                        value: '',
                        field: {
                            fieldId: '',
                        },
                    },
                },
            ],
            readyRelocare: false,
            relatedRegions: [],
        };
        $scope.educationLevels = Candidate.getEducationLevel().map((item) => ({ label: item, value: item }));
        $scope.showResumeErrorFlag = false;
        $scope.showResumeFromLinkErrorFlag = false;
        $scope.saveButtonIsPressed = false;

        if (!$rootScope.descrFlag) {
            $rootScope.descriptionFromResume = '';
        } else {
            $rootScope.descrFlag = false;
        }

        $scope.hideDuplicates = () => {
            $scope.showDuplicates = false;
            $scope.$$phase || $scope.$apply();
        };

        $scope.goToTags = function () {
            $location.path('/company/tags');
        };

        $scope.addInputField = (param, contacts) => {
            contacts[param].push({ value: '', type: param });
            Object.keys(contacts).forEach((item) => {
                contacts[item].isLast = false;
            });
            contacts[param].isLast = true;
        };
        $scope.removeInputField = (param, contacts, index) => contacts[param].splice(index, 1);
        $scope.swapFields = Service.swapFields;
        initNewFields();
        setDefaultCurrency();
        $scope.contactsLength = function (contact) {
            let contactIsExist = false;
            contact.forEach((item) => {
                if (item && item.value) contactIsExist = true;
            });
            return contactIsExist;
        };

        function parseCandidateRegion() {
            if (!$scope?.candidate?.region) return;

            $scope.candidate.country = {
                value: $rootScope.getItemByLang(
                    'country',
                    $scope.candidate.region.googlePlaceId,
                    $rootScope.currentLang,
                ),
                label: $rootScope.getItemByLang(
                    'country',
                    $scope.candidate.region.googlePlaceId,
                    $rootScope.currentLang,
                ),
            };

            if ($scope.candidate.country) {
                getCities($scope.candidate.country?.value);
            }

            $scope.candidate.city = {
                value: $rootScope.getItemByLang('city', $scope.candidate.region.googlePlaceId, $rootScope.currentLang),
                label: $rootScope.getItemByLang('city', $scope.candidate.region.googlePlaceId, $rootScope.currentLang),
            };
        }

        function initNewFields() {
            $scope.languages = [
                {
                    name: null,
                    level: undefined,
                },
            ];
            $scope.allSkills = [
                {
                    skill: undefined,
                    experience: 'e00_no_experience',
                    new: false,
                    willPlus: false,
                },
            ];
            $scope.errorFields = {
                firstName: false,
                lastName: false,
                middleName: false,
                salary: false,
                position: false,
                currentWorkPlace: false,
                currentPosition: false,
            };
            Service.onGetLanguagesOptionsCached().then((options) => ($scope.languagesOptions = options));
            getSkills();
            getPersons();
            getCountries();
            _getCountriesForRelocate();
            _getOriginsOptions();
            _getCandidateGroupsOptions();
        }

        function getCountries() {
            RegionInputService.regionRequests().getAllCountries(
                { lang: $rootScope.currentLang },
                (resp) => {
                    if (resp.status === 'ok') {
                        $scope.countriesOptions = resp.objects.map((item) => ({ label: item, value: item }));
                        $scope.$$phase || $scope.$apply();
                    }
                },
                (err) => {},
            );
        }

        function _getCountriesForRelocate() {
            RegionInputService.regionRequests().getAllCountryForRelocate(
                { lang: $rootScope.currentLang },
                (resp) => {
                    if (resp.status === 'ok') {
                        const anyCountry = resp.objects.find(
                            (country) => country.countryEn === 'Any' && country.countryRu === 'Любая',
                        );

                        $scope.countriesForRelocateOptions = [
                            anyCountry,
                            ...resp.objects.filter(
                                (country) => country.countryEn !== 'Any' && country.countryRu !== 'Любая',
                            ),
                        ].map((item) => ({
                            ...item,
                            label: $rootScope.getItemByLang('country', item, $rootScope.currentLang),
                            value: item.googlePlaceId,
                        }));

                        $scope.$$phase || $scope.$apply();
                    }
                },
                (err) => {},
            );
        }

        function getCities(country, forRelocate = false) {
            if (!country) return;
            RegionInputService.regionRequests().autocompleteCity(
                { country },
                function (resp) {
                    if (resp.status !== 'ok') return;

                    if (forRelocate) {
                        $scope.citiesOptionsForRelocate = [
                            { label: $translate.instant('Any city'), value: 'Any city' },
                            ...RegionInputService.formatCitiesOptions(resp.object[country]),
                        ];
                    } else {
                        $scope.citiesOptions = RegionInputService.formatCitiesOptions(resp.object[country]);
                    }

                    $scope.$$phase || $scope.$apply();
                },
                function (err) {},
            );
        }

        function _getOriginsOptions() {
            Candidate.onAutocompleteAllOrigins($httpParamSerializer({ text: '' }))
                .then((resp) => {
                    $scope.allOriginsData = resp.objects.map((origin) => ({ label: origin, value: origin }));
                })
                .catch((err) => notificationService.error(err.message));
        }

        function _getCandidateGroupsOptions() {
            Service.requestGetCompanyGroups().then((resp) => {
                $scope.candidateGroupsOptions = resp.objects.map((option) => ({
                    ...option,
                    label: option.name,
                    value: option.candidateGroupId,
                }));
            });
        }

        function getSkills() {
            $scope.skillsObject = {};
            Candidate.onGetSkills()
                .then((resp) => {
                    if (resp.status === 'ok') {
                        $rootScope.skills = resp.objects;
                        setSkillsOptions(resp.objects);

                        // $scope.importedSkills = resp.objects.filter((item) => item.type === 'imported');
                        // $scope.systemSkills = resp.objects.filter((item) => item.type === 'general');
                        // $scope.systemSkills.forEach((item) => {
                        //     $scope.skillsObject[item.skill.toLowerCase()] = item.skillId;
                        // });
                        // $scope.customSkills = resp.objects.filter((item) => item.type === 'custom');
                        // $scope.customSkills.forEach((item) => {
                        //     $scope.skillsObject[item.skill.toLowerCase()] = item.skillId;
                        // });
                        // $rootScope.$emit('putSkills', {
                        //     skills: $scope.systemSkills,
                        //     customSkills: $scope.customSkills,
                        // });
                        // $scope.setSkills();
                    }
                })
                .catch((err) => console.error(err));
        }

        const setSkillsOptions = (skillsResp) => {
            $scope.skillsOptions = [
                {
                    label: 'systemSkillsGroup',
                    options: skillsResp
                        .filter((skill) => skill.type === 'general')
                        .map((skill) => ({ label: skill.skill, value: skill })),
                },
                {
                    label: 'customSkillsGroup',
                    options: skillsResp
                        .filter((skill) => skill.type === 'custom')
                        .map((skill) => ({ label: skill.skill, value: skill })),
                },
            ];
        };

        function setDefaultCurrency() {
            Service.onGetDefaultCurrency({
                defaultCurrencyType: 'candidate',
            }).then((resp) => {
                if (resp.status === 'ok') $scope.candidate.currency = { value: resp.object, label: resp.object };
            });
        }

        function getPersons() {
            Person.getAllPersons(
                function (resp) {
                    $scope.associativePerson = resp.object;
                    angular.forEach($scope.associativePerson, function (val, key) {
                        if (angular.equals(resp.status, 'ok')) {
                            $scope.persons.push($scope.associativePerson[key]);
                        }
                    });
                    $rootScope.persons = $scope.persons;
                    var iUser = null;
                    for (var i = 0; i <= $scope.persons.length - 1; i++) {
                        if ($rootScope.me.userId === $scope.persons[i].userId) {
                            iUser = $scope.persons[i];
                            $scope.persons.splice(i, 1);
                            break;
                        }
                    }
                    if (iUser) {
                        $scope.persons.unshift(iUser);
                    }

                    $scope.personsShortList = $scope.persons.map((person) => {
                        return {
                            value: person.fullName,
                            id: person.userId,
                        };
                    });
                    $scope.personsIsLoaded = true;
                },
                function (error) {
                    console.error(error);
                },
            );
        }
        $scope.persons = [];

        $scope.showAddResponsibleUser = function (id) {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/candidate-adding-responsible.html',
                size: '',
                resolve: function () {},
            });
        };

        $scope.getFullCustomFields = function () {
            CustomField.getFullFields(
                {
                    objectType: 'candidate',
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        $scope.allObjCustomField = resp.objects;
                    } else {
                        notificationService.error(resp.message);
                    }
                },
            );
        };

        $scope.getFullCustomFields();
        $scope.checkErrorName = function (name, type) {
            if (
                (name && name.trim().length > 0) ||
                type === 'middleName' ||
                type === 'currentWorkPlace' ||
                type === 'currentPosition'
            )
                $scope.errorFields[type] = false;
            else $scope.errorFields[type] = true;
        };
        $scope.checkDuplicatesByNameAndContacts = function () {
            Candidate.checkDuplicatesByNameAndContacts($scope);
        };

        $scope.$watch('selectedGoogleRegionToRelocate', function (selectedGoogleRegion, oldVal) {
            if (selectedGoogleRegion) {
                $scope.selectedGoogleRegionToRelocate = '';
                $scope.regionsToRelocate.push(selectedGoogleRegion);
                $rootScope.$emit('onCloseGoogleRegionInput');
            }
        });

        $scope.readyRelocateHandler = function () {
            $scope.candidate.readyRelocate = !$scope.candidate.readyRelocate;

            if (!$scope.candidate.readyRelocate) {
                $scope.selectedGoogleRegionToRelocate = '';
                $scope.regionsToRelocate = [];
                $rootScope.$emit('onCloseGoogleRegionInput');
            }
            $scope.$$phase || $scope.$apply();
        };

        const transformDataFromTextResume = (dataObj) => {
            if (dataObj?.position?.length) {
                $scope.positionInitialInput = dataObj.position;
                $scope.onChangePosition({ label: dataObj.position, value: dataObj.position });
            }

            if (dataObj?.descr?.length) {
                $rootScope.descriptionFromResume = dataObj.descr.replaceAll('\n', '');
            }

            parseCandidateRegion();

            if (!$scope.candidate?.fieldValues) {
                $scope.candidate.fieldValues = [];
            }

            $scope.$$phase || $scope.$apply();
        };

        $scope.setCandidatePropsFromFile = function () {
            $timeout(function () {
                if (!$rootScope.resumeToSave && !$rootScope.resumeFromText && !$rootScope.resumeFromLink) {
                    $scope.addedFromResume = true;
                }

                if ($rootScope.resumeToSave) {
                    Candidate.convert($scope, $rootScope.resumeToSave.data.object);

                    if ($rootScope.resumeToSave.data.object.position) {
                        $scope.onChangePosition({
                            label: $rootScope.resumeToSave.data.object.position,
                            value: $rootScope.resumeToSave.data.object.position,
                        });
                    }

                    parseCandidateRegion();

                    if ($rootScope.resumeToSave.data.object.contacts) {
                        $rootScope.resumeToSave.data.object.contacts
                            .filter((obj) => obj.type === 'email')
                            .forEach((email) => {
                                const re =
                                    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                                email.isError = !re.test(String(email.value).toLowerCase());
                            });
                    }

                    $scope.candidate.source = 'cvfile';
                    $scope.callbackFile($rootScope.resumeToSave.data.objects[0], $rootScope.file.filename);
                    $rootScope.resumeToSave = undefined;
                    $rootScope.file = undefined;
                    Candidate.checkDuplicatesByNameAndContacts($scope);
                    $rootScope.$$phase || $scope.$apply();
                }

                if ($rootScope.resumeFromText) {
                    Candidate.convert($scope, $rootScope.resumeFromText);
                    transformDataFromTextResume($rootScope.resumeFromText);
                    $rootScope.fastCandResumeText = undefined;
                    $rootScope.resumeFromText = undefined;
                    Candidate.checkDuplicatesByNameAndContacts($scope);
                }
            }, 500);
        };

        $scope.setCandidatePropsFromFile();

        let resumeFromLinkListener = $rootScope.$on('resumeFromLink', function () {
            $scope.fromLinkSite();
        });

        $scope.$on('$destroy', resumeFromLinkListener);

        $scope.addLinkToCandidate = {
            name: '',
            url: '',
        };

        $scope.deleteRegionToRelocate = function (index) {
            $scope.regionsToRelocate.splice(index, 1);
        };

        $scope.map = {
            center: {
                latitude: 48.379433,
                longitude: 31.165579999999977,
            },
            zoom: 6,
            options: {
                panControl: true,
                zoomControl: true,
                scaleControl: true,
                mapTypeControl: true,
                mapTypeId: 'roadmap',
            },
        };
        $scope.marker = {
            id: 1,
            title: '',
            coords: {
                latitude: null,
                longitude: null,
            },
        };

        $scope.showResumeError = function () {
            $scope.showResumeErrorFlag = true;
        };

        $scope.showResumeLinkError = function () {
            $scope.showResumeFromLinkErrorFlag = true;
        };

        $scope.getPluginDownloadingLink = Service.getPluginDownloadingLinkByCurrentLang;

        $scope.getBrowser = function () {
            if (navigator.saysWho.indexOf('Chrome') != -1) {
                $scope.resumeBrowser = 'Chrome';
            } else if (navigator.saysWho.indexOf('Firefox') != -1) {
                $scope.resumeBrowser = 'Firefox';
            } else {
                $scope.resumeBrowser = $filter('translate')('browser');
            }
        };
        $scope.getBrowser();

        $scope.fromLinkSite = async function (link) {
            if (!$location.search().resumeFromLink) {
                CandidateResumeFromLink.fromLinkSite($scope, link);
            }

            await $rootScope.waitForCondition(200, 100, () => $rootScope.dataFromLink);

            Candidate.convert($scope, $rootScope.dataFromLink);

            if ($rootScope.dataFromLink.origin) {
                $scope.onChangeOrigin({
                    label: $rootScope.dataFromLink.origin,
                    value: $rootScope.dataFromLink.origin,
                });
            }

            if ($rootScope.dataFromLink.position) {
                $scope.onChangePosition({
                    label: $rootScope.dataFromLink.position,
                    value: $rootScope.dataFromLink.position,
                });
            }

            parseCandidateRegion();

            if (!$scope.candidate?.fieldValues) {
                $scope.candidate.fieldValues = [];
            }
        };

        $scope.imgWidthFunc = function (id) {
            var img = new Image();
            img.onload = function () {
                var width = this.width;
                var height = this.height;
                var minus = width - height;
                if (width >= height && minus > 40 && minus <= 100) {
                    $('#page-avatar').css({
                        width: '100%',
                        height: 'auto',
                        margin: 'inherit',
                    });
                } else if ((width >= 300 && width <= 349) || width == height) {
                    $('#page-avatar').css({
                        width: '100%',
                        'object-fit': 'fill',
                        margin: 'inherit',
                    });
                } else if (width >= 350) {
                    $('#page-avatar').css({
                        width: '100%',
                        height: 'auto',
                        margin: 'inherit',
                    });
                } else if (width >= 201) {
                    $('#page-avatar').css({ width: '100%', height: 'auto' });
                } else {
                    $('#page-avatar').css({
                        width: 'inherit',
                        height: 'inherit',
                        display: 'block',
                        margin: '0 auto',
                    });
                }
            };
            img.src =
                $location.$$protocol +
                '://' +
                $location.$$host +
                $scope.serverAddress +
                '/getapp?id=' +
                id +
                '&d=' +
                $rootScope.me.personId;
        };
        $scope.callbackAddPhoto = function (photo) {
            $rootScope.loading = false;
            $scope.candidate.photo = photo;
            $scope.photoLink = photo ? $scope.serverAddress + '/getapp?id=' + photo + '&d=true' : null;
            $scope.imgWidthFunc(photo);
            Candidate.progressUpdate($scope, true);
            $rootScope.closeModal();
        };
        $scope.addPhotoByReference = function (photoUrl) {
            $rootScope.loading = true;
            FileInit.addPhotoByReference(photoUrl, $scope.callbackAddPhoto);
        };
        if ($rootScope.candidateExternalLink) {
            $scope.fromLinkSite($rootScope.candidateExternalLink);
            $rootScope.candidateExternalLink = null;
        } else if ($localStorage.isExist('candidateForSave')) {
            $scope.candidate = angular.fromJson($localStorage.get('candidateForSave'));
            if ($scope.candidate.photoUrl) {
                $scope.photoUrl = $scope.candidate.photoUrl;
                $scope.addPhotoByReference();
            }
            $localStorage.remove('candidateForSave');
            if ($scope.candidate.db) {
                $scope.data = new Date($scope.candidate.db);
            }
            $scope.candidate.db = null;
            if ($scope.candidate.contacts) {
                angular.forEach($scope.candidate.contacts, function (val) {
                    if (!$scope.contacts[val.type][0].value) {
                        $scope.contacts[val.type][0] = val;
                    } else {
                        $scope.contacts[val.type].push(val);
                    }
                });
            }
            $scope.candidate.contacts = null;
        } else {
            $scope.candidate = {
                status: { value: 'active_search', name: 'candidate_status_assoc.active_search' },
                relatedRegions: null,
                skills: [],
                currency: { value: 'USD', label: 'USD' },
            };
        }
        $scope.dateOptions = {
            changeYear: true,
            changeMonth: true,
            yearRange: '1930:-0',
            initialDate: '01/01/1990',
        };
        $scope.fileForSave = [];
        $scope.linksForSave = [];

        FileInit.initCandFileOption($scope, '', '', false);
        $scope.callbackFile = function (resp, names) {
            $scope.fileForSave.push({
                attId: resp,
                fileName: names,
                fileResolution: Service.getFileResolutionFromName(names),
            });
            $scope.progressUpdate();

            if (!$scope.candidate?.fieldValues) {
                $scope.candidate.fieldValues = [];
            }
        };

        $scope.removeFile = function (id) {
            angular.forEach($scope.fileForSave, function (val, ind) {
                if (val.attId === id) {
                    $scope.fileForSave.splice(ind, 1);
                }
            });
        };
        $scope.removeLink = function (id) {
            angular.forEach($scope.linksForSave, function (val, ind) {
                if (val.fileName === id) {
                    $scope.linksForSave.splice(ind, 1);
                }
            });
        };

        if ($rootScope.me.personParams.enableEmployee == 'Y' && $rootScope.me.orgParams.enableEmployee == 'Y')
            $scope.status = Candidate.getStatus();
        else $scope.status = Candidate.getStatus().filter((item) => item.value !== 'employed');

        $scope.cancel = function () {
            $location.path('/candidates/');
        };

        $scope.progressUpdate = function () {
            setTimeout(() => {
                Candidate.progressUpdate($scope, true);
            }, 0);
        };

        $scope.validateCandidateName = (name, type) => {
            if (type === 'currentWorkPlace' || type === 'currentPosition') {
                if (name.length >= 100) $scope.errorFields[type] = true;
                else $scope.errorFields[type] = false;
                return Candidate.validatePosition(name);
            } else {
                if (name.length >= 50) $scope.errorFields[type] = true;
                else $scope.errorFields[type] = false;
                return Candidate.validateName(name);
            }
        };

        $scope.onChangeSelectCustomField = (newValue, index) => {
            $scope.allObjCustomField[index]['value'] = newValue;
            const field = $scope.allObjCustomField[index];
            if ($scope.cfSelectError?.length) {
                $scope.cfSelectError = $scope.cfSelectError.filter((error) => error.fieldId !== field.fieldId);
            }
            field.isValidMandatory = !newValue;
            $scope.$$phase || $scope.$apply();
        };

        $rootScope.addPhoto = function () {
            $('#photoFile').click();
        };

        $scope.callbackErr = function (err) {
            notificationService.error(err);
        };
        Candidate.setPhoto($scope);
        Candidate.fromFile($scope, $rootScope, $location);

        $scope.removePhoto = function () {
            $scope.candidate.photo = '';
            $scope.progressUpdate();
        };
        $scope.candidate.fieldValues = [];
        $scope.editCustomField = function (text, id) {
            $scope.candidate.fieldValues.push({
                objType: 'candidate',
                value: text,
                field: {
                    fieldId: id,
                },
            });
        };
        $scope.editCustomField = function (e, id) {
            $scope.editCustomValue = e.currentTarget.value;
            $scope.editCustomId = id;
        };

        $scope.validationOfEmailField = function (email) {
            if (email.value) {
                const re =
                    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                email.isError = !re.test(String(email.value).toLowerCase());
            } else {
                email.isError = false;
            }
        };
        $scope.validationOfContactField = function (field, type) {
            if (!field.value || (field.value && field.value.length > 5)) {
                $scope.checkDuplicatesByNameAndContacts();
            }
            if (type === 'phone') {
                if (field.value && field.value.length > 20) {
                    field.isError = true;
                    return field.value.slice(0, 20);
                } else {
                    field.isError = false;
                    return field.value;
                }
            }
            if (type === 'facebook' || type === 'linkedin' || type === 'djinni') {
                if (field.value && field.value.length >= 2048) {
                    field.isError = true;
                    return field.value.slice(0, 2048);
                } else {
                    field.isError = false;
                    return field.value;
                }
            }
            if (type === 'other' || type === 'github' || type === 'behance' || type === 'homepage') {
                if (field.value && field.value.length >= 2048) {
                    field.isError = true;
                    return field.value.slice(0, 2048);
                } else {
                    field.isError = false;
                    return field.value;
                }
            }
            if (type === 'email') {
                if (field.value && field.value.length > 50) {
                    field.isError = true;
                    field.toMuchCharacters = true;
                    return field.value.slice(0, 50);
                } else {
                    field.toMuchCharacters = false;
                    return field.value;
                }
            }
            if (field.value && field.value.length > 50) {
                field.isError = true;
                return field.value.slice(0, 50);
            } else {
                field.isError = false;
                return field.value;
            }
        };
        $scope.clearEmailValidationError = function (email) {
            email.isError = false;
        };

        $scope.addCustomFieldParams = function (text, id, fieldValues, fieldValues2, index) {
            angular.forEach($scope.allObjCustomField, function (item) {
                if ($scope.errorMandatoryField && id === item.fieldId && item.type === 'select') {
                    // for dropdown only
                    if (!text) {
                        item.isValidMandatory = item.mandatory ? true : false;
                    } else {
                        item.isValidMandatory = false;
                    }
                }
            });
            if (text.length > 3000) {
                const sliceText = {
                    remind() {
                        Object.keys(CKEDITOR.instances).forEach(function (key) {
                            const ids = CKEDITOR.instances[key].element.$.dataset.id;
                            const data = CKEDITOR.instances[key]._.data;
                            if (ids === id && data) {
                                CKEDITOR.instances[`${key}`].setData(text.substring(0, 3000));
                            }
                        });

                        fieldValues2.descrError = true;
                        $scope.timeoutId = undefined;
                        $rootScope.$$phase || $scope.$apply();
                    },
                    setup() {
                        if (typeof $scope.timeoutId === 'number') {
                            this.cancel();
                        }

                        $scope.timeoutId = setTimeout(() => {
                            this.remind();
                        }, 1000);
                    },

                    cancel() {
                        clearTimeout($scope.timeoutId);
                    },
                };

                sliceText.setup();
            } else {
                $timeout(() => {
                    if (fieldValues2) {
                        fieldValues2.descrError = false;
                    }
                }, 2000);
            }

            if (text !== undefined) {
                $scope.fieldValueState = false;
                if (!$scope.candidate.fieldValues) {
                    $scope.candidate.fieldValues = [];
                }
                $scope.fieldValueState = $scope.candidate.fieldValues.some((el) => el.field.fieldId === id);
                if ($scope.fieldValueState) {
                    if (text === '') {
                        $scope.candidate.fieldValues.forEach((val, ind) => {
                            if (val.field.fieldId === id) $scope.candidate.fieldValues.splice(ind, 1);
                        });
                    } else {
                        $scope.candidate.fieldValues.forEach((val) => {
                            if (val.field.fieldId === id) val.value = text;
                        });
                    }
                } else {
                    if (text !== '') {
                        $scope.candidate.fieldValues.push({
                            objType: 'candidate',
                            value: text,
                            field: {
                                fieldId: id,
                            },
                        });
                    }
                }
            }
        };

        $scope.sendCustomFieldId = function (id) {
            $scope.editCustomId = id;
        };

        $scope.roundMinutes = function (date) {
            var date2 = new Date();

            angular.copy(date, date2);

            date2.setHours(date2.getHours() - 3 + Math.round(date2.getMinutes() / 60));
            date2.setMinutes(0);

            return date2;
        };

        if ($scope.candidate.fieldValues) {
            angular.forEach($scope.candidate.fieldValues, function (val) {
                if (angular.equals(val.type, 'string')) {
                    $scope.fieldValues.value = val.value;
                }
                if (angular.equals(val.type, 'select')) {
                    $scope.fieldValues.value = val.value;
                }
                if (angular.equals(val.type, 'date')) {
                    $scope.fieldValues.dateTimeValue = val.dateTimeValue;
                }
                if (angular.equals(val.type, 'datetime')) {
                    $scope.fieldValues.dateTimeValue = val.dateTimeValue;
                }
            });
        }
        $scope.addedLang = [];
        var myListener = $scope.$on('addedLang', function (event, data) {
            if (data != undefined) {
                $scope.addedLang = data;
            }
        });
        $scope.$on('$destroy', myListener);

        $scope.$on('$destroy', () => {
            if ($rootScope.dataFromLink) {
                $rootScope.dataFromLink = null;
            }
            $rootScope.fastCandResumeText = null;
        });

        let onSelectRegionListener = $rootScope.$on('onSelectRegion', (e, data) => {
            if (data) {
                $scope.candidate.region = data;
                $scope.progressUpdate();
            }
        });

        $scope.$watch('region', (selectedGoogleRegion, oldVal) => {
            $scope.googleRegion = selectedGoogleRegion;
        });

        $scope.$watch('candidate.status', (newVal, oldVal) => {
            $scope.candidatePrevStatus = oldVal.value || oldVal;
        });

        $scope.fullDataCandidate = angular.copy($scope.candidate);
        $scope.fullDataCandidate.saveFunc = function (data) {
            $scope.candidate.educationList = data.educationList;
            $scope.candidate.workExperiences = data.workExperiences;
        };

        $scope.changeLevel = function (level, id) {
            angular.forEach($scope.addedLang, function (val) {
                if (val.languageId === id || val.id === id) {
                    val.level = level;
                }
            });
        };

        $scope.removePhoneError = (index) => {
            $scope.phoneError[index] = false;
        };
        $scope.returnValue = function (field) {
            if (field && typeof field === 'object') return field.value;
            else return field;
        };

        $scope.onValidateMandatoryField = function (id, type, item, value) {
            if ($scope.errorMandatoryField) {
                if (type === 'date') {
                    if (!value) {
                        item.isValidMandatory = item.mandatory ? true : false;
                    } else {
                        item.isValidMandatory = false;
                    }
                }
                if (type === 'datetime') {
                    if (!value) {
                        item.isValidMandatory = item.mandatory ? true : false;
                    } else {
                        item.isValidMandatory = false;
                    }
                }
                if (type === 'string') {
                    if (!value) {
                        item.isValidMandatory = item.mandatory ? true : false;
                    } else {
                        item.isValidMandatory = false;
                    }
                }
            }
        };

        $scope.validateMandatoryFields = function () {
            $scope.errorMandatoryField = false;
            if ($scope.candidate.fieldValues && $scope.candidate.fieldValues.length >= 0) {
                angular.forEach($scope.allObjCustomField, function (item) {
                    if (item.type === 'string' && item.mandatory) {
                        item.isValidMandatory = true;
                        $scope.errorMandatoryField = true;
                    }
                    if (item.type === 'select' && item.mandatory) {
                        item.isValidMandatory = !item.value;
                        $scope.errorMandatoryField = !!item.value;
                    }
                    if (item.type === 'date' && item.mandatory) {
                        item.isValidMandatory = true;
                        $scope.errorMandatoryField = true;
                    }
                    if (item.type === 'datetime' && item.mandatory) {
                        item.isValidMandatory = true;
                        $scope.errorMandatoryField = true;
                    }
                });
            }
            if ($scope.candidate.fieldValues && $scope.candidate.fieldValues.length > 0) {
                angular.forEach($scope.allObjCustomField, function (item) {
                    $scope.candidate.fieldValues.forEach((field) => {
                        if (item.type === 'string' && item.mandatory && item.fieldId === field.field.fieldId) {
                            item.isValidMandatory = field ? false : true;
                            // $scope.errorMandatoryField = field ? false : true;
                        } else if (item.type === 'select' && item.mandatory && item.fieldId === field.field.fieldId) {
                            item.isValidMandatory = field ? false : true;
                        } else if (item.type === 'date' && item.mandatory && item.fieldId === field.field.fieldId) {
                            item.isValidMandatory = field ? false : true;
                        } else if (item.type === 'datetime' && item.mandatory && item.fieldId === field.field.fieldId) {
                            item.isValidMandatory = field ? false : true;
                        }
                    });
                });
            }
        };

        function checkisValidMandatory() {
            var allIsValidFalse = true;

            $scope.allObjCustomField.forEach(function (obj) {
                if (obj.isValidMandatory && obj.isValidMandatory !== false) {
                    allIsValidFalse = false;
                    return;
                }
            });
            $scope.errorMandatoryField = allIsValidFalse ? false : true;
        }

        $scope.saveCandidate = function () {
            let isErrorField = false;

            $scope.candidate.contacts = [];
            Object.keys($scope.contacts).forEach((contact) => {
                $scope.contacts[contact].forEach((item) => {
                    if (item.isError) {
                        isErrorField = true;
                    }
                    if (item.value) {
                        $scope.candidate.contacts.push(item);
                    }
                });
            });
            if (isErrorField) {
                notificationService.error($translate.instant('Candidate fill incorrectly'));
            } else {
                {
                    $rootScope.loading = true;
                    $scope.errorFields.position = !$scope.candidate.position;
                    $scope.errorFields.firstName = !$scope.candidate.firstName;
                    $scope.errorFields.lastName = !$scope.candidate.lastName;
                    const validateErrorFields = Object.values($scope.errorFields).every((value) => value === false);
                    $scope.validateMandatoryFields();
                    checkisValidMandatory();

                    if ($scope.errorMandatoryField) {
                        $rootScope.loading = false;
                        if (validateErrorFields) {
                            let element = document.querySelector('#custom-fields-block');
                            element.scrollIntoView({ behavior: 'smooth' });
                        }
                        notificationService.error($translate.instant('Candidate fill incorrectly'));
                        return;
                    }

                    $scope.validateTinyText = CustomField.validateTinyTextFields(
                        $scope.candidate.fieldValues,
                        null,
                        Service.getClearFieldLength,
                    );

                    angular.forEach($scope.contacts.mphone, (phone, index) => {
                        if (!/^[+0-9]{6,20}$/.test(phone.value) && phone.value) {
                            $scope.phoneError[index] = true;
                        }
                    });

                    if (
                        !$scope.contacts.email.some((item) => item.isError) &&
                        !Object.values($scope.errorFields).some((item) => item) &&
                        !Object.values($scope.phoneError).some((item) => item) &&
                        !Object.values($scope.educationErrorFields).some((item) => item) &&
                        !Object.values($scope.workErrorFields).some((item) => item) &&
                        !$scope.saveButtonIsPressed &&
                        !$scope.validateTinyText.cfErrorStringFlag
                    ) {
                        const candidate = { ...$scope.candidate };
                        $scope.saveButtonIsPressed = true;
                        if ($scope.candidate.educationList) {
                            angular.forEach($scope.candidate.educationList, function (item) {
                                if (typeof item.educationId === 'number') {
                                    item.educationId = null;
                                    if (item.candidateId === '') {
                                        item.candidateId = null;
                                    }
                                }
                            });
                        }
                        if ($scope.candidate.workExperiences) {
                            angular.forEach($scope.candidate.workExperiences, function (item) {
                                if (typeof item.workExperienceId === 'number') {
                                    item.workExperienceId = null;
                                    if (item.candidateId === '') {
                                        item.candidateId = null;
                                    }
                                }
                            });
                        }

                        if ($scope.candidate.photo) {
                            candidate.photo = $scope.candidate.photo;
                        }

                        if (
                            $scope.datepickerOfBirth &&
                            $scope.datepickerOfBirth !== '' &&
                            $scope.datepickerOfBirth !== 'Выбрать дату' &&
                            $scope.datepickerOfBirth !== 'Choose date' &&
                            $scope.datepickerOfBirth !== 'Вибрати дату'
                        )
                            $scope.datepickerOfBirth.setHours(
                                0 - $scope.datepickerOfBirth.getTimezoneOffset() / 60,
                                0,
                                0,
                                0,
                            );
                        candidate.db = Date.parse($scope.datepickerOfBirth);

                        if (!candidate.region && !candidate.region?.fromGoogle && $scope.candidate.country?.value) {
                            candidate['region'] = {
                                country: $scope.candidate.country.value,
                                city:
                                    $scope.candidate?.city?.value &&
                                    $rootScope.getItemByLang(
                                        'city',
                                        $scope.candidate?.city?.value,
                                        $rootScope.currentLang,
                                    ),
                                googlePlaceId: $scope.candidate?.city?.value?.googlePlaceId,
                                fromGoogle: false,
                            };
                        }

                        if (candidate.readyRelocate) {
                            if (
                                $scope.countryForRelocate?.value &&
                                $scope.countryForRelocate.countryEn === 'Any' &&
                                $scope.countryForRelocate.countryRu === 'Любая'
                            ) {
                                candidate.relatedRegions = [
                                    {
                                        country: 'any',
                                        googlePlaceId: {
                                            googlePlaceId:
                                                $scope.countryForRelocate?.value ||
                                                $scope.countriesForRelocateOptions[0]?.value,
                                        },
                                    },
                                ];
                            } else if (
                                !$scope.regionsToRelocate?.length &&
                                $scope.countryForRelocate?.value &&
                                $scope.countryForRelocate.countryEn !== 'Any' &&
                                $scope.countryForRelocate.countryRu !== 'Любая'
                            ) {
                                candidate.relatedRegions = [
                                    {
                                        country: $rootScope.getItemByLang(
                                            'country',
                                            $scope.countryForRelocate,
                                            $rootScope.currentLang,
                                        ),
                                        googlePlaceId: {
                                            googlePlaceId: $scope.countryForRelocate.value,
                                        },
                                    },
                                ];
                            } else if ($scope.regionsToRelocate.length) {
                                candidate.relatedRegions = $scope.regionsToRelocate.map((region) => {
                                    if (region.fromGoogle) return region;

                                    return {
                                        country: $rootScope.getItemByLang(
                                            'country',
                                            region.country,
                                            $rootScope.currentLang,
                                        ),
                                        city:
                                            region.city && region.city.value !== 'Any city'
                                                ? $rootScope.getItemByLang('city', region.city, $rootScope.currentLang)
                                                : null,
                                        googlePlaceId: {
                                            googlePlaceId:
                                                region?.city?.value?.googlePlaceId || region.country.googlePlaceId,
                                        },
                                        fromGoogle: false,
                                    };
                                });
                            }

                            if (candidate.relatedRegions?.length && $scope.countryForRelocate?.value) {
                                candidate?.relatedRegions.push({
                                    country: $rootScope.getItemByLang(
                                        'country',
                                        $scope.countryForRelocate,
                                        $rootScope.currentLang,
                                    ),
                                    googlePlaceId: {
                                        googlePlaceId: $scope.countryForRelocate.value,
                                    },
                                });
                            }

                            if (!candidate.relatedRegions?.length && !$scope.countryForRelocate?.value) {
                                candidate.relatedRegions = [
                                    {
                                        country: 'any',
                                        googlePlaceId: {
                                            googlePlaceId: 'ChIJcSZ_ANY_VsBQRKl9iKtTb',
                                        },
                                    },
                                ];
                            }
                        }

                        candidate.origin = $scope.candidateOrigin;
                        if (candidate.sex === 'male') candidate.sex = true;
                        else if (candidate.sex === 'female') candidate.sex = false;
                        else candidate.sex = undefined;
                        candidate.expirence = $scope.returnValue(candidate.expirence);
                        candidate.employmentType = $scope.candidate?.employmentType?.join(',');
                        candidate.status = $scope.returnValue(candidate.status);
                        candidate.currency = $scope.returnValue(candidate.currency);
                        candidate.education = candidate.education?.value;

                        candidate.descr = $rootScope.descriptionFromResume;

                        if ($scope.languagesModel.length) {
                            candidate.languages = $scope.languagesModel
                                .filter((lang) => lang?.name)
                                .map(({ name, level }) => ({
                                    name: name.label,
                                    level: level?.value,
                                }));
                        }

                        if ($scope.skillsModel.length) {
                            candidate.candidateSkills = $scope.skillsModel
                                .filter((skill) => skill?.skill)
                                .map(({ skill, experience, mustHave }) => ({
                                    skillId: skill?.value?.skillId,
                                    skillName: skill?.value.skillId ? null : skill.label,
                                    type: skill?.value.type || 'custom',
                                    experience: experience.value,
                                    mustHave,
                                }));
                        }

                        candidate.fieldValues = $scope.candidate.fieldValues?.map((item) => {
                            if (item.dateTimeValue) {
                                return {
                                    objType: item.objType,
                                    dateTimeValue: item.dateTimeValue,
                                    field: {
                                        fieldId: item.field.fieldId,
                                    },
                                };
                            } else {
                                return {
                                    objType: item.objType,
                                    value: $filter('removeHtmlTagFromString')(item.value),
                                    field: {
                                        fieldId: item.field.fieldId,
                                    },
                                };
                            }
                        });

                        candidate.fieldValues?.push(
                            ...$scope.allObjCustomField
                                .filter(({ type }) => type === 'select')
                                .map(({ objType, value, fieldId }) => ({
                                    objType,
                                    value: value?.value,
                                    field: { fieldId },
                                })),
                        );

                        // if (candidate.readyRelocate === false) {
                        //     candidate.relatedRegions = [];
                        //     candidate.relocatedRegionIds = '';
                        // }

                        Service.deleteUnnecessaryFields(candidate);
                        Candidate.add(
                            candidate,
                            function (val) {
                                $rootScope.isAddCandidates = false;
                                localStorage.setItem('isAddCandidates', $rootScope.isAddCandidates);
                                if (angular.equals(val.status, 'ok')) {
                                    $rootScope.resumeToSave = null;
                                    $scope.saveButtonIsPressed = false;
                                    notificationService.success($filter('translate')('Candidate saved'));
                                    if ($scope.fileForSave.length > 0) {
                                        angular.forEach($scope.fileForSave, function (valI, i) {
                                            Candidate.addFile(
                                                {
                                                    attId: valI.attId,
                                                    candidateId: val.object.candidateId,
                                                    fileName: valI.fileName,
                                                },
                                                function (resp) {},
                                            );
                                            if ($scope.fileForSave.length - 1 == i) {
                                                $location.path('/candidates/' + val.object.localId);
                                            }
                                        });
                                    } else {
                                        $location.path('/candidates/' + val.object.localId);
                                    }
                                    if ($scope.linksForSave.length > 0) {
                                        angular.forEach($scope.linksForSave, function (valI, i) {
                                            Candidate.addLink(
                                                {
                                                    url: valI.url,
                                                    candidateId: val.object.candidateId,
                                                    name: valI.fileName,
                                                },
                                                function (resp) {},
                                            );
                                            if ($scope.linksForSave.length - 1 == i) {
                                                $location.path('/candidates/' + val.object.localId);
                                            }
                                        });
                                    } else {
                                        $location.path('/candidates/' + val.object.localId);
                                    }
                                } else {
                                    if (val.message === 'blockedDeletingCandidates') {
                                        $rootScope.showBlockDeletingCandidatesModal(val.object);
                                        $scope.candidate.status = {
                                            name: `candidate_status_assoc.${$scope.candidatePrevStatus}`,
                                            value: $scope.candidatePrevStatus,
                                        };
                                    } else if (val.status === 'delete_limit' && val.total === 0) {
                                        notificationService.error(
                                            $filter('translate')(
                                                'deleteLimit.You_have_reached_the_daily_limit_for_removing_candidates_You_can_continue_tomorrow',
                                            ),
                                        );
                                    } else {
                                        notificationService.error(val.message);
                                    }

                                    $scope.saveButtonIsPressed = false;
                                    $rootScope.loading = false;
                                    $rootScope.$$phase || $scope.$apply();
                                }
                            },
                            function () {
                                $scope.saveButtonIsPressed = false;
                                $localStorage.set('candidateForSave', candidate);
                                $cookies.url = $location.$$url;
                                $cookies.cfauth = 'false';
                                $window.location.replace('/');
                            },
                        );
                    } else {
                        notificationService.error($translate.instant('Candidate fill incorrectly'));
                        $('html, body').animate({ scrollTop: 0 }, 'fast');
                        $rootScope.loading = false;
                        $rootScope.$$phase || $scope.$apply();
                    }
                }
            }
        };

        // $('.ui.dropdown').dropdown();

        $scope.fromLinkFile = function () {
            if ($scope.fastCandResumeLinkFile !== undefined) {
                $scope.fastCandLoading = true;
                Candidate.fromLinkFile(
                    { url: $scope.fastCandResumeLinkFile },
                    function (res) {
                        if (angular.equals(res.status, 'ok')) {
                            Candidate.convert($scope, res.object);
                        } else if (angular.equals(res.status, 'error')) {
                            notificationService.error(res.message);
                        }
                        $scope.fastCandLoading = false;
                    },
                    function (val) {
                        $scope.fastCandLoading = false;
                    },
                );
            } else {
                notificationService.error($filter('translate')('Enter a valid url'));
            }
        };

        $scope.fromText = function () {
            $scope.fastCandLoading = true;
            Candidate.fromText(
                { text: $rootScope.fastCandResumeText },
                function (res) {
                    if (angular.equals(res.status, 'ok')) {
                        Candidate.convert($scope, res.object);
                        transformDataFromTextResume(res.object);
                        $scope.fastCandText = false;
                    } else if (angular.equals(res.status, 'error')) {
                        notificationService.error(res.message);
                    }
                    $scope.fastCandLoading = false;
                },
                function (val) {
                    $scope.fastCandLoading = false;
                },
            );
        };

        $scope.fastCandResumeClick = function () {
            if ($scope.fastCandResume) {
                $scope.fastCandResume = false;
            } else {
                $scope.fastCandResume = true;
                $scope.fastCandText = false;
                $scope.fastCandSite = false;
            }
        };
        $scope.fastCandTextClick = function () {
            if ($scope.fastCandText) {
                $scope.fastCandText = false;
            } else {
                $scope.fastCandResume = false;
                $scope.fastCandText = true;
                $scope.fastCandSite = false;
            }
        };
        $scope.fastCandSiteClick = function () {
            if ($scope.fastCandSite) {
                $scope.fastCandSite = false;
            } else {
                $scope.fastCandResume = false;
                $scope.fastCandText = false;
                $scope.fastCandSite = true;
            }
        };
        Candidate.ZIP($scope);

        $scope.showAddLinkFunc = function () {
            $scope.showAddLink = true;
        };
        $scope.closeAddLinkFunc = function () {
            $scope.showAddLink = false;
            $scope.addLinkToCandidate.name = null;
            $scope.addLinkToCandidate.url = null;
            $scope.addLinkErrorShow = false;
        };
        $scope.addLinkInCandidateStart = function () {
            if ($scope.addLinkToCandidate.name && $scope.addLinkToCandidate.url) {
                $scope.addLinkToCandidate.url = $rootScope.addLinkPrefix($scope.addLinkToCandidate.url);
                $scope.linksForSave.push({
                    url: $scope.addLinkToCandidate.url,
                    fileName: $scope.addLinkToCandidate.name,
                });
                $scope.addLinkToCandidate.name = '';
                $scope.addLinkToCandidate.url = '';
                $scope.showAddLink = false;
            } else {
                $scope.addLinkErrorShow = true;
                if (!$scope.addLinkToCandidate.name && !$scope.addLinkToCandidate.url) {
                    notificationService.error($filter('translate')('Please enter a title and URL'));
                }

                if ($scope.addLinkToCandidate.name && !$scope.addLinkToCandidate.url) {
                    notificationService.error($filter('translate')('Please enter a URL'));
                }

                if (!$scope.addLinkToCandidate.name && $scope.addLinkToCandidate.url) {
                    notificationService.error($filter('translate')('Please enter a title'));
                }
            }
        };

        $scope.removeSource = function () {
            $scope.removableSource = $scope.getOriginAutocompleterValue();
            $scope.modalInstance = $uibModal.open({
                animation: true,
                windowClass: 'secondary-modal',
                templateUrl: '../partials/modal/origin-remove.html',
                scope: $scope,
                size: '',
                resolve: {},
            });
        };

        $scope.confirmDeleteOrigin = function () {
            Candidate.removeOriginAll(
                {
                    origin: $scope.removableSource,
                },
                function (resp) {
                    if (resp.status != 'error') {
                        $scope.modalInstance.close();
                        notificationService.success($filter('translate')('Origin removed'));
                        $scope.setOriginAutocompleterValue();
                    } else {
                        $scope.modalInstance.close();
                        notificationService.error(resp.message);
                    }
                },
                function (err) {
                    $scope.modalInstance.close();
                    notificationService.error(err);
                },
            );
        };

        $scope.editOriginName = function () {
            $scope.originOldName = $scope.getOriginAutocompleterValue();
            $rootScope.originName = $scope.originOldName;
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/origin-name-edit.html',
                size: '',
                resolve: {},
            });
        };
        $rootScope.saveOriginName = function () {
            Candidate.editOriginAll(
                {
                    originOld: $scope.originOldName,
                    originNew: $rootScope.originName,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        notificationService.success($filter('translate')('Origin_name_saved'));
                    }
                },
            );
            $scope.setOriginAutocompleterValue($rootScope.originName);
            $rootScope.closeModal();
        };

        $scope.employmentTypesOptions = Service.employmentType().map((item, index) => ({
            label: $translate.instant(item.value),
            value: item.value,
        }));

        $scope.employmentTypeModel = [];
        $scope.onChangeEmploymentType = (value) => {
            $scope.employmentTypeModel = value;
            $scope.candidate.employmentType = value?.map((item) => item.value) || [];
            $scope.progressUpdate();
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.changeRating = function (skill, rating) {
            skill.level = rating;
        };
        $scope.deleteSkill = function (skill) {
            $scope.candidate.skills.splice($scope.candidate.skills.indexOf(skill), 1);
        };
        $scope.resetBirthDate = function () {
            $scope.datepickerOfBirth = '';
            angular.forEach($('.editDate'), function (nval) {
                $(nval).attr('mdp-placeholder', '');
                $(nval).find('label')[0].innerHTML = '';
            });
        };

        $scope.checkDateTime = function (field, val, name) {
            field['value'] = val;

            $scope.editCustomId = field.fieldId;
            let flag = false;
            switch (name) {
                case 'datepickerOfCustomEdit':
                    val = $rootScope.TimeMinusTimeZone(Date.parse(val));
                    if (val == null) {
                        $scope.datepickerOfCustomEdit = '';
                    } else {
                        $scope.datepickerOfCustomEdit = val;
                        if ($scope.candidate.fieldValues && $scope.candidate.fieldValues.length > 0) {
                            angular.forEach($scope.candidate.fieldValues, function (val) {
                                if (val.field.fieldId == $scope.editCustomId) {
                                    val.dateTimeValue = $scope.datepickerOfCustomEdit;
                                    flag = true;
                                }
                            });
                            if (!flag) {
                                $scope.candidate.fieldValues.push({
                                    objType: 'candidate',
                                    dateTimeValue: $scope.datepickerOfCustomEdit,
                                    field: {
                                        fieldId: $scope.editCustomId,
                                    },
                                });
                            }
                        } else {
                            $scope.candidate.fieldValues.push({
                                objType: 'candidate',
                                dateTimeValue: $scope.datepickerOfCustomEdit,
                                field: {
                                    fieldId: $scope.editCustomId,
                                },
                            });
                        }
                    }
                    return $scope.candidate.fieldValues;
                case 'datepickerOfCustomEditTime':
                    if (val == null) {
                        $scope.datepickerOfCustomEditTime = '';
                    } else {
                        $scope.datepickerOfCustomEditTime = Date.parse(val);
                        if ($scope.candidate.fieldValues && $scope.candidate.fieldValues.length > 0) {
                            angular.forEach($scope.candidate.fieldValues, function (val) {
                                if (val.field.fieldId == $scope.editCustomId) {
                                    val.dateTimeValue = $scope.datepickerOfCustomEditTime;
                                    flag = true;
                                }
                            });
                            if (!flag) {
                                $scope.candidate.fieldValues.push({
                                    objType: 'candidate',
                                    dateTimeValue: $scope.datepickerOfCustomEditTime,
                                    field: {
                                        fieldId: $scope.editCustomId,
                                    },
                                });
                            }
                        } else {
                            $scope.candidate.fieldValues.push({
                                objType: 'candidate',
                                dateTimeValue: $scope.datepickerOfCustomEditTime,
                                field: {
                                    fieldId: $scope.editCustomId,
                                },
                            });
                        }
                    }
                    return $scope.candidate.fieldValues;
            }
        };

        $scope.deleteDate = function (field) {
            $scope.editCustomId = field.fieldId;
            $scope.candidate.fieldValues.forEach((nval, ind) => {
                if (field.fieldId == nval.field.fieldId) {
                    nval.dateTimeValue = '';
                    $scope.candidate.fieldValues.splice(ind, 1);
                }
            });
        };

        $rootScope.closeModal = function () {
            $scope.modalInstance.close();
        };

        $rootScope.changeSearchType = function (param) {
            $window.location.replace('/!#/candidates');
            $rootScope.changeSearchTypeNotFromCandidates = param;
        };

        $scope.selectFavoriteContacts = function ($scope, event, contact, index) {
            Candidate.selectFavoriteContacts($scope, event, contact, index);
        };

        $scope.candidate.sex = 'male';
        if ($location.search().resumeFromLink) {
            $scope.fromLinkSite();
        }

        $scope.onChangeResumeLink = (newValue) => {
            $scope.fastCandResumeLinkSite = newValue;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeFirstName = (newValue) => {
            $scope.candidate.firstName = $scope.validateCandidateName(newValue, 'firstName');
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onBlurFirstName = () => {
            $scope.progressUpdate();
            $scope.checkDuplicatesByNameAndContacts();
            $scope.checkErrorName($scope.candidate.firstName, 'firstName');
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeMiddleName = (newValue) => {
            $scope.candidate.middleName = $scope.validateCandidateName(newValue, 'middleName');
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onBlurMiddleName = () => {
            $scope.checkDuplicatesByNameAndContacts();
            $scope.checkErrorName($scope.candidate.middleName, 'middleName');
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeLastName = (newValue) => {
            $scope.candidate.lastName = $scope.validateCandidateName(newValue, 'lastName');
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onBlurLastName = () => {
            $scope.progressUpdate();
            $scope.checkDuplicatesByNameAndContacts();
            $scope.checkErrorName($scope.candidate.lastName, 'lastName');
            $rootScope.$$phase || $scope.$apply();
        };

        /**
         * @param { 'male' | 'female' } newValue
         */
        $scope.onChangeSex = (newValue) => {
            $scope.candidate.sex = newValue;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.originModel = null;
        $scope.onChangeOrigin = (newValue) => {
            $scope.originModel = newValue;
            $scope.candidateOrigin = newValue?.value || null;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeStatus = (newValue) => {
            $scope.candidate.status = newValue;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeLinkName = (newValue) => {
            $scope.addLinkToCandidate.name = newValue;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeLinkUrl = (newValue) => {
            $scope.addLinkToCandidate.url = newValue;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeTags = (newValue) => {
            $scope.candidate.groups = newValue;
            $rootScope.$$phase || $scope.$apply();
        };

        const _fetchPositionsOptions = $rootScope.makeDebounce((text, callback) => {
            fetch('hr/candidate/autocompletePosition', {
                method: 'POST',
                body: $rootScope._objectToFormData({ text }),
            })
                .then((resp) => resp.json())
                .then((data) => {
                    if (data.status !== 'ok') {
                        notificationService.error(data.message);
                    }

                    callback([...data.objects.map((option) => ({ label: option, value: option }))]);
                });
        }, 700);

        $scope.getPositionsOptions = (text, callback) => {
            if (!text.length || (text?.length && text?.length < $scope.inputSearchThreshold)) return callback();
            _fetchPositionsOptions(text, callback);
        };

        $scope.onChangePosition = (newValue, _, __, componentInternals) => {
            $scope.positionModel = newValue;
            $scope.candidate.position = newValue?.value || null;
            componentInternals?.setInputValue?.(newValue?.label);
            $scope.checkErrorName($scope.candidate.position, 'position');
            $scope.progressUpdate();
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeSalary = (newValue) => {
            $scope.candidate.salary = newValue;
            $scope.progressUpdate();
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeCurrency = (newValue) => {
            $scope.candidate.currency = newValue;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeIndustry = (newValue) => {
            $scope.industryModel = newValue;
            $scope.candidate.industry = newValue?.value || null;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeCurrentWorkingPlace = (newValue) => {
            $scope.candidate.currentWorkPlace = $scope.validateCandidateName(newValue, 'currentWorkPlace');
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onBlurCurrentWorkingPlace = () => {
            $scope.checkErrorName($scope.candidate.currentWorkPlace, 'currentWorkPlace');
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeCurrentPosition = (newValue) => {
            $scope.candidate.currentPosition = $scope.validateCandidateName(newValue, 'currentPosition');
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onBlurCurrentPosition = () => {
            $scope.checkErrorName($scope.candidate.currentPosition, 'currentPosition');
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.roleLevelModel = {};
        $scope.onChangeRoleLevel = (newValue) => {
            $scope.roleLevelModel = newValue;
            $scope.candidate.roleLevel = $scope.roleLevelModel?.value;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.addNewSkillField = () => {
            $scope.skillsModel.push({
                skill: undefined,
                experience: {
                    label: 'e00_no_experience',
                    value: 'e00_no_experience',
                },
                mustHave: false,
            });
            $scope.$$phase || $scope.$apply();
        };

        $scope.removeSkillField = (index) => {
            refreshSkillsOptions($scope.skillsModel[index]['skill'], 'restore');
            $scope.skillsModel.splice(index, 1);
            $scope.progressUpdate();
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeSkill = (newValue, index) => {
            if (!newValue) {
                refreshSkillsOptions($scope.skillsModel[index]['skill'], 'restore');
                $scope.skillsModel[index]['skill'] = null;
                $scope.$$phase || $scope.$apply();
                return;
            }

            refreshSkillsOptions(newValue);

            if ($scope.errorFields.skills && newValue.value) $scope.errorFields.skills = false;
            if (newValue.isNew) newValue.value = { skill: newValue.value };
            newValue['new'] = newValue?.isNew || false;
            $scope.skillsModel[index]['skill'] = newValue;

            $scope.progressUpdate();
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeSkillExperience = (newValue, index) => {
            $scope.skillsModel[index]['experience'] = newValue;
            $rootScope.wrongRole = false;
            $scope.$$phase || $scope.$apply();
        };

        const refreshSkillsOptions = (skill, action = 'remove') => {
            if (!skill || skill.isNew) return;

            const type = skill.value.type;
            if (type === 'general') {
                if (action === 'remove') {
                    $scope.skillsOptions[0].options = $scope.skillsOptions[0].options.filter(
                        ({ value }) => value.skillId !== skill.value.skillId,
                    );
                } else if (action === 'restore') {
                    $scope.skillsOptions[0].options.push(skill);
                    $scope.skillsOptions[0].options = $scope.skillsOptions[0].options.sort((a, b) =>
                        a.label.localeCompare(b.label),
                    );
                }
            } else if (type === 'custom') {
                if (action === 'remove') {
                    $scope.skillsOptions[1].options = $scope.skillsOptions[1].options.filter(
                        ({ value }) => value.skillId !== skill.value.skillId,
                    );
                } else if (action === 'restore') {
                    $scope.skillsOptions[1].options.push(skill);
                    $scope.skillsOptions[1].options = $scope.skillsOptions[1].options.sort((a, b) =>
                        a.label.localeCompare(b.label),
                    );
                }
            }
        };

        $scope.addNewLanguageField = (event, index) => {
            $scope.languagesModel.push({
                name: null,
                level: null,
            });
            $scope.$$phase || $scope.$apply();
        };

        $scope.removeLanguageField = (index) => {
            if ($scope.languagesModel[index]['name']) {
                $scope.languagesOptions.push($scope.languagesModel[index].name);
                $scope.languagesOptions = $rootScope.getSortedLangOptions($scope.languagesOptions);
            }

            $scope.languagesModel = $scope.languagesModel.filter((_, i) => i !== index);
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeLanguage = (newValue, index) => {
            if (newValue?.value) {
                $scope.languagesOptions = $scope.languagesOptions.filter(
                    ({ value }) => value.english !== newValue.value.english,
                );
            } else {
                $scope.languagesOptions.push($scope.languagesModel[index].name);
                $scope.languagesOptions = $rootScope.getSortedLangOptions($scope.languagesOptions);
            }

            $scope.languagesModel[index]['name'] = newValue;
            $scope.progressUpdate();
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeLanguageLevel = (newValue, index) => {
            $scope.languagesModel[index]['level'] = newValue;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeExperience = (newValue) => {
            $scope.candidate.expirence = newValue;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeEducation = (newValue) => {
            $scope.candidate.education = newValue;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeCountry = (newValue) => {
            $scope.candidate.country = newValue;
            $scope.candidate.city = null;
            getCities($scope.candidate.country?.value);
            $scope.$$phase || $scope.$apply();
        };

        const _openGoogleDropdown = (id) => {
            setTimeout(() => {
                const googleDropdown = document.querySelector(`#${id} div div`);
                if (googleDropdown) {
                    googleDropdown.click();
                }
            }, 50);
        };

        $scope.onChangeCity = (newValue) => {
            if (newValue?.value === '**go_to_google_location**' && newValue.goToGoogleLocation) {
                $scope.showGoogleLocationPicker = true;
                $scope.initialGooglePlacesInput = newValue.inputValue;
                _openGoogleDropdown('googleLocation');
            } else {
                $scope.candidate.city = newValue;
            }

            $scope.progressUpdate();
            $scope.$$phase || $scope.$apply();
        };

        const _getGooglePlaces = $rootScope.makeDebounce((input, callback) => {
            GoogleMaps.getGooglePlaces(input)
                .then((resp) =>
                    callback(
                        resp.predictions.map((location) => ({
                            ...location,
                            label: location.description,
                            value: location.place_id,
                        })),
                    ),
                )
                .catch((e) => console.error(e));
        }, 500);

        $scope.fetchGooglePlaces = (text, callback) => {
            if (!text || (text?.length && text?.length < $scope.inputGoogleSearchThreshold)) return callback();
            _getGooglePlaces(text, callback);
        };

        const _getGoogleLocationDetails = (placeId) => {
            GoogleMaps.getGooglePlaceDetails(placeId)
                .then((resp) => {
                    $scope.candidate.region = { ...Service.convertToRegionObject(resp), fromGoogle: true };
                    $scope.progressUpdate();
                    $rootScope.$$phase || $scope.$apply();
                })
                .catch((e) => console.error(e));
        };

        const _getGoogleLocationDetailsRelocate = (placeId) => {
            GoogleMaps.getGooglePlaceDetails(placeId)
                .then((resp) => {
                    $scope.regionsToRelocate.push({
                        ...newRegion,
                        fromGoogle: true,
                    });
                    $rootScope.$$phase || $scope.$apply();
                })
                .catch((e) => console.error(e));
        };

        $scope.googleLocationModel = null;
        $scope.onChangeGoogleLocation = (newValue) => {
            $scope.googleLocationModel = newValue;

            if (!newValue) {
                $scope.candidate.region = null;
                $scope.candidate.city = null;
                $scope.candidate.country = null;
                $scope.showGoogleLocationPicker = false;
            } else {
                newValue && _getGoogleLocationDetails(newValue.reference);
            }

            $rootScope.$$phase || $scope.$apply();
        };

        $scope.countryForRelocate = null;
        $scope.onChangeCountryForRelocate = (newValue) => {
            $scope.countryForRelocate = newValue;

            if (newValue && newValue.countryEn === 'Any' && newValue.countryRu === 'Любая') {
                $scope.regionsToRelocate = [];
                $scope.cityForRelocate = null;
            } else {
                getCities($scope.countryForRelocate?.label, true);
            }

            $scope.$$phase || $scope.$apply();
        };

        $scope.cityForRelocate = null;
        $scope.onChangeCityForRelocate = (newValue) => {
            if (newValue?.value === '**go_to_google_location**' && newValue.goToGoogleLocation) {
                $scope.cityForRelocate = null;
                $scope.countryForRelocate = null;
                $scope.showGoogleLocationPickerRelocate = true;
                $scope.initialRelocateGooglePlacesInput = newValue.inputValue;
                _openGoogleDropdown('googleLocationRelocate');
            } else {
                if (
                    $scope.regionsToRelocate.some(
                        (region) =>
                            $scope.countryForRelocate.googlePlaceId === region.country.googlePlaceId &&
                            region.city.value.googlePlaceId === newValue.value.googlePlaceId,
                    )
                ) {
                    return;
                }

                $scope.cityForRelocate = newValue;

                if ($scope.cityForRelocate.value && $scope.countryForRelocate.value) {
                    $scope.regionsToRelocate.push({ country: $scope.countryForRelocate, city: $scope.cityForRelocate });
                    $scope.cityForRelocate = null;
                    $scope.countryForRelocate = null;
                }
            }

            $scope.$$phase || $scope.$apply();
        };

        $scope.googleRelocateLocationModel = null;
        $scope.onChangeRelocateGoogleLocation = (newValue) => {
            $scope.googleRelocateLocationModel = newValue;

            _getGoogleLocationDetailsRelocate(newValue.reference);

            $scope.googleRelocateLocationModel = null;
            $scope.showGoogleLocationPickerRelocate = false;

            $rootScope.$$phase || $scope.$apply();
        };
    },
]);
