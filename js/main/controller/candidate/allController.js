function CandidateAllController(
    $localStorage,
    $state,
    $translate,
    Service,
    $scope,
    ngTableParams,
    Candidate,
    $location,
    $rootScope,
    $filter,
    $cookies,
    serverAddress,
    notificationService,
    googleService,
    $window,
    ScopeService,
    frontMode,
    Vacancy,
    Company,
    vacancyStages,
    $sce,
    $analytics,
    Mail,
    FileInit,
    $uibModal,
    Person,
    $timeout,
    CandidateGroup,
    $anchorScroll,
    CandidateSearchService,
    CustomField,
    CustomFieldsSearchService,
    StreamService,
    CandidatesSlider,
    CandidateResumeFromLink,
    Mailing,
    Email,
    Gdpr,
    $stateParams,
    gamificationNotify,
    $http,
    $httpParamSerializer,
) {
    $scope.candidatesPageView = $rootScope.me.personParams?.candidatesPageView || 'list';
    const initialCandidates = { list: null, loaded: false };
    $scope.newCandidatesOrigin = {};
    $scope.newCandidatesResponsible = {};
    $scope.newCandidatesDesiredPosition = '';
    $rootScope.objectSize = null;
    $scope.candidateObjectSize = null;
    $scope.flagForEmptyBlock = false;
    $scope.originsOptions = [];
    $scope.responsibleOptionsGrouped = [];
    $scope.isCandidates = false;
    $scope.isActualSearch = false;
    $scope.inputSearchThreshold = 2;
    $rootScope.isActualSearch = false;
    $rootScope.isAddCandidates = true;
    $rootScope.candidateLength = null;
    $scope.enableExcelUploadAll = 'N';
    $rootScope.setCurrent = true;
    $scope.a = {};
    $scope.sliderId = CandidatesSlider.getSliderId();
    $scope.a.searchNumber = 1;
    $rootScope.candidateInterviews = [];
    $scope.addCandidateChangeStage = [];
    $scope.candidatesAddToVacancyIds = [];
    $scope.selectedVacancy = null;
    $scope.checkAllCandidates = false;
    $scope.showTagsForMass = false;
    $scope.previousFlag = true;
    $scope.deleteFromSystem = false;
    $scope.placeholder = $filter('translate')('by position');
    $rootScope.addCandidateChangeStage = $scope.addCandidateChangeStage;
    $rootScope.candidatesAddToVacancyIds = $scope.candidatesAddToVacancyIds;
    $rootScope.currentElementPos = true;
    $scope.deleteCandidatesComment = '';
    $scope.persons = [];
    $rootScope.persons = [];
    $scope.selectedLanguages = [];
    $scope.selectedCandidatesName = [];
    $rootScope.isReducedNumberOfCandidates = false;
    $rootScope.candidatesNotFind = false;
    $rootScope.filterSkillsParam = false;
    $scope.tagName = undefined;
    localStorage.setItem('isAddCandidates', $rootScope.isAddCandidates);
    localStorage.setItem('setCurrent', true);
    localStorage.setItem('currentPage', 'candidates');
    localStorage.removeItem('stageUrl');
    sessionStorage.removeItem('localStageUrl');
    localStorage.removeItem('candidatesInStagesVac');
    localStorage.removeItem('getAllCandidates');

    Candidate.getCandidate = [];
    $scope.search = new CandidateSearchService();

    vacancyStages.get(function (resp) {
        $scope.customStages = resp.object ? resp.object.interviewStates : [];
        $rootScope.customStages = resp.object ? resp.object.interviewStates : [];
    });
    $scope.getLettersEnd = function (number) {
        let n = Math.abs(number) % 100,
            n1 = n % 10,
            form = 2;

        if (n > 10 && n < 20) form = 2;
        if (n1 > 1 && n1 < 5) form = 1;
        if (n1 === 1) {
            if (number > 1 && $rootScope.currentLang === 'en') {
                form = 2;
            } else {
                form = 0;
            }
        }
        if (n > 10 && n < 15 && n1 > 0 && n1 < 5) form = 2;
        return form;
    };
    $scope.closeModal = function () {
        $scope.modalInstance.close();
    };

    let oldSortParam;

    isCorporateEmailIntergated();

    (function () {
        $scope.search = new CandidateSearchService();

        $scope.search.addField({
            name: 'boolean',
            placeholder: 'Search by candidates',
        });
        $scope.search.addField({
            name: 'name',
            type: 'personal',
            placeholder: 'by name',
        });
        $scope.search.addField({
            name: 'surname',
            type: 'personal',
            placeholder: 'By surname',
        });
        $scope.search.addField({
            name: 'country',
            type: 'personal',
            data: this.countries,
            placeholder: 'country',
            path: { label: 'value.showName', value: 'value.value' },
        });
        $scope.search.addField({
            name: 'city',
            type: 'personal',
            data: this.filteredCities,
            placeholder: 'city',
            path: { label: 'value.showName', value: 'value.value' },
        });
        $scope.search.addField({
            name: 'sex',
            type: 'personal',
            placeholder: 'sex',
            path: { label: 'value.sex', value: 'value.value' },
        });
        $scope.search.addField({
            name: 'date',
            type: 'personal',
            placeholder: 'date',
        });
        $scope.search.addField({
            name: 'withPersonalContacts',
            type: 'personal',
            placeholder: 'contacts',
            path: { label: 'value.label', value: 'value.value' },
        });
        $scope.search.addField({
            name: 'state',
            type: 'personal',
            placeholder: 'status',
            path: { label: 'value.name', value: 'value.value' },
        });
        $scope.search.addField({
            name: 'position',
            type: 'professional',
            placeholder: 'by position',
        });
        $scope.search.addField({
            name: 'scorecards',
            type: 'professional',
            placeholder: 'by scorecards',
        });
        $scope.search.addField({
            name: 'gdpr',
            type: 'professional',
            placeholder: 'Status Gdpr',
        });
        $scope.search.addField({
            name: 'experience',
            type: 'professional',
            placeholder: 'experience',
            path: { label: 'value.value', value: 'value.value' },
        });
        $scope.search.addField({
            name: 'languages',
            type: 'professional',
            placeholder: 'languages',
        });
        $scope.search.addField({
            name: 'skills',
            type: 'professional',
            placeholder: 'skills',
        });
        $scope.search.addField({
            name: 'salaryTo',
            type: 'professional',
            placeholder: 'salary_before',
        });
        $scope.search.addField({
            name: 'industry',
            type: 'professional',
            placeholder: 'industry',
        });
        $scope.search.addField({
            name: 'employmentType',
            type: 'professional',
            placeholder: 'employment_type',
            path: { label: 'value.value', value: 'value.value' },
        });
        $scope.search.addField({
            name: 'origin',
            type: 'other',
            placeholder: 'source',
        });
        $scope.search.addField({
            name: 'responsible',
            type: 'other',
            placeholder: 'responsible',
            path: { label: 'value.fullName', value: 'value.userId' },
        });
        $scope.search.addField({
            name: 'candidateGroup',
            type: 'other',
            placeholder: 'candidate tags',
        });
        $scope.search.addField({ name: 'matchType' });
        $scope.search.addField({
            name: 'comment',
            type: 'other',
            placeholder: 'comment',
        });

        $scope.customFieldsSearch = new CandidateSearchService();
    })();

    (function () {
        Candidate.autocompleteAllOrigins(
            $httpParamSerializer({ text: '' }),
            (resp) => {
                if (resp.status === 'ok') {
                    $scope.originsOptions = resp.objects.map((label) => ({
                        label: $translate.instant(label),
                        value: label,
                    }));
                }
            },
            (err) => {
                console.error(err);
            },
        );
    })();

    $scope.onChangeCandidateSource = (newValue) => {};

    $scope.imgWidthFunc = function (id) {
        var img = new Image();
        img.onload = function () {
            var width = this.width;
            var height = this.height;
            var minus = width - height;
            if (width >= height && minus > 40 && minus <= 100) {
                $('#page-avatar').css({
                    width: '100%',
                    height: 'auto',
                    margin: 'inherit',
                });
            } else if ((width >= 300 && width <= 349) || width == height) {
                $('#page-avatar').css({
                    width: '100%',
                    'object-fit': 'fill',
                    margin: 'inherit',
                });
            } else if (width >= 350) {
                $('#page-avatar').css({
                    width: '100%',
                    height: 'auto',
                    margin: 'inherit',
                });
            } else if (width >= 201) {
                $('#page-avatar').css({ width: '100%', height: 'auto' });
            } else {
                $('#page-avatar').css({
                    width: 'inherit',
                    height: 'inherit',
                    display: 'block',
                    margin: '0 auto',
                });
            }
        };
        img.src =
            $location.$$protocol +
            '://' +
            $location.$$host +
            $scope.serverAddress +
            '/getapp?id=' +
            id +
            '&d=' +
            $rootScope.me.personId;
    };
    $rootScope.closeModal = function () {
        $scope.modalInstance.close();
    };
    $scope.closeModal = function () {
        $scope.modalInstance.close();
    };
    $rootScope.closeModalAddResumeFromText = function () {
        $scope.modalInstance.close();
        $rootScope.fastCandResumeText = '';
    };
    $rootScope.closeModalResumeFromLink = function () {
        $scope.modalInstance.close();
        $rootScope.fastCandResumeLinkSite = '';
    };
    $rootScope.closeModalAfterActivity = function () {
        $rootScope.modalInstance ? $rootScope.modalInstance.close() : null;
    };

    Company.getParam({ name: 'enableExcelUploadAll' }, function (resp) {
        if (angular.equals(resp.status, 'ok')) {
            $scope.enableExcelUploadAll = resp.object;
        }
    });
    $scope.filterForChange = 'dm';

    $rootScope.candidatesSortType = {
        name: 'Date of last activity',
        values: 'dm',
    };

    $scope.filterSort = [
        { name: 'By relevance', values: 'relevance' },
        { name: 'Date added to database', values: 'dc' },
        {
            name: 'Date of last activity',
            values: 'dm',
        },
        {
            name: 'By last name from A to Z',
            values: 'alphabetically',
        },
        { name: 'By last name from Z to A', values: 'lastName' },
        { name: 'Date of last comment', values: 'lastCommentDate' },
        { name: 'Employment start date', values: 'hireDate' },
        { name: 'Score-sort', values: 'scoreCard' },
    ];

    $rootScope.addCandidateInVacancy = {
        id: '',
        comment: '',
        status: 'longlist',
        date: null,
        showSelect: '',
        showText: false,
        text: '',
    };

    $scope.currentDocPreviewPage = 0;

    $rootScope.addCandidateInVacancySelect2Obj = {
        status: null,
    };
    Candidate.fromFile($scope, $rootScope, $location);
    $rootScope.errorMessageForAddCandidateInVacancy = {
        show: false,
        text: '',
    };

    $scope.menuOptions = [
        [
            $filter('translate')('Open in new tab'),
            function ($itemScope) {
                $itemScope.user.openStatus = 'Y';
                $scope.setCandidatesSlider();
                $window.open(
                    $location.absUrl() + '/slide/' + $scope.sliderId + '/' + $itemScope.user.localId,
                    '_blank',
                );
            },
        ],
        [
            $filter('translate')('Add to vacancy'),
            function ($itemScope) {
                var state = null;
                var showSelect = true;
                var showText = false;
                $rootScope.clickedAddVacancyInCandidate = false;
                $scope.toAddVacancyForm = function (state, showSelect, showText, candidate) {
                    $rootScope.candidateIdForVacancyId = candidate.candidateId;
                    state = null;
                    $rootScope.addCandidateInVacancy.showText = false;
                    $rootScope.addCandidateInVacancy.showSelect = true;
                    $rootScope.VacancyStatusFiltered = null;
                    $rootScope.addCandidateInVacancy.inVacancy = false;
                    $rootScope.addCandidateInVacancy.statusObject = null;
                    $rootScope.addCandidateInVacancy.comment = '';
                    $('#candidateAddToVacancy').select2('val', null);
                    $rootScope.addCandidateInVacancy.status =
                        state !== null
                            ? state
                            : {
                                  value: 'longlist',
                                  withDate: false,
                                  defaultS: true,
                                  single: false,
                                  added: true,
                                  active_color: 'longlist_color',
                                  useAnimation: false,
                                  count: 0,
                                  forAdd: true,
                              };
                    $scope.modalInstance = $uibModal.open({
                        animation: true,
                        templateUrl: '../partials/modal/candidate-add-in-vacancy.html',
                        windowClass: 'candidate-add-in-vacancy',
                        resolve: {
                            items: function () {
                                return $scope.items;
                            },
                        },
                    });
                    $scope.modalInstance.opened.then(function () {
                        setTimeout(function () {
                            $('.addCandidateInvacancyPicker')
                                .datetimepicker({
                                    format: 'dd/mm/yyyy hh:ii',
                                    startView: 2,
                                    minView: 0,
                                    autoclose: true,
                                    weekStart: $rootScope.currentLang == 'ru' || $rootScope.currentLang == 'ua' ? 1 : 7,
                                    language: $translate.use(),
                                })
                                .on('changeDate', function (data) {
                                    $rootScope.addCandidateInVacancy.date = data.date;
                                })
                                .on('hide', function () {
                                    if ($('.addCandidateInvacancyPicker').val() == '') {
                                        $rootScope.addCandidateInVacancy.date = null;
                                    } else {
                                        $rootScope.emailTemplateInModal.text =
                                            $rootScope.emailTemplateInModal.text.replace(
                                                /\[\[interview date and time\]\]/g,
                                                $filter('dateFormat2')(
                                                    $('.addCandidateInvacancyPicker')
                                                        .datetimepicker('getDate')
                                                        .getTime(),
                                                    true,
                                                ),
                                            );
                                        $rootScope.emailTemplateInModal.title =
                                            $rootScope.emailTemplateInModal.title.replace(
                                                /\[\[interview date and time\]\]/g,
                                                $filter('dateFormat2')(
                                                    $('.addCandidateInvacancyPicker')
                                                        .datetimepicker('getDate')
                                                        .getTime(),
                                                    true,
                                                ),
                                            );
                                    }
                                    $('.addCandidateInvacancyPicker').blur();
                                });
                        }, 0);
                    });
                    $rootScope.candidateForUpdateResume = $itemScope.user;
                    Service.createEmailTemplateFunc($scope, $rootScope, 'addCandidateInVacancyMCE', Mail, $location);
                    $rootScope.candnotify = {};
                    Candidate.getContacts({ candidateId: $itemScope.user.candidateId }, function (resp) {
                        var email = '';
                        angular.forEach(resp.objects, function (c) {
                            if (c.type == 'email') {
                                email = c.value;
                            }
                        });
                        $rootScope.candnotify.emails = email.replace(/ /gi, '').split(',');
                        $rootScope.candnotify.sendMail = $rootScope.candnotify.emails[0];
                    });
                    $rootScope.candnotify.show = false;
                    $rootScope.candnotify.fullName = $itemScope.user.fullName;
                    $rootScope.candnotify.name = $itemScope.user;
                    $rootScope.candnotify.send = $localStorage.get('candnotify') != 'false';
                };

                $rootScope.addVacancyInCandidate = function (sendTemplate) {
                    if (!$rootScope.clickedAddVacancyInCandidate) {
                        $rootScope.clickedAddVacancyInCandidate = true;

                        if ($('#candidateAddToVacancy').select2('data') == null) {
                            $rootScope.errorMessageForAddCandidateInVacancy.show = true;
                            $rootScope.errorMessageForAddCandidateInVacancy.text = $filter('translate')(
                                'You must specify the position',
                            );
                            $rootScope.clickedAddVacancyInCandidate = false;
                            return;
                        }
                        var vacancyId = $('#candidateAddToVacancy').select2('data').id;
                        $rootScope.errorMessageForAddCandidateInVacancy.show = false;

                        $rootScope.addCandidateInVacancy.date =
                            $('.addCandidateInvacancyPicker').datetimepicker('getDate') != null &&
                            ($rootScope.addCandidateInVacancy.status.withDate ||
                                $rootScope.addCandidateInVacancy.status.type == 'interview')
                                ? $('.addCandidateInvacancyPicker').datetimepicker('getDate')
                                : null;
                        if ($rootScope.addCandidateInVacancy.status.customInterviewStateId) {
                            Service.vacancyAddInterview(
                                Vacancy,
                                vacancyId,
                                null,
                                $itemScope.user.candidateId,
                                $rootScope.addCandidateInVacancy.comment,
                                $rootScope.addCandidateInVacancy.status.customInterviewStateId,
                                $rootScope.addCandidateInVacancy.date,
                                function (resp) {
                                    resp.object.vacancyId.interviewStatusNotTouchable =
                                        resp.object.vacancyId.interviewStatus;
                                    if (!$itemScope.user.interviews) {
                                        $itemScope.user.interviews = [];
                                    }
                                    $rootScope.clickedAddVacancyInCandidate = false;
                                    $rootScope.addCandidateInVacancy.comment = '';
                                    $rootScope.addCandidateInVacancy.id = null;
                                    $itemScope.user.interviews.push(resp.object);
                                    angular.forEach($itemScope.user.interviews, function (interview) {
                                        if (interview.vacancyId.interviewStatus == undefined) {
                                            interview.vacancyId.interviewStatus =
                                                'longlist,shortlist,interview,approved,notafit,declinedoffer';
                                            interview.vacancyId.interviewStatusNotTouchable =
                                                interview.vacancyId.interviewStatus;
                                        }
                                    });
                                    angular.forEach($itemScope.user.interviews, function (value) {
                                        var array = value.vacancyId.interviewStatus.split(',');
                                        angular.forEach($scope.customStages, function (resp) {
                                            if (value.state == resp.customInterviewStateId) {
                                                value.state = resp.name;
                                            }
                                            angular.forEach(array, function (res) {
                                                if (resp.customInterviewStateId == res) {
                                                    array[array.indexOf(resp.customInterviewStateId)] = resp.name;
                                                }
                                            });
                                        });
                                        value.vacancyId.interviewStatus = array.toString();
                                    });
                                    $rootScope.addCandidateInVacancy.status = null;
                                    $rootScope.addCandidateInVacancy.date = null;
                                    $rootScope.VacancyStatusFiltered = '';
                                    $rootScope.closeModal();
                                },
                                function (resp) {
                                    $rootScope.clickedAddVacancyInCandidate = false;
                                    $rootScope.errorMessageForAddCandidateInVacancy.show = true;
                                    $rootScope.errorMessageForAddCandidateInVacancy.text = resp.message;
                                },
                                frontMode,
                                notificationService,
                                googleService,
                                $scope.selectedCalendar != undefined ? $scope.selectedCalendar.id : null,
                                $filter,
                                $translate.use(),
                                $rootScope,
                            );
                        } else {
                            Service.vacancyAddInterview(
                                Vacancy,
                                vacancyId,
                                null,
                                $itemScope.user.candidateId,
                                $rootScope.addCandidateInVacancy.comment,
                                $rootScope.addCandidateInVacancy.status.value,
                                $rootScope.addCandidateInVacancy.date,
                                function (resp) {
                                    resp.object.vacancyId.interviewStatusNotTouchable =
                                        resp.object.vacancyId.interviewStatus;
                                    if (!$itemScope.user.interviews) {
                                        $itemScope.user.interviews = [];
                                    }
                                    $rootScope.clickedAddVacancyInCandidate = false;
                                    $rootScope.addCandidateInVacancy.comment = '';
                                    $rootScope.addCandidateInVacancy.id = null;
                                    $itemScope.user.interviews.push(resp.object);
                                    angular.forEach($itemScope.user.interviews, function (interview) {
                                        if (interview.vacancyId.interviewStatus == undefined) {
                                            interview.vacancyId.interviewStatus =
                                                'longlist,shortlist,interview,approved,notafit,declinedoffer';
                                            interview.vacancyId.interviewStatusNotTouchable =
                                                interview.vacancyId.interviewStatus;
                                        }
                                    });
                                    angular.forEach($itemScope.user.interviews, function (value) {
                                        var array = value.vacancyId.interviewStatus.split(',');
                                        angular.forEach($scope.customStages, function (resp) {
                                            if (value.state == resp.customInterviewStateId) {
                                                value.state = resp.name;
                                            }
                                            angular.forEach(array, function (res) {
                                                if (resp.customInterviewStateId == res) {
                                                    array[array.indexOf(resp.customInterviewStateId)] = resp.name;
                                                }
                                            });
                                        });
                                        value.vacancyId.interviewStatus = array.toString();
                                    });
                                    $rootScope.addCandidateInVacancy.status = null;
                                    $rootScope.addCandidateInVacancy.date = null;
                                    $rootScope.VacancyStatusFiltered = '';
                                    $rootScope.closeModal();
                                },
                                function (resp) {
                                    $rootScope.clickedAddVacancyInCandidate = false;
                                    $rootScope.errorMessageForAddCandidateInVacancy.show = true;
                                    $rootScope.errorMessageForAddCandidateInVacancy.text = resp.message;
                                },
                                frontMode,
                                notificationService,
                                googleService,
                                $scope.selectedCalendar != undefined ? $scope.selectedCalendar.id : null,
                                $filter,
                                $translate.use(),
                                $rootScope,
                            );
                        }
                        if ($rootScope.candnotify.show && sendTemplate) {
                            var candnotify = $rootScope.candnotify;
                            var changeObj = $rootScope.addCandidateInVacancy;
                            Mail.sendMailByTemplateVerified(
                                {
                                    toEmails: candnotify.sendMail,
                                    vacancyId: $rootScope.VacancyAddedInCandidate.vacancyId,
                                    candidateId: $rootScope.candidateForUpdateResume.candidateId,
                                    fullName: $rootScope.candidateForUpdateResume.fullName,
                                    email: $rootScope.emailTemplateInModal.email,
                                    date: $rootScope.addCandidateInVacancy.date,
                                    lang: $translate.use(),
                                    template: {
                                        type: $rootScope.emailTemplateInModal.type,
                                        title: $rootScope.emailTemplateInModal.title,
                                        text: $rootScope.emailTemplateInModal.text,
                                        fileId:
                                            $rootScope.fileForSave.length > 0 ? $rootScope.fileForSave[0].fileId : null,
                                        fileName:
                                            $rootScope.fileForSave.length > 0
                                                ? $rootScope.fileForSave[0].fileName
                                                : null,
                                    },
                                },
                                function (resp) {
                                    if (resp.status == 'ok') {
                                        notificationService.success($filter('translate')('Letter sent'));
                                        $rootScope.closeModal();
                                    } else {
                                        notificationService.error(
                                            $filter('translate')(
                                                'Error connecting integrate with email. Connect it again',
                                            ),
                                        );
                                    }
                                },
                            );
                        }
                    }
                };
                $scope.toAddVacancyForm(state, showSelect, showText, $itemScope.user);
            },
        ],
        [
            $filter('translate')('Edit candidate'),
            function ($itemScope) {
                if ($rootScope.me.recrutRole != 'client') {
                    $window.open(
                        $location.protocol() +
                            '://' +
                            $location.host() +
                            '/!#/candidate/edit/' +
                            $itemScope.user.localId,
                        '_blank',
                    );
                } else {
                    notificationService.error(
                        $filter('translate')('Only recruiters, admins and freelancers can editing candidates'),
                    );
                }
            },
        ],
        [
            $filter('translate')('Remove candidate'),
            function ($itemScope) {
                $scope.deleteCandidate($itemScope.user);
            },
        ],
    ];
    $rootScope.transferCandidateInOtherVacancyStatus = function () {
        var status = $('#candidateAddToVacancy').select2('data').status;
        if (status == 'approved' && $rootScope.me.recrutRole != 'admin') {
            $rootScope.errorMessageForAddCandidate.text = $filter('translate')(
                'Transfer from the status of approved can only Admin',
            );
            $rootScope.errorMessageForAddCandidate.show = true;
            return;
        }

        $rootScope.changeStatusOfInterviewInVacancy = $rootScope.addCandidateInVacancy;
        $rootScope.changeStatusOfInterviewInVacancy.vacancyId = $('#candidateAddToVacancy').select2('data').id;
        var data =
            $('.addCandidateInvacancyPicker').datetimepicker('getDate') != null &&
            $rootScope.addCandidateInVacancy.status.withDate
                ? $('.addCandidateInvacancyPicker').datetimepicker('getDate')
                : null;
        $rootScope.saveStatusInterviewInVacancy(data);
        $('.addCandidateInVacancy').modal('hide');
        $rootScope.candidateAddedInVacancy = false;
        $('.addCandidateInvacancyPicker').val('');
    };
    $rootScope.saveStatusInterviewInVacancy = function (customDate) {
        if (!$rootScope.clickedSaveStatusInterviewInVacancy) {
            $rootScope.clickedSaveStatusInterviewInVacancy = true;
            $rootScope.changeStatusOfInterviewInVacancy.errorMessage = false;
            var changeObj = $rootScope.changeStatusOfInterviewInVacancy;
            if (changeObj.status.value == 'declinedoffer' && changeObj.comment == '') {
                $rootScope.changeStatusOfInterviewInVacancy.errorMessage = true;
                $rootScope.clickedSaveStatusInterviewInVacancy = false;
                return;
            }
            if ($rootScope.showEmployedFields) {
                changeObj.date =
                    $('.changeStatusOfInterviewEmployed').datetimepicker('getDate') != null
                        ? $('.changeStatusOfInterviewEmployed').datetimepicker('getDate')
                        : customDate != undefined
                        ? customDate
                        : null;
            } else {
                changeObj.date =
                    $('.changeStatusOfInterviewInVacancyPick').datetimepicker('getDate') != null
                        ? $('.changeStatusOfInterviewInVacancyPick').datetimepicker('getDate')
                        : customDate != undefined
                        ? customDate
                        : null;
            }
            if ($rootScope.showEmployedFields && $rootScope.changeStatusOfInterviewInVacancy.vacancyId) {
                Vacancy.editInterview(
                    {
                        personId: $scope.personId,
                        vacancyId: $rootScope.changeStatusOfInterviewInVacancy.vacancyId,
                        candidateId: $rootScope.candidateForUpdateResume.candidateId,
                        interviewId: changeObj.candidate.interviewId,
                        interviewState: changeObj.status.customInterviewStateId
                            ? changeObj.status.customInterviewStateId
                            : changeObj.status.value,
                        comment: changeObj.comment,
                        lang: $translate.use(),
                        probationaryPeriod: $rootScope.probationaryPeriod,
                        dateEmployee: changeObj.date != null ? changeObj.date.getTime() : null,
                    },
                    function (resp) {
                        if (resp.status == 'ok') {
                            if (changeObj.status.customInterviewStateId) {
                                var id = resp.object.interviewId + changeObj.status.customInterviewStateId;
                            } else {
                                var id = resp.object.interviewId + changeObj.status.value;
                            }
                            $scope.showChangeStatusValue = null;
                            $rootScope.clickedSaveStatusInterviewInVacancy = false;
                            if ($rootScope.candnotify.send && $rootScope.candnotify.sendMail.length > 1) {
                                var candnotify = $rootScope.candnotify;
                                var changeObj = $rootScope.changeStatusOfInterviewInVacancy;
                                Mail.sendMailByTemplate(
                                    {
                                        toEmails: candnotify.sendMail,
                                        vacancyId: $rootScope.changedStatusVacancy
                                            ? $rootScope.changedStatusVacancy.vacancyId
                                            : $rootScope.VacancyAddedInCandidate.vacancyId,
                                        candidateId: $rootScope.candidateForUpdateResume.candidateId,
                                        fullName: $rootScope.candidateForUpdateResume.fullName,
                                        email: $rootScope.emailTemplateInModal.email,
                                        date: changeObj.date != null ? changeObj.date.getTime() : null,
                                        lang: $translate.use(),
                                        template: {
                                            type: $rootScope.emailTemplateInModal.type,
                                            title: $rootScope.emailTemplateInModal.title,
                                            text: $rootScope.emailTemplateInModal.text,
                                            fileId:
                                                $rootScope.fileForSave.length > 0
                                                    ? $rootScope.fileForSave[0].fileId
                                                    : null,
                                            fileName:
                                                $rootScope.fileForSave.length > 0
                                                    ? $rootScope.fileForSave[0].fileName
                                                    : null,
                                        },
                                    },
                                    function (resp) {
                                        if (resp.status == 'ok') {
                                            notificationService.success($filter('translate')('Letter sent'));
                                            $rootScope.closeModal();
                                        } else {
                                            notificationService.error(
                                                $filter('translate')(
                                                    'Error connecting integrate with email. Connect it again',
                                                ),
                                            );
                                        }
                                    },
                                );
                            }
                            $rootScope.changeStatusOfInterviewInVacancy = {
                                candidate: {},
                                comment: '',
                                status: '',
                                date: null,
                                exportgoogle: false,
                            };
                            $rootScope.addCandidateInInterviewbuttonClicked = false;
                            $rootScope.closeModal();
                            $('.changeStatusOfInterviewInVacancyPick').val('');
                            $scope.getLastEvent();
                            notificationService.success($filter('translate')('candidate was added to the stage'));
                        } else if (resp.status == 'error') {
                            $rootScope.clickedSaveStatusInterviewInVacancy = false;
                            notificationService.error(resp.message);
                        }
                    },
                    function (err) {
                        $scope.showChangeStatusValue = null;
                        $rootScope.clickedSaveStatusInterviewInVacancy = false;
                        $rootScope.addCandidateInInterviewbuttonClicked = false;
                        console.error(err);
                    },
                );
            } else if ($rootScope.changeStatusOfInterviewInVacancy.vacancyId) {
                Vacancy.editInterview(
                    {
                        personId: $scope.personId,
                        vacancyId: $rootScope.changeStatusOfInterviewInVacancy.vacancyId,
                        candidateId: $rootScope.candidateForUpdateResume.candidateId,
                        interviewState: changeObj.status.name
                            ? changeObj.status.customInterviewStateId
                            : changeObj.status.value,
                        comment: changeObj.comment,
                        date: changeObj.date != null ? changeObj.date.getTime() : null,
                        lang: $translate.use(),
                    },
                    function (resp) {
                        if (resp.status == 'ok') {
                            var changeObj = $rootScope.changeStatusOfInterviewInVacancy;
                            if (changeObj.status.customInterviewStateId) {
                                var id = resp.object.interviewId + changeObj.status.customInterviewStateId;
                            } else {
                                var id = resp.object.interviewId + changeObj.status.value;
                            }
                            $scope.showChangeStatusValue = null;
                            $rootScope.clickedSaveStatusInterviewInVacancy = false;
                            if ($rootScope.candnotify.send && $rootScope.candnotify.sendMail.length > 1) {
                                var candnotify = $rootScope.candnotify;
                                var changeObj = $rootScope.changeStatusOfInterviewInVacancy;
                                Mail.sendMailByTemplate(
                                    {
                                        toEmails: candnotify.sendMail,
                                        vacancyId: $rootScope.changedStatusVacancy
                                            ? $rootScope.changedStatusVacancy.vacancyId
                                            : $rootScope.VacancyAddedInCandidate.vacancyId,
                                        candidateId: $rootScope.candidateForUpdateResume.candidateId,
                                        fullName: $rootScope.candidateForUpdateResume.fullName,
                                        email: $rootScope.emailTemplateInModal.email,
                                        date: changeObj.date != null ? changeObj.date.getTime() : null,
                                        lang: $translate.use(),
                                        template: {
                                            type: $rootScope.emailTemplateInModal.type,
                                            title: $rootScope.emailTemplateInModal.title,
                                            text: $rootScope.emailTemplateInModal.text,
                                            fileId:
                                                $rootScope.fileForSave.length > 0
                                                    ? $rootScope.fileForSave[0].fileId
                                                    : null,
                                            fileName:
                                                $rootScope.fileForSave.length > 0
                                                    ? $rootScope.fileForSave[0].fileName
                                                    : null,
                                        },
                                    },
                                    function (resp) {
                                        if (resp.status == 'ok') {
                                            notificationService.success($filter('translate')('Letter sent'));
                                            $rootScope.closeModal();
                                        } else {
                                            notificationService.error(
                                                $filter('translate')(
                                                    'Error connecting integrate with email. Connect it again',
                                                ),
                                            );
                                        }
                                    },
                                );
                            }
                            $rootScope.changeStatusOfInterviewInVacancy = {
                                candidate: {},
                                comment: '',
                                status: '',
                                date: null,
                                exportgoogle: false,
                            };
                            $rootScope.addCandidateInInterviewbuttonClicked = false;
                            $rootScope.closeModal();
                            $('.changeStatusOfInterviewInVacancyPick').val('');
                            notificationService.success($filter('translate')('candidate was added to the stage'));
                        } else if (resp.status == 'error') {
                            $rootScope.clickedSaveStatusInterviewInVacancy = false;
                            notificationService.error(resp.message);
                        }
                    },
                    function (err) {
                        $scope.showChangeStatusValue = null;
                        $rootScope.clickedSaveStatusInterviewInVacancy = false;
                        $rootScope.addCandidateInInterviewbuttonClicked = false;
                        console.error(err);
                    },
                );
            }
        }
    };
    $scope.serverAddress = serverAddress;
    $scope.candidateFound = false;
    $scope.regionId = null;
    $rootScope.changeStateInCandidate = {
        status: '',
        comment: '',
        fullName: null,
        placeholder: null,
        candidate: null,
    };
    $scope.statusAssoc = Candidate.getStatusAssociative();
    $scope.extensionHas = false;
    $scope.textSearchType = [
        { name: 'words through AND', value: 'AllWords' },
        { name: 'words through OR', value: 'any' },
        { name: 'exact match', value: 'whole' },
        { name: 'Only by position', value: 'byPosition' },
    ];
    $scope.textSearchTypeModel = $scope.textSearchType[0].value;

    $scope.boxParam = {
        cs: {
            text_chooser: true,
            reserve: true,
            region: true,
            salary: true,
            type: true,
            industry: true,
            sex: true,
            status: true,
            age: true,
            language: true,
            skill: true,
        },
    };
    $scope.box = $scope.boxParam.cs;
    Service.gender($scope);
    $rootScope.loading = false;
    $rootScope.searchCheck = $rootScope.searchCheck == undefined ? false : $rootScope.searchCheck;
    $rootScope.searchCheckExternal =
        $rootScope.searchCheckExternal == undefined ? false : $rootScope.searchCheckExternal;
    $scope.industries = Service.getIndustries();

    function scope_update(val) {
        $scope.$broadcast('scopeChanged');
        $scope.tableParams.reload();
    }

    ScopeService.setCurrentControllerUpdateFunc(scope_update);

    if (localStorage.countCandidate) {
        $scope.startPagesShown = localStorage.countCandidate;
    } else {
        $scope.startPagesShown = 15;
    }

    $rootScope.excelExportType = 'candidates';
    $rootScope.loadingExcel = false;
    $rootScope.buildExcel = false;

    $scope.exportToExcelHandler = function () {
        if ($rootScope.loadingExcel) return;

        $rootScope.loading = true;

        Candidate.getAllCandidates($scope.currentSearchParams)
            .then((resp) => {
                const candidatesTotal = resp.total;
                $scope.exportToExcel(candidatesTotal);
            })
            .catch((error) => {
                console.error(error);
            })
            .finally(() => {
                $rootScope.loading = false;
                $rootScope.$$phase || $scope.$apply();
            });
    };

    $scope.exportToExcel = function (candidatesTotal) {
        $rootScope.loadingExcel = true;

        $scope.criteriaForExcel.page.count = candidatesTotal;

        $scope.criteriaForExcel.candidatesIds = $scope.candidatesAddToVacancyIds;

        if ($scope.criteriaForExcel.words == null) {
            $scope.criteriaForExcel.searchFullTextType = null;
        }

        if ($rootScope.excelExportType === 'all') {
            $scope.criteriaForExcel.withCommentsAndHistory = true;
        }
        StreamService.addStream({
            promise: Candidate.excel($scope.criteriaForExcel),
            success: createExcelSuccess,
            error: createExcelError,
        });

        function createExcelSuccess(resp) {
            $rootScope.buildExcel = true;
            $scope.criteriaForExcel.withCommentsAndHistory = false;
            $scope.$apply();
        }

        function createExcelError(resp) {
            $scope.criteriaForExcel.withCommentsAndHistory = false;
            notificationService.error($filter('translate')(resp.message));
            $scope.$apply();
        }
    };

    $scope.toExcelHistory = function () {
        if (!$rootScope.objectSize) {
            notificationService.error($filter('translate')('No candidates for export according to criteria'));
            return;
        }

        if ($rootScope.buildExcel) {
            $rootScope.buildExcel = false;
            $rootScope.loadingExcel = false;
        }

        $scope.onChangeExcelExportType = (type) => {
            $rootScope.excelExportType = type;
            $scope.$$phase || $scope.$apply();
        };

        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/candidate-excel-history.html',
            scope: $scope,
            resolve: {},
            size: '',
        });
    };

    $scope.emitEventAdvancedSearch = function () {
        $scope.$broadcast('openAdvancedSearch');
    };

    $scope.viewExcelHistory = function () {
        $rootScope.closeModal();
        $location.path('excelHistory');
    };

    $scope.externalData = [];

    $scope.hideDetailElement = function () {
        $scope.showMessageAboutChangeTypeOfOtherSiteSearch = false;
    };
    $scope.showDetail = function () {
        $scope.showMessageAboutChangeTypeOfOtherSiteSearch = true;
        $scope.showMessageAboutChangeTypeOfOtherSiteSearchmouseover = true;
    };

    function initializeCandidates() {
        $rootScope.loading = true;

        if ($stateParams.ids) {
            $rootScope.redirectIds = $stateParams.ids;
            $location.search({});
        }

        if (!$rootScope.hasOwnProperty('isNeedToResetSearch')) {
            $rootScope.isNeedToResetSearch = true;
        }

        if ($rootScope.previousLocation.indexOf('/candidates/slide/{sliderDataId}/{id}') !== -1) {
            if ($rootScope.isNeedToResetSearch) {
                Candidate.search.resetParams();
                $rootScope.isSearchActualCandidates = false;
                $rootScope.dropdownScoreCardId = null;
                Candidate.search.resetFieldsValues();
            }
        } else {
            Candidate.search.resetParams();
            $rootScope.isSearchActualCandidates = false;
            $rootScope.dropdownScoreCardId = null;
            Candidate.search.resetFieldsValues();
        }

        if ($rootScope.isNeedToResetSearch) {
            Candidate.search.resetParams();
            $rootScope.dropdownScoreCardId = null;
            Candidate.search.resetFieldsValues();
            $rootScope.isSearchActualCandidates = false;
        }

        if (
            $rootScope.previousLocation.indexOf('/candidates/slide/{sliderDataId}/{id}') === -1 ||
            $rootScope.clickFrom === 'topNavBar' ||
            $rootScope.clickFrom === 'sideNavBar'
        ) {
            $scope.tableParams.$params.page = 1;
            $scope.tableParams.$params.count = 15;
            $rootScope.clickFrom = null;
            $scope.tableParams.reload();
        }

        setActiveScopeParams();

        $scope.getInitialData = () => {
            Candidate.getAllCandidates({
                ...Candidate.search.params,
                displayContacts: true,
                displayLanguages: true,
                displayRelocatedRegion: true,
            }).then((resp) => {
                if (resp.status === 'error') {
                    if (resp.code === 'solrExceptionCandidate') {
                        $scope.countCandidateEmpty = true;
                    } else {
                        notificationService.error(resp.message);
                    }
                }

                resp.objects?.length > 0 && resp?.filter?.vacancyParamsChanged
                    ? ($rootScope.isReducedNumberOfCandidates = true)
                    : ($rootScope.isReducedNumberOfCandidates = false);
                resp.objects?.length === 0 && resp?.filter?.searchByVacancyParams
                    ? ($rootScope.candidatesNotFind = true)
                    : ($rootScope.candidatesNotFind = false);

                $rootScope.isReducedNumberOfCandidates = !!(
                    resp?.objects?.length && resp?.filter?.vacancyParamsChanged
                );

                if (resp.filter?.vacancyParamsChanged) {
                    if (!resp.filter.languages) {
                        $rootScope.resetLanguagesFromActual();
                    }
                    if (!resp.filter.employmentType) {
                        $rootScope.resetEmploymentTypeFromActual();
                    }
                    if (!resp.filter.city) {
                        $rootScope.resetCityFromActual();
                    }
                    if (!resp.filter.country) {
                        $rootScope.resetCountryFromActual();
                    }
                    if (!resp.filter.experience) {
                        $rootScope.resetExperienceFromActual();
                    }
                    if (!resp.filter.candidateSkills) {
                        $rootScope.resetSkillsFromActual();
                    } else {
                        $rootScope.filterSkillsParam = true;
                        $rootScope.skillsParam = resp.filter.candidateSkills;
                    }
                    if (!resp.filter.salaryTo) {
                        $rootScope.resetSalaryFromActual();
                    }
                    if (!resp.filter.role && !resp.filter.roleLevels) {
                        $rootScope.resetRoleFromActual();
                    }
                    if (!resp.filter.roleLevels) {
                        $rootScope.resetRoleFromActual();
                        $rootScope.roleLevelsParam = null;
                    }
                }

                initialCandidates.loaded = true;
                initialCandidates.list = resp;
                $rootScope.loading = false;
                $scope.tableParams.reload();
                $rootScope.redirectToSearch = false;
                if (resp?.filter?.vacancyParamsChanged) {
                    $rootScope.isSearchActualCandidates = true;
                } else {
                    $rootScope.isSearchActualCandidates = false;
                }

                // $rootScope.isSearchActualCandidates = false;
                $scope.oldUsers = $scope.candidates;
            });
        };

        if (!$rootScope.redirectToSearch) {
            $scope.getInitialData();
        }

        Email.getMailboxes().then(
            (resp) => {
                let permittedEmail = resp.filter((email) => email.personalMailing);
                $rootScope.me.emails = permittedEmail;
                $scope.personalMailingEmail = permittedEmail[0] ? permittedEmail[0].email : null;
                Email.setDefaultMailbox($scope.personalMailingEmail);
                if (!permittedEmail && resp.length) $scope.noAllowedMails = true;
            },
            (error) => notificationService.error(error),
        );

        $rootScope.isNeedToResetSearch = true;
    }

    function handlerCandidateMessage() {
        angular.forEach($scope.candidates, function (value) {
            if (value.lastAction) {
                if (value.lastAction.customInterviewStates) {
                    $rootScope.customStages.forEach((stage) => {
                        value.lastAction.customInterviewStates.forEach((custom) => {
                            if (stage.customInterviewStateId === custom.customInterviewStateId) {
                                custom.stateNew = stage.type;
                            }
                        });
                    });
                }
            }
        });

        $scope.candidates?.forEach((user) => {
            if (user.lastAction) {
                if (user.lastAction.descr.indexOf('@') !== -1 && user.lastAction.descr.length > 200) {
                    user.lastAction.tags = true;
                }

                if (user.lastAction.type === 'task_change_status') {
                    if (user.lastAction.stateNew === 'open') user.lastAction.stateNew = 'inwork';
                    if (user.lastAction.stateOld === 'open') user.lastAction.stateOld = 'inwork';
                }

                if (user.lastAction.descr) {
                    user.lastAction.descr = user.lastAction.descr.replaceAll('&nbsp;', ' ');
                    user.lastAction.descrLength = user.lastAction.descr.replace(/(<([^>]+)>)/gi, '').length;
                    user.lastAction.cutDescr = $filter('cutName')(
                        user.lastAction.descr.replace(/(<([^>]+)>)/gi, ''),
                        true,
                        75,
                    );

                    user.lastAction.itsCutDescr =
                        user.lastAction.cutDescr.endsWith('…') &&
                        !$rootScope.checkIfOnlyContainsLink(user.lastAction.descr);

                    let regExp = new RegExp(`([a-zA-Z0-9]{32})`, 'g');
                    let save = user.lastAction?.descr?.match(regExp);
                    save = [...new Set(save)];
                    let getIds = user.lastAction.descr.split('@');

                    let indexOf = user.lastAction.descr.indexOf('@');
                    if (indexOf !== -1 && save !== null) {
                        $scope.persons.forEach((person) => {
                            for (let i = save.length - 1; i >= 0; i--) {
                                if (person.userId === save[i]) {
                                    save.splice(i, 1);
                                }
                            }
                        });

                        getIds.forEach((item, index) => {
                            save.forEach((sav) => {
                                if (item.substr(0, 32) === sav) {
                                    getIds[index] = item.replaceAll(sav, 'userDeleted');
                                }
                            });
                        });
                        user.lastAction.descr = getIds.join('@');
                    }

                    if (user.lastAction.type === 'scorecard_result_save') {
                        if (user.lastAction.descr === 'Default' || user.lastAction.descr === 'Old Default') {
                            user.lastAction.descr = $filter('translate')(user.lastAction.descr);
                        }
                    }
                }
            }
        });

        $scope.persons.forEach((person) => {
            $scope.candidates?.forEach((user) => {
                if (user.lastAction) {
                    if (user.lastAction.descr.indexOf('@') !== -1 && user.lastAction.descr.length > 200) {
                        user.lastAction.tags = true;
                    }
                    if (user.lastAction.descr) {
                        user.lastAction.cutDescr = user.lastAction.cutDescr.replaceAll(
                            `@${person.userId}`,
                            `<a href="!#/users/${
                                person.userId
                            }" style="display: inline; text-decoration: none; line-height: 2; background-color: #DAECE1; border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;">@${
                                $rootScope.useAmericanNameStyle ? person.fullNameEn : person.fullName
                            }</a>`,
                        );

                        user.lastAction.descr = user.lastAction.descr.replaceAll(
                            `<span style="text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;">@${person.userId}</span>`,
                            `<a href="!#/users/${
                                person.userId
                            }" style="display: inline; text-decoration: none; line-height: 2; background-color: #DAECE1; border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;">@${
                                $rootScope.useAmericanNameStyle ? person.fullNameEn : person.fullName
                            }</a>`,
                        );
                    }
                }
            });
        });
    }

    $scope.hideModal = function () {
        $scope.isActualSearch = !$scope.isActualSearch;
    };

    $scope.getActualVacancy = function () {
        Candidate.getActualVacancy().then(
            (response) => {
                if (response.object) {
                    $scope.isActualSearch = true;
                    $rootScope.isActualSearch = true;
                    $rootScope.adviceVacancySettingDto = response.object.adviceVacancySettingDto;
                    $rootScope.actualVacancyInfo = response.object;
                    $scope.tableParams.reload();
                }
            },
            (resp) => ($rootScope.loading = false),
        );
    };

    function candidateDataToAdvSearch(dataObj) {
        const { position, employmentType, experience, region, skills, languages, salary, roleLevels, role } = dataObj;

        $rootScope.vacancyPositionParam = position && { label: position, value: position };

        $rootScope.employmentTypeParam = employmentType && {
            label: `employment_type_assoc.${employmentType}`,
            value: employmentType,
        };

        $rootScope.experienceParam = experience && {
            label: `experience_assoc.${experience}`,
            value: experience,
        };

        if (region) {
            if (region.displayCountry && !region.displayCity) {
                $rootScope.regionParam = {
                    country: {
                        ...region,
                        showName: region.displayCountry,
                        label: region.displayCountry,
                        value: region.displayCountry,
                    },
                };
            } else {
                $rootScope.regionParam = {
                    country: {
                        ...region,
                        showName: region.displayCountry,
                        label: region.displayCountry,
                        value: region.displayCountry,
                    },
                    city: {
                        ...region.googlePlaceId,
                        showName: region.displayCity,
                        label: region.displayCity,
                        value: region.regionId,
                    },
                };
            }
        } else {
            $rootScope.regionParam = null;
        }

        $rootScope.salaryParam = salary;
        $rootScope.roleLevelsParam = $rootScope.isSearchActualCandidates ? role : roleLevels;

        $rootScope.skillsParam = skills?.map((skill) => {
            return {
                skill: {
                    ...skill,
                    label: skill.skillName,
                    value: skill.id,
                },
                experience: {
                    label: skill?.experience,
                    value: skill?.experience,
                },
            };
        });

        $rootScope.languagesParam = languages?.map((language) => {
            const foundLang = $rootScope.allLanguages[language.name];

            return {
                language: {
                    label: $rootScope.capitalize(
                        foundLang[$rootScope.getCurrentLangKey($rootScope.currentLang)] || foundLang.english,
                    ),
                    value: language.name,
                },
                levels: [
                    language.level && {
                        label: language?.level,
                        value: language?.level,
                    },
                ],
            };
        });
    }

    $scope.searchActualCandidates = function () {
        $rootScope.isSearchActualCandidates = true;

        candidateDataToAdvSearch($rootScope.actualVacancyInfo);

        setTimeout(() => {
            $rootScope.triggerSearchCandidates();
            $rootScope.actualVacancy = true;
        }, 200);
    };

    function setParamsForActualCandidates() {
        if ($rootScope.isSearchActualCandidates) {
            //set position
            if ($rootScope.vacancyPositionParam) {
                $scope.search.fields.position['value'] = $rootScope.vacancyPositionParam;
                // $rootScope.isSearchActualCandidates = false;
            } else {
                Candidate.search.setParam('position', null);
            }

            // set employment type
            if ($rootScope.employmentTypeParam) {
                $scope.search.fields.employmentType['value'] = $rootScope.employmentTypeParam;
            } else {
                Candidate.search.setParam('employmentType', null);
            }

            // set experience
            if ($rootScope.experienceParam) {
                $scope.search.fields.experience['value'] = $rootScope.experienceParam;
            } else {
                Candidate.search.setParam('experience', null);
            }

            // set location
            if ($rootScope.regionParam && !$rootScope.resetLocation) {
                $scope.search.fields.country['value'] = $rootScope.regionParam.country;
                // $scope.search.fields.city['value'] = $rootScope.regionParam.city;
            } else {
                Candidate.search.setParam('country', null); // pass value
                Candidate.search.setParam('city', null); // pass id
            }

            // set salary
            if ($rootScope.salaryParam) {
                Candidate.search.setParam('salaryTo', $rootScope.salaryParam);
            } else {
                Candidate.search.setParam('salaryTo', null);
            }

            // set skills
            if ($rootScope.skillsParam && !$rootScope.resetSkills) {
                $scope.search.fields.skills['value'] = $rootScope.skillsParam;
            } else {
                Candidate.search.setParam('candidateSkills', null);
            }

            //set languages
            if ($rootScope.languagesParam && !$rootScope.resetLanguages) {
                $scope.search.fields.languages['value'] = $rootScope.languagesParam;
            } else {
                Candidate.search.setParam('languages', null);
            }

            //set roles
            if ($rootScope.roleLevelsParam && !$rootScope.resetRoleLevels) {
                // if ($rootScope.isSearchActualCandidates) {
                //     Candidate.search.params.roleLevels = $rootScope.roleLevelsParam;
                // }

                if ($scope.search.fields.roleLevels) {
                    $scope.search.fields.roleLevels['value'] = [
                        { value: $rootScope.roleLevelsParam, label: $rootScope.roleLevelsParam },
                    ];
                }
            } else {
                Candidate.search.setParam('roleLevels', null);
            }
        }
    }

    function setActiveScopeParams() {
        var activeParam = ScopeService.getActiveScopeObject();
        const scopeObject = ScopeService.getScopeObject();

        let isRegionScopePrev = false;
        scopeObject.forEach((item) => {
            if (item.name === 'region' && item.isPrev) {
                isRegionScopePrev = true;
            }
        });

        if (activeParam.value) {
            Candidate.search.setParam('personId', null);
            Candidate.search.setParam('country', activeParam.value.value);
        } else if (activeParam.name === 'onlyMy') {
            Candidate.search.setParam('personId', $rootScope.me.userId);
            if (isRegionScopePrev) {
                Candidate.search.setParam('country', null);
            }
        } else if (activeParam.name === 'company') {
            Candidate.search.setParam('personId', null);
            if (isRegionScopePrev) {
                Candidate.search.setParam('country', null);
            }
        }

        setParamsForActualCandidates();

        if (Array.isArray(Candidate.search.params.languages) && !Candidate.search.params.languages.length) {
            $rootScope.isEmptyLanguages = true;
        } else {
            $rootScope.isEmptyLanguages = false;
        }

        return activeParam;
    }

    function setCandidatesTable() {
        $scope.tableParams = new ngTableParams(
            {
                page: Candidate.search.params.page.number + 1,
                count: Candidate.search.params.page.count,
            },
            {
                total: 0,
                getData: function ($defer, params) {
                    if ($rootScope.previousLocation === '/candidates/{id}') {
                        if ($scope.previousFlag) {
                            $scope.tableParams.page($rootScope.previousSearchNumber);
                            $scope.previousFlag = !$scope.previousFlag;
                        }
                    }

                    if (ScopeService.isInit()) {
                        $scope.activeScopeParam = setActiveScopeParams();
                        $rootScope.candidateActiveScopeParam = setActiveScopeParams();

                        if (Candidate.search.resetPagination) {
                            Candidate.search.setParam('page', {
                                number: 0,
                                count: 15,
                            });
                            Candidate.search.resetPagination = false;
                        } else {
                            Candidate.search.setParam('page', {
                                number: params.$params.page - 1,
                                count: params.$params.count,
                            });
                        }
                        if (params.$params.count <= 120) {
                            localStorage.countCandidate = params.$params.count;
                        } else {
                            localStorage.countCandidate = 15;
                        }

                        $scope.criteriaForExcel = angular.copy(Candidate.search.params);
                        $scope.currentSearchParams = angular.copy(Candidate.search.params);
                        $rootScope.searchParamInCandidate = Candidate.search.params;

                        function setPaginationParams(page, count) {
                            if (page || count) {
                                Candidate.search.setParam('page', {
                                    number: page,
                                    count: count,
                                });
                            } else {
                                $scope.isShowMore = false;
                            }
                        }

                        function setTableData(response, page, count) {
                            $rootScope.objectSize = response['objects'] ? response['total'] : 0;
                            localStorage.setItem('objectSize', $rootScope.objectSize);
                            $rootScope.objectSizeCand = $rootScope.objectSize;
                            $rootScope.searchParam = Candidate.search.params;
                            $scope.searchParams = Candidate.search.params;
                            $scope.searchParams.searchCs = true;
                            params.total(response['total']);
                            $scope.paginationParams = {
                                currentPage: Candidate.search.params.page.number,
                                totalCount: $rootScope.objectSize,
                            };
                            $scope.pagesCount =
                                response.allPagesCount ||
                                Math.ceil(response['total'] / Candidate.search.params.page.count);
                            $scope.candidateFound = response['total'] >= 1;
                            $scope.criteriaForExcel['page'] = {
                                number: 0,
                                count: $scope.objectSize,
                            };
                            $scope.limitReached = response['limitReached'];

                            if (page) {
                                $scope.candidates = $scope.candidates.concat(response['objects']);
                            } else {
                                $scope.candidates = response['objects'];
                            }

                            $scope.candidates?.forEach((candidate) => {
                                if (candidate.lastAction?.stateNew || candidate.lastAction?.stateOld) {
                                    if ($scope.customStages) {
                                        $scope.customStages.forEach((stage) => {
                                            if (stage.customInterviewStateId === candidate.lastAction.stateOld) {
                                                candidate.lastAction.customStateOld = candidate.lastAction.stateOld;
                                                candidate.lastAction.stateOld = stage.type;
                                            }
                                            if (stage.customInterviewStateId === candidate.lastAction.stateNew) {
                                                candidate.lastAction.customStateNew = candidate.lastAction.stateNew;
                                                candidate.lastAction.stateNew = stage.type;
                                            }
                                        });
                                    }
                                }
                            });

                            $scope.searchParam.searchCs = true;
                            $scope.isCandidates = true;
                            $scope.flagForEmptyBlock = true;

                            $scope.totalPagesCount = response.allPageCount;
                            $scope.candidateObjectSize = response['objects'] ? response['total'] : 0;
                        }

                        $scope.showFilePreview = function (history) {
                            const file = {
                                fileName: history.descr,
                                fileId: JSON.parse(history.data).fileId,
                            };
                            Service.showModalResume(file, $scope, $rootScope, $location, $sce, $uibModal);
                        };

                        function getCandidates(page, count) {
                            $scope.isCandidates = false;
                            $scope.flagForEmptyBlock = false;
                            $timeout(() => ($rootScope.loading = true));
                            setPaginationParams(page, count);
                            Candidate.getAllCandidates({
                                ...Candidate.search.params,
                                displayContacts: true,
                                displayLanguages: true,
                                displayRelocatedRegion: true,
                                isExtendedSearch: $rootScope.isAdvancedSearchTriggered ? true : undefined,
                            }).then(
                                (response) => {
                                    $rootScope.isAdvancedSearchTriggered = false;
                                    if (response.status === 'error') {
                                        notificationService.error(response.message);
                                        $rootScope.loading = false;
                                    } else {
                                        response.objects.length > 0 && response?.filter?.vacancyParamsChanged
                                            ? ($rootScope.isReducedNumberOfCandidates = true)
                                            : ($rootScope.isReducedNumberOfCandidates = false);
                                        response.objects.length === 0 && response?.filter?.searchByVacancyParams
                                            ? ($rootScope.candidatesNotFind = true)
                                            : ($rootScope.candidatesNotFind = false);

                                        if (response.filter?.vacancyParamsChanged) {
                                            if (!response.filter.languages) {
                                                $rootScope.resetLanguagesFromActual();
                                            }
                                            if (!response.filter.employmentType) {
                                                $rootScope.resetEmploymentTypeFromActual();
                                            }
                                            if (!response.filter.city) {
                                                $rootScope.resetCityFromActual();
                                            }
                                            if (!response.filter.country) {
                                                $rootScope.resetCountryFromActual();
                                            }
                                            if (!response.filter.experience) {
                                                $rootScope.resetExperienceFromActual();
                                            }
                                            if (!response.filter.candidateSkills) {
                                                $rootScope.resetSkillsFromActual();
                                            } else {
                                                $rootScope.filterSkillsParam = true;
                                                $rootScope.skillsParam = response.filter.candidateSkills;
                                            }
                                            if (!response.filter.salaryTo) {
                                                $rootScope.resetSalaryFromActual();
                                            }
                                            if (!response.filter.role && !response.filter.roleLevels) {
                                                $rootScope.resetRoleFromActual();
                                            }
                                            if (!response.filter.roleLevels) {
                                                $rootScope.resetRoleFromActual();
                                                $rootScope.roleLevelsParam = null;
                                            }
                                        }

                                        $scope.flagForEmptyBlock = true;

                                        response.objects.forEach((user) => {
                                            let regExp = new RegExp(`([a-zA-Z0-9]{32})`, 'g');
                                            let save = user.lastAction?.descr?.match(regExp);

                                            if (user.lastAction && user.lastAction.type === 'task_change_status') {
                                                if (user.lastAction.stateNew === 'open')
                                                    user.lastAction.stateNew = 'inwork';
                                                if (user.lastAction.stateOld === 'open')
                                                    user.lastAction.stateOld = 'inwork';
                                            }

                                            if (user.lastAction && user.lastAction.descr) {
                                                user.lastAction.descr = user.lastAction.descr.replaceAll('&nbsp;', ' ');
                                                user.lastAction.descrLength = user.lastAction.descr.replace(
                                                    /(<([^>]+)>)/gi,
                                                    '',
                                                ).length;
                                                user.lastAction.cutDescr = $filter('cutName')(
                                                    user.lastAction.descr.replace(/(<([^>]+)>)/gi, ''),
                                                    true,
                                                    75,
                                                );

                                                user.lastAction.itsCutDescr =
                                                    user.lastAction.cutDescr.endsWith('…') &&
                                                    !$rootScope.checkIfOnlyContainsLink(user.lastAction.descr);
                                            }

                                            if (user.lastAction && user.lastAction.descr) {
                                                $scope.save = user.lastAction.descr.match(regExp);
                                            }

                                            if (user.lastAction && user.lastAction.descr && $scope.save) {
                                                $scope.descrPersons = $scope.persons.filter((person) =>
                                                    $scope.save.some((id) => id === person.userId),
                                                );

                                                if (
                                                    user.lastAction.descr.indexOf('@') !== -1 &&
                                                    user.lastAction.descr.length > 200
                                                ) {
                                                    user.lastAction.tags = true;
                                                }

                                                $scope.descrPersons.forEach((person) => {
                                                    if (user.lastAction.descr) {
                                                        user.lastAction.descr = user.lastAction.descr.replaceAll(
                                                            `<span style="text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;">@${person.userId}</span>`,
                                                            `<a href="!#/users/${
                                                                person.userId
                                                            }" style="display: inline; text-decoration: none; line-height: 2; background-color: #DAECE1; border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;">@${
                                                                $rootScope.useAmericanNameStyle
                                                                    ? person.fullNameEn
                                                                    : person.fullName
                                                            }</a>`,
                                                        );
                                                        user.lastAction.cutDescr = user.lastAction.cutDescr.replaceAll(
                                                            `@${person.userId}`,
                                                            `<a href="!#/users/${
                                                                person.userId
                                                            }" style="display: inline; text-decoration: none; line-height: 2; background-color: #DAECE1; border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;">@${
                                                                $rootScope.useAmericanNameStyle
                                                                    ? person.fullNameEn
                                                                    : person.fullName
                                                            }</a>`,
                                                        );
                                                    }
                                                });
                                            }
                                        });

                                        setTableData(response, page, count);
                                        $scope.checkAllCandidates = true;
                                        $scope.candidates.forEach(function (cand, index) {
                                            $scope.candidatesAddToVacancyIds.forEach(function (cand_added) {
                                                if (cand.candidateId === cand_added) {
                                                    $scope.candidates[index].added = true;
                                                }
                                            });
                                            if (!cand.added) {
                                                $scope.checkAllCandidates = false;
                                            }
                                        });
                                        $defer.resolve($scope.candidates);
                                        $rootScope.loading = false;
                                        $scope.$apply();
                                    }

                                    $scope.setCandidatesSlider();
                                },
                                (resp) => {
                                    $rootScope.loading = false;
                                    $rootScope.isAdvancedSearchTriggered = false;
                                },
                            );
                            $rootScope.isReducedNumberOfCandidates = false;
                            $rootScope.isAdvancedSearchTriggered = false;
                        }

                        if (initialCandidates.list) {
                            setPaginationParams();
                            setTableData(initialCandidates.list);
                            handlerCandidateMessage();
                            $defer.resolve(initialCandidates.list.objects);
                            $scope.$$phase || $scope.$apply();
                            initialCandidates.list = null;
                        } else if (initialCandidates.loaded && Candidate.getCandidatesLoaded()) {
                            getCandidates();
                        }

                        if (!Candidate.getCandidatesLoaded()) {
                            Candidate.setCandidatesLoaded(true);
                        }

                        $scope.displayShowMore = params.total() >= 15 && params.page() !== $scope.pagesCount;

                        $scope.showMore = function () {
                            $scope.isShowMore = true;
                            $scope.displayShowMore = Service.dynamicTableLoading(
                                params.total(),
                                Candidate.search.params.page.number,
                                Candidate.search.params.page.count,
                                getCandidates,
                            );
                        };

                        $scope.a.searchNumber = $scope.tableParams.page();
                        $rootScope.previousSearchNumber = $scope.a.searchNumber;
                    }
                },
            },
        );
    }

    function initCandidates() {
        $scope.getActualVacancy();
        setActiveScopeParams();
        setCandidatesTable();
        initializeCandidates();
        Service.onGetLanguagesObjectCached().then(() => {
            $timeout(() => {
                if ($rootScope.redirectToSearch && $rootScope.candidateAdvSearchDTO) {
                    candidateDataToAdvSearch($rootScope.candidateAdvSearchDTO);
                    $rootScope.triggerSearchCandidates();

                    $scope.getInitialData();
                }
            }, 500);
        });
    }

    // For Pagination Component
    $scope.totalPagesCount = 0;

    $scope.changePage = (pageNumber) => {
        if ($scope.isShowMore) {
            $scope.tableParams.page($rootScope.previousSearchNumber);
            $scope.tableParams.reload();
        } else $scope.tableParams.page(pageNumber);

        if ($scope.candidatesPageView === 'table') {
            document.querySelector('#mainTable').scrollIntoView({
                block: 'start',
            });
        } else if ($scope.candidatesPageView === 'list') {
            document.querySelector('.candidates-cards-list-component').firstElementChild.scrollIntoView({
                block: 'start',
            });
        }

        $scope.$$phase || $scope.$apply();
    };

    $scope.changeAmountOfElements = (amount) => {
        if ($scope.tableParams.count() === amount) return;
        $scope.tableParams.count(amount);
        $scope.tableParams.reload();
    };

    initCandidates();

    $rootScope.changeFilterToDefault = function (sort) {
        $scope.filterForChange = sort;
    };

    $scope.changeFilter = function (sort) {
        if (sort === 'scoreCard' && !Candidate.search.params.scoreCard) {
            notificationService.error($filter('translate')('Select scorecard search'));
            $scope.filterForChange = 'dm';
            return ($rootScope.candidatesSortType = oldSortParam);
        }
        handlerCandidateMessage();
        if (!Candidate.search.params.words && sort == 'relevance') {
            notificationService.error(
                $filter('translate')('Sort by relevance impossible until you enter a value in the Text Search'),
            );
            $rootScope.candidatesSortType = oldSortParam;
        } else {
            if (sort == 'alphabetically') {
                $scope.filterForChange = sort;
            } else {
                $scope.filterForChange = sort;
            }

            Candidate.search.setParam('sort', $scope.filterForChange ? $scope.filterForChange : null);
            Candidate.search.setParam('sortOrder', $scope.filterForChange === 'alphabetically' ? 'ASC' : 'DESC');
            $scope.tableParams.$params.page = 1;
            $scope.tableParams.reload();
        }
    };

    $scope.sortTable = (sortParam) => {
        oldSortParam = $rootScope.candidatesSortType;

        if ($rootScope.candidatesSortType && $rootScope.candidatesSortType.values === sortParam.values) return;

        $rootScope.candidatesSortType = sortParam;

        if (typeof sortParam !== 'string') sortParam = sortParam.values;

        if (this.filterForChange === 'scoreCard' && sortParam === 'dm' && !$rootScope.dropdownScoreCardId) {
            return;
        }
        if (sortParam != undefined && this.filterForChange != sortParam) {
            if (this.filterForChange === 'scoreCard') {
                $rootScope.switchToOtherSort = this.filterForChange !== sortParam;
            }
            if (sortParam === 'scoreCard') {
                $rootScope.switchToOtherSort = false;
            }
            $scope.changeFilter(sortParam);
        }
    };

    $timeout(() => {
        Person.getUsers(function (resp) {
            let obj, person;
            $scope.persons = [];
            $rootScope.persons = [];
            $rootScope.personsNotChanged = [];
            $scope.associativePerson = resp.object;

            let me = {};

            let responsiblePersons = Object.entries(resp.object)
                .filter(([personId, person]) => {
                    if (person.userId === $rootScope.me.userId) {
                        me = angular.copy(person);
                        return false;
                    }

                    return person;
                })
                .map(([personId, person]) => person)
                .sort((a, b) => {
                    return b.cutFullName.toLowerCase() > a.cutFullName.toLowerCase() ? -1 : 1;
                });

            responsiblePersons.unshift(
                Object.assign(me, { fullName: `(${$filter('translate')('Me')}) ${me.fullName}` }),
            );

            angular.forEach($scope.associativePerson, function (val, key) {
                $scope.persons.push($scope.associativePerson[key]);
                $rootScope.persons.push($scope.associativePerson[key]);
                // $rootScope.personsNotChanged.push($scope.associativePerson[key]);
            });

            $scope.responsibleOptionsGrouped = responsiblePersons
                .filter((person) => person.status === 'A')
                .map((option) => ({ label: option.fullName, value: option }));
        });
    }, 0);

    $scope.onChangeResponsible = (newValue) => {
        $scope.newCandidatesResponsible = newValue;
        $scope.$$phase || $scope.$apply();
    };

    $scope.changeInputPage = function (params, searchNumber) {
        var searchNumber = Math.round(searchNumber);
        var maxValue = $filter('roundUp')(params.settings().total / params.count());
        if (searchNumber) {
            if (searchNumber >= 1 && searchNumber <= maxValue) {
                params.page(searchNumber);
                $scope.a.searchNumber = searchNumber;
            }
        }
    };

    $scope.clickExternalMenu = function () {
        $scope.showExternalMenu = !$scope.showExternalMenu;
        if (!$scope.source.openSettingsMenu) {
            $scope.source.openSettingsMenu = true;
            $localStorage.set('search_external', JSON.stringify($scope.source));
        }
    };

    $('.sortBy').click(function (e) {
        e.stopPropagation();
    });

    $scope.clickedUser = null;

    $('body').bind('click', function (event) {
        if ($scope.clickedUser && !$(event.target).hasClass('for-files')) {
            $scope.clickedUser = null;
            $scope.$apply();
        }
    });

    $scope.showUserFiles = function (user) {
        if ($scope.clickedUser !== user) {
            var clickedUserIndex = $scope.candidates.indexOf(user);
            $scope.clickedUser = $scope.candidates[clickedUserIndex];
        } else {
            $scope.clickedUser = null;
        }
    };

    $scope.inHover = function () {
        $scope.showRegionSearchInfoPop = true;
    };
    $scope.outHover = function () {
        $scope.showRegionSearchInfoPop = false;
    };

    $scope.cleanTags = function () {
        $scope.clear();
        $scope.clearTags();
        $rootScope.clickSearch(true);
    };

    $scope.toOneCandidate = function (candidate) {
        $location.path('candidates/' + candidate.localId);
    };
    $scope.toAdd = function () {
        Service.toAddCandidate();
    };
    $scope.toEdit = function (id) {
        if ($rootScope.me.recrutRole != 'client') {
            Service.toEditCandidate(id);
        } else {
            notificationService.error(
                $filter('translate')('Only recruiters, admins and freelancers can edit candidate info'),
            );
        }
    };
    $scope.toParseEmail = function () {
        $location.path('candidate/add/email');
    };
    $scope.toZip = function () {
        $location.path('candidate/add/zip');
    };

    $scope.toSentPreviewer = function (mailing) {
        Mailing.showSentCompaignById(mailing);
    };

    $scope.toScorecard = function (value, candidate) {
        let stateParams = {
            scoreCardId: value.scoreCardId,
            candidateObj: candidate,
            id: candidate.localId,
            isFromVacancyToCandidate: value.vacancyId,
            isFromVacancyToEvaluate: true,
            sliderDataId: $scope.sliderId,
            vacancyName: value.vacancy ? value.vacancy.position : null,
            vacancyId: value.vacancyId,
        };

        let link = $state.href('candidate-slide', stateParams, {
            reload: true,
        });
        window.open(link, '_blank');
    };

    /**
     * @param {import("common/types/candidate").CandidateType} candidate
     */
    $scope.deleteCandidate = (candidate) => {
        $rootScope.changeStateInCandidate.status = 'archived';
        if ($rootScope.useAmericanNameStyle) {
            $rootScope.changeStateInCandidate.fullName = candidate.fullNameEn;
        } else {
            $rootScope.changeStateInCandidate.fullName = candidate.fullName;
        }
        $rootScope.changeStateInCandidate.candidate = candidate;
        $rootScope.changeStateInCandidate.placeholder = $filter('translate')(
            'Write a comment why you want remove this candidate',
        );

        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/candidate-remove.html',
            size: '',
            scope: $scope,
        });

        $scope.modalInstance.closed.then(() => {
            if ($scope.deleteFromSystem) $scope.deleteFromSystem = false;
        });
    };

    $scope.deleteCandidateFromSystemModal = function (candidate) {
        if ($rootScope.useAmericanNameStyle) {
            $rootScope.changeStateInCandidate.fullName = candidate.fullNameEn;
        } else {
            $rootScope.changeStateInCandidate.fullName = candidate.fullName;
        }
        $rootScope.changeStateInCandidate.candidate = candidate;

        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/candidate-remove-from-system.html',
            size: '',
            scope: $scope,
            resolve: function () {},
        });
    };

    $scope.deleteCandidateFromSystem = function ({ id, comment }) {
        $rootScope.loading = true;
        Candidate.deleteCandidateFromSystem({
            candidateId: $rootScope.changeStateInCandidate.candidate
                ? $rootScope.changeStateInCandidate.candidate.candidateId
                : id,
            comment: $rootScope.changeStateInCandidate.comment || comment,
        }).then(
            (resp) => {
                $rootScope.loading = false;
                $scope.tableParams.reload();
                $scope.closeModal();
                $rootScope.changeStateInCandidate.comment = '';
                if ($rootScope.useAmericanNameStyle) {
                    notificationService.success(
                        $filter('translate')('Candidate name has been removed from the database', {
                            name: $rootScope.changeStateInCandidate.candidate
                                ? $rootScope.changeStateInCandidate.candidate.fullNameEn
                                : $scope.selectedCandidatesName[0],
                        }),
                    );
                } else {
                    notificationService.success(
                        $filter('translate')('Candidate name has been removed from the database', {
                            name: $rootScope.changeStateInCandidate.candidate
                                ? $rootScope.changeStateInCandidate.candidate.fullName
                                : $scope.selectedCandidatesName[0],
                        }),
                    );
                }
                $scope.selectedCandidatesName = [];
                $scope.candidatesAddToVacancyIds = [];
            },
            (error) => {
                if (error.object && error.object.limit === 0) {
                    notificationService.error(
                        $filter('translate')(
                            'deleteLimit.You_have_reached_the_daily_limit_for_removing_candidates_You_can_continue_tomorrow',
                        ),
                    );
                } else if (error.message && error.message === 'blockedDeletingCandidates') {
                    $rootScope.showBlockDeletingCandidatesModal(error.object);
                } else if (error.status === 'delete_limit' && error.total === 0) {
                    notificationService.error(
                        $filter('translate')(
                            'deleteLimit.You_have_reached_the_daily_limit_for_removing_candidates_You_can_continue_tomorrow',
                        ),
                    );
                } else if (error.status === 'delete_limit' && error.total > 0) {
                    notificationService.error(
                        $filter('translate')('deleteLimit.You have reached the daily limit for deleting candidates') +
                            ' ' +
                            error.total +
                            ' ' +
                            $filter('translate')('deleteLimit.candidates'),
                    );
                } else {
                    notificationService.error(error.message);
                }
                $rootScope.loading = false;
                $scope.closeModal();
                $rootScope.changeStateInCandidate.comment = '';
            },
        );
    };

    $rootScope.saveStatusOfCandidate = function (deleteFromSystem) {
        if (deleteFromSystem) {
            $scope.deleteCandidateFromSystem({});
            return;
        }
        if ($rootScope.changeStateInCandidate.status != '') {
            Candidate.changeState(
                {
                    candidateId: $rootScope.changeStateInCandidate.candidate.candidateId,
                    comment: $rootScope.changeStateInCandidate.comment,
                    candidateState: $rootScope.changeStateInCandidate.status,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        $rootScope.changeStateInCandidate.candidate.status = resp.object.status;

                        const userName = $rootScope.useAmericanNameStyle
                            ? $rootScope.changeStateInCandidate.candidate.fullNameEn
                            : $rootScope.changeStateInCandidate.candidate.fullName;

                        notificationService.success(
                            $filter('translate')('candidate') +
                                ' ' +
                                userName +
                                ' ' +
                                $filter('translate')('was_deleted'),
                        );
                        if ($scope.candidates.length == 1 && $scope.a.searchNumber > 1) {
                            $scope.tableParams.page($scope.a.searchNumber - 1);
                        } else {
                            $scope.tableParams.reload();
                        }
                    } else if (resp.status === 'delete_limit' && resp.total === 0) {
                        notificationService.error(
                            $filter('translate')(
                                'deleteLimit.You_have_reached_the_daily_limit_for_removing_candidates_You_can_continue_tomorrow',
                            ),
                        );
                    } else if (
                        resp.status === 'error' &&
                        resp.message &&
                        resp.message === 'blockedDeletingCandidates'
                    ) {
                        $rootScope.showBlockDeletingCandidatesModal(resp.object);
                    } else {
                        notificationService.error(resp.message);
                    }
                },
            );
            $rootScope.closeModal();
            $rootScope.changeStateInCandidate.status = '';
            $rootScope.changeStateInCandidate.comment = '';
            $rootScope.deleteCandidateFromSystem = false;
        }
    };

    $rootScope.addFromGmail = function () {
        googleService.gmailAuth('readonly', function (result) {
            if (result.status == 'ok') {
                $('.addEmailToParseQueue').modal('hide');
                Candidate.addToParserQueue(
                    {
                        email: result.email,
                        password: result.code,
                        host: 'gmail',
                    },
                    function (resp) {
                        if (resp && resp.status == 'ok') {
                        } else if (resp.message) {
                            notificationService.error(resp.message);
                        }
                    },
                    function (resp) {},
                );
            }
        });
    };
    $scope.ageEmptyFieldAdd = function (whatAge) {
        if (whatAge == 'ageFrom') {
            if ($scope.ageSearchFrom.indexOf(null) == -1) {
                $scope.ageSearchFrom.unshift(null);
            }
        } else {
            if ($scope.ageSearchTo.indexOf(null) == -1) {
                $scope.ageSearchTo.unshift(null);
            }
        }
    };

    if ($rootScope.me.personParams.enableEmployee == 'Y' && $rootScope.me.orgParams.enableEmployee == 'Y')
        $scope.status = Candidate.getStatus();
    else $scope.status = Candidate.getStatus().filter((item) => item.value !== 'employed');

    $scope.statusFilter = $scope.status.map((item) => {
        return { text: item.value };
    });

    // $scope.updateLinkHistorySearch = function () {
    //     Candidate.getSearchHistoryUniqueLink(function (resp) {
    //         var mas = {};
    //         var today = new Date();
    //         var yesterday = new Date().setDate(today.getDate() - 1);
    //         angular.forEach(resp, function (r, i) {
    //             if (r.words || r.position || r.company || r.country) {
    //                 var d = new Date(r.dc);
    //                 var key;
    //                 if (
    //                     today.getFullYear() == d.getFullYear() &&
    //                     today.getMonth() == d.getMonth() &&
    //                     today.getDate() == d.getDate()
    //                 ) {
    //                     key = $filter('dateFormat')(r.dc) + 'today';
    //                 } else if (
    //                     today.getFullYear() == d.getFullYear() &&
    //                     today.getMonth() == d.getMonth() &&
    //                     today.getDate() - 1 == d.getDate()
    //                 ) {
    //                     key = $filter('dateFormat')(r.dc) + 'yesterday';
    //                 } else {
    //                     key = $filter('dateFormat')(r.dc);
    //                 }
    //                 if (mas[key]) {
    //                     mas[key].push(r);
    //                 } else {
    //                     mas[key] = [r];
    //                 }
    //             }
    //         });
    //         $scope.linkHistorySearch = mas;
    //         $scope.linkHistorySearchKey = Object.keys(mas).sort().reverse();
    //         $scope.emptyLinkHistory = JSON.stringify($scope.linkHistorySearch) == '{}';
    //     });
    // };
    // $scope.updateLinkHistorySearch();

    if ($rootScope.eventListenerPing) {
        document.removeEventListener('cleverstaffExtensionPong', $rootScope.eventListenerPing);
    }
    $rootScope.eventListenerPing = function (event) {
        $scope.extensionHas = true;
    };
    document.addEventListener('cleverstaffExtensionPong', $rootScope.eventListenerPing);
    document.dispatchEvent(new CustomEvent('cleverstaffExtensionPing'));

    $scope.getPluginDownloadingLink = Service.getPluginDownloadingLinkByCurrentLang;

    $scope.getBrowser = function () {
        if (navigator.saysWho.indexOf('Chrome') != -1) {
            return 'Chrome';
        } else if (navigator.saysWho.indexOf('Firefox') != -1) {
            return 'Firefox';
        } else {
            return $filter('translate')('browser');
        }
    };
    $scope.isGoodBrowser = function () {
        return $scope.getBrowser() === 'Chrome' || $scope.getBrowser() === 'Firefox';
    };

    navigator.saysWho = (function () {
        var ua = navigator.userAgent,
            tem,
            M = ua.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i) || [];
        if (/trident/i.test(M[1])) {
            tem = /\brv[ :]+(\d+)/g.exec(ua) || [];
            return 'IE ' + (tem[1] || '');
        }
        if (M[1] === 'Chrome') {
            tem = ua.match(/\bOPR\/(\d+)/);
            if (tem != null) return 'Opera ' + tem[1];
        }
        M = M[2] ? [M[1], M[2]] : [navigator.appName, navigator.appVersion, '-?'];
        if ((tem = ua.match(/version\/(\d+)/i)) != null) M.splice(1, 1, tem[1]);
        return M.join(' ');
    })();
    Candidate.ZIP($scope);

    $scope.tofullinformation = function (event, user) {
        if (event.ctrlKey) return false;
        user.openStatus = 'Y';
        $scope.setCandidatesSlider();
    };
    $scope.setCandidatesSlider = function () {
        const searchParams = Candidate.search.params;
        searchParams.page.count = Candidate.search.params.page.count;
        $rootScope.stageUrl = null;
        $scope.sliderId = CandidatesSlider.getSliderId();

        if (Candidate.search.customFieldsValues !== null || Candidate.search.source === 'advancedSource') {
            CandidatesSlider.setParamsForSlider(
                'advanced-search-candidates',
                $scope.candidates,
                $scope.objectSize,
                $scope.pagesCount,
                Object.assign(searchParams),
            );
            // Candidate.search.customFieldsValues = null;
            $rootScope.showSlider = true;
        } else if (Candidate.search.params.searchFullTextType === 'booleanSearch') {
            CandidatesSlider.setParamsForSlider(
                'search-candidates',
                $scope.candidates,
                $scope.objectSize,
                $scope.pagesCount,
                Object.assign(searchParams),
            );
            $rootScope.showSlider = true;
        } else if ($rootScope.redirectIds) {
            CandidatesSlider.setParamsForSlider(
                'candidates',
                $scope.candidates,
                $scope.objectSize,
                $scope.pagesCount,
                Object.assign(searchParams),
            );
            $rootScope.showSlider = true;
        } else {
            CandidatesSlider.setParamsForSlider(
                'candidates',
                $scope.candidates,
                $scope.objectSize,
                $scope.pagesCount,
                Object.assign(searchParams),
            );
            $rootScope.showSlider = false;
        }
        $scope.$$phase || $scope.$apply();
    };
    $scope.setSearchedRegion = function (region) {
        $scope.filteredCities = [];

        angular.forEach($scope.cities, function (nval) {
            if (nval.type == 'city' && (nval.country == region.country || nval.countryRu == region.country)) {
                $scope.filteredCities.push(nval);
            }
        });
        $scope.filteredCities.unshift({ showName: 'city' });
        $scope.search.fields.city.reset({});
    };

    if ($rootScope.changeSearchTypeNotFromCandidates) {
        $scope.changeSearchType($rootScope.changeSearchTypeNotFromCandidates);
        $rootScope.changeSearchTypeNotFromCandidates = null;
    }
    $scope.parJson = function (json) {
        return JSON.parse(json);
    };
    $rootScope.setDocCounter = function () {
        $scope.currentDocPreviewPage = 0;
    };
    $scope.prevDoc = function () {
        $scope.currentDocPreviewPage -= 1;
    };
    $scope.nextDoc = function () {
        $scope.currentDocPreviewPage += 1;
    };
    $scope.fileForSave = [];
    $rootScope.fileForSave = []; /*For modal window*/

    FileInit.initVacancyTemplateInCandidateFileOption($scope, $rootScope, '', '', false, $filter);
    $scope.callbackFileTemplateInCandidate = function (resp, names) {
        $scope.fileForSave.push({
            fileId: resp,
            fileName: names,
            fileResolution: Service.getFileResolutionFromName(names),
        });
        $rootScope.fileForSave.push({
            fileId: resp,
            fileName: names,
            fileResolution: Service.getFileResolutionFromName(names),
        });
    };
    $scope.removeFile = function (id) {
        angular.forEach($scope.fileForSave, function (val, ind) {
            if (val.attId === id) {
                $scope.fileForSave.splice(ind, 1);
            }
        });
    };
    $rootScope.removeFile = function (id) {
        angular.forEach($rootScope.fileForSave, function (val, ind) {
            if (val.attId === id) {
                $rootScope.fileForSave.splice(ind, 1);
            }
        });
    };
    $scope.showCandidateFiles = function (id) {
        $scope.candidateIdFiles = id;
        $('body').mouseup(function (e) {
            if ($('.saveCandidateFile').has(e.target).length === 0) {
                $scope.candidateIdFiles = null;
                $scope.$apply();
            }
        });
    };

    $scope.showAddResumeFromText = function () {
        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/add-resume-from-text.html',
            size: 'lg',
            resolve: function () {},
        });
    };

    $scope.onAddCandidateClick = () => {
        $location.path('candidate/add');
        $rootScope.$$phase || $scope.$apply();
    };

    $rootScope.checkTextForResume = function () {
        $rootScope.loading = true;
        Candidate.fromText(
            { text: $rootScope.fastCandResumeText },
            function (res) {
                if (angular.equals(res.status, 'ok')) {
                    $rootScope.resumeFromText = res.object;
                    $rootScope.loading = false;
                    $location.path('candidate/add');
                    $rootScope.closeModal();
                } else if (angular.equals(res.status, 'error')) {
                    notificationService.error(res.message);
                }
                $scope.fastCandLoading = false;
            },
            function (error) {
                console.error(error);
            },
        );
    };

    $scope.showAddResumeFromLink = function () {
        $scope.onChangeFastLink = (newValue) => {
            $rootScope.fastCandResumeLinkSite = newValue;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/add-resume-from-link.html',
            size: 'lg',
            scope: $scope,
            resolve: function () {},
        });
    };

    $rootScope.checkTextForLink = function () {
        $rootScope.closeModal();
        $rootScope.descrFlag = true;
        CandidateResumeFromLink.fromLinkSite($scope, $rootScope.fastCandResumeLinkSite);
    };

    $scope.getFirstLetters = function (string) {
        return Service.firstLetters(string);
    };

    $scope.checkLink = (text) => {
        const validateUrl = /(http|https|www):\/\/([\w.]+\/?)\S*/;

        if (validateUrl.test(text)) {
            return `<a target='_blank' href=${text}>${text}</a>`;
        } else {
            return text;
        }
    };

    $scope.openCandidatesChangeModal = function (param) {
        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: `../partials/modal/candidates-change-${param}.html`,
            scope: $scope,
        });
    };

    $scope.candidatesChangeOriginModel = (newValue) => {
        $scope.newCandidatesOrigin = newValue;
        $rootScope.$$phase || $scope.$apply();
    };

    $scope.candidatesChangeOrigin = () => {
        handleCandidatesChange(
            Candidate.onMassChangeOrigin,
            {
                candidateIds: $scope.candidatesAddToVacancyIds,
                origin: $scope.newCandidatesOrigin.value,
            },
            () => {
                $scope.newCandidatesOrigin = null;
            },
            "The candidate's source has changed",
        );
    };

    $scope.candidatesChangeResponsible = function () {
        handleCandidatesChange(
            Candidate.onMassChangeResponsibles,
            {
                candidateIds: $scope.candidatesAddToVacancyIds,
                userId: $scope.newCandidatesResponsible.value.userId,
            },
            () => ($scope.newCandidatesResponsible = null),
            'Responsible changed',
        );
    };

    $scope.candidatesChangeDesiredPosition = function (position) {
        handleCandidatesChange(
            Candidate.onMassChangePosition,
            {
                candidateIds: $scope.candidatesAddToVacancyIds,
                position: position.value,
            },
            () => ($scope.newCandidatesDesiredPosition = null),
            'The desired position has changed',
        );
    };

    function handleCandidatesChange(action, payload, resetField, notificationText) {
        $rootScope.loading = true;
        action(payload)
            .then(() => {
                resetField();
                $scope.candidatesAddToVacancyIds = [];
                $scope.selectedCandidatesName = [];
                $scope.addCandidateChangeStage = [];
                $scope.checkAllCandidates = false;
                _unselectAllCandidates();
                $rootScope.closeModal();
                $scope.tableParams.reload();
                notificationService.success($filter('translate')(notificationText));
            })
            .catch((error) => console.error(error.message || error.statusText))
            .finally(() => {
                $rootScope.loading = false;
                $rootScope.$$phase || $scope.$apply();
            });
    }

    $scope.changeCandidateAdded = function (event, candidate) {
        if (event.target.tagName === 'A') return;
        candidate.added = !candidate.added;
    };

    const _unselectAllCandidates = () => {
        $scope.candidates = $scope.candidates.map((item) => ({ ...item, added: false }));
    };

    $scope.pushCandidateToVacancy = (event, candidate, isPushAll = false) => {
        candidate.added = event.target.checked;

        $scope.candidates = $scope.candidates.map((item) => {
            if (item.candidateId === candidate.candidateId) return { ...item, added: event.target.checked };
            return item;
        });

        if (candidate.added) {
            if (!$scope.candidatesAddToVacancyIds.some((cId) => cId === candidate.candidateId)) {
                $scope.candidatesAddToVacancyIds.push(candidate.candidateId);
                if ($rootScope.useAmericanNameStyle) {
                    $scope.selectedCandidatesName.push(candidate.fullNameEn);
                } else {
                    $scope.selectedCandidatesName.push(candidate.fullName);
                }
                $scope.addCandidateChangeStage.push(candidate);

                $scope.candidatesAddToVacancyIds.forEach((cId) => {
                    if (candidate.candidateId === cId) candidate.added = true;
                });
            }
        } else {
            $scope.candidatesAddToVacancyIds.splice($scope.candidatesAddToVacancyIds.indexOf(candidate.candidateId), 1);
            $scope.addCandidateChangeStage.splice($scope.addCandidateChangeStage.indexOf(candidate), 1);
            if ($rootScope.useAmericanNameStyle) {
                $scope.selectedCandidatesName.splice($scope.selectedCandidatesName.indexOf(candidate.fullNameEn), 1);
            } else {
                $scope.selectedCandidatesName.splice($scope.selectedCandidatesName.indexOf(candidate.fullName), 1);
            }
        }

        $rootScope.$$phase || $scope.$apply();
    };

    $scope.pushAllCandidatesToVacancy = (event) => {
        $scope.checkAllCandidates = !$scope.checkAllCandidates;
        angular.forEach($scope.candidates, function (resp) {
            if ($scope.checkAllCandidates) {
                resp.added = true;
            } else {
                resp.added = false;
            }
            $scope.pushCandidateToVacancy(event, resp, true);
        });

        $rootScope.$$phase || $scope.$apply();
    };

    $scope.showAddCandidatesInVacancy = function () {
        $scope.isPresent = false;
        $scope.isAutoActionForStage = true;
        $scope.emailAutoActionForStage = false;
        $scope.testAutoActionForStage = false;

        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/candidates-add-in-vacancy.html',
            scope: $scope,
            size: '',
            resolve: function () {},
        });

        $scope.modalInstance.closed.then(() => {
            $scope.selectedVacancy = null;
        });
    };

    const _fetchAllVacancies = $rootScope.makeDebounce((inputText, callback) => {
        if (inputText?.length && inputText?.length < $scope.inputSearchThreshold) return callback();

        const fetchOnlyMyVacancies =
            ScopeService.getActiveScopeObject().name === 'onlyMy' && ScopeService.getActiveScopeObject().check;

        Vacancy.onGetAllVacancies({
            position: inputText.trim(),
            showCandidate: false,
            ...(fetchOnlyMyVacancies && { responsibleId: $rootScope.userId }),
        }).then((resp) => {
            if (resp.status !== 'ok') return;
            callback($rootScope.addClientAndLocationToVacancyLabels(resp.objects));
        });
    }, 300);

    $scope.getAllVacancies = (inputText, callback) => {
        if (inputText?.length && inputText?.length < $scope.inputSearchThreshold) return callback();
        _fetchAllVacancies(inputText, callback);
    };

    $rootScope.addCandidatesToVacancy = function ({ vacancyId }) {
        if (vacancyId && $scope.candidatesAddToVacancyIds.length) {
            $rootScope.loading = true;
            Vacancy.setInterviewList(
                {
                    candidateIds: $scope.candidatesAddToVacancyIds,
                    vacancyId,
                    interviewState: 'longlist',
                    status: 'in_process',
                    interviewSource: $rootScope.searchFromAdvice
                        ? 'searchFromAdvice'
                        : $rootScope.actualVacancy
                        ? 'actualVacancy'
                        : undefined,
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        $scope.checkAllCandidates = false;

                        if (!resp.objects) {
                            if ($scope.candidatesAddToVacancyIds.length === 1) {
                                notificationService.error(
                                    $filter('translate')('Candidate has been added to this position'),
                                );
                            } else if ($scope.candidatesAddToVacancyIds.length > 1) {
                                notificationService.error(
                                    $filter('translate')('Candidates has been added to this position'),
                                );
                            }
                            $rootScope.loading = false;
                            return;
                        }

                        $rootScope.closeModal();
                        if ($scope.candidatesAddToVacancyIds.length == 1) {
                            notificationService.success($filter('translate')('Candidate added in vacancy'));
                        } else if ($scope.candidatesAddToVacancyIds.length > 1) {
                            notificationService.success($filter('translate')('Candidates added in vacancy'));
                        }
                        $scope.candidatesAddToVacancyIds = [];
                        $scope.selectedCandidatesName = [];
                        $scope.addCandidateChangeStage = [];
                        _unselectAllCandidates();
                        $rootScope.loading = false;
                    } else {
                        $rootScope.loading = false;
                        notificationService.error(resp.message);
                    }
                },
            );
        } else {
            notificationService.error($translate.instant('Select a vacancy'));
            $rootScope.loading = false;
        }
    };

    $scope.deleteCandidates = function (deleteFromSystem, comment) {
        if ($scope.candidatesAddToVacancyIds.length > 120) {
            notificationService.error($filter('translate')('You can select up to 120 candidates'));
            return;
        }
        if (deleteFromSystem && $scope.candidatesAddToVacancyIds.length === 1) {
            $scope.deleteCandidateFromSystem({
                id: $scope.candidatesAddToVacancyIds[0],
                comment: comment,
            });
        } else if (deleteFromSystem) {
            deleteCandidatesFromSystem(comment);
        } else {
            deleteCandidates(comment);
        }
    };

    function deleteCandidatesFromSystem(comment) {
        $rootScope.loading = true;
        Candidate.deleteCandidateFromSystem({
            ids: $scope.candidatesAddToVacancyIds,
            comment: comment,
        }).then(
            (resp) => {
                $scope.tableParams.reload();
                $rootScope.loading = false;
                if ($scope.candidatesAddToVacancyIds.length === 1) {
                    notificationService.success($filter('translate')('Candidate has been removed from the database'));
                } else {
                    notificationService.success(
                        $filter('translate')('Candidates amount has been removed from the database'),
                    );
                }
                $scope.candidatesAddToVacancyIds = [];
                $scope.selectedCandidatesName = [];
                $rootScope.closeModal();
            },
            (error) => {
                if (error.object && error.object.limit === 0) {
                    notificationService.error(
                        $filter('translate')(
                            'deleteLimit.You_have_reached_the_daily_limit_for_removing_candidates_You_can_continue_tomorrow',
                        ),
                    );
                } else if (error.message && error.message === 'blockedDeletingCandidates') {
                    $rootScope.showBlockDeletingCandidatesModal(error.object);
                } else if (error.status === 'delete_limit' && error.total === 0) {
                    notificationService.error(
                        $filter('translate')(
                            'deleteLimit.You_have_reached_the_daily_limit_for_removing_candidates_You_can_continue_tomorrow',
                        ),
                    );
                } else if (error.status === 'delete_limit' && error.total > 0) {
                    notificationService.error(
                        $filter('translate')('deleteLimit.You have reached the daily limit for deleting candidates') +
                            ' ' +
                            error.total +
                            ' ' +
                            $filter('translate')('deleteLimit.candidates'),
                    );
                } else {
                    notificationService.error(error.message);
                }
                $rootScope.closeModal();
                $rootScope.loading = false;
            },
        );
    }

    function deleteCandidates(comment) {
        $rootScope.loading = true;
        Candidate.deleteCandidates({
            ids: $scope.candidatesAddToVacancyIds,
            comment: comment,
            candidateState: 'archived',
        })
            .then((resp) => {
                if ($scope.candidatesAddToVacancyIds.length === 1) {
                    notificationService.success($filter('translate')('Candidate removed'));
                } else {
                    notificationService.success($filter('translate')('Candidates were deleted'));
                }
                $scope.candidatesAddToVacancyIds = [];
                $scope.selectedCandidatesName = [];

                $rootScope.closeModal();
                $scope.tableParams.reload();
            })
            .catch((error) => {
                if (error.status === 'delete_limit' && error.total === 0) {
                    notificationService.error(
                        $filter('translate')(
                            'deleteLimit.You_have_reached_the_daily_limit_for_removing_candidates_You_can_continue_tomorrow',
                        ),
                    );
                } else if (error.status === 'error' && error.message && error.message === 'blockedDeletingCandidates') {
                    $rootScope.showBlockDeletingCandidatesModal(error.object);
                } else if (error.status === 'delete_limit' && error.total > 0) {
                    notificationService.error(
                        $filter('translate')('deleteLimit.You have reached the daily limit for deleting candidates') +
                            ' ' +
                            error.total +
                            ' ' +
                            $filter('translate')('deleteLimit.candidates'),
                    );
                } else {
                    notificationService.error(error.message);
                }
                $rootScope.closeModal();
                $rootScope.loading = false;
            })
            .finally(() => {
                $rootScope.loading = false;
                $rootScope.$$phase || $scope.$apply();
            });
    }

    $scope.getStageTemplate = function ({ vacancyId }) {
        $scope.isPresent = false;
        $scope.isAutoActionForStage = true;
        $scope.emailAutoActionForStage = false;
        $scope.testAutoActionForStage = false;

        const isPresentReq = {
            method: 'GET',
            url: `/hr/autoaction/isPresent?vacancyId=${vacancyId}&stage=longlist`,
        };

        $http(isPresentReq).then(function (resp) {
            if (resp.data.object === true) $scope.isPresent = true;
        });

        const req = {
            method: 'POST',
            url: `/hr/autoaction/getByStage`,
            data: {
                vacancyId,
                customStateId: 'longlist',
            },
        };
        $http(req).then(function (resp) {
            $scope.isAutoActionForStage = false;
            if (resp.data.status === 'ok') {
                resp.data.objects.forEach((item) => {
                    if (item.type === 'sendMail') {
                        $scope.emailAutoActionForStage = item;
                    }
                    if (item.type === 'sendTest') {
                        $scope.testAutoActionForStage = item;
                    }
                });
            }
        });
    };

    $scope.onChangeVacancy = (newValue) => {
        $scope.selectedVacancy = newValue;
        $scope.getStageTemplate($scope.selectedVacancy);
        $rootScope.$$phase || $scope.$apply();
    };

    const _fetchPositionsOptions = $rootScope.makeDebounce((text, callback) => {
        fetch('hr/candidate/autocompletePosition', {
            method: 'POST',
            body: $rootScope._objectToFormData({ text }),
        })
            .then((resp) => resp.json())
            .then((data) => {
                if (data.status !== 'ok') {
                    notificationService.error(data.message);
                }

                callback([...data.objects.map((option) => ({ label: option, value: option }))]);
            });
    }, 700);

    $scope.getPositionsOptions = (text, callback) => {
        if (!text || (text?.length && text?.length < $scope.inputSearchThreshold)) return callback();

        _fetchPositionsOptions(text, callback);
    };

    $scope.onChangePosition = (newValue) => {
        $scope.newCandidatesDesiredPosition = newValue;
        $rootScope.$$phase || $scope.$apply();
    };

    $scope.deleteCandidatesModal = function () {
        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/delete-candidates.html',
            size: '',
            scope: $scope,
            resolve: function () {},
        });

        $scope.modalInstance.closed.then(() => {
            if ($scope.deleteFromSystem) $scope.deleteFromSystem = false;
        });
    };

    $scope.onChangeDeleteFromSystem = (event) => {
        $scope.deleteFromSystem = event.target.checked;
        $rootScope.$$phase || $scope.$apply();
    };

    $scope.openGdprModal = function () {
        if ($rootScope.isAccountTrial && !$rootScope.me.emails.length) {
            notificationService.error($filter('translate')('block-autotests'));
            return;
        }

        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/gdpr-confirm.html',
            size: '',
            scope: $scope,
            resolve: function () {},
        });

        $rootScope.sendMassGdpr = () => {
            $rootScope.loading = true;

            Gdpr.sendMassGdprReq({ candidateIds: $rootScope.candidatesAddToVacancyIds }).then(() => {
                notificationService.success($filter('translate')('Withdraw request successful title'));
                $scope.candidatesAddToVacancyIds = [];
                $scope.checkAllCandidates = false;
                angular.forEach($scope.tableParams.data, function (candidate) {
                    candidate.added = false;
                });
                $rootScope.closeModal();
                $scope.tableParams.reload();
                $rootScope.loading = false;
            });
        };
    };

    $scope.getTags = function (tagsDropdown) {
        $scope.tagPage = $scope.tagPage + 1;
        Service.requestGetCompanyGroups()
            .then((resp) => {
                $scope.candidateGroupsOptions = resp.objects.map((option) => ({
                    ...option,
                    label: option.name,
                    value: option.candidateGroupId,
                }));

                $rootScope.$$phase || $scope.$apply();
            })
            .catch((resp) => console.error(resp));
    };

    $scope.showTagsForMassModal = function () {
        $scope.getTags();
        $scope.tagsModel = [];
        $rootScope.candidatesAddToVacancyIds = $scope.candidatesAddToVacancyIds;

        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/candidates-add-tags-multiple.html',
            size: '',
            scope: $scope,
            resolve: function () {},
        });
    };

    $scope.onChangeTags = (newValue) => {
        $scope.tagsModel = newValue;
        $rootScope.$$phase || $scope.$apply();
    };

    function initUsersTags() {
        $scope.users = [];
        $scope.repsonsibleUsers = [];

        $rootScope.persons.forEach((i, index) => {
            $rootScope.addCandidateChangeStage.forEach((select) => {
                if (i.userId === select.responsibleId && i.recrutRole !== 'client' && i.recrutRole !== 'researcher') {
                    $scope.repsonsibleUsers.push({
                        id: index + 1,
                        fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                        userId: i.userId,
                        responsible: true,
                    });
                }
            });

            if (
                i.status === 'A' &&
                i.recrutRole !== 'client' &&
                i.recrutRole !== 'freelancer' &&
                i.recrutRole !== 'researcher'
            ) {
                $scope.users.push({
                    id: index + 1,
                    fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                    userId: i.userId,
                    responsible: false,
                });
            }
        });
        $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));
    }

    function deleteFromTags(id, type) {
        $scope.users.filter((user, index) => {
            if (user[type] == id) {
                $scope.users.splice(index, 1);
            }
        });
        $scope.repsonsibleUsers.forEach((user, index) => {
            if (user.userId == id) {
                $scope.repsonsibleUsers.splice(index, 1);
            }
        });
    }

    $scope.showModalAddCommentToCandidate = function () {
        $rootScope.addCandidateChangeStage = $scope.addCandidateChangeStage;
        $rootScope.candidatesAddToVacancyIds = $scope.candidatesAddToVacancyIds;
        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/add-comment-candidate.html',
            scope: $scope,
            size: '',
            resolve: {},
        });

        initUsersTags();

        $rootScope.ckEditorOneCandidate = {
            height: 140,
            autoGrow_maxHeight: 340,
            toolbar: [],
            enterMode: CKEDITOR.ENTER_BR,
            shiftEnterMode: CKEDITOR.ENTER_BR,
            extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
            removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
            editorplaceholder: $translate.instant('mention placeholder'),
            mentions: [
                {
                    feed: dataCallback,
                    minChars: 0,
                    itemTemplate: '<li style="padding: 10px" data-id="{id}">{fullname}</li>',
                    outputTemplate: `<a href="#" style="text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;">@{fullname}</a>&nbsp`,
                    marker: '@',
                    pattern: /@([\p{L}\p{M}]*(?:\s[\p{L}\p{M}]*)?)?$/u,
                },
            ],
            on: {
                paste: function (ev) {
                    ev.data.dataValue = '';
                    CKEDITOR.instances['ckEditorOneCandidate'].insertText(ev.data.dataTransfer._.data.Text);
                    setTimeout(() => {
                        let bodyHeight = ev.editor.document.$.children[0].offsetHeight;
                        if (bodyHeight > ev.editor.config.height) {
                            if (bodyHeight < 605) {
                                ev.editor.resize(null, bodyHeight);
                            }
                        }
                    }, 0);
                },
                afterInsertHtml: function (ev) {
                    let user = ev.editor.data;
                    if (user) {
                        deleteFromTags(user.userId, 'userId');
                        $scope.deletedUsers.push(user);
                    }
                },
                instanceReady: function (ev) {
                    $scope.deletedUsers = [];
                    $scope.afterDelete = [];

                    ev.editor.dataProcessor.writer.setRules('p', {
                        indent: false,
                        breakBeforeOpen: false,
                        breakAfterOpen: false,
                        breakBeforeClose: false,
                        breakAfterClose: false,
                    });
                    ev.editor.dataProcessor.writer.setRules('br', {
                        indent: false,
                        breakBeforeOpen: false,
                        breakAfterOpen: false,
                        breakBeforeClose: false,
                        breakAfterClose: false,
                    });
                },
            },
        };

        function dataCallback(opts, callback) {
            opts.query = opts.query.replace(/\u00A0/g, ' ');
            let text = CKEDITOR.instances['ckEditorOneCandidate'].getData();

            $scope.deletedUsers.forEach((user, index) => {
                if (!text.includes(`${user.fullname}`)) {
                    if (user.responsible) {
                        $scope.repsonsibleUsers.push(user);
                    } else {
                        $scope.users.push(user);
                    }
                    $scope.afterDelete.push(user.id);

                    $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));
                }
            });

            $scope.afterDelete.forEach((id) => {
                $scope.deletedUsers = $scope.deletedUsers.filter((user) => user.id !== id);
            });
            $scope.afterDelete = [];

            $scope.repsonsibleUsers = $scope.repsonsibleUsers.filter((thing, index) => {
                return (
                    index ===
                    $scope.repsonsibleUsers.findIndex((obj) => {
                        return JSON.stringify(obj) === JSON.stringify(thing);
                    })
                );
            });

            let showAll =
                $scope.repsonsibleUsers.length === 1 && $scope.repsonsibleUsers[0].userId === $rootScope.me.userId;

            if (opts.query.length === 0 && $scope.repsonsibleUsers.length > 0 && !showAll) {
                setTimeout(function () {
                    callback(
                        $scope.repsonsibleUsers.filter(function (item) {
                            return item.fullname;
                        }),
                    );
                }, 0);
            } else {
                setTimeout(function () {
                    callback(
                        $scope.users.filter(function (item) {
                            return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                        }),
                    );
                }, 0);
            }
        }
        $(document)
            .unbind('keydown')
            .keydown(function (e) {
                if (e.ctrlKey === true && e.which === 13) {
                    $rootScope.addCommentInCandidate();
                }
            });
    };

    $scope.toCreateSms = function (disabled = false) {
        if (disabled) {
            return;
        }
        if ($rootScope.me.orgParams.alphaSms !== 'Y') {
            $location.path('/integration-page');
            return;
        }
        if ($scope.candidatesAddToVacancyIds.length === 0) {
            notificationService.error($filter('translate')('Choose Candidates'));
            return;
        }
        const candidate = $scope.addCandidateChangeStage[0];

        $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/create-sms-modal.html',
            windowClass: 'create-sms-modal-wrapper',
            size: '',
            controller: 'createSms',
            controllerAs: 'vm',
            resolve: {
                candidate: () => candidate || null,
                candidates: () => ($scope.addCandidateChangeStage.length > 1 ? $scope.addCandidateChangeStage : null),
                answerData: () => null,
                vacancy: () => null,
                client: () => null,
                stages: () => null,
                currentStage: () => null,
                fetchVacancies: () => $rootScope.getAllVacancies,
                responsibleOptionsGrouped: () => $scope.responsibleOptionsGrouped.map((resp) => resp.value),
            },
        });
    };

    $scope.toCreateMailing = function (disabled = false) {
        if (disabled) {
            return;
        }
        if ($scope.candidatesAddToVacancyIds.length === 0) {
            notificationService.error($filter('translate')('Choose Candidates'));
            return;
        }
        $rootScope.loading = true;
        if ($scope.candidatesAddToVacancyIds.length === 1) {
            Person.isIntegratedEmail().then(
                (resp) => {
                    if (resp.object.emails.some((email) => email.personalMailing)) {
                        const candidate = $scope.addCandidateChangeStage[0];
                        $scope.emailIsEnabled = true;
                        $uibModal.open({
                            animation: true,
                            templateUrl: '../partials/modal/create-letter-personal-mailing-modal.html',
                            windowClass: 'create-letter-personal-mailing-modal-wrapper',
                            size: '',
                            controller: 'createLetterForPersonalMailing',
                            controllerAs: 'vm',
                            resolve: {
                                candidate: () => candidate,
                                candidates: () => null,
                                answerData: () => null,
                                vacancy: () => null,
                                client: () => null,
                                stages: () => null,
                                currentStage: () => null,
                            },
                        });
                        $rootScope.loading = false;
                    } else {
                        $scope.emailIsEnabled = false;
                        onRejected();
                    }
                },
                (resp) => {
                    if (resp) {
                        $scope.noOneEmailIntegrated = true;
                        onRejected();
                    } else {
                        $rootScope.loading = false;
                    }
                },
            );
        } else if ($scope.candidatesAddToVacancyIds.length > 1) {
            Person.isIntegratedEmail().then(
                (resp) => {
                    if (resp.object.emails.some((email) => email.personalMailing)) {
                        const candidate = $scope.addCandidateChangeStage[0];
                        const candidates = $scope.addCandidateChangeStage;
                        $scope.emailIsEnabled = true;
                        $uibModal.open({
                            animation: true,
                            templateUrl: '../partials/modal/create-letter-personal-mailing-modal.html',
                            windowClass: 'create-letter-personal-mailing-modal-wrapper',
                            size: '',
                            controller: 'createLetterForPersonalMailing',
                            controllerAs: 'vm',
                            resolve: {
                                candidate: () => candidate || null,
                                candidates: () => candidates,
                                answerData: () => null,
                                vacancy: () => null,
                                client: () => null,
                                stages: () => null,
                                currentStage: () => null,
                            },
                        });
                        $rootScope.loading = false;
                    } else {
                        $scope.emailIsEnabled = false;
                        onRejected();
                    }
                },
                (resp) => {
                    if (resp) {
                        $scope.noOneEmailIntegrated = true;
                        onRejected();
                    } else {
                        $rootScope.loading = false;
                    }
                },
            );
        }

        function onRejected() {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/not-allowed-mailing.html',
                controller: ['$uibModalInstance', 'emailIsEnabled', 'noOneEmailIntegrated', notAllowedMailing],
                controllerAs: 'vm',
                windowClass: 'corporate-email-not-integrated-modal secondary-modal',
                size: '',
                resolve: {
                    emailIsEnabled: function () {
                        return $scope.emailIsEnabled;
                    },
                    noOneEmailIntegrated: function () {
                        return $scope.noOneEmailIntegrated;
                    },
                },
            });
            $scope.modalInstance.result.then(
                function () {},
                function () {},
            );
            $rootScope.loading = false;
        }
    };
    $rootScope.commentCandidate = {
        comment: '',
    };
    $rootScope.addCommentInCandidate = function () {
        let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorOneCandidate.getData());
        $rootScope.commentCandidate.comment = text.replaceAll('<br />', ' &nbsp<br>&nbsp ');
        initUsersTags();
        if ($rootScope.commentCandidate.comment != undefined && $rootScope.commentCandidate.comment.length > 0) {
            Candidate.setMessage(
                {
                    comment: $rootScope.commentCandidate.comment,
                    candidateIds: $scope.candidatesAddToVacancyIds,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        $scope.tableParams.reload();
                        $rootScope.closeModal();
                        $rootScope.commentCandidate.comment = null;
                        angular.forEach($scope.addCandidateChangeStage, function (val) {
                            val.added = false;
                        });
                        $scope.checkAllCandidates = false;
                        $scope.addCandidateChangeStage = [];
                        $scope.candidatesAddToVacancyIds = [];
                        notificationService.success($filter('translate')('Comment added'));
                    }
                },
                function (error) {
                    notificationService.error(error.message);
                },
            );
        } else {
            notificationService.error($filter('translate')('enter a comment'));
        }
    };
    $scope.editOriginName = function () {
        $scope.originOldName = $scope.getOriginAutocompleterValue();
        $rootScope.originName = $scope.originOldName;
        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/origin-name-edit.html',
            size: '',
            resolve: {},
        });
    };
    $rootScope.saveOriginName = function () {
        Candidate.editOriginAll(
            {
                originOld: $scope.originOldName,
                originNew: $rootScope.originName,
            },
            function (resp) {
                if (resp.status == 'ok') {
                    notificationService.success($filter('translate')('Origin_name_saved'));
                }
            },
        );
        $scope.setOriginAutocompleterValue($rootScope.originName);
        $rootScope.closeModal();
    };
    $scope.editTagName = function (tagObject) {
        $rootScope.tagForEdit = {};
        var tagSelected = false;
        $rootScope.tagForEdit.name = $(tagObject).parent().children().first().html();
        $scope.oldTagName = $rootScope.tagForEdit.name;
        angular.forEach($scope.groupsForEdit, function (group) {
            if (group.name == $rootScope.tagForEdit.name && !tagSelected) {
                tagSelected = true;
                $rootScope.tagForEdit.id = group.candidateGroupId;
                $scope.modalInstance = $uibModal.open({
                    animation: true,
                    templateUrl: '../partials/modal/tag-name-edit.html',
                    size: '',
                    resolve: {},
                });
            }
        });
    };
    $rootScope.saveEditTagName = function () {
        var oldId = $rootScope.tagForEdit.id;
        CandidateGroup.editGroup(
            {
                candidateGroupId: $rootScope.tagForEdit.id,
                name: $rootScope.tagForEdit.name,
            },
            function (resp) {
                if (resp.status == 'ok') {
                    notificationService.success($filter('translate')('Tag_name_saved'));
                    $('.select2-search-choice').each(function () {
                        if ($(this).children().first().html() == $scope.oldTagName) {
                            $(this).children().first().text($rootScope.tagForEdit.name);
                        }
                        angular.forEach($scope.groupIdsForSearch, function (tagId, tagIndex) {
                            if (tagId == oldId) {
                                $scope.groupIdsForSearch[tagIndex] = resp.object.candidateGroupId;
                            }
                        });
                        angular.forEach($scope.groupsForEdit, function (group) {
                            if (group.name == $scope.oldTagName) {
                                group.name = $rootScope.tagForEdit.name;
                                group.candidateGroupId = resp.object.candidateGroupId;
                            }
                        });
                        angular.forEach($scope.candidateGroups, function (group) {
                            if (group.name == $scope.oldTagName) {
                                group.name = $rootScope.tagForEdit.name;
                                group.candidateGroupId = resp.object.candidateGroupId;
                            }
                        });
                    });
                } else {
                    notificationService.error();
                }
            },
        );
        $rootScope.closeModal();
    };

    $rootScope.addTagsForMass = function () {
        if ($scope.tagsModel.length === 0) {
            notificationService.error($filter('translate')('Select tags'));
            return;
        }
        let tagIds = $scope.tagsModel.map((tag) => tag.candidateGroupId);
        $rootScope.loading = true;

        CandidateGroup.addList(
            {
                ids: tagIds,
                candidatesIds: $scope.candidatesAddToVacancyIds,
            },
            function (res) {
                $rootScope.loading = false;
                if (res.status === 'ok') {
                    $scope.candidatesAddToVacancyIds = [];
                    _unselectAllCandidates();
                    $scope.checkAllCandidates = false;
                    angular.forEach($scope.tableParams.data, function (candidate) {
                        candidate.added = false;
                    });
                    notificationService.success($filter('translate')('Tags added'));
                    $rootScope.closeModal();
                    $scope.tagsModel = [];
                    updateCandidateGroups(res.objects);

                    $scope.tableParams.reload();
                } else {
                    notificationService.error(res.message);
                }

                $rootScope.$$phase || $scope.$apply();
            },
            function (err) {
                notificationService.error(`Add tags response status: ${err.status}`);
                $rootScope.loading = false;
            },
        );
    };

    function updateCandidateGroups(groups) {
        if (groups) {
            groups.forEach((oneAddedGroup) => {
                if ($scope.candidateGroups) {
                    $scope.candidateGroups.some((oneExistedGroup, index) => {
                        if (oneAddedGroup.candidateGroupId !== oneExistedGroup.candidateGroupId) {
                            if (index === $scope.candidateGroups.length - 1) $scope.candidateGroups.push(oneAddedGroup);
                            return false;
                        } else {
                            return true;
                        }
                    });
                }
            });
        }
    }

    $scope.parentClick = function (event) {
        let element = event.target;

        if (element.classList.contains('select-input-field')) {
            clickOnSelectBlock(element);
            return;
        }

        removeActiveBlock();
    };

    function clickOnSelectBlock(element) {
        if (element.classList.contains('select-input-field')) {
            removeActiveBlock();
            element.nextElementSibling.style = '';
            element.nextElementSibling.classList.toggle('activeBlock');
            return;
        }
    }

    function removeActiveBlock() {
        let activeBlock = document.querySelector('.activeBlock');
        if (activeBlock) {
            activeBlock.style.zIndex = '-1';
            activeBlock.classList.remove('activeBlock');
        }
    }

    function setTextfielsInObject(data) {
        for (let i in data) {
            data[i]['text'] = data[i]['name'];
        }
    }

    function isCorporateEmailIntergated() {
        Person.isIntegratedCorporateEmail().then(
            (resp) => {
                $scope.isCorporateEmailIntegrated = true;
            },
            (resp) => {
                if (resp) {
                    $scope.isCorporateEmailIntegrated = false;
                }
            },
        );
    }

    function notAllowedMailing($uibModalInstance, emailIsEnabled, noOneEmailIntegrated) {
        const vm = this;
        vm.isPersonalMailing = true;
        vm.emailIsEnabled = emailIsEnabled;
        vm.noOneEmailIntegrated = noOneEmailIntegrated;

        vm.closeModal = function () {
            $uibModalInstance.close();
        };
    }

    FileInit.initFileExcellUpload($rootScope, $scope, 'candidate', { allowedType: ['xls', 'xlsx'] }, $filter);

    $scope.$on('$destroy', () => {
        // $rootScope.searchFromAdvice = false;
        $rootScope.isSearchActualCandidates = false;
        $rootScope.actualVacancy = false;
    });
}

controller.controller('CandidateController', [
    '$localStorage',
    '$state',
    '$translate',
    'Service',
    '$scope',
    'ngTableParams',
    'Candidate',
    '$location',
    '$rootScope',
    '$filter',
    '$cookies',
    'serverAddress',
    'notificationService',
    'googleService',
    '$window',
    'ScopeService',
    'frontMode',
    'Vacancy',
    'Company',
    'vacancyStages',
    '$sce',
    '$analytics',
    'Mail',
    'FileInit',
    '$uibModal',
    'Person',
    '$timeout',
    'CandidateGroup',
    '$anchorScroll',
    'CandidateSearchService',
    'CustomField',
    'CustomFieldsSearchService',
    'StreamService',
    'CandidatesSlider',
    'CandidateResumeFromLink',
    'Mailing',
    'Email',
    'Gdpr',
    '$stateParams',
    'CandidatesSlider',
    '$http',
    '$httpParamSerializer',
    CandidateAllController,
]);
