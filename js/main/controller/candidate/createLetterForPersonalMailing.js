controller.controller('createLetterForPersonalMailing', [
    '$rootScope',
    '$uibModalInstance',
    '$translate',
    '$scope',
    'notificationService',
    'Mail',
    'candidate',
    'Person',
    'FileInit',
    '$filter',
    'Service',
    'PersonalMailing',
    'candidates',
    'vacancy',
    'client',
    'answerData',
    'stages',
    'currentStage',
    '$location',
    '$sce',
    'Email',
    function (
        $rootScope,
        $uibModalInstance,
        $translate,
        $scope,
        notificationService,
        Mail,
        candidate,
        Person,
        FileInit,
        $filter,
        Service,
        PersonalMailing,
        candidates,
        vacancy,
        client,
        answerData,
        stages,
        currentStage,
        $location,
        $sce,
        Email,
    ) {
        const vm = this;
        let currentInstance = null;
        $rootScope.emailTemplateModel = '';
        vm.editorId = 'personalMailingEditor';

        vm.$onInit = function () {
            Mail.getAllTemplates({ forVacancy: true, type: 'candidate' }, function (resp) {
                $rootScope.loading = false;
                if (resp.status === 'ok') {
                    $rootScope.testTemplates = resp.object;
                    vm.customTemplates = resp.object.custom;
                    $rootScope.templatesCustomLetter = resp.object.custom;
                    $rootScope.templatesGeneralLetter = resp.object.general;
                    $rootScope.templatesCustomLetter.forEach((item) => {
                        item.label = item.type ? item.type : item.name;
                    });
                    $rootScope.templatesGeneralLetter.forEach((item) => {
                        item.label = item.type ? item.type : item.name;
                    });
                    vm.templatesGrouped = [
                        {
                            label: 'My templates',
                            options: $rootScope.templatesCustomLetter,
                        },
                        {
                            label: 'General templates',
                            options: $rootScope.templatesGeneralLetter,
                        },
                    ];
                    vm.generalTemplates = resp.object.general;
                }
            });
            vm.forbitSave = false;
            $rootScope.$on('updateRendered', () => {
                updateRendered();
            });
            $scope.$on('isAddEmailForCandidateOpened', (evt, data) => {
                vm.forbitSave = data;
            });
            vm.stages = stages;
            vm.currentStage = currentStage && (currentStage.customInterviewStateId || currentStage.value || 'longlist');
            vm.filterByVacancy = Boolean(vm.stages && vm.currentStage);

            vm.emailFiles = [];
            vm.candidate = candidate.interviewId ? candidate.candidateId : candidate;
            vm.candidates = candidates;
            vm.showTemplates = vacancy;
            vm.vacancy = vacancy;
            vm.isF = false;
            vm.customTemplates = [];
            vm.generalTemplates = [];
            vm.templateLoaded = false;
            vm.vacancyId = candidate.vacancy;

            vm.vacancyLink = vm.vacancy
                ? `${$location.$$protocol + '://' + $location.$$host}/i/vacancy-${vm.vacancy.localId}`
                : '';
            vm.candidateId = vm.candidate.interviewId ? vm.candidate.candidateId.localId : vm.candidate.localId;

            if (!$location.url().includes('/vacancies/')) {
                vm.editorVariables = PersonalMailing.editorVariables.filter(
                    (variable) => variable.id !== '[[client name]]',
                );
            } else {
                vm.editorVariables = PersonalMailing.editorVariables;
            }

            vm.templateModel = initTemplateSettings();
            vm.templateRendered = initTemplateSettings();
            vm.isManyCandidates = vm.candidates && vm.candidates.length > 0;
            vm.recipientsMailsTitle = vm.isManyCandidates
                ? $filter('translate')('Recipients_all')
                : $filter('translate')("Recipient's Mails");

            vm.tabs = {
                emails: true,
                templates: false,
                preview: false,
            };

            if (vm.candidates) {
                vm.candidatesIds = vm.candidates.map((item) => {
                    return item.candidateId.candidateId ? item.candidateId.candidateId : item.candidateId;
                });
            }
            Promise.all([initSignature(), getPersonInfo(), initCkEditorOptions(), initFileAttach()])
                .then(() => {
                    updateRendered();
                })
                .finally(() => {
                    $rootScope.loading = false;
                });
        };
        vm.closeModal = function () {
            $uibModalInstance.close();
        };

        $rootScope.clearFieldsPersonalMailing = function () {
            vm.templateModel = initTemplateSettings();
            updateRendered();
        };

        vm.addEmailTemplateToSend = function (value) {
            $rootScope.emailTemplateModel = value;
            if (!value) {
                vm.templateModel.text = '';
                vm.templateModel.title = '';
                vm.emailFiles = [];
                updateRendered();
                $scope.$apply();
                return;
            }
            vm.emailFiles = [];
            vm.templateModel.title = $rootScope.emailTemplateModel.title;
            vm.templateModel.text = $rootScope.emailTemplateModel.text;
            if ($rootScope.emailTemplateModel.fileId && $rootScope.emailTemplateModel.fileName) {
                vm.emailFiles.push({
                    id: $rootScope.emailTemplateModel.fileId,
                    name: $rootScope.emailTemplateModel.fileName,
                });
            }
            if ($rootScope.emailTemplateModel.filesIdName) {
                vm.emailFiles = Service.filesIdNameToArray($rootScope.emailTemplateModel.filesIdName);
            }

            updateRendered();
            $scope.$apply();
        };

        vm.clickOnTab = function (tab) {
            vm.tabs[tab] = true;

            Object.keys(vm.tabs).forEach((item) => {
                if (item !== tab) {
                    vm.tabs[item] = false;
                }
            });
        };
        vm.sendLetter = (templateModel, candidateEmail) => {
            if (!candidateEmail && !vm.candidates) {
                notificationService.error($filter('translate')('You should fill all obligatory fields.'));
                document.getElementById('error-btn-wrapper').classList.add('error-btn-wrapper');
                return;
            }
            if (!templateModel.title) {
                notificationService.error($filter('translate')('You should fill all obligatory fields.'));
                document.getElementById('email-title').classList.add('error');
                return;
            }
            if (vm.candidatesWithoutEmails > 0) {
                notificationService.error($filter('translate')('Not all recipients have emails'));
                $rootScope.$emit('Not all recipients have emails');
                return;
            }
            if ($filter('isTextLengthMoreThen')(vm.templateModel.text, 30000)) {
                notificationService.error($filter('translate')('text_should_be_no_longer_than_30k_characters'));
                return;
            }

            const candidatesEmails = vm.candidates
                ? vm.candidates.map((candidate) => candidate.emails.filter((item) => item.default)[0].value).join(',')
                : null;
            const emails = candidatesEmails ? candidatesEmails : candidateEmail.value;
            const template = vm.templateRendered;
            let emailFilesString = {};

            if (vm.emailFiles && vm.emailFiles.length > 0) {
                vm.emailFiles.forEach((item) => {
                    emailFilesString[item.fileId] = item.fileName;
                });
                emailFilesString = JSON.stringify(emailFilesString);
            } else {
                emailFilesString = null;
            }

            const queryParams = {
                toEmails: emails,
                candidates: vm.candidates || [vm.candidate],
                email: vm.personEmail,
                lang: $translate.use(),
                template: {
                    title: template.title,
                    text: template.text,
                    filesIdName: emailFilesString,
                },
            };

            $rootScope.loading = true;
            Mail.sendMailForPersonalMailing(queryParams)
                .then(() => {
                    notificationService.success($filter('translate')('Letter sent'));
                    if ($rootScope.resetPipelineSelected) $rootScope.resetPipelineSelected();
                    vm.closeModal();
                })
                .catch((err) => {
                    if (err.code === 'errorCheckEmailTemplate') {
                        vm.clickOnTab('preview');
                        notificationService.error(
                            $filter('translate')(
                                'You have added an Operator for which there is no information. Please correct this before submitting.',
                            ),
                        );
                    } else {
                        notificationService.error($filter('translate')(err.message));
                    }
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $scope.$apply();
                });
        };
        vm.fieldFocused = function (event) {
            event.currentTarget.classList.remove('error');
        };
        vm.removeFile = function (file) {
            vm.emailFiles = vm.emailFiles.filter((email) => email.fileId !== file.fileId);
        };
        vm.changeTitleTemplate = function (value) {
            vm.templateModel.title = value;
            updateRendered();
            $scope.$apply();
        };

        vm.changeTitle = function () {
            updateRendered();
        };
        vm.onTransformTextWithStyles = function (html_code) {
            return $sce.trustAsHtml(html_code);
        };
        vm.textInsertData = function (text, isLetter) {
            if (text && vm.candidate) {
                let newText;
                if (!vm.candidates || vm.candidates.length === 1) {
                    const candidate = vm.candidates && vm.candidates[0] ? vm.candidates[0] : vm.candidate;
                    text = text.replace(/\[\[candidate name\]\]/g, candidate.firstName);
                }
                if (client) {
                    text = text.replace(/\[\[client name\]\]/g, client.name);
                }
                if (!vm.candidates || vm.candidates.length === 1) {
                    const candidate = vm.candidates && vm.candidates[0] ? vm.candidates[0] : vm.candidate;
                    text = text.replace(
                        /\[\[full name\]\]/g,
                        $rootScope.useAmericanNameStyle ? candidate.fullNameEn : candidate.fullName,
                    );
                }
                if (vm.vacancyLink && vm.vacancy.vacancyName) {
                    text = text
                        .replace(/\[\[vacancy link\]\]/g, `<a href='${vm.vacancyLink}'>${vm.vacancy.vacancyName}</a>`)
                        .replace(
                            /\[\[vacancy name\]\]/g,
                            isLetter
                                ? `<a href='${vm.vacancyLink}'>${vm.vacancy.vacancyName}</a>`
                                : vm.vacancy.vacancyName,
                        );
                }
                if (vm.personContacts) {
                    newText = text
                        .replace(
                            /\[\[recruiter's name\]\]/g,
                            $rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName,
                        )
                        .replace(
                            /\[\[recruiter's phone\]\]/g,
                            vm.personContacts.mobPhone
                                ? vm.personContacts.mobPhone
                                : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        )
                        .replace(
                            /\[\[recruiter's phone 2\]\]/g,
                            vm.personContacts.phoneWork
                                ? vm.personContacts.phoneWork
                                : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        )
                        .replace(
                            /\[\[recruiter's Skype\]\]/g,
                            vm.personContacts.skype
                                ? vm.personContacts.skype
                                : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        )
                        .replace(
                            /\[\[recruiter's Facebook\]\]/g,
                            vm.personContacts.facebook
                                ? '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                      vm.personContacts.facebook +
                                      '">' +
                                      vm.personContacts.facebook +
                                      '</a>'
                                : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        )
                        .replace(
                            /\[\[recruiter's LinkedIn\]\]/g,
                            vm.personContacts.linkedin
                                ? '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                      vm.personContacts.linkedin +
                                      '">' +
                                      vm.personContacts.linkedin +
                                      '</a>'
                                : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        )
                        .replace(
                            /\[\[interview date and time\]\]/g,
                            ` <span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        )
                        .replace(
                            /\[\[attendance date and time\]\]/g,
                            `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        );
                    if (!vm.personContacts.skype) {
                        newText = newText.replace('<span style="font-size: 14px;">Skype: </span><br />', '');
                    }
                } else {
                    newText = text;
                }
                if (isLetter && vm.signature) {
                    newText += vm.signature;
                }
                return newText;
            } else if (isLetter && vm.signature) {
                return vm.signature;
            }
        };

        function getPersonInfo() {
            vm.personContacts = {};
            if ($rootScope.me.emails.length > 0) {
                vm.personEmail = $rootScope.me.emails[0].email;
                $rootScope.me.contacts.forEach((contact) => {
                    switch (contact.contactType) {
                        case 'mphone':
                            vm.personContacts.mobPhone = contact.value;
                            break;
                        case 'phoneWork':
                            vm.personContacts.phoneWork = contact.value;
                            break;
                        case 'skype':
                            vm.personContacts.skype = contact.value;
                            break;
                        case 'linkedin':
                            vm.personContacts.linkedin = contact.value;
                            break;
                        case 'facebook':
                            vm.personContacts.facebook = contact.value;
                    }
                });
                vm.editorVariables.forEach((item) => {
                    if (item.field && !vm.personContacts.hasOwnProperty(item.field)) {
                        item.isAvailable = false;
                    }
                });
            }
        }

        function initCkEditorOptions() {
            vm.ckEditorOptions = {
                toolbarGroups: [
                    {
                        name: 'document',
                        groups: ['mode', 'document', 'doctools'],
                    },
                    { name: 'clipboard', groups: ['clipboard', 'undo'] },
                    {
                        name: 'editing',
                        groups: ['find', 'selection', 'spellchecker', 'editing'],
                    },
                    { name: 'forms', groups: ['forms'] },
                    { name: 'basicstyles', groups: ['basicstyles', 'cleanup'] },
                    { name: 'colors', groups: ['colors'] },
                    {
                        name: 'paragraph',
                        groups: ['list', 'indent', 'blocks', 'align', 'bidi', 'paragraph'],
                    },
                    { name: 'links', groups: ['links'] },
                    { name: 'insert', groups: ['insert'] },
                    { name: 'styles', groups: ['styles'] },
                    { name: 'tools', groups: ['tools'] },
                    { name: 'others', groups: ['others'] },
                    { name: 'about', groups: ['about'] },
                ],
                removeButtons:
                    'Subscript,Superscript,Source,Save,Templates,NewPage,Preview,Print,PasteFromWord,Paste,Copy,Cut,Find,Replace,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,RemoveFormat,CopyFormatting,Outdent,Indent,CreateDiv,Blockquote,JustifyCenter,JustifyLeft,JustifyRight,JustifyBlock,Language,BidiRtl,BidiLtr,Link,Unlink,Anchor,Table,Flash,Image,HorizontalRule,Smiley,SpecialChar,PageBreak,Iframe,Maximize,About,ShowBlocks,PasteText,Format,Styles,BGColor',
                height: 130,
                on: {
                    pluginsLoaded: function () {
                        var editor = this,
                            config = editor.config;

                        function createItemForDropdown(name, isAvailable) {
                            return isAvailable
                                ? `<span>${$filter('translate')(name)}</span>`
                                : `<span style='color: red'>${$filter('translate')(name)}</span>`;
                        }

                        editor.ui.addRichCombo(`${$filter('translate')('operators')}`, {
                            label: `${$filter('translate')('operators')}`,
                            title: `${$filter('translate')('operators')}`,
                            toolbar: 'about,0',

                            panel: {
                                css: [CKEDITOR.skin.getPath('editor')].concat(config.contentsCss),
                                multiSelect: false,
                                attributes: {
                                    'aria-label': 'My Dropdown Title',
                                },
                            },

                            init: function () {
                                this.startGroup('Operators');
                                vm.editorVariables.forEach((editorVariable) => {
                                    this.add(
                                        editorVariable.isAvailable
                                            ? editorVariable.id
                                            : `[[${$filter('translate')('NO INFORMATION')}]]`,
                                        createItemForDropdown(editorVariable.name, editorVariable.isAvailable),
                                    );
                                });
                            },

                            onClick: function (value) {
                                editor.focus();
                                editor.fire('saveSnapshot');

                                editor.insertHtml(value);

                                editor.fire('saveSnapshot');
                            },
                        });
                    },
                },
            };

            vm.templateLoaded = true;
        }

        function initFileAttach() {
            FileInit.initFileCandidatePersonalMailing(vm, $filter);

            vm.callbackFile = function (resp, name) {
                vm.emailFiles = vm.emailFiles || [];
                const isUnique = !vm.emailFiles.some((item) => item.name === name);
                if (isUnique) {
                    vm.emailFiles.push({ fileId: resp, fileName: name });
                }
                return isUnique;
            };
        }

        function initSignature() {
            return Email.getMailboxes().then((resp) => {
                vm.signature = `<signature><p>${
                    resp.filter((item) => item.personalMailing)[0].signature || ''
                }</p><signature>`;
            });
        }

        function initTemplateSettings() {
            let obj = {};
            Object.defineProperties(obj, {
                title: {
                    value: answerData ? answerData.subject : '',
                    writable: true,
                },
            });
            Object.defineProperties(obj, {
                text: { value: '', writable: true },
            });
            return obj;
        }

        function updateRendered() {
            vm.templateRendered = templateInsertData(vm.templateModel);
        }

        function templateInsertData(template) {
            return {
                text: vm.textInsertData(template.text, true),
                title: vm.textInsertData(template.title),
            };
        }
    },
]);
