controller.controller('createSms', [
    '$rootScope',
    '$uibModalInstance',
    '$translate',
    '$scope',
    'notificationService',
    'Mail',
    'candidate',
    'Person',
    'FileInit',
    '$filter',
    'Service',
    'PersonalMailing',
    'candidates',
    'vacancy',
    'client',
    'answerData',
    'stages',
    'currentStage',
    '$location',
    '$sce',
    'SmsService',
    'fetchVacancies',
    'responsibleOptionsGrouped',
    'AlphasmsIntegrationService',
    function (
        $rootScope,
        $uibModalInstance,
        $translate,
        $scope,
        notificationService,
        Mail,
        candidate,
        Person,
        FileInit,
        $filter,
        Service,
        PersonalMailing,
        candidates,
        vacancy,
        client,
        answerData,
        stages,
        currentStage,
        $location,
        $sce,
        SmsService,
        fetchVacancies,
        responsibleOptionsGrouped,
        AlphasmsIntegrationService,
    ) {
        const vm = this;
        $rootScope.smsTemplateModel = '';
        vm.editorId = 'personalMailingEditor';

        vm.$onInit = function () {
            vm.responsibleOptions = responsibleOptionsGrouped[0].options || responsibleOptionsGrouped;
            SmsService.getTemplates(function (resp) {
                $rootScope.loading = false;
                if (resp.status === 'ok') {
                    vm.smsTemplates = resp.objects.filter((template) => !template.forInterview);
                }
            });
            vm.forbitSave = false;
            $rootScope.$on('updateRendered', () => {
                updateRendered();
            });
            $scope.$on('isAddEmailForCandidateOpened', (evt, data) => {
                vm.forbitSave = data;
            });
            vm.stages = stages;
            vm.currentStage = currentStage && (currentStage.customInterviewStateId || currentStage.value || 'longlist');

            vm.onFetchVacancies = $rootScope.makeDebounce(fetchVacancies, 300);
            vm.selectedVacancy = null;

            vm.selectedDate = null;

            vm.createCalendarEvent = false;
            vm.calendarTitle = '';

            vm.responsibleUsers = [];

            vm.candidate = candidate.interviewId ? candidate.candidateId : candidate;
            vm.candidatePhoneNumbers = vm.candidate.contacts?.filter((contact) => contact?.type === 'mphone') || [];
            vm.selectedCandidatePhoneNumbers = vm.candidatePhoneNumbers[0] ? [vm.candidatePhoneNumbers[0]] : [];
            vm.candidates = candidates;
            vm.showTemplates = vacancy;
            vm.vacancy = vacancy;
            vm.isF = false;
            vm.smsTemplates = [];
            vm.templateLoaded = false;
            vm.vacancyId = candidate.vacancy;
            vm.vacancyLink = vm.vacancy
                ? `${$location.$$protocol + '://' + $location.$$host}/i/vacancy-${vm.vacancy.localId}`
                : '';
            vm.candidateId = vm.candidate.interviewId ? vm.candidate.candidateId.localId : vm.candidate.localId;

            if (!$location.url().includes('/vacancies/')) {
                vm.editorVariables = [
                    ...PersonalMailing.editorVariables.filter((variable) => variable.id !== '[[client name]]'),
                    {
                        name: 'Date and time',
                        id: '[[attendance date and time]]',
                        isAvailable: true,
                    },
                    {
                        name: 'Vacancy name',
                        id: '[[vacancy name]]',
                        isAvailable: true,
                    },
                ];
            } else {
                vm.editorVariables = PersonalMailing.editorVariables;
            }

            vm.templateModel = initTemplateSettings();
            vm.templateModelOriginalText = '';
            vm.templateRendered = initTemplateSettings();
            vm.isManyCandidates = vm.candidates && vm.candidates.length > 0;
            vm.recipientsSmsTitle = vm.isManyCandidates
                ? $filter('translate')('Recipients_all')
                : $filter('translate')('alphasms5');

            vm.tabs = {
                sms: true,
                templates: false,
                preview: false,
            };

            if (vm.candidates) {
                vm.candidatesIds = vm.candidates.map((item) => {
                    return item.candidateId.candidateId ? item.candidateId.candidateId : item.candidateId;
                });
            }
            Promise.all([getPersonInfo(), initCkEditorOptions()])
                .then(() => {
                    updateRendered();
                })
                .finally(() => {
                    $rootScope.loading = false;
                });
        };
        vm.closeModal = function () {
            $uibModalInstance.close();
        };

        vm.addCandidatePhoneNumber = (value, f) => {
            value.forEach((phoneObj) => {
                if (phoneObj.isNew && !phoneObj.addedToOptions) {
                    vm.candidatePhoneNumbers.push({
                        addedToOptions: true,
                        ...phoneObj,
                    });
                    phoneObj.addedToOptions = true;
                }
            });
            vm.selectedCandidatePhoneNumbers = value;
            $rootScope.$$phase || $scope.$apply();
        };

        vm.onChangeVacancy = (value) => {
            vm.selectedVacancy = value;
            updateRendered();
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.$watch('vm.selectedDate', (newValue) => {
            if (newValue) {
                updateRendered();
                if (vm.createCalendarEvent) {
                    vm.calendarTitle = $filter('translate')('alphasms10', {
                        dateTime: $filter('dateFormatSimpleAction')(vm.selectedDate, true),
                    });
                }
                $rootScope.$$phase || $scope.$apply();
            }
        });

        vm.onChangeCalendarCheckbox = (value) => {
            vm.createCalendarEvent = value.target.checked;
            vm.calendarTitle = $filter('translate')('alphasms10', {
                dateTime: $filter('dateFormatSimpleAction')(vm.selectedDate, true),
            });

            $rootScope.$$phase || $scope.$apply();
        };

        vm.onChangeCalendarTitle = (value) => {
            vm.calendarTitle = value;
            $rootScope.$$phase || $scope.$apply();
        };

        vm.onChangeResponsibleUser = (value) => {
            vm.responsibleUsers = value;
            $rootScope.$$phase || $scope.$apply();
        };

        vm.addEmailTemplateToSend = function (value) {
            $rootScope.smsTemplateModel = value;
            if (!value) {
                vm.templateModel.text = '';
                vm.templateModelOriginalText = '';
                updateRendered();
                $rootScope.$$phase || $scope.$apply();
                return;
            }
            vm.templateModel.text = $rootScope.smsTemplateModel.text;
            vm.templateModelOriginalText = $rootScope.smsTemplateModel.text;

            updateRendered();
            $rootScope.$$phase || $scope.$apply();
        };

        vm.clickOnTab = function (tab) {
            vm.tabs[tab] = true;

            Object.keys(vm.tabs).forEach((item) => {
                if (item !== tab) {
                    vm.tabs[item] = false;
                }
            });
        };

        // SEND!
        vm.sendSms = () => {
            if (
                vm.candidatesWithoutPhones > 0 ||
                (!vm.selectedCandidatePhoneNumbers?.length && !vm.candidates?.length)
            ) {
                notificationService.error($filter('translate')('Not all recipients have numbers'));
                return;
            }
            if (
                !vm.templateRendered.text?.length ||
                (vm.isVacancyOperator && !vm.selectedVacancy) ||
                (vm.isTimeDateOperator && !vm.selectedDate)
            ) {
                notificationService.error($filter('translate')('You should fill all obligatory fields.'));
                return;
            }

            if (vm.templateRendered?.text?.includes('color:red')) {
                notificationService.error(
                    $filter('translate')(
                        'You have added an Operator for which there is no information. Please correct this before submitting.',
                    ),
                );
                return;
            }

            const candidatesPhones = vm.candidates
                ? vm.candidates.reduce((acc, candidate) => {
                      const defaultPhoneNumber = candidate.phoneNumbers.find((item) => item.default)?.value;
                      if (defaultPhoneNumber) {
                          acc[candidate.candidateId] = defaultPhoneNumber;
                      }
                      return acc;
                  }, {})
                : {
                      [vm.candidate.candidateId]: vm.selectedCandidatePhoneNumbers
                          .map((phone) => phone.value)
                          .join(','),
                  };
            const responsibleIds = vm.responsibleUsers.map((resp) => resp.userId);
            const text = vm.templateRendered.text.replace(/<br\s*\/?>/gi, '');
            const targetDate = new Date(vm.selectedDate).getTime();
            const vacancyId = $location.url().includes('/vacancies/') ? vm.vacancy?.vacancyId : null;
            const smsTemplateId = $rootScope.smsTemplateModel ? $rootScope.smsTemplateModel.smsTemplateId : null;

            const queryParams = {
                candidatesPhones,
                text,
                createTask: vm.createCalendarEvent,
                targetDate,
                title: vm.calendarTitle,
                responsibleIds,
                vacancyId,
                smsTemplateId,
            };

            $rootScope.loading = true;
            AlphasmsIntegrationService.sendToCandidates(queryParams)
                .then(() => {
                    notificationService.success($filter('translate')('SMS sent'));
                    if ($rootScope.resetPipelineSelected) $rootScope.resetPipelineSelected();
                    vm.closeModal();
                })
                .catch((err) => {
                    if (err.code === 'errorCheckEmailTemplate') {
                        vm.clickOnTab('preview');
                        notificationService.error(
                            $filter('translate')(
                                'You have added an Operator for which there is no information. Please correct this before submitting.',
                            ),
                        );
                    } else {
                        notificationService.error($filter('translate')(err.message));
                    }
                })
                .finally(() => {
                    if ($location.url().includes('candidates/slide')) $rootScope.updateTasksData();
                    $rootScope.loading = false;
                    $scope.$apply();
                });
        };
        vm.fieldFocused = function (event) {
            event.currentTarget.classList.remove('error');
        };
        vm.removeFile = function (file) {
            vm.emailFiles = vm.emailFiles.filter((email) => email.fileId !== file.fileId);
        };
        vm.changeTitleTemplate = function (value) {
            vm.templateModel.title = value;
            updateRendered();
            $scope.$apply();
        };

        vm.changeTitle = function () {
            updateRendered();
        };
        vm.resetToDefault = function () {
            vm.templateModel.text = vm.templateModelOriginalText;
            updateRendered();
            $rootScope.$$phase || $scope.$apply();
        };
        vm.onTransformTextWithStyles = function (html_code) {
            return $sce.trustAsHtml(html_code);
        };
        vm.textInsertData = function (text) {
            vm.isTimeDateOperator = text.includes('[[attendance date and time]]');
            vm.isVacancyOperator = text.includes('[[vacancy name]]');
            if (text && vm.candidate) {
                let newText;
                if (!vm.candidates || vm.candidates.length === 1) {
                    const candidate = vm.candidates && vm.candidates[0] ? vm.candidates[0] : vm.candidate;
                    text = text.replace(/\[\[candidate name\]\]/g, candidate.firstName);
                }

                text = text.replace(
                    /\[\[client name\]\]/g,
                    vm.selectedVacancy?.clientId?.name
                        ? vm.selectedVacancy?.clientId.name
                        : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                );

                text = text.replace(
                    /\[\[attendance date and time\]\]/g,
                    vm.selectedDate
                        ? $filter('dateFormatSimpleAction')(vm.selectedDate, true)
                        : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                );
                text = text.replace(
                    /\[\[vacancy name\]\]/g,
                    vm.selectedVacancy
                        ? vm.selectedVacancy.position
                        : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                );

                if (!vm.candidates || vm.candidates.length === 1) {
                    const candidate = vm.candidates && vm.candidates[0] ? vm.candidates[0] : vm.candidate;
                    text = text.replace(
                        /\[\[full name\]\]/g,
                        $rootScope.useAmericanNameStyle ? candidate.fullNameEn : candidate.fullName,
                    );
                }
                if (vm.vacancyLink && vm.vacancy.vacancyName) {
                    text = text
                        .replace(/\[\[vacancy link\]\]/g, vm.vacancyLink)
                        .replace(/\[\[vacancy name\]\]/g, vm.vacancy.vacancyName);
                }
                if (vm.personContacts) {
                    newText = text
                        .replace(
                            /\[\[recruiter's name\]\]/g,
                            $rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName,
                        )
                        .replace(
                            /\[\[recruiter's phone\]\]/g,
                            vm.personContacts.mobPhone
                                ? vm.personContacts.mobPhone
                                : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        )
                        .replace(
                            /\[\[recruiter's phone 2\]\]/g,
                            vm.personContacts.phoneWork
                                ? vm.personContacts.phoneWork
                                : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        )
                        .replace(
                            /\[\[recruiter's Skype\]\]/g,
                            vm.personContacts.skype
                                ? vm.personContacts.skype
                                : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        )
                        .replace(
                            /\[\[recruiter's Facebook\]\]/g,
                            vm.personContacts.facebook
                                ? vm.personContacts.facebook
                                : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        )
                        .replace(
                            /\[\[recruiter's LinkedIn\]\]/g,
                            vm.personContacts.linkedin
                                ? vm.personContacts.linkedin
                                : `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        )
                        .replace(
                            /\[\[interview date and time\]\]/g,
                            ` <span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        )
                        .replace(
                            /\[\[attendance date and time\]\]/g,
                            `<span style='color:red'>[[${$filter('translate')('NO INFORMATION')}]]</span>`,
                        );
                } else {
                    newText = text;
                }
                return newText;
            }
        };

        function getPersonInfo() {
            vm.personContacts = {};
            if ($rootScope.me.emails.length > 0) {
                vm.personEmail = $rootScope.me.emails[0].email;
                $rootScope.me.contacts.forEach((contact) => {
                    switch (contact.contactType) {
                        case 'mphone':
                            vm.personContacts.mobPhone = contact.value;
                            break;
                        case 'phoneWork':
                            vm.personContacts.phoneWork = contact.value;
                            break;
                        case 'skype':
                            vm.personContacts.skype = contact.value;
                            break;
                        case 'linkedin':
                            vm.personContacts.linkedin = contact.value;
                            break;
                        case 'facebook':
                            vm.personContacts.facebook = contact.value;
                    }
                });
                vm.editorVariables.forEach((item) => {
                    if (item.field && !vm.personContacts.hasOwnProperty(item.field)) {
                        item.isAvailable = false;
                    }
                });
            }
        }

        function initCkEditorOptions() {
            vm.ckEditorOptions = {
                toolbarGroups: [
                    {
                        name: 'document',
                        groups: ['mode', 'document', 'doctools'],
                    },
                    { name: 'clipboard', groups: ['clipboard', 'undo'] },
                    {
                        name: 'editing',
                        groups: ['find', 'selection', 'spellchecker', 'editing'],
                    },
                    { name: 'forms', groups: ['forms'] },
                    { name: 'basicstyles', groups: ['basicstyles', 'cleanup'] },
                    { name: 'colors', groups: ['colors'] },
                    {
                        name: 'paragraph',
                        groups: ['list', 'indent', 'blocks', 'align', 'bidi', 'paragraph'],
                    },
                    { name: 'links', groups: ['links'] },
                    { name: 'insert', groups: ['insert'] },
                    { name: 'styles', groups: ['styles'] },
                    { name: 'tools', groups: ['tools'] },
                    { name: 'others', groups: ['others'] },
                    { name: 'about', groups: ['about'] },
                ],
                removeButtons:
                    'Subscript,Superscript,Source,Save,Templates,NewPage,Preview,Print,PasteFromWord,Paste,Copy,Cut,Find,Replace,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,RemoveFormat,CopyFormatting,Outdent,Indent,CreateDiv,Blockquote,JustifyCenter,JustifyLeft,JustifyRight,JustifyBlock,Language,BidiRtl,BidiLtr,Link,Unlink,Anchor,Table,Flash,Image,HorizontalRule,Smiley,SpecialChar,PageBreak,Iframe,Maximize,About,ShowBlocks,PasteText,Format,Styles,BGColor',
                height: 130,
                on: {
                    pluginsLoaded: function () {
                        var editor = this,
                            config = editor.config;

                        function createItemForDropdown(name, isAvailable) {
                            return isAvailable
                                ? `<span>${$filter('translate')(name)}</span>`
                                : `<span style='color: red'>${$filter('translate')(name)}</span>`;
                        }

                        editor.ui.addRichCombo(`${$filter('translate')('operators')}`, {
                            label: `${$filter('translate')('operators')}`,
                            title: `${$filter('translate')('operators')}`,
                            toolbar: 'about,0',

                            panel: {
                                css: [CKEDITOR.skin.getPath('editor')].concat(config.contentsCss),
                                multiSelect: false,
                                attributes: {
                                    'aria-label': 'My Dropdown Title',
                                },
                            },

                            init: function () {
                                this.startGroup('Operators');
                                vm.editorVariables.forEach((editorVariable) => {
                                    this.add(
                                        editorVariable.isAvailable
                                            ? editorVariable.id
                                            : `<span style='color:red'>[[${$filter('translate')(
                                                  'NO INFORMATION',
                                              )}]]</span>`,
                                        createItemForDropdown(editorVariable.name, editorVariable.isAvailable),
                                    );
                                });
                            },

                            onClick: function (value) {
                                editor.focus();
                                editor.fire('saveSnapshot');

                                editor.insertHtml(value);

                                editor.fire('saveSnapshot');
                            },
                        });
                    },
                },
            };

            vm.templateLoaded = true;
        }

        function initTemplateSettings() {
            let obj = {};
            Object.defineProperties(obj, {
                title: {
                    value: answerData ? answerData.subject : '',
                    writable: true,
                },
            });
            Object.defineProperties(obj, {
                text: { value: '', writable: true },
            });
            return obj;
        }

        function updateRendered() {
            vm.templateRendered = templateInsertData(vm.templateModel);
        }

        function templateInsertData(template) {
            return {
                text: vm.textInsertData(template.text),
            };
        }
    },
]);
