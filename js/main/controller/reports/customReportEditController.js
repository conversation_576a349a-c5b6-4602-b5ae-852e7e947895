function CustomReportEditCtrl(
    $rootScope,
    $scope,
    Vacancy,
    Service,
    $location,
    $stateParams,
    notificationService,
    $filter,
    translateWords,
    $translate,
    vacancyStages,
    Stat,
    Company,
    vacancyStages,
    Person,
    $uibModal,
    CustomReportsService,
    CustomReportEditService,
    $uibModal,
) {
    try {
        $rootScope.redirectToReports();

        let filterVacancy = (vacancy) => {
            let statuses = this.data.vacancyStatuses,
                index = vacancy.position.toLocaleLowerCase().indexOf(this.query.toLocaleLowerCase());

            if (index !== -1 && statuses.some((item) => item == vacancy.status)) {
                return vacancy;
            }
        };

        let showCurrentBlock = (event) => {
            CustomReportEditService.showBlocks.call(null, event);
        };

        let _parentClick = (event) => {
            showCurrentBlock(event, $scope);
        };

        $rootScope.onChangeEditReportName = (newValue) => {
            this.data.name = newValue;
            $rootScope.$$phase || $rootScope.$apply();
        };

        $rootScope.vacancyStatuses = [];
        $rootScope.fieldVacancyEditList = [];
        CustomReportEditService.buildReport.call(this, $scope);
        this.showChoosingMenu = CustomReportsService.showChoosingMenu;
        this.removeReport = CustomReportsService.removeReport;
        this.remove = CustomReportsService.remove;
        this.closeModalOnRemove = CustomReportsService.closeModal;
        this.inHover = CustomReportsService.inHover;
        this.outHover = CustomReportsService.outHover;
        this.data = CustomReportEditService.editReport;
        this.fieldsList = CustomReportEditService.editReport.vacancyFields;
        this.checkListFields = CustomReportEditService.editReport.fieldsList;
        this.dateRange = CustomReportEditService.dateRange;
        this.selectValue = CustomReportEditService.selectValue.bind(this);
        this.selectValueStages = CustomReportEditService.selectValueStages.bind(this);
        this.selectAllStages = CustomReportEditService.selectAllStages.bind(this);
        this.selectValueVacancyFields = CustomReportEditService.selectValueVacancyFields.bind(this);
        this.changeNameOrDescription = CustomReportEditService.changeNameOrDescription;
        this.editNameOrDescr = CustomReportEditService.editNameOrDescr;
        this.closeModal = CustomReportEditService.closeModal;
        this.saveNameOrDescr = CustomReportEditService.saveNameOrDescr;
        this.saveCustomReport = CustomReportEditService.saveCustomReport;
        this.onClickSaveBtn = CustomReportEditService.onClickSaveBtn;
        this.showOrHideCandidates = CustomReportEditService.showOrHideCandidates;
        this.selectDateRange = CustomReportEditService.selectDateRange.bind(this);
        this.activateTimeRange = CustomReportEditService.activateTimeRange.bind(this);
        this.selectAllVacancies = CustomReportEditService.selectAllVacancies.bind(this);
        $rootScope.selectValueAllVacancyFields = $rootScope.selectValueAllVacancyFields.bind(this);
        $rootScope.selectAllUsersValue = $rootScope.selectAllUsersValue.bind(this);
        $rootScope.selectAllStatusValue = $rootScope.selectAllStatusValue.bind(this);
        this.filterVacancy = filterVacancy;
        this.parentClick = _parentClick;
        this.checkListFields = this.fieldsList.some((field) => field);
        this.selectRange = 'customRange';
        this.disabled = false;
    } catch (error) {
        console.error(error, 'error');
    }
}
controller.controller('CustomReportEditCtrl', [
    '$rootScope',
    '$scope',
    'Vacancy',
    'Service',
    '$location',
    '$stateParams',
    'notificationService',
    '$filter',
    'translateWords',
    '$translate',
    'vacancyStages',
    'Stat',
    'Company',
    'vacancyStages',
    'Person',
    '$uibModal',
    'CustomReportsService',
    'CustomReportEditService',
    '$uibModal',
    CustomReportEditCtrl,
]);
