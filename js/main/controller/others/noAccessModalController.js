controller.controller('noAccessModalController', [
    '$rootScope',
    '$scope',
    function ($rootScope, $scope) {
        $scope.showResetPassword = false;
        $scope.showCanNotGetCode = false;
        $scope.isSAMLLoginActivated = false;

        $scope.submitHandler = function () {
            $rootScope.errorSignin = {};

            if ($scope.isSAMLLoginActivated) {
                return $rootScope.loginSocial('saml');
            }

            switch ($rootScope.authWithExternalService) {
                case 'google':
                    $rootScope.loginForm.authorizationCode
                        ? $rootScope.loginSocial('google')
                        : setAuthorizationCodeError();
                    break;
                case 'fb':
                    $rootScope.loginForm.authorizationCode ? $rootScope.loginSocial('fb') : setAuthorizationCodeError();
                    break;
                default:
                    $rootScope.login();
            }
        };

        $scope.toogleCanNotGetCode = function (show) {
            $scope.showCanNotGetCode = show;
        };

        $scope.toggleResetBlock = function () {
            $scope.showResetPassword = true;
        };

        $scope.activateSAMLLogin = function () {
            $scope.isSAMLLoginActivated = true;
        };

        $scope.updateLogin = function (value) {
            $rootScope.loginForm.login = value;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.updatePassword = function (value) {
            $rootScope.loginForm.password = value;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.updateAuthorizationCode = function (value) {
            $rootScope.loginForm.authorizationCode = value;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onClickGoogle = function () {
            $rootScope.loginSocial('google');
        };

        $scope.onClickFb = function () {
            $rootScope.loginSocial('fb');
        };

        function setAuthorizationCodeError() {
            let authorizationCode = document.getElementById('no-access-id-authorizationCode');

            $rootScope.errorSignin.mistake = 'input_authorization_code';
            $rootScope.errorSignin.type = 'incorrect';
            authorizationCode.style.border = '1px solid red';

            $rootScope.$$phase || $scope.$apply();
        }
    },
]);
