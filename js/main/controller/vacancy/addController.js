controller.controller('vacancyAddController', [
    'FileInit',
    '$scope',
    'Vacancy',
    'Service',
    'GoogleMaps',
    'Client',
    '$location',
    '$rootScope',
    'notificationService',
    '$filter',
    '$translate',
    '$localStorage',
    '$cookies',
    '$window',
    'Person',
    'Company',
    'Candidate',
    'CustomField',
    'CustomFieldList',
    '$timeout',
    'RegionInputService',
    'scoreCardsMainService',
    '$uibModal',
    'Application',
    'allRolesList',
    'prioritiesOptions',
    function (
        FileInit,
        $scope,
        Vacancy,
        Service,
        GoogleMaps,
        Client,
        $location,
        $rootScope,
        notificationService,
        $filter,
        $translate,
        $localStorage,
        $cookies,
        $window,
        Person,
        Company,
        Candidate,
        CustomField,
        CustomFieldList,
        $timeout,
        RegionInputService,
        scoreCardsMainService,
        $uibModal,
        Application,
        allRolesList,
        prioritiesOptions,
    ) {
        $scope.returnValue = function (field) {
            if (field && typeof field === 'object') return field.value;
            else return field;
        };
        $scope.callbackFile = function (resp, names) {
            $scope.fileForSave.push({ attId: resp, fileName: names });
        };
        $scope.removeFile = function (id) {
            angular.forEach($scope.fileForSave, function (val, ind) {
                if (val.attId === id) {
                    $scope.fileForSave.splice(ind, 1);
                }
            });
        };

        $scope.errorFields = {
            industry: false,
            vacancyName: false,
            clientName: false,
            category: false,
            employmentType: false,
            country: false,
            city: false,
            skills: false,
        };
        $rootScope.showSelectAll = false;

        const validateSalary = () => {
            if (!$scope.vacancy.salaryFrom && !$scope.vacancy.salaryTo) {
                return ($scope.salaryError = false);
            }

            $scope.salaryError = parseInt($scope.vacancy.salaryFrom) > parseInt($scope.vacancy.salaryTo);
        };

        $scope.numberOnlyInputHandler = (value) => {
            if (parseInt(value) <= 0) {
                return (value = 0);
            }

            if (!value.match(/^[0-9]+$/)) {
                return parseInt(value.replace(/[^0-9]/g, ''));
            }

            validateSalary();

            return parseInt(value);
        };

        $scope.skillExperienceOptions = Service.skillVacancyExperience();

        $scope.languageLevelOptions = Service.languagesLevel();

        $scope.hideSalary = false;
        $scope.typeVacancy = false;

        $scope.showRequiredStar = false;

        $scope.skillsModel = [];
        $scope.languages = [];

        $scope.inputSearchThreshold = 1;

        $scope.showGoogleLocationPicker = false;
        $scope.initialGooglePlacesInput = '';

        $scope.addNewSkillField = () => {
            $scope.skillsModel.push({
                skill: undefined,
                experience: {
                    label: 'e00_no_experience',
                    value: 'e00_no_experience',
                },
                mustHave: true,
            });
            $scope.$$phase || $scope.$apply();
        };

        $scope.removeSkillField = (index) => {
            refreshSkillsOptions($scope.skillsModel[index]['skill'], 'restore');
            $scope.skillsModel.splice(index, 1);
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeSkill = (newValue, index) => {
            if (!newValue) {
                refreshSkillsOptions($scope.skillsModel[index]['skill'], 'restore');
                $scope.skillsModel[index]['skill'] = null;
                $scope.$$phase || $scope.$apply();
                return;
            }

            refreshSkillsOptions(newValue);

            if ($scope.errorFields.skills && newValue.value) $scope.errorFields.skills = false;
            if (newValue.isNew) newValue.value = { skill: newValue.value };
            newValue['new'] = newValue?.isNew || false;
            $scope.skillsModel[index]['skill'] = newValue;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeSkillExperience = (newValue, index) => {
            $scope.skillsModel[index]['experience'] = newValue;
            $rootScope.wrongRole = false;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeSkillMustHave = (event, index) => {
            $scope.skillsModel[index]['mustHave'] = !event.target.checked;
            $scope.$$phase || $scope.$apply();
        };

        const refreshSkillsOptions = (skill, action = 'remove') => {
            if (!skill || skill.isNew) return;

            const type = skill.value.type;
            if (type === 'general') {
                if (action === 'remove') {
                    $scope.skillsOptions[0].options = $scope.skillsOptions[0].options.filter(
                        ({ value }) => value.skillId !== skill.value.skillId,
                    );
                } else if (action === 'restore') {
                    $scope.skillsOptions[0].options.push(skill);
                    $scope.skillsOptions[0].options = $scope.skillsOptions[0].options.sort((a, b) =>
                        a.label.localeCompare(b.label),
                    );
                }
            } else if (type === 'custom') {
                const index = $scope.skillsOptions.length === 1 ? 0 : 1;

                if (action === 'remove') {
                    $scope.skillsOptions[index].options = $scope.skillsOptions[index].options.filter(
                        ({ value }) => value.skillId !== skill.value.skillId,
                    );
                } else if (action === 'restore') {
                    $scope.skillsOptions[index].options.push(skill);
                    $scope.skillsOptions[index].options = $scope.skillsOptions[index].options.sort((a, b) =>
                        a.label.localeCompare(b.label),
                    );
                }
            }
        };

        $scope.addNewLanguageField = (event, index) => {
            $scope.languages.push({
                name: null,
                level: null,
            });
            $scope.$$phase || $scope.$apply();
        };

        $scope.removeLanguageField = (index) => {
            if ($scope.languages[index]['name']) {
                $scope.languagesOptions.push($scope.languages[index].name);
                $scope.languagesOptions = $rootScope.getSortedLangOptions($scope.languagesOptions);
            }

            $scope.languages = $scope.languages.filter((_, i) => i !== index);
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeLanguage = (newValue, index) => {
            if (newValue?.value) {
                $scope.languagesOptions = $scope.languagesOptions.filter(
                    ({ value }) => value.english !== newValue.value.english,
                );
            } else {
                $scope.languagesOptions.push($scope.languages[index].name);
                $scope.languagesOptions = $rootScope.getSortedLangOptions($scope.languagesOptions);
            }

            $scope.languages[index]['name'] = newValue;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeLanguageLevel = (newValue, index) => {
            $scope.languages[index]['level'] = newValue;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeSelectCustomField = (newValue, index) => {
            $scope.allObjCustomField[index]['value'] = newValue;
            if ($scope.cfSelectError?.length) {
                $scope.cfSelectError = $scope.cfSelectError.filter(
                    (error) => error.fieldId !== $scope.allObjCustomField[index].fieldId,
                );
            }

            $scope.$$phase || $scope.$apply();
        };

        $scope.cancel = function () {
            $location.path('/vacancies');
        };

        if (
            $rootScope.me.personParams.clientAccessLevel &&
            $rootScope.me.personParams.clientAccessLevel === 'hide' &&
            $location.path().match('vacancy/add')
        ) {
            $location.path('/vacancies');
        }

        $scope.onChangeVacancyName = (newValue) => {
            $scope.errorFields.vacancyName = !Boolean(newValue);
            $scope.vacancy.position = newValue;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onBlurVacancyName = (event) => {
            if (!$scope.vacancy.position) {
                return ($scope.errorFields.vacancyName = true);
            }

            $scope.onSelectCategoryFromVacancyPosition($scope.vacancy.position);
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeClient = (newValue) => {
            $scope.vacancy.client = newValue;
            $scope.vacancy.clientId = newValue?.value || null;
            $scope.errorFields.clientName = !Boolean(newValue?.value);
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeCategory = (newValue) => {
            $scope.skillsModel = [];
            $scope.vacancy.category = newValue;
            $scope.errorFields.category = !Boolean(newValue?.value);
            newValue?.value && $scope.onGetSkillsByCategory($scope.vacancy.category.value);
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeExperience = (newValue) => {
            $scope.vacancy.experience = newValue;
            $scope.errorFields.experience = !Boolean(newValue?.value);
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeRole = (newValue) => {
            $scope.vacancy.role = newValue;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeEmploymentType = (newValue) => {
            $scope.vacancy.employmentType = newValue;
            $scope.errorFields.employmentType = !Boolean(newValue?.value);
            if (newValue.value === 'remote') {
                $scope.errorFields.country = !Boolean(newValue?.value);
                $scope.errorFields.city = !Boolean(newValue?.value);
            }
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeCountry = (newValue) => {
            $scope.vacancy.country = newValue;
            if ($scope.vacancy?.employmentType?.value !== 'remote') {
                $scope.errorFields.country = !Boolean(newValue?.value);
            }
            $scope.$$phase || $scope.$apply();
            getCities($scope.vacancy.country?.value);
        };

        const _openGoogleDropdown = () => {
            setTimeout(() => {
                const googleDropdown = document.querySelector('#googleLocation div div');
                if (googleDropdown) {
                    googleDropdown.click();
                }
            }, 50);
        };

        $scope.onChangeCity = (newValue) => {
            if (newValue?.value === '**go_to_google_location**' && newValue.goToGoogleLocation) {
                $scope.showGoogleLocationPicker = true;
                $scope.initialGooglePlacesInput = newValue.inputValue;
                _openGoogleDropdown();
            } else {
                $scope.vacancy.city = newValue;
            }

            if ($scope.vacancy?.employmentType?.value !== 'remote') {
                $scope.errorFields.city = !Boolean(newValue?.value);
            }
            $scope.$$phase || $scope.$apply();
        };

        const _getGooglePlaces = $rootScope.makeDebounce((input, callback) => {
            GoogleMaps.getGooglePlaces(input)
                .then((resp) =>
                    callback(
                        resp.predictions.map((location) => ({
                            ...location,
                            label: location.description,
                            value: location.place_id,
                        })),
                    ),
                )
                .catch((e) => console.error(e));
        }, 500);

        $scope.fetchGooglePlaces = (text, callback) => {
            if (!text || (text?.length && text?.length < $scope.inputSearchThreshold)) return callback();
            _getGooglePlaces(text, callback);
        };

        const _getGoogleLocationDetails = (placeId) => {
            GoogleMaps.getGooglePlaceDetails(placeId)
                .then((resp) => {
                    $scope.vacancy.region = { ...Service.convertToRegionObject(resp), fromGoogle: true };
                })
                .catch((e) => console.error(e));
        };

        $scope.googleLocationModel = null;
        $scope.onChangeGoogleLocation = (newValue) => {
            $scope.googleLocationModel = newValue;

            if (!newValue) {
                $scope.vacancy.city = null;
                $scope.vacancy.country = null;
                $scope.showGoogleLocationPicker = false;
                $scope.vacancy.region.fromGoogle = false;
            } else {
                newValue && _getGoogleLocationDetails(newValue.reference);
            }

            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeCurrency = (newValue) => {
            $scope.vacancy.currency = newValue;
            $scope.$$phase || $scope.$apply();
        };

        $scope.changeSalaryVisibility = (event) => {
            $scope.hideSalary = !$scope.hideSalary;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeNumberOfPositions = (newValue) => {
            $scope.vacancy.numberOfPositions = newValue;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangePriority = (newValue) => {
            $scope.vacancy.priority = newValue;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeBudget = (newValue) => {
            $scope.vacancy.budget = newValue;
            $scope.$$phase || $scope.$apply();
        };

        /**
         * @param value { 'publicAccess' | 'privateAccess' }
         */
        $scope.publicAccessChangeHandler = (currentValue) => {
            $scope.vacancy.vacanciesForCandidatesAccess =
                currentValue === 'publicAccess' ? 'privateAccess' : 'publicAccess';
            $scope.$$phase || $scope.$apply();
        };

        $scope.clearDateFinish = () => {
            $scope.dateFinish = '';
            $scope.vacancy.dateFinish = '';
        };

        $scope.clearDatePayment = () => {
            $scope.datePayment = '';
            $scope.vacancy.datePayment = '';
        };

        $scope.editCustomField = function (text, id) {
            if (text && $scope.validateTinyText?.length) {
                $scope.validateTinyText = $scope.cfSelectError.filter((error) => error.fieldId !== id);
            }

            if (text !== undefined) {
                $scope.fieldValueState = false;
                $scope.fieldValueState = $scope.vacancy.fieldValues.some((el) => el.field.fieldId === id);
                if ($scope.fieldValueState) {
                    if (text === '') {
                        $scope.vacancy.fieldValues.forEach((val, ind) => {
                            if (val.field.fieldId === id) $scope.vacancy.fieldValues.splice(ind, 1);
                        });
                    } else {
                        $scope.vacancy.fieldValues.forEach((val) => {
                            if (val.field.fieldId === id) val.value = text?.trimEnd();
                        });
                    }
                } else {
                    if (text !== '') {
                        $scope.vacancy.fieldValues.push({
                            objType: 'vacancy',
                            value: text?.trimEnd(),
                            field: {
                                fieldId: id,
                            },
                        });
                    }
                }
            }
        };

        // $scope.sendCustomFieldId = function (id) {
        //     $scope.editCustomId = id;
        //     $scope.editCustomFieldValueId = undefined;
        // };

        $scope.onSelectCategoryFromVacancyPosition = function (model) {
            $scope.checkRequiredExp();
            $rootScope.loading = true;
            Vacancy.onValidateVacancy({
                position: model,
            })
                .then((resp) => {
                    if (resp.status === 'ok') {
                        const { category } = resp.object;

                        $scope.typeVacancy = false;
                        $scope.typeVacancy = resp.object.it;
                        if (resp.object.industry) {
                            $scope.industry = resp.object.industry;
                            $scope.errorFields.industry = false;
                        }

                        if (category && $scope.vacancy.category !== category) {
                            $scope.vacancy.category = {
                                label: $filter('bigFirstLetterCategory')(category),
                                value: category,
                            };
                            $scope.vacancy.category && $scope.onGetSkillsByCategory($scope.vacancy.category.value);
                        }
                    } else {
                        console.error(resp.message);
                    }
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $scope.$$phase || $scope.$apply();
                });
        };

        // $scope.updateVacancyAutoTest = function (autoTest) {
        //     $scope.autoTest = angular.copy(autoTest);
        //     if (!$scope.autoTest.enabled && $scope.disableAutoTestResponse) $scope.disableAutoTestResponse();
        // };
        //
        // $scope.updateVacancyAutoTestResponse = function (autoTestResponse) {
        //     $scope.autoTestResponse = angular.copy(autoTestResponse);
        // };

        // $scope.setAutoTestResponseDisable = function (disableTestResponse) {
        //     $scope.disableAutoTestResponse = disableTestResponse;
        // };

        // $scope.removeErrorStyles = function () {
        //     $scope.errorFields.experience = false;
        //     $rootScope.wrongRole = false;
        // };

        // $scope.isNeedRegion = function () {
        //     if (!$scope.vacancy.employmentType.value) return true;
        //     else return $scope.vacancy.employmentType.value;
        // };

        $scope.visibilityChanged = function () {
            notificationService.success($filter('translate')('Vacancy visibility changed'));
        };

        $scope.onGetSkillsByCategory = function (category) {
            $scope.errorFields.category = false;
            $rootScope.loading = true;
            Vacancy.onGetSkillsByCategory(category)
                .then((resp) => {
                    $scope.skillsForCategory = resp.objects;

                    $scope.skillsOptions = [
                        {
                            label: 'systemSkillsGroup',
                            options: resp.objects.map((skill) => ({ label: skill.skill, value: skill })),
                        },
                        {
                            label: 'customSkillsGroup',
                            options: $scope.customSkills.map((skill) => ({ label: skill.skill, value: skill })),
                        },
                    ];
                })
                .finally(() => {
                    const category = $scope.vacancy.category.value;

                    if (category !== 'HR' && category !== 'SALES') $scope.errorFields.experience = false;
                    else if (
                        (category === 'HR' || category === 'SALES') &&
                        $scope.tryToSave &&
                        !$scope.vacancy.experience
                    )
                        $scope.errorFields.experience = true;
                    if (
                        !(
                            $scope.industry === 'IT' &&
                            category !== 'HR' &&
                            category !== 'SALES' &&
                            $scope.vacancy.skills &&
                            $scope.vacancy.skills.length === 0
                        )
                    )
                        $rootScope.skillError = false;
                    else if ($scope.industry === 'IT' && category !== 'HR' && category !== 'SALES' && $scope.tryToSave)
                        $rootScope.skillError = true;
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        };

        // $scope.onChangeEmployment = function (e) {
        //     $scope.errorFields.employmentType = false;
        //     if (e.value === 'remote') {
        //         $rootScope.errorRegion = false;
        //         setBorderStyle('#s2id_1_country_container');
        //         setBorderStyle('#s2id_1_city_container');
        //     }
        // };

        // $scope.onChangeCustomSelect = function (value, id) {
        //     if (value) {
        //         $scope.vacancy.fieldValues.push({
        //             objType: 'vacancy',
        //             value: value,
        //             field: {
        //                 fieldId: id,
        //             },
        //         });
        //
        //         if ($scope.cfSelectError?.length) {
        //             $scope.cfSelectError = $scope.cfSelectError.filter((error) => error.fieldId !== id);
        //         }
        //     }
        // };

        // $scope.onChangeRole = function (value, id) {
        //     $rootScope.wrongRole = false;
        // };

        $scope.showIncorrectRoleModal = function (role) {
            $rootScope.incorrectRole = role;
            $scope.modalInstance = $uibModal.open({
                animation: true,
                windowClass: 'secondary-modal',
                templateUrl: '../partials/modal/incorrect-role.html',
                size: '',
                resolve: function () {},
            });
        };
        $rootScope.closeModal = function () {
            $scope.modalInstance.close();
        };
        $rootScope.backToRequierments = function () {
            $rootScope.closeModal();
            $rootScope.onScrollToBlockBySelector('#skills-block');
            $rootScope.wrongRole = true;
        };

        $scope.validateRole = function () {
            if (
                $scope.skillsModel &&
                $scope.skillsModel.some((item) => item.experience.value) &&
                $scope.vacancy.position
            ) {
                const experienceList = ['e3_3years', 'e4_4years', 'e5_5years', 'e6_10years'];
                const juniorSkillsLength = $scope.skillsModel.filter((item) =>
                    ['e1_1year', 'e2_2years', ...experienceList].includes(item.experience.value),
                ).length;
                const middleSkillsLength = $scope.skillsModel.filter((item) =>
                    ['e2_2years', ...experienceList].includes(item.experience.value),
                ).length;
                const seniorSkillsLength = $scope.skillsModel.filter((item) =>
                    experienceList.includes(item.experience.value),
                ).length;

                const vacancyPosition = $scope.vacancy.position.toLowerCase();

                if (vacancyPosition.includes('middle') && vacancyPosition.includes('junior')) {
                    if (!juniorSkillsLength) {
                        $scope.showIncorrectRoleModal('Junior');
                    }
                    return juniorSkillsLength > 0;
                }

                if (vacancyPosition.includes('middle')) {
                    if (!middleSkillsLength) {
                        $scope.showIncorrectRoleModal('Middle');
                    }
                    return middleSkillsLength > 0;
                }

                if (vacancyPosition.includes('senior') && !seniorSkillsLength) {
                    $scope.showIncorrectRoleModal('Senior');
                    return false;
                }
                if (
                    vacancyPosition.toLowerCase().includes('lead') &&
                    !vacancyPosition.toLowerCase().includes('leadgen') &&
                    !vacancyPosition.toLowerCase().includes('lead generator') &&
                    !vacancyPosition.toLowerCase().includes('lead generation') &&
                    !seniorSkillsLength
                ) {
                    $scope.showIncorrectRoleModal('lead');
                    return false;
                }
                if (vacancyPosition.toLowerCase().includes('head') && !seniorSkillsLength) {
                    $scope.showIncorrectRoleModal('head');
                    return false;
                }

                const role = $scope.vacancy.role?.value;
                if (role && role.toLowerCase().includes('middle') && !middleSkillsLength) {
                    $scope.showIncorrectRoleModal('Middle');
                    return false;
                }
                if (role && role.toLowerCase().includes('teamlead') && !seniorSkillsLength) {
                    $scope.showIncorrectRoleModal('team lead');
                    return false;
                }
                if (role && role.toLowerCase().includes('lead') && !seniorSkillsLength) {
                    $scope.showIncorrectRoleModal('lead');
                    return false;
                }
                if (role && role.toLowerCase().includes('senior') && !seniorSkillsLength) {
                    $scope.showIncorrectRoleModal('Senior');
                    return false;
                }
                if (
                    $scope.vacancy.experience &&
                    $scope.vacancy.experience.value === 'e3_3years' &&
                    !juniorSkillsLength
                ) {
                    $scope.showIncorrectRoleModal('e3_3years');
                    return false;
                }
                if (
                    $scope.vacancy.experience &&
                    $scope.vacancy.experience.value === 'e4_4years' &&
                    !middleSkillsLength
                ) {
                    $scope.showIncorrectRoleModal('e4_4years');
                    return false;
                }
                if (
                    $scope.vacancy.experience &&
                    $scope.vacancy.experience.value === 'e5_5years' &&
                    !seniorSkillsLength
                ) {
                    $scope.showIncorrectRoleModal('e5_5years');
                    return false;
                }
                if (
                    $scope.vacancy.experience &&
                    $scope.vacancy.experience.value === 'e6_10years' &&
                    !seniorSkillsLength
                ) {
                    $scope.showIncorrectRoleModal('e6_10years');
                    return false;
                } else return true;
            } else return true;
        };

        const vacancyNamedShouldUseExp = [
            'ceo',
            'сео',
            'chief executive officer',
            'coo',
            'chief operating officer',
            'соо',
            'сто',
            'chief technology officer',
            'cto',
            'смо',
            'chief marketing officer',
            'cmo',
            'сфо',
            'chief financial officer',
            'cfo',
            'human resources director',
            'hrd',
            'сро',
            'chief product officer',
            'cpo',
            'сао',
            'chief accounting officer',
            'cao',
            'сіо',
            'chief information officer',
            'cio',
            'сво',
            'chief visionary officer',
            'cvo',
            'chief security officer',
            'cso',
            'chief technical officer',
            'chief technical officer (cto)',
            'hrd (human resources director)',
            'ceo/founder',
        ];

        const exceptNamed = ['leadgen', 'lead generator', 'lead generation'];

        const additionalWords = [
            'директор',
            'руководитель',
            'керівник',
            'начальник',
            'director',
            'head',
            'chief',
            'lead',
        ];

        $scope.checkRequiredExp = function () {
            if (exceptNamed.includes($scope.vacancy.position?.toLowerCase())) {
                $scope.showRequiredStar = false;
            } else {
                if (
                    $scope.vacancy.position
                        ?.toLowerCase()
                        .split(' ')
                        .some((word) => additionalWords.includes(word))
                ) {
                    $scope.showRequiredStar = true;
                } else {
                    $scope.showRequiredStar = vacancyNamedShouldUseExp.includes($scope.vacancy.position.toLowerCase());
                }
            }
        };

        $scope.validateNameAndExp = function () {
            $rootScope.needToShowNamedExp = true;
            $scope.needCheckManedExp = true;

            angular.forEach(exceptNamed, function (exp) {
                if ($scope.vacancy.position.toLowerCase() === exp) {
                    $rootScope.needToShowNamedExp = false;
                    setTimeout(() => {
                        $rootScope.needToShowNamedExp = true;
                    }, 0);
                }
            });

            if ($scope.vacancy.position) {
                if (
                    $scope.vacancy.position
                        .toLowerCase()
                        .split(' ')
                        .some((word) => additionalWords.includes(word))
                ) {
                    if ($rootScope.needToShowNamedExp && !$scope.vacancy.experience) {
                        $rootScope.showErrorExpCreateVacancy();
                        $scope.errorFields.experience = true;
                        $scope.needCheckManedExp = false;
                        setTimeout(() => {
                            $scope.needCheckManedExp = true;
                        }, 0);
                    }
                }
            }

            angular.forEach(vacancyNamedShouldUseExp, function (val) {
                if ($scope.vacancy.position) {
                    if (
                        $scope.vacancy.position.toLowerCase() === val &&
                        !$scope.vacancy.experience &&
                        $scope.needCheckManedExp
                    ) {
                        if ($rootScope.needToShowNamedExp) {
                            $scope.errorFields.experience = true;
                            $rootScope.showErrorExpCreateVacancy();
                        }
                    }
                }
            });
        };

        $scope.onGetSkillsAndSaveVacancyHandler = function () {
            $scope.disableButton = true;
            $rootScope.loading = true;

            // $scope.allSkills.forEach((item) => {
            //     if (item.skill) item.skillId = $scope.skillsObject[item.skill.toLowerCase()];
            // });

            Vacancy.onValidateVacancy({
                description: $scope.vacancy.descr,
                category: $scope.vacancy.category?.value || null,
                skills: $scope.skillsModel
                    ?.filter((skill) => skill.skill)
                    .map((item) => ({
                        ...item,
                        skill: item.skill?.label || null,
                        new: item.skill?.new || false,
                        skillId: item.skill?.value.skillId || null,
                        experience: item.experience?.value,
                        type: item?.type || 'general',
                        mustHave: item?.mustHave,
                    })),
            })
                .then((resp) => {
                    $scope.disableButton = false;
                    if (resp.object.skillsFromDescription && resp.object.skillsFromDescription.length > 0) {
                        const skillsFromDescription = resp.object.skillsFromDescription.map((item) => {
                            // $scope.skillsObject[item.skill.toLowerCase()] = item.skillId;
                            return {
                                skill: {
                                    label: item.skill,
                                    value: {
                                        ...item,
                                        type: item.type || 'custom',
                                    },
                                },
                                experience: { label: 'e00_no_experience', value: 'e00_no_experience' },
                                new: false,
                                willPlus: false,
                            };
                        });
                        showSkillsFromDescriptionModal(skillsFromDescription);
                    } else {
                        if (!$scope.vacancy.priority) {
                            $scope.vacancy.priority = $scope.prioritiesOptions[1];
                        }

                        $scope.save();
                    }
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $scope.$$phase || $scope.$apply();
                });
        };

        function showSkillsFromDescriptionModal(skills) {
            class SkillsFromDescriptionModal {
                constructor(skills) {
                    this.skills = skills;
                }
                onSetSkillsFromDescription(skills) {
                    $scope.skillsModel = [...$scope.skillsModel, ...skills];
                    $rootScope.skillError = false;

                    if ($scope.errorFields.skills) $scope.errorFields.skills = false;
                    $rootScope.closeModal();
                    $rootScope.onScrollToBlockBySelector('#skills-block');
                    $scope.$$phase || $scope.$apply();
                }
                onSaveVacancy() {
                    $scope.save();
                    $rootScope.closeModal();
                }
            }
            $scope.modalInstance = $uibModal.open({
                templateUrl: '../partials/modal/skills-from-description.html',
                windowClass: 'secondary-modal',
                controller: ['skills', SkillsFromDescriptionModal],
                controllerAs: 'vm',
                resolve: {
                    skills: () => {
                        return skills;
                    },
                    onSetSkillsFromDescription: () => {
                        return this.onSetSkillsFromDescription;
                    },
                    onSaveVacancy: () => {
                        return this.onSaveVacancy;
                    },
                },
            });
        }

        $scope.scrollToError = function () {
            if (
                $scope.errorFields.industry ||
                $scope.errorFields.vacancyName ||
                $scope.errorFields.clientName ||
                $scope.errorFields.category ||
                $scope.errorFields.country ||
                $scope.errorFields.city ||
                $scope.errorFields.skills ||
                $scope.errorFields.experience
            ) {
                $rootScope.onScrollToBlockBySelector('#headerOfVacancyPage');
            } else {
                setTimeout(() => {
                    for (const div of document.querySelectorAll('div.error-text')) {
                        if (
                            div.textContent.includes($filter('translate')('Empty mandatory field')) &&
                            (div.classList.contains('ng-scope') || div.classList.contains('show-error-message'))
                        ) {
                            div.scrollIntoView({ block: 'center', behavior: 'smooth' });
                            break;
                        }

                        if (
                            div.textContent.includes($filter('translate')('Maximum number of characters exceeded by'))
                        ) {
                            div.scrollIntoView({ block: 'center', behavior: 'smooth' });
                            break;
                        }
                    }
                }, 200);
            }
        };

        $scope.save = function () {
            const { position, client, category, employmentType, role, country, city, priority } = $scope.vacancy;

            $scope.tryToSave = true;

            // const isNeedRegion = $scope.isNeedRegion();
            if (!$scope.industry) {
                $scope.errorFields.industry = true;
            }

            if (!position) {
                $scope.errorFields.vacancyName = true;
            }

            if (!client?.value) {
                $scope.errorFields.clientName = true;
            }

            if ($scope.industry === 'IT' && !category?.value) {
                $scope.errorFields.category = true;
            }

            if (
                $scope.industry === 'IT' &&
                (!$scope.skillsModel.length || !$scope.skillsModel.some((item) => item.skill?.value))
            ) {
                $scope.errorFields.skills = true;
            }

            if (
                $scope.industry === 'IT' &&
                $scope.skillsModel.length &&
                $scope.skillsModel.some((item) => item.skill?.value)
            ) {
                $scope.errorFields.skills = false;
            }

            if (!employmentType?.value || employmentType?.value !== 'remote') {
                if (!country?.value && !$scope.vacancy.region?.fromGoogle) {
                    $scope.errorFields.country = true;
                }
                if (!city?.value && !$scope.vacancy.region?.fromGoogle) {
                    $scope.errorFields.city = true;
                }
            }

            if ($rootScope.skillsToSave && $rootScope.skillsToSave.length > 0) {
                $scope.vacancy.skills = $rootScope.skillsToSave.map((item) => {
                    if (item.new) {
                        return {
                            skillName: item.skill,
                            experience: item.experience,
                            mustHave: !item.willPlus,
                            type: item.type || 'custom',
                        };
                    } else {
                        return {
                            skillId: +$scope.skillsObject[item.skill.toLowerCase()],
                            experience: item.experience,
                            mustHave: !item.willPlus,
                            type: item.type || 'custom',
                        };
                    }
                });
            } else $scope.vacancy.skills = [];

            validateSkills();
            if ($scope.industry === 'IT' && (category === 'HR' || category === 'SALES') && !$scope.vacancy.experience)
                $scope.errorFields.experience = true;

            if (!$scope.industry) $scope.errorFields.industry = true;
            else if ($scope.industry === 'IT') $scope.vacancy.industry = 'IT';
            else if ($scope.industry !== 'IT') $scope.vacancy.industry = null;
            if (!category && $scope.industry === 'IT') $scope.errorFields.category = true;
            if (!employmentType?.value) $scope.errorFields.employmentType = true;
            if ($scope.industry && $scope.vacancy.position) {
                if (category && $scope.industry === 'IT') {
                    $scope.validateNameAndExp();
                }
                if ($scope.industry !== 'IT') {
                    $scope.validateNameAndExp();
                }
            }
            $scope.vacancy.fieldValues = $scope.vacancy.fieldValues.filter((item) => item.value || item.dateTimeValue);
            $scope.validateTinyText = CustomField.validateTinyTextFields(
                $scope.vacancy.fieldValues,
                $scope.allObjCustomField,
                Service.getClearFieldLength,
            );
            $scope.validateMandatory = $scope.validateMandatoryFields();

            // if (!$scope.validateRole()) return;

            //validation
            if (
                !(
                    Object.values($scope.errorFields).some((field) => field) ||
                    $scope.validateMandatory ||
                    $scope.validateTinyText.cfErrorStringFlag ||
                    $rootScope.skillError
                )
            ) {
                if (
                    $scope.vacancy.salaryTo >= $scope.vacancy.salaryFrom ||
                    !$scope.vacancy.salaryTo ||
                    !$scope.vacancy.salaryFrom
                ) {
                    if ($scope.vacancy.salaryFrom >= 999999999 || $scope.vacancy.salaryTo >= 999999999) {
                        $rootScope.onScrollToBlockBySelector('#salaryBlock');
                        return notificationService.error(
                            $filter('translate')('Vacancy filled incorrectly. Please try again.'),
                        );
                    }

                    if (
                        $scope.skillsModel.filter((skill) => skill?.skill).length !==
                        new Set($scope.skillsModel.filter((skill) => skill?.skill).map(({ skill }) => skill?.label))
                            .size
                    ) {
                        return notificationService.error(
                            $filter('translate')('You have the same skills. They must be unique.'),
                        );
                    }

                    $rootScope.loading = true;
                    if ($scope.vacancy.experience && $scope.vacancy.experience.value) {
                        $scope.vacancy.experience = $scope.vacancy.experience.value || null;
                    }
                    $scope.vacancy.dateFinish = Date.parse($scope.dateFinish);
                    $scope.vacancy.datePayment = Date.parse($scope.datePayment);
                    if ($scope.vacancy.fieldValues.dateTimeValue != undefined) {
                        $('.datepickerOfCustom').datetimepicker('setDate', new Date($scope.editCustomValue));
                    }
                    if ($scope.vacancy.fieldValues.dateTimeValue != undefined) {
                        $('.datepickerOfCustomTime').datetimepicker('setDate', new Date($scope.editCustomValue));
                    }
                    if ($scope.copyVacancy && $scope.copyVacancy[0]) {
                        $scope.vacancy.interviews = $scope.copyVacancy[0].interviews;
                    }
                    // Service.deleteUnnecessaryFields($scope.vacancy);
                    $scope.payload = { ...$scope.vacancy };

                    $scope.payload.fieldValues = $scope.vacancy.fieldValues.map((item) => {
                        if (item.dateTimeValue) {
                            return {
                                objType: item.objType,
                                dateTimeValue: item.dateTimeValue,
                                field: {
                                    fieldId: item.field.fieldId,
                                },
                            };
                        } else {
                            return {
                                objType: item.objType,
                                value: $filter('removeHtmlTagFromString')(item.value),
                                field: {
                                    fieldId: item.field.fieldId,
                                },
                            };
                        }
                    });

                    if ($scope.allObjCustomField?.length) {
                        const selectFieldIds = $scope.allObjCustomField
                            ?.filter(({ type }) => type === 'select')
                            .map(({ fieldId }) => fieldId);

                        $scope.payload.fieldValues = $scope.payload.fieldValues.filter(
                            ({ field }) => !selectFieldIds.includes(field.fieldId),
                        );

                        $scope.payload.fieldValues.push(
                            ...$scope.allObjCustomField
                                ?.filter(({ type }) => type === 'select')
                                .map(({ objType, value, fieldId }) => ({
                                    objType,
                                    value: value?.value,
                                    field: { fieldId },
                                })),
                        );
                    }

                    if ($scope.payload.customFields) delete $scope.payload.customFields;

                    if ($scope.languages) {
                        $scope.payload.languages = $scope.languages
                            .filter((lang) => lang?.name)
                            .map(({ name, level }) => ({
                                name: name.label,
                                level: level?.value,
                            }));
                    }

                    $scope.payload.recallTemplateId = $scope.currentRecall.id;

                    $scope.payload.currency =
                        ($scope.vacancy.currency && $scope.vacancy.currency.value) || $scope.vacancy.currency;
                    $scope.payload.employmentType = $scope.returnValue($scope.vacancy.employmentType.value);

                    $scope.disableButton = true;

                    //Redesign
                    $scope.payload['category'] = { category: category?.value || null };

                    $scope.payload['role'] = role?.value;

                    $scope.payload['skills'] = $scope.skillsModel
                        .filter((skill) => skill?.skill)
                        .map(({ skill, experience, mustHave }) => ({
                            skillId: skill?.value?.skillId,
                            skillName: skill?.value.skillId ? null : skill.label,
                            type: skill?.value.type || 'custom',
                            experience: experience.value,
                            mustHave,
                        }));

                    if ($scope.vacancy.region && $scope.vacancy.region.fromGoogle) {
                        $scope.payload['region'] = $scope.vacancy.region;
                    } else {
                        $scope.payload['region'] = {
                            country: country?.value,
                            city: city?.value?.cityEn,
                            area: city?.value?.areaEn,
                            fullName: city?.label,
                            googlePlaceId: city?.value.googlePlaceId,
                            lang: $rootScope.currentLang,
                        };
                    }

                    $scope.payload['priority'] = priority?.value || $scope.prioritiesOptions[1].value;
                    $scope.payload['hideSalary'] = $scope.hideSalary;

                    //Save vacancy
                    Vacancy.add(
                        $scope.payload,
                        function (resp) {
                            if (angular.equals(resp.status, 'ok')) {
                                delete $rootScope.region;
                                if ($rootScope.vacancyHidden) hideVacancy(resp.object.vacancyId);
                                $rootScope.loading = false;
                                if ($scope.fileForSave.length > 0) {
                                    angular.forEach($scope.fileForSave, function (valI, i) {
                                        Vacancy.addFileFromCache(
                                            {
                                                attId: valI.attId,
                                                vacancyId: resp.object.vacancyId,
                                                fileName: valI.fileName,
                                            },
                                            function (resp) {},
                                        );
                                        if ($scope.fileForSave.length - 1 == i) {
                                            $rootScope.loading = false;
                                            notificationService.success(
                                                $filter('translate')('vacancy_save_1') +
                                                    $scope.vacancy.position +
                                                    $filter('translate')('vacancy_save_2'),
                                            );
                                            $location.path('vacancies/' + resp.object.localId);
                                        }
                                    });
                                } else {
                                    $rootScope.loading = false;
                                    $rootScope.redirectToSuggestions = true;
                                    $location.path('vacancies/' + resp.object.localId);
                                    if ($rootScope.me.recrutRole != 'client') {
                                        setTimeout(() => {
                                            $rootScope.showSuccessCreateVacancy();
                                        }, 2000);
                                    }
                                }
                                if ($scope.autoTest.enabled && $scope.autoTest.test)
                                    $scope.autoTest.onSave(resp.object.vacancyId);
                                if ($scope.autoTestResponse.enabled && parseInt($scope.autoTestResponse.value))
                                    $scope.autoTestResponse.onSave(resp.object.vacancyId);
                                if ($scope.applicationId) {
                                    Application.onSetApplicationStatus({
                                        localId: $scope.applicationId,
                                        vacancyLocalId: resp.object.localId,
                                        vacancyId: resp.object.vacancyId,
                                        status: 'inwork',
                                    }).then((resp) => {
                                        if (resp.status === 'ok') {
                                            notificationService.success(
                                                $filter('translate')('Application transferred to vacancy'),
                                            );
                                        }
                                    });
                                }
                            } else {
                                $rootScope.loading = false;
                                notificationService.error(resp.message);
                            }
                            $scope.disableButton = false;
                            $localStorage.remove('copyVacancy');
                        },
                        function (err) {
                            $rootScope.loading = false;
                            //notificationService.error($filter('translate')('service temporarily unvailable'));
                            $localStorage.set('vacancyForSave', $scope.vacancy);
                            $cookies.url = $location.$$url;
                            $cookies.cfauth = 'false';
                            $window.location.replace('/');
                        },
                    );
                } else {
                    $scope.salaryError = true;
                    $rootScope.onScrollToBlockBySelector('#salaryBlock');
                    notificationService.error($filter('translate')('Vacancy filled incorrectly. Please try again.'));
                }
            } else {
                notificationService.error($filter('translate')('Vacancy filled incorrectly. Please try again.'));
                $scope.scrollToError();
            }
        };
        $scope.salaryErrorHandler = function () {
            if (
                $scope.vacancy.salaryTo >= $scope.vacancy.salaryFrom ||
                !$scope.vacancy.salaryTo ||
                !$scope.vacancy.salaryFrom
            )
                $scope.salaryError = false;
            else $scope.salaryError = true;
        };
        $scope.removeSalaryError = function () {
            $scope.salaryError = false;
        };

        $scope.checkDateTime = function (id, fieldValueId, val, name) {
            $scope.editCustomId = id;
            let flag = false;
            switch (name) {
                case 'datepickerOfCustomEdit':
                    val = $rootScope.TimeMinusTimeZone(Date.parse(val));

                    if (val == null) {
                        $scope.datepickerOfCustomEdit = '';
                        $scope.vacancy.fieldValues = $scope.vacancy.fieldValues.filter(
                            ({ field }) => field.fieldId !== id,
                        );
                        $scope.$$phase || $scope.$apply();
                    } else {
                        if (typeof val !== 'number') $scope.datepickerOfCustomEdit = Date.parse(val);
                        else $scope.datepickerOfCustomEdit = val;
                        if ($scope.vacancy.fieldValues && $scope.vacancy.fieldValues.length > 0) {
                            angular.forEach($scope.vacancy.fieldValues, function (val) {
                                if (val.field.fieldId == $scope.editCustomId) {
                                    val.dateTimeValue = $scope.datepickerOfCustomEdit;
                                    flag = true;
                                }
                            });
                            if (!flag) {
                                $scope.vacancy.fieldValues.push({
                                    objType: 'vacancy',
                                    dateTimeValue: $scope.datepickerOfCustomEdit,
                                    field: {
                                        fieldId: $scope.editCustomId,
                                    },
                                });
                            }
                        } else {
                            $scope.vacancy.fieldValues.push({
                                objType: 'vacancy',
                                dateTimeValue: $scope.datepickerOfCustomEdit,
                                field: {
                                    fieldId: $scope.editCustomId,
                                },
                            });
                        }
                        if ($scope.cfDateError?.length && val) {
                            $scope.cfDateError = $scope.cfDateError.filter((error) => error.fieldId !== id);
                        }
                    }
                    return $scope.vacancy.fieldValues;
                case 'datepickerOfCustomEditTime':
                    if (val == null) {
                        $scope.datepickerOfCustomEditTime = '';
                        $scope.vacancy.fieldValues = $scope.vacancy.fieldValues.filter(
                            ({ field }) => field.fieldId !== id,
                        );

                        return angular.forEach($('.editDate' + '.' + id), function (nval) {
                            $(nval).attr('mdp-placeholder', $filter('translate')('Choose date'));
                            $(nval).find('label')[0].innerHTML = $filter('translate')('Choose date');
                            $(nval).find('input')[0].value = $filter('translate')('Choose date');
                        });
                    } else {
                        if (typeof val !== 'number') $scope.datepickerOfCustomEditTime = Date.parse(val);
                        else $scope.datepickerOfCustomEditTime = val;
                        if ($scope.vacancy.fieldValues && $scope.vacancy.fieldValues.length > 0) {
                            angular.forEach($scope.vacancy.fieldValues, function (val) {
                                if (val.field.fieldId == $scope.editCustomId) {
                                    val.dateTimeValue = $scope.datepickerOfCustomEditTime;
                                    flag = true;
                                }
                            });
                            if (!flag) {
                                $scope.vacancy.fieldValues.push({
                                    objType: 'vacancy',
                                    dateTimeValue: $scope.datepickerOfCustomEditTime,
                                    field: {
                                        fieldId: $scope.editCustomId,
                                    },
                                });
                            }
                        } else {
                            $scope.vacancy.fieldValues.push({
                                objType: 'vacancy',
                                dateTimeValue: $scope.datepickerOfCustomEditTime,
                                field: {
                                    fieldId: $scope.editCustomId,
                                },
                            });
                        }
                        if ($scope.cfDateTimeError?.length && val) {
                            $scope.cfDateTimeError = $scope.cfDateTimeError.filter((error) => error.fieldId !== id);
                        }
                    }
                    return $scope.vacancy.fieldValues;
            }
        };

        $scope.deleteDate = function (id) {
            $scope.editCustomId = id;
            $scope.vacancy.fieldValues.forEach((nval, ind) => {
                if (id == nval.field.fieldId) {
                    nval.dateTimeValue = '';
                    $scope.vacancy.fieldValues.splice(ind, 1);
                }
            });
        };
        $scope.validSalary = function (event) {
            if (event.keyCode == 43 || event.keyCode == 45 || event.keyCode == 101 || event.keyCode == 69)
                event.preventDefault();
        };
        $scope.validateNumberOfPosition = function () {
            let count = +$scope.vacancy.numberOfPositions;
            if (count === 0) return '';
            count = '' + count;
            if (count > 100) {
                $scope.errorFields.numberOfPositions = true;
                if (count.slice(0, 3) === '100') {
                    return count.slice(0, 3);
                } else return count.slice(0, 2);
            } else {
                $scope.errorFields.numberOfPositions = false;
                return count;
            }
        };
        $scope.setDefaultSkills = function () {
            // $scope.vacancy.category = null;
            $scope.errorFields.industry = false;
            validateSkills();
            if ($scope.industry !== 'IT') {
                $scope.errorFields.category = false;
                $scope.errorFields.skills = false;
                $scope.errorFields.experience = false;
            }
            if ($scope.industry === 'IT' && !$scope.vacancy.employmentType?.value && $scope.tryToSave)
                $scope.errorFields.category = !!(
                    !$scope.vacancy.category?.value &&
                    $scope.industry === 'IT' &&
                    $scope.tryToSave
                );
            $rootScope.vacancyIndustry = $scope.industry;
        };

        function setFieldsToVacancy(allFields) {
            let fieldsToVacancy = {};
            $scope.applicationId = allFields.localId;
            Object.keys(allFields).forEach((key) => {
                if (
                    allFields.template.optional.includes(key) ||
                    key === 'industry' ||
                    key === 'position' ||
                    key === 'category' ||
                    key === 'role' ||
                    key === 'region' ||
                    key === 'salaryFrom' ||
                    key === 'salaryTo' ||
                    key === 'currency' ||
                    key === 'customFields' ||
                    key === 'client' ||
                    key === 'languages' ||
                    key === 'skills' ||
                    key === 'experience' ||
                    key === 'employmentType' ||
                    key === 'budget' ||
                    key === 'numberOfPositions' ||
                    key === 'priority' ||
                    key === 'responsibilities' ||
                    key === 'modificationDate' ||
                    key === 'datePayment' ||
                    key === 'dateFinish' ||
                    key === 'status'
                ) {
                    fieldsToVacancy[key] = allFields[key];
                }
            });
            delete fieldsToVacancy.companyDescription;
            delete fieldsToVacancy.offer;
            delete fieldsToVacancy.responsibilities;
            return fieldsToVacancy;
        }

        function validateSkills() {
            if (
                $scope.industry === 'IT' &&
                $scope.vacancy.category?.value &&
                $scope.vacancy.category?.value !== 'HR' &&
                $scope.vacancy.category?.value !== 'SALES' &&
                $scope.skillsModel.length === 0
            ) {
                $rootScope.skillError = true;
                $rootScope.$$phase || $rootScope.$apply();
            } else {
                $rootScope.skillError = false;
                $rootScope.$$phase || $rootScope.$apply();
            }
        }

        function showSkillsFromDescriptionModal(skillsFromDescription) {
            class SkillsFromDescriptionModal {
                constructor(skills) {
                    this.skills = skills;
                }

                onSetSkillsFromDescription(skills) {
                    $scope.skillsModel = $scope.skillsModel.concat(skillsFromDescription);
                    if ($scope.errorFields.skills) $scope.errorFields.skills = false;
                    // $rootScope.skillError = false;
                    $rootScope.closeModal();
                    $rootScope.onScrollToBlockBySelector('#skills-block');
                }

                onSaveVacancy() {
                    $scope.save();
                    $rootScope.closeModal();
                }
            }

            $scope.modalInstance = $uibModal.open({
                templateUrl: '../partials/modal/skills-from-description.html',
                controller: ['skills', SkillsFromDescriptionModal],
                controllerAs: 'vm',
                resolve: {
                    skills: () => {
                        return skillsFromDescription;
                    },
                    onSetSkillsFromDescription: () => {
                        return this.onSetSkillsFromDescription;
                    },
                    onSaveVacancy: () => {
                        return this.onSaveVacancy;
                    },
                },
            });
        }

        function setDescriptionFromApplication() {
            let descr = '';
            if ($rootScope.vacancyApplicationToSave.responsibilities) {
                descr = $rootScope.vacancyApplicationToSave.responsibilities;
            }
            if ($rootScope.vacancyApplicationToSave.companyDescription) {
                if (descr) {
                    descr = descr + '</br>' + $rootScope.vacancyApplicationToSave.companyDescription;
                } else descr = $rootScope.vacancyApplicationToSave.companyDescription;
            }
            if ($rootScope.vacancyApplicationToSave.offer) {
                if (descr) {
                    descr = descr + '</br>' + $rootScope.vacancyApplicationToSave.offer;
                } else descr = $rootScope.vacancyApplicationToSave.offer;
            }
            return descr;
        }

        function setBorderStyle(selector, style) {
            const element = document.querySelector(selector);
            if (!element) return;

            switch (style) {
                case 'error':
                    // element.style.border = '1px solid #b94a48';
                    element.style.borderRadius = '6px';
                    break;

                default:
                    element.style.border = 'inherit';
            }
        }

        function hideVacancy(vacId) {
            Vacancy.changeVisibility('hidden', vacId).then(
                (resp) => {},
                (error) => {
                    console.error('/Error in changeVisibility request', error);
                    notificationService.error('Request hideVacancy status code: ' + error.status);
                },
            );
        }

        function setCustomFields() {
            if (!$rootScope.vacancyApplicationToSave.customFields?.length) return;

            $scope.allObjCustomField = $rootScope.vacancyApplicationToSave.customFields;

            $scope.allObjCustomField?.forEach((field, index) => {
                if ((field.type === 'date' || field.type === 'datetime') && field.fieldValue) {
                    field['value'] = new Date(field.fieldValue.dateTimeValue);
                    $scope.vacancy.fieldValues.push({
                        objType: 'vacancy',
                        fieldValueId: field.fieldValue.fieldValueId,
                        dateTimeValue: field.fieldValue.dateTimeValue,
                        field: {
                            fieldId: field.fieldId,
                        },
                    });
                }

                if (field.type === 'select') {
                    if (field?.fieldValue?.value) {
                        field.value = field.params.filter((param) => param.value === field?.fieldValue?.value)[0];
                        $scope.vacancy.fieldValues.push({
                            objType: 'vacancy',
                            fieldValueId: field.fieldValue.fieldValueId,
                            value: field.fieldValue.value,
                            field: {
                                fieldId: field.fieldId,
                            },
                        });
                    }
                }

                if (field.type === 'string' && field.fieldValue?.value) {
                    $rootScope
                        .waitForCondition(
                            300,
                            100,
                            () =>
                                CKEDITOR?.instances &&
                                Object.values(CKEDITOR.instances).every((editor) => editor.status === 'ready'),
                        )
                        .then(() => {
                            setTimeout(() => {
                                $scope.fieldValues[index.toString()].value = field?.fieldValue?.value;
                                $scope.editCustomField(field?.fieldValue?.value, field.fieldId);
                                $scope.$$phase || $scope.$apply();
                            }, 1000);
                        });
                }
            });
        }

        function initRest() {
            if ($scope.vacancy.category) {
                $rootScope.loading = true;
                Vacancy.onGetSkillsByCategory()
                    .then((resp) => {
                        $scope.customSkills = resp.objects;
                        $rootScope.customSkills = resp.objects;
                        if ($scope.customSkills) {
                            $scope.customSkills.forEach((item) => {
                                $scope.skillsObject[item.skill.toLowerCase()] = item.skillId;
                            });
                        }
                        return Vacancy.onGetSkillsByCategory($scope.vacancy.category.value);
                    })
                    .then((resp) => {
                        $scope.skillsForCategory = resp.objects;
                        $rootScope.skills = resp.objects;
                        if ($scope.skillsForCategory && $scope.skillsForCategory.length) {
                            $scope.skillsForCategory.forEach((item) => {
                                $scope.skillsObject[item.skill.toLowerCase()] = item.skillId;
                            });
                        }

                        $scope.skillsOptions = [
                            {
                                label: 'systemSkillsGroup',
                                options: resp.objects.map((skill) => ({ label: skill.skill, value: skill })),
                            },
                            {
                                label: 'customSkillsGroup',
                                options: $scope.customSkills.map((skill) => ({ label: skill.skill, value: skill })),
                            },
                        ];

                        $scope.isLoadedSkillsByCategory = true;
                    })
                    .catch((err) => console.error(err.message))
                    .finally(() => {
                        if (!$scope.vacancy.skills || !$scope.vacancy.skills.length) {
                            $scope.allSkills = [
                                {
                                    skill: undefined,
                                    experience: 'e00_no_experience',
                                    new: false,
                                    willPlus: false,
                                },
                            ];
                        } else {
                            $scope.allSkills = $scope.vacancy.skills.map((item) => {
                                return {
                                    dm: item.dm,
                                    experience: item.experience,
                                    id: item.id,
                                    willPlus: !item.mustHave,
                                    skillId: item.skillId,
                                    skill: item.skillName,
                                    vacancyId: item.vacancyId,
                                    type: item.type,
                                };
                            });
                        }
                        $rootScope.loading = false;
                        $scope.$$phase || $scope.$apply();
                    });
            } else {
                $rootScope.loading = true;
                Vacancy.onGetSkillsByCategory()
                    .then((resp) => {
                        $scope.customSkills = resp.objects;
                        $rootScope.customSkills = resp.objects;
                        if ($scope.customSkills) {
                            $scope.customSkills.forEach((item) => {
                                $scope.skillsObject[item.skill.toLowerCase()] = item.skillId;
                            });
                        }

                        $scope.skillsOptions = [
                            {
                                label: 'customSkillsGroup',
                                options: $scope.customSkills.map((skill) => ({ label: skill.skill, value: skill })),
                            },
                        ];
                    })
                    .catch((err) => console.error(err.message))
                    .finally(() => {
                        if (!$scope.vacancy.skills || !$scope.vacancy.skills.length) {
                            $scope.allSkills = [
                                {
                                    skill: undefined,
                                    experience: 'e00_no_experience',
                                    new: false,
                                    willPlus: false,
                                },
                            ];
                        } else {
                            $scope.allSkills = $scope.vacancy.skills.map((item) => {
                                return {
                                    dm: item.dm,
                                    experience: item.experience,
                                    id: item.id,
                                    willPlus: !item.mustHave,
                                    skillId: item.skillId,
                                    skill: item.skillName,
                                    vacancyId: item.vacancyId,
                                    type: item.type,
                                };
                            });
                        }
                        $rootScope.loading = false;
                        $scope.$$phase || $scope.$apply();
                    });
            }

            Vacancy.onGetAllCategories().then((resp) => {
                $scope.categories = resp.objects.map((category) => ({
                    label: $filter('bigFirstLetterCategory')(category),
                    value: category,
                }));
            });
            Client.init();
            Client.all(Client.search.params, function (response) {
                if ($rootScope.me.recrutRole == 'client') {
                    if (response.objects) {
                        $scope.vacancy.clientId = response.objects[0];
                    } else {
                        notificationService.error($filter('translate')('Your company removed'));
                        $window.location.replace('!#/vacancies');
                    }
                }
            });
            Vacancy.all(Vacancy.searchOptions(), function (response) {
                $rootScope.objectSize = response['objects'] != undefined ? response['total'] : 0;
            });
            Company.getParams(function (resp) {
                $scope.companyParams = resp.object;
                $rootScope.publicLink =
                    $location.$$protocol +
                    '://' +
                    $location.$$host +
                    '/i/' +
                    $scope.companyParams.nameAlias +
                    '-vacancies';
            });
        }

        const setLanguagesFromApplication = (languages) => {
            if (!languages || !languages.length) return;

            Service.onGetLanguagesCached().then((languages) => {
                const langKey = $rootScope.getCurrentLangKey($rootScope.currentLang);
                $scope.languagesOptions = languages.map((lang) => ({
                    label: $filter('bigFirstLetter')(lang.translation[langKey]),
                    value: lang.translation,
                }));

                $scope.languages = $scope.vacancy.languages.map(({ name, level }) => {
                    const { label, value } = $scope.languagesOptions.find((lang) => lang.value.english === name);

                    $scope.languagesOptions = $scope.languagesOptions.filter(({ value }) => value.english !== name);

                    return {
                        name: { label, value },
                        level: { label: level, value: level },
                    };
                });
                $scope.$$phase || $scope.$apply();
            });
        };

        function setModelFromApplication() {
            $scope.isApplication = true;
            $scope.vacancy.descr = setDescriptionFromApplication();

            let allFields = setFieldsToVacancy($rootScope.vacancyApplicationToSave);
            const { category, employmentType, priority, currency, role, experience, languages } = allFields;

            $scope.vacancy = {
                ...$scope.vacancy,
                ...allFields,
                category: category && { label: category[0] + category.slice(1).toLowerCase(), value: category },
                employmentType: { label: employmentType, value: employmentType },
                currency: currency && { label: currency, value: currency },
                role: role && { label: role, value: role },
                experience: experience && $scope.experience.find(({ value }) => value === experience),
            };

            priority && $scope.onChangePriority(prioritiesOptions.find(({ value }) => value === priority));

            if (allFields?.skills.length) {
                $scope.skillsModel = allFields.skills.map((skill) => ({
                    skill: {
                        label: skill.skillName,
                        value: skill,
                    },
                    experience: {
                        label: skill.experience || 'e00_no_experience',
                        value: skill.experience || 'e00_no_experience',
                    },
                    willBePlus: !skill.mustHave,
                }));
            }

            languages?.length && setLanguagesFromApplication(languages);

            $scope.checkRequiredExp();

            if ($scope.vacancy.skills.length) {
                $scope.vacancy.skills.forEach((item) => {
                    $scope.skillsObject[item.skillName.toLowerCase()] = item.skillId;
                });
            }
            $scope.vacancy.industry === 'IT' ? ($scope.industry = 'IT') : ($scope.industry = 'noIT');
            if ($scope.vacancy.datePayment != undefined) {
                $scope.datePayment = new Date($scope.vacancy.datePayment);
            }
            if ($scope.vacancy.dateFinish != undefined) {
                $scope.dateFinish = new Date($scope.vacancy.dateFinish);
            }
            if ($scope.vacancy.category && $scope.vacancy.category.category) {
                $scope.vacancy.category = $scope.vacancy.category.category;
            }
            setCustomFields();

            if ($scope.vacancy?.region?.country) {
                $scope.vacancy.country = {
                    label: $scope.vacancy.region.displayCountry,
                    value: $scope.vacancy.region.displayCountry,
                };
                getCities($scope.vacancy.region.displayCountry);
            }
            if ($scope.vacancy?.region?.city) {
                $scope.vacancy.city = {
                    label: $scope.vacancy.region.displayCity,
                    value: $scope.vacancy.region.googlePlaceId,
                };
            }
        }

        function setMethodsForCustomFieldsWithVacancyApplication() {
            $scope.addCustomFieldParams = function (value, id, fieldValueId) {
                $scope.fieldValueState = false;
                $scope.fieldState = false;
                if ($scope.vacancy.fieldValues && $scope.vacancy.fieldValues.length > 0)
                    $scope.fieldValueState = $scope.vacancy.fieldValues.some((el) => el.fieldValueId === fieldValueId);
                if ($scope.fieldValueState && fieldValueId) {
                    if (value === '') {
                        $scope.vacancy.fieldValues.forEach((val, ind) => {
                            if (val.fieldValueId === fieldValueId && !val.dateTimeValue)
                                $scope.vacancy.fieldValues.splice(ind, 1);
                        });
                    } else {
                        $scope.vacancy.fieldValues.forEach((val) => {
                            if (val.fieldValueId === fieldValueId && !val.dateTimeValue) val.value = value;
                        });
                    }
                } else {
                    if (value !== '') {
                        $scope.fieldState = $scope.vacancy.fieldValues.some((el) => el.field.fieldId === id);
                        if ($scope.fieldState) {
                            $scope.vacancy.fieldValues.forEach((el) => {
                                if (el.field.fieldId === id && !el.dateTimeValue) el.value = value;
                            });
                        } else {
                            $scope.vacancy.fieldValues.push({
                                objType: 'vacancy',
                                fieldValueId: fieldValueId,
                                value: value,
                                field: {
                                    fieldId: id,
                                },
                            });
                        }
                    } else {
                        if ($scope.vacancy.fieldValues && $scope.vacancy.fieldValues.length > 0) {
                            $scope.vacancy.fieldValues.forEach((val, ind) => {
                                if (val.field.fieldId === id && !val.dateTimeValue)
                                    $scope.vacancy.fieldValues.splice(ind, 1);
                            });
                        }
                    }
                }
            };
            $scope.validateMandatoryFields = function () {
                $scope.cfSelectError = [];
                $scope.cfSelectErrorState = false;

                $scope.cfDateError = [];
                $scope.cfDateErrorState = false;

                $scope.cfDateTimeError = [];
                $scope.cfDateTimeErrorState = false;

                $scope.cfTinyTextErrorState = false;

                $scope.cfErrorFlag = false;

                $scope.vacancy?.customFields?.forEach((el) => {
                    if (el.type === 'select' && el.mandatory) {
                        if (!el?.value?.value) {
                            $scope.cfSelectError.push({
                                state: true,
                                fieldId: el.fieldId,
                            });
                        }
                    }

                    if (el.type === 'date' && el.mandatory) {
                        if (!el?.value) {
                            $scope.cfSelectError.push({
                                state: true,
                                fieldId: el.fieldId,
                            });
                        }
                    }
                    if (el.type === 'datetime' && el.mandatory) {
                        if (!el?.value) {
                            $scope.cfSelectError.push({
                                state: true,
                                fieldId: el.fieldId,
                            });
                        }
                    }

                    if (el.type === 'string' && el.mandatory) {
                        let tinyText = $scope.validateTinyText.filter((item) => item.fieldId === el.fieldId);
                        let pushErrorObj = {
                            state: true,
                            fieldId: el.fieldId,
                            diffChars: tinyText.diffChars ? tinyText.diffChars : 0,
                        };
                        if ($scope.vacancy.fieldValues && $scope.vacancy.fieldValues.length > 0) {
                            $scope.cfTinyTextErrorState = $scope.vacancy.fieldValues.some(
                                (dt) => el.fieldId === dt.field.fieldId,
                            );
                            if (!$scope.cfTinyTextErrorState) $scope.validateTinyText.push(pushErrorObj);
                        } else {
                            $scope.validateTinyText.push(pushErrorObj);
                        }
                    }
                });

                if (
                    $scope.cfDateError.length > 0 ||
                    $scope.cfDateTimeError.length > 0 ||
                    $scope.cfSelectError.length > 0 ||
                    $scope.validateTinyText.length > 0
                ) {
                    $scope.cfErrorFlag = true;
                }

                return $scope.cfErrorFlag;
            };
        }

        function setMethodsForCustomFields() {
            $scope.validateMandatoryFields = function () {
                $scope.cfSelectError = [];
                $scope.cfSelectErrorState = false;

                $scope.cfDateError = [];
                $scope.cfDateErrorState = false;

                $scope.cfDateTimeError = [];
                $scope.cfDateTimeErrorState = false;

                $scope.cfTinyTextErrorState = false;

                $scope.cfErrorFlag = false;

                $scope.allObjCustomField.forEach((el) => {
                    if (el.type === 'select' && el.mandatory) {
                        if (!el?.value?.value) {
                            $scope.cfSelectError.push({
                                state: true,
                                fieldId: el.fieldId,
                            });
                        }

                        // if ($scope.vacancy.fieldValues.length > 0) {
                        //     $scope.cfSelectErrorState = $scope.vacancy.fieldValues.some(
                        //         (s) => el.fieldId === s.field.fieldId,
                        //     );
                        //     if (!$scope.cfSelectErrorState){}
                        //         $scope.cfSelectError.push({
                        //             state: true,
                        //             fieldId: el.fieldId,
                        //         });
                        // } else {
                        //     $scope.cfSelectError.push({
                        //         state: true,
                        //         fieldId: el.fieldId,
                        //     });
                        // }
                    }

                    if (el.type === 'date' && el.mandatory) {
                        if ($scope.vacancy.fieldValues.length > 0) {
                            $scope.cfDateErrorState = $scope.vacancy.fieldValues.some(
                                (d) => el.fieldId === d.field.fieldId,
                            );
                            if (!$scope.cfDateErrorState)
                                $scope.cfDateError.push({
                                    state: true,
                                    fieldId: el.fieldId,
                                });
                        } else {
                            $scope.cfDateError.push({
                                state: true,
                                fieldId: el.fieldId,
                            });
                        }
                    }
                    if (el.type === 'datetime' && el.mandatory) {
                        if ($scope.vacancy.fieldValues.length > 0) {
                            $scope.cfDateTimeErrorState = $scope.vacancy.fieldValues.some(
                                (dt) => el.fieldId === dt.field.fieldId,
                            );
                            if (!$scope.cfDateTimeErrorState)
                                $scope.cfDateTimeError.push({
                                    state: true,
                                    fieldId: el.fieldId,
                                });
                        } else {
                            $scope.cfDateTimeError.push({
                                state: true,
                                fieldId: el.fieldId,
                            });
                        }
                    }
                    if (el.type === 'string' && el.mandatory) {
                        let tinyText = $scope.validateTinyText.filter((item) => item.fieldId === el.fieldId);
                        let pushErrorObj = {
                            state: true,
                            fieldId: el.fieldId,
                            diffChars: tinyText.diffChars ? tinyText.diffChars : 0,
                        };
                        if ($scope.vacancy.fieldValues && $scope.vacancy.fieldValues.length > 0) {
                            $scope.cfTinyTextErrorState = $scope.vacancy.fieldValues.some(
                                (dt) => el.fieldId === dt.field.fieldId,
                            );
                            if (!$scope.cfTinyTextErrorState) $scope.validateTinyText.push(pushErrorObj);
                        } else {
                            $scope.validateTinyText.push(pushErrorObj);
                        }
                    }
                });

                if (
                    $scope.cfDateError.length > 0 ||
                    $scope.cfDateTimeError.length > 0 ||
                    $scope.cfSelectError.length > 0 ||
                    $scope.validateTinyText.length > 0
                ) {
                    $scope.cfErrorFlag = true;
                }

                return $scope.cfErrorFlag;
            };

            $scope.addCustomFieldParams = function (text, id) {
                if (text && $scope.cfSelectError.length) {
                    $scope.cfSelectError = $scope.cfSelectError.filter((error) => error.fieldId !== id);
                }

                $scope.fieldValueState = false;
                $scope.fieldValueState = $scope.vacancy.fieldValues.some((el) => el.field.fieldId === id);
                if ($scope.fieldValueState) {
                    if (text === '' || text === 'null') {
                        $scope.vacancy.fieldValues.forEach((val, ind) => {
                            if (val.field.fieldId === id) $scope.vacancy.fieldValues.splice(ind, 1);
                        });
                    } else {
                        $scope.vacancy.fieldValues.forEach((val) => {
                            if (val.field.fieldId === id) val.value = text;
                        });
                    }
                } else {
                    $scope.vacancy.fieldValues.push({
                        objType: 'vacancy',
                        value: text,
                        field: {
                            fieldId: id,
                        },
                    });
                }
            };
        }
        function setDefaultCurrency() {
            Service.onGetDefaultCurrency({
                defaultCurrencyType: 'vacancy',
            }).then((resp) => {
                if (resp.status === 'ok') $scope.vacancy.currency = { value: resp.object, label: resp.object };
            });
        }

        function definitionOfVariables() {
            $scope.experience = Service.experience().map(Vacancy.experienceOptionsMap);
            $scope.employmentTypeOptions = Service.employmentType();
            $scope.numberPosition = Service.numberPosition();
            $scope.status = Vacancy.status();
            delete $scope.status[5];
            $scope.currency = Service.currency();
            $scope.objType = 'vacancy';
            $scope.type = 'Vacancy add';
            $scope.disableButton = false;
            $scope.showStatus = true;
            $rootScope.wrongRole = false;
            $rootScope.skillError = false;
            $rootScope.vacancyHidden = false;
            $scope.tryToSave = false;
            $scope.clickedAddVacancy = false;
            $rootScope.errorRegion = false;
            $scope.paymentDate = null;
            $scope.finishDate = null;
            $scope.allRoles = allRolesList.map((role) => ({ label: role, value: role }));
            $scope.prioritiesOptions = prioritiesOptions;
            $scope.allSkills = [
                {
                    skill: undefined,
                    experience: 'e00_no_experience',
                    new: false,
                    willPlus: false,
                },
            ];
            $scope.fileForSave = [];
            $scope.addedLang = [];
            $scope.mustHaveSkills = [];
            $scope.niceToHaveSkills = [];
            $scope.skillsObject = {};
            // $scope.errorFields = {
            //     clientName: false,
            //     category: false,
            //     industry: false,
            //     experience: false,
            //     employmentType: false,
            //     numberOfPositions: false,
            // };
            $scope.ckEditorOptions = {
                uiColor: '#77b472',
                toolbarGroups: [
                    { name: 'clipboard', groups: ['clipboard', 'undo'] },
                    {
                        name: 'document',
                        groups: ['mode', 'document', 'doctools'],
                    },
                    {
                        name: 'editing',
                        groups: ['find', 'selection', 'spellchecker', 'editing'],
                    },
                    { name: 'forms', groups: ['forms'] },
                    { name: 'basicstyles', groups: ['basicstyles', 'cleanup'] },
                    { name: 'colors', groups: ['colors'] },
                    {
                        name: 'paragraph',
                        groups: ['list', 'indent', 'blocks', 'align', 'bidi', 'paragraph'],
                    },
                    { name: 'links', groups: ['links'] },
                    { name: 'insert', groups: ['insert'] },
                    { name: 'styles', groups: ['styles'] },
                    { name: 'tools', groups: ['tools'] },
                    { name: 'others', groups: ['others'] },
                    { name: 'about', groups: ['about'] },
                ],

                removeButtons:
                    'Source,Save,Templates,Cut,Find,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Subscript,Superscript,CopyFormatting,RemoveFormat,NumberedList,BulletedList,Outdent,Indent,Blockquote,CreateDiv,JustifyLeft,JustifyCenter,JustifyRight,JustifyBlock,BidiLtr,BidiRtl,Language,Link,Unlink,Anchor,Image,Flash,Table,HorizontalRule,Smiley,SpecialChar,PageBreak,Iframe,Styles,Format,Font,FontSize,Maximize,ShowBlocks,About,NewPage,Preview,Print,Copy,Paste,PasteText,PasteFromWord,Replace,BGColor',
            };
            $scope.ckEditorDescriptionOptions = {
                toolbarGroups: [
                    {
                        name: 'document',
                        groups: ['mode', 'document', 'doctools'],
                    },
                    { name: 'clipboard', groups: ['clipboard', 'undo'] },
                    {
                        name: 'editing',
                        groups: ['find', 'selection', 'spellchecker', 'editing'],
                    },
                    { name: 'forms', groups: ['forms'] },
                    { name: 'basicstyles', groups: ['basicstyles', 'cleanup'] },
                    { name: 'colors', groups: ['colors'] },
                    {
                        name: 'paragraph',
                        groups: ['list', 'indent', 'blocks', 'align', 'bidi', 'paragraph'],
                    },
                    { name: 'links', groups: ['links'] },
                    { name: 'insert', groups: ['insert'] },
                    { name: 'styles', groups: ['styles'] },
                    { name: 'tools', groups: ['tools'] },
                    { name: 'others', groups: ['others'] },
                    { name: 'about', groups: ['about'] },
                ],
                removeButtons:
                    'Source,Save,NewPage,Preview,Print,Templates,Cut,Copy,Paste,PasteText,PasteFromWord,Find,Replace,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Subscript,Superscript,RemoveFormat,CopyFormatting,Outdent,Indent,Blockquote,CreateDiv,JustifyBlock,BidiLtr,BidiRtl,Language,Anchor,Unlink,Link,Image,Flash,Table,Smiley,SpecialChar,PageBreak,Iframe,ShowBlocks,Maximize,About',
            };
            $scope.autoTest = {
                template: { text: '', title: '' },
                test: null,
            };
            $scope.autoTestResponse = {};
            $scope.fieldValues = {
                objType: 'vacancy',
                value: '',
                dateTimeValue: '',
                field: {
                    fieldId: '',
                },
            };
            $scope.allObjCustomField = CustomFieldList.objects;
            $scope.allObjCustomField.forEach((item) => {
                if (item.params) {
                    item.params.forEach((param) => (param.value = $filter('removeHTML')(param.value)));
                }
            });
            $scope.vacancy = {
                position: '',
                datePayment: '',
                accessType: 'public',
                dateFinish: '',
                numberOfPositions: '',
                budget: '',
                priority: null,
                vacanciesForCandidatesAccess: 'publicAccess',
            };
            if ($location.search().isApplication && $rootScope.vacancyApplicationToSave?.currency)
                $scope.vacancy.currency = {
                    value: $rootScope.vacancyApplicationToSave.currency,
                    label: $rootScope.vacancyApplicationToSave.currency,
                };
            else setDefaultCurrency();
            $rootScope.addVacancyClientId = null;
            $scope.vacancy.fieldValues = [];
            FileInit.initFileOption($scope, '');
        }

        $scope.onChangeRecall = (value) => {
            $scope.currentRecall = value;
            $scope.$$phase || $scope.$apply();
        };

        function setFields() {
            definitionOfVariables();
            if ($location.$$search && $location.$$search.isApplication && $rootScope.vacancyApplicationToSave) {
                $rootScope.onScrollToBlockBySelector('#headerOfVacancyPage');
                setModelFromApplication();
                setMethodsForCustomFieldsWithVacancyApplication();
            } else {
                setMethodsForCustomFields();
            }

            Company.getRecallTemplate().then((resp) => {
                const recalls = resp.objects;
                $scope.currentRecall = { name: recalls[0].templateName, id: recalls[0].recallTemplateId };

                if ($scope.currentRecall.name === 'Default') {
                    $scope.currentRecall.name = $filter('translate')('Default-recall-template');
                }

                $scope.recallTemplates = recalls.map((item) => {
                    return {
                        name:
                            item.templateName === 'Default'
                                ? $filter('translate')('Default-recall-template')
                                : item.templateName,
                        id: item.recallTemplateId,
                    };
                });
            });
        }

        function initListeners() {
            $scope.$watch(
                function () {
                    return $location.path();
                },
                function (newPath, oldPath) {
                    if (newPath != '/vacancy/add' && oldPath == '/vacancy/add') {
                        $localStorage.remove('copyVacancy');
                    }
                },
            );
            var myListener = $scope.$on('addedLang', function (event, data) {
                if (data != undefined) {
                    $scope.addedLang = data;
                }
            });
            $scope.$on('$destroy', myListener);

            $rootScope.$on('scoreCardSelected', (evt, data) => {
                $scope.vacancy.scoreCardIds = data.map((card) => card.id);
                $scope.$$phase || $scope.$apply();
            });
        }

        getClients = () => {
            const formData = new FormData();
            formData.append('text', '');

            fetch(`hr/client/autocompleteClients`, {
                method: 'POST',
                body: formData,
            })
                .then((r) => r.json())
                .then((data) => {
                    $scope.allClientsOptions = data.objects.map((client) => ({
                        label: client.name,
                        value: client.clientId,
                    }));
                    $scope.$$phase || $scope.$apply();
                });
        };

        function getCountries() {
            RegionInputService.regionRequests().getAllCountries(
                { lang: $rootScope.currentLang },
                (resp) => {
                    if (resp.status === 'ok') {
                        const filteredCountriesList = $rootScope.filterCountriesList(resp.objects);

                        $scope.countriesOptions = filteredCountriesList.map((item) => ({ label: item, value: item }));
                        $scope.$$phase || $scope.$apply();
                    }
                },
                (err) => {},
            );
        }

        function getCities(country) {
            if (!country) return;
            RegionInputService.regionRequests().autocompleteCity(
                { country },
                function (resp) {
                    if (resp.status === 'ok') {
                        $scope.citiesOptions = RegionInputService.formatCitiesOptions(resp.object[country]);
                        $scope.$$phase || $scope.$apply();
                    }
                },
                function (err) {},
            );
        }

        function initController() {
            setFields();
            initRest();
            getClients();
            Service.onGetLanguagesOptionsCached().then((options) => ($scope.languagesOptions = options));
            getCountries();
            initListeners();
        }

        initController();
    },
]);
