controller.controller('vacanciesController', [
    'localStorageService',
    '$scope',
    'Vacancy',
    'ngTableParams',
    '$location',
    'Client',
    '$rootScope',
    '$filter',
    'Service',
    'ScopeService',
    'Company',
    'notificationService',
    'serverAddress',
    '$timeout',
    'Person',
    '$uibModal',
    '$anchorScroll',
    'Candidate',
    '$window',
    function (
        localStorageService,
        $scope,
        Vacancy,
        ngTableParams,
        $location,
        Client,
        $rootScope,
        $filter,
        Service,
        ScopeService,
        Company,
        notificationService,
        serverAddress,
        $timeout,
        Person,
        $uibModal,
        $anchorScroll,
        Candidate,
        $window,
    ) {
        $scope.vacanciesPageView = $rootScope.me.personParams?.vacanciesPageView || 'list';

        $scope.vacanciesSortOptions = [
            { label: 'sortingOptions.vacancyTitle', value: 'alphabetically', direction: 'asc' },
            { label: 'sortingOptions.location', value: 'city', direction: 'asc' },
            ...($rootScope.me.personParams.clientAccessLevel !== 'hide'
                ? [{ label: 'sortingOptions.clientName', value: 'clientName', direction: 'asc' }]
                : []),
            { label: 'sortingOptions.openingDate', value: 'dc', direction: 'asc' },
            { label: 'sortingOptions.deadline', value: 'dateFinish', direction: 'asc' },
            { label: 'sortingOptions.priority', value: 'priority', direction: 'asc' },
            { label: 'sortingOptions.status', value: 'status', direction: 'asc' },
        ];

        let currentPage = Vacancy.search.params.page.number;
        $scope.flagForEmptyBlock = false;
        $scope.isVacancies = false;
        $scope.vacanciesSize;

        $rootScope.vacancySortType = {};

        $scope.toOneObject = function (localId) {
            $location.path('/vacancies/' + localId);
        };
        $scope.toAdd = function () {
            $location.path('/vacancy/add');
        };

        $scope.addShortVacancy = function (relocate) {
            $rootScope.loading = true;
            if ($rootScope.me.personParams.clientAccessLevel === 'hide' && $rootScope.me.recrutRole === 'freelancer') {
                notificationService.error($filter('translate')('This function is not available'));
                $rootScope.loading = false;
                return;
            }
            if ($scope.shortAddVacancyForm.$valid && $('#clientToAddAutocompleater').select2('data')) {
                if ($('#clientToAddAutocompleater').select2('data')) {
                    $scope.vacancy.clientId.clientId = $('#clientToAddAutocompleater').select2('data').id;
                } else {
                    $scope.showClientErrorOnAdd = true;
                }
                if ($('#pac-input').val().length == 0) {
                    $scope.vacancy.region = null;
                } else if ($('#pac-input').val().length > 0) {
                    $scope.vacancy.region = $scope.region;
                }
                $scope.vacancy.numberOfPositions = 1;
                if ($scope.vacancy.clientId.clientId) {
                    Vacancy.add($scope.vacancy, function (resp) {
                        if (resp.status == 'ok') {
                            notificationService.success(
                                $filter('translate')('vacancy_save_1') +
                                    $scope.vacancy.position +
                                    $filter('translate')('vacancy_save_2'),
                            );
                            $scope.vacancy.position = '';
                            $scope.vacancy.employmentType = '';
                            $scope.regionInput = '';
                            $('#clientToAddAutocompleater').select2('val', '');
                            $('#clientToAddAutocompleater').trigger('change');
                            $scope.shortAddVacancyForm.regionInput.$pristine = true;
                            $scope.shortAddVacancyForm.position.$pristine = true;
                            if (relocate) {
                                $location.path('vacancies/' + resp.object.localId);
                            } else {
                                $scope.tableParams.reload();
                            }
                            $rootScope.loading = false;
                        } else {
                            $rootScope.loading = false;
                            notificationService.error(resp.message);
                        }
                    });
                } else {
                    $rootScope.loading = false;
                    notificationService.error($filter('translate')('choose_client'));
                }
            } else {
                $rootScope.loading = false;
                $scope.shortAddVacancyForm.position.$pristine = false;
                notificationService.error($filter('translate')('Please fill in all fields'));
            }
        };
        $scope.getFirstLetters = function (str) {
            return Service.firstLetters(str);
        };
        $scope.onSnowCurrency = function (from, to) {
            return /^([0-9]+|\d+)$/g.test(to) || /^([0-9]+|\d+)$/g.test(from);
        };

        $scope.redirectToVacancy = function (id) {
            $location.path('/vacancies/' + id);
        };

        // Filter 'dateCounter' not translating and not working and i create this function
        $rootScope.visibilityDateFinishVacancy = (date) => {
            if (!date) {
                return '';
            }
            var msDate = null;
            msDate = new Date(date);

            var today = new Date();
            today.setUTCHours(0, 0, 0, 0);
            msDate.setUTCHours(0, 0, 0, 0);
            var seconds = (today.getTime() - msDate.getTime()) / 1000;
            var future = false;
            if (seconds < 0) {
                seconds = Math.abs(seconds);
                future = true;
            }

            var res = '';
            $rootScope.dateFuture = future;
            var day = parseInt(seconds / 3600 / 24);
            var translate = $filter('translate');
            if (day === 0) {
                return translate('today');
            } else if (day === 1) {
                if (future) {
                    return translate('tomorrow');
                } else {
                    return translate('yesterday');
                }
            } else {
                var cases = [2, 0, 1, 1, 1, 2];
                res +=
                    day +
                    ' ' +
                    [translate('day'), translate('days'), translate('days_1')][
                        day % 100 > 4 && day % 100 < 20 ? 2 : cases[day % 10 < 5 ? day % 10 : 5]
                    ];
            }
            if (!future) {
                res += ' ' + translate('ago');
                res = `${res}`;
            } else {
                res = translate('in_1') + ' ' + res;
            }
            return res;
        };

        $scope.redirectToClient = function (id) {
            $location.path('/clients/' + id);
        };

        $scope.showChangeStatusOfVacancy = function (status, vacancy, modelIndex) {
            $scope.oneVacancy = vacancy;
            $rootScope.changeStateObject.status_old = $scope.oneVacancy.status;
            $rootScope.changeStateObject.status = status;
            $scope.canceledChange = true;
            if (status === $scope.oneVacancy.status) {
                notificationService.error($filter('translate')('You cannot change the status to the same'));
                return;
            }
            $rootScope.changeStateObject.placeholder = $filter('translate')(
                'Write_a_comment_why_do_you_change_vacancy_status',
            );
            $scope.numberOfCandidatesInDifferentStates = function () {
                var totalCount = 0;
                Vacancy.getCounts(
                    {
                        vacancyId: vacancy.vacancyId,
                    },
                    function (statusesCount) {
                        $scope.statusesCount = statusesCount.objects;
                        angular.forEach($scope.VacancyStatusFiltered, function (val) {
                            val.count = 0;
                        });
                        angular.forEach($scope.statusesCount, function (item) {
                            angular.forEach($scope.VacancyStatusFiltered, function (valS) {
                                if (valS.name) {
                                    valS.value = valS.name;
                                }
                                if (item.item == valS.value) {
                                    valS.count = item.count;
                                    totalCount = totalCount + item.count;
                                }
                                if (item.item == valS.customInterviewStateId) {
                                    valS.count = item.count;
                                    totalCount = totalCount + item.count;
                                }
                            });
                        });
                        $scope.hideCongratulation =
                            $scope.statusesCount.length === 1 && $scope.statusesCount[0].item === 'approved';
                        $scope.numberAllCandidateInVacancy = totalCount;
                    },
                );
            };
            if (status == 'completed') {
                $scope.numberOfCandidatesInDifferentStates();
                $rootScope.changeStateObject.placeholder = $filter('translate')('Placeholder comment');
                setTimeout(function () {
                    var hasApproved = false;
                    angular.forEach($scope.statusesCount, function (i) {
                        if (i.item == 'approved') {
                            hasApproved = true;
                        }
                    });
                    if (!hasApproved) {
                        notificationService.error(
                            $filter('translate')('You must move one of the candidates to status Hired'),
                        );
                    } else {
                        $scope.modalInstance = $uibModal.open({
                            animation: true,
                            templateUrl: '../partials/modal/vacancy-change-status.html',
                            size: '',
                            scope: $scope,
                            resolve: function () {},
                        });
                        $scope.modalInstance.closed.then(function () {
                            setTimeout(function () {
                                if ($scope.canceledChange) {
                                    $scope.vacancyChangeStatutes[modelIndex] = $rootScope.changeStateObject.status_old;
                                    $scope.$apply();
                                }
                            }, 0);
                        });
                    }
                }, 500);
            } else {
                $scope.hideCongratulation = true;
                if (status != 'inwork') {
                    $scope.modalInstance = $uibModal.open({
                        animation: true,
                        templateUrl: '../partials/modal/vacancy-change-status.html',
                        size: '',
                        scope: $scope,
                        resolve: function () {},
                    });
                    $scope.modalInstance.closed.then(function () {
                        setTimeout(function () {
                            if ($scope.canceledChange) {
                                $scope.vacancyChangeStatutes[modelIndex] = $rootScope.changeStateObject.status_old;
                                $scope.$apply();
                            }
                        }, 0);
                    });
                } else if (
                    status == 'inwork' &&
                    vacancy.responsiblesPerson != undefined &&
                    vacancy.responsiblesPerson.length > 0
                ) {
                    $scope.modalInstance = $uibModal.open({
                        animation: true,
                        templateUrl: '../partials/modal/vacancy-change-status.html',
                        size: '',
                        scope: $scope,
                        resolve: function () {},
                    });
                    $scope.modalInstance.closed.then(function () {
                        setTimeout(function () {
                            if ($scope.canceledChange) {
                                $scope.vacancyChangeStatutes[modelIndex] = $rootScope.changeStateObject.status_old;
                                $scope.$apply();
                            }
                        }, 0);
                    });
                } else if ($scope.needAutoSetResponsible && vacancy.status == 'inwork') {
                    $rootScope.changeResponsibleInVacancy.id = $rootScope.me.userId;
                    $rootScope.changeResponsibleInVacancy.comment =
                        'Поскольку вы являетесь единственным пользователем Вашей компании, мы назначили Вас ответственным';
                    $rootScope.saveResponsibleUserInVacancy();
                    $scope.modalInstance = $uibModal.open({
                        animation: true,
                        templateUrl: '../partials/modal/vacancy-change-status.html',
                        size: '',
                        scope: $scope,
                        resolve: function () {},
                    });
                    $scope.modalInstance.closed.then(function () {
                        setTimeout(function () {
                            if ($scope.canceledChange) {
                                $scope.vacancyChangeStatutes[modelIndex] = $rootScope.changeStateObject.status_old;
                                $scope.$apply();
                            }
                        }, 0);
                    });
                } else if (status == 'inwork' && !$scope.needAutoSetResponsible) {
                    notificationService.error($filter('translate')('You must set a responsible') + '!');
                }
            }
        };
        $scope.emitEventAdvancedSearch = function () {
            $scope.$broadcast('openAdvancedSearch');
        };

        $rootScope.resetTable = function () {
            setTimeout(() => {
                Vacancy.sortField({ sortField: 'openDate', sortOrder: 'ASC' });
                Vacancy.search.params['sortOrder'] = 'ASC';
                Vacancy.search.params.sort = 'dc';

                $rootScope.vacancySortType = {
                    name: 'dc',
                    sort: 'desc',
                };
            }, 300);
        };

        $scope.sortTable = function (sortParam) {
            if (typeof sortParam !== 'string') sortParam = sortParam.value;

            if (sortParam === 'dc') {
                Vacancy.search.switchSortDate(sortParam);

                setTimeout(() => {
                    $scope.tableParams.reload();
                }, 500);
            } else {
                Vacancy.search.setParam('sort', sortParam);
                $scope.searchParam['sort'] = Vacancy.search.params.sort;
                $scope.searchParam['sortOrder'] = Vacancy.search.params.sortOrder;

                switch (sortParam) {
                    case 'alphabetically':
                        Vacancy.sortField({
                            sortField: 'name',
                            sortOrder: Vacancy.search.params.sortOrder,
                        });
                        break;
                    case 'city':
                        Vacancy.sortField({
                            sortField: 'location',
                            sortOrder: Vacancy.search.params.sortOrder,
                        });
                        break;
                    case 'clientName':
                        Vacancy.sortField({
                            sortField: 'client',
                            sortOrder: Vacancy.search.params.sortOrder,
                        });
                        break;
                    case 'dc':
                        Vacancy.sortField({
                            sortField: 'openDate',
                            sortOrder: Vacancy.search.params.sortOrder,
                        });
                        break;
                    case 'dateFinish':
                        Vacancy.sortField({
                            sortField: 'finishDate',
                            sortOrder: Vacancy.search.params.sortOrder,
                        });
                        break;
                    case 'priority':
                        Vacancy.sortField({
                            sortField: 'priority',
                            sortOrder: Vacancy.search.params.sortOrder,
                        });
                        break;
                    case 'status':
                        Vacancy.sortField({
                            sortField: 'status',
                            sortOrder: Vacancy.search.params.sortOrder,
                        });
                        break;
                }

                $rootScope.vacancySortType = {
                    name: sortParam,
                    sort: Vacancy.search.params.sortOrder.toLowerCase(),
                };

                setTimeout(() => {
                    $scope.tableParams.reload();
                }, 500);
            }
        };

        $rootScope.saveVacancyStatus = function () {
            $rootScope.usersCrystalsMap = [];
            $rootScope.userCrystalsMapped = [];
            $rootScope.clickedSaveVacancyStatus = false;
            if (!$rootScope.clickedSaveVacancyStatus && $rootScope.changeStateObject.status) {
                $scope.canceledChange = false;
                $rootScope.clickedSaveVacancyStatus = true;
                $rootScope.closeModal();
                Vacancy.changeState(
                    {
                        vacancyId: $scope.oneVacancy.vacancyId,
                        comment: $rootScope.changeStateObject.comment,
                        vacancyState: $rootScope.changeStateObject.status,
                    },
                    function (resp) {
                        if (resp.status == 'ok') {
                            $rootScope.completedPoints = resp.object.crystals;
                            $rootScope.closedVacancyName = resp.object.position;

                            if (resp.object.usersCrystalsMap) {
                                $rootScope.usersCrystalsMap = resp.object.usersCrystalsMap;
                                $rootScope.userCrystalsMapped = Object.entries($rootScope.usersCrystalsMap);
                            }

                            $rootScope.changeStateObject.comment = '';
                            notificationService.success($filter('translate')('vacancy change status'));
                            if (
                                ($rootScope.changeStateObject.status == 'canceled' ||
                                    $rootScope.changeStateObject.status == 'completed' ||
                                    $rootScope.changeStateObject.status == 'recommendation') &&
                                $scope.vacancies.length == 1 &&
                                $scope.a.searchNumber > 1
                            ) {
                                $scope.tableParams.page($scope.a.searchNumber - 1);
                            } else {
                                $scope.tableParams.reload();
                            }
                            // Service.showCongratulationsHandler(resp.object.dc, $scope.hideCongratulation);
                            //
                            // resetHideCongratulationValue();
                            $rootScope.changeStateObject.status_old;
                            if (
                                $rootScope.me.recrutRole != 'client' &&
                                $rootScope.changeStateObject.status_old !== 'completed' &&
                                $rootScope.changeStateObject.status === 'completed' &&
                                window.screen.width > 1099 &&
                                window.screen.height > 600 &&
                                resp.object.usersCrystalsMap !== undefined
                            ) {
                                setTimeout(() => {
                                    $rootScope.showCongratulation();
                                }, 1500);
                            }
                        } else if (resp.message) {
                            notificationService.error(resp.message);
                        }
                        $rootScope.clickedSaveVacancyStatus = false;
                    },
                    function (err) {
                        $rootScope.clickedSaveVacancyStatus = false;
                    },
                );
            }
        };
        $rootScope.closeModal = function () {
            $scope.modalInstance.close();
        };

        function sortVacanciesByUserID(data) {
            let userID = $rootScope.userId,
                newData = [],
                i = 0;

            for (; i < data.length; i++) {
                let currentVacancy = data[i];

                if (!currentVacancy || !currentVacancy['responsibles']) continue;

                currentVacancy['responsibles'].forEach((j) => {
                    if (userID == j.personId) {
                        data.splice(i, 1);
                        newData.push(currentVacancy);
                        i--;
                    }
                });
            }
            return newData.concat(data);
        }

        function scope_update(val) {
            $scope.$broadcast('scopeChanged');
            $scope.tableParams.reload();
        }

        function resetHideCongratulationValue() {
            $scope.hideCongratulation = true;
        }

        function setPersonsInView(resp) {
            $scope.persons = [];
            $rootScope.persons = [];
            $rootScope.personsNotChanged = [];
            $scope.associativePerson = resp.object;
            angular.forEach($scope.associativePerson, function (val, key) {
                $scope.persons.push($scope.associativePerson[key]);
                $rootScope.persons.push($scope.associativePerson[key]);
                $rootScope.personsNotChanged.push($scope.associativePerson[key]);
            });
        }

        function setVacanciesTable() {
            if (!$rootScope.hasOwnProperty('isNeedToResetSearch')) {
                $rootScope.isNeedToResetSearch = true;
            }

            if ($rootScope.previousLocation === '/vacancies/{id}') {
                if ($rootScope.isNeedToResetSearch) {
                    Vacancy.search.resetParams();
                    Vacancy.search.resetFieldsValues();
                }
            } else {
                Vacancy.search.resetParams();
                Vacancy.search.resetFieldsValues();
            }

            $scope.tableParams = new ngTableParams(
                {
                    page: 1,
                    count: Vacancy.search.params.page.count,
                },
                {
                    total: 0,
                    getData: function ($defer, params) {
                        if ($rootScope.previousLocation === '/vacancies/{id}') {
                            if ($rootScope.clickFrom === 'topNavBar' || $rootScope.clickFrom === 'sideNavBar') {
                                $scope.tableParams.page(1);
                                $rootScope.clickFrom = null;
                            } else {
                                $scope.tableParams.page($rootScope.previousSearchNumber);
                            }
                            $rootScope.previousLocation = '';
                        }
                        if (ScopeService.isInit()) {
                            if (Vacancy.search.resetPagination) {
                                Vacancy.search.setParam('page', {
                                    number: 0,
                                    count: 15,
                                });
                                params.count(15);
                                params.page(1);
                                Vacancy.search.resetPagination = false;
                            } else {
                                Vacancy.search.setParam('page', {
                                    number: params.$params.page - 1,
                                    count: params.$params.count,
                                });
                            }

                            function setVacancyParam() {
                                var activeParam = ScopeService.getActiveScopeObject();
                                $scope.activeScopeParam = activeParam;
                                Vacancy.search.setParam('page', {
                                    number: params.$params.page - 1,
                                    count: params.$params.count,
                                });
                                localStorage.countVacancy = params.$params.count;
                                $scope.searchParam = angular.copy(Vacancy.search.params);
                            }

                            if ($scope.updateParams) $scope.updateParams();

                            function getVacancies(page, count) {
                                $scope.flagForEmptyBlock = false;
                                $scope.isVacancies = false;
                                $rootScope.loading = true;
                                if (page || count) {
                                    currentPage = page;
                                    Vacancy.search.setParam('page', {
                                        number: page,
                                        count: count,
                                    });
                                } else {
                                    $scope.isShowMore = false;
                                    currentPage = Vacancy.search.params.page.number;
                                    if (document.getElementById('scrollup'))
                                        document.getElementById('scrollup').style.display = 'none';
                                }
                                Vacancy.getSortStatus()
                                    .then((res) => {
                                        $rootScope.sortType = res.object.sortOrder;
                                        $scope.getSort = res.object.sortField;

                                        function sortMethods(sort, sortOrder) {
                                            if (sort === 'dc') {
                                                Vacancy.search.switchStartDc();

                                                $rootScope.vacancySortType = {
                                                    name: sort,
                                                    sort: sortOrder === 'ASC' ? 'desc' : 'asc',
                                                };
                                            } else {
                                                Vacancy.search.params['sortOrder'] = sortOrder;
                                                Vacancy.search.params.sort = sort;
                                                $scope.searchParam['sort'] = sort;
                                                $scope.searchParam['sortOrder'] = sortOrder;

                                                $rootScope.vacancySortType = {
                                                    name: sort,
                                                    sort: sortOrder === 'ASC' ? 'asc' : 'desc',
                                                };
                                            }
                                        }

                                        switch (res.object.sortField) {
                                            case 'name':
                                                sortMethods('alphabetically', res.object.sortOrder);
                                                break;
                                            case 'location':
                                                sortMethods('city', res.object.sortOrder);
                                                break;
                                            case 'client':
                                                sortMethods('clientName', res.object.sortOrder);
                                                break;
                                            case 'openDate':
                                                sortMethods('dc', res.object.sortOrder);
                                                break;
                                            case 'finishDate':
                                                sortMethods('dateFinish', res.object.sortOrder);
                                                break;
                                            case 'priority':
                                                sortMethods('priority', res.object.sortOrder);
                                                break;
                                            case 'status':
                                                sortMethods('status', res.object.sortOrder);
                                                break;
                                        }
                                    })
                                    .then(() => {
                                        Vacancy.search.params.vacancyFields = ['skills', 'languages'];

                                        $rootScope.loading = true;
                                        Vacancy.all(
                                            Vacancy.search.params,
                                            function (response) {
                                                $scope.flagForEmptyBlock = true;
                                                $scope.isVacancies = true;
                                                $rootScope.loading = false;
                                                if (response.status !== 'error') {
                                                    $scope.totalPagesCount = response.allPageCount;

                                                    if (document.getElementById('vacanciesEmptyBlock')) {
                                                        document.getElementById(
                                                            'vacanciesEmptyBlock',
                                                        ).style.visibility = 'visible';
                                                    }
                                                    $rootScope.objectSize =
                                                        response['objects'] != undefined
                                                            ? response['total']
                                                            : undefined;
                                                    $scope.vacanciesSize =
                                                        response['objects'] != undefined
                                                            ? response['total']
                                                            : undefined;
                                                    $rootScope.vacanciesSize =
                                                        response['objects'] != undefined
                                                            ? response['total']
                                                            : undefined;
                                                    $scope.paginationParams = {
                                                        currentPage: Vacancy.search.params.page.number,
                                                        totalCount: $rootScope.objectSize,
                                                    };
                                                    let pagesCount = Math.ceil(
                                                        response['total'] / Vacancy.search.params.page.count,
                                                    );
                                                    if ($rootScope.previousLocation !== '/vacancies/{id}') {
                                                        if (pagesCount === Vacancy.search.params.page.number + 1) {
                                                            $('#show_more').hide();
                                                        } else {
                                                            $('#show_more').show();
                                                        }
                                                    }
                                                    params.total(response['total']);
                                                    angular.forEach(response['objects'], function (val) {
                                                        if (val.region) {
                                                            if (val.region.city) {
                                                                val.regionShort = val.region.displayCity;
                                                            } else if (val.region.country)
                                                                val.regionShort = val.region.displayCountry;
                                                        }
                                                    });
                                                    if (
                                                        !Vacancy.search.params['sort'] &&
                                                        !Vacancy.search.params['sortOrder']
                                                    ) {
                                                        response['objects'] = sortVacanciesByUserID(
                                                            response['objects'],
                                                        );
                                                    }

                                                    if (page) {
                                                        $scope.vacancies = $scope.vacancies.concat(response['objects']);
                                                    } else {
                                                        $scope.vacancies = response['objects'];
                                                    }
                                                    $scope.vacanciesFound = response['total'] >= 1;
                                                    $scope.vacancyChangeStatutes = $scope.vacancies.map(
                                                        (el) => el.status,
                                                    );
                                                    $defer.resolve($scope.vacancies);
                                                } else {
                                                    if (response.code === 'solrExceptionVacancy') {
                                                        $scope.countVacancyEmpty = true;
                                                    } else {
                                                        notificationService.error(response.message);
                                                    }
                                                }

                                                $scope.searchParam.searchCs = true;
                                                $rootScope.loading = false;
                                                $scope.displayShowMore = true;
                                            },
                                            function (error) {
                                                $rootScope.loading = false;
                                                console.error('Error in Vacancy.all request: ', error);
                                            },
                                        );
                                    });
                            }

                            setVacancyParam();
                            getVacancies();
                            $scope.showMore = function () {
                                setVacancyParam();
                                $scope.isShowMore = true;
                                $scope.displayShowMore = Service.dynamicTableLoading(
                                    params.total(),
                                    currentPage,
                                    $scope.tableParams.count(),
                                    getVacancies,
                                );
                                $scope.a.searchNumber = currentPage + 1;
                                $scope.$apply();
                            };
                            $scope.a.searchNumber = $scope.tableParams.page();
                            $rootScope.previousSearchNumber = $scope.a.searchNumber;
                            $rootScope.allClientsVacancies = false;
                        }
                    },
                },
            );
        }

        function handleLocalStorageData() {
            localStorage.removeItem('getAllCandidates');
            localStorage.removeItem('currentPage');
            localStorage.setItem('setCurrent', true);
            localStorage.setItem('currentPage', 'vacancies');
        }

        function initInitialState() {
            $scope.vacanciesFound = null;
            $scope.onlyMe = $rootScope.onlyMe;
            $scope.salaryObject = Service.getSalary();
            $scope.previousFlag = true;
            $scope.paginationParams = {
                currentPage: 1,
                totalCount: 0,
            };
            $scope.a = {};
            $scope.a.searchNumber = 1;
            $scope.status = Vacancy.status();
            $scope.statusValues = $scope.status && $scope.status.map((status) => status.value);
            $scope.employmentType = Service.employmentType();
            $scope.regionId = null;
            $scope.loader = false;
            $scope.searchParam = {};
            $scope.searchStatus = '';
            $scope.chosenStatuses = [];
            $scope.currentStatus = null;
            $scope.isSearched = false;
            $scope.vacancy = {
                accessType: 'public',
                currency: 'USD',
                dateFinish: null,
                datePayment: null,
                descr: '',
                sex: null,
                langs: '',
                status: 'open',
                clientId: {
                    clientId: $rootScope.addVacancyClientId,
                },
            };
            $rootScope.addVacancyClientId = null;
            $rootScope.searchCheckVacancy =
                $rootScope.searchCheckVacancy == undefined ? false : $rootScope.searchCheckVacancy;
            $rootScope.changeStateObject = {
                status: '',
                comment: '',
                placeholder: null,
            };
            $rootScope.setCurrent = true;
            $rootScope.currentElementPos = true;
            $rootScope.statusInter = Vacancy.getInterviewStatus();
            Candidate.candidateLastRequestParams = null;
            Candidate.getCandidate = [];
            Vacancy.getCandidate = [];
        }

        async function initController() {
            await Service.onGetLanguagesObjectCached();

            handleLocalStorageData();
            initInitialState();

            if ($rootScope.curentOnlyMenWatch !== undefined) {
                $rootScope.curentOnlyMenWatch();
            }
            if ($rootScope.curentRegionWatch !== undefined) {
                $rootScope.curentRegionWatch();
            }
            ScopeService.setCurrentControllerUpdateFunc(scope_update);
            if (localStorage.countVacancy) {
                $scope.startPagesShown = localStorage.countVacancy;
            } else {
                $scope.startPagesShown = 15;
            }
            if ($rootScope.previousLocation == '/reports/vacancy') {
                if ($rootScope.searchParamInVacancies != undefined) {
                    $rootScope.searchParamInVacancies = null;
                }
            }
            if (!$scope.vacancies) Vacancy.setIsLoadVacancies(true);
            if (Vacancy.getIsLoadVacancies()) {
                setVacanciesTable();
            }

            Person.getUsers(setPersonsInView);
        }

        // For Pagination Component
        $scope.totalPagesCount = 0;

        $scope.changePage = (pageNumber) => {
            if ($scope.isShowMore) {
                $scope.tableParams.page($rootScope.previousSearchNumber);
                $scope.tableParams.reload();
            } else $scope.tableParams.page(pageNumber);

            if ($scope.vacanciesPageView === 'table') {
                document.querySelector('#mainTable').scrollIntoView({
                    block: 'start',
                });
            } else if ($scope.vacanciesPageView === 'list') {
                document.querySelector('.vacancies-cards-list-component').firstElementChild.scrollIntoView({
                    block: 'start',
                });
            }

            $scope.$$phase || $scope.$apply();
        };

        $scope.changeAmountOfElements = (amount) => {
            if ($scope.tableParams.count() === amount) return;
            $scope.tableParams.count(amount);
            $scope.$$phase || $scope.$apply();
        };

        initController();
    },
]);
