angular.module('services.iteratorService', []).factory('IteratorService', [
    function () {
        return class Iterator {
            constructor(items) {
                this.items = items;
                this.index = 0;
            }

            rewind() {
                this.index = 0;
                return this.items[this.index];
            }

            next() {
                if (!this.hasNext()) return;

                this.index++;
                return this.items[this.index];
            }

            back() {
                if (!this.hasPrev()) return;

                this.index--;
                return this.items[this.index];
            }

            onNext(condition) {
                if (condition) this.next();
            }

            current() {
                return this.items[this.index];
            }

            currentIndex() {
                return this.index;
            }

            hasPrev() {
                return this.index > 0;
            }

            hasNext() {
                return this.index < this.items.length - 1;
            }

            each(callback) {
                this.items.forEach((item) => {
                    callback(item);
                });
            }
        };
    },
]);
