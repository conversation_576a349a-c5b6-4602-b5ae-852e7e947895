angular
    .module('services.WorkUAIntegration', [])
    .factory('workUAIntegrationService', [
        '$http',
        'notificationService',
        'serverAddress',
        '$q',
        '$resource',
        WorkUAIntegration,
    ]);

function WorkUAIntegration($http, notificationService, serverAddress, $q, $resource) {
    return {
        onCheckIntegrationStatus: onCheckIntegrationStatus,
        onGetVacancyInfo: onGetVacancyInfo,
        onIntegrateAccount: onIntegrateAccount,
        onDeactivateIntegration: onDeactivateIntegration,
        onGetVacancyFields: onGetVacancyFields,
        onAutocomplete: onAutocomplete,
        onPublish: onPublish,
        onRemovePublish: onRemovePublish,
        onEdit: onEdit,
        onLinkVacancyWithPublished: onLinkVacancyWithPublished,
        OnUnlinkVacancy: OnUnlinkVacancy,
    };
    function requests() {
        return $resource(
            serverAddress + '/workua/:action',
            { action: '@action' },
            {
                getVacancyInfo: {
                    method: 'GET',
                    params: {
                        action: 'getVacancyInfo',
                    },
                },
                checkIntegrationStatus: {
                    method: 'GET',
                    params: {
                        action: 'checkIntegrationStatus',
                    },
                },
                integrateAccount: {
                    method: 'POST',
                    params: {
                        action: 'integrateAccount',
                    },
                },
                deactivateIntegration: {
                    method: 'GET',
                    params: {
                        action: 'deactivateIntegration',
                    },
                },
                getVacancyFields: {
                    method: 'GET',
                    params: {
                        action: 'getVacancyFields',
                    },
                },
                getVacancyFromWorkUa: {
                    method: 'GET',
                    params: {
                        action: 'getVacancyFromWorkUa',
                    },
                },
                autocomplet: {
                    method: 'GET',
                    params: {
                        action: 'autocomplete',
                    },
                },
                publishVacancy: {
                    method: 'POST',
                    params: {
                        action: 'publishVacancy',
                    },
                },
                editVacancy: {
                    method: 'POST',
                    params: {
                        action: 'editVacancy',
                    },
                },
                deleteVacancy: {
                    method: 'DELETE',
                    params: {
                        action: 'deleteVacancy',
                    },
                },
                linkVacancyWithPublished: {
                    method: 'POST',
                    params: {
                        action: 'linkVacancyWithPublished',
                    },
                },
                unlinkVacancy: {
                    method: 'POST',
                    params: {
                        action: 'unlinkVacancy',
                    },
                },
                //////////////////////////
            },
        );
    }

    //////////////////////////////////////////////////////////////////////////////////////////////////////////
    function onCheckIntegrationStatus() {
        return new Promise((resolve, reject) => {
            requests().checkIntegrationStatus(
                (resp) => {
                    resp.status ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }

    function onGetVacancyInfo(vacancy_id) {
        return new Promise((resolve, reject) => {
            requests().getVacancyInfo(
                { vacancyId: vacancy_id },
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }

    function onIntegrateAccount(login, pass) {
        return new Promise((resolve, reject) => {
            requests().integrateAccount(
                { login: login, password: pass },
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onDeactivateIntegration() {
        return new Promise((resolve, reject) => {
            requests().deactivateIntegration(
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onGetVacancyFields(vacancyId, edit) {
        if (!edit) {
            return new Promise((resolve, reject) => {
                requests().getVacancyFields(
                    { vacancyId: vacancyId },
                    (resp) => {
                        resp.status === 'ok' ? resolve(resp) : reject(resp);
                    },
                    (err) => reject(err),
                );
            });
        } else {
            return new Promise((resolve, reject) => {
                requests().getVacancyFromWorkUa(
                    { vacancyId: vacancyId },
                    (resp) => {
                        resp.status === 'ok' ? resolve(resp) : reject(resp);
                    },
                    (err) => reject(err),
                );
            });
        }
    }
    function onAutocomplete(type) {
        return new Promise((resolve, reject) => {
            requests().autocomplet(
                { type: type },
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onPublish(data) {
        return new Promise((resolve, reject) => {
            requests().publishVacancy(
                data,
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onEdit(data) {
        return new Promise((resolve, reject) => {
            requests().editVacancy(
                data,
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onRemovePublish(data) {
        return new Promise((resolve, reject) => {
            requests().deleteVacancy(
                { vacancyId: data },
                (resp) => {
                    resolve(resp);
                },
                (err) => reject(err),
            );
        });
    }

    function onLinkVacancyWithPublished(data) {
        return new Promise((resolve, reject) => {
            requests().linkVacancyWithPublished(
                data,
                (resp) => {
                    resp.status === 'vacancyPosted' || resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function OnUnlinkVacancy(data) {
        return new Promise((resolve, reject) => {
            requests().unlinkVacancy(
                data,
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
}
