angular
    .module('services.rabotaUAIntegration', [])
    .factory('rabotaUaIntegrationService', [
        '$http',
        'notificationService',
        'serverAddress',
        '$q',
        '$resource',
        RabotaUAIntegration,
    ]);

function RabotaUAIntegration($http, notificationService, serverAddress, $q, $resource) {
    return {
        getRubricsFromRabotaUa: getRubricsFromRabotaUa,
        getCityListFromRabotaUa: getCityListFromRabotaUa,
        getJobTypesForRabotaUa: getJobTypesForRabotaUa,
        getLanguageListFromRabotaUa: getLanguageListFromRabotaUa,
        getLanguageSkillListFromRabotaUa: getLanguageSkillListFromRabotaUa,
        onIntegrateAccount: onIntegrateAccount,
        onDeactivateIntegration: onDeactivateIntegration,
        onCheckIntegrationStatus: onCheckIntegrationStatus,
        onPublish: onPublish,
        onGetVacancyInfo: onGetVacancyInfo,
        onAutocomplete: onAutocomplete,
        onGetVacancyFields: onGetVacancyFields,
        onEditVacancy: onEditVacancy,
        onRemovePublish: onRemovePublish,
        onGetVacancyForEdit: onGetVacancyForEdit,
        onGetRegionByCityId: getRegionByCityId,
        getAllPublishTypes: getAllPublishTypes,
        onLinkVacancyWithPublished: onLinkVacancyWithPublished,
        onUnlinkVacancy: onUnlinkVacancy,
    };

    function requests() {
        return $resource(
            serverAddress + '/rabotaua/:action',
            { action: '@action' },
            {
                integrateAccount: {
                    method: 'POST',
                    params: {
                        action: 'integrateAccount',
                    },
                },
                deactivateIntegration: {
                    method: 'GET',
                    params: {
                        action: 'deactivateIntegration',
                    },
                },
                checkIntegrationStatus: {
                    method: 'GET',
                    params: {
                        action: 'checkIntegrationStatus',
                    },
                },
                publishVacancy: {
                    method: 'POST',
                    params: {
                        action: 'publishVacancy',
                    },
                },
                getVacancyInfo: {
                    method: 'GET',
                    params: {
                        action: 'getVacancyInfo',
                    },
                },
                getVacancyForEdit: {
                    method: 'GET',
                    params: {
                        action: 'getVacancyForEdit',
                    },
                },
                autocomplet: {
                    method: 'GET',
                    params: {
                        action: 'autocomplete',
                    },
                },
                getVacancyFromRabotaUa: {
                    method: 'GET',
                    params: {
                        action: 'getVacancyFromRabotaUa',
                    },
                },
                getVacancyFields: {
                    method: 'POST',
                    params: {
                        action: 'getVacancyFields',
                    },
                },
                editVacancy: {
                    method: 'PUT',
                    params: {
                        action: 'editVacancy',
                    },
                },
                deleteVacancy: {
                    method: 'DELETE',
                    params: {
                        action: 'deleteVacancy',
                    },
                },
                getRegionByCityId: {
                    method: 'GET',
                    params: {
                        action: 'getRegionByCityId',
                    },
                },
                linkVacancyWithPublished: {
                    method: 'POST',
                    params: {
                        action: 'linkVacancyWithPublished',
                    },
                },
                unlinkVacancy: {
                    method: 'POST',
                    params: {
                        action: 'unlinkVacancy',
                    },
                },
            },
        );
    }

    function getRubricsFromRabotaUa() {
        return $http({
            method: 'GET',
            url: 'https://employer-api.rabota.ua/values/rubrics',
        });
    }

    function getCityListFromRabotaUa() {
        return $http({
            method: 'GET',
            url: 'https://employer-api.rabota.ua/values/citylist',
        });
    }

    function getLanguageListFromRabotaUa() {
        return $http({
            method: 'GET',
            url: 'https://employer-api.rabota.ua/values/languagelist',
        });
    }

    function getLanguageSkillListFromRabotaUa() {
        return $http({
            method: 'GET',
            url: 'https://employer-api.rabota.ua/values/languageskilllist',
        });
    }

    function getAllPublishTypes() {
        return [{ value: 'Business' }, { value: 'Optimum' }, { value: 'Professional' }, { value: 'Anonym' }];
    }

    function getJobTypesForRabotaUa() {
        return [
            {
                id: 1,
                name: 'fullEmployment',
            },
            {
                id: 2,
                name: 'underemployment',
            },
            {
                id: 3,
                name: 'remote',
            },
            {
                id: 4,
                name: 'trainingAndPractice',
            },
            {
                id: 5,
                name: 'projectWork',
            },
        ];
    }
    function onIntegrateAccount(login, pass) {
        return new Promise((resolve, reject) => {
            requests().integrateAccount(
                { login: login, password: pass },
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onDeactivateIntegration() {
        return new Promise((resolve, reject) => {
            requests().deactivateIntegration(
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onCheckIntegrationStatus() {
        return new Promise((resolve, reject) => {
            requests().checkIntegrationStatus(
                (resp) => {
                    resp.status ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onPublish(data) {
        return new Promise((resolve, reject) => {
            requests().publishVacancy(
                data,
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onGetVacancyInfo(vacancy_id) {
        return new Promise((resolve, reject) => {
            requests().getVacancyInfo(
                { vacancyId: vacancy_id },
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onAutocomplete(type) {
        return new Promise((resolve, reject) => {
            requests().autocomplet(
                { type: type },
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onGetVacancyFields(googlePlaceId, languages) {
        return new Promise((resolve, reject) => {
            requests().getVacancyFields(
                { googlePlaceId: googlePlaceId, languages: languages },
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onGetVacancyForEdit(vacancyId) {
        return new Promise((resolve, reject) => {
            requests().getVacancyForEdit(
                { vacancyId: vacancyId },
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onEditVacancy(data) {
        return new Promise((resolve, reject) => {
            requests().editVacancy(
                data,
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function onRemovePublish(vacancyId) {
        return new Promise((resolve, reject) => {
            requests().deleteVacancy(
                { vacancyId: vacancyId },
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
    function getRegionByCityId(cityId) {
        return new Promise((resolve, reject) => {
            requests().getRegionByCityId(
                { cityId: cityId },
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }

    function onLinkVacancyWithPublished(data) {
        return new Promise((resolve, reject) => {
            requests().linkVacancyWithPublished(
                data,
                (resp) => {
                    resp.status === 'vacancyPosted' || resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }

    function onUnlinkVacancy(data) {
        return new Promise((resolve, reject) => {
            requests().unlinkVacancy(
                data,
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }
}
