angular
    .module('services.HHVacancyPosting', [])
    .factory('hhVacancyPostingService', [
        '$http',
        'notificationService',
        'serverAddress',
        '$q',
        '$resource',
        HHIntegration,
    ]);

function HHIntegration($http, notificationService, serverAddress, $q, $resource) {
    return {
        getProfessionalRoles: getProfessionalRoles,
        getAllAreas: getAllAreas,
        getAreas: getAreas,
        citiesToSelect2Format: citiesToSelect2Format,
        countriesToSelect2Format: countriesToSelect2Format,
        findArea: findArea,
        getBillingTypes: getBillingTypes,
        getVacancyTypes: getVacancyTypes,
        getEmploymentType: getEmploymentType,
        getSchedule: getSchedule,
        getExperience: getExperience,
        getCurrency: getCurrency,
        publishVacancy: publishVacancy,
        editVacancy: editVacancy,
        auth: auth,
        onGetVacancyStatistic: onGetVacancyStatistic,
        onArchiveVacancyPublication: onArchiveVacancyPublication,
        getCompareEmploymentType: getCompareEmploymentType,
        authFromIntegrationPage: authFromIntegrationPage,
        logout: logout,
        getDepartments: getDepartments,
    };
    function requests() {
        return $resource(
            serverAddress + '/hh/:action',
            { action: '@action' },
            {
                publishVacancy: {
                    method: 'POST',
                    params: {
                        action: 'publishVacancy',
                    },
                },
                editVacancy: {
                    method: 'PUT',
                    params: {
                        action: 'editVacancy',
                    },
                },
                getVacancyRegionHH: {
                    method: 'GET',
                    params: {
                        action: 'getVacancyRegionHH',
                    },
                },
                getAuthDataInfo: {
                    method: 'GET',
                    params: {
                        action: 'getAuthDataInfo',
                    },
                },
                getVacancyStatistic: {
                    method: 'GET',
                    params: {
                        action: 'getVacancyStatistic',
                    },
                },
                archiveVacancy: {
                    method: 'DELETE',
                    params: {
                        action: 'archiveVacancy',
                    },
                },
                getToken: {
                    method: 'GET',
                    params: {
                        action: 'getToken',
                    },
                },
                logout: {
                    method: 'GET',
                    params: {
                        action: 'logout',
                    },
                },
                getDepartments: {
                    method: 'GET',
                    params: {
                        action: 'getDepartments',
                    },
                },
            },
        );
    }

    function getDepartments(exportedData) {
        return new Promise((resolve, reject) => {
            requests().getDepartments(
                exportedData,
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }

    function publishVacancy(exportedData) {
        return new Promise((resolve, reject) => {
            requests().publishVacancy(
                exportedData,
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }

    function editVacancy(exportedData) {
        return new Promise((resolve, reject) => {
            requests().editVacancy(
                exportedData,
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }

    function onGetAuthDataInfo(localId) {
        return new Promise((resolve, reject) => {
            requests().getAuthDataInfo(
                { localId: localId },
                (resp) => {
                    resp.status ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }

    function onArchiveVacancyPublication(vacancy_id) {
        return new Promise((resolve, reject) => {
            requests().archiveVacancy(
                { vacancyId: vacancy_id },
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }

    function onGetVacancyStatistic(vacancy_id) {
        return new Promise((resolve, reject) => {
            requests().getVacancyStatistic(
                { vacancyId: vacancy_id },
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }

    function getProfessionalRoles(lang) {
        return $http
            .get('https://api.hh.ru/professional_roles', {
                params: {
                    locale: lang,
                },
            })
            .then((resp) => resp.data)
            .catch(() => notificationService.error('ERROR GETTING PROFESSIONAL ROLES'));
    }

    async function auth(localId) {
        const resp = await onGetAuthDataInfo(localId);
        if (resp.status === 'ok' && resp.object) {
            const redirectData = resp.object;
            const el = document.createElement('a');
            el.href = getHhAuthLink(redirectData.clientId, redirectData.redirectUrl, redirectData.state);
            el.click();
        } else if (resp.status === 'authorized') return true;
    }

    async function authFromIntegrationPage() {
        const resp = await onGetAuthDataInfo();
        if (resp.status === 'ok' && resp.object) {
            const redirectData = resp.object;
            const el = document.createElement('a');
            el.href = getHhAuthLink(redirectData.clientId, redirectData.redirectUrl, redirectData.state);
            el.click();
        } else if (resp.status === 'authorized') return true;
    }

    function logout() {
        return new Promise((resolve, reject) => {
            requests().logout(
                (resp) => {
                    resp.status === 'ok' ? resolve(resp) : reject(resp);
                },
                (err) => reject(err),
            );
        });
    }

    function getHhAuthLink(client_id, redirect_uri, state) {
        return `https://hh.ru/oauth/authorize?response_type=code&client_id=${client_id}&state=${state}&redirect_uri=${encodeURI(
            redirect_uri,
        )}`;
    }

    function getAllAreas(lang) {
        return $http
            .get('https://api.hh.ru/areas', {
                params: {
                    locale: lang,
                },
            })
            .then((resp) => resp.data);
    }

    function getAreas(lang, vacancy_id) {
        const requests = [];

        requests.push($http.get(`https://api.hh.ru/areas?locale=${lang}`));

        requests.push(
            $http.get(`${serverAddress}/hh/getVacancyRegionHH`, {
                params: {
                    vacancyId: vacancy_id,
                },
            }),
        );

        return $q.all(requests).then((resp) => {
            let areas = resp[0].data;
            const otherCountries = areas.splice(
                areas.findIndex((x) => x.id === '1001'),
                1,
            );
            const Ukraine = areas.splice(
                areas.findIndex((x) => x.id === '5'),
                1,
            );
            areas.unshift(Ukraine[0]);
            areas.push(otherCountries[0]);
            return {
                areasFromHH: areas,
                vacancyAreaId: resp[resp.length - 1].data.message || null,
            };
        });
    }

    function findArea(id, currentArea) {
        var i, currentRegion, result;

        if (id === currentArea.id) {
            return currentArea;
        } else {
            // Use a for loop instead of forEach to avoid nested functions
            // Otherwise "return" will not work properly
            let iterableObj = currentArea.areas || currentArea;
            for (i = 0; i < iterableObj.length; i += 1) {
                currentRegion = currentArea.areas ? currentArea.areas[i] : currentArea[i];

                // Search in the current child
                result = findArea(id, currentRegion);

                // Return the result if the node has been found
                if (result !== false) {
                    return result;
                }
            }

            // The node has not been found and we have no more options
            return false;
        }
    }

    function citiesToSelect2Format(cities) {
        if (!cities || !cities.length) return null;

        return cities.reduce((result, oneCity) => {
            if (oneCity.areas.length > 0) {
                oneCity.areas.forEach((cityObj) => {
                    let city = {};
                    city.id = cityObj.id;
                    city.text = `${cityObj.name}, ${oneCity.name}`;
                    result.push(city);
                });
            } else {
                let city = {};
                city.id = oneCity.id;
                city.text = oneCity.name;
                result.push(city);
            }
            return result;
        }, []);
    }

    function countriesToSelect2Format(countries) {
        if (!countries || !countries.length) return null;

        return countries.reduce((result, oneCountry) => {
            result.push({
                id: oneCountry.id,
                text: oneCountry.name,
            });
            return result;
        }, []);
    }

    function getBillingTypes(lang) {
        return lang === 'en'
            ? [
                  { id: 'free', name: 'Free' },
                  { id: 'standard', name: 'Standard' },
                  { id: 'standard_plus', name: 'Standard plus' },
                  { id: 'premium', name: 'Premium' },
              ]
            : lang === 'ru'
            ? [
                  { id: 'free', name: 'Бесплатная' },
                  { id: 'standard', name: 'Стандарт' },
                  { id: 'standard_plus', name: 'Стандарт плюс' },
                  { id: 'premium', name: 'Премиум' },
              ]
            : [
                  { id: 'free', name: 'Безкоштовна' },
                  { id: 'standard', name: 'Стандарт' },
                  { id: 'standard_plus', name: 'Стандарт плюс' },
                  { id: 'premium', name: 'Преміум' },
              ];
    }

    function getVacancyTypes(lang) {
        return lang === 'en'
            ? [
                  { id: 'open', name: 'Open' },
                  { id: 'closed', name: 'Closed' },
                  { id: 'anonymous', name: 'Anonymous' },
                  { id: 'direct', name: 'Advertising' },
              ]
            : lang === 'ru'
            ? [
                  { id: 'open', name: 'Открытая' },
                  { id: 'closed', name: 'Закрытая' },
                  { id: 'anonymous', name: 'Анонимная' },
                  { id: 'direct', name: 'Рекламная' },
              ]
            : [
                  { id: 'open', name: 'Відкрита' },
                  { id: 'closed', name: 'Закрита' },
                  { id: 'anonymous', name: 'Анонімна' },
                  { id: 'direct', name: 'Рекламна' },
              ];
    }

    function getCompareEmploymentType(lang, employmentType) {
        const ourEmploymentTypes = {
            ru: {
                'full employment': { id: 'full', name: 'Полная занятость' },
                underemployment: { id: 'part', name: 'Частичная занятость' },
                'project work': { id: 'project', name: 'Проектная работа' },
            },
            en: {
                'full employment': { id: 'full', name: 'Full time' },
                underemployment: { id: 'part', name: 'Part time' },
                'project work': { id: 'project', name: 'Project work' },
            },
            ua: {
                'full employment': { id: 'full', name: 'Повна зайнятість' },
                underemployment: { id: 'part', name: 'Часткова зайнятість' },
                'project work': { id: 'project', name: 'Проєктна робота' },
            },
        };
        for (let t in ourEmploymentTypes[lang]) {
            if (t === employmentType) {
                let ourEmploymentTypeElement = ourEmploymentTypes[lang][t];
                return ourEmploymentTypeElement;
            }
        }
        return null;
    }

    function getEmploymentType(lang) {
        return lang === 'en'
            ? [
                  { id: 'full', name: 'Full time' },
                  { id: 'part', name: 'Part time' },
                  { id: 'project', name: 'Project work' },
                  { id: 'volunteer', name: 'Volunteering' },
                  { id: 'probation', name: 'Work placement' },
              ]
            : lang === 'ru'
            ? [
                  { id: 'full', name: 'Полная занятость' },
                  { id: 'part', name: 'Частичная занятость' },
                  { id: 'project', name: 'Проектная работа' },
                  { id: 'volunteer', name: 'Волонтерство' },
                  { id: 'probation', name: 'Стажировка' },
              ]
            : [
                  { id: 'full', name: 'Повна зайнятість' },
                  { id: 'part', name: 'Часткова зайнятість' },
                  { id: 'project', name: 'Проєктна робота' },
                  { id: 'volunteer', name: 'Волонтерство' },
                  { id: 'probation', name: 'Стажування' },
              ];
    }

    function getSchedule(lang) {
        return lang === 'en'
            ? [
                  { id: 'fullDay', name: 'Full day' },
                  { id: 'shift', name: 'Shift schedule' },
                  { id: 'flexible', name: 'Flexible schedule' },
                  { id: 'remote', name: 'Remote working' },
                  { id: 'flyInFlyOut', name: 'Rotation based work' },
              ]
            : lang === 'ru'
            ? [
                  { id: 'fullDay', name: 'Полный день' },
                  { id: 'shift', name: 'Сменный график' },
                  { id: 'flexible', name: 'Гибкий график' },
                  { id: 'remote', name: 'Удаленная работа' },
                  { id: 'flyInFlyOut', name: 'Вахтовый метод' },
              ]
            : [
                  { id: 'fullDay', name: 'Повний день' },
                  { id: 'shift', name: 'Змінний графік' },
                  { id: 'flexible', name: 'Гнучкий графік' },
                  { id: 'remote', name: 'Віддалена робота' },
                  { id: 'flyInFlyOut', name: 'Вахтовий метод' },
              ];
    }

    function getExperience(lang) {
        return lang === 'en'
            ? [
                  { id: 'noExperience', name: 'No experience' },
                  { id: 'between1And3', name: 'Between 1 and 3 years' },
                  { id: 'between3And6', name: 'Between 3 and 6 years' },
                  { id: 'moreThan6', name: 'More than 6 years' },
              ]
            : lang === 'ru'
            ? [
                  { id: 'noExperience', name: 'Нет опыта' },
                  { id: 'between1And3', name: 'От 1 года до 3 лет' },
                  { id: 'between3And6', name: 'От 3 до 6 лет' },
                  { id: 'moreThan6', name: 'Более 6 лет' },
              ]
            : [
                  { id: 'noExperience', name: 'Немає досвіду' },
                  { id: 'between1And3', name: 'Від 1 року до 3 років' },
                  { id: 'between3And6', name: 'Від 3 до 6 років' },
                  { id: 'moreThan6', name: 'Більше 6 років' },
              ];
    }

    function getCurrency(lang) {
        return lang === 'en'
            ? [
                  {
                      code: 'AZN',
                      abbr: 'AZN',
                      name: 'Manats',
                      default: false,
                      rate: 0.02594,
                      in_use: false,
                  },
                  {
                      code: 'BYR',
                      abbr: 'BYN',
                      name: 'Belarusian rubles',
                      default: false,
                      rate: 0.032609,
                      in_use: false,
                  },
                  {
                      code: 'EUR',
                      abbr: 'EUR',
                      name: 'Euro',
                      default: false,
                      rate: 0.013502,
                      in_use: true,
                  },
                  {
                      code: 'GEL',
                      abbr: 'GEL',
                      name: 'Georgian lari',
                      default: false,
                      rate: 0.0344,
                      in_use: false,
                  },
                  {
                      code: 'KGS',
                      abbr: 'KGS',
                      name: 'Kyrgyzstani som',
                      default: false,
                      rate: 1.067244,
                      in_use: false,
                  },
                  {
                      code: 'KZT',
                      abbr: 'KZT',
                      name: 'Tenge',
                      default: false,
                      rate: 5.769375,
                      in_use: false,
                  },
                  {
                      code: 'RUR',
                      abbr: 'RUB',
                      name: 'Rubles',
                      default: true,
                      rate: 1.0,
                      in_use: true,
                  },
                  {
                      code: 'UAH',
                      abbr: 'UAH',
                      name: 'Hryvnias',
                      default: false,
                      rate: 0.407173,
                      in_use: false,
                  },
                  {
                      code: 'USD',
                      abbr: 'USD',
                      name: 'Dollars',
                      default: false,
                      rate: 0.01529,
                      in_use: true,
                  },
                  {
                      code: 'UZS',
                      abbr: 'UZS',
                      name: 'Uzbekistani som',
                      default: false,
                      rate: 127.817742,
                      in_use: false,
                  },
              ]
            : lang === 'ru'
            ? [
                  {
                      code: 'AZN',
                      abbr: 'AZN',
                      name: 'Манаты',
                      default: false,
                      rate: 0.02594,
                      in_use: false,
                  },
                  {
                      code: 'BYR',
                      abbr: 'бел. руб.',
                      name: 'Белорусские рубли',
                      default: false,
                      rate: 0.032609,
                      in_use: false,
                  },
                  {
                      code: 'EUR',
                      abbr: 'EUR',
                      name: 'Евро',
                      default: false,
                      rate: 0.013502,
                      in_use: true,
                  },
                  {
                      code: 'GEL',
                      abbr: 'GEL',
                      name: 'Грузинский лари',
                      default: false,
                      rate: 0.0344,
                      in_use: false,
                  },
                  {
                      code: 'KGS',
                      abbr: 'KGS',
                      name: 'Киргизский сом',
                      default: false,
                      rate: 1.067244,
                      in_use: false,
                  },
                  {
                      code: 'KZT',
                      abbr: 'KZT',
                      name: 'Тенге',
                      default: false,
                      rate: 5.769375,
                      in_use: false,
                  },
                  {
                      code: 'RUR',
                      abbr: 'руб.',
                      name: 'Рубли',
                      default: true,
                      rate: 1.0,
                      in_use: true,
                  },
                  {
                      code: 'UAH',
                      abbr: 'грн.',
                      name: 'Гривны',
                      default: false,
                      rate: 0.407173,
                      in_use: false,
                  },
                  {
                      code: 'USD',
                      abbr: 'USD',
                      name: 'Доллары',
                      default: false,
                      rate: 0.01529,
                      in_use: true,
                  },
                  {
                      code: 'UZS',
                      abbr: 'сум',
                      name: 'Узбекский сум',
                      default: false,
                      rate: 127.817742,
                      in_use: false,
                  },
              ]
            : [
                  {
                      code: 'AZN',
                      abbr: 'AZN',
                      name: 'Манаты',
                      default: false,
                      rate: 0.02594,
                      in_use: false,
                  },
                  {
                      code: 'BYR',
                      abbr: 'бел. руб.',
                      name: 'Белорусские рубли',
                      default: false,
                      rate: 0.032609,
                      in_use: false,
                  },
                  {
                      code: 'EUR',
                      abbr: 'EUR',
                      name: 'Евро',
                      default: false,
                      rate: 0.013502,
                      in_use: true,
                  },
                  {
                      code: 'GEL',
                      abbr: 'GEL',
                      name: 'Грузинский лари',
                      default: false,
                      rate: 0.0344,
                      in_use: false,
                  },
                  {
                      code: 'KGS',
                      abbr: 'KGS',
                      name: 'Киргизский сом',
                      default: false,
                      rate: 1.067244,
                      in_use: false,
                  },
                  {
                      code: 'KZT',
                      abbr: 'KZT',
                      name: 'Тенге',
                      default: false,
                      rate: 5.769375,
                      in_use: false,
                  },
                  {
                      code: 'RUR',
                      abbr: 'руб.',
                      name: 'Рубли',
                      default: true,
                      rate: 1.0,
                      in_use: true,
                  },
                  {
                      code: 'UAH',
                      abbr: 'грн.',
                      name: 'Гривны',
                      default: false,
                      rate: 0.407173,
                      in_use: false,
                  },
                  {
                      code: 'USD',
                      abbr: 'USD',
                      name: 'Доллары',
                      default: false,
                      rate: 0.01529,
                      in_use: true,
                  },
                  {
                      code: 'UZS',
                      abbr: 'сум',
                      name: 'Узбекский сум',
                      default: false,
                      rate: 127.817742,
                      in_use: false,
                  },
              ];
    }
}
