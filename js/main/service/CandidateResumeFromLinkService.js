angular.module('services.candidateResumeFromLink', ['ngResource', 'ngCookies']).factory('CandidateResumeFromLink', [
    '$rootScope',
    'Candidate',
    'notificationService',
    '$filter',
    '$location',
    function ($rootScope, Candidate, notificationService, $filter, $location) {
        const candidateResumeFromLink = {};

        candidateResumeFromLink.fromLinkSite = (scope, value) => {
            if (value && (value.includes('work.ua') || value.includes('linkedin.com') || value.includes('grc'))) {
                notificationService.error(
                    $filter('translate')('Use our browser extension to save the candidate from this link'),
                );
                return;
            }
            let link, fastCandResumeLinkSite;
            if ($('#fastCandResumeLinkSite').val()) {
                link = $('#fastCandResumeLinkSite').val();
            } else if (value) {
                link = value;
                fastCandResumeLinkSite = value;
            }

            if (!link) return;
            $rootScope.loading = true;
            if (
                fastCandResumeLinkSite === undefined &&
                (link.indexOf('http://') === -1 || link.indexOf('https://') === -1 || link.indexOf('ftp://') === -1)
            ) {
                if (link.indexOf('https://') > -1) {
                    fastCandResumeLinkSite = link;
                } else {
                    fastCandResumeLinkSite = 'http://' + link;
                }
            }

            if (fastCandResumeLinkSite !== undefined) {
                if (fastCandResumeLinkSite.indexOf('linkedin.com/profile/view') === -1) {
                    scope.fastCandLoading = true;
                    Candidate.fromLinkSite(
                        { url: fastCandResumeLinkSite },
                        function (res) {
                            if (angular.equals(res.status, 'ok')) {
                                $rootScope.dataFromLink = res.object;

                                if (res.object.descr) {
                                    $rootScope.descriptionFromResume = res.object.descr;
                                }

                                $location.path('candidate/add');
                                $location.search('resumeFromLink', 'true');
                            } else if (angular.equals(res.status, 'error')) {
                                notificationService.error(res.message);
                            }
                            scope.fastCandLoading = false;
                            $rootScope.loading = false;
                        },
                        function () {
                            scope.fastCandLoading = false;
                            $rootScope.loading = false;
                        },
                    );
                } else {
                    notificationService.error(
                        $filter('translate')(
                            'Incorrect link of LinkedinIn public profile. You can get correct link at the Contact Info.',
                        ),
                    );
                }
            } else {
                notificationService.error($filter('translate')('Enter a valid url'));
            }
            $rootScope.resumeFromLink = false;
        };

        return candidateResumeFromLink;
    },
]);
