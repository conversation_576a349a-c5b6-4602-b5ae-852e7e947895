angular.module('services.mailing', []).factory('Mailing', [
    'serverAddress',
    '$location',
    '$resource',
    '$q',
    '$state',
    'notificationService',
    '$translate',
    '$filter',
    '$rootScope',
    '$window',
    '$localStorage',
    'Vacancy',
    'Person',
    'Email',
    function (
        serverAddress,
        $location,
        $resource,
        $q,
        $state,
        notificationService,
        $translate,
        $filter,
        $rootScope,
        $window,
        $localStorage,
        Vacancy,
        Person,
        Email,
    ) {
        let service = $resource(
            serverAddress + '/:service/:action',
            { service: 'mailing', action: '@action' },
            {
                setList: {
                    method: 'POST',
                    params: {
                        action: 'addMembersAndList',
                    },
                },
                updateMailingList: {
                    method: 'POST',
                    params: {
                        action: 'updateMailingList',
                    },
                },
                createMailing: {
                    method: 'POST',
                    params: {
                        action: 'createMailing',
                    },
                },
                sendMailing: {
                    method: 'GET',
                    params: {
                        action: 'sendMailing',
                    },
                },
                updateMailing: {
                    method: 'POST',
                    params: {
                        action: 'updateMailing',
                    },
                },
                getAllMailings: {
                    method: 'POST',
                    params: {
                        action: 'getAllMailings',
                    },
                },
                getMailing: {
                    method: 'GET',
                    params: {
                        action: 'getMailing',
                    },
                },
                getCounts: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        action: 'interview/getCounts',
                    },
                },
                getMailingList: {
                    method: 'GET',
                    params: {
                        action: 'getMailingList',
                    },
                },
                sendTestMailing: {
                    method: 'POST',
                    params: {
                        action: 'sendTestMailing',
                    },
                },
                deleteMailing: {
                    method: 'DELETE',
                    params: {
                        action: 'deleteMailing',
                    },
                },
                getAnalytics: {
                    method: 'GET',
                    params: {
                        action: 'getAnalytics',
                    },
                },
                cloneMailing: {
                    method: 'POST',
                    params: {
                        action: 'cloneMailing',
                    },
                },
                enableMailing: {
                    method: 'POST',
                    params: {
                        action: 'enableMailing',
                    },
                },
                getCompaignPriceForMailing: {
                    method: 'POST',
                    params: {
                        action: 'getMailingPrice',
                    },
                },
                getDkim: {
                    method: 'GET',
                    params: {
                        action: 'getSpfAndDkim',
                    },
                },
                getCandidatesFromInterview: {
                    method: 'POST',
                    params: {
                        action: 'getCandidatesFromInterview',
                    },
                },
            },
        );

        service.getCurrentStep = function () {
            try {
                return JSON.parse($localStorage.get('currentStep')) || 'mailing.details';
            } catch (err) {
                return 'mailing.details';
            }
        };

        service.onGetCounts = new PromiseWrapper('getCounts');

        function PromiseWrapper(request) {
            return function (params) {
                return new Promise((resolve, reject) => {
                    service[request](
                        params,
                        (resp) => {
                            if (resp.status === 'ok') {
                                resolve(resp);
                            } else {
                                reject(resp);
                            }
                        },
                        (error) => reject(error),
                    );
                });
            };
        }

        service.candidatesForMailing = [];
        service.listObject = {};
        service.savedCampaigns = {};
        service.internalName = '';

        service.updateField = function (field, value) {
            service[field] = value;
            let subscriberListParams = JSON.parse($localStorage.get('subscriberListParams'));
            subscriberListParams = subscriberListParams ? subscriberListParams : {};
            subscriberListParams[field] = value;
            subscriberListParams.isFirstStepHasChanges = true;
            $localStorage.set('subscriberListParams', JSON.stringify(subscriberListParams));
        };

        service.getEditorVariables = [
            {
                name: 'Candidate name',
                id: '[candidate name]',
                field: '',
                isAvailable: true,
            },
            {
                name: 'Candidate full name',
                id: '[full name]',
                field: '',
                isAvailable: true,
            },
            {
                name: "recruiter's name",
                id: "[recruiter's name]",
                field: '',
                isAvailable: true,
            },
            {
                name: "recruiter's phone",
                id: "[recruiter's phone]",
                field: 'mobPhone',
                isAvailable: true,
            },
            {
                name: "recruiter's Skype",
                id: "[recruiter's Skype]",
                field: 'skype',
                isAvailable: true,
            },
            {
                name: "recruiter's Facebook",
                id: "[recruiter's Facebook]",
                field: 'facebook',
                isAvailable: true,
            },
            {
                name: "recruiter's LinkedIn",
                id: "[recruiter's LinkedIn]",
                field: 'linkedin',
                isAvailable: true,
            },
        ];

        service.getInternal = function () {
            return service.internalName;
        };

        service.setStep = function (step) {
            $localStorage.set('currentStep', JSON.stringify(step));
            $state.go(step);
        };

        service.getMailingDetails = function () {
            try {
                return JSON.parse($localStorage.get('subscriberListParams'));
            } catch (error) {
                return null;
            }
        };

        service.getMailboxFromIntegrated = function (email, emails) {
            let mailbox = false;
            let allEmails = emails ? emails : service.integratedMailBoxes;
            if (email && allEmails) {
                allEmails.some((oneMailBox) => {
                    if (oneMailBox.email === email) {
                        mailbox = oneMailBox;
                        return true;
                    } else {
                        return false;
                    }
                });
            }
            return mailbox;
        };

        service.checkDkimSettings = function (mailBox) {
            return new $q((resolve, reject) => {
                service.getDkim(
                    { email: mailBox },
                    (resp) => {
                        if (resp.status !== 'error') {
                            resolve(resp);
                        } else {
                            reject(resp.message);
                            notificationService.error(resp.message);
                        }
                    },
                    (error) => {
                        notificationService.error(error.status);
                        reject(error.status);
                    },
                );
            });
        };

        service.newMailing = function () {
            service.candidatesForMailing = [];
            $localStorage.remove('candidatesForMailingIds');
            $localStorage.remove('candidatesForMailing');
            $localStorage.remove('mailingRecipientsSource');
            $localStorage.remove('subscriberListParams');
            $localStorage.remove('currentStep');
            $localStorage.remove('stepClickable');
            $localStorage.remove('savedFields');
            service.internalName = '';
            service.setStep('mailing.details');
            $location.url('/mailing');
        };

        service.showSentCompaignById = function (id) {
            service.getMailing(
                { mailingId: id },
                (resp) => {
                    if (resp.status !== 'error') {
                        service.toSentPreview(resp.object);
                    } else {
                        notificationService.error(resp.message);
                    }
                },
                (error) => {
                    notificationService.error(error.message);
                },
            );
        };

        service.updateSubList = function (internal, candidates, isNotificationInProgress) {
            let paramsObject = {};
            paramsObject = subscriberListParamsPrepared(internal, candidates);
            let existedList = service.getMailingDetails();
            if (existedList && existedList.mailingListId) {
                paramsObject.mailingListId = existedList.mailingListId;
            }
            let recipientsSource = JSON.parse($localStorage.get('mailingRecipientsSource'));
            if (recipientsSource) {
                paramsObject.vacancyId = recipientsSource.vacancyId;
                paramsObject.vacancyName = recipientsSource.localId;
                paramsObject.stageId = recipientsSource.state;
                paramsObject.stageName = recipientsSource.stageName;
            }

            return $q((resolve, reject) => {
                if (
                    paramsObject.mailingListId &&
                    paramsObject.mailingMembers &&
                    paramsObject.mailingMembers.every((item) => item.email)
                ) {
                    service.updateMailingList(
                        paramsObject,
                        (resp) => {
                            if (resp.status !== 'error') {
                                resolve(resp);
                                if (existedList.mailingId) {
                                    service.saveMailing().then(
                                        (result) => {
                                            if (!isNotificationInProgress) {
                                                notificationService.success($filter('translate')('Changes are saved'));
                                            }
                                            resolve('new list');
                                        },
                                        (error) => {
                                            reject('New subList save and compaign update error', error.message);
                                        },
                                    );
                                }
                            } else {
                                reject(resp);
                            }
                        },
                        (error) => {
                            reject(error);
                        },
                    );
                } else {
                    if (
                        paramsObject &&
                        paramsObject.mailingMembers &&
                        paramsObject.mailingMembers.every((item) => item.email)
                    ) {
                        service.setList(
                            paramsObject,
                            (resp) => {
                                if (resp.object && resp.object.mailingListId) {
                                    if (existedList) {
                                        existedList.mailingListId = resp.object.mailingListId;
                                        existedList.vacancyId = resp.object.vacancyId;
                                        existedList.localId = recipientsSource.localId;
                                        existedList.internalName = resp.object.internalName || resp.object.name;
                                        $localStorage.set('subscriberListParams', JSON.stringify(existedList));
                                    } else {
                                        paramsObject.mailingListId = resp.object.mailingListId;
                                        $localStorage.set('subscriberListParams', JSON.stringify(paramsObject));
                                    }
                                    resolve(resp);
                                } else {
                                    reject(resp);
                                }
                            },
                            (error) => {
                                reject('New subList save error', error.message);
                            },
                        );
                    } else {
                        resolve();
                    }
                }
            });
        };

        service.saveSubscribersList = function (
            topic,
            internal,
            Name,
            Mail,
            candidates,
            savedRecipientsSource,
            toSave,
            goToEditor,
        ) {
            let savedMailing = JSON.parse($localStorage.get('savedFields'));
            let subscriberListParams = JSON.parse($localStorage.get('subscriberListParams'));
            let mailingText = savedMailing && savedMailing.text ? savedMailing.text : '';
            $rootScope.loading = true;
            let paramsObject = {};
            paramsObject = subscriberListParamsPrepared(topic, candidates);
            let existedList = service.getMailingDetails();
            let recipientsSource = JSON.parse($localStorage.get('mailingRecipientsSource'));
            if (recipientsSource) {
                paramsObject.vacancyId = recipientsSource.vacancyId;
                paramsObject.vacancyName = recipientsSource.localId;
                paramsObject.stageId = recipientsSource.state;
                paramsObject.stageName = recipientsSource.stageName;
            }
            if (existedList && existedList.mailingListId) {
                paramsObject.mailingListId = existedList.mailingListId;
                updateList();
            } else {
                if (subscriberListParams.isFirstStepHasChanges) {
                    saveNewList();
                } else {
                    $rootScope.loading = false;
                    if (goToEditor) service.setStep('mailing.editor');
                }
            }
            function saveNewList() {
                paramsObject.fromName = Name;
                paramsObject.fromMail = Mail;
                paramsObject.subject = topic;
                paramsObject.internalName = internal;
                paramsObject.mailingId = existedList ? existedList.mailingId : null;
                if (
                    !(
                        paramsObject &&
                        paramsObject.mailingMembers &&
                        paramsObject.mailingMembers.every((item) => item.email)
                    )
                )
                    return;
                service.setList(
                    paramsObject,
                    function (resp) {
                        if (resp.object && resp.object.mailingListId) {
                            paramsObject.mailingListId = resp.object.mailingListId;
                            $localStorage.set('subscriberListParams', JSON.stringify(paramsObject));
                            if (toSave) {
                                service.saveMailing(mailingText).then(
                                    (result) => {
                                        notificationService.success($filter('translate')('Changes are saved'));
                                        subscriberListParams.isFirstStepHasChanges = false;
                                        if (goToEditor) service.setStep('mailing.editor');
                                    },
                                    (error) => {},
                                );
                            }
                        } else {
                            if (resp.code === 'incorrectEmail') {
                                notificationService.error($filter('translate')('Candidate incorrect email'));
                            } else {
                                notificationService.error(resp.message);
                            }
                        }
                        $rootScope.loading = false;
                    },
                    function (error) {
                        $rootScope.loading = false;
                        notificationService.error(error);
                    },
                );
            }
            function updateList() {
                service.updateMailingList(
                    paramsObject,
                    function (resp) {
                        $rootScope.candidaterForSendTestMailingFullName = resp.object.mailingMembers[0].fullName;
                        $localStorage.set('mailingFullName', resp.object.mailingMembers[0].fullName);
                        $rootScope.candidaterForSendTestMailingFirstName = resp.object.mailingMembers[0].firstName;
                        if (resp.object && resp.object.mailingListId) {
                            existedList.mailingMembers = paramsObject.mailingMembers;
                            existedList.name = paramsObject.name;
                            existedList.fromName = Name;
                            existedList.fromMail = Mail;
                            existedList.subject = topic;
                            existedList.internalName = internal;
                            $localStorage.set('subscriberListParams', JSON.stringify(existedList));
                            if (subscriberListParams.isFirstStepHasChanges) {
                                service.saveMailing(mailingText).then(
                                    (result) => {
                                        notificationService.success($filter('translate')('Changes are saved'));
                                        if (goToEditor) service.setStep('mailing.editor');
                                    },
                                    (error) => {},
                                );
                            } else {
                                if (goToEditor) service.setStep('mailing.editor');
                            }
                        } else {
                            if (resp.code == 'incorrectEmail') {
                                notificationService.error($filter('translate')('Candidate incorrect email'));
                            } else {
                                notificationService.error(resp.message);
                            }
                        }
                        $rootScope.loading = false;
                    },
                    function (error) {
                        $rootScope.loading = false;
                        notificationService.error(error);
                    },
                );
            }
        };

        service.saveTestSubscribersList = function (mail) {
            let params = {
                name: 'testList',
                mailingMembers: [
                    {
                        email: mail,
                        firstName: 'firstName',
                        lastName: 'lastName',
                    },
                ],
            };
            return new Promise((resolve, reject) => {
                service.setList(
                    params,
                    function (resp) {
                        if (resp.object && resp.object.mailingListId) {
                            resolve(resp.object.mailingListId);
                        } else {
                            reject(resp);
                        }
                    },
                    function (err) {
                        reject(err);
                    },
                );
            });
        };

        service.toCreateMailing = function ($uibModal, $scope, candidates, mailingSource) {
            delete $rootScope.VacancyStatusFiltered;
            $localStorage.remove('candidatesForMailingIds');
            $localStorage.remove('candidatesForMailing');
            $localStorage.remove('subscriberListParams');
            $localStorage.remove('currentStep');
            $localStorage.remove('stepClickable');
            $localStorage.remove('savedFields');
            $localStorage.set('mailingRecipientsSource', JSON.stringify(mailingSource));
            service.internalName = '';
        };

        service.setCandidatesForMailing = function (candidates) {
            service.candidatesForMailing = candidates;
        };
        service.getCandidatesForMailing = function () {
            return service.candidatesForMailing;
        };

        service.updateCompaignFromEditor = function (htmlText, topic, fromName, fromMail) {
            let savedDetails = service.savedFields || JSON.parse($localStorage.get('savedFields'));
            let existedList = service.getMailingDetails();
            let mailingText = existedList && existedList.text ? existedList.text : '';
            function updateList(resolve, reject) {
                if (
                    existedList.mailingListId &&
                    existedList.mailingMembers &&
                    existedList.mailingMembers.every((item) => item.email)
                ) {
                    service.updateMailingList(
                        existedList,
                        (resp) => {
                            if (resp.status !== 'error') {
                                existedList.name = topic;
                                existedList.fromName = fromName;
                                existedList.fromMail = fromMail;
                                existedList.subject = topic;
                                existedList.text = htmlText;
                                $localStorage.set('subscriberListParams', JSON.stringify(existedList));
                                if (
                                    isSecondStepHasChanges(savedDetails, {
                                        subject: topic,
                                        fromName: fromName,
                                        fromMail: fromMail,
                                        text: htmlText,
                                    })
                                ) {
                                    service.saveMailing(mailingText).then(
                                        (result) => {
                                            notificationService.success($filter('translate')('Changes are saved'));
                                            resolve(result);
                                            $rootScope.loading = false;
                                        },
                                        (error) => {
                                            reject(error);
                                            $rootScope.loading = false;
                                        },
                                    );
                                }
                            } else {
                                reject(resp);
                            }
                        },
                        (error) => {
                            reject(error);
                        },
                    );
                }
            }
            return new Promise((resolve, reject) => {
                updateList(resolve, reject);
            });
        };

        service.saveMailing = function (text) {
            let newMailing = JSON.parse($localStorage.get('subscriberListParams'));
            let mailingForSend = {
                subject: newMailing.name || newMailing.subject,
                internalName: newMailing.internalName,
                html: text || newMailing.text,
                fromName: newMailing.fromName,
                fromEmail: newMailing.fromMail,
                mailingList: newMailing.mailingList,
                mailingId: newMailing.mailingId,
                mailingListId: newMailing.mailingListId,
            };
            newMailing.text = text || newMailing.text;
            newMailing.isFirstStepHasChanges = false;
            $localStorage.set('subscriberListParams', JSON.stringify(newMailing));
            $localStorage.set('savedFields', JSON.stringify(newMailing));
            return new Promise((resolve, reject) => {
                if (mailingForSend.mailingId) {
                    service.updateMailing(
                        mailingForSend,
                        function (res) {
                            $rootScope.testMailSentFlag = res.object.testMailSent;
                            if (res.status !== 'error') {
                                resolve(res);
                            } else {
                                notificationService.error(res.message || res.status);
                                reject(res.status);
                            }
                        },
                        function (err) {
                            reject(err);
                        },
                    );
                } else {
                    service.createMailing(
                        mailingForSend,
                        function (res) {
                            if (res.status !== 'error') {
                                newMailing.mailingId = res.object.mailingId;
                                $localStorage.set('subscriberListParams', JSON.stringify(newMailing));
                                resolve(res);
                            } else {
                                notificationService.error(res.message || res.status);
                                reject(res.status);
                            }
                        },
                        function (err) {
                            reject(err);
                        },
                    );
                }
            });
        };

        service.createTestCampaign = function (mailingListId) {
            let newMailing = JSON.parse($localStorage.get('subscriberListParams'));
            let mailingForSend = {
                subject: newMailing.name || newMailing.subject + ' -- TEST',
                html: newMailing.text,
                internalName: newMailing.internalName,
                fromName: newMailing.fromName,
                fromEmail: newMailing.fromMail,
                mailingListId: newMailing.mailingListId,
            };
            return new Promise((resolve, reject) => {
                service.createMailing(
                    mailingForSend,
                    function (res) {
                        if (res.status != 'error') {
                            resolve(res.object.mailingId);
                        } else {
                            notificationService.error(res.message || res.status);
                            reject(res.status);
                        }
                    },
                    function (err) {
                        reject(err);
                    },
                );
            });
        };

        service.toThePreview = function (text) {
            let htmlText = '';
            if (text) {
                htmlText = text;
            } else {
                htmlText = service.getMailingDetails().text;
            }
            if (text) {
                service.saveMailing(htmlText).then(
                    (result) => {
                        service.setStep('mailing.preview');
                    },
                    (error) => {
                        service.setStep('mailing.preview');
                    },
                );
            } else {
                service.setStep('mailing.preview');
            }
        };

        service.sendCampaign = function (campaignId) {
            let details = service.getMailingDetails();
            service.internalName = '';
            return $q((resolve, reject) => {
                if (details && details.mailingId) {
                    service.sendMailing(
                        {
                            mailingId: campaignId ? campaignId : details.mailingId,
                        },
                        function (resp) {
                            if (resp.status !== 'error') {
                                resolve(resp);
                                $localStorage.remove('candidatesForMailingIds');
                                $localStorage.remove('candidatesForMailing');
                                $localStorage.remove('subscriberListParams');
                                $localStorage.remove('savedFields');
                            } else {
                                reject(resp);
                                notificationService.error(resp.message);
                            }
                        },
                    );
                } else {
                    reject('no details.mailingId');
                    notificationService.error('Nothing to send');
                }
            });
        };

        service.sendTestMail = function (testEmail) {
            let testLocal = JSON.parse($localStorage.get('candidatesForMailing'));
            let fullName = testLocal[0].fullName;
            let firstName = testLocal[0].firstName;
            let lastName = testLocal[0].lastName;
            let middleName = testLocal[0].middleName;
            let newMailing = JSON.parse($localStorage.get('subscriberListParams'));
            let details = service.getMailingDetails();
            let requestData = {
                mailingList: {
                    name: 'testSubscriberListName',
                    mailingMembers: [
                        {
                            email: testEmail,
                            firstName: firstName,
                            lastName: lastName,
                            middleName: middleName,
                            fullName: fullName,
                        },
                    ],
                },
                mailing: {
                    subject: newMailing.name || newMailing.subject,
                    internalName: newMailing.internalName,
                    html: newMailing.text,
                    fromName: newMailing.fromName,
                    fromEmail: newMailing.fromMail,
                    mailingId: details.mailingId,
                },
            };
            return $q((resolve, reject) => {
                service.sendTestMailing(
                    requestData,
                    function (resp) {
                        if (resp.status !== 'error') {
                            notificationService.success($filter('translate')('Sent test email'));
                            resolve('ok');
                        } else {
                            notificationService.error(resp.message);
                            reject(resp.message);
                        }
                    },
                    function (error) {
                        notificationService.error('request Error');
                        reject(error);
                    },
                );
            });
        };

        service.afterSending = function () {
            $location.url('/mailings/prepared');
            Person.getMe((response) => {
                if (response.status === 'ok') {
                    $rootScope.me = response.object;
                }
            });
        };

        service.makeStepClickable = function (step) {
            let currentStepProgress = $localStorage.get('stepClickable');
            if (!currentStepProgress || step > currentStepProgress) {
                $localStorage.set('stepClickable', step);
                currentStepProgress = step;
            }
            if (currentStepProgress > 2) {
                $('#step_3').addClass('clickable');
            }
        };

        service.toEditMailing = function (mailingForEdit) {
            let subscriberListParams = {};
            let mailingName = '';
            let vacancySelectParam = {};
            let mailingListId = mailingForEdit.mailingListId;
            if (mailingListId) {
                service.getMailingList(
                    {
                        mailingListId: mailingListId,
                    },
                    function (resp) {
                        if (resp.status !== 'error') {
                            mailingName = resp.object.name;
                            vacancySelectParam = {
                                localId: resp.object.vacancy ? resp.object.vacancy.localId : null,
                                vacancyId: resp.object.vacancyId,
                                state: resp.object.stageId,
                                stageName: resp.object.stageName,
                            };
                            subscriberListParams = {
                                mailingMembers: resp.object.mailingMembers,
                                name: mailingName,
                                fromName: mailingForEdit.fromName,
                                fromMail: mailingForEdit.fromEmail,
                                mailingListId: mailingListId,
                                subject: mailingForEdit.name || mailingForEdit.subject,
                                internalName: mailingForEdit.internalName,
                                vacancy: resp.object.vacancy,
                                text: mailingForEdit.html,
                                mailingId: mailingForEdit.mailingId,
                            };
                            service.savedFields = subscriberListParams;
                            $localStorage.set('savedFields', JSON.stringify(subscriberListParams));
                            $localStorage.remove('mailingRecipientsSource');
                            $localStorage.remove('candidatesForMailing');
                            $localStorage.set('mailingRecipientsSource', JSON.stringify(vacancySelectParam));
                            $localStorage.set('subscriberListParams', subscriberListParams);
                            $localStorage.set('currentStep', JSON.stringify('mailing.details'));
                            if (
                                mailingForEdit.subject &&
                                mailingForEdit.html &&
                                mailingForEdit.fromName &&
                                mailingForEdit.fromEmail
                            ) {
                                $localStorage.set('stepClickable', 3);
                            } else {
                                $localStorage.set('stepClickable', 2);
                            }
                            $location.url('/mailing');
                        }
                    },
                );
            } else {
                subscriberListParams = {
                    fromName: mailingForEdit.fromName,
                    fromMail: mailingForEdit.fromEmail,
                    subject: mailingForEdit.name || mailingForEdit.subject,
                    internalName: mailingForEdit.internalName,
                    text: mailingForEdit.html,
                    mailingId: mailingForEdit.mailingId,
                };
                $localStorage.remove('mailingRecipientsSource');
                $localStorage.set('subscriberListParams', subscriberListParams);
                $localStorage.set('stepClickable', 2);
                $localStorage.set('currentStep', JSON.stringify('mailing.details'));
                $location.url('/mailing');
            }
        };

        service.toSentMailingFromHistory = function (mailingId) {
            service.getMailing(
                { mailingId: mailingId },
                (resp) => {
                    if (resp.status !== 'error') {
                        service.toSentPreview(resp.object);
                    } else {
                        notificationService.error(resp.message);
                    }
                },
                (error) => {},
            );
        };

        service.toSentPreview = function (mailing) {
            let id = mailing.mailingListId ? mailing.mailingListId : mailing;
            let sentPreviewObj = {};
            let candidatesContacts = [];
            service.getMailingList(
                {
                    mailingListId: id,
                },
                (resp) => {
                    if (resp.status !== 'error') {
                        resp.object.mailingMembers.forEach((currentValue) => {
                            candidatesContacts.push(
                                _.pick(currentValue, ['firstName', 'lastName', 'email', 'localId']),
                            );
                        });
                        candidatesContacts.forEach((currentValue) => {
                            currentValue.fullName = currentValue.firstName + ' ' + currentValue.lastName;
                        });
                        sentPreviewObj = {
                            name: resp.object.name,
                            fromEmail: mailing.fromEmail,
                            fromName: mailing.fromName,
                            html: mailing.html,
                            sendDate: mailing.sendDate,
                            subject: mailing.name || mailing.subject,
                            internalName: mailing.internalName,
                            receivers: candidatesContacts,
                            mailingId: mailing.mailingId,
                        };

                        $localStorage.set('sentMailing', JSON.stringify(sentPreviewObj));
                        $state.go('sent-mailing');
                    } else {
                        notificationService.error(resp.message);
                    }
                },
                (error) => {
                    notificationService.error(error.message);
                },
            );
        };

        service.emailValidation = function (email) {
            let regForValidation =
                /^[_]?[a-zA-Z0-9]+([\.\-_][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([\.\-_][a-zA-Z0-9]+)*\.[a-zA-Z]{2,10}$$/;
            return regForValidation.test(email);
        };

        function isSecondStepHasChanges(savedParams, currentParams) {
            if (!savedParams) {
                savedParams = service.getMailingDetails();
                if (!savedParams.fromName) {
                    savedParams.fromName = $rootScope.me.fullName;
                }
                savedParams.subject = savedParams.name;
            }
            for (let propName in currentParams) {
                if (currentParams[propName] !== savedParams[propName]) return true;
            }
            return false;
        }

        service.editorChangeStep = function (text, topic, fromName, fromMail, step) {
            text = text ? text : '';
            topic = topic ? topic : '';
            fromName = fromName ? fromName : '';
            fromMail = fromMail ? fromMail : '';
            let htmlText = text;
            return $q((resolve, reject) => {
                let savedDetails = service.savedFields || JSON.parse($localStorage.get('savedFields'));
                if (text.trim() && topic.trim() && fromName.trim() && service.emailValidation(fromMail)) {
                    $localStorage.set('stepClickable', 3);
                } else {
                    $localStorage.set('stepClickable', 2);
                }
                if (
                    isSecondStepHasChanges(savedDetails, {
                        subject: topic,
                        fromName: fromName,
                        fromMail: fromMail,
                        text: htmlText,
                    })
                ) {
                    service.updateCompaignFromEditor(htmlText, topic, fromName, fromMail).then(
                        (result) => {
                            resolve(result);
                            if (step == 'details') {
                                service.setStep('mailing.details');
                            } else {
                                if (step == 'preview') service.setStep('mailing.preview');
                            }
                        },
                        (error) => {
                            reject(error);
                        },
                    );
                } else {
                    resolve('no changes');
                    if (step == 'details') {
                        service.setStep('mailing.details');
                    } else {
                        if (step == 'preview') service.setStep('mailing.preview');
                    }
                }
            });
        };

        service.getVacancyParams = function (params) {
            return new Promise((resolve, reject) => {
                Vacancy.getByLocalId(
                    params,
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve({
                                statuses: resp.object.interviewStatus,
                                ...resp.object,
                            });
                        } else {
                            reject(resp.message);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };

        service.getCompaignPrice = function (params) {
            return new Promise((resolve, reject) => {
                service.getCompaignPriceForMailing(
                    params,
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => {
                        reject(error);
                    },
                );
            });
        };

        service.enableMailingService = function (params) {
            return new Promise((resolve, reject) => {
                service.enableMailing(params, (resp) => {
                    if (resp.status === 'ok') {
                        resolve(resp);
                    } else {
                        reject(resp);
                    }
                });
            });
        };

        service.sortCandidatesList = function (candidatesList) {
            if (candidatesList) {
                let incorrectEmails = false;
                let isChangesNotSaved = false;
                let emptyEmails = 0;
                //find not valid-----------------
                angular.forEach(candidatesList, (candidate) => {
                    if (candidate.email) {
                        if (!service.emailValidation(candidate.email.value)) {
                            candidate.wrongEmail = true;
                            incorrectEmails = true;
                        } else {
                            if (candidate.editable) {
                                isChangesNotSaved = true;
                            }
                        }
                    } else {
                        candidate.wrongEmail = true;
                        incorrectEmails = true;
                        emptyEmails++;
                    }
                });

                //find duplicates
                const lookup = candidatesList.reduce((a, elem) => {
                    a[elem.email?.value] = ++a[elem.email?.value] || 0;
                    return a;
                }, {});
                const duplicatesExist = Object.values(lookup).some((el) => el);

                candidatesList.forEach((candidate) => {
                    candidate.duplicateGroupId = !candidate.wrongEmail && lookup[candidate.email?.value] >= 1;
                });

                const groupedCandidates = candidatesList.reduce((groups, item) => {
                    groups.wrongEmail = groups.wrongEmail ?? [];
                    groups.duplicate = groups.duplicate ?? [];
                    groups.other = groups.other ?? [];

                    item.wrongEmail && groups.wrongEmail.push(item);
                    item.duplicate && groups.duplicate.push(item);
                    !item.wrongEmail && !item.duplicate && groups.other.push(item);

                    return groups;
                }, {});

                candidatesList = [
                    ...groupedCandidates.wrongEmail,
                    ...groupedCandidates.duplicate,
                    ...groupedCandidates.other,
                ];

                return {
                    filterCandidatesList: candidatesList,
                    isIncorrectEmails: incorrectEmails,
                    isDuplicatedEmails: duplicatesExist,
                    emptyEmails: emptyEmails,
                    isChangesNotSaved: isChangesNotSaved,
                };
            }
        };

        service.getIntegratedMailBoxes = function () {
            let mailBoxes = [];
            return new $q((resolve, reject) => {
                Email.personEmails(
                    { type: 'all' },
                    (resp) => {
                        if (resp.status !== 'error' && resp.objects) {
                            for (let i = 0; i < resp.objects.length; i++) {
                                if (resp.objects[i].permitMailing) {
                                    mailBoxes.push(resp.objects[i]);
                                }
                            }
                            resolve(mailBoxes);
                        } else {
                            notificationService.error(resp.message);
                            reject(resp.code);
                        }
                    },
                    (error) => {
                        reject();
                    },
                );
            });
        };

        service.integratedMailBoxes = [];

        service.getUserEmailsWithMailingEnabled = function () {
            let mailBoxes = [];
            service.integratedMailBoxes = [];
            return new $q((resolve, reject) => {
                Email.personEmails(
                    { type: 'all' },
                    (resp) => {
                        if (resp.status !== 'error' && resp.objects) {
                            for (let i = 0; i < resp.objects.length; i++) {
                                if (resp.objects[i].permitMailing) {
                                    service.integratedMailBoxes.push(resp.objects[i]);
                                    mailBoxes.push(resp.objects[i].email);
                                }
                            }
                            resolve(mailBoxes);
                        } else {
                            notificationService.error(resp.message);
                            reject(resp.code);
                        }
                    },
                    (error) => {
                        reject();
                    },
                );
            });
        };

        service.transformCandidatesForMailing = (candidates) => {
            candidates.forEach((candidate) => {
                candidate.emails = [];
                if (candidate.contacts) {
                    candidate.contacts.forEach((contact) => {
                        if (contact.type === 'email') {
                            candidate.emails.push(contact);
                        }
                        if (contact.default) {
                            candidate.email = contact;
                        }
                    });
                    if (!candidate.email && candidate.emails.length > 0) candidate.email = candidate.emails[0];
                    candidate.newEmail = candidate.email;
                }
                candidate.mailing = true;
            });
        };

        service.getCandidatesFullName = (candidates) => {
            return candidates.map((candidate) => ({
                ...candidate,
                fullName: `${candidate.lastName}${candidate.firstName ? ' ' + candidate.firstName : ''} ${
                    candidate.middleName ? candidate.middleName : ''
                } `.trim(),
                fullNameEn: `${candidate.firstName}${candidate.middleName ? ' ' + candidate.middleName : ''} ${
                    candidate.lastName
                } `.trim(),
            }));
        };

        service.getCartRenderingInfo = () => {
            return {
                type: 'pie',
                plot: {
                    borderColor: '#eee',
                    borderWidth: 3,
                    valueBox: {
                        placement: 'out',
                        text: '%t\n%npv%',
                        fontFamily: 'Open Sans',
                    },
                    tooltip: {
                        fontSize: '18',
                        fontFamily: 'Open Sans',
                        padding: '5 10',
                    },
                    animation: {
                        effect: 2,
                        method: 5,
                        speed: 900,
                        sequence: 1,
                        delay: 1000,
                    },
                },
                plotarea: {
                    margin: '20 0 0 0',
                },
                globals: {
                    'font-size': '14px',
                },
                series: [],
            };
        };

        function subscriberListParamsPrepared(internal, candidates) {
            let prepared = {
                name: internal,
                mailingMembers: [],
            };
            candidates.forEach(function (o) {
                let neededFields = {};
                neededFields.candidateId = o.candidateId;
                neededFields.firstName = o.firstName ? o.firstName : '';
                neededFields.lastName = o.lastName ? o.lastName : '';
                neededFields.middleName = o.middleName ? o.middleName : null;
                neededFields.email = o.email ? o.email.value : null;
                neededFields.localId = o.localId;
                neededFields.fullName = o.fullName;
                neededFields.fullNameEn = o.fullNameEn ? o.fullNameEn : '';
                prepared.mailingMembers.push(neededFields);
            });
            return prepared;
        }

        return service;
    },
]);
