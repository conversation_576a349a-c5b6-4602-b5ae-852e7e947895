angular.module('services.mail', ['ngResource']).factory('Mail', [
    '$resource',
    'serverAddress',
    '$uibModal',
    '$rootScope',
    function ($resource, serverAddress, $uibModal, $rootScope) {
        var service = $resource(
            serverAddress + '/mail/:param',
            { param: '@param' },
            {
                createTemplate: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'createTemplate',
                    },
                },
                getTemplate: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getTemplate',
                    },
                },
                getAllTemplates: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getAllTemplates',
                    },
                },
                getTemplateVacancy: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getTemplateVacancy',
                    },
                },
                getTemplatesVacancy: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getTemplatesVacancy',
                    },
                },
                getDefaultTemplates: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getDefaultTemplates',
                    },
                },
                removeTemplate: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'removeTemplate',
                    },
                },
                getTemplatePost: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getTemplate',
                    },
                },
                getTemplateForStage: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getTemplateForStage',
                    },
                },
                saveTemplateForStage: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'saveTemplateForStage',
                    },
                },
                updateTemplate: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'updateTemplate',
                    },
                },
                sendMailByTemplate: {
                    method: 'POST',
                    params: {
                        param: 'sendMailByTemplate',
                    },
                },
                getDefaultTemplate: {
                    method: 'GET',
                    params: {
                        param: 'getDefaultTemplate',
                    },
                },
                testTemplate: {
                    method: 'GET',
                    params: {
                        param: 'getTestTemplate',
                    },
                },
            },
        );

        service.getTestTemplate = function () {
            return new Promise((resolve, reject) => {
                service.testTemplate(
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };

        service.sendMailForPersonalMailing = function (queryPrams) {
            return new Promise((resolve, reject) => {
                service.sendMailByTemplate(
                    queryPrams,
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };

        service.sendMailByTemplateVerified = function (queryPrams, successCallback, errorCallback) {
            service.sendMailByTemplate(
                queryPrams,
                function (resp) {
                    if (resp.status == 'error' && resp.code == 'errorSendFromGmail') {
                        errorCallback(resp);
                        $rootScope.modalInstance && $rootScope.closeModal();
                        $rootScope.modalInstance = $uibModal.open({
                            animation: true,
                            windowClass: 'secondary-modal',
                            templateUrl: '../partials/modal/reintegrate-gmail.html',
                            size: '',
                            resolve: function () {},
                        });
                    } else if (resp.code == 'сouldNotGetRefreshTokenIntegration') {
                        errorCallback(resp);
                        $rootScope.closeModal();
                        $rootScope.modalInstance = $uibModal.open({
                            animation: true,
                            templateUrl: '../partials/modal/gmail-access.html',
                            resolve: {},
                        });
                    } else {
                        successCallback(resp);
                    }
                },
                function (err) {
                    if (errorCallback) {
                        errorCallback(err);
                    }
                },
            );
        };

        service.getVacancyTemplate = new PromiseWrapper('getTemplateVacancy');
        service.onCreateTemplate = new PromiseWrapper('createTemplate');
        service.onGetTemplate = new PromiseWrapper('getTemplate');
        service.onUpdateTemplate = new PromiseWrapper('updateTemplate');
        service.onGetTemplateForStage = new PromiseWrapper('getTemplateForStage');

        function PromiseWrapper(request) {
            return function (params) {
                return new Promise((resolve, reject) => {
                    service[request](
                        params,
                        (resp) => {
                            if (resp.status === 'ok') {
                                resolve(resp);
                            } else {
                                reject(resp);
                            }
                        },
                        (error) => reject(error),
                    );
                });
            };
        }

        return service;
    },
]);
