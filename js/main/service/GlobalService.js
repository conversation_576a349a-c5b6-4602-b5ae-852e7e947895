angular
    .module('services.globalService', ['ngResource', 'pascalprecht.translate', 'services.notice'])
    .factory('Service', [
        '$resource',
        'serverAddress',
        '$filter',
        '$translate',
        '$location',
        'notificationService',
        '$rootScope',
        '$timeout',
        'Email',
        '$http',
        '$uibModal',
        'notificationData',
        function (
            $resource,
            serverAddress,
            $filter,
            $translate,
            $location,
            notificationService,
            $rootScope,
            $timeout,
            Email,
            $http,
            $uibModal,
            notificationData,
        ) {
            var service = $resource(
                serverAddress + '/:service/:action',
                { service: '@service', action: '@action' },
                {
                    getRegions: {
                        method: 'GET',
                        params: {
                            service: 'region',
                            action: 'get',
                        },
                    },
                    getScorecards: {
                        method: 'GET',
                        params: {
                            service: 'scorecard',
                            action: 'autocompleteScoreCards',
                        },
                    },
                    generateFileToken: {
                        method: 'GET',
                        params: {
                            action: 'generateFileToken',
                        },
                    },
                    getRegionsTwo: {
                        method: 'GET',
                        params: {
                            service: 'region',
                            action: 'get2',
                        },
                    },
                    history: {
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'action',
                            action: 'get',
                        },
                    },
                    notice: {
                        method: 'POST',
                        headers: {
                            Pragma: 'no-cache',
                            Expires: -1,
                            'Cache-Control': 'no-cache',
                        },
                        params: {
                            service: 'notice',
                            action: 'get',
                        },
                    },
                    readNotice: {
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'notice',
                            action: 'read',
                        },
                    },
                    publicVacancy: {
                        method: 'GET',
                        params: {
                            service: 'public',
                            action: 'getVacancy',
                        },
                    },
                    publicCandidate: {
                        method: 'GET',
                        params: {
                            service: 'public',
                            action: 'getCandidate',
                        },
                    },
                    saveAccessLogEntry: {
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'public',
                            action: 'saveAccessLogEntry',
                        },
                    },
                    addCandidate: {
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'public',
                            action: 'addRecall',
                        },
                    },
                    addCandidateReply: {
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'public',
                            action: 'addReply',
                        },
                    },
                    getEvaluation: {
                        method: 'GET',
                        params: {
                            service: 'public',
                            action: 'getEvaluation',
                        },
                    },
                    sendDailyReportExample: {
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'public',
                            action: 'sendDailyReportExample',
                        },
                    },
                    getClientNames: {
                        method: 'GET',
                        params: {
                            service: 'public',
                            action: 'getClientNames',
                        },
                    },
                    addVacancyPackage: {
                        method: 'POST',
                        params: {
                            service: 'public',
                            action: 'addVacancyPackage',
                        },
                    },
                    saveBrowserWithPlugin: {
                        method: 'GET',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'person',
                            action: 'saveBrowserWithPlugin',
                        },
                    },
                    getOrgLogoId: {
                        method: 'GET',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'company',
                            action: 'getOrgLogoId',
                        },
                    },
                    removeWallpaper: {
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'company',
                            action: 'removeWallpaper',
                        },
                    },
                    removeLogo: {
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'company',
                            action: 'removeLogo',
                        },
                    },
                    readAt: {
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'at',
                            action: 'read',
                        },
                    },
                    getGroups: {
                        method: 'GET',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'candidateGroup',
                            action: 'getGroups',
                        },
                    },
                    search: {
                        method: 'GET',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'candidateGroup',
                            action: 'search',
                        },
                    },
                    getAllOpenVacancy: {
                        method: 'GET',
                        params: {
                            service: 'public',
                            action: 'getAllOpenVacancy',
                        },
                    },
                    markNotice: {
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            param: 'markNotice',
                        },
                    },
                    checkContactsByProfileId: {
                        method: 'GET',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'candidateForTBot',
                            action: 'checkContactsByProfileId',
                        },
                    },
                    getProfileId: {
                        method: 'GET',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'candidateForTBot',
                            action: 'getProfileId',
                        },
                    },
                    addApplicant: {
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'candidateForTBot',
                            action: 'addApplicant',
                        },
                    },
                    setContactsAndSurname: {
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: 'candidateForTBot',
                            action: 'setContactsAndSurname',
                        },
                    },
                    getLanguages: {
                        method: 'GET',
                        params: {
                            service: 'public',
                            action: 'getLanguages',
                        },
                    },
                    getCompanyInformation: {
                        method: 'GET',
                        params: {
                            service: 'public',
                            action: 'getCompanyInformation',
                        },
                    },
                    disable: {
                        method: 'GET',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: '2fa',
                            action: 'disable',
                        },
                    },
                    activate: {
                        method: 'GET',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: '2fa',
                            action: 'activate',
                        },
                    },
                    generate: {
                        method: 'GET',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: '2fa',
                            action: 'generate',
                        },
                    },
                    disableForUser: {
                        method: 'GET',
                        headers: {
                            'Content-type': 'application/json; charset=UTF-8',
                        },
                        params: {
                            service: '2fa',
                            action: 'disableForUser',
                        },
                    },
                    getDefaultCurrency: {
                        method: 'GET',
                        params: {
                            service: 'currency',
                            action: 'default',
                        },
                    },
                },
            );
            $rootScope.completedPoints = '';
            const wordFileTypes = ['doc', 'docx', 'xls', 'xlsx', 'pptx', 'ppt', 'rtf', 'odt'];
            const textFileTypes = ['pdf', 'ttf', 'txt'];
            const imageFileTypes = ['jpg', 'jpeg', 'png', 'gif'];

            String.prototype.replaceAll = function (search, replace) {
                return this.split(search).join(replace);
            };

            String.prototype.customTrim = function () {
                return this.replace(/\s+/g, ' ');
            };

            const isIncludesType = (types, fileType) => {
                return types.some((type) => fileType.includes(type));
            };

            service.swapFields = function (candidate) {
                let swap = candidate.lastName;
                candidate.lastName = candidate.firstName;
                candidate.firstName = swap;
            };

            service.updatedFieldsDict = {
                first_name: 'first_name',
                last_name: 'last_name',
                position: 'position',
                region: 'region',
                description: 'description',
                url: 'home_page',
                work_experience: 'work_experience',
                education: 'education',
                languages: 'languages',
                skills: 'skills',
                certifications: 'Certificates',
                photo: 'photo',
            };

            service.onGetDesiredSkills = {
                e00_no_experience: {
                    en: 'Experience_in',
                    ru: 'Experience_in',
                },
                e01_less_than1year: {
                    en: 'Less than 1 year',
                    ru: 'Меньше 1 года',
                },
                e1_1year: {
                    en: '1 year',
                    ru: '1 год',
                },
                e2_2years: {
                    en: '2 years',
                    ru: '2 года',
                },
                e3_3years: {
                    en: '3 years',
                    ru: '3 года',
                },
                e4_4years: {
                    en: '4 years',
                    ru: '4 года',
                },
                e5_5years: {
                    en: '5+ years',
                    ru: '5+ лет',
                },
                e6_10years: {
                    en: '10+ years',
                    ru: '10+ лет',
                },
            };

            service.requestGetVacancy = function (params) {
                $rootScope.loading = true;
                return new Promise((resolve, reject) => {
                    service.publicVacancy(
                        params,
                        (resp) => resolve(resp),
                        (error) => reject(error),
                    );
                });
            };
            service.transliterate = function (text) {
                text = text.toLowerCase();
                const arr = [
                    {
                        а: 'a',
                        б: 'b',
                        в: 'v',
                        г: 'g',
                        д: 'd',
                        е: 'e',
                        ё: 'e',
                        ж: 'zh',
                        з: 'z',
                        и: 'i',
                        й: 'i',
                        к: 'k',
                        л: 'l',
                        м: 'm',
                        н: 'n',
                        о: 'o',
                        п: 'p',
                        р: 'r',
                        с: 's',
                        т: 't',
                        у: 'u',
                        ф: 'f',
                        х: 'kh',
                        ц: 'с',
                        ч: 'ch',
                        ш: 'sh',
                        щ: 'shch',
                        ъ: 'ы',
                        yi: 'y',
                        ь: '',
                        э: 'e',
                        ю: 'ju',
                        я: 'ya',
                        ґ: 'g',
                        є: 'ie',
                        і: 'i',
                        ї: 'i',
                        ы: 'yi',
                    },
                    {
                        кс: 'x',
                        зг: 'zgh',
                        льг: 'lg',
                        а: 'a',
                        б: 'b',
                        в: 'w',
                        г: 'g',
                        д: 'd',
                        е: 'e',
                        ё: 'e',
                        ж: 'zh',
                        з: 'z',
                        и: 'i',
                        й: 'j',
                        к: 'k',
                        л: 'l',
                        м: 'm',
                        н: 'n',
                        о: 'o',
                        п: 'p',
                        р: 'r',
                        с: 's',
                        т: 't',
                        у: 'u',
                        ф: 'f',
                        х: 'h',
                        ц: 'тс',
                        ч: 'ch',
                        ш: 'sh',
                        щ: 'shh',
                        ъ: 'ie',
                        ы: 'y',
                        ь: '',
                        э: 'e',
                        ю: 'iu',
                        я: 'ia',
                        ґ: 'g',
                        є: 'ie',
                        і: 'i',
                        ї: 'i',
                    },
                    {
                        кс: 'x',
                        зг: 'zgh',
                        льг: 'lg',
                        дж: 'j',
                        а: 'a',
                        б: 'b',
                        в: 'w',
                        г: 'g',
                        д: 'd',
                        е: 'e',
                        ё: 'e',
                        ж: 'zh',
                        з: 'z',
                        и: 'i',
                        й: 'j',
                        к: 'k',
                        л: 'l',
                        м: 'm',
                        н: 'n',
                        о: 'o',
                        п: 'p',
                        р: 'r',
                        с: 's',
                        т: 't',
                        у: 'u',
                        ф: 'f',
                        х: 'h',
                        ц: 'тс',
                        ч: 'ch',
                        ш: 'sh',
                        щ: 'shh',
                        ъ: 'ie',
                        ы: 'y',
                        ь: '',
                        э: 'e',
                        ю: 'iu',
                        я: 'ia',
                        ґ: 'g',
                        є: 'ie',
                        і: 'i',
                        ї: 'i',
                    },
                    {
                        кс: 'x',
                        зг: 'zgh',
                        льг: 'lg',
                        дж: 'j',
                        а: 'a',
                        б: 'b',
                        в: 'v',
                        г: 'g',
                        д: 'd',
                        е: 'e',
                        ё: 'e',
                        ж: 'zh',
                        з: 'z',
                        и: 'i',
                        й: 'j',
                        к: 'k',
                        л: 'l',
                        м: 'm',
                        н: 'n',
                        о: 'o',
                        п: 'p',
                        р: 'r',
                        с: 's',
                        т: 't',
                        у: 'u',
                        ф: 'f',
                        х: 'h',
                        ц: 'тс',
                        ч: 'ch',
                        ш: 'sh',
                        щ: 'shh',
                        ъ: 'ie',
                        ы: 'y',
                        ь: '',
                        э: 'e',
                        ю: 'iu',
                        я: 'ia',
                        ґ: 'g',
                        є: 'ie',
                        і: 'i',
                        ї: 'i',
                    },
                    {
                        кс: 'x',
                        зг: 'zgh',
                        льг: 'lg',
                        а: 'a',
                        б: 'b',
                        в: 'v',
                        г: 'g',
                        д: 'd',
                        е: 'e',
                        ё: 'e',
                        ж: 'zh',
                        з: 'z',
                        и: 'i',
                        й: 'j',
                        к: 'k',
                        л: 'l',
                        м: 'm',
                        н: 'n',
                        о: 'o',
                        п: 'p',
                        р: 'r',
                        с: 's',
                        т: 't',
                        у: 'u',
                        ф: 'f',
                        х: 'h',
                        ц: 'тs',
                        ч: 'ch',
                        ш: 'sh',
                        щ: 'shh',
                        ъ: 'ie',
                        ы: 'y',
                        ь: '',
                        э: 'e',
                        ю: 'iu',
                        я: 'ia',
                        ґ: 'g',
                        є: 'ie',
                        і: 'i',
                        ї: 'i',
                    },
                    {
                        кс: 'x',
                        зг: 'zgh',
                        а: 'a',
                        б: 'b',
                        в: 'w',
                        г: 'g',
                        д: 'd',
                        е: 'e',
                        ё: 'e',
                        ж: 'zh',
                        з: 'z',
                        и: 'i',
                        й: 'y',
                        к: 'k',
                        л: 'l',
                        м: 'm',
                        н: 'n',
                        о: 'o',
                        п: 'p',
                        р: 'r',
                        с: 's',
                        т: 't',
                        у: 'u',
                        ф: 'f',
                        х: 'h',
                        ц: 'тs',
                        ч: 'ch',
                        ш: 'sh',
                        щ: 'shh',
                        ъ: 'ie',
                        ы: 'y',
                        ь: '',
                        э: 'e',
                        ю: 'iu',
                        я: 'ia',
                        ґ: 'g',
                        є: 'ie',
                        і: 'i',
                        ї: 'i',
                    },
                    {
                        кс: 'h',
                        зг: 'zgh',
                        а: 'a',
                        б: 'b',
                        в: 'v',
                        г: 'g',
                        д: 'd',
                        е: 'e',
                        ё: 'e',
                        ж: 'zh',
                        з: 'z',
                        и: 'i',
                        й: 'y',
                        к: 'k',
                        л: 'l',
                        м: 'm',
                        н: 'n',
                        о: 'o',
                        п: 'p',
                        р: 'r',
                        с: 's',
                        т: 't',
                        у: 'u',
                        ф: 'f',
                        х: 'kh',
                        ц: 'тс',
                        ч: 'ch',
                        ш: 'sh',
                        щ: 'shch',
                        ъ: '',
                        ы: 'y',
                        ь: "'",
                        э: 'e',
                        ю: 'yu',
                        я: 'ja',
                        ґ: 'g',
                        є: 'ie',
                        і: 'i',
                        ї: 'i',
                    },
                    {
                        кс: 'ks',
                        зг: 'zgh',
                        а: 'a',
                        б: 'b',
                        в: 'v',
                        г: 'h',
                        ґ: 'g',
                        д: 'd',
                        е: 'e',
                        ё: 'e',
                        є: 'ie',
                        ж: 'zh',
                        з: 'z',
                        и: 'y',
                        і: 'i',
                        ї: 'i',
                        й: 'y',
                        к: 'k',
                        л: 'l',
                        м: 'm',
                        н: 'n',
                        о: 'o',
                        п: 'p',
                        р: 'r',
                        с: 's',
                        т: 't',
                        у: 'u',
                        ф: 'f',
                        х: 'h',
                        ц: 'c',
                        ч: 'ch',
                        ш: 'sh',
                        ъ: '',
                        ы: 'y',
                        ь: "'",
                        э: 'e',
                        ю: 'yu',
                        я: 'ya',
                        щ: 'sc',
                    },
                    {
                        shch: 'щ',
                        yu: 'ю',
                        ij: 'ій',
                        zh: 'ж',
                        ch: 'ч',
                        sh: 'ш',
                        zgh: 'зг',
                        ya: 'ья',
                        ia: 'я',
                        a: 'а',
                        b: 'б',
                        v: 'в',
                        g: 'г',
                        d: 'д',
                        e: 'е',
                        z: 'з',
                        i: 'і',
                        j: 'й',
                        k: 'к',
                        l: 'л',
                        m: 'м',
                        n: 'н',
                        o: 'о',
                        p: 'п',
                        r: 'р',
                        s: 'с',
                        t: 'т',
                        u: 'у',
                        f: 'ф',
                        kh: 'х',
                        tc: 'ц',
                        y: 'й',
                        x: 'кс',
                        c: 'с',
                        q: 'к',
                        w: 'в',
                        h: 'х',
                        "'": 'ь',
                    },
                    {
                        shch: 'щ',
                        lg: 'льг',
                        yu: 'ю',
                        ij: 'ий',
                        zh: 'ж',
                        ch: 'ч',
                        sh: 'ш',
                        zgh: 'зг',
                        ya: 'я',
                        ia: 'я',
                        a: 'а',
                        b: 'б',
                        v: 'в',
                        g: 'г',
                        d: 'д',
                        e: 'е',
                        z: 'з',
                        i: 'и',
                        j: 'й',
                        k: 'к',
                        l: 'л',
                        m: 'м',
                        n: 'н',
                        o: 'о',
                        p: 'п',
                        r: 'р',
                        s: 'с',
                        t: 'т',
                        u: 'у',
                        f: 'ф',
                        kh: 'х',
                        tc: 'ц',
                        y: 'ы',
                        x: 'кс',
                        c: 'с',
                        q: 'к',
                        w: 'в',
                        h: 'х',
                        "'": 'ь',
                    },
                    {
                        zh: 'ж',
                        yu: 'ю',
                        ch: 'ч',
                        sh: 'ш',
                        zgh: 'зг',
                        ya: 'я',
                        ia: 'я',
                        ts: 'ц',
                        j: 'дж',
                        shch: 'щ',
                        sch: 'ш',
                        tc: 'ц',
                        tz: 'ц',
                        ju: 'ю',
                        a: 'а',
                        b: 'б',
                        g: 'г',
                        d: 'д',
                        e: 'э',
                        z: 'з',
                        i: 'и',
                        q: 'к',
                        x: 'кс',
                        k: 'к',
                        l: 'л',
                        m: 'м',
                        n: 'н',
                        o: 'о',
                        p: 'п',
                        r: 'р',
                        s: 'с',
                        t: 'т',
                        u: 'у',
                        f: 'ф',
                        h: 'х',
                        y: 'й',
                        w: 'в',
                        v: 'в',
                        c: 'к',
                        yi: 'ы',
                        ij: 'ий',
                        "'": 'ь',
                    },
                    {
                        zh: 'ж',
                        yu: 'ю',
                        ch: 'ч',
                        sh: 'ш',
                        zgh: 'зг',
                        ya: 'я',
                        ia: 'я',
                        ts: 'ц',
                        j: 'дж',
                        shch: 'щ',
                        sch: 'ш',
                        tc: 'ц',
                        tz: 'ц',
                        iu: 'ю',
                        a: 'а',
                        b: 'б',
                        g: 'г',
                        d: 'д',
                        e: 'е',
                        yo: 'ё',
                        z: 'з',
                        q: 'к',
                        x: 'кс',
                        yi: 'ы',
                        i: 'и',
                        y: 'й',
                        k: 'к',
                        l: 'л',
                        m: 'м',
                        n: 'н',
                        o: 'о',
                        p: 'п',
                        r: 'р',
                        s: 'с',
                        t: 'т',
                        u: 'у',
                        f: 'ф',
                        h: 'х',
                        w: 'в',
                        v: 'в',
                        c: 'к',
                        "'": 'ь',
                    },
                    {
                        zh: 'ж',
                        ch: 'ч',
                        eh: 'э',
                        sh: 'ш',
                        zgh: 'зг',
                        ya: 'я',
                        ia: 'я',
                        ts: 'ц',
                        j: 'дж',
                        shch: 'щ',
                        sch: 'ш',
                        tc: 'ц',
                        tz: 'ц',
                        iu: 'ю',
                        a: 'а',
                        b: 'б',
                        g: 'г',
                        d: 'д',
                        e: 'ё',
                        z: 'з',
                        q: 'к',
                        x: 'кс',
                        yi: 'ы',
                        i: 'и',
                        y: 'ы',
                        k: 'к',
                        l: 'л',
                        m: 'м',
                        n: 'н',
                        o: 'о',
                        p: 'п',
                        r: 'р',
                        s: 'с',
                        t: 'т',
                        u: 'у',
                        f: 'ф',
                        h: 'х',
                        w: 'в',
                        v: 'в',
                        c: 'ц',
                        "'": 'ь',
                    },
                    {
                        shch: 'щ',
                        yu: 'ю',
                        ij: 'ій',
                        zh: 'ж',
                        ch: 'ч',
                        sh: 'ш',
                        zgh: 'зг',
                        ya: 'ья',
                        ia: 'я',
                        a: 'а',
                        b: 'б',
                        v: 'в',
                        g: 'г',
                        d: 'д',
                        e: 'ё',
                        z: 'з',
                        i: 'і',
                        j: 'й',
                        k: 'к',
                        l: 'л',
                        m: 'м',
                        n: 'н',
                        o: 'о',
                        p: 'п',
                        r: 'р',
                        s: 'с',
                        t: 'т',
                        u: 'у',
                        f: 'ф',
                        kh: 'х',
                        tc: 'ц',
                        y: 'й',
                        x: 'кс',
                        c: 'с',
                        q: 'к',
                        w: 'в',
                        h: 'х',
                        "'": 'ь',
                    },
                ];
                let arrResults = [];
                arr.forEach((item) => {
                    let currText = text;
                    Object.keys(item).forEach((i) => {
                        currText = currText.replaceAll(i, item[i]);
                    });
                    arrResults.push(currText);
                });

                return arrResults;
            };
            service.getRegions2 = function (callback) {
                service.getRegionsTwo(
                    function (resp) {
                        var country = [];
                        var cities = [];
                        var sortArrayCountry = [];
                        var sortArrayCities = [];
                        var sortObjectCountry = {};
                        var sortObjectCities = {};

                        const areaKey = 'area' + $filter('bigFirstLetter')($rootScope.currentLang);

                        angular.forEach(resp.object, function (valC, keyC) {
                            country.push({
                                id: keyC,
                                value: keyC,
                                name: keyC,
                                type: 'country',
                                showName: keyC,
                                country: keyC,
                                nameRu: keyC,
                            });
                            angular.forEach(valC, function (val, key) {
                                if (val.type == 'country') {
                                    country.push({
                                        id: val.displayCity + keyC,
                                        value: val.regionId,
                                        name: val.displayCity,
                                        type: 'country',
                                        showName: val.displayCity,
                                        nameRu:
                                            val.googlePlaceId == undefined || val.googlePlaceId.cityRu == undefined
                                                ? val.city
                                                : val.googlePlaceId.cityRu,
                                        nameEn:
                                            val.googlePlaceId == undefined || val.googlePlaceId.cityEn == undefined
                                                ? val.city
                                                : val.googlePlaceId.cityEn,
                                        country: keyC,
                                        countryRu:
                                            val.googlePlaceId == undefined || val.googlePlaceId.countryRu == undefined
                                                ? val.country
                                                : val.googlePlaceId.countryRu,
                                    });
                                } else {
                                    const cityIndexes = [];
                                    for (i = 0; i < valC.length; i++) {
                                        if (valC[i].displayCity === val.displayCity) {
                                            cityIndexes.push(i);
                                        }
                                    }

                                    const showRegion =
                                        cityIndexes.length > 1 && val.googlePlaceId && val.googlePlaceId[areaKey];

                                    cities.push({
                                        id: `${val.displayCity}${keyC}${val.regionId.slice(0, 6)}`,
                                        value: val.regionId,
                                        name: val.displayCity,
                                        type: 'city',
                                        showName: showRegion
                                            ? `${val.displayCity} (${val.googlePlaceId[areaKey]})`
                                            : val.displayCity,
                                        nameRu:
                                            val.googlePlaceId == undefined || val.googlePlaceId.cityRu == undefined
                                                ? val.city
                                                : val.googlePlaceId.cityRu,
                                        nameEn:
                                            val.googlePlaceId == undefined || val.googlePlaceId.cityEn == undefined
                                                ? val.city
                                                : val.googlePlaceId.cityEn,
                                        country: keyC,
                                        countryRu:
                                            val.googlePlaceId == undefined || val.googlePlaceId.countryRu == undefined
                                                ? val.country
                                                : val.googlePlaceId.countryRu,

                                        areaEn: val.googlePlaceId?.areaEn || null,
                                        areaPl: val.googlePlaceId?.areaPl || null,
                                        areaUa: val.googlePlaceId?.areaUa || null,
                                        areaRu: val.googlePlaceId?.areaRu || null,
                                    });
                                }
                                angular.forEach(country, function (valIn, keyIn) {
                                    if (valIn.value == val.country) {
                                        valIn.name = val.displayCountry;
                                        valIn.showName = val.displayCountry;
                                        valIn.nameRu =
                                            val.googlePlaceId == undefined || val.googlePlaceId.countryRu == undefined
                                                ? val.country
                                                : val.googlePlaceId.countryRu;
                                    }
                                });
                            });
                        });
                        angular.forEach(country, function (data) {
                            sortArrayCountry.push(data.id);
                        });
                        angular.forEach(cities, function (data) {
                            sortArrayCities.push(data.id);
                        });
                        sortArrayCountry = sortArrayCountry.sort();
                        sortArrayCities = sortArrayCities.sort();

                        angular.forEach(sortArrayCountry, function (name, key) {
                            angular.forEach(country, function (obj) {
                                if (name == obj.id) {
                                    sortObjectCountry[key] = obj;
                                }
                            });
                        });
                        angular.forEach(sortArrayCities, function (name, key) {
                            angular.forEach(cities, function (obj) {
                                if (name == obj.id) {
                                    sortObjectCities[key] = obj;
                                }
                            });
                        });
                        if (callback != undefined) callback(sortObjectCountry, sortObjectCities);
                    },
                    function (error) {
                        console.error(error);
                    },
                );
            };

            let regions = [];

            service.saveRegions2 = function (callback) {
                if (regions.length === 0 || regions.regionLang !== $rootScope.currentLang) {
                    service.getRegions2(function (sortObjectCountry, sortObjectCities) {
                        regions = [sortObjectCountry, sortObjectCities];
                        regions.regionLang = $rootScope.currentLang;
                        callback(...regions);
                    });
                } else {
                    callback(...regions);
                }
            };

            service.getIndustries = function () {
                return [
                    { value: 'Accounting, Auditing', label: 'Accounting, Auditing' },
                    { value: 'Agriculture, agribusiness', label: 'Agriculture, agribusiness' },
                    { value: 'Automotive', label: 'Automotive' },
                    { value: 'Aviation & Aerospace', label: 'Aviation & Aerospace' },
                    { value: 'Beauty, fitness and sports', label: 'Beauty, fitness and sports' },
                    { value: 'Charity & NGO', label: 'Charity & NGO' },
                    { value: 'Chemicals', label: 'Chemicals' },
                    { value: 'Construction and architecture', label: 'Construction and architecture' },
                    { value: 'Consulting', label: 'Consulting' },
                    { value: 'Consumer Goods', label: 'Consumer Goods' },
                    { value: 'Culture, music, show business', label: 'Culture, music, show business' },
                    { value: 'Design, creativity', label: 'Design, creativity' },
                    { value: 'Energy industry', label: 'Energy industry' },
                    { value: 'E-Commerce', label: 'E-Commerce' },
                    { value: 'Education', label: 'Education' },
                    { value: 'Engineering', label: 'Engineering' },
                    { value: 'Engineering Consulting', label: 'Engineering Consulting' },
                    { value: 'Engineering Services', label: 'Engineering Services' },
                    { value: 'Finance, bank', label: 'Finance, bank' },
                    { value: 'FMCG', label: 'FMCG' },
                    { value: 'Government', label: 'Government' },
                    { value: 'Healthcare, hospital', label: 'Healthcare, hospital' },
                    { value: 'HR Management, HR', label: "HR Management, HR'" },
                    {
                        value: 'Hotel and restaurant business, tourism',
                        label: 'Hotel and restaurant business, tourism',
                    },
                    { value: 'Insurance', label: 'Insurance' },
                    { value: 'IT, computers, the Internet', label: 'IT, computers, the Internet' },
                    { value: 'IT Consulting', label: 'IT Consulting' },
                    { value: 'Logistics, warehouse, Foreign Trade', label: 'Logistics, warehouse, Foreign Trade' },
                    { value: 'Legal', label: 'Legal' },
                    { value: 'Manufacturing', label: 'Manufacturing' },
                    { value: 'Marketing, Advertising, PR', label: 'Marketing, Advertising, PR' },
                    { value: 'Media, publishing, printing', label: 'Media, publishing, printing' },
                    { value: 'Medicine, pharmacy', label: 'Medicine, pharmacy' },
                    { value: 'Mining', label: 'Mining' },
                    { value: 'Network Marketing and MLM', label: 'Network Marketing and MLM' },
                    { value: 'Oil & Gas', label: 'Oil & Gas' },
                    { value: 'Real Estate', label: 'Real Estate' },
                    { value: 'Retail', label: 'Retail' },
                    { value: 'Sales Jobs', label: 'Sales Jobs' },
                    { value: 'Sea', label: 'Sea' },
                    { value: 'Secretariat, outsourcing, ACS', label: 'Secretariat, outsourcing, ACS' },
                    { value: 'Security, Safety', label: 'Security, Safety' },
                    { value: 'Service industries', label: 'Service industries' },
                    { value: 'Telecommunications', label: 'Telecommunications' },
                    { value: 'Top management, senior management', label: 'Top management, senior management' },
                    { value: 'Transport, Telecom', label: 'Transport, Telecom' },
                    { value: 'Travel & Tourism', label: 'Travel & Tourism' },
                    { value: 'Work at home', label: 'Work at home' },
                    { value: 'Work for students, early career', label: 'Work for students, early career' },
                    { value: 'Work without special training', label: 'Work without special training' },
                    { value: 'Other areas of activity', label: 'Other areas of activity' },
                ];
            };

            service.getSalary = function () {
                return [
                    { name: 'up to 500', salaryFrom: '0', salaryTo: '500' },
                    { name: '500-1500', salaryFrom: '500', salaryTo: '1500' },
                    { name: '1500-2500', salaryFrom: '1500', salaryTo: '2500' },
                    { name: '2500-3500', salaryFrom: '2500', salaryTo: '3500' },
                    { name: '3500-4500', salaryFrom: '3500', salaryTo: '4500' },
                    { name: 'more', salaryFrom: '4500', salaryTo: '' },
                ];
            };

            service.currency = function () {
                return [
                    { label: 'UAH', value: 'UAH' },
                    { label: 'USD', value: 'USD' },
                    { label: 'EUR', value: 'EUR' },
                    { label: 'PLN', value: 'PLN' },
                    { label: 'AMD', value: 'AMD' },
                    { label: 'ARS', value: 'ARS' },
                    { label: 'AUD', value: 'AUD' },
                    { label: 'AZN', value: 'AZN' },
                    { label: 'BRL', value: 'BRL' },
                    { label: 'BYN', value: 'BYN' },
                    { label: 'CAD', value: 'CAD' },
                    { label: 'CNY', value: 'CNY' },
                    { label: 'COP', value: 'COP' },
                    { label: 'CZK', value: 'CZK' },
                    { label: 'DZD', value: 'CZK' },
                    { label: 'EGP', value: 'EGP' },
                    { label: 'GBP', value: 'GBP' },
                    { label: 'GEL', value: 'GEL' },
                    { label: 'HKD', value: 'HKD' },
                    { label: 'IDR', value: 'IDR' },
                    { label: 'INR', value: 'INR' },
                    { label: 'JPY', value: 'JPY' },
                    { label: 'KGS', value: 'KGS' },
                    { label: 'KRW', value: 'KRW' },
                    { label: 'KZT', value: 'KZT' },
                    { label: 'LKR', value: 'LKR' },
                    { label: 'MDL', value: 'MDL' },
                    { label: 'MXN', value: 'MXN' },
                    { label: 'MYR', value: 'MYR' },
                    { label: 'PHP', value: 'PHP' },
                    { label: 'RON', value: 'RON' },
                    { label: 'RUB', value: 'RUB' },
                    { label: 'SGD', value: 'SGD' },
                    { label: 'THB', value: 'THB' },
                    { label: 'UZS', value: 'UZS' },
                    { label: 'ZAR', value: 'ZAR' },
                ];
            };
            service.positionLevel = function () {
                return [
                    { value: 'specialty workers' },
                    { value: 'specialist  (entry level)' },
                    { value: 'specialist' },
                    { value: 'Senior Specialist / Team Leader' },
                    { value: 'middle manager / head of department' },
                    { value: 'top manager / CEO / President' },
                ];
            };

            service.employmentType = function () {
                return [
                    { label: 'employment_type_assoc.fullEmployment', value: 'fullEmployment' },
                    { label: 'employment_type_assoc.underemployment', value: 'underemployment' },
                    { label: 'employment_type_assoc.remote', value: 'remote' },
                    { label: 'employment_type_assoc.trainingAndPractice', value: 'trainingAndPractice' },
                    { label: 'employment_type_assoc.projectWork', value: 'projectWork' },
                    { label: 'employment_type_assoc.temporaryWork', value: 'temporaryWork' },
                    { label: 'employment_type_assoc.shiftWork', value: 'shiftWork' },
                    { label: 'employment_type_assoc.relocate', value: 'relocate' },
                ];
            };
            service.numberPosition = function () {
                return [
                    { value: '1' },
                    { value: '2' },
                    { value: '3' },
                    { value: '4' },
                    { value: '5' },
                    { value: '6' },
                    { value: '7' },
                    { value: '8' },
                    { value: '9' },
                    { value: '10' },
                ];
            };
            service.employmentTypeTwo = function () {
                if ($translate.use() == 'ua') {
                    return [
                        { text: 'повна зайнятість', id: 'fullEmployment' },
                        { text: 'неповна зайнятість ', id: 'underemployment' },
                        { text: 'Віддалена робота ', id: 'remote' },
                        {
                            text: 'навчання, практика',
                            id: 'trainingAndPractice',
                        },
                        { text: 'проектна робота', id: 'projectWork' },
                        {
                            text: 'сезонна, тимчасова робота',
                            id: 'temporaryWork',
                        },
                        {
                            text: 'позмінна робота/вахтовий метод',
                            id: 'shiftWork',
                        },
                    ];
                } else if ($translate.use() == 'ru') {
                    return [
                        { text: 'полная занятость', id: 'fullEmployment' },
                        { text: 'неполная занятость ', id: 'underemployment' },
                        { text: 'удаленная работа ', id: 'remote' },
                        {
                            text: 'обучение, практика',
                            id: 'trainingAndPractice',
                        },
                        { text: 'проектная работа', id: 'projectWork' },
                        {
                            text: 'сезонная, временная работа',
                            id: 'temporaryWork',
                        },
                        { text: 'переезд', id: 'relocate' },
                        { text: 'работа по сменам/вахтовый метод', id: 'shiftWork' },
                    ];
                } else if ($translate.use() == 'pl') {
                    return [
                        { text: 'pełny etat', id: 'fullEmployment' },
                        { text: 'niepełny etat ', id: 'underemployment' },
                        { text: 'praca zdalna ', id: 'remote' },
                        {
                            text: 'szkolenia, praktyki',
                            id: 'trainingAndPractice',
                        },
                        { text: 'praca nad projektem', id: 'projectWork' },
                        {
                            text: 'sezonowa, tymczasowa praca',
                            id: 'temporaryWork',
                        },
                        { text: 'relokacja', id: 'relocate' },
                        { text: 'praca zmianowa/praca na zmiany', id: 'shiftWork' },
                    ];
                } else {
                    return [
                        { text: 'Full Time', id: 'fullEmployment' },
                        { text: 'Part Time', id: 'underemployment' },
                        { text: 'Remote', id: 'remote' },
                        {
                            text: 'Training, Practice',
                            id: 'trainingAndPractice',
                        },
                        { text: 'Project', id: 'projectWork' },
                        { text: 'Temporary', id: 'temporaryWork' },
                        { text: 'Relocate', id: 'relocate' },
                        { text: 'Shift work/rotational shift work', id: 'shiftWork' },
                    ];
                }
            };
            service.createArrayByEmploymentType = function (arrayOfName) {
                var array = service.employmentTypeTwo();
                var respArray = [];
                angular.forEach(arrayOfName, function (valueOfName) {
                    angular.forEach(array, function (vlOfArr) {
                        if (valueOfName == vlOfArr.id) {
                            respArray.push(vlOfArr);
                        }
                    });
                });
                return respArray;
            };

            service.experience = function () {
                return [
                    { label: 'experience_assoc.e00_no_experience', value: 'e00_no_experience' },
                    { label: 'experience_assoc.e01_less_than1year', value: 'e01_less_than1year' },
                    { label: 'experience_assoc.e1_1year', value: 'e1_1year' },
                    { label: 'experience_assoc.e2_2years', value: 'e2_2years' },
                    { label: 'experience_assoc.e3_3years', value: 'e3_3years' },
                    { label: 'experience_assoc.e4_4years', value: 'e4_4years' },
                    { label: 'experience_assoc.e5_5years', value: 'e5_5years' },
                    { label: 'experience_assoc.e6_10years', value: 'e6_10years' },
                ];
            };
            service.skillCandidateExperience = function () {
                return [
                    {
                        label: 'e00_no_experience',
                        value: 'e00_no_experience',
                    },
                    {
                        label: 'advancedSearchSkills.no_experience',
                        value: 'no_experience',
                    },
                    {
                        label: 'advancedSearchSkills.e01_less_than1year',
                        value: 'e01_less_than1year',
                    },
                    {
                        label: 'advancedSearchSkills.e1_1year',
                        value: 'e1_1year',
                    },
                    {
                        label: 'advancedSearchSkills.e2_2years',
                        value: 'e2_2years',
                    },
                    {
                        label: 'advancedSearchSkills.e3_3years',
                        value: 'e3_3years',
                    },
                    {
                        label: 'advancedSearchSkills.e4_4years',
                        value: 'e4_4years',
                    },
                    {
                        label: 'advancedSearchSkills.e5_5years',
                        value: 'e5_5years',
                    },
                    {
                        label: 'advancedSearchSkills.e6_10years',
                        value: 'e6_10years',
                    },
                ];
            };
            service.skillExperience = function () {
                return [
                    {
                        label: 'experience_skills.e00_no_experience',
                        value: 'e00_no_experience',
                    },
                    {
                        label: 'advancedSearchSkills.e01_less_than1year',
                        value: 'e01_less_than1year',
                    },
                    {
                        label: 'advancedSearchSkills.e1_1year',
                        value: 'e1_1year',
                    },
                    {
                        label: 'advancedSearchSkills.e2_2years',
                        value: 'e2_2years',
                    },
                    {
                        label: 'advancedSearchSkills.e3_3years',
                        value: 'e3_3years',
                    },
                    {
                        label: 'advancedSearchSkills.e4_4years',
                        value: 'e4_4years',
                    },
                    {
                        label: 'advancedSearchSkills.e5_5years',
                        value: 'e5_5years',
                    },
                    {
                        label: 'advancedSearchSkills.e6_10years',
                        value: 'e6_10years',
                    },
                ];
            };
            service.skillVacancyExperience = function () {
                return [
                    {
                        label: 'e00_no_experience',
                        value: 'e00_no_experience',
                    },
                    {
                        label: 'advancedSearchSkills.e01_less_than1year',
                        value: 'e01_less_than1year',
                    },
                    {
                        label: 'advancedSearchSkills.e1_1year',
                        value: 'e1_1year',
                    },
                    {
                        label: 'advancedSearchSkills.e2_2years',
                        value: 'e2_2years',
                    },
                    {
                        label: 'advancedSearchSkills.e3_3years',
                        value: 'e3_3years',
                    },
                    {
                        label: 'advancedSearchSkills.e4_4years',
                        value: 'e4_4years',
                    },
                    {
                        label: 'advancedSearchSkills.e5_5years',
                        value: 'e5_5years',
                    },
                    {
                        label: 'advancedSearchSkills.e6_10years',
                        value: 'e6_10years',
                    },
                ];
            };
            service.languagesLevel = function () {
                return [
                    {
                        label: 'Basic',
                        value: 'Basic',
                    },
                    {
                        label: 'Pre_Intermediate',
                        value: 'Pre_Intermediate',
                    },
                    {
                        label: 'Intermediate',
                        value: 'Intermediate',
                    },
                    {
                        label: 'Upper_Intermediate',
                        value: 'Upper_Intermediate',
                    },
                    {
                        label: 'Advanced',
                        value: 'Advanced',
                    },
                    {
                        label: 'Proficient',
                        value: 'Proficient',
                    },
                    {
                        label: 'Native',
                        value: 'Native',
                    },
                ];
            };
            var UALang = [
                'Англійська розмовна',
                'Англійська професійна',
                'Англійська середня',
                'Англійська початківець',
                'Білоруська розмовна',
                'Білоруська професійна',
                'Білоруська середня',
                'Білоруська початківець',
                'Іспанська розмовна',
                'Іспанська професійна',
                'Іспанська середня',
                'Іспанська початківець',
                'Італійська розмовна',
                'Італійська професійна',
                'Італійська середня',
                'Італійська початківець',
                'Казахська розмовна',
                'Казахська професійна',
                'Казахська середня',
                'Казахська початківець',
                'Китайська розмовна',
                'Китайська професійна',
                'Китайська середня',
                'Китайська початківець',
                'Малайська розмовна',
                'Малайська професійна',
                'Малайська середня',
                'Малайська початківець',
                'Мандаринська розмовна',
                'Мандаринська професійна',
                'Мандаринська середня',
                'Мандаринська початківець',
                'Молдавська розмовна',
                'Молдавська професійна',
                'Молдавська середня',
                'Молдавська початківець',
                'Німецька розмовна',
                'Німецька професійна',
                'Німецька середня',
                'Німецька початківець',
                'Португальська розмовна',
                'Португальська професійна',
                'Португальська середня',
                'Португальська початківець',
                'Російська розмовна',
                'Російська професійна',
                'Російська середня',
                'Російська початківець',
                'Тамільська розмовна',
                'Тамільська професійна',
                'Тамільська середня',
                'Тамільська початківець',
                'Українська розмовна',
                'Українська професійна',
                'Українська середня',
                'Українська початківець',
                'Французька розмовна',
                'Французька професійна',
                'Французька середня',
                'Французька початківець',
                'Гінді розмовна',
                'Гінді професійна',
                'Гінді середня',
                'Гінді початківець',
                'Японська розмовна',
                'Японська професійна',
                'Японська середня',
                'Японська початківець',
            ];

            var RULang = [
                'Английский разговорный',
                'Английский профессиональный',
                'Английский средний',
                'Английский начинающий',
                'Белорусский разговорный',
                'Белорусский профессиональный',
                'Белорусский средний',
                'Белорусский начинающий',
                'Испанский разговорный',
                'Испанский профессиональный',
                'Испанский средний',
                'Испанский начинающий',
                'Итальянский разговорный',
                'Итальянский профессиональный',
                'Итальянский средний',
                'Итальянский начинающий',
                'Казахский разговорный',
                'Казахский профессиональный',
                'Казахский средний',
                'Казахский начинающий',
                'Китайский разговорный',
                'Китайский профессиональный',
                'Китайский средний',
                'Китайский начинающий',
                'Малайский разговорный',
                'Малайский профессиональный',
                'Малайский средний',
                'Малайский начинающий',
                'Мандаринский разговорный',
                'Мандаринский профессиональный',
                'Мандаринский средний',
                'Мандаринский начинающий',
                'Молдавский разговорный',
                'Молдавский профессиональный',
                'Молдавский средний',
                'Молдавский начинающий',
                'Немецкий разговорный',
                'Немецкий профессиональный',
                'Немецкий средний',
                'Немецкий начинающий',
                'Португальский разговорный',
                'Португальский профессиональный',
                'Португальский средний',
                'Португальский начинающий',
                'Русский разговорный',
                'Русский профессиональный',
                'Русский средний',
                'Русский начинающий',
                'Тамильский разговорный',
                'Тамильский профессиональный',
                'Тамильский средний',
                'Тамильский начинающий',
                'Украинский разговорный',
                'Украинский профессиональный',
                'Украинский средний',
                'Украинский начинающий',
                'Французский разговорный',
                'Французский профессиональный',
                'Французский средний',
                'Французский начинающий',
                'Хинди разговорный',
                'Хинди профессиональный',
                'Хинди средний',
                'Хинди начинающий',
                'Японский разговорный',
                'Японский профессиональный',
                'Японский средний',
                'Японский начинающий',
            ];
            var ENLang = [
                'English Oral',
                'English Professional',
                'English Intermediate',
                'English Elementary',
                'Belarusian Oral',
                'Belarusian Professional',
                'Belarusian Intermediate',
                'Belarusian Elementary',
                'Spanish Oral',
                'Spanish Professional',
                'Spanish Intermediate',
                'Spanish Elementary',
                'Italian Oral',
                'Italian Professional',
                'Italian Intermediate',
                'Italian Elementary',
                'Kazakh Oral',
                'Kazakh Professional',
                'Kazakh Intermediate',
                'Kazakh Elementary',
                'Chinese Oral',
                'Chinese Professional',
                'Chinese Intermediate',
                'Chinese Elementary',
                'Malay Oral',
                'Malay Professional',
                'Malay Intermediate',
                'Malay Elementary',
                'Mandarin Oral',
                'Mandarin Professional',
                'Mandarin Intermediate',
                'Mandarin Elementary',
                'Moldovan Oral',
                'Moldovan Professional',
                'Moldovan Intermediate',
                'Moldovan Elementary',
                'German Oral',
                'German Professional',
                'German Intermediate',
                'German Elementary',
                'Portuguese Oral',
                'Portuguese Professional',
                'Portuguese Intermediate',
                'Portuguese Elementary',
                'Russian Oral',
                'Russian Professional',
                'Russian Intermediate',
                'Russian Elementary',
                'Tamil Oral',
                'Tamil Professional',
                'Tamil Intermediate',
                'Tamil Elementary',
                'Ukrainian Oral',
                'Ukrainian Professional',
                'Ukrainian Intermediate',
                'Ukrainian Elementary',
                'French Oral',
                'French Professional',
                'French Intermediate',
                'French Elementary',
                'Hindi Oral',
                'Hindi Professional',
                'Hindi Intermediate',
                'Hindi Elementary',
                'Japanese Oral',
                'Japanese Professional',
                'Japanese Intermediate',
                'Japanese Elementary',
            ];

            service.lang = function () {
                if ($translate.use() == 'ua') {
                    return UALang;
                } else if ($translate.use() == 'ru') {
                    return RULang;
                } else {
                    return ENLang;
                }
            };
            service.langTranslator = function (currentLang) {
                if (!currentLang || currentLang.length == 0) {
                    return currentLang;
                }
                currentLang = currentLang.replace(/,/g, ', ');
                for (var i = 0; i < ENLang.length; i++) {
                    if ($translate.use() == 'ua') {
                        currentLang = currentLang.replace(ENLang[i], UALang[i]).replace(RULang[i], UALang[i]);
                    }
                    if ($translate.use() == 'en') {
                        currentLang = currentLang.replace(UALang[i], ENLang[i]).replace(RULang[i], ENLang[i]);
                    }
                    if ($translate.use() == 'ru') {
                        currentLang = currentLang.replace(ENLang[i], RULang[i]).replace(UALang[i], RULang[i]);
                    }
                }
                return currentLang;
            };

            service.gender = function ($scope) {
                $scope.sexObjectRU = [
                    { name: 'Мужчина', value: true },
                    { name: 'Женщина', value: false },
                ];
                $scope.sexObject = [
                    { name: 'Male', value: true },
                    { name: 'Female', value: false },
                ];
                $scope.sexObjectUA = [
                    { name: 'Чоловік', value: true },
                    { name: 'Жінка', value: false },
                ];
            };

            service.genderTwo = function ($scope) {
                $scope.sexObjectRU = [
                    { name: 'Мужчина', value: true },
                    { name: 'Женщина', value: false },
                    { name: 'Не имеет значения', value: null },
                ];
                $scope.sexObject = [
                    { name: 'Male', value: true },
                    { name: 'Female', value: false },
                    { name: "Doesn't matter", value: null },
                ];
                $scope.sexObjectUA = [
                    { name: 'Чоловік', value: true },
                    { name: 'Жінка', value: false },
                    { name: 'Не має значення', value: null },
                ];
            };

            service.toAddCandidate = function (path) {
                $location.path('candidate/add');
            };
            service.toEditCandidate = function (id, sliderId, path) {
                if (sliderId) {
                    $location.path('candidate/edit/slide/' + sliderId + '/' + id);
                } else {
                    $location.path('candidate/edit/' + id);
                }
            };
            service.toMergeCandidate = function (id, path) {
                $location.path('candidate/merge/' + id);
            };

            service.getCountryLinkedIn = function () {
                return [
                    { key: 'ru', value: 'Россия' },
                    { key: 'au', value: 'Австралия' },
                    { key: 'at', value: 'Австрия' },
                    { key: 'az', value: 'Азербайджан' },
                    { key: 'ax', value: 'Аландские о-ва' },
                    { key: 'al', value: 'Албания' },
                    { key: 'dz', value: 'Алжир' },
                    { key: 'as', value: 'Американское Самоа' },
                    { key: 'ai', value: 'Ангилья' },
                    { key: 'ao', value: 'Ангола' },
                    { key: 'ad', value: 'Андорра' },
                    { key: 'aq', value: 'Антарктида' },
                    { key: 'ag', value: 'Антигуа и Барбуда' },
                    { key: 'ar', value: 'Аргентина' },
                    { key: 'am', value: 'Армения' },
                    { key: 'aw', value: 'Аруба' },
                    { key: 'af', value: 'Афганистан' },
                    { key: 'bs', value: 'Багамские о-ва' },
                    { key: 'bd', value: 'Бангладеш' },
                    { key: 'bb', value: 'Барбадос' },
                    { key: 'bh', value: 'Бахрейн' },
                    { key: 'by', value: 'Беларусь' },
                    { key: 'bz', value: 'Белиз' },
                    { key: 'be', value: 'Бельгия' },
                    { key: 'bj', value: 'Бенин' },
                    { key: 'bm', value: 'Бермудские о-ва' },
                    { key: 'bg', value: 'Болгария' },
                    { key: 'bo', value: 'Боливия' },
                    { key: 'ba', value: 'Босния и Герцеговина' },
                    { key: 'bw', value: 'Ботсвана' },
                    { key: 'br', value: 'Бразилия' },
                    {
                        key: 'io',
                        value: 'Британская территория в Индийском океане',
                    },
                    { key: 'bn', value: 'Бруней-Даруссалам' },
                    { key: 'bf', value: 'Буркина-Фасо' },
                    { key: 'bi', value: 'Бурунди' },
                    { key: 'bt', value: 'Бутан' },
                    { key: 'vu', value: 'Вануату' },
                    { key: 'va', value: 'Ватикан' },
                    { key: 'gb', value: 'Великобритания' },
                    { key: 'hu', value: 'Венгрия' },
                    { key: 've', value: 'Венесуэла' },
                    { key: 'vg', value: 'Виргинские о-ва (Британские)' },
                    { key: 'vi', value: 'Виргинские о-ва (США)' },
                    { key: 'tl', value: 'Восточный Тимор' },
                    { key: 'tp', value: 'Восточный Тимор' },
                    { key: 'tl', value: 'Восточный Тимор' },
                    { key: 'tp', value: 'Восточный Тимор' },
                    { key: 'vn', value: 'Вьетнама' },
                    { key: 'ga', value: 'Габон' },
                    { key: 'ht', value: 'Гаити' },
                    { key: 'gy', value: 'Гайана' },
                    { key: 'gm', value: 'Гамбия' },
                    { key: 'gh', value: 'Гана' },
                    { key: 'gp', value: 'Гваделупа' },
                    { key: 'gt', value: 'Гватемала' },
                    { key: 'gn', value: 'Гвинея' },
                    { key: 'gw', value: 'Гвинея-Бисау' },
                    { key: 'de', value: 'Германия' },
                    { key: 'gg', value: 'Гернси' },
                    { key: 'gi', value: 'Гибралтар' },
                    { key: 'hn', value: 'Гондурас' },
                    { key: 'hk', value: 'Гонконг' },
                    { key: 'gd', value: 'Гренада' },
                    { key: 'gl', value: 'Гренландия' },
                    { key: 'gr', value: 'Греция' },
                    { key: 'ge', value: 'Грузия' },
                    { key: 'gu', value: 'Гуам' },
                    { key: 'dk', value: 'Дания' },
                    { key: 'cd', value: 'Демократическая Республика Конго' },
                    { key: 'je', value: 'Джерси' },
                    { key: 'dj', value: 'Джибути' },
                    { key: 'dm', value: 'Доминика' },
                    { key: 'do', value: 'Доминиканская Республика' },
                    { key: 'eg', value: 'Египет' },
                    { key: 'zm', value: 'Замбия' },
                    { key: 'eh', value: 'Западная Сахара' },
                    { key: 'zw', value: 'Зимбабве' },
                    { key: 'il', value: 'Израиль' },
                    { key: 'in', value: 'Индия' },
                    { key: 'id', value: 'Индонезия' },
                    { key: 'jo', value: 'Иордания' },
                    { key: 'iq', value: 'Ирак' },
                    { key: 'ir', value: 'Иран' },
                    { key: 'ie', value: 'Ирландия' },
                    { key: 'is', value: 'Исландия' },
                    { key: 'es', value: 'Испания' },
                    { key: 'it', value: 'Италия' },
                    { key: 'ye', value: 'Йемен' },
                    { key: 'cv', value: 'Кабо-Верде' },
                    { key: 'kz', value: 'Казахстан' },
                    { key: 'ky', value: 'Каймановы о-ва' },
                    { key: 'kh', value: 'Камбоджа' },
                    { key: 'cm', value: 'Камерун' },
                    { key: 'ca', value: 'Канада' },
                    {
                        key: 'cb',
                        value: 'Карибский бассейн (страны и территории)',
                    },
                    { key: 'qa', value: 'Катар' },
                    { key: 'ke', value: 'Кения' },
                    { key: 'cy', value: 'Кипр' },
                    { key: 'kg', value: 'Киргизия' },
                    { key: 'ki', value: 'Кирибати' },
                    { key: 'cn', value: 'Китай' },
                    { key: 'cc', value: 'Кокосовые о-ва (о-ва Килинг)' },
                    { key: 'co', value: 'Колумбия' },
                    { key: 'km', value: 'Коморские о-ва' },
                    { key: 'cg', value: 'Конго' },
                    { key: 'ko', value: 'Косово' },
                    { key: 'cr', value: 'Коста-Рика' },
                    { key: 'ci', value: "Кот-д'Ивуар" },
                    { key: 'cu', value: 'Куба' },
                    { key: 'kw', value: 'Кувейт' },
                    { key: 'la', value: 'Лаос' },
                    { key: 'lv', value: 'Латвия' },
                    { key: 'ls', value: 'Лесото' },
                    { key: 'lr', value: 'Либерия' },
                    { key: 'lb', value: 'Ливан' },
                    { key: 'ly', value: 'Ливия' },
                    { key: 'lt', value: 'Литва' },
                    { key: 'li', value: 'Лихтенштейн' },
                    { key: 'lu', value: 'Люксембург' },
                    { key: 'mu', value: 'Маврикий' },
                    { key: 'mr', value: 'Мавритания' },
                    { key: 'mg', value: 'Мадагаскар' },
                    { key: 'yt', value: 'Майотта' },
                    { key: 'mo', value: 'Макао' },
                    { key: 'mk', value: 'Северная Македония' },
                    { key: 'mw', value: 'Малави' },
                    { key: 'my', value: 'Малайзия' },
                    { key: 'ml', value: 'Мали' },
                    { key: 'mv', value: 'Мальдивские о-ва' },
                    { key: 'mt', value: 'Мальта' },
                    { key: 'ma', value: 'Марокко' },
                    { key: 'mq', value: 'Мартиника' },
                    { key: 'mh', value: 'Маршалловы о-ва' },
                    { key: 'mx', value: 'Мексика' },
                    { key: 'mz', value: 'Мозамбик' },
                    { key: 'md', value: 'Молдова' },
                    { key: 'mc', value: 'Монако' },
                    { key: 'mn', value: 'Монголия' },
                    { key: 'ms', value: 'Монтсеррат' },
                    { key: 'mm', value: 'Мьянма' },
                    { key: 'na', value: 'Намибия' },
                    { key: 'nr', value: 'Науру' },
                    { key: 'np', value: 'Непал' },
                    { key: 'ne', value: 'Нигер' },
                    { key: 'ng', value: 'Нигерия' },
                    { key: 'an', value: 'Нидерландские Антильские о-ва' },
                    { key: 'nl', value: 'Нидерланды' },
                    { key: 'ni', value: 'Никарагуа' },
                    { key: 'nu', value: 'Ниуэ' },
                    { key: 'nz', value: 'Новая Зеландия' },
                    { key: 'nc', value: 'Новая Каледония' },
                    { key: 'no', value: 'Норвегия' },
                    { key: 'nf', value: 'Норфолк (о-в)' },
                    { key: 'ae', value: 'ОАЭ' },
                    { key: 'om', value: 'Оман' },
                    { key: 'ck', value: 'Острова Кука' },
                    { key: 'im', value: 'Остров Мэн' },
                    { key: 'cx', value: 'Остров Рождества' },
                    { key: 'pk', value: 'Пакистан' },
                    { key: 'pw', value: 'Палау' },
                    { key: 'ps', value: 'Палестинская территория' },
                    { key: 'pa', value: 'Панама' },
                    { key: 'pg', value: 'Папуа-Новая Гвинея' },
                    { key: 'py', value: 'Парагвай' },
                    { key: 'pe', value: 'Перу' },
                    { key: 'pn', value: 'Питкэрн' },
                    { key: 'pl', value: 'Польша' },
                    { key: 'pt', value: 'Португалия' },
                    { key: 'pr', value: 'Пуэрто-Рико' },
                    { key: 're', value: 'Реюньон' },
                    { key: 'rw', value: 'Руанда' },
                    { key: 'ro', value: 'Румыния' },
                    { key: 'sv', value: 'Сальвадор' },
                    { key: 'ws', value: 'Самоа' },
                    { key: 'sm', value: 'Сан-Марино' },
                    { key: 'st', value: 'Сан-Томе и Принсипи' },
                    { key: 'sa', value: 'Саудовская Аравия' },
                    { key: 'sz', value: 'Свазиленд' },
                    { key: 'sj', value: 'Свальбард и Ян-Майен (о-ва)' },
                    { key: 'sh', value: 'Святая Елена (о-в)' },
                    { key: 'kp', value: 'Северная Корея' },
                    { key: 'mp', value: 'Северные Марианские о-ва' },
                    { key: 'sc', value: 'Сейшельские о-ва' },
                    { key: 'sn', value: 'Сенегал' },
                    { key: 'pm', value: 'Сен-Пьер и Микелон' },
                    { key: 'vc', value: 'Сент-Винсент и Гренадины' },
                    { key: 'kn', value: 'Сент-Китс и Невис' },
                    { key: 'lc', value: 'Сент-Люсия' },
                    { key: 'rs', value: 'Сербия' },
                    { key: 'sg', value: 'Сингапур' },
                    { key: 'sy', value: 'Сирия' },
                    { key: 'sk', value: 'Словацкая Республика' },
                    { key: 'si', value: 'Словения' },
                    { key: 'sb', value: 'Соломоновы о-ва' },
                    { key: 'so', value: 'Сомали' },
                    { key: 'sd', value: 'Судан' },
                    { key: 'sr', value: 'Суринам' },
                    { key: 'us', value: 'США' },
                    { key: 'sl', value: 'Сьерра-Леоне' },
                    { key: 'tj', value: 'Таджикистан' },
                    { key: 'th', value: 'Таиланд' },
                    { key: 'tw', value: 'Тайвань' },
                    { key: 'tz', value: 'Танзания' },
                    { key: 'tc', value: 'Теркс и Кайкос (о-ва)' },
                    { key: 'tg', value: 'Того' },
                    { key: 'tk', value: 'Токелау' },
                    { key: 'to', value: 'Тонга' },
                    { key: 'tt', value: 'Тринидад и Тобаго' },
                    { key: 'tv', value: 'Тувалу' },
                    { key: 'tn', value: 'Тунис' },
                    { key: 'tm', value: 'Туркменистан' },
                    { key: 'tr', value: 'Турция' },
                    { key: 'ug', value: 'Уганда' },
                    { key: 'uz', value: 'Узбекистан' },
                    { key: 'ua', value: 'Украина' },
                    { key: 'wf', value: 'Уоллис и Футуна' },
                    { key: 'uy', value: 'Уругвай' },
                    { key: 'fo', value: 'Фарерские о-ва' },
                    { key: 'fm', value: 'Федеративные Штаты Микронезии' },
                    { key: 'fj', value: 'Фиджи' },
                    { key: 'ph', value: 'Филиппины' },
                    { key: 'fi', value: 'Финляндия' },
                    {
                        key: 'fk',
                        value: 'Фолклендские о-ва (Мальвинские о-ва)',
                    },
                    { key: 'fr', value: 'Франция' },
                    { key: 'gf', value: 'Французская Гвиана' },
                    { key: 'pf', value: 'Французская Полинезия' },
                    { key: 'tf', value: 'Французские Южные Территории' },
                    { key: 'hr', value: 'Хорватия' },
                    { key: 'cf', value: 'Центральноафриканская Республика' },
                    { key: 'td', value: 'Чад' },
                    { key: 'me', value: 'Черногория' },
                    { key: 'cz', value: 'Чешская Республика' },
                    { key: 'cl', value: 'Чили' },
                    { key: 'ch', value: 'Швейцария' },
                    { key: 'se', value: 'Швеция' },
                    { key: 'lk', value: 'Шри-Ланка' },
                    { key: 'ec', value: 'Эквадор' },
                    { key: 'gq', value: 'Экваториальная Гвинея' },
                    { key: 'er', value: 'Эритрея' },
                    { key: 'ee', value: 'Эстония' },
                    { key: 'et', value: 'Эфиопия' },
                    { key: 'za', value: 'ЮАР' },
                    { key: 'kr', value: 'Южная Корея' },
                    { key: 'ss', value: 'Южный Судан' },
                    { key: 'jm', value: 'Ямайка' },
                    { key: 'jp', value: 'Япония' },
                    { key: 'oo', value: 'Другое' },
                    { key: 'us', value: 'United States' },
                    { key: 'af', value: 'Afghanistan' },
                    { key: 'ax', value: 'Aland Islands' },
                    { key: 'al', value: 'Albania' },
                    { key: 'dz', value: 'Algeria' },
                    { key: 'as', value: 'American Samoa' },
                    { key: 'ad', value: 'Andorra' },
                    { key: 'ao', value: 'Angola' },
                    { key: 'ai', value: 'Anguilla' },
                    { key: 'aq', value: 'Antarctica' },
                    { key: 'ag', value: 'Antigua and Barbuda' },
                    { key: 'ar', value: 'Argentina' },
                    { key: 'am', value: 'Armenia' },
                    { key: 'aw', value: 'Aruba' },
                    { key: 'au', value: 'Australia' },
                    { key: 'at', value: 'Austria' },
                    { key: 'az', value: 'Azerbaijan' },
                    { key: 'bs', value: 'Bahamas' },
                    { key: 'bh', value: 'Bahrain' },
                    { key: 'bd', value: 'Bangladesh' },
                    { key: 'bb', value: 'Barbados' },
                    { key: 'by', value: 'Belarus' },
                    { key: 'be', value: 'Belgium' },
                    { key: 'bz', value: 'Belize' },
                    { key: 'bj', value: 'Benin' },
                    { key: 'bm', value: 'Bermuda' },
                    { key: 'bt', value: 'Bhutan' },
                    { key: 'bo', value: 'Bolivia' },
                    { key: 'ba', value: 'Bosnia and Herzegovina' },
                    { key: 'bw', value: 'Botswana' },
                    { key: 'br', value: 'Brazil' },
                    { key: 'io', value: 'British Indian Ocean Territory' },
                    { key: 'bn', value: 'Brunei Darussalam' },
                    { key: 'bg', value: 'Bulgaria' },
                    { key: 'bf', value: 'Burkina Faso' },
                    { key: 'bi', value: 'Burundi' },
                    { key: 'kh', value: 'Cambodia' },
                    { key: 'cm', value: 'Cameroon' },
                    { key: 'ca', value: 'Canada' },
                    { key: 'cv', value: 'Cape Verde' },
                    { key: 'cb', value: 'Caribbean Nations' },
                    { key: 'ky', value: 'Cayman Islands' },
                    { key: 'cf', value: 'Central African Republic' },
                    { key: 'td', value: 'Chad' },
                    { key: 'cl', value: 'Chile' },
                    { key: 'cn', value: 'China' },
                    { key: 'cx', value: 'Christmas Island' },
                    { key: 'cc', value: 'Cocos (Keeling) Islands' },
                    { key: 'co', value: 'Colombia' },
                    { key: 'km', value: 'Comoros' },
                    { key: 'cg', value: 'Congo' },
                    { key: 'ck', value: 'Cook Islands' },
                    { key: 'cr', value: 'Costa Rica' },
                    { key: 'ci', value: 'Cote D"Ivoire (Ivory Coast)' },
                    { key: 'hr', value: 'Croatia' },
                    { key: 'cu', value: 'Cuba' },
                    { key: 'cy', value: 'Cyprus' },
                    { key: 'cz', value: 'Czech Republic' },
                    { key: 'cd', value: 'Democratic Republic of the Congo' },
                    { key: 'dk', value: 'Denmark' },
                    { key: 'dj', value: 'Djibouti' },
                    { key: 'dm', value: 'Dominica' },
                    { key: 'do', value: 'Dominican Republic' },
                    { key: 'tp', value: 'East Timor' },
                    { key: 'ec', value: 'Ecuador' },
                    { key: 'eg', value: 'Egypt' },
                    { key: 'sv', value: 'El Salvador' },
                    { key: 'gq', value: 'Equatorial Guinea' },
                    { key: 'er', value: 'Eritrea' },
                    { key: 'ee', value: 'Estonia' },
                    { key: 'et', value: 'Ethiopia' },
                    { key: 'fk', value: 'Falkland Islands (Malvinas)' },
                    { key: 'fo', value: 'Faroe Islands' },
                    { key: 'fm', value: 'Federated States of Micronesia' },
                    { key: 'fj', value: 'Fiji' },
                    { key: 'fi', value: 'Finland' },
                    { key: 'fr', value: 'France' },
                    { key: 'gf', value: 'French Guiana' },
                    { key: 'pf', value: 'French Polynesia' },
                    { key: 'tf', value: 'French Southern Territories' },
                    { key: 'ga', value: 'Gabon' },
                    { key: 'gm', value: 'Gambia' },
                    { key: 'ge', value: 'Georgia' },
                    { key: 'de', value: 'Germany' },
                    { key: 'gh', value: 'Ghana' },
                    { key: 'gi', value: 'Gibraltar' },
                    { key: 'gr', value: 'Greece' },
                    { key: 'gl', value: 'Greenland' },
                    { key: 'gd', value: 'Grenada' },
                    { key: 'gp', value: 'Guadeloupe' },
                    { key: 'gu', value: 'Guam' },
                    { key: 'gt', value: 'Guatemala' },
                    { key: 'gg', value: 'Guernsey' },
                    { key: 'gn', value: 'Guinea' },
                    { key: 'gw', value: 'Guinea-Bissau' },
                    { key: 'gy', value: 'Guyana' },
                    { key: 'ht', value: 'Haiti' },
                    { key: 'hn', value: 'Honduras' },
                    { key: 'hk', value: 'Hong Kong' },
                    { key: 'hu', value: 'Hungary' },
                    { key: 'is', value: 'Iceland' },
                    { key: 'in', value: 'India' },
                    { key: 'id', value: 'Indonesia' },
                    { key: 'ir', value: 'Iran' },
                    { key: 'iq', value: 'Iraq' },
                    { key: 'ie', value: 'Ireland' },
                    { key: 'im', value: 'Isle of Man' },
                    { key: 'il', value: 'Israel' },
                    { key: 'it', value: 'Italy' },
                    { key: 'jm', value: 'Jamaica' },
                    { key: 'jp', value: 'Japan' },
                    { key: 'je', value: 'Jersey' },
                    { key: 'jo', value: 'Jordan' },
                    { key: 'kz', value: 'Kazakhstan' },
                    { key: 'ke', value: 'Kenya' },
                    { key: 'ki', value: 'Kiribati' },
                    { key: 'kr', value: 'Korea' },
                    { key: 'kp', value: 'Korea (North)' },
                    { key: 'ko', value: 'Kosovo' },
                    { key: 'kw', value: 'Kuwait' },
                    { key: 'kg', value: 'Kyrgyzstan' },
                    { key: 'la', value: 'Laos' },
                    { key: 'lv', value: 'Latvia' },
                    { key: 'lb', value: 'Lebanon' },
                    { key: 'ls', value: 'Lesotho' },
                    { key: 'lr', value: 'Liberia' },
                    { key: 'ly', value: 'Libya' },
                    { key: 'li', value: 'Liechtenstein' },
                    { key: 'lt', value: 'Lithuania' },
                    { key: 'lu', value: 'Luxembourg' },
                    { key: 'mo', value: 'Macao' },
                    { key: 'mk', value: 'Macedonia' },
                    { key: 'mg', value: 'Madagascar' },
                    { key: 'mw', value: 'Malawi' },
                    { key: 'my', value: 'Malaysia' },
                    { key: 'mv', value: 'Maldives' },
                    { key: 'ml', value: 'Mali' },
                    { key: 'mt', value: 'Malta' },
                    { key: 'mh', value: 'Marshall Islands' },
                    { key: 'mq', value: 'Martinique' },
                    { key: 'mr', value: 'Mauritania' },
                    { key: 'mu', value: 'Mauritius' },
                    { key: 'yt', value: 'Mayotte' },
                    { key: 'mx', value: 'Mexico' },
                    { key: 'md', value: 'Moldova' },
                    { key: 'mc', value: 'Monaco' },
                    { key: 'mn', value: 'Mongolia' },
                    { key: 'me', value: 'Montenegro' },
                    { key: 'ms', value: 'Montserrat' },
                    { key: 'ma', value: 'Morocco' },
                    { key: 'mz', value: 'Mozambique' },
                    { key: 'mm', value: 'Myanmar' },
                    { key: 'na', value: 'Namibia' },
                    { key: 'nr', value: 'Nauru' },
                    { key: 'np', value: 'Nepal' },
                    { key: 'nl', value: 'Netherlands' },
                    { key: 'an', value: 'Netherlands Antilles' },
                    { key: 'nc', value: 'New Caledonia' },
                    { key: 'nz', value: 'New Zealand' },
                    { key: 'ni', value: 'Nicaragua' },
                    { key: 'ne', value: 'Niger' },
                    { key: 'ng', value: 'Nigeria' },
                    { key: 'nu', value: 'Niue' },
                    { key: 'nf', value: 'Norfolk Island' },
                    { key: 'mp', value: 'Northern Mariana Islands' },
                    { key: 'no', value: 'Norway' },
                    { key: 'pk', value: 'Pakistan' },
                    { key: 'pw', value: 'Palau' },
                    { key: 'ps', value: 'Palestinian Territory' },
                    { key: 'pa', value: 'Panama' },
                    { key: 'pg', value: 'Papua New Guinea' },
                    { key: 'py', value: 'Paraguay' },
                    { key: 'pe', value: 'Peru' },
                    { key: 'ph', value: 'Philippines' },
                    { key: 'pn', value: 'Pitcairn' },
                    { key: 'pl', value: 'Poland' },
                    { key: 'pt', value: 'Portugal' },
                    { key: 'pr', value: 'Puerto Rico' },
                    { key: 'qa', value: 'Qatar' },
                    { key: 're', value: 'Reunion' },
                    { key: 'ro', value: 'Romania' },
                    { key: 'ru', value: 'Russian Federation' },
                    { key: 'ru', value: 'Russia' },
                    { key: 'rw', value: 'Rwanda' },
                    { key: 'sh', value: 'Saint Helena' },
                    { key: 'kn', value: 'Saint Kitts and Nevis' },
                    { key: 'lc', value: 'Saint Lucia' },
                    { key: 'pm', value: 'Saint Pierre and Miquelon' },
                    { key: 'vc', value: 'Saint Vincent and the Grenadines' },
                    { key: 'ws', value: 'Samoa' },
                    { key: 'sm', value: 'San Marino' },
                    { key: 'st', value: 'Sao Tome and Principe' },
                    { key: 'sa', value: 'Saudi Arabia' },
                    { key: 'sn', value: 'Senegal' },
                    { key: 'rs', value: 'Serbia' },
                    { key: 'sc', value: 'Seychelles' },
                    { key: 'sl', value: 'Sierra Leone' },
                    { key: 'sg', value: 'Singapore' },
                    { key: 'sk', value: 'Slovak Republic' },
                    { key: 'si', value: 'Slovenia' },
                    { key: 'sb', value: 'Solomon Islands' },
                    { key: 'so', value: 'Somalia' },
                    { key: 'za', value: 'South Africa' },
                    { key: 'ss', value: 'South Sudan' },
                    { key: 'es', value: 'Spain' },
                    { key: 'lk', value: 'Sri Lanka' },
                    { key: 'sd', value: 'Sudan' },
                    { key: 'om', value: 'Sultanate of Oman' },
                    { key: 'sr', value: 'Suriname' },
                    { key: 'sj', value: 'Svalbard and Jan Mayen' },
                    { key: 'sz', value: 'Swaziland' },
                    { key: 'se', value: 'Sweden' },
                    { key: 'ch', value: 'Switzerland' },
                    { key: 'sy', value: 'Syria' },
                    { key: 'tw', value: 'Taiwan' },
                    { key: 'tj', value: 'Tajikistan' },
                    { key: 'tz', value: 'Tanzania' },
                    { key: 'th', value: 'Thailand' },
                    { key: 'tl', value: 'Timor-Leste' },
                    { key: 'tg', value: 'Togo' },
                    { key: 'tk', value: 'Tokelau' },
                    { key: 'to', value: 'Tonga' },
                    { key: 'tt', value: 'Trinidad and Tobago' },
                    { key: 'tn', value: 'Tunisia' },
                    { key: 'tr', value: 'Turkey' },
                    { key: 'tm', value: 'Turkmenistan' },
                    { key: 'tc', value: 'Turks and Caicos Islands' },
                    { key: 'tv', value: 'Tuvalu' },
                    { key: 'ug', value: 'Uganda' },
                    { key: 'ua', value: 'Ukraine' },
                    { key: 'ae', value: 'United Arab Emirates' },
                    { key: 'gb', value: 'United Kingdom' },
                    { key: 'uy', value: 'Uruguay' },
                    { key: 'uz', value: 'Uzbekistan' },
                    { key: 'vu', value: 'Vanuatu' },
                    { key: 'va', value: 'Vatican City State (Holy See)' },
                    { key: 've', value: 'Venezuela' },
                    { key: 'vn', value: 'Vietnam' },
                    { key: 'vg', value: 'Virgin Islands (British)' },
                    { key: 'vi', value: 'Virgin Islands (U.S.)' },
                    { key: 'wf', value: 'Wallis and Futuna' },
                    { key: 'eh', value: 'Western Sahara' },
                    { key: 'ye', value: 'Yemen' },
                    { key: 'zm', value: 'Zambia' },
                    { key: 'zw', value: 'Zimbabwe' },
                    { key: 'oo', value: 'Other' },
                ];
            };

            service.getAllCounties = function (lang) {
                const countries = {
                    en: {
                        AF: 'Afghanistan',
                        AX: '\u00c5land Islands',
                        AL: 'Albania',
                        DZ: 'Algeria',
                        AS: 'American Samoa',
                        AD: 'Andorra',
                        AO: 'Angola',
                        AI: 'Anguilla',
                        AQ: 'Antarctica',
                        AG: 'Antigua & Barbuda',
                        AR: 'Argentina',
                        AM: 'Armenia',
                        AW: 'Aruba',
                        AC: 'Ascension Island',
                        AU: 'Australia',
                        AT: 'Austria',
                        AZ: 'Azerbaijan',
                        BS: 'Bahamas',
                        BH: 'Bahrain',
                        BD: 'Bangladesh',
                        BB: 'Barbados',
                        BY: 'Belarus',
                        BE: 'Belgium',
                        BZ: 'Belize',
                        BJ: 'Benin',
                        BM: 'Bermuda',
                        BT: 'Bhutan',
                        BO: 'Bolivia',
                        BA: 'Bosnia & Herzegovina',
                        BW: 'Botswana',
                        BR: 'Brazil',
                        IO: 'British Indian Ocean Territory',
                        VG: 'British Virgin Islands',
                        BN: 'Brunei',
                        BG: 'Bulgaria',
                        BF: 'Burkina Faso',
                        BI: 'Burundi',
                        KH: 'Cambodia',
                        CM: 'Cameroon',
                        CA: 'Canada',
                        IC: 'Canary Islands',
                        CV: 'Cape Verde',
                        BQ: 'Caribbean Netherlands',
                        KY: 'Cayman Islands',
                        CF: 'Central African Republic',
                        EA: 'Ceuta & Melilla',
                        TD: 'Chad',
                        CL: 'Chile',
                        CN: 'China',
                        CX: 'Christmas Island',
                        CC: 'Cocos (Keeling) Islands',
                        CO: 'Colombia',
                        KM: 'Comoros',
                        CG: 'Congo - Brazzaville',
                        CD: 'Congo - Kinshasa',
                        CK: 'Cook Islands',
                        CR: 'Costa Rica',
                        CI: 'C\u00f4te d\u2019Ivoire',
                        HR: 'Croatia',
                        CU: 'Cuba',
                        CW: 'Cura\u00e7ao',
                        CY: 'Cyprus',
                        CZ: 'Czechia',
                        DK: 'Denmark',
                        DG: 'Diego Garcia',
                        DJ: 'Djibouti',
                        DM: 'Dominica',
                        DO: 'Dominican Republic',
                        EC: 'Ecuador',
                        EG: 'Egypt',
                        SV: 'El Salvador',
                        GQ: 'Equatorial Guinea',
                        ER: 'Eritrea',
                        EE: 'Estonia',
                        ET: 'Ethiopia',
                        FK: 'Falkland Islands',
                        FO: 'Faroe Islands',
                        FJ: 'Fiji',
                        FI: 'Finland',
                        FR: 'France',
                        GF: 'French Guiana',
                        PF: 'French Polynesia',
                        TF: 'French Southern Territories',
                        GA: 'Gabon',
                        GM: 'Gambia',
                        GE: 'Georgia',
                        DE: 'Germany',
                        GH: 'Ghana',
                        GI: 'Gibraltar',
                        GR: 'Greece',
                        GL: 'Greenland',
                        GD: 'Grenada',
                        GP: 'Guadeloupe',
                        GU: 'Guam',
                        GT: 'Guatemala',
                        GG: 'Guernsey',
                        GN: 'Guinea',
                        GW: 'Guinea-Bissau',
                        GY: 'Guyana',
                        HT: 'Haiti',
                        HN: 'Honduras',
                        HK: 'Hong Kong SAR China',
                        HU: 'Hungary',
                        IS: 'Iceland',
                        IN: 'India',
                        ID: 'Indonesia',
                        IR: 'Iran',
                        IQ: 'Iraq',
                        IE: 'Ireland',
                        IM: 'Isle of Man',
                        IL: 'Israel',
                        IT: 'Italy',
                        JM: 'Jamaica',
                        JP: 'Japan',
                        JE: 'Jersey',
                        JO: 'Jordan',
                        KZ: 'Kazakhstan',
                        KE: 'Kenya',
                        KI: 'Kiribati',
                        XK: 'Kosovo',
                        KW: 'Kuwait',
                        KG: 'Kyrgyzstan',
                        LA: 'Laos',
                        LV: 'Latvia',
                        LB: 'Lebanon',
                        LS: 'Lesotho',
                        LR: 'Liberia',
                        LY: 'Libya',
                        LI: 'Liechtenstein',
                        LT: 'Lithuania',
                        LU: 'Luxembourg',
                        MO: 'Macau SAR China',
                        MK: 'Macedonia',
                        MG: 'Madagascar',
                        MW: 'Malawi',
                        MY: 'Malaysia',
                        MV: 'Maldives',
                        ML: 'Mali',
                        MT: 'Malta',
                        MH: 'Marshall Islands',
                        MQ: 'Martinique',
                        MR: 'Mauritania',
                        MU: 'Mauritius',
                        YT: 'Mayotte',
                        MX: 'Mexico',
                        FM: 'Micronesia',
                        MD: 'Moldova',
                        MC: 'Monaco',
                        MN: 'Mongolia',
                        ME: 'Montenegro',
                        MS: 'Montserrat',
                        MA: 'Morocco',
                        MZ: 'Mozambique',
                        MM: 'Myanmar (Burma)',
                        NA: 'Namibia',
                        NR: 'Nauru',
                        NP: 'Nepal',
                        NL: 'Netherlands',
                        NC: 'New Caledonia',
                        NZ: 'New Zealand',
                        NI: 'Nicaragua',
                        NE: 'Niger',
                        NG: 'Nigeria',
                        NU: 'Niue',
                        NF: 'Norfolk Island',
                        KP: 'North Korea',
                        MP: 'Northern Mariana Islands',
                        NO: 'Norway',
                        OM: 'Oman',
                        PK: 'Pakistan',
                        PW: 'Palau',
                        PS: 'Palestinian Territories',
                        PA: 'Panama',
                        PG: 'Papua New Guinea',
                        PY: 'Paraguay',
                        PE: 'Peru',
                        PH: 'Philippines',
                        PN: 'Pitcairn Islands',
                        PL: 'Poland',
                        PT: 'Portugal',
                        PR: 'Puerto Rico',
                        QA: 'Qatar',
                        RE: 'R\u00e9union',
                        RO: 'Romania',
                        RU: 'Russia',
                        RW: 'Rwanda',
                        WS: 'Samoa',
                        SM: 'San Marino',
                        ST: 'S\u00e3o Tom\u00e9 & Pr\u00edncipe',
                        SA: 'Saudi Arabia',
                        SN: 'Senegal',
                        RS: 'Serbia',
                        SC: 'Seychelles',
                        SL: 'Sierra Leone',
                        SG: 'Singapore',
                        SX: 'Sint Maarten',
                        SK: 'Slovakia',
                        SI: 'Slovenia',
                        SB: 'Solomon Islands',
                        SO: 'Somalia',
                        ZA: 'South Africa',
                        GS: 'South Georgia & South Sandwich Islands',
                        KR: 'South Korea',
                        SS: 'South Sudan',
                        ES: 'Spain',
                        LK: 'Sri Lanka',
                        BL: 'St. Barth\u00e9lemy',
                        SH: 'St. Helena',
                        KN: 'St. Kitts & Nevis',
                        LC: 'St. Lucia',
                        MF: 'St. Martin',
                        PM: 'St. Pierre & Miquelon',
                        VC: 'St. Vincent & Grenadines',
                        SD: 'Sudan',
                        SR: 'Suriname',
                        SJ: 'Svalbard & Jan Mayen',
                        SZ: 'Swaziland',
                        SE: 'Sweden',
                        CH: 'Switzerland',
                        SY: 'Syria',
                        TW: 'Taiwan',
                        TJ: 'Tajikistan',
                        TZ: 'Tanzania',
                        TH: 'Thailand',
                        TL: 'Timor-Leste',
                        TG: 'Togo',
                        TK: 'Tokelau',
                        TO: 'Tonga',
                        TT: 'Trinidad & Tobago',
                        TA: 'Tristan da Cunha',
                        TN: 'Tunisia',
                        TR: 'Turkey',
                        TM: 'Turkmenistan',
                        TC: 'Turks & Caicos Islands',
                        TV: 'Tuvalu',
                        UM: 'U.S. Outlying Islands',
                        VI: 'U.S. Virgin Islands',
                        UG: 'Uganda',
                        UA: 'Ukraine',
                        AE: 'United Arab Emirates',
                        GB: 'United Kingdom',
                        US: 'United States',
                        UY: 'Uruguay',
                        UZ: 'Uzbekistan',
                        VU: 'Vanuatu',
                        VA: 'Vatican City',
                        VE: 'Venezuela',
                        VN: 'Vietnam',
                        WF: 'Wallis & Futuna',
                        EH: 'Western Sahara',
                        YE: 'Yemen',
                        ZM: 'Zambia',
                        ZW: 'Zimbabwe',
                    },
                    ru: {
                        AU: '\u0410\u0432\u0441\u0442\u0440\u0430\u043b\u0438\u044f',
                        AT: '\u0410\u0432\u0441\u0442\u0440\u0438\u044f',
                        AZ: '\u0410\u0437\u0435\u0440\u0431\u0430\u0439\u0434\u0436\u0430\u043d',
                        AX: '\u0410\u043b\u0430\u043d\u0434\u0441\u043a\u0438\u0435 \u043e-\u0432\u0430',
                        AL: '\u0410\u043b\u0431\u0430\u043d\u0438\u044f',
                        DZ: '\u0410\u043b\u0436\u0438\u0440',
                        AS: '\u0410\u043c\u0435\u0440\u0438\u043a\u0430\u043d\u0441\u043a\u043e\u0435 \u0421\u0430\u043c\u043e\u0430',
                        AI: '\u0410\u043d\u0433\u0438\u043b\u044c\u044f',
                        AO: '\u0410\u043d\u0433\u043e\u043b\u0430',
                        AD: '\u0410\u043d\u0434\u043e\u0440\u0440\u0430',
                        AQ: '\u0410\u043d\u0442\u0430\u0440\u043a\u0442\u0438\u0434\u0430',
                        AG: '\u0410\u043d\u0442\u0438\u0433\u0443\u0430 \u0438 \u0411\u0430\u0440\u0431\u0443\u0434\u0430',
                        AR: '\u0410\u0440\u0433\u0435\u043d\u0442\u0438\u043d\u0430',
                        AM: '\u0410\u0440\u043c\u0435\u043d\u0438\u044f',
                        AW: '\u0410\u0440\u0443\u0431\u0430',
                        AF: '\u0410\u0444\u0433\u0430\u043d\u0438\u0441\u0442\u0430\u043d',
                        BS: '\u0411\u0430\u0433\u0430\u043c\u044b',
                        BD: '\u0411\u0430\u043d\u0433\u043b\u0430\u0434\u0435\u0448',
                        BB: '\u0411\u0430\u0440\u0431\u0430\u0434\u043e\u0441',
                        BH: '\u0411\u0430\u0445\u0440\u0435\u0439\u043d',
                        BY: '\u0411\u0435\u043b\u0430\u0440\u0443\u0441\u044c',
                        BZ: '\u0411\u0435\u043b\u0438\u0437',
                        BE: '\u0411\u0435\u043b\u044c\u0433\u0438\u044f',
                        BJ: '\u0411\u0435\u043d\u0438\u043d',
                        BM: '\u0411\u0435\u0440\u043c\u0443\u0434\u044b',
                        BG: '\u0411\u043e\u043b\u0433\u0430\u0440\u0438\u044f',
                        BO: '\u0411\u043e\u043b\u0438\u0432\u0438\u044f',
                        BQ: '\u0411\u043e\u043d\u044d\u0439\u0440, \u0421\u0438\u043d\u0442-\u042d\u0441\u0442\u0430\u0442\u0438\u0443\u0441 \u0438 \u0421\u0430\u0431\u0430',
                        BA: '\u0411\u043e\u0441\u043d\u0438\u044f \u0438 \u0413\u0435\u0440\u0446\u0435\u0433\u043e\u0432\u0438\u043d\u0430',
                        BW: '\u0411\u043e\u0442\u0441\u0432\u0430\u043d\u0430',
                        BR: '\u0411\u0440\u0430\u0437\u0438\u043b\u0438\u044f',
                        IO: '\u0411\u0440\u0438\u0442\u0430\u043d\u0441\u043a\u0430\u044f \u0442\u0435\u0440\u0440\u0438\u0442\u043e\u0440\u0438\u044f \u0432 \u0418\u043d\u0434\u0438\u0439\u0441\u043a\u043e\u043c \u043e\u043a\u0435\u0430\u043d\u0435',
                        BN: '\u0411\u0440\u0443\u043d\u0435\u0439-\u0414\u0430\u0440\u0443\u0441\u0441\u0430\u043b\u0430\u043c',
                        BF: '\u0411\u0443\u0440\u043a\u0438\u043d\u0430-\u0424\u0430\u0441\u043e',
                        BI: '\u0411\u0443\u0440\u0443\u043d\u0434\u0438',
                        BT: '\u0411\u0443\u0442\u0430\u043d',
                        VU: '\u0412\u0430\u043d\u0443\u0430\u0442\u0443',
                        VA: '\u0412\u0430\u0442\u0438\u043a\u0430\u043d',
                        GB: '\u0412\u0435\u043b\u0438\u043a\u043e\u0431\u0440\u0438\u0442\u0430\u043d\u0438\u044f',
                        HU: '\u0412\u0435\u043d\u0433\u0440\u0438\u044f',
                        VE: '\u0412\u0435\u043d\u0435\u0441\u0443\u044d\u043b\u0430',
                        VG: '\u0412\u0438\u0440\u0433\u0438\u043d\u0441\u043a\u0438\u0435 \u043e-\u0432\u0430 (\u0411\u0440\u0438\u0442\u0430\u043d\u0441\u043a\u0438\u0435)',
                        VI: '\u0412\u0438\u0440\u0433\u0438\u043d\u0441\u043a\u0438\u0435 \u043e-\u0432\u0430 (\u0421\u0428\u0410)',
                        UM: '\u0412\u043d\u0435\u0448\u043d\u0438\u0435 \u043c\u0430\u043b\u044b\u0435 \u043e-\u0432\u0430 (\u0421\u0428\u0410)',
                        TL: '\u0412\u043e\u0441\u0442\u043e\u0447\u043d\u044b\u0439 \u0422\u0438\u043c\u043e\u0440',
                        VN: '\u0412\u044c\u0435\u0442\u043d\u0430\u043c',
                        GA: '\u0413\u0430\u0431\u043e\u043d',
                        HT: '\u0413\u0430\u0438\u0442\u0438',
                        GY: '\u0413\u0430\u0439\u0430\u043d\u0430',
                        GM: '\u0413\u0430\u043c\u0431\u0438\u044f',
                        GH: '\u0413\u0430\u043d\u0430',
                        GP: '\u0413\u0432\u0430\u0434\u0435\u043b\u0443\u043f\u0430',
                        GT: '\u0413\u0432\u0430\u0442\u0435\u043c\u0430\u043b\u0430',
                        GN: '\u0413\u0432\u0438\u043d\u0435\u044f',
                        GW: '\u0413\u0432\u0438\u043d\u0435\u044f-\u0411\u0438\u0441\u0430\u0443',
                        DE: '\u0413\u0435\u0440\u043c\u0430\u043d\u0438\u044f',
                        GG: '\u0413\u0435\u0440\u043d\u0441\u0438',
                        GI: '\u0413\u0438\u0431\u0440\u0430\u043b\u0442\u0430\u0440',
                        HN: '\u0413\u043e\u043d\u0434\u0443\u0440\u0430\u0441',
                        HK: '\u0413\u043e\u043d\u043a\u043e\u043d\u0433 (\u0441\u043f\u0435\u0446\u0438\u0430\u043b\u044c\u043d\u044b\u0439 \u0430\u0434\u043c\u0438\u043d\u0438\u0441\u0442\u0440\u0430\u0442\u0438\u0432\u043d\u044b\u0439 \u0440\u0430\u0439\u043e\u043d)',
                        GD: '\u0413\u0440\u0435\u043d\u0430\u0434\u0430',
                        GL: '\u0413\u0440\u0435\u043d\u043b\u0430\u043d\u0434\u0438\u044f',
                        GR: '\u0413\u0440\u0435\u0446\u0438\u044f',
                        GE: '\u0413\u0440\u0443\u0437\u0438\u044f',
                        GU: '\u0413\u0443\u0430\u043c',
                        DK: '\u0414\u0430\u043d\u0438\u044f',
                        JE: '\u0414\u0436\u0435\u0440\u0441\u0438',
                        DJ: '\u0414\u0436\u0438\u0431\u0443\u0442\u0438',
                        DG: '\u0414\u0438\u0435\u0433\u043e-\u0413\u0430\u0440\u0441\u0438\u044f',
                        DM: '\u0414\u043e\u043c\u0438\u043d\u0438\u043a\u0430',
                        DO: '\u0414\u043e\u043c\u0438\u043d\u0438\u043a\u0430\u043d\u0441\u043a\u0430\u044f \u0420\u0435\u0441\u043f\u0443\u0431\u043b\u0438\u043a\u0430',
                        EG: '\u0415\u0433\u0438\u043f\u0435\u0442',
                        ZM: '\u0417\u0430\u043c\u0431\u0438\u044f',
                        EH: '\u0417\u0430\u043f\u0430\u0434\u043d\u0430\u044f \u0421\u0430\u0445\u0430\u0440\u0430',
                        ZW: '\u0417\u0438\u043c\u0431\u0430\u0431\u0432\u0435',
                        IL: '\u0418\u0437\u0440\u0430\u0438\u043b\u044c',
                        IN: '\u0418\u043d\u0434\u0438\u044f',
                        ID: '\u0418\u043d\u0434\u043e\u043d\u0435\u0437\u0438\u044f',
                        JO: '\u0418\u043e\u0440\u0434\u0430\u043d\u0438\u044f',
                        IQ: '\u0418\u0440\u0430\u043a',
                        IR: '\u0418\u0440\u0430\u043d',
                        IE: '\u0418\u0440\u043b\u0430\u043d\u0434\u0438\u044f',
                        IS: '\u0418\u0441\u043b\u0430\u043d\u0434\u0438\u044f',
                        ES: '\u0418\u0441\u043f\u0430\u043d\u0438\u044f',
                        IT: '\u0418\u0442\u0430\u043b\u0438\u044f',
                        YE: '\u0419\u0435\u043c\u0435\u043d',
                        CV: '\u041a\u0430\u0431\u043e-\u0412\u0435\u0440\u0434\u0435',
                        KZ: '\u041a\u0430\u0437\u0430\u0445\u0441\u0442\u0430\u043d',
                        KY: '\u041a\u0430\u0439\u043c\u0430\u043d\u043e\u0432\u044b \u043e-\u0432\u0430',
                        KH: '\u041a\u0430\u043c\u0431\u043e\u0434\u0436\u0430',
                        CM: '\u041a\u0430\u043c\u0435\u0440\u0443\u043d',
                        CA: '\u041a\u0430\u043d\u0430\u0434\u0430',
                        IC: '\u041a\u0430\u043d\u0430\u0440\u0441\u043a\u0438\u0435 \u043e-\u0432\u0430',
                        QA: '\u041a\u0430\u0442\u0430\u0440',
                        KE: '\u041a\u0435\u043d\u0438\u044f',
                        CY: '\u041a\u0438\u043f\u0440',
                        KG: '\u041a\u0438\u0440\u0433\u0438\u0437\u0438\u044f',
                        KI: '\u041a\u0438\u0440\u0438\u0431\u0430\u0442\u0438',
                        CN: '\u041a\u0438\u0442\u0430\u0439',
                        KP: '\u041a\u041d\u0414\u0420',
                        CC: '\u041a\u043e\u043a\u043e\u0441\u043e\u0432\u044b\u0435 \u043e-\u0432\u0430',
                        CO: '\u041a\u043e\u043b\u0443\u043c\u0431\u0438\u044f',
                        KM: '\u041a\u043e\u043c\u043e\u0440\u044b',
                        CG: '\u041a\u043e\u043d\u0433\u043e - \u0411\u0440\u0430\u0437\u0437\u0430\u0432\u0438\u043b\u044c',
                        CD: '\u041a\u043e\u043d\u0433\u043e - \u041a\u0438\u043d\u0448\u0430\u0441\u0430',
                        XK: '\u041a\u043e\u0441\u043e\u0432\u043e',
                        CR: '\u041a\u043e\u0441\u0442\u0430-\u0420\u0438\u043a\u0430',
                        CI: '\u041a\u043e\u0442-\u0434\u2019\u0418\u0432\u0443\u0430\u0440',
                        CU: '\u041a\u0443\u0431\u0430',
                        KW: '\u041a\u0443\u0432\u0435\u0439\u0442',
                        CW: '\u041a\u044e\u0440\u0430\u0441\u0430\u043e',
                        LA: '\u041b\u0430\u043e\u0441',
                        LV: '\u041b\u0430\u0442\u0432\u0438\u044f',
                        LS: '\u041b\u0435\u0441\u043e\u0442\u043e',
                        LR: '\u041b\u0438\u0431\u0435\u0440\u0438\u044f',
                        LB: '\u041b\u0438\u0432\u0430\u043d',
                        LY: '\u041b\u0438\u0432\u0438\u044f',
                        LT: '\u041b\u0438\u0442\u0432\u0430',
                        LI: '\u041b\u0438\u0445\u0442\u0435\u043d\u0448\u0442\u0435\u0439\u043d',
                        LU: '\u041b\u044e\u043a\u0441\u0435\u043c\u0431\u0443\u0440\u0433',
                        MU: '\u041c\u0430\u0432\u0440\u0438\u043a\u0438\u0439',
                        MR: '\u041c\u0430\u0432\u0440\u0438\u0442\u0430\u043d\u0438\u044f',
                        MG: '\u041c\u0430\u0434\u0430\u0433\u0430\u0441\u043a\u0430\u0440',
                        YT: '\u041c\u0430\u0439\u043e\u0442\u0442\u0430',
                        MO: '\u041c\u0430\u043a\u0430\u043e (\u0441\u043f\u0435\u0446\u0438\u0430\u043b\u044c\u043d\u044b\u0439 \u0430\u0434\u043c\u0438\u043d\u0438\u0441\u0442\u0440\u0430\u0442\u0438\u0432\u043d\u044b\u0439 \u0440\u0430\u0439\u043e\u043d)',
                        MK: '\u041c\u0430\u043a\u0435\u0434\u043e\u043d\u0438\u044f',
                        MW: '\u041c\u0430\u043b\u0430\u0432\u0438',
                        MY: '\u041c\u0430\u043b\u0430\u0439\u0437\u0438\u044f',
                        ML: '\u041c\u0430\u043b\u0438',
                        MV: '\u041c\u0430\u043b\u044c\u0434\u0438\u0432\u044b',
                        MT: '\u041c\u0430\u043b\u044c\u0442\u0430',
                        MA: '\u041c\u0430\u0440\u043e\u043a\u043a\u043e',
                        MQ: '\u041c\u0430\u0440\u0442\u0438\u043d\u0438\u043a\u0430',
                        MH: '\u041c\u0430\u0440\u0448\u0430\u043b\u043b\u043e\u0432\u044b \u041e\u0441\u0442\u0440\u043e\u0432\u0430',
                        MX: '\u041c\u0435\u043a\u0441\u0438\u043a\u0430',
                        MZ: '\u041c\u043e\u0437\u0430\u043c\u0431\u0438\u043a',
                        MD: '\u041c\u043e\u043b\u0434\u043e\u0432\u0430',
                        MC: '\u041c\u043e\u043d\u0430\u043a\u043e',
                        MN: '\u041c\u043e\u043d\u0433\u043e\u043b\u0438\u044f',
                        MS: '\u041c\u043e\u043d\u0442\u0441\u0435\u0440\u0440\u0430\u0442',
                        MM: '\u041c\u044c\u044f\u043d\u043c\u0430 (\u0411\u0438\u0440\u043c\u0430)',
                        NA: '\u041d\u0430\u043c\u0438\u0431\u0438\u044f',
                        NR: '\u041d\u0430\u0443\u0440\u0443',
                        NP: '\u041d\u0435\u043f\u0430\u043b',
                        NE: '\u041d\u0438\u0433\u0435\u0440',
                        NG: '\u041d\u0438\u0433\u0435\u0440\u0438\u044f',
                        NL: '\u041d\u0438\u0434\u0435\u0440\u043b\u0430\u043d\u0434\u044b',
                        NI: '\u041d\u0438\u043a\u0430\u0440\u0430\u0433\u0443\u0430',
                        NU: '\u041d\u0438\u0443\u044d',
                        NZ: '\u041d\u043e\u0432\u0430\u044f \u0417\u0435\u043b\u0430\u043d\u0434\u0438\u044f',
                        NC: '\u041d\u043e\u0432\u0430\u044f \u041a\u0430\u043b\u0435\u0434\u043e\u043d\u0438\u044f',
                        NO: '\u041d\u043e\u0440\u0432\u0435\u0433\u0438\u044f',
                        AC: '\u043e-\u0432 \u0412\u043e\u0437\u043d\u0435\u0441\u0435\u043d\u0438\u044f',
                        IM: '\u043e-\u0432 \u041c\u044d\u043d',
                        NF: '\u043e-\u0432 \u041d\u043e\u0440\u0444\u043e\u043b\u043a',
                        CX: '\u043e-\u0432 \u0420\u043e\u0436\u0434\u0435\u0441\u0442\u0432\u0430',
                        SH: '\u043e-\u0432 \u0421\u0432. \u0415\u043b\u0435\u043d\u044b',
                        TC: '\u043e-\u0432\u0430 \u0422\u0451\u0440\u043a\u0441 \u0438 \u041a\u0430\u0439\u043a\u043e\u0441',
                        AE: '\u041e\u0410\u042d',
                        OM: '\u041e\u043c\u0430\u043d',
                        CK: '\u041e\u0441\u0442\u0440\u043e\u0432\u0430 \u041a\u0443\u043a\u0430',
                        PN: '\u043e\u0441\u0442\u0440\u043e\u0432\u0430 \u041f\u0438\u0442\u043a\u044d\u0440\u043d',
                        PK: '\u041f\u0430\u043a\u0438\u0441\u0442\u0430\u043d',
                        PW: '\u041f\u0430\u043b\u0430\u0443',
                        PS: '\u041f\u0430\u043b\u0435\u0441\u0442\u0438\u043d\u0441\u043a\u0438\u0435 \u0442\u0435\u0440\u0440\u0438\u0442\u043e\u0440\u0438\u0438',
                        PA: '\u041f\u0430\u043d\u0430\u043c\u0430',
                        PG: '\u041f\u0430\u043f\u0443\u0430 \u2013 \u041d\u043e\u0432\u0430\u044f \u0413\u0432\u0438\u043d\u0435\u044f',
                        PY: '\u041f\u0430\u0440\u0430\u0433\u0432\u0430\u0439',
                        PE: '\u041f\u0435\u0440\u0443',
                        PL: '\u041f\u043e\u043b\u044c\u0448\u0430',
                        PT: '\u041f\u043e\u0440\u0442\u0443\u0433\u0430\u043b\u0438\u044f',
                        PR: '\u041f\u0443\u044d\u0440\u0442\u043e-\u0420\u0438\u043a\u043e',
                        KR: '\u0420\u0435\u0441\u043f\u0443\u0431\u043b\u0438\u043a\u0430 \u041a\u043e\u0440\u0435\u044f',
                        RE: '\u0420\u0435\u044e\u043d\u044c\u043e\u043d',
                        RU: '\u0420\u043e\u0441\u0441\u0438\u044f',
                        RW: '\u0420\u0443\u0430\u043d\u0434\u0430',
                        RO: '\u0420\u0443\u043c\u044b\u043d\u0438\u044f',
                        SV: '\u0421\u0430\u043b\u044c\u0432\u0430\u0434\u043e\u0440',
                        WS: '\u0421\u0430\u043c\u043e\u0430',
                        SM: '\u0421\u0430\u043d-\u041c\u0430\u0440\u0438\u043d\u043e',
                        ST: '\u0421\u0430\u043d-\u0422\u043e\u043c\u0435 \u0438 \u041f\u0440\u0438\u043d\u0441\u0438\u043f\u0438',
                        SA: '\u0421\u0430\u0443\u0434\u043e\u0432\u0441\u043a\u0430\u044f \u0410\u0440\u0430\u0432\u0438\u044f',
                        SZ: '\u0421\u0432\u0430\u0437\u0438\u043b\u0435\u043d\u0434',
                        MP: '\u0421\u0435\u0432\u0435\u0440\u043d\u044b\u0435 \u041c\u0430\u0440\u0438\u0430\u043d\u0441\u043a\u0438\u0435 \u043e-\u0432\u0430',
                        SC: '\u0421\u0435\u0439\u0448\u0435\u043b\u044c\u0441\u043a\u0438\u0435 \u041e\u0441\u0442\u0440\u043e\u0432\u0430',
                        BL: '\u0421\u0435\u043d-\u0411\u0430\u0440\u0442\u0435\u043b\u0435\u043c\u0438',
                        MF: '\u0421\u0435\u043d-\u041c\u0430\u0440\u0442\u0435\u043d',
                        PM: '\u0421\u0435\u043d-\u041f\u044c\u0435\u0440 \u0438 \u041c\u0438\u043a\u0435\u043b\u043e\u043d',
                        SN: '\u0421\u0435\u043d\u0435\u0433\u0430\u043b',
                        VC: '\u0421\u0435\u043d\u0442-\u0412\u0438\u043d\u0441\u0435\u043d\u0442 \u0438 \u0413\u0440\u0435\u043d\u0430\u0434\u0438\u043d\u044b',
                        KN: '\u0421\u0435\u043d\u0442-\u041a\u0438\u0442\u0441 \u0438 \u041d\u0435\u0432\u0438\u0441',
                        LC: '\u0421\u0435\u043d\u0442-\u041b\u044e\u0441\u0438\u044f',
                        RS: '\u0421\u0435\u0440\u0431\u0438\u044f',
                        EA: '\u0421\u0435\u0443\u0442\u0430 \u0438 \u041c\u0435\u043b\u0438\u043b\u044c\u044f',
                        SG: '\u0421\u0438\u043d\u0433\u0430\u043f\u0443\u0440',
                        SX: '\u0421\u0438\u043d\u0442-\u041c\u0430\u0440\u0442\u0435\u043d',
                        SY: '\u0421\u0438\u0440\u0438\u044f',
                        SK: '\u0421\u043b\u043e\u0432\u0430\u043a\u0438\u044f',
                        SI: '\u0421\u043b\u043e\u0432\u0435\u043d\u0438\u044f',
                        US: '\u0421\u043e\u0435\u0434\u0438\u043d\u0435\u043d\u043d\u044b\u0435 \u0428\u0442\u0430\u0442\u044b',
                        SB: '\u0421\u043e\u043b\u043e\u043c\u043e\u043d\u043e\u0432\u044b \u041e\u0441\u0442\u0440\u043e\u0432\u0430',
                        SO: '\u0421\u043e\u043c\u0430\u043b\u0438',
                        SD: '\u0421\u0443\u0434\u0430\u043d',
                        SR: '\u0421\u0443\u0440\u0438\u043d\u0430\u043c',
                        SL: '\u0421\u044c\u0435\u0440\u0440\u0430-\u041b\u0435\u043e\u043d\u0435',
                        TJ: '\u0422\u0430\u0434\u0436\u0438\u043a\u0438\u0441\u0442\u0430\u043d',
                        TH: '\u0422\u0430\u0438\u043b\u0430\u043d\u0434',
                        TW: '\u0422\u0430\u0439\u0432\u0430\u043d\u044c',
                        TZ: '\u0422\u0430\u043d\u0437\u0430\u043d\u0438\u044f',
                        TG: '\u0422\u043e\u0433\u043e',
                        TK: '\u0422\u043e\u043a\u0435\u043b\u0430\u0443',
                        TO: '\u0422\u043e\u043d\u0433\u0430',
                        TT: '\u0422\u0440\u0438\u043d\u0438\u0434\u0430\u0434 \u0438 \u0422\u043e\u0431\u0430\u0433\u043e',
                        TA: '\u0422\u0440\u0438\u0441\u0442\u0430\u043d-\u0434\u0430-\u041a\u0443\u043d\u044c\u044f',
                        TV: '\u0422\u0443\u0432\u0430\u043b\u0443',
                        TN: '\u0422\u0443\u043d\u0438\u0441',
                        TM: '\u0422\u0443\u0440\u043a\u043c\u0435\u043d\u0438\u0441\u0442\u0430\u043d',
                        TR: '\u0422\u0443\u0440\u0446\u0438\u044f',
                        UG: '\u0423\u0433\u0430\u043d\u0434\u0430',
                        UZ: '\u0423\u0437\u0431\u0435\u043a\u0438\u0441\u0442\u0430\u043d',
                        UA: '\u0423\u043a\u0440\u0430\u0438\u043d\u0430',
                        WF: '\u0423\u043e\u043b\u043b\u0438\u0441 \u0438 \u0424\u0443\u0442\u0443\u043d\u0430',
                        UY: '\u0423\u0440\u0443\u0433\u0432\u0430\u0439',
                        FO: '\u0424\u0430\u0440\u0435\u0440\u0441\u043a\u0438\u0435 \u043e-\u0432\u0430',
                        FM: '\u0424\u0435\u0434\u0435\u0440\u0430\u0442\u0438\u0432\u043d\u044b\u0435 \u0428\u0442\u0430\u0442\u044b \u041c\u0438\u043a\u0440\u043e\u043d\u0435\u0437\u0438\u0438',
                        FJ: '\u0424\u0438\u0434\u0436\u0438',
                        PH: '\u0424\u0438\u043b\u0438\u043f\u043f\u0438\u043d\u044b',
                        FI: '\u0424\u0438\u043d\u043b\u044f\u043d\u0434\u0438\u044f',
                        FK: '\u0424\u043e\u043b\u043a\u043b\u0435\u043d\u0434\u0441\u043a\u0438\u0435 \u043e-\u0432\u0430',
                        FR: '\u0424\u0440\u0430\u043d\u0446\u0438\u044f',
                        GF: '\u0424\u0440\u0430\u043d\u0446\u0443\u0437\u0441\u043a\u0430\u044f \u0413\u0432\u0438\u0430\u043d\u0430',
                        PF: '\u0424\u0440\u0430\u043d\u0446\u0443\u0437\u0441\u043a\u0430\u044f \u041f\u043e\u043b\u0438\u043d\u0435\u0437\u0438\u044f',
                        TF: '\u0424\u0440\u0430\u043d\u0446\u0443\u0437\u0441\u043a\u0438\u0435 \u042e\u0436\u043d\u044b\u0435 \u0442\u0435\u0440\u0440\u0438\u0442\u043e\u0440\u0438\u0438',
                        HR: '\u0425\u043e\u0440\u0432\u0430\u0442\u0438\u044f',
                        CF: '\u0426\u0410\u0420',
                        TD: '\u0427\u0430\u0434',
                        ME: '\u0427\u0435\u0440\u043d\u043e\u0433\u043e\u0440\u0438\u044f',
                        CZ: '\u0427\u0435\u0445\u0438\u044f',
                        CL: '\u0427\u0438\u043b\u0438',
                        CH: '\u0428\u0432\u0435\u0439\u0446\u0430\u0440\u0438\u044f',
                        SE: '\u0428\u0432\u0435\u0446\u0438\u044f',
                        SJ: '\u0428\u043f\u0438\u0446\u0431\u0435\u0440\u0433\u0435\u043d \u0438 \u042f\u043d-\u041c\u0430\u0439\u0435\u043d',
                        LK: '\u0428\u0440\u0438-\u041b\u0430\u043d\u043a\u0430',
                        EC: '\u042d\u043a\u0432\u0430\u0434\u043e\u0440',
                        GQ: '\u042d\u043a\u0432\u0430\u0442\u043e\u0440\u0438\u0430\u043b\u044c\u043d\u0430\u044f \u0413\u0432\u0438\u043d\u0435\u044f',
                        ER: '\u042d\u0440\u0438\u0442\u0440\u0435\u044f',
                        EE: '\u042d\u0441\u0442\u043e\u043d\u0438\u044f',
                        ET: '\u042d\u0444\u0438\u043e\u043f\u0438\u044f',
                        ZA: '\u042e\u0410\u0420',
                        GS: '\u042e\u0436\u043d\u0430\u044f \u0413\u0435\u043e\u0440\u0433\u0438\u044f \u0438 \u042e\u0436\u043d\u044b\u0435 \u0421\u0430\u043d\u0434\u0432\u0438\u0447\u0435\u0432\u044b \u043e-\u0432\u0430',
                        SS: '\u042e\u0436\u043d\u044b\u0439 \u0421\u0443\u0434\u0430\u043d',
                        JM: '\u042f\u043c\u0430\u0439\u043a\u0430',
                        JP: '\u042f\u043f\u043e\u043d\u0438\u044f',
                    },
                    ua: {
                        AF: 'Афганістан',
                        AX: 'Аландські острови',
                        AL: 'Албанія',
                        DZ: 'Алжир',
                        AS: 'Американське Самоа',
                        AD: 'Андорра',
                        AO: 'Ангола',
                        AI: 'Ангілья',
                        AQ: 'Антарктида',
                        AG: 'Антигуа і Барбуда',
                        AR: 'Аргентина',
                        AM: 'Вірменія',
                        AW: 'Аруба',
                        AC: 'Острів Вознесіння',
                        AU: 'Австралія',
                        AT: 'Австрія',
                        AZ: 'Азербайджан',
                        BS: 'Багамські острови',
                        BH: 'Бахрейн',
                        BD: 'Бангладеш',
                        BB: 'Барбадос',
                        BY: 'Білорусь',
                        BE: 'Бельгія',
                        BZ: 'Беліз',
                        BJ: 'Бенін',
                        BM: 'Бермудські острови',
                        BT: 'Бутан',
                        BO: 'Болівія',
                        BA: 'Боснія і Герцеговина',
                        BW: 'Ботсвана',
                        BR: 'Бразилія',
                        IO: 'Британська територія в Індійському океані',
                        VG: 'Британські Віргінські острови',
                        BN: 'Бруней',
                        BG: 'Болгарія',
                        BF: 'Буркіна-Фасо',
                        BI: 'Бурунді',
                        KH: 'Камбоджа',
                        CM: 'Камерун',
                        CA: 'Канада',
                        IC: 'Канарські острови',
                        CV: 'Кабо-Верде',
                        BQ: 'Карибські Нідерланди',
                        KY: 'Кайманові острови',
                        CF: 'Центрально-Африканська Республіка',
                        EA: 'Сеута та Мелілья',
                        TD: 'Чад',
                        CL: 'Чилі',
                        CN: 'Китай',
                        CX: 'Острів Різдва',
                        CC: 'Кокосові (Кілінг) острови',
                        CO: 'Колумбія',
                        KM: 'Коморські острови',
                        CG: 'Конго - Браззавіль',
                        CD: 'Конго - Кіншаса',
                        CK: 'Острови Кука',
                        CR: 'Коста-Ріка',
                        CI: "Кот-д'Івуар",
                        HR: 'Хорватія',
                        CU: 'Куба',
                        CW: 'Кюрасао',
                        CY: 'Кіпр',
                        CZ: 'Чехія',
                        DK: 'Данія',
                        DG: 'Дієго Гарсія',
                        DJ: 'Джібуті',
                        DM: 'Домініка',
                        DO: 'Домініканська Республіка',
                        EC: 'Еквадор',
                        EG: 'Єгипет',
                        SV: 'Сальвадор',
                        GQ: 'Екваторіальна Гвінея',
                        ER: 'Еритрея',
                        EE: 'Естонія',
                        ET: 'Ефіопія',
                        FK: 'Фолклендські острови',
                        FO: 'Фарерські острови',
                        FJ: 'Фіджі',
                        FI: 'Фінляндія',
                        FR: 'Франція',
                        GF: 'Французька Гвіана',
                        PF: 'Французька Полінезія',
                        TF: 'Французькі південні території',
                        GA: 'Габон',
                        GM: 'Гамбія',
                        GE: 'Грузія',
                        DE: 'Німеччина',
                        GH: 'Гана',
                        GI: 'Гібралтар',
                        GR: 'Греція',
                        GL: 'Гренландія',
                        GD: 'Гренада',
                        GP: 'Гваделупа',
                        GU: 'Гуам',
                        GT: 'Гватемала',
                        GG: 'Гернсі',
                        GN: 'Гвінея',
                        GW: 'Гвінея-Бісау',
                        GY: 'Гаяна',
                        HT: 'Гаїті',
                        HN: 'Гондурас',
                        HK: 'Гонконг, ОАР Китай',
                        HU: 'Угорщина',
                        IS: 'Ісландія',
                        IN: 'Індії',
                        ID: 'Індонезія',
                        IR: 'Іран',
                        IQ: 'Ірак',
                        IE: 'Ірландія',
                        IM: 'Острів Мен',
                        IL: 'Ізраїль',
                        IT: 'Італія',
                        JM: 'Ямайка',
                        JP: 'Японія',
                        JE: 'Джерсі',
                        JO: 'Джордан',
                        KZ: 'Казахстан',
                        KE: 'Кенія',
                        KI: 'Кірібаті',
                        XK: 'Косово',
                        KW: 'Кувейт',
                        KG: 'Киргизстан',
                        LA: 'Лаос',
                        LV: 'Латвія',
                        LB: 'Ліван',
                        LS: 'Лесото',
                        LR: 'Ліберія',
                        LY: 'Лівія',
                        LI: 'Ліхтенштейн',
                        LT: 'Литва',
                        LU: 'Люксембург',
                        MO: 'Макао, ОАР Китай',
                        MK: 'Македонія',
                        MG: 'Мадагаскар',
                        MW: 'Малаві',
                        MY: 'Малайзія',
                        MV: 'Мальдіви',
                        ML: 'Малі',
                        MT: 'Мальта',
                        MH: 'Маршаллові острови',
                        MQ: 'Мартиніка',
                        MR: 'Мавританія',
                        MU: 'Маврикій',
                        YT: 'Майотта',
                        MX: 'Мексика',
                        FM: 'Мікронезія',
                        MD: 'Молдова',
                        MC: 'Монако',
                        MN: 'Монголія',
                        ME: 'Чорногорія',
                        MS: 'Монсеррат',
                        MA: 'Марокко',
                        MZ: 'Мозамбік',
                        MM: "М'янма (Бірма)",
                        NA: 'Намібія',
                        NR: 'Науру',
                        NP: 'Непал',
                        NL: 'Нідерланди',
                        NC: 'Нова Каледонія',
                        NZ: 'Нова Зеландія',
                        NI: 'Нікарагуа',
                        NE: 'Нігер',
                        NG: 'Нігерія',
                        NU: 'Ніуе',
                        NF: 'Острів Норфолк',
                        KP: 'Північна Корея',
                        MP: 'Північні Маріанські острови',
                        NO: 'Норвегія',
                        OM: 'Оман',
                        PK: 'Пакистан',
                        PW: 'Палау',
                        PS: 'Палестинські території',
                        PA: 'Панама',
                        PG: 'Папуа-Нова Гвінея',
                        PY: 'Парагвай',
                        PE: 'Перу',
                        PH: 'Філіппіни',
                        PN: 'Острови Піткерн',
                        PL: 'Польща',
                        PT: 'Португалія',
                        PR: 'Пуерто-Ріко',
                        QA: 'Катар',
                        RE: 'Реюньйон',
                        RO: 'Румунія',
                        RU: 'Росія',
                        RW: 'Руанда',
                        WS: 'Самоа',
                        SM: 'Сан-Марино',
                        ST: 'Сан-Томе та Принципі',
                        SA: 'Саудівська Аравія',
                        SN: 'Сенегал',
                        RS: 'Сербія',
                        SC: 'Сейшельські острови',
                        SL: 'Сьєрра-Леоне',
                        SG: 'Сінгапур',
                        SX: 'Сінт-Мартен',
                        SK: 'Словаччина',
                        SI: 'Словенія',
                        SB: 'Соломонові острови',
                        SO: 'Сомалі',
                        ZA: 'Південна Африка',
                        GS: 'Південна Джорджія та Південні Сандвічеві острови',
                        KR: 'Південна Корея',
                        SS: 'Південний Судан',
                        ES: 'Іспанія',
                        LK: 'Шрі-Ланка',
                        BL: 'Св. Барт\u00e9лемі',
                        SH: 'Св. Хелена',
                        KN: 'Св. Кітс і Невіс',
                        LC: 'Св. Люсія',
                        MF: 'Св. Мартін',
                        PM: "Св. П'єр і Мікелон",
                        VC: 'Св. Вінсент і Гренадини',
                        SD: 'Судан',
                        SR: 'Суринам',
                        SJ: 'Шпіцберген і Ян-Маєн',
                        SZ: 'Свазіленд',
                        SE: 'Швеція',
                        CH: 'Швейцарія',
                        SY: 'Сирія',
                        TW: 'Тайвань',
                        TJ: 'Таджикистан',
                        TZ: 'Танзанія',
                        TH: 'Таїланд',
                        TL: 'Тимор-Лешті',
                        TG: 'Того',
                        TK: 'Токелау',
                        TO: 'Тонга',
                        TT: 'Тринідад і Тобаго',
                        TA: 'Трістан да Кунья',
                        TN: 'Туніс',
                        TR: 'Туреччина',
                        TM: 'Туркменістан',
                        TC: 'Острови Теркс і Кайкос',
                        TV: 'Тувалу',
                        UM: 'США Віддалені острови',
                        VI: 'США Віргінські острови',
                        UG: 'Уганда',
                        UA: 'Україна',
                        AE: 'Об’єднані Арабські Емірати',
                        GB: 'Сполучене Королівство',
                        US: 'Сполучені Штати',
                        UY: 'Уругвай',
                        UZ: 'Узбекистан',
                        VU: 'Вануату',
                        VA: 'Ватикан',
                        VE: 'Венесуела',
                        VN: "В'єтнам",
                        WF: 'Уолліс і Футуна',
                        EH: 'Західна Сахара',
                        YE: 'Ємен',
                        ZM: 'Замбія',
                        ZW: 'Зімбабве',
                    },
                    pl: {
                        AF: 'Afganistan',
                        AX: 'Wyspy Alandzkie',
                        AL: 'Albania',
                        DZ: 'Algieria',
                        AS: 'Samoa Amerykańskie',
                        AD: 'Andora',
                        AO: 'Angola',
                        AI: 'Anguilla',
                        AQ: 'Antarktyda',
                        AG: 'Antigua i Barbuda',
                        AR: 'Argentyna',
                        AM: 'Armenia',
                        AW: 'Aruba',
                        AC: 'Wyspa Wniebowstąpienia',
                        AU: 'Australia',
                        AT: 'Austria',
                        AZ: 'Azerbejdżan',
                        BS: 'Bahamy',
                        BH: 'Bahrajn',
                        BD: 'Bangladesz',
                        BB: 'Barbados',
                        BY: 'Białoruś',
                        BE: 'Belgia',
                        BZ: 'Belize',
                        BJ: 'Benin',
                        BM: 'Bermudy',
                        BT: 'Bhutan',
                        BO: 'Boliwia',
                        BA: 'Bośnia i Hercegowina',
                        BW: 'Botswana',
                        BR: 'Brazylia',
                        IO: 'Brytyjskie Terytorium Oceanu Indyjskiego',
                        VG: 'Brytyjskie Wyspy Dziewicze',
                        BN: 'Brunei',
                        BG: 'Bułgaria',
                        BF: 'Burkina Faso',
                        BI: 'Burundi',
                        KH: 'Kambodża',
                        CM: 'Kamerun',
                        CA: 'Kanada',
                        IC: 'Wyspy Kanaryjskie',
                        CV: 'Republika Zielonego Przylądka',
                        BQ: 'Antyle Holenderskie',
                        KY: 'Kajmany',
                        CF: 'Republika Środkowoafrykańska',
                        EA: 'Ceuta i Melilla',
                        TD: 'Czad',
                        CL: 'Chile',
                        CN: 'Chiny',
                        CX: 'Wyspa Bożego Narodzenia',
                        CC: 'Wyspy Kokosowe',
                        CO: 'Kolumbia',
                        KM: 'Komory',
                        CG: 'Kongo - Brazzaville',
                        CD: 'Kongo - Kinszasa',
                        CK: 'Wyspy Cooka',
                        CR: 'Kostaryka',
                        CI: 'Côte d’Ivoire',
                        HR: 'Chorwacja',
                        CU: 'Kuba',
                        CW: 'Curaçao',
                        CY: 'Cypr',
                        CZ: 'Czechy',
                        DK: 'Dania',
                        DG: 'Diego Garcia',
                        DJ: 'Dżibuti',
                        DM: 'Dominika',
                        DO: 'Republika Dominikańska',
                        EC: 'Ekwador',
                        EG: 'Egipt',
                        SV: 'Salwador',
                        GQ: 'Gwinea Równikowa',
                        ER: 'Erytrea',
                        EE: 'Estonia',
                        ET: 'Etiopia',
                        FK: 'Falklandy',
                        FO: 'Wyspy Owcze',
                        FJ: 'Fidżi',
                        FI: 'Finlandia',
                        FR: 'Francja',
                        GF: 'Gujana Francuska',
                        PF: 'Polinezja Francuska',
                        TF: 'Francuskie Terytoria Południowe i Antarktyczne',
                        GA: 'Gabon',
                        GM: 'Gambia',
                        GE: 'Gruzja',
                        DE: 'Niemcy',
                        GH: 'Ghana',
                        GI: 'Gibraltar',
                        GR: 'Grecja',
                        GL: 'Grenlandia',
                        GD: 'Grenada',
                        GP: 'Gwadelupa',
                        GU: 'Guam',
                        GT: 'Gwatemala',
                        GG: 'Guernsey',
                        GN: 'Gwinea',
                        GW: 'Gwinea Bissau',
                        GY: 'Gujana',
                        HT: 'Haiti',
                        HN: 'Honduras',
                        HK: 'Hongkong (SRA)',
                        HU: 'Węgry',
                        IS: 'Islandia',
                        IN: 'Indie',
                        ID: 'Indonezja',
                        IR: 'Iran',
                        IQ: 'Irak',
                        IE: 'Irlandia',
                        IM: 'Wyspa Man',
                        IL: 'Izrael',
                        IT: 'Włochy',
                        JM: 'Jamajka',
                        JP: 'Japonia',
                        JE: 'Jersey',
                        JO: 'Jordania',
                        KZ: 'Kazachstan',
                        KE: 'Kenia',
                        KI: 'Kiribati',
                        XK: 'Kosowo',
                        KW: 'Kuwejt',
                        KG: 'Kirgistan',
                        LA: 'Laos',
                        LV: 'Łotwa',
                        LB: 'Liban',
                        LS: 'Lesotho',
                        LR: 'Liberia',
                        LY: 'Libia',
                        LI: 'Liechtenstein',
                        LT: 'Litwa',
                        LU: 'Luksemburg',
                        MO: 'Makau (SRA)',
                        MK: 'Macedonia Północna',
                        MG: 'Madagaskar',
                        MW: 'Malawi',
                        MY: 'Malezja',
                        MV: 'Malediwy',
                        ML: 'Mali',
                        MT: 'Malta',
                        MH: 'Wyspy Marshalla',
                        MQ: 'Martynika',
                        MR: 'Mauretania',
                        MU: 'Mauritius',
                        YT: 'Majotta',
                        MX: 'Meksyk',
                        FM: 'Mikronezja',
                        MD: 'Mołdawia',
                        MC: 'Monako',
                        MN: 'Mongolia',
                        ME: 'Czarnogóra',
                        MS: 'Montserrat',
                        MA: 'Maroko',
                        MZ: 'Mozambik',
                        MM: 'Mjanma (Birma)',
                        NA: 'Namibia',
                        NR: 'Nauru',
                        NP: 'Nepal',
                        NL: 'Holandia',
                        NC: 'Nowa Kaledonia',
                        NZ: 'Nowa Zelandia',
                        NI: 'Nikaragua',
                        NE: 'Niger',
                        NG: 'Nigeria',
                        NU: 'Niue',
                        NF: 'Wyspa Norfolk',
                        KP: 'Korea Północna',
                        MP: 'Mariany Północne',
                        NO: 'Norwegia',
                        OM: 'Oman',
                        PK: 'Pakistan',
                        PW: 'Palau',
                        PS: 'Terytoria Palestyńskie',
                        PA: 'Panama',
                        PG: 'Papua-Nowa Gwinea',
                        PY: 'Paragwaj',
                        PE: 'Peru',
                        PH: 'Filipiny',
                        PN: 'Wyspy Pitcairn',
                        PL: 'Polska',
                        PT: 'Portugalia',
                        PR: 'Portoryko',
                        QA: 'Katar',
                        RE: 'Reunion',
                        RO: 'Rumunia',
                        RU: 'Rosja',
                        RW: 'Rwanda',
                        WS: 'Samoa',
                        SM: 'San Marino',
                        ST: 'Wyspy Świętego Tomasza i Książęca',
                        SA: 'Arabia Saudyjska',
                        SN: 'Senegal',
                        RS: 'Serbia',
                        SC: 'Seszele',
                        SL: 'Sierra Leone',
                        SG: 'Singapur',
                        SX: 'Sint Maarten',
                        SK: 'Słowacja',
                        SI: 'Słowenia',
                        SB: 'Wyspy Salomona',
                        SO: 'Somalia',
                        ZA: 'Republika Południowej Afryki',
                        GS: 'Georgia Południowa i Sandwich Południowy',
                        KR: 'Korea Południowa',
                        SS: 'Sudan Południowy',
                        ES: 'Hiszpania',
                        LK: 'Sri Lanka',
                        BL: 'Saint-Barthélemy',
                        SH: 'Wyspa Świętej Heleny, Wyspa Wniebowstąpienia i Tristan da Cunha',
                        KN: 'Saint Kitts i Nevis',
                        LC: 'Saint Lucia',
                        MF: 'Saint-Martin',
                        PM: 'Saint-Pierre i Miquelon',
                        VC: 'Saint Vincent i Grenadyny',
                        SD: 'Sudan',
                        SR: 'Surinam',
                        SJ: 'Svalbard i Jan Mayen',
                        SZ: 'Eswatini',
                        SE: 'Szwecja',
                        CH: 'Szwajcaria',
                        SY: 'Syria',
                        TW: 'Tajwan',
                        TJ: 'Tadżykistan',
                        TZ: 'Tanzania',
                        TH: 'Tajlandia',
                        TL: 'Timor Wschodni',
                        TG: 'Togo',
                        TK: 'Tokelau',
                        TO: 'Tonga',
                        TT: 'Trynidad i Tobago',
                        TA: 'Tristan da Cunha',
                        TN: 'Tunezja',
                        TR: 'Turcja',
                        TM: 'Turkmenistan',
                        TC: 'Turks i Caicos',
                        TV: 'Tuvalu',
                        UM: 'Dalekie Wyspy Mniejsze Stanów Zjednoczonych',
                        VI: 'Wyspy Dziewicze Stanów Zjednoczonych',
                        UG: 'Uganda',
                        UA: 'Ukraina',
                        AE: 'Zjednoczone Emiraty Arabskie',
                        GB: 'Wielka Brytania',
                        US: 'Stany Zjednoczone',
                        UY: 'Urugwaj',
                        UZ: 'Uzbekistan',
                        VU: 'Vanuatu',
                        VA: 'Watykan',
                        VE: 'Wenezuela',
                        VN: 'Wietnam',
                        WF: 'Wallis i Futuna',
                        EH: 'Sahara Zachodnia',
                        YE: 'Jemen',
                        ZM: 'Zambia',
                        ZW: 'Zimbabwe',
                    },
                };
                return countries[lang] || countries['ru'];
            };

            service.dynamicTableLoading = function (total, page, count, getDataFunction) {
                let rocketElement = document.getElementById('scrollup');
                let pagesPerOneLoad = count,
                    currentPage = page,
                    pagesCount = Math.ceil(total / pagesPerOneLoad),
                    displayShowMore;
                if (currentPage < pagesCount - 1) {
                    $rootScope.loading = true;
                    currentPage++;
                    displayShowMore = currentPage < pagesCount - 1;
                    updateData(currentPage);
                }

                function updateData(pageNext) {
                    if (getDataFunction) {
                        if (rocketElement) {
                            moveUpFunc();
                        }
                        getDataFunction(pageNext, pagesPerOneLoad);
                    }
                }

                function moveUpFunc() {
                    let scrollUp = rocketElement; // найти элемент
                    scrollUp.style.display = 'block';
                    scrollUp.style.position = 'fixed';
                    scrollUp.style.bottom = '20px';
                    scrollUp.style.left = '0px';
                    scrollUp.onmouseover = function () {
                        // добавить прозрачность
                        scrollUp.style.opacity = 0.3;
                        scrollUp.style.filter = 'alpha(opacity=30)';
                    };

                    scrollUp.onmouseout = function () {
                        //убрать прозрачность
                        scrollUp.style.opacity = 0.5;
                        scrollUp.style.filter = 'alpha(opacity=50)';
                    };

                    scrollUp.onclick = function () {
                        //обработка клика
                        window.scrollTo(0, 0);
                    };
                }

                return displayShowMore;
            };

            service.createModelForSelect = function (model, _type, ...data) {
                return model.map((item) => {
                    let modelForSelect = {};

                    modelForSelect['text'] = item[data[0]];
                    modelForSelect['value'] = item[data[1]];
                    modelForSelect['accountId'] = item[data[2]];

                    if (_type) {
                        modelForSelect.type = _type;
                    }

                    return modelForSelect;
                });
            };

            service.deleteUnnecessaryFields = function (object) {
                if (object) {
                    delete object.actions;
                    delete object.creator;
                    delete object.responsible;
                    delete object.files;
                    delete object.fullName;
                    delete object.fullName2;
                }
            };

            service.pageScrollOnTop = function () {
                $('html, body').animate({ scrollTop: 0 }, 'slow');
            };

            service.listenerForScope = function ($scope, $rootScope) {
                if ($rootScope.curentOnlyMenWatch !== undefined) {
                    $rootScope.curentOnlyMenWatch();
                }
                if ($rootScope.curentRegionWatch !== undefined) {
                    $rootScope.curentRegionWatch();
                }
                $rootScope.curentOnlyMenWatch = $rootScope.$watch('onlyMeChange', function (val) {
                    if ($rootScope.onlyMeChange === true) {
                        $scope.onlyMe = $rootScope.onlyMe;
                        $rootScope.regionId = null;
                        $scope.regionId = null;
                        $scope.tableParams.reload();
                    } else if ($rootScope.onlyMeChange === false) {
                        $scope.tableParams.reload();
                    }
                    $rootScope.onlyMeChange = null;
                });
                $rootScope.curentRegionWatch = $rootScope.$watch('regionChange', function (val) {
                    $scope.regionId = $rootScope.regionId;
                    $scope.regionIdType = $rootScope.regionIdType;
                    if (val) {
                        $rootScope.onlyMe = null;
                        $scope.tableParams.reload();
                        $rootScope.regionChange = false;
                    }
                });
            };

            service.listenerForScopeLight = function ($scope, $rootScope) {
                if ($rootScope.curentOnlyMenWatch !== undefined) {
                    $rootScope.curentOnlyMenWatch();
                }
                if ($rootScope.curentRegionWatch !== undefined) {
                    $rootScope.curentRegionWatch();
                }
                $rootScope.curentOnlyMenWatch = $rootScope.$watch('onlyMeChange', function (val) {
                    if ($rootScope.onlyMeChange === true) {
                        $scope.onlyMe = $rootScope.onlyMe;
                        $rootScope.regionId = null;
                        $scope.regionIdType = null;
                        $scope.regionId = null;
                        $scope.search();
                    } else if ($rootScope.onlyMeChange === false) {
                        $scope.search();
                    }
                    $rootScope.onlyMeChange = null;
                });
                $rootScope.curentRegionWatch = $rootScope.$watch('regionChange', function (val) {
                    $scope.regionId = $rootScope.regionId;
                    $scope.regionIdType = $rootScope.regionIdType;
                    if (val) {
                        $rootScope.onlyMe = null;
                        $scope.search();
                        $rootScope.regionChange = false;
                    }
                });
            };

            //---//
            service.googleCalendarCreateEvent = function (
                googleService,
                startDate,
                fullName,
                vacancyPos,
                calId,
                comment,
                id,
                $filter,
            ) {
                var endDate = angular.copy(startDate);
                endDate = new Date(endDate);
                endDate.setHours(endDate.getHours() + 1);

                googleService.createEvent({
                    end: {
                        dateTime: endDate,
                    },
                    start: {
                        dateTime: startDate,
                    },
                    summary:
                        $filter('translate')('Interview for') +
                        ' ' +
                        fullName +
                        '. ' +
                        $filter('translate')('Vacancy') +
                        ':' +
                        vacancyPos,
                    calendarId: calId,
                    description: comment,
                    id: id + Math.floor(Math.random() * (9999999999999 - 0 + 1)) + 0,
                });
            };

            service.googleCalendarUpdateEvent = function (
                googleService,
                startDate,
                fullName,
                vacancyPos,
                calId,
                comment,
                id,
                $filter,
            ) {
                var endDate = angular.copy(startDate);
                endDate = new Date(endDate);
                endDate.setHours(endDate.getHours() + 1);
                googleService.updateEvent({
                    end: {
                        dateTime: endDate,
                    },
                    start: {
                        dateTime: startDate,
                    },
                    calendarId: calId,
                    summary:
                        $filter('translate')('Interview for') +
                        ' ' +
                        fullName +
                        '. ' +
                        $filter('translate')('Vacancy') +
                        ':' +
                        vacancyPos,
                    description: comment,
                    eventId: id,
                });
            };

            service.vacancyAddInterview = function (
                Vacancy,
                vacancyId,
                position,
                candidateId,
                comment,
                interviewState,
                date,
                callback,
                errorBack,
                frontMode,
                notificationService,
                googleService,
                selectedCalendarId,
                $filter,
                lang,
                $rootScope,
                createGoogleMeet,
                meetComment,
                meetParticipants,
                createTeams,
                minutesDuration,
                summary,
            ) {
                if (!candidateId) {
                    console.error('candidateId is required field');
                    return;
                }

                $rootScope.hover = false;

                Vacancy.addInterview(
                    {
                        vacancyId: vacancyId,
                        candidateId: candidateId,
                        comment: comment,
                        interviewState: interviewState,
                        lang: lang,
                        date: date,
                        createGoogleMeet: createGoogleMeet,
                        meetComment: meetComment,
                        meetParticipants: meetParticipants,
                        createTeams: createTeams,
                        summary: summary,
                        minutesDuration: minutesDuration,
                    },
                    function (resp, e) {
                        if (angular.equals(resp.status, 'ok')) {
                            callback(resp);
                        } else {
                            errorBack(resp);
                        }
                    },
                    function (err) {
                        console.error(err);
                    },
                );
            };
            service.vacancyAddInterviewFromAdvice = function (
                Vacancy,
                vacancyId,
                position,
                candidateId,
                comment,
                interviewState,
                date,
                callback,
                errorBack,
                frontMode,
                notificationService,
                googleService,
                selectedCalendarId,
                $filter,
                lang,
                $rootScope,
            ) {
                if (!candidateId) {
                    console.error('candidateId is required field');
                    return;
                }
                Vacancy.addInterview(
                    {
                        vacancyId: vacancyId,
                        candidateId: candidateId,
                        comment: comment,
                        interviewState: interviewState,
                        lang: lang,
                        date: date,
                        interviewSource: 'advice',
                    },
                    function (resp) {
                        if (angular.equals(resp.status, 'ok')) {
                            if (date && $rootScope.candnotify.send && $rootScope.candnotify.sendMail) {
                                var candnotify = $rootScope.candnotify;
                                Vacancy.sendInterviewCreateMail(
                                    {
                                        email: candnotify.sendMail,
                                        vacancyId: vacancyId,
                                        candidateId: candidateId,
                                        fullName: candnotify.fullName,
                                        date: date,
                                        lang: lang,
                                    },
                                    function (resp) {},
                                );
                            }
                            callback(resp);
                        } else {
                            errorBack(resp);
                        }
                    },
                    function (err) {
                        console.error(err);
                    },
                );
            };

            service.vacancyAddInterviewAndNotify = function (
                Vacancy,
                vacancyId,
                position,
                candidateId,
                comment,
                interviewState,
                date,
                callback,
                errorBack,
                frontMode,
                notificationService,
                googleService,
                selectedCalendarId,
                $filter,
                lang,
                $rootScope,
                createGoogleMeet,
                meetComment,
                meetParticipants,
                createTeams,
                minutesDuration,
                summary,
            ) {
                $rootScope.hover = false;

                Vacancy.setInterviewAndNotify(
                    {
                        vacancyId: vacancyId,
                        candidateId: candidateId,
                        comment: comment,
                        interviewState: interviewState,
                        lang: lang,
                        date: date,
                        createGoogleMeet: createGoogleMeet,
                        meetComment: meetComment,
                        meetParticipants: meetParticipants,
                        createTeams: createTeams,
                        minutesDuration: minutesDuration,
                        summary: summary,
                    },
                    function (resp, e) {
                        if (angular.equals(resp.status, 'ok')) {
                            callback(resp);
                            // notificationService.success($filter('translate')('added_candidate'));
                        } else if (resp.code === 'googleMeetError') {
                            errorBack(resp);
                        } else {
                            notificationService.error(
                                $filter('translate')('Candidate has been added to this position'),
                            );
                            errorBack(resp);
                        }
                    },
                    function (err) {
                        console.error(err);
                    },
                );
            };

            var isAutoRefreshed = false;
            service.autoRefreshIN = function () {
                if (!isAutoRefreshed) {
                    setInterval(function () {
                        if (IN) IN.User.refresh();
                    }, 600000);
                    isAutoRefreshed = true;
                }
            };

            service.getPlaceInfo = function (placeText, callback) {
                try {
                    $('#crutchGoogleMap').append("<div id='googleCrutch'></div>");
                    if (!!google) {
                        var map = new google.maps.Map(document.getElementById('googleCrutch'), {
                            mapTypeId: 'roadmap',
                            zoom: 15,
                        });
                        var placeService = new google.maps.places.PlacesService(map);
                        var service = new google.maps.places.AutocompleteService();
                        service.getPlacePredictions({ input: placeText, types: ['(regions)'] }, function (val) {
                            placeService.getDetails({ placeId: val[0].place_id }, function (val) {
                                $('#crutchGoogleMap').empty();
                                callback(val);
                            });
                        });
                    }
                } catch (e) {}
            };

            service.isNotBlank = function (str) {
                if (!str) return false;
                if (typeof str !== 'string' || !str.trim() || str.trim() == 'null' || str.trim() == 'undefined')
                    return false;
                return !/^\s*$/.test(str);
            };
            service.getParameterByName = function (name, url) {
                if (!url) url = window.location.href;
                name = name.replace(/[\[\]]/g, '\\$&');
                const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                    results = regex.exec(url);
                if (!results) return null;
                if (!results[2]) return '';
                return decodeURIComponent(results[2].replace(/\+/g, ' '));
            };

            service.convertToRegionObject = function (place, scope) {
                var object = {
                    country: null,
                    area: null,
                    city: null,
                    lat: null,
                    lng: null,
                    lang: 'ru',
                    regionId: null,
                    fullName: 'full',
                };
                if (place != null) {
                    angular.forEach(place.address_components, function (val) {
                        angular.forEach(val.types, function (valT) {
                            switch (valT) {
                                case 'country':
                                    object.country = val.long_name;
                                    break;
                                case 'administrative_area_level_1':
                                    object.area = val.long_name;
                                    break;
                                case 'locality':
                                    object.city = val.long_name;
                                    break;
                            }
                        });
                    });
                    object.regionId = place.id;
                    object.googlePlaceId = { googlePlaceId: place.place_id };
                    if (scope) {
                        if (scope.map != undefined) {
                            scope.map.center.latitude = place.geometry.location.lat();
                            scope.map.center.longitude = place.geometry.location.lng();
                        }
                        if (scope.marker != undefined) {
                            scope.marker.coords.latitude = place.geometry.location.lat();
                            scope.marker.coords.longitude = place.geometry.location.lng();
                        }
                    }
                    if (place.geometry != null) {
                        object.lat = place.geometry.location.k;
                        object.lng = place.geometry.location.D;
                    } else {
                        object.lat = 48.379433;
                        object.lng = 31.165579999999977;
                    }
                    object.fullName = place.formatted_address;
                    return object;
                } else {
                    return null;
                }
            };

            service.addContactsInCandidateObject = function ($scope) {
                var candidate = $scope.pageObject.employee.candidateId;
                var contacts = $scope.pageObject.contacts;
                var array = [];
                if (!candidate.contacts) {
                    candidate.contacts = [];
                }
                if (contacts.email) {
                    array.push({ type: 'email', value: contacts.email });
                }
                if (contacts.mphone) {
                    array.push({ type: 'mphone', value: contacts.mphone });
                }
                if (contacts.skype) {
                    array.push({ type: 'skype', value: contacts.skype });
                }
                if (contacts.linkedin) {
                    array.push({ type: 'linkedin', value: contacts.linkedin });
                }
                if (contacts.facebook) {
                    array.push({ type: 'facebook', value: contacts.facebook });
                }
                if (contacts.homepage) {
                    array.push({ type: 'homepage', value: contacts.homepage });
                }
                candidate.contacts = array;
            };

            service.differenceBetweenTwoDates = function (firstDate, secondDate) {
                var oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds

                return Math.round((new Date(firstDate) - secondDate) / oneDay);
            };

            service.getUrlVars = function (url) {
                var hash;
                var myJson = {};
                var hashes = url.slice(url.indexOf('?') + 1).split('&');
                for (var i = 0; i < hashes.length; i++) {
                    hash = hashes[i].split('=');
                    myJson[hash[0]] = hash[1];
                }
                return myJson;
            };

            service.initDocuments = function (file) {
                const fileType = (file.fileName.match(/\.([0-9a-z]+)$/i) || [])[1];
                if (fileType) {
                    if (isIncludesType(wordFileTypes, fileType)) {
                        file.showFiles = true;
                    } else if (isIncludesType(textFileTypes, fileType)) {
                        file.showFiles = true;
                        file.showText = true;
                    } else if (isIncludesType(imageFileTypes, fileType)) {
                        file.showImg = true;
                    } else if (fileType.includes('html')) {
                        file.notShow = true;
                    }
                }
                file.fileName = file.fileName.replace(/\[*]*/g, '');
                return file;
            };
            service.onGeneratePasscodeForEnterprise = function (finalDate, client) {
                return new Promise((resolve, reject) => {
                    $http({
                        method: 'GET',
                        url: '/hr/generatePasscodeForEnterprise' + '?finalDate=' + finalDate + '&client=' + client,
                    })
                        .then((resp) => {
                            if (resp.status === 200) {
                                resolve(resp.data);
                            } else reject(resp);
                        })
                        .catch((err) => console.error(err.message));
                });
            };
            service.onGetLastGeneratedPasscode = function () {
                return new Promise((resolve, reject) => {
                    $http({
                        method: 'GET',
                        url: '/hr/getLastGeneratedPasscode',
                    })
                        .then((resp) => {
                            if (resp.status === 200) {
                                resolve(resp.data);
                            } else reject(resp);
                        })
                        .catch((err) => console.error(err.message));
                });
            };

            service.onGetSAMLSettings = function () {
                return new Promise((resolve, reject) => {
                    $http({
                        method: 'GET',
                        url: '/hr/saml/getSamlSettings',
                    })
                        .then((resp) => {
                            if (resp.status === 200) {
                                resolve(resp.data);
                            } else reject(resp);
                        })
                        .catch((err) => console.error(err.message));
                });
            };
            service.onDeleteSAMLSettings = function () {
                return new Promise((resolve, reject) => {
                    $http({
                        method: 'GET',
                        url: '/hr/saml/deleteSamlSettings ',
                    })
                        .then((resp) => {
                            if (resp.status === 200) {
                                resolve(resp.data);
                            } else reject(resp);
                        })
                        .catch((err) => console.error(err.message));
                });
            };
            service.onSaveSAMLSettings = function (data) {
                return new Promise((resolve, reject) => {
                    $http({
                        method: 'POST',
                        url: '/hr/saml/saveSamlSettings ',
                        data,
                    })
                        .then((resp) => {
                            if (resp.status === 200) {
                                resolve(resp.data);
                            } else reject(resp);
                        })
                        .catch((err) => console.error(err.message));
                });
            };
            service.onGetIdpParams = function () {
                return new Promise((resolve, reject) => {
                    $http({
                        method: 'GET',
                        url: '/hr/saml/getIdpParams ',
                    })
                        .then((resp) => {
                            if (resp.status === 200) {
                                resolve(resp.data);
                            } else reject(resp);
                        })
                        .catch((err) => console.error(err.message));
                });
            };

            service.getVersion = function () {
                return new Promise((resolve, reject) => {
                    $http({
                        method: 'GET',
                        url: '/js/Version.json',
                    })
                        .then((resp) => {
                            if (resp && resp.status === 200 && resp.data) {
                                notificationData['releaseNumber'] = resp.data.release_number;
                                notificationData['popup_versions'] = resp.data.popup_versions;
                                resolve(resp.data);
                            } else reject(resp);
                        })
                        .catch((err) => {
                            console.error('Upd version error:', err);
                        });
                });
            };

            service.getLinkFileForShow = function (file, $location, $sce) {
                return new Promise((resolve, reject) => {
                    let linkFileForShow;
                    let checkFileForPreview = '/hr/checkFileForPreview/' + $rootScope.me.orgId + '/' + file.fileId;
                    if (file.showFiles) {
                        if (
                            $rootScope.me.orgParams.restrictFileView &&
                            $rootScope.me.orgParams.restrictFileView !== 'false'
                        ) {
                            const fileExtension =
                                file.fileName && /[.]/.exec(file.fileName) ? /[^.]+$/.exec(file.fileName) : '';
                            const availableExtensions = ['.pdf', '.rtf', '.doc', '.docx'];
                            const fileExtensionFull = fileExtension && fileExtension[0] ? `.${fileExtension[0]}` : '';
                            if (!availableExtensions.includes(fileExtensionFull)) {
                                notificationService.error(
                                    $filter('translate')(
                                        'When the function "Request authorization to view external links to documents" is enabled, the preview is available only for pdf, doc, docx and rtf files.',
                                    ),
                                );
                                return;
                            }
                            fetch('https://' + $location.$$host + checkFileForPreview)
                                .then((data) => {
                                    return data.json();
                                })
                                .then((data) => {
                                    if (data.status === 'ok') {
                                        $http({
                                            method: 'GET',
                                            url: '/hr/generateFileToken/' + file.fileId,
                                        })
                                            .then((resp) => {
                                                if (resp.data.status === 'ok') {
                                                    const fileExtensionFullName = fileExtension && fileExtension.input;
                                                    let fileUrl =
                                                        '/hr/showConvertedFile/' +
                                                        $rootScope.me.orgId +
                                                        '/' +
                                                        file.fileId +
                                                        '/' +
                                                        fileExtensionFullName +
                                                        '?token=' +
                                                        resp.data.message +
                                                        (fileExtensionFull === '.pdf' ? '#navpanes=0' : '');
                                                    let fileUrlFull = 'https://' + $location.$$host + fileUrl;
                                                    resolve(fileUrlFull);
                                                }
                                            })
                                            .catch((err) => console.error(err.message));
                                    } else {
                                        notificationService.error(data.message);
                                    }
                                });
                        } else {
                            const fileExtension =
                                file.fileName && /[.]/.exec(file.fileName) ? /[^.]+$/.exec(file.fileName) : '';
                            const fileExtensionFull = fileExtension && fileExtension[0] ? `.${fileExtension[0]}` : '';
                            const fileExtensionFullName =
                                fileExtension &&
                                fileExtension.input &&
                                fileExtension.input.replaceAll('/', '_').replaceAll('\\', '_').replaceAll(':', '_');
                            let fileUrl =
                                '/hr/showFile/' + $rootScope.me.orgId + '/' + file.fileId + '/' + fileExtensionFullName;
                            let fileUrlFull = 'https://' + $location.$$host + fileUrl;

                            if (file.showText) {
                                linkFileForShow = fileUrl;
                            } else {
                                linkFileForShow = $sce.trustAsResourceUrl(
                                    'https://view.officeapps.live.com/op/embed.aspx?src=' + fileUrlFull,
                                );
                            }

                            fetch('https://' + $location.$$host + checkFileForPreview)
                                .then((data) => {
                                    return data.json();
                                })
                                .then((data) => {
                                    if (data.status === 'ok') {
                                        resolve(linkFileForShow);
                                    } else {
                                        notificationService.error(data.message);
                                    }
                                });
                        }
                    } else {
                        resolve(linkFileForShow);
                    }
                });
            };

            service.getLinkImgForShow = function (file) {
                let linkImgForShow;

                if (file.showImg) {
                    linkImgForShow = '/hr/getapp?id=' + file.fileId + '/';
                }

                return linkImgForShow;
            };
            service.showModalResume = async function (file, $scope, $rootScope, $location, $sce, $uibModal) {
                file = service.initDocuments(file);

                if (!file || file.notShow) {
                    notificationService.error($filter('translate')('You can not view this file'));
                    return;
                }

                $scope.shownResume = file;

                if (file.showFiles) {
                    $scope.linkFileForShow = await service.getLinkFileForShow(file, $location, $sce);
                } else if (file.showImg) {
                    $scope.linkImgForShow = service.getLinkImgForShow(file);
                }

                if (file.showFiles || file.showImg) {
                    $scope.modalInstance = $uibModal
                        .open({
                            animation: true,
                            templateUrl: '../partials/modal/candidate-show-file.html',
                            size: 'lg',
                            windowClass: 'show-resume',
                            scope: $scope,
                        })
                        .result.catch(() => {});
                } else if (!file.notShow) {
                    window.location =
                        serverAddress +
                        '/getapp/' +
                        file.fileId +
                        '/' +
                        service.getFileResolutionFromName(file.fileName).substring(1);
                }
            };

            service.getFileResolutionFromName = function (fileName) {
                const fileResolution = fileName && /[.]/.exec(fileName) ? /[^.]+$/.exec(fileName) : '';
                return fileResolution && fileResolution[0] ? `.${fileResolution[0]}` : '';
            };

            service.roundMinutes = function (date) {
                date.setHours(date.getHours());
                date.setMinutes(0);

                return date;
            };

            $rootScope.replaceOperators = function ($scope) {
                $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                    /\[\[candidate name\]\]/g,
                    $rootScope.candnotify.name.firstName,
                );

                if ($rootScope.useAmericanNameStyle) {
                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                        /\[\[full name\]\]/g,
                        $rootScope.candnotify.name.fullNameEn,
                    );
                } else {
                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                        /\[\[full name\]\]/g,
                        $rootScope.candnotify.name.fullName,
                    );
                }

                $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                    /\[\[vacancy name\]\]/g,
                    '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}" target="_blank" href=' +
                        $scope.publicLink +
                        ' data-mce-href=' +
                        $scope.publicLink +
                        '>' +
                        $rootScope.VacancyAddedInCandidate?.position +
                        '</a>',
                );
                $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                    .replace(/\[\[recruiter&#39;s name\]\]/g, $rootScope.me.fullName)
                    .replace(
                        /\[\[recruiter's name\]\]/g,
                        $rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName,
                    );
                $rootScope.emailTemplateInModal.title = $rootScope.emailTemplateInModal.title.replace(
                    /\[\[vacancy link\]\]/g,
                    '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}" target="_blank" href=' +
                        $scope.publicLink +
                        ' data-mce-href=' +
                        $scope.publicLink +
                        '>' +
                        $rootScope.VacancyAddedInCandidate?.position +
                        '</a>',
                );
                $rootScope.emailTemplateInModal.title = $rootScope.emailTemplateInModal.title.replace(
                    /\[\[vacancy name\]\]/g,
                    $rootScope.VacancyAddedInCandidate?.position,
                );
                $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                    .replace(
                        /\[\[recruiter&#39;s phone\]\]/g,
                        $rootScope.me.contacts[0] && $rootScope.me.contacts[0].value
                            ? $rootScope.me.contacts[0].value
                            : '',
                    )
                    .replace(
                        /\[\[recruiter's phone\]\]/g,
                        $rootScope.me.contacts[0] && $rootScope.me.contacts[0].value
                            ? $rootScope.me.contacts[0].value
                            : '',
                    );
                $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                    .replace(
                        /\[\[recruiter&#39;s phone 2\]\]/g,
                        $scope.isPhoneWork ? $rootScope.me.contacts[1].value : '',
                    )
                    .replace(/\[\[recruiter's phone 2\]\]/g, $scope.isPhoneWork ? $rootScope.me.contacts[1].value : '');
                $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                    .replace(
                        /\[\[recruiter&#39;s Skype\]\]/g,
                        $rootScope.staticEmailTemplate.skype ? $rootScope.staticEmailTemplate.skype : '',
                    )
                    .replace(
                        /\[\[recruiter's Skype\]\]/g,
                        $rootScope.staticEmailTemplate.skype ? $rootScope.staticEmailTemplate.skype : '',
                    );
                if (!$rootScope.staticEmailTemplate.skype) {
                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(/Skype:/g, '');
                }
                if ($rootScope.staticEmailTemplate.facebook) {
                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                        .replace(
                            /\[\[recruiter&#39;s Facebook\]\]/g,
                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                $rootScope.staticEmailTemplate.facebook +
                                '">' +
                                $rootScope.staticEmailTemplate.facebook +
                                '</a>',
                        )
                        .replace(
                            /\[\[recruiter's Facebook\]\]/g,
                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                $rootScope.staticEmailTemplate.facebook +
                                '">' +
                                $rootScope.staticEmailTemplate.facebook +
                                '</a>',
                        );
                } else {
                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                        .replace(/\[\[recruiter&#39;s Facebook\]\]/g, '')
                        .replace(/\[\[recruiter's Facebook\]\]/g, '');
                }
                if ($rootScope.staticEmailTemplate.linkedin) {
                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                        .replace(
                            /\[\[recruiter&#39;s LinkedIn\]\]/g,
                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                $rootScope.staticEmailTemplate.linkedin +
                                '">' +
                                $rootScope.staticEmailTemplate.linkedin +
                                '</a>',
                        )
                        .replace(
                            /\[\[recruiter's LinkedIn\]\]/g,
                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                $rootScope.staticEmailTemplate.linkedin +
                                '">' +
                                $rootScope.staticEmailTemplate.linkedin +
                                '</a>',
                        );
                } else {
                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                        .replace(/\[\[recruiter&#39;s LinkedIn\]\]/g, '')
                        .replace(/\[\[recruiter's LinkedIn\]\]/g, '');
                }
                if ($rootScope.me.emails.length == 1) {
                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                        /\[\[recruiterEmail\]\]/g,
                        $rootScope.me.emails[0].email,
                    );
                }
            };
            $rootScope.replaceOperators = $rootScope.replaceOperators.bind(this);

            service.createEmailTemplateFunc = function ($scope, $rootScope, id, Mail, $location) {
                let dataContacts = {};
                $rootScope.me.contacts.forEach((item) => {
                    if (dataContacts[item['contactType']]) {
                        return (dataContacts[item['contactType'].toLowerCase()] = item['value'] || ' ');
                    }
                });
                $rootScope.staticEmailTemplate = {
                    candidateName: 'John Dou',
                    date: 1463749200000,
                    recruiterName: $rootScope.me.fullName,
                    recruiterEmail:
                        $rootScope.me.emails && $rootScope.me.emails.length > 0
                            ? $rootScope.me.emails[0].email
                            : $rootScope.me.login,
                };
                angular.forEach($rootScope.me.contacts, function (val) {
                    if (val.contactType == 'phoneWork') {
                        $rootScope.staticEmailTemplate.phoneWork = val.value;
                    }
                    if (val.contactType == 'skype') {
                        $rootScope.staticEmailTemplate.skype = val.value;
                    }
                    if (val.contactType == 'linkedin') {
                        $rootScope.staticEmailTemplate.linkedin = val.value;
                    }
                    if (val.contactType == 'facebook') {
                        $rootScope.staticEmailTemplate.facebook = val.value;
                    }
                });
                $rootScope.hover = false;

                $rootScope.Hover = function () {
                    $rootScope.hover = false;
                };
                $rootScope.HoverLeave = function () {
                    $rootScope.hover = true;
                };
                $rootScope.status2 = false;

                if (id === 'addCandidateInVacancyMCE') {
                    $rootScope.changeTemplateInAddCandidate = (status) => {
                        $rootScope.candnotify.show = false;
                        if (status) {
                            $rootScope.hover = $rootScope.status2;

                            if (status['googleCalendarPrefix'] !== '') {
                                if (typeof status === 'string') {
                                    // Отличить два объекта чтоб разпарсить второй
                                    status = JSON.parse(status); // первый при выборе вакансии он имеет свойство status['googleCalendarPrefix'] не обязатнльго ""// второй при выборе этапа -в нем такого свойства нет он нам для парсинга нужен
                                }
                            }
                            $rootScope.$$phase || $rootScope.$apply();
                            $rootScope.addCandidateInVacancy.status = status;
                            $rootScope.emailTemplateModel = '';
                            Mail.getAllTemplates(
                                {
                                    forVacancy: true,
                                    type:
                                        $rootScope.addCandidateInVacancy.status.type === 'refuse'
                                            ? 'refusal'
                                            : 'interview',
                                },
                                function (resp) {
                                    $rootScope.loading = false;
                                    if (resp.status === 'ok') {
                                        $rootScope.testTemplates = resp.object;
                                        $rootScope.templatesCustomLetter = resp.object.custom;
                                        $rootScope.templatesGeneralLetter = resp.object.general;
                                    }
                                },
                            );

                            if (
                                $rootScope.addCandidateInVacancy.status.value == 'interview' ||
                                $rootScope.addCandidateInVacancy.status.withDate ||
                                $rootScope.addCandidateInVacancy.status.value == 'longlist' ||
                                $rootScope.addCandidateInVacancy.status.value == 'shortlist' ||
                                $rootScope.addCandidateInVacancy.status.value == 'notafit' ||
                                $rootScope.addCandidateInVacancy.status.value == 'declinedoffer' ||
                                $rootScope.addCandidateInVacancy.status.value == 'offer_declined' ||
                                $rootScope.addCandidateInVacancy.status.value == 'probation_failure' ||
                                $rootScope.addCandidateInVacancy.status.value == 'no_response' ||
                                $rootScope.addCandidateInVacancy.status.value == 'no_contacts' ||
                                $rootScope.addCandidateInVacancy.status.type == 'interview' ||
                                $rootScope.addCandidateInVacancy.status.type == 'refuse'
                            ) {
                                var templateType = 'candidateCreateInterviewNotification';
                                if (
                                    $rootScope.addCandidateInVacancy.status.value == 'notafit' ||
                                    $rootScope.addCandidateInVacancy.status.value == 'declinedoffer' ||
                                    $rootScope.addCandidateInVacancy.status.value == 'offer_declined' ||
                                    $rootScope.addCandidateInVacancy.status.value == 'no_response' ||
                                    $rootScope.addCandidateInVacancy.status.value == 'no_contacts' ||
                                    $rootScope.addCandidateInVacancy.status.valueOf == 'probation_failure' ||
                                    $rootScope.addCandidateInVacancy.status.type == 'refuse'
                                ) {
                                    templateType = 'refuseCandidateInVacancy';
                                } else if (
                                    $rootScope.addCandidateInVacancy.status.value == 'longlist' ||
                                    $rootScope.addCandidateInVacancy.status.value == 'shortlist'
                                ) {
                                    templateType = 'seeVacancy';
                                } else if ($rootScope.addCandidateInVacancy.status.value == 'accept_offer') {
                                    templateType = 'offerAccepted';
                                }
                                setTimeout(() => {
                                    let isGetTemplateForStage =
                                        !$scope.emailAutoActionForStage &&
                                        ($rootScope.addCandidateInVacancy.status.withDate ||
                                            $rootScope.addCandidateInVacancy.status.value == 'interview' ||
                                            $rootScope.addCandidateInVacancy.status.value == 'notafit' ||
                                            $rootScope.addCandidateInVacancy.status.value == 'declinedoffer' ||
                                            $rootScope.addCandidateInVacancy.status.value == 'no_response' ||
                                            $rootScope.addCandidateInVacancy.status.value == 'no_contacts' ||
                                            $rootScope.addCandidateInVacancy.status.type == 'interview' ||
                                            $rootScope.addCandidateInVacancy.status.type == 'refuse') &&
                                        !$scope.isPresent &&
                                        templateType != 'offerAccepted';
                                    $rootScope.emailTemplateInModal.title = '';
                                    $rootScope.emailTemplateInModal.text = '';
                                    if (isGetTemplateForStage) {
                                        Mail.getTemplateForStage(
                                            {
                                                stateId: status.customInterviewStateId
                                                    ? status.customInterviewStateId
                                                    : status.value,
                                                vacancyId: $rootScope.VacancyAddedInCandidate.vacancyId,
                                                type: templateType,
                                            },
                                            function (data) {
                                                $rootScope.emailTemplateModel = data.object;
                                                $rootScope.emailTemplateModel.label = data.object.type
                                                    ? data.object.type
                                                    : data.object.name;
                                                $rootScope.emailTemplateInModal = data.object;
                                                $rootScope.tempTemplate = data.object;
                                                $scope.isPhoneWork = false;
                                                if ($rootScope.me.contacts[1] !== undefined) {
                                                    if ($rootScope.me.contacts[1].contactType == 'phoneWork') {
                                                        $scope.isPhoneWork = true;
                                                    }
                                                }
                                                $rootScope.fileForSave = [];
                                                if (!$scope.publicLink) {
                                                    $scope.publicLink =
                                                        location.protocol +
                                                        '//' +
                                                        location.host +
                                                        '/i#/vacancy-' +
                                                        $rootScope.vacancyForAddCandidate;
                                                }
                                                $rootScope.emailTemplateInModal = data.object;
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text.replace(
                                                        /\[\[candidate name\]\]/g,
                                                        $rootScope.candnotify.name.firstName,
                                                    );

                                                if ($rootScope.useAmericanNameStyle) {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text.replace(
                                                            /\[\[full name\]\]/g,
                                                            $rootScope.candnotify.name.fullNameEn,
                                                        );
                                                } else {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text.replace(
                                                            /\[\[full name\]\]/g,
                                                            $rootScope.candnotify.name.fullName,
                                                        );
                                                }

                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text.replace(
                                                        /\[\[vacancy name\]\]/g,
                                                        '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}" target="_blank" href=' +
                                                            $scope.publicLink +
                                                            ' data-mce-href=' +
                                                            $scope.publicLink +
                                                            '>' +
                                                            $rootScope.VacancyAddedInCandidate.position +
                                                            '</a>',
                                                    );
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(
                                                            /\[\[recruiter&#39;s name\]\]/g,
                                                            $rootScope.me.fullName,
                                                        )
                                                        .replace(
                                                            /\[\[recruiter's name\]\]/g,
                                                            $rootScope.useAmericanNameStyle
                                                                ? $rootScope.me.fullNameEn
                                                                : $rootScope.me.fullName,
                                                        );
                                                $rootScope.emailTemplateInModal.title =
                                                    $rootScope.emailTemplateInModal.title.replace(
                                                        /\[\[vacancy link\]\]/g,
                                                        '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}" target="_blank" href=' +
                                                            $scope.publicLink +
                                                            ' data-mce-href=' +
                                                            $scope.publicLink +
                                                            '>' +
                                                            $rootScope.VacancyAddedInCandidate.position +
                                                            '</a>',
                                                    );
                                                $rootScope.emailTemplateInModal.title =
                                                    $rootScope.emailTemplateInModal.title.replace(
                                                        /\[\[vacancy name\]\]/g,
                                                        $rootScope.VacancyAddedInCandidate.position,
                                                    );
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(
                                                            /\[\[recruiter&#39;s phone\]\]/g,
                                                            $rootScope.me.contacts[0] && $rootScope.me.contacts[0].value
                                                                ? $rootScope.me.contacts[0].value
                                                                : '',
                                                        )
                                                        .replace(
                                                            /\[\[recruiter's phone\]\]/g,
                                                            $rootScope.me.contacts[0] && $rootScope.me.contacts[0].value
                                                                ? $rootScope.me.contacts[0].value
                                                                : '',
                                                        );
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(
                                                            /\[\[recruiter&#39;s phone 2\]\]/g,
                                                            $scope.isPhoneWork ? $rootScope.me.contacts[1].value : '',
                                                        )
                                                        .replace(
                                                            /\[\[recruiter's phone 2\]\]/g,
                                                            $scope.isPhoneWork ? $rootScope.me.contacts[1].value : '',
                                                        );
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(
                                                            /\[\[recruiter&#39;s Skype\]\]/g,
                                                            $rootScope.staticEmailTemplate.skype
                                                                ? $rootScope.staticEmailTemplate.skype
                                                                : '',
                                                        )
                                                        .replace(
                                                            /\[\[recruiter's Skype\]\]/g,
                                                            $rootScope.staticEmailTemplate.skype
                                                                ? $rootScope.staticEmailTemplate.skype
                                                                : '',
                                                        );
                                                if (!$rootScope.staticEmailTemplate.skype) {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text.replace(/Skype:/g, '');
                                                }
                                                if ($rootScope.staticEmailTemplate.facebook) {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(
                                                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                                    $rootScope.staticEmailTemplate.facebook +
                                                                    '">' +
                                                                    $rootScope.staticEmailTemplate.facebook +
                                                                    '</a>',
                                                            )
                                                            .replace(
                                                                /\[\[recruiter's Facebook\]\]/g,
                                                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                                    $rootScope.staticEmailTemplate.facebook +
                                                                    '">' +
                                                                    $rootScope.staticEmailTemplate.facebook +
                                                                    '</a>',
                                                            );
                                                } else {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(/\[\[recruiter&#39;s Facebook\]\]/g, '')
                                                            .replace(/\[\[recruiter's Facebook\]\]/g, '');
                                                }
                                                if ($rootScope.staticEmailTemplate.linkedin) {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(
                                                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                                    $rootScope.staticEmailTemplate.linkedin +
                                                                    '">' +
                                                                    $rootScope.staticEmailTemplate.linkedin +
                                                                    '</a>',
                                                            )
                                                            .replace(
                                                                /\[\[recruiter's LinkedIn\]\]/g,
                                                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                                    $rootScope.staticEmailTemplate.linkedin +
                                                                    '">' +
                                                                    $rootScope.staticEmailTemplate.linkedin +
                                                                    '</a>',
                                                            );
                                                } else {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(/\[\[recruiter&#39;s LinkedIn\]\]/g, '')
                                                            .replace(/\[\[recruiter's LinkedIn\]\]/g, '');
                                                }
                                                if ($rootScope.me.emails.length == 1) {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text.replace(
                                                            /\[\[recruiterEmail\]\]/g,
                                                            $rootScope.me.emails[0].email,
                                                        );
                                                }

                                                $rootScope.saveDefaultTemplate = $rootScope.emailTemplateInModal.text;
                                                if (Email.getDefaultMailbox())
                                                    $rootScope.emailTemplateInModal.email = Email.getDefaultMailbox();
                                                Email.setDefaultSignature(Email.getDefaultMailbox());
                                                // if (
                                                //     $rootScope.emailTemplateInModal.fileId &&
                                                //     $rootScope.emailTemplateInModal.fileName
                                                // ) {
                                                //     $rootScope.fileForSave.push({
                                                //         fileId: $rootScope.emailTemplateInModal.fileId,
                                                //         fileName: $rootScope.emailTemplateInModal.fileName,
                                                //     });
                                                // }
                                                if ($rootScope.emailTemplateInModal.filesIdName)
                                                    $rootScope.fileForSave = service.filesIdNameToArray(
                                                        $rootScope.emailTemplateInModal.filesIdName,
                                                    );
                                                if (
                                                    $rootScope.me.contacts[0] &&
                                                    $rootScope.me.contacts[0].contactType == 'mphone'
                                                ) {
                                                    if ($rootScope.me.contacts[1].contactType == 'phoneWork') {
                                                        // mhpone - 1, phoneWork - 1
                                                        $rootScope.emailTemplateInModal.text =
                                                            $rootScope.emailTemplateInModal.text
                                                                .replace(
                                                                    /\[\[recruiter&#39;s phone\]\]/g,
                                                                    $rootScope.me.contacts[0].value
                                                                        ? $rootScope.me.contacts[0].value
                                                                        : '',
                                                                )
                                                                .replace(
                                                                    /\[\[recruiter's phone\]\]/g,
                                                                    $rootScope.me.contacts[0].value
                                                                        ? $rootScope.me.contacts[0].value
                                                                        : '',
                                                                );
                                                        $rootScope.emailTemplateInModal.text =
                                                            $rootScope.emailTemplateInModal.text
                                                                .replace(
                                                                    /\[\[recruiter&#39;s phone 2\]\]/g,
                                                                    $rootScope.me.contacts[1].value
                                                                        ? $rootScope.me.contacts[1].value
                                                                        : '',
                                                                )
                                                                .replace(
                                                                    /\[\[recruiter's phone 2\]\]/g,
                                                                    $rootScope.me.contacts[1].value
                                                                        ? $rootScope.me.contacts[1].value
                                                                        : '',
                                                                );
                                                    }
                                                    // mhpone - 0, phoneWork - nan
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(
                                                                /\[\[recruiter&#39;s phone\]\]/g,
                                                                $rootScope.me.contacts[0].contactType == 'mphone'
                                                                    ? $rootScope.me.contacts[0].value
                                                                    : '',
                                                            )
                                                            .replace(
                                                                /\[\[recruiter's phone\]\]/g,
                                                                $rootScope.me.contacts[0].value
                                                                    ? $rootScope.me.contacts[0].value
                                                                    : '',
                                                            );
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(
                                                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                                                $rootScope.me.contacts[0].contactType == 'phoneWork'
                                                                    ? $rootScope.me.contacts[0].value
                                                                    : '',
                                                            )
                                                            .replace(
                                                                /\[\[recruiter's phone 2\]\]/g,
                                                                $rootScope.me.contacts[0].contactType == 'phoneWork'
                                                                    ? $rootScope.me.contacts[0].value
                                                                    : '',
                                                            );
                                                } else {
                                                    // mhpone - none, phoneWork - 0
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(
                                                                /\[\[recruiter&#39;s phone\]\]/g,
                                                                $rootScope.me.contacts[0] &&
                                                                    $rootScope.me.contacts[0].contactType == 'mphone'
                                                                    ? $rootScope.me.contacts[0].value
                                                                    : '',
                                                            )
                                                            .replace(
                                                                /\[\[recruiter's phone\]\]/g,
                                                                $rootScope.me.contacts[0] &&
                                                                    $rootScope.me.contacts[0].contactType == 'mphone'
                                                                    ? $rootScope.me.contacts[0].value
                                                                    : '',
                                                            );
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(
                                                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                                                $rootScope.me.contacts[0] &&
                                                                    $rootScope.me.contacts[0].contactType == 'phoneWork'
                                                                    ? $rootScope.me.contacts[0].value
                                                                    : '',
                                                            )
                                                            .replace(
                                                                /\[\[recruiter's phone 2\]\]/g,
                                                                $rootScope.me.contacts[0] &&
                                                                    $rootScope.me.contacts[0].contactType == 'phoneWork'
                                                                    ? $rootScope.me.contacts[0].value
                                                                    : '',
                                                            );
                                                }

                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(
                                                            /\[\[recruiter&#39;s Skype\]\]/g,
                                                            $rootScope.staticEmailTemplate.skype
                                                                ? $rootScope.staticEmailTemplate.skype
                                                                : '',
                                                        )
                                                        .replace(
                                                            /\[\[recruiter's Skype\]\]/g,
                                                            $rootScope.staticEmailTemplate.skype
                                                                ? $rootScope.staticEmailTemplate.skype
                                                                : '',
                                                        );
                                                if (!$rootScope.staticEmailTemplate.skype) {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text.replace(/Skype:/g, '');
                                                }
                                                if ($rootScope.staticEmailTemplate.facebook) {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(
                                                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                                    $rootScope.staticEmailTemplate.facebook +
                                                                    '">' +
                                                                    $rootScope.staticEmailTemplate.facebook +
                                                                    '</a>',
                                                            )
                                                            .replace(
                                                                /\[\[recruiter's Facebook\]\]/g,
                                                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                                    $rootScope.staticEmailTemplate.facebook +
                                                                    '">' +
                                                                    $rootScope.staticEmailTemplate.facebook +
                                                                    '</a>',
                                                            );
                                                } else {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(/\[\[recruiter&#39;s Facebook\]\]/g, '')
                                                            .replace(/\[\[recruiter's Facebook\]\]/g, '');
                                                }
                                                if ($rootScope.staticEmailTemplate.linkedin) {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(
                                                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                                    $rootScope.staticEmailTemplate.linkedin +
                                                                    '">' +
                                                                    $rootScope.staticEmailTemplate.linkedin +
                                                                    '</a>',
                                                            )
                                                            .replace(
                                                                /\[\[recruiter's LinkedIn\]\]/g,
                                                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                                    $rootScope.staticEmailTemplate.linkedin +
                                                                    '">' +
                                                                    $rootScope.staticEmailTemplate.linkedin +
                                                                    '</a>',
                                                            );
                                                } else {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(/\[\[recruiter&#39;s LinkedIn\]\]/g, '')
                                                            .replace(/\[\[recruiter's LinkedIn\]\]/g, '');
                                                }
                                                if ($rootScope.me.emails.length == 1) {
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text.replace(
                                                            /\[\[recruiterEmail\]\]/g,
                                                            $rootScope.me.emails[0].email,
                                                        );
                                                }
                                            },
                                        );
                                    }
                                }, 100);
                            }
                        }
                    };
                } else {
                    $rootScope.changeTemplateInChangeStatusCandidate = (status) => {
                        if (
                            (status && $rootScope.changeStatusOfInterviewInVacancy.status.value == 'interview') ||
                            $rootScope.changeStatusOfInterviewInVacancy.status.withDate ||
                            $rootScope.changeStatusOfInterviewInVacancy.status.value == 'longlist' ||
                            $rootScope.changeStatusOfInterviewInVacancy.status.value == 'shortlist' ||
                            $rootScope.changeStatusOfInterviewInVacancy.status.value == 'notafit' ||
                            $rootScope.changeStatusOfInterviewInVacancy.status.value == 'declinedoffer' ||
                            $rootScope.changeStatusOfInterviewInVacancy.status.value == 'offer_declined' ||
                            $rootScope.changeStatusOfInterviewInVacancy.status.value == 'probation_failure' ||
                            $rootScope.changeStatusOfInterviewInVacancy.status.value == 'no_response' ||
                            $rootScope.changeStatusOfInterviewInVacancy.status.value == 'no_contacts' ||
                            $rootScope.changeStatusOfInterviewInVacancy.status.type == 'interview' ||
                            $rootScope.changeStatusOfInterviewInVacancy.status.type == 'refuse'
                        ) {
                            var templateType = 'candidateCreateInterviewNotification';
                            if (
                                $rootScope.changeStatusOfInterviewInVacancy.status.value == 'notafit' ||
                                $rootScope.changeStatusOfInterviewInVacancy.status.value == 'declinedoffer' ||
                                $rootScope.changeStatusOfInterviewInVacancy.status.value == 'offer_declined' ||
                                $rootScope.changeStatusOfInterviewInVacancy.status.value == 'probation_failure' ||
                                $rootScope.changeStatusOfInterviewInVacancy.status.value == 'no_response' ||
                                $rootScope.changeStatusOfInterviewInVacancy.status.value == 'no_contacts' ||
                                $rootScope.changeStatusOfInterviewInVacancy.status.type == 'refuse'
                            ) {
                                templateType = 'refuseCandidateInVacancy';
                            } else if (
                                $rootScope.changeStatusOfInterviewInVacancy.status.value == 'longlist' ||
                                $rootScope.changeStatusOfInterviewInVacancy.status.value == 'shortlist'
                            ) {
                                templateType = 'seeVacancy';
                            } else if ($rootScope.changeStatusOfInterviewInVacancy.status.value == 'accept_offer') {
                                templateType = 'offerAccepted';
                            }
                            setTimeout(() => {
                                let isGetTemplateForStage =
                                    !$scope.emailAutoActionForStage &&
                                    ($rootScope.changeStatusOfInterviewInVacancy.status.withDate ||
                                        $rootScope.changeStatusOfInterviewInVacancy.status.value == 'interview' ||
                                        $rootScope.changeStatusOfInterviewInVacancy.status.value == 'notafit' ||
                                        $rootScope.changeStatusOfInterviewInVacancy.status.value == 'declinedoffer' ||
                                        $rootScope.changeStatusOfInterviewInVacancy.status.value == 'no_response' ||
                                        $rootScope.changeStatusOfInterviewInVacancy.status.value == 'no_contacts' ||
                                        $rootScope.changeStatusOfInterviewInVacancy.status.type == 'interview' ||
                                        $rootScope.changeStatusOfInterviewInVacancy.status.type == 'refuse') &&
                                    !$scope.isPresent &&
                                    templateType != 'offerAccepted';
                                $rootScope.emailTemplateInModal.title = '';
                                $rootScope.emailTemplateInModal.text = '';
                                if (isGetTemplateForStage) {
                                    Mail.getTemplateForStage(
                                        {
                                            stateId: status.customInterviewStateId
                                                ? status.customInterviewStateId
                                                : status.value,
                                            vacancyId: $rootScope.changedStatusVacancy.vacancyId,
                                            type: templateType,
                                        },
                                        function (data) {
                                            $rootScope.emailTemplateModel = data.object;
                                            $rootScope.emailTemplateModel.label = data.object.type
                                                ? data.object.type
                                                : data.object.name;
                                            $rootScope.tempTemplate = data.object;
                                            $scope.publicLink =
                                                $location.$$protocol +
                                                '://' +
                                                $location.$$host +
                                                '/i/vacancy-' +
                                                $rootScope.changedStatusVacancy.localId;
                                            $rootScope.fileForSave = [];
                                            let publicLink = $scope.publicLink ? $scope.publicLink : '';
                                            let position = $rootScope.changedStatusVacancy.position
                                                ? $rootScope.changedStatusVacancy.position
                                                : '';
                                            $rootScope.emailTemplateInModal = data.object;
                                            $rootScope.emailTemplateInModal.text =
                                                $rootScope.emailTemplateInModal.text.replace(
                                                    /\[\[candidate name\]\]/g,
                                                    $rootScope.candnotify.name.firstName,
                                                );
                                            if ($rootScope.useAmericanNameStyle) {
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text.replace(
                                                        /\[\[full name\]\]/g,
                                                        $rootScope.candnotify.name.fullNameEn,
                                                    );
                                            } else {
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text.replace(
                                                        /\[\[full name\]\]/g,
                                                        $rootScope.candnotify.name.fullName,
                                                    );
                                            }

                                            if (!dataContacts.skype) {
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text.replace(/Skype:/g, '');
                                            }

                                            $rootScope.emailTemplateInModal.text =
                                                $rootScope.emailTemplateInModal.text.replace(
                                                    /\[\[vacancy name\]\]/g,
                                                    '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                        publicLink +
                                                        '">' +
                                                        position +
                                                        '</a>',
                                                );
                                            $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                                                .replace(
                                                    /\[\[recruiter&#39;s name\]\]/g,
                                                    $rootScope.me.fullName ? $rootScope.me.fullName : '',
                                                )
                                                .replace(
                                                    /\[\[recruiter's name\]\]/g,
                                                    $rootScope.useAmericanNameStyle
                                                        ? $rootScope.me.fullNameEn
                                                        : $rootScope.me.fullName,
                                                );
                                            $rootScope.emailTemplateInModal.title =
                                                $rootScope.emailTemplateInModal.title.replace(
                                                    /\[\[vacancy link\]\]/g,
                                                    '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                        publicLink +
                                                        '">' +
                                                        position +
                                                        '</a>',
                                                );
                                            $rootScope.emailTemplateInModal.title =
                                                $rootScope.emailTemplateInModal.title.replace(
                                                    /\[\[vacancy name\]\]/g,
                                                    $rootScope.changedStatusVacancy.position,
                                                );
                                            if (
                                                $rootScope.me.contacts[0] &&
                                                $rootScope.me.contacts[0].contactType == 'mphone'
                                            ) {
                                                if ($rootScope.me.contacts[1].contactType == 'phoneWork') {
                                                    // mhpone - 1, phoneWork - 1
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(
                                                                /\[\[recruiter&#39;s phone\]\]/g,
                                                                $rootScope.me.contacts[0].value
                                                                    ? $rootScope.me.contacts[0].value
                                                                    : '',
                                                            )
                                                            .replace(
                                                                /\[\[recruiter's phone\]\]/g,
                                                                $rootScope.me.contacts[0].value
                                                                    ? $rootScope.me.contacts[0].value
                                                                    : '',
                                                            );
                                                    $rootScope.emailTemplateInModal.text =
                                                        $rootScope.emailTemplateInModal.text
                                                            .replace(
                                                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                                                $rootScope.me.contacts[1].value
                                                                    ? $rootScope.me.contacts[1].value
                                                                    : '',
                                                            )
                                                            .replace(
                                                                /\[\[recruiter's phone 2\]\]/g,
                                                                $rootScope.me.contacts[1].value
                                                                    ? $rootScope.me.contacts[1].value
                                                                    : '',
                                                            );
                                                }
                                                // mhpone - 0, phoneWork - nan
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(
                                                            /\[\[recruiter&#39;s phone\]\]/g,
                                                            $rootScope.me.contacts[0].contactType == 'mphone'
                                                                ? $rootScope.me.contacts[0].value
                                                                : '',
                                                        )
                                                        .replace(
                                                            /\[\[recruiter's phone\]\]/g,
                                                            $rootScope.me.contacts[0].value
                                                                ? $rootScope.me.contacts[0].value
                                                                : '',
                                                        );
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(
                                                            /\[\[recruiter&#39;s phone 2\]\]/g,
                                                            $rootScope.me.contacts[0].contactType == 'phoneWork'
                                                                ? $rootScope.me.contacts[0].value
                                                                : '',
                                                        )
                                                        .replace(
                                                            /\[\[recruiter's phone 2\]\]/g,
                                                            $rootScope.me.contacts[0].contactType == 'phoneWork'
                                                                ? $rootScope.me.contacts[0].value
                                                                : '',
                                                        );
                                            } else {
                                                // mhpone - none, phoneWork - 0
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(
                                                            /\[\[recruiter&#39;s phone\]\]/g,
                                                            $rootScope.me.contacts[0] &&
                                                                $rootScope.me.contacts[0].contactType == 'mphone'
                                                                ? $rootScope.me.contacts[0].value
                                                                : '',
                                                        )
                                                        .replace(
                                                            /\[\[recruiter's phone\]\]/g,
                                                            $rootScope.me.contacts[0] &&
                                                                $rootScope.me.contacts[0].contactType == 'mphone'
                                                                ? $rootScope.me.contacts[0].value
                                                                : '',
                                                        );
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(
                                                            /\[\[recruiter&#39;s phone 2\]\]/g,
                                                            $rootScope.me.contacts[0] &&
                                                                $rootScope.me.contacts[0].contactType == 'phoneWork'
                                                                ? $rootScope.me.contacts[0].value
                                                                : '',
                                                        )
                                                        .replace(
                                                            /\[\[recruiter's phone 2\]\]/g,
                                                            $rootScope.me.contacts[0] &&
                                                                $rootScope.me.contacts[0].contactType == 'phoneWork'
                                                                ? $rootScope.me.contacts[0].value
                                                                : '',
                                                        );
                                            }

                                            $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                                                .replace(
                                                    /\[\[recruiter&#39;s Skype\]\]/g,
                                                    $rootScope.staticEmailTemplate.skype
                                                        ? $rootScope.staticEmailTemplate.skype
                                                        : '',
                                                )
                                                .replace(
                                                    /\[\[recruiter's Skype\]\]/g,
                                                    $rootScope.staticEmailTemplate.skype
                                                        ? $rootScope.staticEmailTemplate.skype
                                                        : '',
                                                );
                                            if (!$rootScope.staticEmailTemplate.skype) {
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text.replace(/Skype:/g, '');
                                            }
                                            if ($rootScope.staticEmailTemplate.facebook) {
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(
                                                            /\[\[recruiter&#39;s Facebook\]\]/g,
                                                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                                $rootScope.staticEmailTemplate.facebook +
                                                                '">' +
                                                                $rootScope.staticEmailTemplate.facebook +
                                                                '</a>',
                                                        )
                                                        .replace(
                                                            /\[\[recruiter's Facebook\]\]/g,
                                                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                                $rootScope.staticEmailTemplate.facebook +
                                                                '">' +
                                                                $rootScope.staticEmailTemplate.facebook +
                                                                '</a>',
                                                        );
                                            } else {
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(/\[\[recruiter&#39;s Facebook\]\]/g, '')
                                                        .replace(/\[\[recruiter's Facebook\]\]/g, '');
                                            }
                                            if ($rootScope.staticEmailTemplate.linkedin) {
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(
                                                            /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                                $rootScope.staticEmailTemplate.linkedin +
                                                                '">' +
                                                                $rootScope.staticEmailTemplate.linkedin +
                                                                '</a>',
                                                        )
                                                        .replace(
                                                            /\[\[recruiter's LinkedIn\]\]/g,
                                                            '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                                                $rootScope.staticEmailTemplate.linkedin +
                                                                '">' +
                                                                $rootScope.staticEmailTemplate.linkedin +
                                                                '</a>',
                                                        );
                                            } else {
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text
                                                        .replace(/\[\[recruiter&#39;s LinkedIn\]\]/g, '')
                                                        .replace(/\[\[recruiter's LinkedIn\]\]/g, '');
                                            }
                                            if ($rootScope.me.emails.length == 1) {
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text.replace(
                                                        /\[\[recruiterEmail\]\]/g,
                                                        $rootScope.me.emails[0].email,
                                                    );
                                            }
                                            if ($rootScope.me.emails.length == 1) {
                                                $rootScope.emailTemplateInModal.text =
                                                    $rootScope.emailTemplateInModal.text.replace(
                                                        /\[\[recruiterEmail\]\]/g,
                                                        $rootScope.me.emails[0].email,
                                                    );
                                            }
                                            if (Email.getDefaultMailbox()) {
                                                $rootScope.emailTemplateInModal.email = Email.getDefaultMailbox();

                                                Email.setDefaultSignature(Email.getDefaultMailbox());
                                            }
                                            if ($rootScope.emailTemplateInModal.filesIdName)
                                                $rootScope.fileForSave = service.filesIdNameToArray(
                                                    $rootScope.emailTemplateInModal.filesIdName,
                                                );
                                        },
                                    );
                                }
                            }, 100);
                        }
                    };
                }

                $rootScope.changeTemplate = function (value) {
                    if (!value) {
                        $rootScope.emailTemplateModel = null;
                        $rootScope.clearFieldsPersonalMailing && $rootScope.clearFieldsPersonalMailing();
                        $rootScope.clearCandidateTemplate();
                        $rootScope.$$phase || $scope.$apply();
                        return;
                    }
                    if (!$rootScope.googleMeet) {
                        $rootScope.tempEmailTemplate = value;
                        $rootScope.emailTemplateModel = value;
                        $rootScope.$$phase || $scope.$apply();
                    }

                    $rootScope.staticEmailTemplate = {
                        candidateName: 'John Dou',
                        date: 1463749200000,
                        recruiterName: $rootScope.me.fullName,
                        recruiterEmail:
                            $rootScope.me.emails && $rootScope.me.emails.length > 0
                                ? $rootScope.me.emails[0].email
                                : $rootScope.me.login,
                    };
                    angular.forEach($rootScope.me.contacts, function (val) {
                        if (val.contactType == 'phoneWork') {
                            $rootScope.staticEmailTemplate.phoneWork = val.value;
                        }
                        if (val.contactType == 'skype') {
                            $rootScope.staticEmailTemplate.skype = val.value;
                        }
                        if (val.contactType == 'linkedin') {
                            $rootScope.staticEmailTemplate.linkedin = val.value;
                        }
                        if (val.contactType == 'facebook') {
                            $rootScope.staticEmailTemplate.facebook = val.value;
                        }
                    });
                    let position = $rootScope.changedStatusVacancy?.position
                        ? $rootScope.changedStatusVacancy?.position
                        : '';
                    let publicLink = $scope.publicLink ? $scope.publicLink : '';
                    if (!$rootScope.googleMeet) {
                        $rootScope.tempEmailTemplate = { ...$rootScope.emailTemplateModel };
                        $rootScope.emailTemplateInModal = $rootScope.emailTemplateModel;
                    }
                    if ($rootScope.candidateTimeChangeStatus) {
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                            /\[\[interview date and time\]\]/g,
                            $rootScope.candidateTimeChangeStatus,
                        );
                    }
                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                        /\[\[candidate name\]\]/g,
                        $rootScope.candnotify.name.firstName,
                    );
                    if ($rootScope.useAmericanNameStyle) {
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                            /\[\[full name\]\]/g,
                            $rootScope.candnotify.name.fullNameEn,
                        );
                    } else {
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                            /\[\[full name\]\]/g,
                            $rootScope.candnotify.name.fullName,
                        );
                    }

                    if (!dataContacts.skype) {
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                            /Skype:/g,
                            '',
                        );
                    }

                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                        /\[\[vacancy name\]\]/g,
                        '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                            publicLink +
                            '">' +
                            position +
                            '</a>',
                    );
                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                        .replace(/\[\[recruiter&#39;s name\]\]/g, $rootScope.me.fullName ? $rootScope.me.fullName : '')
                        .replace(
                            /\[\[recruiter's name\]\]/g,
                            $rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName,
                        );
                    $rootScope.emailTemplateInModal.title = $rootScope.emailTemplateInModal.title.replace(
                        /\[\[vacancy link\]\]/g,
                        '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                            publicLink +
                            '">' +
                            position +
                            '</a>',
                    );
                    $rootScope.emailTemplateInModal.title = $rootScope.emailTemplateInModal.title.replace(
                        /\[\[vacancy name\]\]/g,
                        $rootScope.changedStatusVacancy?.position,
                    );
                    if ($rootScope.me.contacts[0] && $rootScope.me.contacts[0].contactType == 'mphone') {
                        if ($rootScope.me.contacts[1].contactType == 'phoneWork') {
                            // mhpone - 1, phoneWork - 1
                            $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                                .replace(
                                    /\[\[recruiter&#39;s phone\]\]/g,
                                    $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                                )
                                .replace(
                                    /\[\[recruiter's phone\]\]/g,
                                    $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                                );
                            $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                                .replace(
                                    /\[\[recruiter&#39;s phone 2\]\]/g,
                                    $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                                )
                                .replace(
                                    /\[\[recruiter's phone 2\]\]/g,
                                    $rootScope.me.contacts[1].value ? $rootScope.me.contacts[1].value : '',
                                );
                        }
                        // mhpone - 0, phoneWork - nan
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[0].contactType == 'mphone'
                                    ? $rootScope.me.contacts[0].value
                                    : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[0].value ? $rootScope.me.contacts[0].value : '',
                            );
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[0].contactType == 'phoneWork'
                                    ? $rootScope.me.contacts[0].value
                                    : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[0].contactType == 'phoneWork'
                                    ? $rootScope.me.contacts[0].value
                                    : '',
                            );
                    } else {
                        // mhpone - none, phoneWork - 0
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                            .replace(
                                /\[\[recruiter&#39;s phone\]\]/g,
                                $rootScope.me.contacts[0] && $rootScope.me.contacts[0].contactType == 'mphone'
                                    ? $rootScope.me.contacts[0].value
                                    : '',
                            )
                            .replace(
                                /\[\[recruiter's phone\]\]/g,
                                $rootScope.me.contacts[0] && $rootScope.me.contacts[0].contactType == 'mphone'
                                    ? $rootScope.me.contacts[0].value
                                    : '',
                            );
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                            .replace(
                                /\[\[recruiter&#39;s phone 2\]\]/g,
                                $rootScope.me.contacts[0] && $rootScope.me.contacts[0].contactType == 'phoneWork'
                                    ? $rootScope.me.contacts[0].value
                                    : '',
                            )
                            .replace(
                                /\[\[recruiter's phone 2\]\]/g,
                                $rootScope.me.contacts[0] && $rootScope.me.contacts[0].contactType == 'phoneWork'
                                    ? $rootScope.me.contacts[0].value
                                    : '',
                            );
                    }

                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                        .replace(
                            /\[\[recruiter&#39;s Skype\]\]/g,
                            $rootScope.staticEmailTemplate.skype ? $rootScope.staticEmailTemplate.skype : '',
                        )
                        .replace(
                            /\[\[recruiter's Skype\]\]/g,
                            $rootScope.staticEmailTemplate.skype ? $rootScope.staticEmailTemplate.skype : '',
                        );
                    if (!$rootScope.staticEmailTemplate.skype) {
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                            /Skype:/g,
                            '',
                        );
                    }
                    if ($rootScope.staticEmailTemplate.facebook) {
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                            .replace(
                                /\[\[recruiter&#39;s Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.staticEmailTemplate.facebook +
                                    '">' +
                                    $rootScope.staticEmailTemplate.facebook +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's Facebook\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.staticEmailTemplate.facebook +
                                    '">' +
                                    $rootScope.staticEmailTemplate.facebook +
                                    '</a>',
                            );
                    } else {
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                            .replace(/\[\[recruiter&#39;s Facebook\]\]/g, '')
                            .replace(/\[\[recruiter's Facebook\]\]/g, '');
                    }
                    if ($rootScope.staticEmailTemplate.linkedin) {
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                            .replace(
                                /\[\[recruiter&#39;s LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.staticEmailTemplate.linkedin +
                                    '">' +
                                    $rootScope.staticEmailTemplate.linkedin +
                                    '</a>',
                            )
                            .replace(
                                /\[\[recruiter's LinkedIn\]\]/g,
                                '<a style="font-weight: 600; {cursor: pointer;text-decoration: blink;color: #1A6986; text-decoration: none} :hover {text-decoration: underline;}"target="_blank" href="' +
                                    $rootScope.staticEmailTemplate.linkedin +
                                    '">' +
                                    $rootScope.staticEmailTemplate.linkedin +
                                    '</a>',
                            );
                    } else {
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                            .replace(/\[\[recruiter&#39;s LinkedIn\]\]/g, '')
                            .replace(/\[\[recruiter's LinkedIn\]\]/g, '');
                    }
                    if ($rootScope.me.emails.length == 1) {
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                            /\[\[recruiterEmail\]\]/g,
                            $rootScope.me.emails[0].email,
                        );
                    }

                    $rootScope.$$phase || $scope.$apply();
                };

                $rootScope.addEmailFromWhatSend = function (email) {
                    if ($rootScope.emailThatAlreadyUsed) {
                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                            $rootScope.emailThatAlreadyUsed.email,
                            email.email,
                        );
                    }
                    $rootScope.emailTemplateInModal.email = [];
                    $rootScope.emailThatAlreadyUsed = email;
                    localStorage.emailThatAlreadyUsed = email.email;
                    $rootScope.emailTemplateInModal.email = $rootScope.emailTemplateInModal.email + email.email;
                    $rootScope.emailTemplateInModal.text = Email.insertSignatures(
                        $rootScope.emailTemplateInModal.text,
                        email.signature,
                    );
                };
            };
            service.firstLetters = function (string = '') {
                var array = [];
                var word1 = [];
                var word2 = [];
                var words = string !== undefined ? string.split(' ') : '';
                word1.push(words[0]);
                var letter1 = word1[0].split('');
                array.push(letter1[0]);
                if (words.length > 1) {
                    word2.push(words[1]);
                    var letter2 = word2[0].split('');
                    array.push(letter2[0]);
                }
                var acronym = array.join('');
                return acronym;
            };
            service.removeDuplicates = function (arr, prop) {
                var new_arr = [];
                var lookup = {};

                for (var i in arr) {
                    lookup[arr[i][prop]] = arr[i];
                }

                for (i in lookup) {
                    new_arr.push(lookup[i]);
                }
                return new_arr;
            };

            service.getObjectPropertyValueByPath = function (obj = {}, path = '') {
                if (!path) return obj;

                let prevProp = null;

                path.split('.').forEach((prop) => {
                    prevProp = prevProp ? prevProp[prop] : obj[prop];
                });

                return prevProp || prevProp === false ? prevProp : obj;
            };

            service.getObjectPropertyCountPath = function (obj = {}, path = '') {
                if (!obj) return obj;
                obj = obj.count.toString();

                let prevProp = null;

                obj.split('.').forEach((prop) => {
                    prevProp = prevProp ? prevProp[prop] : obj[prop];
                });

                return prevProp || prevProp === false ? prevProp : obj;
            };

            service.copyToClipboard = function (text) {
                if (window.clipboardData && window.clipboardData.setData) {
                    // IE specific code path to prevent textarea being shown while dialog is visible.
                    return clipboardData.setData('Text', text);
                } else if (document.queryCommandSupported && document.queryCommandSupported('copy')) {
                    var textarea = document.createElement('textarea');
                    textarea.textContent = text;
                    textarea.style.position = 'fixed'; // Prevent scrolling to bottom of page in MS Edge.
                    document.body.appendChild(textarea);
                    textarea.select();
                    try {
                        return document.execCommand('copy'); // Security exception may be thrown by some browsers.
                    } catch (ex) {
                        console.warn('Copy to clipboard failed.', ex);
                        return false;
                    } finally {
                        document.body.removeChild(textarea);
                    }
                }
            };

            service.sanitizeStringFromXSS = function (string) {
                return DOMPurify.sanitize(string, { ALLOWED_ATTR: ['href', 'target', 'style', 'id'] });
            };

            service.returnMonthNameFromNumber = function (number) {
                switch (number) {
                    case 1:
                        return $filter('translate')('January');
                    case 2:
                        return $filter('translate')('February');
                    case 3:
                        return $filter('translate')('March');
                    case 4:
                        return $filter('translate')('April');
                    case 5:
                        return $filter('translate')('May');
                    case 6:
                        return $filter('translate')('June');
                    case 7:
                        return $filter('translate')('July');
                    case 8:
                        return $filter('translate')('August');
                    case 9:
                        return $filter('translate')('September');
                    case 10:
                        return $filter('translate')('October');
                    case 11:
                        return $filter('translate')('November');
                    case 12:
                        return $filter('translate')('December');
                }
            };

            service.emailValidation = function (email) {
                let regForValidation =
                    /^[_]?[a-zA-Z0-9]+([\.\-_][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([\.\-_][a-zA-Z0-9]+)*\.[a-zA-Z]{2,10}$$/;
                return regForValidation.test(email);
            };

            //Check all field with specified selectors
            //function([{selector,validatorFunc},...]) => [notValidelement, ...]
            service.fieldsValidation = function (fieldsArray) {
                let notValidFields = [];
                fieldsArray.forEach((field) => {
                    let elem = $(field.selector);
                    if (elem && elem.length === 1) {
                        if (!validateField(field, elem[0].value)) {
                            if (notValidFields.indexOf(elem[0]) === -1) {
                                notValidFields.push(elem[0]);
                                elem.removeClass('valid').addClass('not-valid');
                            }
                        } else {
                            elem.removeClass('not-valid').addClass('valid');
                        }
                    } else {
                        if (elem.length > 1) {
                            $(elem).each((index, oneElem) => {
                                if (!validateField(field, oneElem.value)) {
                                    if (notValidFields.indexOf(oneElem) === -1) {
                                        notValidFields.push(oneElem);
                                        $(oneElem).removeClass('valid').addClass('not-valid');
                                    }
                                } else {
                                    $(oneElem).removeClass('not-valid').addClass('valid');
                                }
                            });
                        }
                    }
                });

                function validateField(field, fieldElemValue) {
                    if (field.validator && typeof field.validator === 'function') {
                        return field.validator.call(null, fieldElemValue);
                    } else {
                        return commonValidation(fieldElemValue);
                    }
                }

                function commonValidation(fieldValue) {
                    return !!(fieldValue && fieldValue.trim().length > 0);
                }

                return notValidFields;
            };

            service.itsMobileOrTablet = function () {
                let value = false;
                const userAgent = navigator.userAgent.toLowerCase();
                const isMobile = /iPhone|Android/i.test(navigator.userAgent);
                const isTablet =
                    /(ipad|tablet|(android(?!.*mobile))|(windows(?!.*phone)(.*touch))|kindle|playbook|silk|(puffin(?!.*(IP|AP|WP))))/.test(
                        userAgent,
                    );

                if (isMobile || isTablet || window.innerWidth < 1000) {
                    value = true;
                }
                return value;
            };

            service.setObjectPropertyValueByPath = function (obj = {}, value, path = '') {
                var path = angular.copy(path);
                var obj = angular.copy(obj);
                path = path.split('.');
                for (let i = 0; i < path.length - 1; i++) {
                    let prop = path[i];
                    if (prop in obj) {
                        obj = obj[prop];
                    } else {
                        obj[prop] = {};
                        obj = obj[prop];
                    }
                }

                obj[path[path.length - 1]] = value;

                return obj;
            };

            service.editorHtmlToPlainText = function (editorHtml) {
                if (typeof editorHtml == 'string' && editorHtml.length > 0) {
                    let htmlElement = $(
                        editorHtml.replace(new RegExp('(</p>)|(<br />)|(<p>&nbsp;</p>)', 'g'), '%0A$1'),
                    );
                    let hrefs = htmlElement.find('a');
                    hrefs.each(function (index) {
                        let oneLink = $(this);
                        let hrefValue = oneLink.attr('href');
                        if (hrefValue !== oneLink.html()) {
                            if (hrefValue.indexOf('#') === 0) {
                                if (index === 0) {
                                    oneLink.html(
                                        '%0A&nbsp;&nbsp;&nbsp;' +
                                            oneLink.html() +
                                            ' (' +
                                            window.location.origin +
                                            window.location.pathname +
                                            hrefValue +
                                            ')%0A',
                                    );
                                } else {
                                    oneLink.html(
                                        oneLink.html() +
                                            ' (' +
                                            window.location.origin +
                                            window.location.pathname +
                                            hrefValue +
                                            ')%0A',
                                    );
                                }
                            } else if (hrefValue.indexOf(/i/) === 0) {
                                oneLink.html(oneLink.html() + ' (' + window.location.origin + hrefValue + ')');
                            } else {
                                oneLink.html(oneLink.html() + ' (' + hrefValue + ')');
                            }
                        }
                    });
                    return htmlElement.text().replace(/&/g, '%26');
                } else {
                    return '';
                }
            };

            service.filesIdNameToArray = function (filesString) {
                try {
                    if (filesString) {
                        let filesObject = JSON.parse(filesString);
                        let fileForSave = [];
                        for (const key in filesObject) {
                            fileForSave.push({
                                fileId: key,
                                fileName: filesObject[key],
                            });
                        }
                        return fileForSave;
                    }
                } catch (e) {
                    console.error('error in parse');
                    console.error('Error in parse filesIdName:', e);
                }
            };

            service.filesIdNameToObjectInString = function (filesArray) {
                let filesObject = {};
                filesArray.forEach((oneFile) => {
                    filesObject[oneFile.fileId] = oneFile.fileName;
                });
                return JSON.stringify(filesObject);
            };

            service.getPluginDownloadingLinkByCurrentLang = function () {
                let downloadingLink = '';
                let detectBrowser = navigator.userAgent.indexOf('Edg') > -1;
                detectBrowser
                    ? (downloadingLink =
                          'https://microsoftedge.microsoft.com/addons/detail/recruiters-integration-t/bhdbljaliojkkiojaoigepeagkdpljga')
                    : (downloadingLink =
                          'https://chrome.google.com/webstore/detail/recruiters-integration-to/komohkkfnbgjojbglkikdfbkjpefkjem?utm_source=chrome-ntp-icon');
                return downloadingLink;
            };

            service.copyToClipboard = function (text) {
                if (window.clipboardData && window.clipboardData.setData) {
                    // IE specific code path to prevent textarea being shown while dialog is visible.
                    return clipboardData.setData('Text', text);
                } else if (document.queryCommandSupported && document.queryCommandSupported('copy')) {
                    var textarea = document.createElement('textarea');
                    textarea.textContent = text;
                    textarea.style.position = 'fixed'; // Prevent scrolling to bottom of page in MS Edge.
                    document.body.appendChild(textarea);
                    textarea.select();
                    try {
                        return document.execCommand('copy'); // Security exception may be thrown by some browsers.
                    } catch (ex) {
                        console.warn('Copy to clipboard failed.', ex);
                        return false;
                    } finally {
                        document.body.removeChild(textarea);
                    }
                }
            };

            service.showCongratulationsHandler = function (dc, hideCongratulation = true) {
                if (compareDateWithNowaday(dc)) {
                    return;
                } else if (hideCongratulation) {
                    return;
                } else {
                    showCongratulations();
                }
            };

            function showCongratulations() {
                class Congratulations {
                    constructor($uibModalInstance, $scope, $uibModal) {
                        this.$uibModalInstance = $uibModalInstance;
                        this.$scope = $scope;
                        this.$uibModal = $uibModal;
                    }

                    closeModal() {
                        this.$uibModalInstance.close();
                    }

                    chooseGiffForShowing() {
                        this.giffToogle = Math.floor(Math.random() * 2);
                    }

                    $onInit() {
                        this.chooseGiffForShowing();
                    }
                }

                const modalInstance = $uibModal.open({
                    templateUrl: './partials/modal/congratulations-modal.html',
                    windowClass: 'congratulations',
                    controller: ['$uibModalInstance', '$scope', '$uibModal', Congratulations],
                    controllerAs: 'vm',
                    resolve: {},
                });
            }

            function compareDateWithNowaday(date) {
                return transformDate(date) === transformDate();
            }

            function transformDate(date) {
                let dateObj = date ? new Date(date) : new Date();
                let month = dateObj.getUTCMonth() + 1; //months from 1-12
                let day = dateObj.getUTCDate();
                let year = dateObj.getUTCFullYear();

                return `${year}/${month}/${day}`;
            }

            service.getClearFieldLength = function (string) {
                if (string && typeof string === 'object') return string.value.replace(/(<([^>]+)>)/gi, '');
                return string.replace(/(<([^>]+)>)/gi, '');
            };
            service.onGetHistory = new PromiseWrapper('history');
            service.onDisable2fa = new PromiseWrapper('disable');
            service.onActivate2fa = new PromiseWrapper('activate');
            service.onDisableForUser = new PromiseWrapper('disableForUser');
            service.onRemoveWallpaper = new PromiseWrapper('removeWallpaper');
            service.onGetDefaultCurrency = new PromiseWrapper('getDefaultCurrency');

            service.createPromiseWrapper = function (service, request) {
                return function (params = null) {
                    return new Promise((resolve, reject) => {
                        service[request](
                            params,
                            (resp) => {
                                if (resp.status === 'ok') {
                                    resolve(resp);
                                } else {
                                    reject(resp);
                                }
                            },
                            (error) => reject(error),
                        );
                    });
                };
            };

            service.createPersonPromiseWrapper = function (service, request) {
                return function (params = null) {
                    return new Promise((resolve, reject) => {
                        service[request](
                            params,
                            (resp) => {
                                if (resp) {
                                    resolve(resp);
                                } else {
                                    reject(resp);
                                }
                            },
                            (error) => reject(error),
                        );
                    });
                };
            };

            /**
             * @param {array} arr The array you want to filter for duplicates
             * @param {array<string>} indexedKeys The keys that form the compound key
             *     which is used to filter duplicates
             * @param {boolean} isPrioritizeFormer Set this to true, if you want to remove
             *     duplicates that occur later, false, if you want those to be removed
             *     that occur later.
             */
            service.distinctArray = function (arr, indexedKeys, isPrioritizeFormer = true) {
                const lookup = new Map();
                const makeIndex = (el) => indexedKeys.reduce((index, key) => `${index};;${el[key]}`, '');
                arr.forEach((el) => {
                    const index = makeIndex(el);
                    if (lookup.has(index) && isPrioritizeFormer) {
                        return;
                    }
                    lookup.set(index, el);
                });

                return Array.from(lookup.values());
            };

            service.requestGetGroups = function (params) {
                return new Promise((resolve, reject) => {
                    service.search(
                        params,
                        (resp) => {
                            resolve(resp);
                        },
                        (error) => reject(error),
                    );
                });
            };

            service.requestGetCompanyGroups = function (params) {
                return new Promise((resolve, reject) => {
                    service.getGroups(
                        params,
                        (resp) => {
                            resolve(resp);
                        },
                        (error) => reject(error),
                    );
                });
            };

            service.onGenerate2faQR = async function () {
                const responce = await fetch(serverAddress + '/2fa/generate', {
                    method: 'GET',
                });

                const img = URL.createObjectURL(await responce.blob());
                const tfaCode = responce.headers.get('tfaCode');

                return { tfaCode, img };
            };

            service.onAddApplicant = function (params) {
                $rootScope.loading = true;
                return new Promise((resolve, reject) => {
                    service.addApplicant(
                        params,
                        (resp) => {
                            resolve(resp);
                        },
                        (error) => reject(error),
                    );
                });
            };

            service.addMeToMeetUsers = (participants, modifier = undefined) => {
                modifier ??= (person) => {
                    return {
                        id: person.userId ? person.userId : person.candidateId,
                        fullName: person.fullName,
                        email: person.login ? person.login : 'no email',
                        isUser: !!person.userId,
                        key: `${person.login ? person.login : ''}-${window.crypto.randomUUID()}`,
                    };
                };

                if (!participants.find((participant) => $rootScope.me.login === participant.email)) {
                    participants.unshift(modifier($rootScope.me));
                }

                return participants;
            };

            service.onGetAllOpenVacancy = new PromiseWrapper('getAllOpenVacancy');
            service.onGetPublicVacancy = new PromiseWrapper('publicVacancy');
            service.onGetLanguages = new PromiseWrapper('getLanguages');
            service.onGetCompanyInformation = new PromiseWrapper('getCompanyInformation');

            service._getLanguagesPending = false;

            /**
             * Retrieves a cached list of languages.
             *
             * Fetches languages from the server and caches it in the $rootScope.
             * If it is already exists in the $rootScope, it returns the cached list.
             * @param params
             * @returns {Promise<Array<Record<LocalesFull, string>>>}
             */
            service.onGetLanguagesCached = async (params) => {
                if (service._getLanguagesPending) {
                    await $rootScope.waitForCondition(200, 100, () => !service._getLanguagesPending);
                    return service.onGetLanguagesCached();
                }

                return new Promise((resolve, reject) => {
                    if ($rootScope.allLanguagesList?.length) {
                        return resolve($rootScope.allLanguagesList);
                    }

                    service._getLanguagesPending = true;
                    service['getLanguages'](
                        params,
                        (resp) => {
                            service._getLanguagesPending = false;
                            if (resp.status === 'ok' && resp.objects?.length) {
                                $rootScope.allLanguagesList = resp.objects;
                                resolve(resp.objects);
                            } else {
                                reject(resp);
                            }
                        },
                        (error) => reject(error),
                    );
                });
            };

            /**
             * Retrieves a cached object of languages.
             *
             * If it is already exists in the $rootScope, it returns the cached obj.
             * Otherwise, it fetches languages from the server and caches it in the $rootScope.
             *
             * @returns {Promise<Record<string, Record<LocalesFull, string>>>} A promise that resolves with the object of languages.
             * @rejects {Object} An error object if the request fails.
             */
            service.onGetLanguagesObjectCached = function () {
                return new Promise((resolve, reject) => {
                    if ($rootScope.allLanguages && Object.keys($rootScope.allLanguages).length) {
                        return resolve($rootScope.allLanguages);
                    }

                    service
                        .onGetLanguagesCached()
                        .then((languages) => {
                            $rootScope.allLanguages = {};

                            languages.forEach((item) => {
                                $rootScope.allLanguages[item.key] = {
                                    russian: item.translation.russian || null,
                                    english: item.translation.english || null,
                                    ukrainian: item.translation.ukrainian || null,
                                    polish: item.translation.polish || null,
                                };
                            });

                            resolve($rootScope.allLanguages);
                        })
                        .catch((error) => {
                            notificationService.error(error);
                            reject(error);
                        });
                });
            };

            service.onGetLanguagesOptionsCached = function () {
                return new Promise((resolve, reject) => {
                    if ($rootScope.languagesOptions?.length) return resolve($rootScope.languagesOptions);

                    service
                        .onGetLanguagesCached()
                        .then((languages) => {
                            const langKey = $rootScope.getCurrentLangKey($rootScope.currentLang);
                            $rootScope.languagesOptions = languages.map((lang) => ({
                                label: $filter('bigFirstLetter')(lang.translation[langKey]),
                                value: lang.translation,
                            }));
                            resolve($rootScope.languagesOptions);
                        })
                        .catch((error) => {
                            notificationService.error(error);
                            reject(error);
                        });
                });
            };

            service.onCheckContactsByProfileId = function (params) {
                $rootScope.loading = true;
                return new Promise((resolve, reject) => {
                    service.checkContactsByProfileId(
                        params,
                        (resp) => {
                            resolve(resp);
                        },
                        (error) => reject(error),
                    );
                });
            };
            service.onGetProfileId = function (params) {
                $rootScope.loading = true;
                return new Promise((resolve, reject) => {
                    service.getProfileId(
                        params,
                        (resp) => {
                            resolve(resp);
                        },
                        (error) => reject(error),
                    );
                });
            };
            service.onSetContactsAndSurname = function (params) {
                $rootScope.loading = true;
                return new Promise((resolve, reject) => {
                    service.setContactsAndSurname(
                        params,
                        (resp) => {
                            resolve(resp);
                        },
                        (error) => reject(error),
                    );
                });
            };

            service.startLoader = function () {
                const requestId = Math.random().toString(36).substring(7);
                $rootScope.loader = true;
                $rootScope.$$phase || $rootScope.$apply();
                return () => {
                    $rootScope.loader = false;
                    $rootScope.$$phase || $rootScope.$apply();
                };
            };
            $rootScope.showErrorExpCreateVacancy = function () {
                showErrorExpCreateVacancy();
            };

            function showErrorExpCreateVacancy() {
                class Success {
                    constructor($uibModalInstance, $scope, $uibModal) {
                        this.$uibModalInstance = $uibModalInstance;
                        this.$scope = $scope;
                        this.$uibModal = $uibModal;
                    }

                    closeModal() {
                        this.$uibModalInstance.close();
                        const experienceElement = document.getElementById('vacancy-page-experience');
                        experienceElement.scrollIntoView({
                            block: 'center',
                            behavior: 'smooth',
                        });
                    }
                }

                const modalInstance = $uibModal.open({
                    templateUrl: './partials/modal/vacancy-error-experience.html',
                    controller: ['$uibModalInstance', '$scope', '$uibModal', Success],
                    backdrop: 'static',
                    controllerAs: 'vm',
                    resolve: {},
                });
            }

            $rootScope.onOpenInstructionPopup = function () {
                if (
                    window.screen.width > 1099 &&
                    window.screen.height > 600 &&
                    ($rootScope.me.orgParams.gamificationEnabled == 'true' ||
                        $rootScope.me.orgParams.gamificationEnabled === undefined)
                ) {
                    showInstructions();
                }
            };

            $rootScope.showSuccessCreateVacancy = function () {
                showSuccessCreateVacancy();
            };

            $rootScope.showSuccessEditVacancy = function () {
                showSuccessEditVacancy();
            };

            $rootScope.showCongratulation = function () {
                if (
                    window.screen.width > 1099 &&
                    window.screen.height > 600 &&
                    ($rootScope.me.orgParams.gamificationEnabled == 'true' ||
                        $rootScope.me.orgParams.gamificationEnabled === undefined)
                ) {
                    showCongratulation();
                }
            };

            $rootScope.showLevel = function () {
                if (
                    window.screen.width > 1099 &&
                    window.screen.height > 600 &&
                    ($rootScope.me.orgParams.gamificationEnabled == 'true' ||
                        $rootScope.me.orgParams.gamificationEnabled === undefined)
                ) {
                    showLevel();
                }
            };

            function showSuccessEditVacancy() {
                if (
                    window.screen.width > 1099 &&
                    window.screen.height > 600 &&
                    ($rootScope.me.orgParams.gamificationEnabled == 'true' ||
                        $rootScope.me.orgParams.gamificationEnabled === undefined)
                ) {
                    class Success {
                        constructor($uibModalInstance, $scope, $uibModal) {
                            this.$uibModalInstance = $uibModalInstance;
                            this.$scope = $scope;
                            this.$uibModal = $uibModal;
                        }

                        closeModal() {
                            this.$uibModalInstance.close();
                        }
                    }

                    const modalInstance = $uibModal.open({
                        templateUrl: './partials/modal/achievements-success-edit.html',
                        windowClass: 'gamification-popup-for-success',
                        controller: ['$uibModalInstance', '$scope', '$uibModal', Success],
                        controllerAs: 'vm',
                        resolve: {},
                    });
                }
            }

            function showSuccessCreateVacancy() {
                if (
                    window.screen.width > 1099 &&
                    window.screen.height > 600 &&
                    ($rootScope.me.orgParams.gamificationEnabled == 'true' ||
                        $rootScope.me.orgParams.gamificationEnabled === undefined)
                ) {
                    class Success {
                        constructor($uibModalInstance, $scope, $uibModal) {
                            this.$uibModalInstance = $uibModalInstance;
                            this.$scope = $scope;
                            this.$uibModal = $uibModal;
                        }

                        closeModal() {
                            this.$uibModalInstance.close();
                        }
                    }

                    const modalInstance = $uibModal.open({
                        templateUrl: './partials/modal/achievements-success.html',
                        windowClass: 'gamification-popup-for-success',
                        controller: ['$uibModalInstance', '$scope', '$uibModal', Success],
                        controllerAs: 'vm',
                        resolve: {},
                    });
                }
            }

            function showCongratulation() {
                class Congratulation {
                    constructor($uibModalInstance, $scope, $uibModal) {
                        this.$uibModalInstance = $uibModalInstance;
                        this.$scope = $scope;
                        this.$uibModal = $uibModal;
                    }

                    closeModal() {
                        this.$uibModalInstance.close();
                    }
                }

                $rootScope.usersMapped = [];
                $rootScope.decUsers = '';
                $rootScope.oneUser = [];

                for (const [key, value] of Object.entries($rootScope.usersCrystalsMap)) {
                    value.forEach((element) => {
                        let decStones = '';
                        if (key) {
                            if (key >= 5) {
                                decStones = 'stones';
                            } else if (key >= 2 && key <= 4) {
                                decStones = 'камня';
                            } else if (key == 1) {
                                decStones = 'камень';
                            }
                        }

                        if (element.userId === $rootScope.me.userId) {
                            $rootScope.oneUser.push({
                                count: key,
                                fullName: element.fullName,
                                userId: element.userId,
                                decStones: decStones,
                            });
                        } else {
                            $rootScope.usersMapped.push({
                                count: key,
                                fullName: $rootScope.useAmericanNameStyle ? element.fullNameEn : element.fullName,
                                userId: element.userId,
                                sex: element.sex,
                                decStones: decStones,
                            });
                        }
                    });
                }

                if ($rootScope.usersMapped[0]) {
                    if ($rootScope.usersMapped.length > 1) {
                        $rootScope.decUsers = 'receive many';
                    } else {
                        if ($rootScope.usersMapped[0].sex) {
                            $rootScope.decUsers = 'receive m';
                        } else {
                            $rootScope.decUsers = 'receive f';
                        }
                    }
                }

                $rootScope.usersMapped.sort((a, b) => b.count - a.count);

                const modalInstance = $uibModal.open({
                    templateUrl: './partials/modal/achievements-congratulation.html',
                    windowClass: 'gamification-popup-for-congratulation close-pop-up',
                    controller: ['$uibModalInstance', '$scope', '$uibModal', Congratulation],
                    controllerAs: 'vm',
                    resolve: {},
                });
            }

            function showLevel() {
                class Level {
                    constructor($uibModalInstance, $scope, $uibModal) {
                        this.$uibModalInstance = $uibModalInstance;
                        this.$scope = $scope;
                        this.$uibModal = $uibModal;
                    }

                    closeModal() {
                        this.$uibModalInstance.close();
                    }
                }

                const modalInstance = $uibModal.open({
                    templateUrl: './partials/modal/achievements-level.html',
                    windowClass: 'gamification-popup-for-level',
                    controller: ['$uibModalInstance', '$scope', '$uibModal', Level],
                    controllerAs: 'vm',
                    resolve: {},
                });
            }

            function showInstructions() {
                class Instructions {
                    constructor($uibModalInstance, $scope, $uibModal) {
                        this.$uibModalInstance = $uibModalInstance;
                        this.$scope = $scope;
                        this.$uibModal = $uibModal;
                    }

                    closeModal() {
                        let data = {
                            name: 'showAchievementsInstruction',
                            userId: $rootScope.me.userId,
                            value: 'false',
                        };
                        fetch(serverAddress + `/person/changeUserParam`, {
                            method: 'POST',
                            body: JSON.stringify(data),
                            headers: {
                                'Content-Type': 'application/json',
                            },
                        });
                        this.$uibModalInstance.close();
                    }

                    $onInit() {
                        this.currentSlide = 1;
                    }
                }

                const modalInstance = $uibModal.open({
                    templateUrl: './partials/modal/achievements-instruction.html',
                    windowClass: 'gamification-popup',
                    controller: ['$uibModalInstance', '$scope', '$uibModal', Instructions],
                    backdrop: true,
                    controllerAs: 'vm',
                    resolve: {},
                });
            }

            $rootScope.showNPS = function () {
                showNPS();
            };

            $rootScope.redirectToVacancy = function () {
                $rootScope.closeModal();
                $location.path('/candidates');
            };

            function showNPS() {
                class NPS {
                    constructor($uibModalInstance, $scope, $uibModal, $rootScope) {
                        this.$uibModalInstance = $uibModalInstance;
                        this.$scope = $scope;
                        this.$uibModal = $uibModal;
                        this.$rootScope = $rootScope;
                    }

                    numberOfQuestion = $rootScope.numberOfQuestionForNPS;
                    rate = null;
                    recommendation = null;
                    opinion = null;

                    setRate(n) {
                        this.rate = n;
                        this.numberOfQuestion = 3;
                    }

                    setRecommendation(n) {
                        this.recommendation = n;
                        this.numberOfQuestion = 2;
                    }

                    sendNPS() {
                        if (this.opinion == null) {
                            this.opinion = 'Без комментариев';
                        }

                        if (this.opinion !== null) {
                            if (this.opinion.length === 0) {
                                this.opinion = 'Без комментариев';
                            }
                        }

                        $rootScope
                            .NpsOnPost(this.rate, this.recommendation, this.opinion)
                            .then()
                            .finally(() => {
                                this.closeModal();
                            });
                    }

                    sendNPSLater() {
                        this.opinion = null;

                        $rootScope
                            .NpsOnPost(this.rate, this.recommendation, this.opinion)
                            .then()
                            .finally(() => {
                                this.closeModal();
                            });
                    }

                    closeModal() {
                        localStorage.setItem('showOnce', 'false');
                        this.$uibModalInstance.close();
                    }
                }

                const modalInstance = $uibModal.open({
                    templateUrl: './partials/modal/nps.html',
                    controller: ['$uibModalInstance', '$scope', '$uibModal', NPS],
                    controllerAs: 'vm',
                    backdrop: 'static',
                    resolve: {},
                });
            }

            function PromiseWrapper(request) {
                return function (params) {
                    return new Promise((resolve, reject) => {
                        service[request](
                            params,
                            (resp) => {
                                if (resp.status === 'ok') {
                                    resolve(resp);
                                } else {
                                    reject(resp);
                                }
                            },
                            (error) => reject(error),
                        );
                    });
                };
            }

            return service;
        },
    ]);
