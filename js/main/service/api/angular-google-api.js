var app = angular.module('googleApi', []).value('version', '0.1');

app.provider('googleService', function () {
    this.configure = function (conf) {
        this.config = conf;
    };

    this.$get = function ($rootScope, $timeout, $location, notificationService, $filter, Person) {
        const config = this.config;
        let socialCode;
        let socialLogin;

        return {
            addCalendar: function (CLIENT_ID) {
                return new Promise(function (resolve, reject) {
                    const google_url =
                        'https://accounts.google.com/o/oauth2/auth' +
                        '?client_id=' +
                        CLIENT_ID +
                        '&scope=https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/calendar.events email profile openid' +
                        '&openid.realm' +
                        '&include_granted_scopes=true' +
                        '&access_type=offline' +
                        '&prompt=select_account' +
                        '&redirect_uri=' +
                        location.protocol +
                        '//' +
                        document.domain +
                        '/white.html' +
                        '&response_type=code token permission';
                    const win = window.open(google_url, 'windowname1', getPopupParams());
                    const pollTimer = window.setInterval(function () {
                        try {
                            if (win.document.URL.indexOf(gup(google_url, 'redirect_uri')) !== -1) {
                                window.clearInterval(pollTimer);
                                const url = win.document.URL;
                                const code = gup(url, 'code');
                                const access_token = gup(url, 'access_token');
                                win.close();
                                if (access_token !== '') {
                                    getUserInfoGoogle(access_token, code);
                                }
                            }
                        } catch (e) {}
                    }, 500);

                    function gup(url, name) {
                        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                        var regexS = '[\\?&#]' + name + '=([^&#]*)';
                        var regex = new RegExp(regexS);
                        var results = regex.exec(url);
                        if (results === null) return '';
                        else return results[1];
                    }

                    function getPopupParams() {
                        var w = 650;
                        var h = 550;
                        var left = screen.width / 2 - w / 2;
                        var top = screen.height / 2 - h / 2;
                        return 'width=' + w + ', height=' + h + ', top=' + top + ', left=' + left;
                    }

                    function getUserInfoGoogle(token, code) {
                        $.ajax({
                            url: 'https://www.googleapis.com/oauth2/v1/userinfo?access_token=' + token,
                            data: null,
                            success: function () {
                                resolve(code);
                            },
                            dataType: 'jsonp',
                        });
                    }
                });
            },
            getCalendar: function () {
                return new Promise(function (resolve, reject) {
                    Person.onGetCalendarList()
                        .then((resp) => {
                            if (resp && resp.objects && resp.objects[0]) {
                                const calendarsInfo = {
                                    calendars: resp.objects,
                                    selectedCalendar: null,
                                };

                                if (calendarsInfo.calendars && calendarsInfo.calendars.length) {
                                    angular.forEach(calendarsInfo.calendars, function (value, key) {
                                        if (value.summary === config.calendarName && value.accessRole === 'owner') {
                                            calendarsInfo.selectedCalendar = value;
                                        }
                                    });

                                    if (calendarsInfo.selectedCalendar) {
                                        if (!$rootScope.$$phase) {
                                            $rootScope.$apply();
                                        }

                                        resolve(calendarsInfo);
                                    } else {
                                        reject('wrong_account');
                                    }
                                } else {
                                    reject();
                                }
                            } else if (resp.status === 'error' || resp.code) {
                                reject('401_error');
                            } else {
                                reject();
                            }
                        })
                        .catch((error) => {
                            reject(error.status);
                        });
                });
            },
            createEvent: function (event) {
                if (event.calendarId != null) {
                    event.id = event.id.replace(/[w-zW-Z_-]/g, '');
                    var object = this;
                    this.getEvent(event.calendarId, event.id, function (val) {});
                }
            },
            gmailAuth: function (type, event, gmailIntegration) {
                var httpTypeUrl = '';
                switch (type) {
                    case 'manageCalendars':
                        httpTypeUrl = 'https://www.googleapis.com/auth/calendar';
                        break;
                    case 'compose':
                        httpTypeUrl = 'https://www.googleapis.com/auth/gmail.compose';
                        break;
                    case 'modify':
                        httpTypeUrl = 'https://www.googleapis.com/auth/gmail.modify';
                        break;
                    case 'readonly':
                        httpTypeUrl = 'https://www.googleapis.com/auth/gmail.readonly';
                        break;
                    case 'fullAccess':
                        httpTypeUrl = 'https://mail.google.com/';
                        break;
                    default:
                        httpTypeUrl = 'https://www.googleapis.com/auth/gmail.readonly';
                        break;
                }

                if (gmailIntegration) {
                    httpTypeUrl =
                        'https://www.googleapis.com/auth/gmail.send+https://www.googleapis.com/auth/gmail.readonly';
                }

                var result = { status: '', code: '', email: '' };
                var google_url =
                    'https://accounts.google.com/o/oauth2/auth' +
                    '?client_id=' +
                    apiKey.google.client_id +
                    '&scope=' +
                    httpTypeUrl +
                    '%20email' +
                    '&state=/profile989a' +
                    '&redirect_uri=' +
                    location.protocol +
                    '//' +
                    document.domain +
                    '/white.html' +
                    '&response_type=code,token' +
                    '&access_type=offline';

                function gup(url, name) {
                    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                    var regexS = '[\\?&]' + name + '=([^&#]*)';
                    var regex = new RegExp(regexS);
                    var results = regex.exec(url);
                    if (results === null) return '';
                    else return results[1];
                }

                function getPopupParams() {
                    var w = 650;
                    var h = 550;
                    var left = screen.width / 2 - w / 2;
                    var top = screen.height / 2 - h / 2;
                    return 'width=' + w + ', height=' + h + ', top=' + top + ', left=' + left;
                }

                var win = window.open(google_url, 'windowname2', getPopupParams());
                var pollTimer = window.setInterval(function () {
                    try {
                        if (win.document.URL.indexOf(gup(google_url, 'redirect_uri')) !== -1) {
                            window.clearInterval(pollTimer);
                            var url = win.document.URL;
                            var code = gup(url, 'code');
                            var access_token = gup(url, 'access_token');
                            win.close();
                            result.code = code;
                            $.ajax({
                                url: 'https://www.googleapis.com/oauth2/v1/userinfo?access_token=' + access_token,
                                data: null,
                                success: function (resp) {
                                    result.status = 'ok';
                                    result.email = resp.email;
                                    result.ownerName = resp.given_name;

                                    return event(result);
                                },
                                error: function () {
                                    result.status = 'error';
                                    return event(result);
                                },
                                dataType: 'jsonp',
                            });
                        }
                    } catch (e) {}
                }, 500);
            },
            signin: function (social) {
                if (social == 'google') {
                    if ($rootScope.authWithExternalService) {
                        signToSystemFromGoogle('google');
                    } else {
                        signInGoogle();
                    }

                    function signInGoogle() {
                        var google_url =
                            'https://accounts.google.com/o/oauth2/auth' +
                            '?client_id=' +
                            apiKey.google.client_id +
                            '&scope=email%20profile' +
                            '&state=/profile' +
                            '&redirect_uri=' +
                            location.protocol +
                            '//' +
                            document.domain +
                            '/white.html' +
                            '&response_type=code%20token';
                        var win = window.open(google_url, 'windowname1', getPopupParams());
                        var pollTimer = window.setInterval(function () {
                            try {
                                if (win.document.URL.indexOf(gup(google_url, 'redirect_uri')) !== -1) {
                                    window.clearInterval(pollTimer);
                                    var url = win.document.URL;
                                    var code = gup(url, 'code');
                                    var access_token = gup(url, 'access_token');
                                    win.close();
                                    if (access_token !== '') {
                                        getUserInfoGoogle(access_token, code);
                                    }
                                }
                            } catch (e) {}
                        }, 500);
                    }
                    function getUserInfoGoogle(token, code) {
                        $.ajax({
                            url: 'https://www.googleapis.com/oauth2/v1/userinfo?access_token=' + token,
                            data: null,
                            success: function (user) {
                                signToSystemFromGoogle('google', code, user.email);
                            },
                            dataType: 'jsonp',
                        });
                    }
                    function signToSystemFromGoogle(social, code, login) {
                        if (code) socialCode = code;
                        if (login) socialLogin = login;

                        var lang = localStorage.getItem('NG_TRANSLATE_LANG_KEY');
                        var userTimeZoneOffset = new Date().getTimezoneOffset();

                        if (!lang) {
                            lang = 'en';
                        }

                        const payload =
                            $rootScope.authWithExternalService && $rootScope.loginForm.authorizationCode
                                ? {
                                      socialKey: code || socialCode,
                                      timeZoneOffset: userTimeZoneOffset,
                                      social: social,
                                      lang: lang,
                                      tfaCode: $rootScope.loginForm.authorizationCode,
                                      login: login || socialLogin,
                                  }
                                : {
                                      socialKey: code || socialCode,
                                      timeZoneOffset: userTimeZoneOffset,
                                      social: social,
                                      lang: lang,
                                      login: login || socialLogin,
                                  };

                        $.ajax({
                            url: '/hr/person/auth',
                            type: 'POST',
                            data: JSON.stringify(payload),
                            contentType: 'application/json; charset=utf-8',
                            dataType: 'json',
                            success: function (data) {
                                localStorage.otherSessionsRemoves = data.object
                                    ? data.object.otherSessionsRemoves
                                    : false;
                                if (data.object && data.object.personId !== undefined) {
                                    location.reload();
                                } else if (data.status === 'error') {
                                    if (data.code === 'tfaCodeIsEmpty') {
                                        $rootScope.showAuthorizationCodeInput = true;
                                        $rootScope.authWithExternalService = social;
                                    } else if (data.code === 'tfaVerificationError') {
                                        $rootScope.errorSignin.mistake = 'Invalid authorization code';
                                        $rootScope.errorSignin.type = 'incorrect';
                                        $('#no-access-id-authorizationCode').css('border-color', 'red');
                                    } else if (data.message == 'unknownEmail') {
                                        $('#no-access-server_message')
                                            .text($filter('translate')(data.message))
                                            .css('color', 'red')
                                            .show();
                                    } else {
                                        notificationService.error(data.message);
                                    }
                                }
                                $rootScope.$$phase || $rootScope.$apply();
                            },
                            error: function (data) {
                                if (data.status === 'error') {
                                    notificationService.error(data.message);
                                }
                            },
                        });
                    }
                    function getPopupParams() {
                        var w = 650;
                        var h = 550;
                        var left = screen.width / 2 - w / 2;
                        var top = screen.height / 2 - h / 2;
                        return 'width=' + w + ', height=' + h + ', top=' + top + ', left=' + left;
                    }
                    function gup(url, name) {
                        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                        var regexS = '[\\?&]' + name + '=([^&#]*)';
                        var regex = new RegExp(regexS);
                        var results = regex.exec(url);
                        if (results === null) return '';
                        else return results[1];
                    }
                } else if (social == 'fb') {
                    if ($rootScope.authWithExternalService) {
                        signInToSystemFromFacebook();
                    } else {
                        signInFacebook();
                    }

                    function signInFacebook() {
                        FB.login(
                            function (response) {
                                if (response.authResponse) {
                                    var access_token = response.authResponse.accessToken; //get access token
                                    getUserInfoFacebook(access_token);
                                }
                            },
                            {
                                scope: 'email',
                            },
                        );
                    }
                    function getUserInfoFacebook(code) {
                        FB.api('/me?fields=email,first_name,last_name', function (user) {
                            signInToSystemFromFacebook(code, user.email);
                        });
                    }
                    function signInToSystemFromFacebook(code, login) {
                        if (code) socialCode = code;
                        if (login) socialLogin = login;

                        var lang = localStorage.getItem('NG_TRANSLATE_LANG_KEY');
                        var userTimeZoneOffset = new Date().getTimezoneOffset();

                        if (!lang) {
                            lang = 'en';
                        }

                        const payload =
                            $rootScope.authWithExternalService && $rootScope.loginForm.authorizationCode
                                ? {
                                      socialKey: code || socialCode,
                                      timeZoneOffset: userTimeZoneOffset,
                                      social: 'facebook',
                                      lang: lang,
                                      tfaCode: $rootScope.loginForm.authorizationCode,
                                      login: login || socialLogin,
                                  }
                                : {
                                      socialKey: code || socialCode,
                                      timeZoneOffset: userTimeZoneOffset,
                                      social: 'facebook',
                                      lang: lang,
                                      login: login || socialLogin,
                                  };

                        $.ajax({
                            url: '/hr/person/auth',
                            type: 'POST',
                            data: JSON.stringify(payload),
                            contentType: 'application/json; charset=utf-8',
                            dataType: 'json',
                            success: function (data) {
                                localStorage.otherSessionsRemoves = data.object
                                    ? data.object.otherSessionsRemoves
                                    : false;
                                if (data.object && data.object.personId !== undefined) {
                                    location.reload();
                                } else if (data.status === 'error') {
                                    if (data.code === 'tfaCodeIsEmpty') {
                                        $rootScope.showAuthorizationCodeInput = true;
                                        $rootScope.authWithExternalService = social;
                                    } else if (data.code === 'tfaVerificationError') {
                                        $rootScope.errorSignin.mistake = 'Invalid authorization code';
                                        $rootScope.errorSignin.type = 'incorrect';
                                        $('#no-access-id-authorizationCode').css('border-color', 'red');
                                    } else {
                                        notificationService.error(data.message);
                                    }
                                }
                                $rootScope.$$phase || $rootScope.$apply();
                            },
                            error: function (data) {
                                if (data.status === 'error') {
                                    notificationService.error(data.message);
                                }
                            },
                        });
                    }
                } else if (social === 'saml') {
                    let respOrgId;

                    $.ajax({
                        url: `/hr/saml/getAuthOrgId?login=${$rootScope.loginForm.login}`,
                        type: 'GET',
                        success: function (data) {
                            if (data.status === 'error') {
                                return notificationService.error(data.message);
                            }
                            respOrgId = data.object;
                            const lang = localStorage.getItem('NG_TRANSLATE_LANG_KEY');

                            window.location.href = `/RecrutSales/saml/login?orgId=${respOrgId}&lang=${lang}`;
                        },
                        error: function (data) {
                            if (data.status === 'error') {
                                notificationService.error(data.message);
                            }
                        },
                    });
                }
            },
        };
    };
});
