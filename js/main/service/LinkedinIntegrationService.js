angular.module('services.LIIntegration', []).factory('LIIntegrationService', [
    '$http',
    '$resource',
    'serverAddress',
    '$rootScope',
    'notificationService',
    '$translate',
    function ($http, $resource, serverAddress, $rootScope, notificationService, $translate) {
        class LIIntegration {
            requests() {
                return $resource(
                    serverAddress + '/linked/:action',
                    { action: '@action' },
                    {
                        apiPostVacancy: {
                            method: 'GET',
                            params: {
                                action: 'apiPostVacancy',
                            },
                        },
                        removeToken: {
                            method: 'GET',
                            params: {
                                action: 'removeToken',
                            },
                        },
                        integrate: {
                            method: 'GET',
                            params: {
                                action: 'integrate',
                            },
                        },
                    },
                );
            }
            onDeactivateLinkedinIntegration() {
                return new Promise((resolve, reject) => {
                    this.requests().removeToken(
                        (resp) => {
                            resp.status === 'ok' ? resolve(resp) : reject(resp);
                        },
                        (error) => reject(error),
                    );
                });
            }
            shareVacancy(vId) {
                $rootScope.loading = true;
                this.requests().integrate(
                    { vacancyId: vId },
                    (resp) => {
                        $rootScope.loading = false;
                        if (resp.status === 'ok' && resp.object) {
                            const redirectData = resp.object;
                            const el = document.createElement('a');
                            el.href = LIIntegration.getLILink(redirectData.redirectUrl, redirectData.state);
                            el.target = '_blank';
                            el.click();
                        } else if (resp.status === 'vacancyPosted') {
                            notificationService.success($translate.instant('Vacancy posted on your LinkedIn'));
                        }
                    },
                    (err) => {
                        $rootScope.loading = false;
                        $scope.apply();
                    },
                );
            }
            integrate() {
                $rootScope.loading = true;
                return new Promise((resolve, reject) => {
                    this.requests().integrate(
                        (resp) => {
                            resp.status ? resolve(resp) : reject(resp);
                        },
                        (err) => reject(err),
                    );
                })
                    .then((resp) => {
                        if (resp.status === 'ok' && resp.object) {
                            const redirectData = resp.object;
                            const el = document.createElement('a');
                            el.href = LIIntegration.getLILink(redirectData.redirectUrl, redirectData.state);
                            el.target = '_blank';
                            el.click();
                        }
                    })
                    .catch((resp) => {
                        console.error(resp.message);
                    })
                    .finally(() => {
                        $rootScope.loading = false;
                        $rootScope.$apply();
                    });
            }
            static getLILink(redirectUrl, state) {
                return `https://www.linkedin.com/oauth/v2/authorization?client_id=${
                    apiKey.linkedIn.api_key
                }&redirect_uri=${encodeURI(
                    redirectUrl,
                )}&response_type=code&scope=r_liteprofile%20r_emailaddress%20w_member_social&state=${state}`;
            }
        }

        return new LIIntegration();
    },
]);
