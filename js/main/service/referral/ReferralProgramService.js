angular.module('services.referralProgram', []).factory('ReferralProgram', [
    '$resource',
    'serverAddress',
    '$uibModal',
    '$rootScope',
    '$state',
    'notificationService',
    '$translate',
    '$q',
    '$filter',
    'Service',
    function (
        $resource,
        serverAddress,
        $uibModal,
        $rootScope,
        $state,
        notificationService,
        $translate,
        $q,
        $filter,
        Service,
    ) {
        const contentType = {
            'Content-type': 'application/json; charset=UTF-8',
        };
        const service = $resource(
            serverAddress + '/referral/:action',
            { action: '@action' },
            {
                getReferralInfo: {
                    method: 'GET',
                    headers: contentType,
                    params: {
                        action: 'getReferralInfo',
                    },
                },
                checkDiscount: {
                    method: 'GET',
                    headers: contentType,
                    params: {
                        action: 'checkFirstLoginByReferralLink',
                    },
                },
                checkBonus: {
                    method: 'GET',
                    headers: contentType,
                    params: {
                        action: 'checkBonus',
                    },
                },
                getEarnings: {
                    method: 'POST',
                    headers: contentType,
                    params: {
                        action: 'getEarnings',
                    },
                },
                getWithdrawalHistory: {
                    method: 'GET',
                    headers: contentType,
                    params: {
                        action: 'getWithdrawalHistory',
                    },
                },
                getTotalReward: {
                    method: 'GET',
                    headers: contentType,
                    params: {
                        action: 'getTotalReward',
                    },
                },
                withdrawReward: {
                    method: 'POST',
                    headers: contentType,
                    params: {
                        action: 'withdrawReward',
                    },
                },
                topUpAccount: {
                    method: 'POST',
                    headers: contentType,
                    params: {
                        action: 'topUpAccount',
                    },
                },
            },
        );

        service.addRefLinks = function (referralData, lang) {
            let extPageUrl = '/signup.html?aff_code=';
            if (lang === 'ru') {
                extPageUrl = '/ru/signup.html?aff_code=';
            }

            if (lang === 'ua') {
                extPageUrl = '/ua/signup.html?aff_code=';
            }

            if (lang === 'pl') {
                extPageUrl = '/pl/signup.html?aff_code=';
            }

            referralData.link = location.origin + extPageUrl + referralData.originLink;
            referralData.template =
                referralData.originTemplate +
                `<p><a href="${referralData.link}" target="_blank">${referralData.link}</a></p>`;
            return referralData;
        };

        service.getReferralInfoProcessed = function () {
            $rootScope.loading = true;
            return new $q((resolve, reject) => {
                service.getReferralInfo(
                    (resp) => {
                        if (resp.status !== 'error') {
                            resp.object.originLink = resp.object.link;
                            resp.object.originTemplate = resp.object.template;
                            const refData = service.addRefLinks(resp.object, $rootScope.currentLang);

                            refData.myEmails = [];
                            $rootScope.emailTemplateInModal = $rootScope.emailTemplateInModal || {
                                email: '',
                            };
                            if ($rootScope.me.emails) {
                                $rootScope.me.emails.filter((emailObj) => {
                                    if (emailObj.personalMailing) {
                                        refData.myEmails.push(emailObj);
                                        return true;
                                    }
                                    return false;
                                });
                            }
                            if (refData.myEmails && refData.myEmails[0])
                                refData.emailSender = refData.myEmails[0].email;
                            else refData.emailSender = '';

                            resolve(refData);
                        } else {
                            notificationService.error(resp.message);
                            reject(resp);
                        }
                        $rootScope.loading = false;
                    },
                    (error) => {
                        $rootScope.loading = false;
                        notificationService.error(`GetRferralInfo response status: ${error.status}`);
                        reject(error);
                    },
                );
            });
        };

        service.getReferralPopups = function (me) {
            if (me.orgParams.referralPopup === 'Y') {
                service.checkDiscount((resp) => {
                    if (resp.status === 'ok') showDiscountPopup(resp.object);
                });
            } else {
                service.checkBonus((resp) => {
                    if (resp.object) showBonusPopup(JSON.parse(resp.object));
                });
            }
        };

        service.getEarningsTableData = function (earningsParams) {
            return new Promise((resolve, reject) => {
                $rootScope.loading = true;
                Promise.all([getPromise('getEarnings', earningsParams), getPromise('getWithdrawalHistory')])
                    .then(
                        ([earnings, history]) => {
                            $rootScope.loading = false;
                            const tableData = addHistoryToEarnings(earnings, history);
                            resolve(tableData);
                        },
                        (reason) => {
                            $rootScope.loading = false;
                            reject(reason);
                        },
                    )
                    .catch((error) => {
                        $rootScope.loading = false;
                        reject(error);
                    });
            });
        };

        service.emailValidation = function (email) {
            const regForValidation =
                /^[_]?[a-zA-Z0-9]+([\.\-_][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([\.\-_][a-zA-Z0-9]+)*\.[a-zA-Z]{2,10}$$/;
            return regForValidation.test(email);
        };

        function getPromise(requestName, params) {
            return new Promise((resolve, reject) => {
                service[requestName](
                    params,
                    (resp) => {
                        resolve(resp);
                    },
                    (error) => {
                        reject(error);
                    },
                );
            });
        }

        function addHistoryToEarnings(earnings, history) {
            let withdrawAvailable = false;

            earnings.object.forEach((oneEarning) => {
                oneEarning.reward = oneEarning.reward / 10000;
                if (oneEarning.reward && oneEarning.status === 'A' && oneEarning.firstPaymant) withdrawAvailable = true;
                if (history.object && history.object.length) {
                    history.object.forEach((oneHist) => {
                        if (oneEarning.id === oneHist.referralInfoIds) {
                            if (oneHist.type === 'pay') oneEarning.withdrawDate = oneHist.confirmDate;
                            else oneEarning.withdrawDate = oneHist.dc;
                            oneEarning.withdrawType = oneHist.type;
                            oneEarning.withdrawStatus = oneHist.status;
                        }
                    });
                }
            });

            earnings.withdrawAvailable = withdrawAvailable;
            return earnings;
        }

        function showBonusPopup(bonusInfo) {
            const bonusParams = {
                companyName: bonusInfo.orgName,
                companyPayment: bonusInfo.paymentSize,
                availableBonus: bonusInfo.bonusSize,
            };
            class getBonusController {
                constructor($uibModalInstance, bonusParams) {
                    this.$uibModalInstance = $uibModalInstance;
                    this.bonusParams = bonusParams;
                }
                closeModal() {
                    this.$uibModalInstance.close();
                }
                getBonus() {
                    $rootScope.activePage = 'Referral program';
                    $rootScope.navName = null;
                    $state.go('referral.earnings');
                }
            }
            $rootScope.modalInstance = $uibModal.open({
                templateUrl: 'partials/modal/referral-program/get-bonus.html',
                controller: ['$uibModalInstance', 'bonusParams', getBonusController],
                controllerAs: 'vm',
                backdrop: 'static',
                windowClass: 'referral-bonus-popup',
                resolve: {
                    bonusParams: function () {
                        return bonusParams;
                    },
                },
            });
            $rootScope.closeModal = function () {
                $rootScope.modalInstance.close();
            };
            $rootScope.modalInstance.closed.then(() => {
                service.checkBonus({ param: true }, (resp) => {});
            });
        }
        $rootScope.closeModal = function () {
            $rootScope.modalInstance.close();
        };
        function showDiscountPopup(dc) {
            const discountEndDate = $filter('dateFormatShort')(dc + 1209600000);
            $rootScope.modalInstance = $uibModal.open({
                templateUrl: 'partials/modal/referral-program/get-discount.html',
                controller: ['discountEndDate', getDiscountController],
                controllerAs: 'vm',
                backdrop: 'static',
                windowClass: 'referral-discount-popup',
                resolve: {
                    discountEndDate: function () {
                        return discountEndDate;
                    },
                },
            });
            $rootScope.modalInstance.closed.then(() => {
                service.checkDiscount({ param: true });
            });
            function getDiscountController(discountEndDate) {
                this.discountEndDate = discountEndDate;
                this.goToPayment = function () {
                    $rootScope.modalInstance.close();
                    $state.go('pay');
                };
            }
        }

        return service;
    },
]);
