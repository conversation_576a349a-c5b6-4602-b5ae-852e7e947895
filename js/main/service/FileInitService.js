angular.module('services.fileinit', ['services.candidate']).factory('FileInit', [
    'serverAddress',
    '$http',
    'Candidate',
    'notificationService',
    '$rootScope',
    '$filter',
    '$translate',
    '$location',
    function (serverAddress, $http, Candidate, notificationService, $rootScope, $filter, $translate, $location) {
        let fileUploadingQueue = [];

        function fileUploadWithQueue(uri, file, settings, $scope, path) {
            fileUploadingQueue.push({
                uri: uri,
                file: file,
                settings: settings,
                scopeFIle: $scope.file,
                path: path,
            });
            if (fileUploadingQueue.length === 1) {
                fileUpload($scope, settings);
            }
        }

        function fileUploadWithQueuePublicVacancy(uri, file, settings, $scope, path) {
            fileUploadingQueue.push({
                uri: uri,
                file: file,
                settings: settings,
                scopeFIle: $scope.file,
                path: path,
            });
            if (fileUploadingQueue.length === 1) {
                fileUploadPublicVacancy($scope, settings);
            }
        }

        function fileUpload($scope, settings) {
            $rootScope.loading = true;
            fileUploadingQueue[0].file
                .$upload(
                    fileUploadingQueue[0].uri,
                    fileUploadingQueue[0].scopeFIle,
                    fileUploadingQueue[0].settings,
                    $scope,
                )
                .then(function (data) {
                    $rootScope.loading = false;
                    var resp = JSON.parse(data.response);
                    if (data.status == 200 && resp.status != 'error') {
                        if ($scope.callbackFile != undefined) {
                            $scope.callbackFile(data.data.objects[0], fileUploadingQueue[0].scopeFIle.filename);
                            if (fileUploadingQueue.length === 1) {
                                if (fileUploadingQueue[0].path === 'candidate' && $scope.updateCandidate) {
                                    $scope.updateCandidate();
                                } else if (fileUploadingQueue[0].path === 'client' && $scope.updateClient) {
                                    $scope.updateClient();
                                }
                            }
                            new PNotify({
                                styling: 'jqueryui',
                                type: 'success',
                                text: $filter('translate')('history_info.added_file'),
                            });
                        }
                    } else if (resp.status == 'error') {
                        $rootScope.loading = false;
                        notificationService.error(resp.message);
                        if ($scope.callbackFileError != undefined) {
                            $scope.callbackFileError('error');
                        }
                    }
                    fileUploadingQueue.shift();
                    if (fileUploadingQueue.length > 0) fileUpload($scope);
                    $('#file').val('');
                })
                .catch(function (data) {
                    $rootScope.loading = false;

                    if (data && data.response) {
                        if (data.response[0].code === 'type') {
                            new PNotify({
                                styling: 'jqueryui',
                                type: 'error',
                                text:
                                    $filter('translate')('Allowed file formats') +
                                    ': ' +
                                    settings.allowedType.join(', '),
                            });
                        }
                        if (data.response[0].code === 'size') {
                            new PNotify({
                                styling: 'jqueryui',
                                type: 'error',
                                text: $filter('translate')('big-file-size'),
                            });
                        }
                    }
                    fileUploadingQueue.shift();
                    if (fileUploadingQueue.length > 0) fileUpload($scope);
                    $('#file').val('');
                });
        }
        function fileUploadPublicVacancy($scope, settings) {
            $rootScope.loading = true;
            fileUploadingQueue[0].file
                .$upload(
                    fileUploadingQueue[0].uri,
                    fileUploadingQueue[0].scopeFIle,
                    fileUploadingQueue[0].settings,
                    $scope,
                )
                .then(function (data) {
                    $rootScope.loading = false;
                    var resp = JSON.parse(data.response);
                    if (resp.status === 'forbidden') {
                        notificationService.error($translate.instant('limit_10_attachments'));
                    }
                    if (resp.code === 'removeWrongElementsFromFile') {
                        notificationService.error($translate.instant('removeWrongElementsFromFile'));
                    }
                    if (data.status === 200 && resp.status !== 'error') {
                        if ($scope.callbackFile !== undefined) {
                            $scope.callbackFile(data.data.objects[0], fileUploadingQueue[0].scopeFIle.filename);
                            if (fileUploadingQueue.length === 1) {
                                if (fileUploadingQueue[0].path === 'candidate' && $scope.updateCandidate) {
                                    $scope.updateCandidate();
                                } else if (fileUploadingQueue[0].path === 'client' && $scope.updateClient) {
                                    $scope.updateClient();
                                }
                            }
                        }
                    } else if (resp.status == 'error' && resp.code != 'removeWrongElementsFromFile') {
                        $rootScope.loading = false;
                        notificationService.error(resp.message);
                        if ($scope.callbackFileError != undefined) {
                            $scope.callbackFileError('error');
                        }
                    }
                    fileUploadingQueue.shift();
                    if (fileUploadingQueue.length > 0) fileUpload($scope);
                    $('#file').val('');
                })
                .catch(function (data) {
                    $rootScope.loading = false;

                    if (data && data.response) {
                        if (data.response[0].code === 'type') {
                            new PNotify({
                                styling: 'jqueryui',
                                type: 'error',
                                text:
                                    $filter('translate')('Allowed file formats') +
                                    ': ' +
                                    settings.allowedType.join(', '),
                            });
                        }
                        if (data.response[0].code === 'size') {
                            new PNotify({
                                styling: 'jqueryui',
                                type: 'error',
                                text: $filter('translate')('big-file-size'),
                            });
                        }
                    }
                    fileUploadingQueue.shift();
                    if (fileUploadingQueue.length > 0) fileUpload($scope);
                    $('#file').val('');
                });
        }

        return {
            initFileOption: function ($scope, path, setings, $filter) {
                $scope.file = {}; //Model
                $scope.options = {
                    change: function (file) {
                        $rootScope.loading = true;
                        var uri = serverAddress;
                        if (path != undefined) uri = uri + '/' + path;
                        uri = uri + '/addFile';
                        if ($scope.objectId != undefined) uri = uri + '/' + $scope.objectId;
                        $scope.fileIsSelected = true;
                        $scope.ngShowOldFile = false;
                        file.$preview(file).then(function (data) {
                            /** @namespace data.item.thumb */
                            $scope.newImgSrc = data.item.thumb;
                            $scope.fileName = data.item.filename;
                            $scope.ngShowNewImage = true;
                        });
                        fileUploadWithQueue(uri, file, setings, $scope, path);
                    },
                };
            },
            initFileOptionForPublicVacancy: function ($scope, path, setings, $filter) {
                $scope.file = {}; //Model
                $scope.options = {
                    change: function (file) {
                        $rootScope.loading = true;
                        var uri = serverAddress;
                        if (path != undefined) uri = uri + '/' + path;
                        uri = uri + '/addCvFile';
                        if ($scope.objectId != undefined) uri = uri + '/' + $scope.objectId;
                        $scope.fileIsSelected = true;
                        $scope.ngShowOldFile = false;
                        file.$preview(file).then(function (data) {
                            /** @namespace data.item.thumb */
                            $scope.newImgSrc = data.item.thumb;
                            $scope.fileName = data.item.filename;
                            $scope.ngShowNewImage = true;
                        });
                        fileUploadWithQueuePublicVacancy(uri, file, setings, $scope, path);
                    },
                };
            },
            initFileVacancy: function ($scope, path, $filter, setings) {
                $scope.fileForVacancy = {}; //Model
                $scope.optionsForVacancy = {
                    change: function (file) {
                        $rootScope.loading = true;
                        var uri = serverAddress;
                        if (path != undefined) uri = uri + '/' + path;
                        uri = uri + '/addFile';
                        if ($scope.objectId != undefined) uri = uri + '/' + $scope.objectId;
                        $scope.fileIsSelected = true;
                        $scope.ngShowOldFile = false;
                        file.$preview(file).then(function (data) {
                            /** @namespace data.item.thumb */
                            $scope.newImgSrc = data.item.thumb;
                            $scope.fileName = data.item.filename;
                            $scope.ngShowNewImage = true;
                        });
                        file.$upload(uri, $scope.fileForVacancy, setings, $scope)
                            .then(function (data) {
                                $rootScope.loading = false;
                                var resp = JSON.parse(data.response);

                                if (data.status == 200 && resp.status != 'error') {
                                    if ($scope.callbackFile) {
                                        $scope.callbackFile(data.data.objects[0], $scope.fileForVacancy.filename);
                                        new PNotify({
                                            styling: 'jqueryui',
                                            type: 'success',
                                            text: $filter('translate')('history_info.added_file'),
                                        });
                                        $rootScope.loading = false;
                                    }
                                } else if (resp.status == 'error') {
                                    notificationService.error(resp.message);
                                    $rootScope.loading = false;
                                    if ($scope.callbackFileError != undefined) {
                                        $scope.callbackFileError('error');
                                    }
                                }
                            })
                            .catch(function (data) {
                                $rootScope.loading = false;

                                if (data.response[0].code == 'type') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text:
                                            $filter('translate')('Allowed file formats') +
                                            ': ' +
                                            setings.allowedType.join(', '),
                                    });
                                }
                                if (data.response[0].code == 'size') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: $filter('translate')('big-file-size'),
                                    });
                                }
                            });
                    },
                };
            },

            initFileCandidatePersonalMailing: function ($scope, $filter, settings) {
                $scope.filesForEmal = {};
                $scope.optionsForEmail = {
                    change: function (file) {
                        const extensions = ['.exe', '.ini', '.bin', '.lnk'];
                        $rootScope.loading = true;
                        if (
                            file.size < 5242880 &&
                            !extensions.some((extension) => file.filename.indexOf(extension) !== -1)
                        ) {
                            let uri = serverAddress;

                            const tempFile =
                                $location.$$url.includes('/email/vacancy') || $location.$$url.includes('/clients');

                            uri = uri + `/addFileForEmail?tempFile=${tempFile}`;

                            $scope.fileIsSelected = true;
                            $scope.ngShowOldFile = false;
                            file.$preview(file).then(function (data) {
                                $scope.newImgSrc = data.item.thumb;
                                $scope.fileName = data.item.filename;
                                $scope.ngShowNewImage = true;
                            });
                            file.$upload(uri, $scope.filesForEmal, settings, $scope).then(
                                function (data) {
                                    $rootScope.loading = false;
                                    let resp = JSON.parse(data.response);

                                    if (resp.status !== 'error') {
                                        if ($scope.callbackFile) {
                                            const isUnique = $scope.callbackFile(
                                                data.data.objects[0],
                                                $scope.filesForEmal.filename,
                                            );
                                            if (isUnique) {
                                                new PNotify({
                                                    styling: 'jqueryui',
                                                    type: 'success',
                                                    text: $filter('translate')('history_info.added_file'),
                                                });
                                            } else {
                                                new PNotify({
                                                    styling: 'jqueryui',
                                                    type: 'error',
                                                    text: $filter('translate')('history_info.duplicated_file'),
                                                });
                                            }
                                            $rootScope.loading = false;
                                        }
                                    } else if (resp.status === 'error') {
                                        if (resp.message === 'tooLargeUpload5mb') {
                                            notificationService.error(
                                                $filter('translate')('Max. size of 1 file is 5mb'),
                                            );
                                        } else if (
                                            resp.message === 'Invalid file extension' ||
                                            resp.message === 'invalidFileExtension'
                                        ) {
                                            notificationService.error($filter('translate')('Invalid file type'));
                                        } else {
                                            notificationService.error(resp.message);
                                        }
                                        $rootScope.loading = false;
                                        if ($scope.callbackFileError) {
                                            $scope.callbackFileError('error');
                                        }
                                    }
                                },
                                (error) => {
                                    $rootScope.loading = false;
                                    notificationService.error(error.response[0].msg);
                                },
                            );
                        } else {
                            $rootScope.loading = false;
                            if (file.size > 5242880) {
                                notificationService.error($filter('translate')('Max. size of 1 file is 5mb'));
                            } else {
                                notificationService.error($filter('translate')('Invalid file type'));
                            }
                        }
                    },
                };
            },
            initCandFileOption: function ($scope, path, setings, toSave, $filter) {
                $scope.file = {}; //Model
                $scope.options = {
                    change: function (file) {
                        var uri = serverAddress;
                        if (path != undefined) uri = uri + '/' + path;
                        uri = uri + '/addFile';
                        if ($scope.objectId != undefined) uri = uri + '/' + $scope.objectId;
                        $scope.fileIsSelected = true;
                        $scope.ngShowOldFile = false;
                        file.$preview(file).then(function (data) {
                            $scope.newImgSrc = data.item.thumb;
                            $scope.fileName = data.item.filename;
                            $scope.ngShowNewImage = true;
                        });
                        file.$upload(uri, $scope.file, setings, $scope)
                            .then(function (data) {
                                $rootScope.loading = false;
                                if (data.data.status == 'ok') {
                                    if ($scope.callbackFile != undefined) {
                                        $scope.callbackFile(data.data.objects[0], $scope.file.filename);
                                        new PNotify({
                                            styling: 'jqueryui',
                                            type: 'success',
                                            text: $filter('translate')('history_info.added_file'),
                                        });
                                    }
                                    $scope.fastCandAttachProcessId = data.data.objects[0];
                                    $scope.fastCandAttachProcess = true;
                                } else {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: data.data.message,
                                    });
                                }
                            })
                            .catch(function (data) {
                                $rootScope.loading = false;
                                if (data.response[0].code == 'type') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: 'Возможные форматы файла: ' + setings.allowedType.join(', '),
                                    });
                                }
                                if (data.response[0].code == 'size') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: $filter('translate')('big-file-size'),
                                    });
                                }
                            });
                    },
                };
            },
            initVacancyTemplateInCandidateFileOption: function ($scope, $rootScope, path, setings, toSave, $filter) {
                $scope.file = {}; //Model
                $rootScope.optionsTemplate = {
                    change: function (file) {
                        var uri = serverAddress;
                        uri = uri + '/addFile';
                        if ($scope.fileId != undefined) uri = uri + '/' + $scope.objectId;
                        $scope.fileIsSelected = true;
                        $scope.ngShowOldFile = false;
                        file.$preview(file).then(function (data) {
                            $scope.newImgSrc = data.item.thumb;
                            $scope.fileName = data.item.filename;
                            $scope.ngShowNewImage = true;
                        });
                        file.$upload(uri, $scope.file, setings, $scope)
                            .then(function (data) {
                                $rootScope.loading = false;
                                if (data.data.status == 'ok') {
                                    if ($scope.callbackFileTemplateInCandidate != undefined) {
                                        $scope.callbackFileTemplateInCandidate(
                                            data.data.objects[0],
                                            $scope.file.filename,
                                        );
                                        new PNotify({
                                            styling: 'jqueryui',
                                            type: 'success',
                                            text: $filter('translate')('history_info.added_file'),
                                        });
                                    }
                                    $scope.fastCandAttachProcessId = data.data.objects[0];
                                    $scope.fastCandAttachProcess = true;
                                } else {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: data.data.message,
                                    });
                                }
                            })
                            .catch(function (data) {
                                $rootScope.loading = false;
                                if (data.response[0].code == 'type') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: 'Возможные форматы файла: ' + setings.allowedType.join(', '),
                                    });
                                }
                                if (data.response[0].code == 'size') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: $filter('translate')('big-file-size'),
                                    });
                                }
                            });
                    },
                };
            },
            initVacancyTemplateFileOption: function ($scope, path, setings, toSave, $filter) {
                $scope.file = {}; //Model
                $scope.optionsForTemplate = {
                    change: function (file) {
                        var uri = serverAddress;
                        if (path != undefined) uri = uri + '/' + path;

                        const tempFile =
                            $location.$$url.includes('/email/vacancy') || $location.$$url.includes('/clients');

                        uri = uri + `/addFileForEmail?tempFile=${tempFile}`;

                        if ($scope.fileId != undefined) uri = uri + '/' + $scope.objectId;
                        $scope.fileIsSelected = true;
                        $scope.ngShowOldFile = false;
                        file.$preview(file).then(function (data) {
                            $scope.newImgSrc = data.item.thumb;
                            $scope.fileName = data.item.filename;
                            $scope.ngShowNewImage = true;
                        });
                        $rootScope.loading = true;
                        file.$upload(uri, $scope.file, setings, $scope)
                            .then(function (data) {
                                $rootScope.loading = false;
                                if (data.data.status == 'ok') {
                                    if ($scope.callbackFileForTemplate != undefined) {
                                        $scope.callbackFileForTemplate(data.data.objects[0], $scope.file.filename);
                                        new PNotify({
                                            styling: 'jqueryui',
                                            type: 'success',
                                            text: $filter('translate')('history_info.added_file'),
                                        });
                                    }
                                    $scope.fastCandAttachProcessId = data.data.objects[0];
                                    $scope.fastCandAttachProcess = true;
                                } else {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: data.data.message,
                                    });
                                }
                            })
                            .catch(function (data) {
                                $rootScope.loading = false;
                                if (data.response[0].code == 'type') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: 'Возможные форматы файла: ' + setings.allowedType.join(', '),
                                    });
                                }
                                if (data.response[0].code == 'size') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: $filter('translate')('big-file-size'),
                                    });
                                }
                            });
                    },
                };
            },
            initVacancyTemplateInCandidateFileOption: function ($scope, $rootScope, path, setings, toSave, $filter) {
                $scope.file = {}; //Model
                $rootScope.optionsTemplate = {
                    change: function (file) {
                        var uri = serverAddress;
                        uri = uri + '/addFile';
                        if ($scope.fileId != undefined) uri = uri + '/' + $scope.objectId;
                        $scope.fileIsSelected = true;
                        $scope.ngShowOldFile = false;
                        file.$preview(file).then(function (data) {
                            $scope.newImgSrc = data.item.thumb;
                            $scope.fileName = data.item.filename;
                            $scope.ngShowNewImage = true;
                        });
                        file.$upload(uri, $scope.file, setings, $scope)
                            .then(function (data) {
                                $rootScope.loading = false;
                                if (data.data.status == 'ok') {
                                    if ($scope.callbackFileTemplateInCandidate != undefined) {
                                        $scope.callbackFileTemplateInCandidate(
                                            data.data.objects[0],
                                            $scope.file.filename,
                                        );
                                        new PNotify({
                                            styling: 'jqueryui',
                                            type: 'success',
                                            text: $filter('translate')('history_info.added_file'),
                                        });
                                    }
                                    $scope.fastCandAttachProcessId = data.data.objects[0];
                                    $scope.fastCandAttachProcess = true;
                                } else {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: data.data.message,
                                    });
                                }
                            })
                            .catch(function (data) {
                                $rootScope.loading = false;
                                if (data.response[0].code == 'type') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: 'Возможные форматы файла: ' + setings.allowedType.join(', '),
                                    });
                                }
                                if (data.response[0].code == 'size') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: $filter('translate')('big-file-size'),
                                    });
                                }
                            });
                    },
                };
            },
            initFileOptionForEditFromResume: function ($scope, path, setings) {
                $scope.file = {}; //Model
                $scope.optionsForEditFromResume = {
                    change: function (file) {
                        var uri = serverAddress;
                        if (path != undefined) uri = uri + '/' + path;
                        uri = uri + '/updateFromFile';
                        if ($scope.objectId != undefined) uri = uri + '/' + $scope.objectId;
                        $scope.fileIsSelected = true;
                        $scope.ngShowOldFile = false;
                        file.$preview(file).then(function (data) {
                            /** @namespace data.item.thumb */
                            $scope.newImgSrc = data.item.thumb;
                            $scope.fileName = data.item.filename;
                            $scope.ngShowNewImage = true;
                        });
                        file.$upload(uri, $scope.file, setings, $scope)
                            .then(function (data) {
                                $rootScope.loading = false;
                                var resp = JSON.parse(data.response);

                                if (data.status == 200 && resp.status != 'error') {
                                    if ($scope.callbackFile != undefined) {
                                        $scope.updateCandidate();
                                    }
                                } else if (resp.status == 'error') {
                                    notificationService.error(resp.message);
                                    if ($scope.callbackFileError != undefined) {
                                        $scope.callbackFileError('error');
                                    }
                                }
                                $('.confirmationResumeUpdate.modal').modal('hide');
                            })
                            .catch(function (data) {
                                $rootScope.loading = false;

                                if (data.response[0].code == 'type') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: 'Возможные форматы файла: ' + setings.allowedType.join(', '),
                                    });
                                }
                                if (data.response[0].code == 'size') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: $filter('translate')('big-file-size'),
                                    });
                                }
                            });
                    },
                };
            },
            initFileExcellUpload: function ($rootScope, $scope, path, setings, $filter) {
                $scope.file = {}; //Model
                $scope.options = {
                    change: function (file) {
                        $rootScope.loading = true;
                        var uri = serverAddress;
                        if (path != undefined) uri = uri + '/' + path;
                        uri = uri + '/uploadExcelFile';
                        if ($scope.objectId != undefined) uri = uri + '/' + $scope.objectId;
                        $scope.fileIsSelected = true;
                        $scope.ngShowOldFile = false;
                        file.$preview(file).then(function (data) {
                            /** @namespace data.item.thumb */
                            $scope.newImgSrc = data.item.thumb;
                            $scope.fileName = data.item.filename;
                            $scope.ngShowNewImage = true;
                        });
                        file.$upload(uri, $scope.file, setings, $scope)
                            .then(function (data) {
                                $('#file').val('');
                                var resp = JSON.parse(data.response);

                                if (data.status == 200 && resp.status != 'error') {
                                    if (resp.size > 0) notificationService.success(resp.message);
                                    if ($scope.tableParams) $scope.tableParams.reload();
                                    $rootScope.loading = false;
                                } else if (resp.status == 'error') {
                                    $rootScope.loading = false;
                                    notificationService.error(resp.message);
                                    if ($scope.callbackFileError != undefined) {
                                        $scope.callbackFileError('error');
                                    }
                                }
                            })
                            .catch(function (data) {
                                $('#file').val('');
                                $rootScope.loading = false;
                                if (data.response[0].code == 'type') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text:
                                            $filter('translate')('Allowed file formats') +
                                            ': ' +
                                            setings.allowedType.join(', '),
                                    });
                                }
                                if (data.response[0].code == 'size') {
                                    new PNotify({
                                        styling: 'jqueryui',
                                        type: 'error',
                                        text: $filter('translate')('big-file-size'),
                                    });
                                }
                            });
                    },
                };
            },
            addPhotoByReference: function (url, callback) {
                $http({
                    url: serverAddress + '/addPhotoByReference',
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' },
                    params: { reference: url },
                }).then(function (resp) {
                    if (resp.data.status == 'ok') {
                        callback(resp.data.object);
                    } else if (resp.data.status == 'error') {
                        callback('error');
                        notificationService.error(resp.data.message);
                    }
                });
            },
        };
        var FileInit = $resource(
            serverAddress + '/action/:param',
            { param: '@param' },
            {
                changeFileName: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'changeFileName',
                    },
                },
            },
        );

        return FileInit;
    },
]);
