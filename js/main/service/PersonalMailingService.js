angular.module('services.personalMailing', ['ngResource', 'ngCookies']).factory('PersonalMailing', [
    '$resource',
    'serverAddress',
    function ($resource, serverAddress) {
        const personalMailing = $resource(
            serverAddress + '/personalMailing/:param',
            { param: '@param' },
            {
                getHistory: {
                    method: 'GET',
                    params: {
                        param: 'getHistory',
                    },
                },
                markAsRead: {
                    method: 'GET',
                    params: {
                        param: 'markAsRead',
                    },
                },
            },
        );

        personalMailing.editorVariables = [
            {
                name: 'Candidate name',
                id: '[[candidate name]]',
                field: '',
                isAvailable: true,
            },
            {
                name: 'Candidate full name',
                id: '[[full name]]',
                field: '',
                isAvailable: true,
            },
            {
                name: 'Client name',
                id: '[[client name]]',
                isAvailable: true,
            },
            {
                name: "recruiter's name",
                id: "[[recruiter's name]]",
                field: '',
                isAvailable: true,
            },
            {
                name: "recruiter's phone",
                id: "[[recruiter's phone]]",
                field: 'mobPhone',
                isAvailable: true,
            },
            {
                name: "recruiter's Skype",
                id: "[[recruiter's Skype]]",
                field: 'skype',
                isAvailable: true,
            },
            {
                name: "recruiter's Facebook",
                id: "[[recruiter's Facebook]]",
                field: 'facebook',
                isAvailable: true,
            },
            {
                name: "recruiter's LinkedIn",
                id: "[[recruiter's LinkedIn]]",
                field: 'linkedin',
                isAvailable: true,
            },
        ];

        personalMailing.templateInfo = [
            'Use this template to send the interviw invitation & details when you move candidates to job stages with an interview.',
            'Use this template to describe candidates that thay do not meet the vacancy criteria.',
            'Use this template to send your candidates the letter with the vacancy proposal',
            'Use this template on the “Offer accepted” stage to send all the needed details and the date of start to your new employee',
        ];

        personalMailing.onGetHistory = function (candidateId, number, count) {
            return new Promise((resolve, reject) => {
                personalMailing.getHistory(
                    { candidateId, number, count },
                    (resp) => {
                        resp.status ? resolve(resp) : reject(resp);
                    },
                    (err) => reject(err),
                );
            });
        };
        personalMailing.onMarkAsRead = function (id) {
            return new Promise((resolve, reject) => {
                personalMailing.markAsRead(
                    { id },
                    (resp) => {
                        resp.status ? resolve(resp) : reject(resp);
                    },
                    (err) => reject(err),
                );
            });
        };

        return personalMailing;
    },
]);
