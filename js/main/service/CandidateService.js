angular.module('services.candidate', ['ngResource', 'ngCookies']).factory('Candidate', [
    '$resource',
    'serverAddress',
    '$filter',
    '$localStorage',
    'notificationService',
    '$rootScope',
    '$translate',
    'Service',
    '$http',
    'RegionInputService',
    '$stateParams',
    function (
        $resource,
        serverAddress,
        $filter,
        $localStorage,
        notificationService,
        $rootScope,
        $translate,
        Service,
        $http,
        RegionInputService,
        $stateParams,
    ) {
        var options;

        var vacancy = $resource(
            serverAddress + '/vacancy/:param',
            { param: '@param' },
            {
                actual: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'actual',
                    },
                },
            },
        );

        var candidate = $resource(
            serverAddress + '/candidate/:param',
            { param: '@param' },
            {
                all: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'get',
                    },
                },
                getLanguages: {
                    method: 'GET',
                    params: {
                        param: 'getLanguages',
                    },
                },
                autocompleteWithDuplicates: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/x-www-form-urlencoded',
                    },
                    params: {
                        param: 'autocompleteWithDuplicates',
                    },
                },
                participationInVacancies: {
                    method: 'GET',
                    params: {
                        param: 'participationInVacancies',
                    },
                },
                createExcel: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'createExcel',
                    },
                },
                getExternal: {
                    method: 'POST',
                    params: {
                        param: 'getExternal',
                    },
                },
                add: {
                    method: 'PUT',
                    params: {
                        param: 'add',
                    },
                },
                getSkills: {
                    method: 'GET',
                    params: {
                        param: 'getSkills',
                    },
                },
                edit: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'edit',
                    },
                },
                sendToMail: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'sendToMail',
                    },
                },
                one: {
                    method: 'GET',
                    params: {
                        param: 'get',
                    },
                },
                changeState: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'changeState',
                    },
                },
                setResponsible: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'setResponsible',
                    },
                },
                removeFile: {
                    method: 'GET',
                    params: {
                        param: 'removeFile',
                    },
                },
                setMessage: {
                    method: 'POST',
                    params: {
                        param: 'setMessage',
                    },
                },
                addFile: {
                    method: 'GET',
                    params: {
                        param: 'addFile',
                    },
                },
                addFromRecall: {
                    method: 'GET',
                    params: {
                        param: 'addFromRecall',
                    },
                },
                mathRecallWithCandidate: {
                    method: 'GET',
                    params: {
                        param: 'mathRecallWithCandidate',
                    },
                },
                mergeCandidates: {
                    method: 'PUT',
                    params: {
                        param: 'mergeCandidates',
                    },
                },
                getAdvices: {
                    method: 'GET',
                    params: {
                        param: 'getAdvices',
                    },
                },
                fromLinkFile: {
                    method: 'POST',
                    params: {
                        param: 'fromLinkFile',
                    },
                },
                fromLinkSite: {
                    method: 'POST',
                    params: {
                        param: 'fromLinkSite',
                    },
                },
                fromText: {
                    method: 'POST',
                    params: {
                        param: 'fromText',
                    },
                },
                getContacts: {
                    method: 'GET',
                    params: {
                        param: 'getContacts',
                    },
                },
                getDuplicates: {
                    method: 'POST',
                    params: {
                        param: 'getDuplicates',
                    },
                },
                getDuplicatesByName: {
                    method: 'POST',
                    params: {
                        param: 'getDuplicatesByName',
                    },
                },
                getDuplicatesByNameAndContacts: {
                    method: 'POST',
                    headers: {
                        Pragma: 'no-cache',
                        Expires: -1,
                        'Cache-Control': 'no-cache',
                    },
                    params: {
                        param: 'getDuplicatesByNameAndContacts',
                    },
                },
                addToParserQueue: {
                    method: 'POST',
                    params: {
                        param: 'addToParserQueue',
                    },
                },
                removeFromParserQueue: {
                    method: 'POST',
                    params: {
                        param: 'removeFromParserQueue',
                    },
                },
                getParseEmailData: {
                    method: 'GET',
                    params: {
                        param: 'getParseEmailData',
                    },
                },
                getMessages: {
                    method: 'GET',
                    params: {
                        param: 'getMessages',
                    },
                },
                getParseEmailHistory: {
                    method: 'POST',
                    params: {
                        param: 'getParseEmailHistory',
                    },
                },
                saveSearchFilter: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'saveSearchFilter',
                    },
                },
                // getSearchHistory: {
                //     method: 'POST',
                //     headers: {
                //         'Content-type': 'application/json; charset=UTF-8',
                //     },
                //     params: {
                //         param: 'getSearchHistory',
                //     },
                // },
                getSearchHistoryAdmin: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getSearchHistoryAdmin',
                    },
                },
                checkMailbox: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'checkMailbox',
                    },
                },
                addLink: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'addLink',
                    },
                },
                getCandidateLinks: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getCandidateLinks',
                    },
                },
                removeCandidateLink: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'removeCandidateLink',
                    },
                },
                autocompleteSkill: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'autocompleteSkill',
                    },
                },
                autocompleteOrigin: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/x-www-form-urlencoded',
                    },
                    params: {
                        param: 'autocompleteOrigin',
                    },
                },
                autocompleteAllOrigins: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/x-www-form-urlencoded',
                    },
                    params: {
                        param: 'autocompleteAllOrigins',
                    },
                },
                autocompleteCandidates: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'autocomplete',
                    },
                },
                updateFromFile: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'updateFromFile',
                    },
                },
                getCV: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getCV',
                    },
                },
                getCvArchive: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getCvArchive',
                    },
                },
                addEmailAccess: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'addEmailAccess',
                    },
                },
                editEmailAccess: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'editEmailAccess',
                    },
                },
                editOriginAll: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'editOriginAll',
                    },
                },
                removeOriginAll: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'removeOriginAll',
                    },
                },
                deleteImportedSkill: {
                    method: 'DELETE',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'imported-skill',
                    },
                },
                getCandidateProperties: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getCandidateProperties',
                    },
                },
                deleteCandidate: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'deleteCandidate',
                    },
                },
                saveCandidateFilterValues: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'saveCandidateFilterValues',
                    },
                },
                candidateFilterTemplate: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getCandidateFilterTemplate',
                    },
                },
                excelByStage: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'createExcelByStage',
                    },
                },
                changeOpenStatus: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'changeOpenStatus',
                    },
                },
                setDefaultEmail: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'setDefaultEmail',
                    },
                },
                saveContacts: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'saveContacts',
                    },
                },
                massChangeOrigin: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'massChangeOrigin',
                    },
                },
                massChangePosition: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'massChangePosition',
                    },
                },
                massChangeResponsibles: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'massChangeResponsibles',
                    },
                },
            },
        );

        const languageLevels = [
            'Level is not specified',
            'Basic',
            'Pre_Intermediate',
            'Intermediate',
            'Upper_Intermediate',
            'Advanced',
            'Proficient',
            'Native',
        ];

        const regExpForName =
            /[^A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC\s'-]/g;

        // candidate.getSearchHistoryUniqueLink = function (callback) {
        //     candidate.getSearchHistory({ type: 'linkedin' }, function (resp) {
        //         var history = [];
        //         angular.forEach(resp.objects, function (val, key) {
        //             if (history.length < 8) {
        //                 var has = false;
        //                 angular.forEach(history, function (valHistory, keyHistory) {
        //                     if (
        //                         (valHistory.company === val.company ||
        //                             (valHistory.company &&
        //                                 val.company &&
        //                                 $.trim(valHistory.company.toUpperCase()) ===
        //                                     $.trim(val.company.toUpperCase()))) &&
        //                         (valHistory.position === val.position ||
        //                             (valHistory.position &&
        //                                 val.position &&
        //                                 $.trim(valHistory.position.toUpperCase()) ===
        //                                     $.trim(val.position.toUpperCase()))) &&
        //                         (valHistory.words === val.words ||
        //                             (valHistory.words &&
        //                                 val.words &&
        //                                 $.trim(valHistory.words.toUpperCase()) === $.trim(val.words.toUpperCase()))) &&
        //                         valHistory.countryCode == val.countryCode
        //                     ) {
        //                         has = true;
        //                     }
        //                 });
        //                 if (!has) {
        //                     history.push(val);
        //                 }
        //             }
        //         });
        //         if (callback != undefined) callback(history);
        //     });
        // };

        candidate.searchOptions = function () {
            return options;
        };
        candidate.setOptions = function (name, value) {
            if (name === 'dateTo' || name === 'dateFrom') {
                setAgeRange(name, value);
            } else {
                options[name] = value;
            }
        };
        candidate.onDeleteImportedSkill = new PromiseWrapper('deleteImportedSkill');
        candidate.onParticipationInVacancies = new PromiseWrapper('participationInVacancies');

        candidate.uploadPromoLogo = function (fileUp) {
            $rootScope.loading = true;
            let FD = new FormData();
            let blobBin = atob(fileUp.split(',')[1]);
            let array = [];
            for (let i = 0; i < blobBin.length; i++) {
                array.push(blobBin.charCodeAt(i));
            }
            let file = new Blob([new Uint8Array(array)], {
                type: 'image/png',
            });
            FD.append('image', file);
            return $http({
                url: serverAddress + '/candidate/addPhoto',
                method: 'POST',
                data: FD,
                withCredentials: true,
                headers: { 'Content-Type': undefined },
                transformRequest: angular.identity,
            });
        };

        candidate.init = function () {
            options = {
                state: null,
                id: null,
                personId: null,
                regions: null,
                employmentType: null,
                industry: null,
                sort: null,
                sortOrder: 'DESC',
                org: null,
                origin: null,
                city: null,
                country: null,
                responsibleIds: null,
                ids: null,
                page: { number: 0, count: 100 },
                words: null,
                name: null,
                position: null,
                salary: null,
                sex: null,
                candidateGroupId: null,
            };
            $rootScope.flagToAddEvent = true;
        };

        candidate.getEducationLevel = function () {
            return ['Master', 'Bachelor', 'Associate', 'HighSchool', 'Student'];
        };

        candidate.getStatusForRemove = function () {
            return [
                { value: 'archived', translate: 'candidate_status_assoc.archived' },
                { value: 'active_search', translate: 'candidate_status_assoc.active_search' },
                { value: 'passive_search', translate: 'candidate_status_assoc.passive_search' },
                { value: 'work', translate: 'candidate_status_assoc.work' },
                { value: 'freelancer', translate: 'candidate_status_assoc.freelancer' },
                { value: 'employed', translate: 'candidate_status_assoc.employed' },
                { value: 'not_searching', translate: 'candidate_status_assoc.not_searching' },
                { value: 'only_remote', translate: 'candidate_status_assoc.only_remote' },
                { value: 'only_relocation_abroad', translate: 'candidate_status_assoc.only_relocation_abroad' },
            ];
        };

        candidate.getStatus = function () {
            return [
                {
                    value: 'active_search',
                    name: 'candidate_status_assoc.active_search',
                },
                {
                    value: 'not_searching',
                    name: 'candidate_status_assoc.not_searching',
                },
                {
                    value: 'passive_search',
                    name: 'candidate_status_assoc.passive_search',
                },
                {
                    value: 'employed',
                    name: 'candidate_status_assoc.employed',
                },
                {
                    value: 'freelancer',
                    name: 'candidate_status_assoc.freelancer',
                },
                {
                    value: 'archived',
                    name: 'candidate_status_assoc.archived',
                },
                { value: 'work', name: 'candidate_status_assoc.work' },
                {
                    value: 'only_remote',
                    name: 'candidate_status_assoc.only_remote',
                },
                {
                    value: 'only_relocation_abroad',
                    name: 'candidate_status_assoc.only_relocation_abroad',
                },
            ];
        };
        candidate.getStatusAssociative = function () {
            return {
                active_search: 'active search',
                not_searching: 'not searching',
                passive_search: 'passive search',
                employed: 'employed',
            };
        };

        candidate.fromFile = function ($scope, $rootScope, $location) {
            $scope.optionsForResumeFile = {
                change: function (file) {
                    const fileExtensions = ['.doc', '.docx', '.pdf', '.rtf'];
                    const fileIsValid = fileExtensions.some((fileExtensions) =>
                        file.filename.toLowerCase().includes(fileExtensions),
                    );

                    if (!fileIsValid) {
                        notificationService.error(
                            `${$filter('translate')(
                                'This file format is not supported, please choose another one with such possible extensions:',
                            )} ${fileExtensions.join(', ')}.`,
                        );
                        return;
                    }

                    $rootScope.loading = true;
                    $scope.fastCandLoading = true;
                    file.$preview(file).then(function (data) {
                        $scope.newImgSrc = data.item.thumb;
                        $scope.ngShowNewImage = true;
                    });

                    file.$upload(serverAddress + '/candidate/fromFile', file)
                        .then(function (data) {
                            $scope.fastCandLoading = false;
                            $rootScope.descriptionFromResume = data.data.object.descr.replaceAll('\n', '');
                            $rootScope.descrFlag = true;
                            $rootScope.loading = false;
                            if (data.data.status != 'error') {
                                $rootScope.file = file;
                                $rootScope.resumeToSave = data;
                                $location.path('candidate/add');
                                $scope.setCandidatePropsFromFile();
                            } else {
                                $scope.callbackErr(data.data.message);
                                $scope.fastCandLoading = false;
                            }
                        })
                        .catch(function (data) {
                            if (
                                file.status === 'error' &&
                                (file.code === 'textInFileIsEmpty' || file.code === 'youTryToUploadEmptyFile')
                            ) {
                                notificationService.error(file.message);
                            }
                            $scope.fastCandLoading = false;
                            $rootScope.loading = false;
                        });
                },
                setError: function (err, data) {
                    $scope.callbackErr(data.statusText);
                    $scope.fastCandLoading = false;
                },
            };
        };
        candidate.setPhoto = function ($scope, error) {
            $scope.file = {}; //Model
            $scope.optionsForPhoto = {
                change: function (file) {
                    file.$preview(file).then(function (data) {
                        $scope.newImgSrc = data.item.thumb;
                        $scope.ngShowNewImage = true;
                    });
                    file.$upload(serverAddress + '/candidate/addPhoto', file)
                        .then(function (data) {
                            if (data.data.status === 'error') {
                                notificationService.error(data.data.message);
                            } else {
                                $scope.callbackAddPhoto(data.data.objects[0]);
                            }
                        })
                        .catch(function (err) {
                            notificationService.error(err.message);
                        });
                },
                setError: function (err, data) {
                    $scope.callbackErr(data.statusText);
                },
            };
        };

        var duplicatesByNameAndContacts = false;
        candidate.checkDuplicatesByNameAndContacts = function ($scope, page) {
            $scope.dublicetesTypeName = '';
            $scope.dublicetesTypeMphone = '';
            $scope.dublicetesTypeEmail = '';
            $scope.dublicetesTypeSkype = '';
            $scope.dublicetesTypeLinkedin = '';
            $scope.dublicetesTypeFacebook = '';
            $scope.dublicetesTypeTelegram = '';
            $scope.dublicetesTypeWhatsApp = '';
            $scope.dublicetesTypeViber = '';
            $scope.dublicetesTypeGithub = '';
            $scope.dublicetesTypeBehance = '';
            $scope.dublicetesTypeHomepage = '';
            $scope.dublicetesTypeOther = '';
            if (
                (!duplicatesByNameAndContacts &&
                    $scope.contacts &&
                    $scope.contacts.email &&
                    $scope.contacts.email.length > 4) ||
                (!duplicatesByNameAndContacts &&
                    $scope.contacts &&
                    $scope.contacts.skype &&
                    $scope.contacts.skype.length > 4) ||
                (!duplicatesByNameAndContacts &&
                    $scope.contacts &&
                    $scope.contacts.linkedin &&
                    $scope.contacts.linkedin.length > 4) ||
                (!duplicatesByNameAndContacts &&
                    $scope.contacts &&
                    ($scope.contacts.mphone || $scope.contacts.mphone2 || $scope.contacts.mphone3)) ||
                (!duplicatesByNameAndContacts &&
                    $scope.candidate.firstName &&
                    $scope.candidate.firstName.length > 3 &&
                    $scope.candidate.lastName &&
                    $scope.candidate.lastName.length > 3)
            ) {
                duplicatesByNameAndContacts = true;
                setTimeout(function () {
                    $scope.addPhone = '';
                    if ($scope.contacts.mphone) {
                        $scope.addPhone = $scope.contacts.mphone;
                    }
                    if (
                        ($scope.contacts.mphone == undefined || $scope.contacts.mphone == '') &&
                        $scope.contacts.mphone2 &&
                        $scope.contacts.mphone3
                    ) {
                        $scope.addPhone = $scope.contacts.mphone2.concat(', ', $scope.contacts.mphone3);
                    }
                    if (
                        ($scope.contacts.mphone == undefined || $scope.contacts.mphone == '') &&
                        ($scope.contacts.mphone2 == undefined || $scope.contacts.mphone2 == '') &&
                        $scope.contacts.mphone3
                    ) {
                        $scope.addPhone = $scope.contacts.mphone3;
                    }
                    if (
                        ($scope.contacts.mphone == undefined || $scope.contacts.mphone == '') &&
                        $scope.contacts.mphone2 &&
                        ($scope.contacts.mphone3 == undefined || $scope.contacts.mphone3 == '')
                    ) {
                        $scope.addPhone = $scope.contacts.mphone2;
                    }
                    if (
                        $scope.contacts.mphone &&
                        $scope.contacts.mphone2 &&
                        ($scope.contacts.mphone3 == undefined || $scope.contacts.mphone3 == '')
                    ) {
                        $scope.addPhone = $scope.contacts.mphone.concat(', ', $scope.contacts.mphone2);
                    }
                    if (
                        $scope.contacts.mphone3 &&
                        $scope.contacts.mphone &&
                        ($scope.contacts.mphone2 == undefined || $scope.contacts.mphone2 == '')
                    ) {
                        $scope.addPhone = $scope.contacts.mphone.concat(', ', $scope.contacts.mphone3);
                    }
                    if ($scope.contacts.mphone && $scope.contacts.mphone2 && $scope.contacts.mphone3) {
                        $scope.addPhone = $scope.contacts.mphone
                            .concat(', ', $scope.contacts.mphone2)
                            .concat(', ', $scope.contacts.mphone3);
                    }
                    candidate.getDuplicatesByNameAndContacts(
                        {
                            email: $scope.contacts.email
                                .map(function (c) {
                                    return c.value;
                                })
                                .join(','),
                            skype: $scope.contacts.skype
                                .map(function (c) {
                                    return c.value;
                                })
                                .join(','),
                            linkedInUrl: $scope.contacts.linkedin
                                .map(function (c) {
                                    return c.value;
                                })
                                .join(','),
                            phone: $scope.addPhone
                                .map(function (c) {
                                    return c.value;
                                })
                                .join(','),
                            firstName: $scope.candidate.firstName,
                            lastName: $scope.candidate.lastName,
                        },
                        function (res) {
                            $scope.duplicatesByNameAndContacts = [];
                            if (res.status === 'ok' && res.objects != undefined && res.objects.length > 0) {
                                $scope.isDublicatedContact = false;
                                angular.forEach(res.objects, function (c, i) {
                                    if (c.candidateId != $scope.candidate.candidateId) {
                                        $scope.duplicatesByNameAndContacts.push(c);
                                        if (c.type == 'name') {
                                            $scope.dublicetesTypeName = c.type;
                                        }
                                        if (c.type == 'mphone') {
                                            $scope.dublicetesTypeMphone = c.type;
                                            $scope.isDublicatedContact = true;
                                        }
                                        if (c.type == 'email') {
                                            $scope.dublicetesTypeEmail = c.type;
                                            $scope.isDublicatedContact = true;
                                        }
                                        if (c.type == 'skype') {
                                            $scope.dublicetesTypeSkype = c.type;
                                            $scope.isDublicatedContact = true;
                                        }
                                        if (c.type == 'linkedin') {
                                            $scope.dublicetesTypeLinkedin = c.type;
                                            $scope.isDublicatedContact = true;
                                        }
                                        if (c.type == 'facebook') {
                                            $scope.dublicetesTypeFacebook = c.type;
                                            $scope.isDublicatedContact = true;
                                        }
                                        if (c.type == 'telegram') {
                                            $scope.dublicetesTypeTelegram = c.type;
                                            $scope.isDublicatedContact = true;
                                        }
                                        if (c.type == 'whatsApp') {
                                            $scope.dublicetesTypeWhatsApp = c.type;
                                            $scope.isDublicatedContact = true;
                                        }
                                        if (c.type == 'viber') {
                                            $scope.dublicetesTypeViber = c.type;
                                            $scope.isDublicatedContact = true;
                                        }
                                        if (c.type == 'github') {
                                            $scope.dublicetesTypeGithub = c.type;
                                            $scope.isDublicatedContact = true;
                                        }
                                        if (c.type == 'behance') {
                                            $scope.dublicetesTypeBehance = c.type;
                                            $scope.isDublicatedContact = true;
                                        }
                                        if (c.type == 'homepage') {
                                            $scope.dublicetesTypeHomepage = c.type;
                                            $scope.isDublicatedContact = true;
                                        }
                                        if (c.type == 'other') {
                                            $scope.dublicetesTypeOther = c.type;
                                            $scope.isDublicatedContact = true;
                                        }
                                    }
                                });
                            } else {
                                $scope.duplicatesByNameAndContacts = [];
                            }
                            duplicatesByNameAndContacts = false;
                            if ($scope.duplicatesByNameAndContacts && $scope.duplicatesByNameAndContacts.length) {
                                if ($scope.isDublicatedContact) {
                                    $scope.showDuplicates = true;
                                    if (page !== 'candidate') {
                                        const contactsBlock = document.getElementById('contactsBlock');
                                        if (contactsBlock) {
                                            $scope.duplicatesTop = contactsBlock.offsetTop + 24;
                                            $scope.duplicatesLeft =
                                                contactsBlock.offsetLeft + 16 + contactsBlock.clientWidth;
                                        }
                                    } else {
                                        if (document.getElementById('contactsBlock') !== null) {
                                            $scope.duplicatesTop =
                                                document.getElementById('contactsBlock').offsetTop + 150;
                                            $scope.duplicatesLeft =
                                                document.getElementById('contactsBlock').getBoundingClientRect().width +
                                                25;
                                        }
                                    }
                                } else if (page !== 'candidate') {
                                    $scope.showDuplicates = true;
                                    $rootScope.$$phase || $scope.$apply();
                                    const namesBlock = document.getElementById('nameBlock');
                                    if (namesBlock) {
                                        $scope.duplicatesTop = namesBlock.offsetTop + 24;
                                        $scope.duplicatesLeft = namesBlock.offsetLeft + 16 + namesBlock.clientWidth;
                                    }
                                }
                            }
                            $rootScope.$$phase || $scope.$apply();
                        },
                        function (resp) {
                            $scope.duplicatesByNameAndContacts = [];
                            duplicatesByNameAndContacts = false;
                        },
                    );
                    $rootScope.$$phase || $scope.$apply();
                }, 1000);
            } else {
                $scope.duplicatesByNameAndContacts = [];
                $rootScope.$$phase || $scope.$apply();
            }
        };

        function countCandProperties($scope, cand) {
            let candidate = angular.copy(cand);
            let allPuncts = 15;
            let tenPersents = (allPuncts / 100) * 10;
            let count = 0;

            if (candidate) {
                candidate.languages = $scope.languagesModel || $scope.candidate.languages;
                candidate.skillsModel = $scope.skillsModel || $scope.candidate?.candidateSkills;

                if (candidate.firstName && candidate.lastName) {
                    count += tenPersents;
                }
                if (
                    candidate.skillsModel &&
                    candidate.skillsModel.length &&
                    candidate.skillsModel.some((skill) => skill.skill || skill.skillName)
                ) {
                    count += tenPersents;
                }
                if (
                    candidate.languages &&
                    candidate.languages.length &&
                    candidate.languages.some((language) => language.name)
                ) {
                    count += tenPersents;
                }
                if (candidate.contacts && candidate.contacts.length) {
                    let thereIsAtLeastOneContact = candidate.contacts.some(
                        (item) => item.value && item.value.some((item) => item.value),
                    );
                    if (thereIsAtLeastOneContact) {
                        count += tenPersents;
                    }
                }
                if (
                    (candidate.country?.value && candidate.city?.value) ||
                    (candidate.region && candidate.region.city)
                ) {
                    count += tenPersents;
                }
                if (
                    ($scope.employmentType && $scope.employmentType.length) ||
                    (candidate.employmentType && candidate.employmentType.length)
                ) {
                    count += tenPersents;
                }
                if (
                    (candidate.descr && candidate.descr.length > 500) ||
                    ($rootScope.descriptionFromResume && $rootScope.descriptionFromResume.length > 500)
                ) {
                    count += tenPersents;
                }
                if (candidate.salary) {
                    count += tenPersents;
                }
                if (candidate.expirence) {
                    count += tenPersents;
                }
                if (candidate.position) {
                    count += tenPersents;
                }
                $rootScope.$$phase || $scope.$apply();
            }
            return count;
        }

        candidate.externalSource = function () {
            if (
                $localStorage.isExist('search_external') &&
                JSON.parse($localStorage.get('search_external')).version != undefined &&
                JSON.parse($localStorage.get('search_external')).version == 6
            ) {
                return JSON.parse($localStorage.get('search_external'));
            } else {
                return {
                    openSettingsMenu: false,
                    externalToSearch: ['rabota', 'hhUa', 'work', 'djinni', 'superJobUa'],
                    visibleUa: 'none',
                    visibleRu: 'none',
                    version: 6,
                    visibleKz: 'none',
                    visibleBy: 'none',
                    sourcesUa: [
                        {
                            value: 'robota',
                            name: 'robota.ua',
                            check: false,
                        },
                        {
                            value: 'hhUa',
                            check: false,
                            name: 'hh.ua',
                        },
                        {
                            value: 'work',
                            check: false,
                            name: 'work.ua',
                        },
                        {
                            value: 'djinni',
                            check: false,
                            name: 'djinni.co',
                        },
                        {
                            value: 'superJobUa',
                            check: false,
                            name: 'superjob.ua',
                        },
                    ],
                    sourcesRu: [
                        {
                            value: 'hhRu',
                            check: false,
                            name: 'hh.ru',
                        },
                        {
                            value: 'superJobRu',
                            check: false,
                            name: 'superjob.ru',
                        },
                        {
                            value: 'djinni',
                            check: false,
                            name: 'djinni.co',
                        },
                    ],
                    sourcesKz: [
                        {
                            value: 'hhKz',
                            check: false,
                            name: 'hh.kz',
                        },
                    ],
                    sourcesBy: [
                        {
                            value: 'hhBy',
                            check: false,
                            name: 'jobs.tut.by',
                        },
                        {
                            value: 'superJobBy',
                            check: false,
                            name: 'by.superjob.ru',
                        },
                        {
                            value: 'djinni',
                            check: false,
                            name: 'djinni.co',
                        },
                    ],
                };
            }
        };

        candidate.progressUpdate = function ($scope) {
            const contactTypes = [
                'email',
                'mphone',
                'skype',
                'linkedin',
                'telegram',
                'facebook',
                'googleplus',
                'homepage',
            ];
            let cand = angular.copy($scope.candidate);

            cand.contacts = [];
            if ($scope.contacts !== undefined) {
                contactTypes.forEach((contactType) => {
                    cand.contacts.push({
                        type: contactType,
                        value: $scope.contacts[contactType],
                    });
                });
            } else if ($scope.candidate.contacts) {
                cand.contacts = $scope.candidate.contacts.map((contact) => {
                    return {
                        type: contact.type,
                        value: [
                            {
                                type: contact.type,
                                value: contact.value,
                                default: contact.default,
                            },
                        ],
                    };
                });
            }
            let c = countCandProperties($scope, cand);
            $scope.progressPct = c / 15 < 1 ? c / 15 : 1;
            $scope.progressPct = +$scope.progressPct.toFixed(2);

            $rootScope.$$phase || $scope.$apply();
        };

        candidate.convert = function ($scope, object) {
            if (
                !object.fullName &&
                !object.photo &&
                !object.education &&
                !object.position &&
                !object.expirence &&
                !object.languages &&
                !object.coreSkills &&
                !object.contacts &&
                !object.db
            ) {
                new PNotify({
                    styling: 'jqueryui',
                    type: 'error',
                    text: $filter('translate')("We found small amount of data, it doesn't look like resume."),
                });
            } else {
                $scope.candidate = null;
                $scope.candidate = object;
                object.employmentType && object.employmentType.length
                    ? ($scope.candidate.employmentType = object.employmentType.split(','))
                    : ($scope.candidate.employmentType = []);
                $scope.candidate.descr = object.descr;
                if ($scope.candidate.salary == 0) {
                    $scope.candidate.salary = '';
                }

                if ($scope.candidate.region && $scope.candidate.region.googlePlaceId) {
                    //TODO set region
                } else if (object.city) {
                    const fromGoogle = object.region && object.region.fromGoogle;
                    Service.getPlaceInfo(object.city + ' ' + object.country, function (resp) {
                        var reg = {
                            ...Service.convertToRegionObject(resp),
                            fromGoogle: fromGoogle,
                        };
                        $scope.regionInput = reg.fullName;
                        $('#pac-input').val(reg.fullName);
                        $scope.candidate.region = reg;
                        $scope.region = reg;
                    });
                }

                if ($scope.candidate.currency == undefined) {
                    $scope.candidate.currency = { label: 'USD', value: 'USD' };
                }
                if (typeof $scope.candidate.currency === 'string') {
                    $scope.candidate.currency = { label: $scope.candidate.currency, value: $scope.candidate.currency };
                }
                if ($scope.candidate.status == undefined) {
                    $scope.candidate.status = 'active_search';
                }
                $scope.datepickerOfBirth = null;
                if (object.db != undefined) {
                    $scope.datepickerOfBirth = new Date(object.db);
                }
                $scope.photoLink = $scope.candidate.photo
                    ? $scope.serverAddress + '/getapp?id=' + $scope.candidate.photo + '&d=true'
                    : null;
                $scope.fileForSave = [];
                if (object.contacts) {
                    $.each(object.contacts, function (i, contact) {
                        if (contact.type === 'mphone' || 'gmail') $scope.contacts[contact.type] = [];
                        const values = contact.value.split(', ');
                        if (values.length > 1) {
                            for (let j = 0; j < values.length; j++) {
                                $scope.contacts[contact.type].push({
                                    type: contact.type,
                                    value: values[j],
                                    default: contact.default,
                                });
                            }
                        }
                        if (values.length === 1) {
                            $scope.contacts[contact.type].push(contact);
                        }
                    });
                }
                onSetFullDataCandidate($scope);
            }
            $scope.checkDuplicatesByNameAndContacts();
            $scope.$$phase || $scope.$apply();
        };

        candidate.ZIP = function ($scope, $interval) {
            $scope.file = {}; //Model
            $scope.optionsForZIP = {
                change: function (file) {
                    $rootScope.disabledBtn = true;
                    file.$upload(serverAddress + '/uploadZipFile', file)
                        .then(function (resp) {
                            if (resp.status && angular.equals(resp.status, 'error')) {
                                notificationService.error(resp.message);
                            } else {
                                $scope.zipInfo = function () {
                                    var fullPath = $('#zip').val();
                                    if (fullPath) {
                                        if ($scope.zipBrowser == 'Firefox') {
                                            $scope.filename = fullPath;
                                        } else {
                                            var startIndex =
                                                fullPath.indexOf('\\') >= 0
                                                    ? fullPath.lastIndexOf('\\')
                                                    : fullPath.lastIndexOf('/');
                                            var filename = fullPath.substring(startIndex);
                                            if (filename.indexOf('\\') === 0 || filename.indexOf('/') === 0) {
                                                $scope.filename = filename.substring(1);
                                            }
                                        }
                                    }
                                };
                                $scope.zipInfo();
                                $scope.response = JSON.parse(resp.response);
                                if ($scope.response.status == 'ok') {
                                    notificationService.success(
                                        $filter('translate')('Your archive successfully loaded'),
                                    );
                                    if ($scope.regionzip.length <= 1) {
                                        $.ajax({
                                            url: '/hr/setZipFileParams',
                                            type: 'POST',
                                            data:
                                                '{"fileName":"' +
                                                $scope.filename.slice($scope.filename.lastIndexOf('\\') + 1) +
                                                '","type":"' +
                                                $scope.zipType +
                                                '","countries":"' +
                                                $scope.regionzip[0].country +
                                                '","countryIds":"' +
                                                $scope.regionzip[0].googlePlaceId.googlePlaceId +
                                                '","onlyResume":"' +
                                                $scope.zipTypeFiles +
                                                '"}',
                                            dataType: 'json',
                                            contentType: 'application/json',
                                        });
                                    } else if ($scope.regionzip.length <= 2) {
                                        $.ajax({
                                            url: '/hr/setZipFileParams',
                                            type: 'POST',
                                            data:
                                                '{"fileName":"' +
                                                $scope.filename.slice($scope.filename.lastIndexOf('\\') + 1) +
                                                '","type":"' +
                                                $scope.zipType +
                                                '","countries":"' +
                                                $scope.regionzip[0].country +
                                                ',' +
                                                $scope.regionzip[1].country +
                                                '","countryIds":"' +
                                                $scope.regionzip[0].googlePlaceId.googlePlaceId +
                                                ',' +
                                                $scope.regionzip[1].googlePlaceId.googlePlaceId +
                                                '","onlyResume":"' +
                                                $scope.zipTypeFiles +
                                                '"}',
                                            dataType: 'json',
                                            contentType: 'application/json',
                                        });
                                    } else {
                                        $.ajax({
                                            url: '/hr/setZipFileParams',
                                            type: 'POST',
                                            data:
                                                '{"fileName":"' +
                                                $scope.filename.slice($scope.filename.lastIndexOf('\\') + 1) +
                                                '","type":"' +
                                                $scope.zipType +
                                                '","countries":"' +
                                                $scope.regionzip[0].country +
                                                ',' +
                                                $scope.regionzip[1].country +
                                                ',' +
                                                $scope.regionzip[2].country +
                                                '","countryIds":"' +
                                                $scope.regionzip[0].googlePlaceId.googlePlaceId +
                                                ',' +
                                                $scope.regionzip[1].googlePlaceId.googlePlaceId +
                                                ',' +
                                                $scope.regionzip[2].googlePlaceId.googlePlaceId +
                                                '","onlyResume":"' +
                                                $scope.zipTypeFiles +
                                                '"}',
                                            dataType: 'json',
                                            contentType: 'application/json',
                                        });
                                    }
                                } else {
                                    notificationService.error($scope.response.message);
                                }
                                $scope.updateZipList();
                                var stopRefreshing = false;
                                var start = $interval(function () {
                                    $scope.updateZipList();
                                    angular.forEach($scope.zipUploads, function (data) {
                                        if ($scope.response.object.uplId == data.uplId) {
                                            if (data.status == 'finished') {
                                                stopRefreshing = true;
                                            }
                                        }
                                    });
                                    if (stopRefreshing) {
                                        $interval.cancel(start);
                                    }
                                }, 5000);
                            }
                        })
                        .finally(() => {
                            $rootScope.disabledBtn = false;
                        });
                },
            };
        };

        candidate.convert2 = function ($scope, object, toSave) {
            var updateText = '';
            if ($scope.datepickerOfBirth == null && object.db) {
                $scope.datepickerOfBirth = $filter('date')(object.db, 'dd/MM/yyyy');
                updateText += ' ' + $filter('translate')('date_of_birth');
            }
            if (!$scope.candidate.fullName && object.fullName) {
                $scope.candidate.fullName = object.fullName;
                if (updateText) {
                    updateText += ',';
                }
                updateText += ' ' + $filter('translate')('full_name');
            }
            if (!$scope.candidate.position && object.position) {
                $scope.candidate.position = $scope.setPositionAutocompleterValue(object.position);
                if (updateText) {
                    updateText += ',';
                }
                updateText += ' ' + $filter('translate')('position');
            }
            if (!$scope.candidate.photo && object.photo) {
                $scope.candidate.photo = object.photo;
                if (updateText) {
                    updateText += ',';
                }
                updateText += ' ' + $filter('translate')('photo');
            }
            if ($scope.candidate.photo) {
                $scope.photoLink = $scope.candidate.photo
                    ? $scope.serverAddress + '/getapp?id=' + $scope.candidate.photo + '&d=true'
                    : null;
            }
            if (!$scope.regionInput && object.city) {
                $scope.regionInput = object.city;
                if (updateText) {
                    updateText += ',';
                }
                updateText += ' ' + $filter('translate')('city');
            }
            if (!toSave && object.contacts != undefined) {
                $.each(object.contacts, function (i, c) {
                    if (angular.equals(c.type, 'email') && !$scope.contacts.email && c.value) {
                        $scope.contacts.email = c.value;
                        if (updateText) {
                            updateText += ',';
                        }
                        updateText += ' ' + $filter('translate')('Email');
                    }
                    candidate.checkDuplicatesByNameAndContacts($scope);
                    if (angular.equals(c.type, 'skype') && !$scope.contacts.skype && c.value) {
                        $scope.contacts.skype = c.value;
                        if (updateText) {
                            updateText += ',';
                        }
                        updateText += ' ' + $filter('translate')('Skype');
                    }
                    if (angular.equals(c.type, 'mphone') && !$scope.contacts.mphone && c.value) {
                        $scope.contacts.mphone = c.value;
                        if (updateText) {
                            updateText += ',';
                        }
                        updateText += ' ' + $filter('translate')('phone');
                    }

                    if (angular.equals(c.type, 'homepage') && !$scope.contacts.homepage && c.value) {
                        $scope.contacts.homepage = c.value;
                        if (updateText) {
                            updateText += ',';
                        }
                        updateText += ' ' + $filter('translate')('home_page');
                    }
                });
                $scope.candidate.position = $scope.getPositionAutocompleterValue();
            }
            if (toSave && object.contacts != undefined && false) {
                $.each(object.contacts, function (i, c) {
                    if (angular.equals(c.type, 'email') && c.value) {
                        var needContact = true;
                        $.each($scope.candidate.contacts, function (j, cOld) {
                            if (angular.equals(cOld.type, 'email') && cOld.value) {
                                needContact = false;
                            }
                        });
                        if (needContact) {
                            $scope.candidate.contacts.push({
                                type: 'email',
                                value: c.value,
                                default: c.default,
                            });
                            if (updateText) {
                                updateText += ',';
                            }
                            updateText += ' ' + $filter('translate')('Email');
                        }
                    }
                    candidate.checkDuplicatesByNameAndContacts($scope);
                    if (angular.equals(c.type, 'skype') && c.value) {
                        var needContact = true;
                        $.each($scope.candidate.contacts, function (j, cOld) {
                            if (angular.equals(cOld.type, 'skype') && cOld.value) {
                                needContact = false;
                            }
                        });
                        if (needContact) {
                            $scope.candidate.contacts.push({
                                type: 'skype',
                                value: c.value,
                                default: c.default,
                            });
                            if (updateText) {
                                updateText += ',';
                            }
                            updateText += ' ' + $filter('translate')('Skype');
                        }
                    }
                    if (angular.equals(c.type, 'mphone') && c.value) {
                        var needContact = true;
                        $.each($scope.candidate.contacts, function (j, cOld) {
                            if (angular.equals(cOld.type, 'mphone') && cOld.value) {
                                needContact = false;
                            }
                        });
                        if (needContact) {
                            $scope.candidate.contacts.push({
                                type: 'mphone',
                                value: c.value,
                                default: c.default,
                            });
                            if (updateText) {
                                updateText += ',';
                            }
                            updateText += ' ' + $filter('translate')('phone');
                        }
                    }
                });
            }
            if (!$scope.candidate.descr && object.descr && updateText !== '') {
                $scope.candidate.descr = object.descr;
                if (updateText) {
                    updateText += ',';
                }
                updateText += ' ' + $filter('translate')('description');
            }
            if (updateText) {
                new PNotify({
                    styling: 'jqueryui',
                    type: 'success',
                    text: $filter('translate')('Added new data') + ':<br/>' + updateText,
                });
            }
        };
        //////////////////////////////////////////////////////////////////////// Search params

        var searchParams = null;
        candidate.getSearchParams = function () {
            return searchParams;
        };
        candidate.setSearchParams = function (data) {
            searchParams = data;
        };

        candidate.init();

        candidate.getAllCandidates = function (params) {
            if (Array.isArray($rootScope.redirectIds)) {
                params.ids = $rootScope.redirectIds;
            } else {
                if ($rootScope.redirectIds) params.ids = [$rootScope.redirectIds];
            }

            if ($rootScope.searchFromAdvice) {
                params.searchByVacancyParams = true;
            }

            candidate.candidateLastRequestParams = params;
            localStorage.setItem('candidateLastRequestParams', JSON.stringify(params));
            return new Promise(function (resolve, reject) {
                let data;
                candidate.all(
                    params,
                    function (response) {
                        if (!response.objects) {
                            $rootScope.loading = false;
                            resolve(response, params);
                            return;
                        }
                        candidate.getCandidate = response.objects.map(function (item) {
                            return item.localId;
                        });
                        data = candidate.getCandidate;
                        localStorage.setItem('getAllCandidates', JSON.stringify(data));
                        $rootScope.flag = true;
                        resolve(response, params);
                    },
                    function () {
                        reject();
                    },
                );
            });
        };

        candidate.getActualVacancy = function (params) {
            return new Promise(function (resolve, reject) {
                vacancy.actual(
                    params,
                    function (resp) {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    function (error) {
                        return reject(error);
                    },
                );
            });
        };

        candidate.deleteCandidateFromSystem = function (params) {
            return new Promise(function (resolve, reject) {
                candidate.deleteCandidate(
                    params,
                    function (resp) {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    function (error) {
                        return reject(error);
                    },
                );
            });
        };
        candidate.onGetCandidateFields = new PromiseWrapper('one');
        candidate.onGetSkills = new PromiseWrapper('getSkills');
        candidate.onGetAutocompleteWithDuplicates = new PromiseWrapper('autocompleteWithDuplicates');

        candidate.setAgeRange = function (years, dateFromTo) {
            return years ? ageRangeToMs(years, dateFromTo) : null;
        };

        candidate.setCandidateLangs = function ($scope) {
            Service.onGetLanguagesObjectCached().then(() => {
                if ($scope) {
                    $scope.candidate.languages = $scope.candidate.languages.filter((item) => item.name);

                    if ($scope.candidate.languages && $scope.candidate.languages.length > 0) {
                        $scope.languages = $scope.candidate.languages.map((item) => ({
                            ...item,
                            name: $filter('bigFirstLetter')($rootScope.allLanguages[item.name.toLowerCase()].english),
                            nameRu: $filter('bigFirstLetter')($rootScope.allLanguages[item.name.toLowerCase()].russian),
                            nameUa: $filter('bigFirstLetter')(
                                $rootScope.allLanguages[item.name.toLowerCase()].ukrainian,
                            ),
                            namePl: $filter('bigFirstLetter')($rootScope.allLanguages[item.name.toLowerCase()].polish),
                        }));
                    } else {
                        $scope.languages = [{ name: null, level: undefined }];
                    }
                }
            });
        };

        function onSetFullDataCandidate($scope) {
            candidate.setCandidateLangs($scope);
            $scope.fullDataCandidate = {
                ...$scope.fullDataCandidate,
                ...$scope.candidate,
            };
            $scope.$$phase || $scope.$apply();
        }

        function setAgeRange(name, value) {
            if (typeof value !== 'number') {
                options[name] = null;
            } else {
                if (name === 'dateTo') {
                    if (typeof options['dateFrom'] === 'number') {
                        options['dateFrom'] = ageRangeToMs(value + 1) + 86400000;
                    }
                    options['dateTo'] = ageRangeToMs(value);
                } else {
                    options['dateFrom'] = ageRangeToMs(value + 1) + 86400000;
                }
            }
        }

        function ageRangeToMs(years, dateFromTo) {
            let date;
            if (dateFromTo == 'dateFrom') {
                date = new Date(new Date().setFullYear(new Date().getFullYear() - years));
                date.setHours(23, 59, 59, 0);
                return years ? date.getTime() : years;
            } else if (dateFromTo == 'dateTo') {
                date = new Date(new Date().setFullYear(new Date().getFullYear() - years - 1) + 86400000);
                date.setHours(0, 0, 0, 0);
                return years ? date.getTime() : years;
            }
        }

        function ageRangeToYears(ms) {
            return ms ? new Date().getFullYear() - new Date(ms).getFullYear() : ms;
        }

        candidate.deleteCandidates = function (param) {
            return new Promise(function (resolve, reject) {
                candidate.changeState(
                    {
                        candidateIds: param.ids,
                        comment: param.comment,
                        candidateState: param.candidateState,
                    },
                    function (resp) {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    function (error) {
                        reject(error);
                    },
                );
            });
        };

        candidate.search = {
            _params: {
                candidateGroupId: null,
                candidateGroupIds: null,
                city: null,
                country: null,
                countries: null,
                dateFrom: null,
                dateTo: null,
                employmentType: null,
                experience: null,
                id: null,
                ids: null,
                industry: null,
                languages: [],
                name: null,
                org: null,
                origin: null,
                page: { number: 0, count: 15 },
                personId: null,
                position: null,
                regions: null,
                responsibleIds: null,
                salary: null,
                salaryTo: null,
                searchFullTextType: null,
                sex: null,
                skills: null,
                sort: 'dm',
                sortOrder: 'DESC',
                state: null,
                withPersonalContacts: null,
                words: null,
                containsWord: false,
                fieldValues: [],
                dateDmFrom: null,
                dateDmTo: null,
                dateDcFrom: null,
                dateDcTo: null,
                vacancyId: null,
            },

            resetPagination: false,

            set params(params) {
                params.customFields.forEach((field) => {
                    if (field.field.type === 'date') {
                        field.dateTimeValue = $rootScope.TimeMinusTimeZone(field.dateTimeValue);
                    }
                });

                this._params = Object.assign(this._params, params);
            },

            get params() {
                return this._params;
            },

            setParam(param, value) {
                this.params[param] = value;
            },

            _searchValues: {},
            _customFieldsValues: {},

            set searchValues(values) {
                this._searchValues = angular.copy(values);
            },

            get searchValues() {
                return angular.equals({}, this._searchValues) ? null : this._searchValues;
            },

            get customFieldsValues() {
                return angular.equals({}, this._customFieldsValues) ? null : this._customFieldsValues;
            },

            set customFieldsValues(values) {
                this._customFieldsValues = angular.copy(values);
            },

            resetFieldsValues() {
                this._searchValues = {};
                this._customFieldsValues = {};
            },

            resetParams() {
                for (let param in this._params) {
                    switch (param) {
                        case 'page':
                            this._params[param] = { number: 0, count: 15 };
                            break;
                        case 'sort':
                            this._params[param] = 'dm';
                            break;
                        case 'sortOrder':
                            this._params[param] = 'DESC';
                            break;
                        case 'candidateGroupIds':
                        case 'languages':
                            this._params[param] = [];
                            break;
                        default:
                            this._params[param] = null;
                    }
                }
            },
        };

        _wrapWordsInBrackets = function (string) {
            if (!string) return;

            const booleanWords = ['OR', 'AND', 'NOT', '(', ')'];
            if (string && booleanWords.every((booleanWord) => !string.includes(booleanWord))) {
                return string;
            }

            let words = string
                .trim()
                .replace(/\)/g, '!)')
                .replace(/\(/g, '(!')
                .split(' ')
                .join('$')
                .split('!')
                .join('$')
                .split('$');
            words = words
                .map((word) => (!booleanWords.includes(word) && !word.match(/[',"]/g) ? `'${word}'` : word))
                .join(' ');
            if (words.includes('(')) {
                let deleteAfter = words.split('');
                deleteAfter.splice(words.indexOf('(') + 1, 1);
                deleteAfter.splice(words.indexOf(')') - 2, 1);
                deleteAfter = deleteAfter.join('');
                return deleteAfter;
            }
            return words;
        };

        candidate.setSearchParamsMethod = function (
            country,
            search,
            activeScopeParam,
            customFieldsSearch,
            scorecards = false,
            isHideSearchActualCandidates,
        ) {
            let skillsPayload = [];
            let countries = null;
            if ($rootScope.skillsParam && $rootScope.redirectToSuggestions) {
                if (($rootScope.skillsParam && $rootScope.skillsParam[0].skill) || $rootScope.skillsParam[0].skillId) {
                    $rootScope.skillsParam.forEach((item) => {
                        skillsPayload.push({ experience: item.experience, skillId: item.skillId, type: item.type });
                    });
                } else {
                    skillsPayload = [];
                }
            }

            const candidateSkills = [];
            const gdprStatuses = [];

            if (search.fields.skills && search.fields.skills.value?.length) {
                search.fields.skills.value.forEach((skill) => {
                    if (skill?.skill?.value) {
                        candidateSkills.push({
                            skillId: skill.skill.skillId,
                            type: skill.skill.type,
                            experience: skill.experience.value,
                        });
                    }
                });
            }

            candidate.search.resetPagination = true;

            if ($rootScope.searchFromAdvice && $rootScope.regionParam?.country) {
                country = $rootScope.regionParam.country.value;
            } else {
                if (country) {
                    if (country?.value?.length > 1) {
                        countries = country.value.map((val) => (val.id === 'Not specified' ? val.value : val.showName));
                        country = null;
                    } else {
                        country =
                            country?.value[0]?.id === 'Not specified'
                                ? country?.value[0]?.value
                                : country?.value[0]?.showName;
                    }
                }
            }

            if (!search.fields.scorecards.value?.name && candidate.search._params.sort === 'scoreCard') {
                candidate.search._params.sort = 'dm';
                $rootScope.candidatesSortType = {
                    name: 'Date of last activity',
                    values: 'dm',
                };
                $rootScope.changeFilterToDefault('dm');
            }

            if (search.fields.gdpr.selectedStatuses) {
                search.fields.gdpr.selectedStatuses.forEach((status) => {
                    gdprStatuses.push(status.value);
                });
            }

            if (
                search.fields.scorecards.value &&
                candidate.search._params.sort === 'scoreCard' &&
                !$rootScope.switchToOtherSort
            ) {
                candidate.search._params.sort = 'scoreCard';
                $rootScope.changeFilterToDefault('scoreCard');
            }

            if (search.fields.scorecards.value?.name && scorecards && !$rootScope.switchToOtherSort) {
                candidate.search._params.sort = 'scoreCard';
                $rootScope.candidatesSortType = { name: 'Score-sort', values: 'scoreCard' };
                $rootScope.changeFilterToDefault('scoreCard');
            }

            if (search.fields.languages && search.fields.languages.selectedLanguages?.length > 0) {
                $rootScope.searchSaveLanguage = search.fields.languages;
            }

            const states = [];

            if (search.fields.state.getValue() !== null) {
                if (search.fields.state.getValue().value.length) {
                    search.fields.state.getValue().value.forEach((state) => {
                        states.push(state.value);
                    });
                }
            }

            if (isHideSearchActualCandidates) {
                $rootScope.isSearchActualCandidates = false;
                $rootScope.searchFromAdvice = false;
            }

            candidate.search.params = {
                candidateGroupId: null,
                candidateGroupIds: search.fields.candidateGroup.getTagsIds(),
                city: search.fields.city.getValue(),
                country,
                countries,
                dateFrom: candidate.setAgeRange(search.fields.date.dateFrom.value?.value, 'dateFrom'),
                dateTo: candidate.setAgeRange(search.fields.date.dateTo.value?.value, 'dateTo'),
                employmentType: search.fields.employmentType.getValue(),
                experience: search.fields.experience.getValue(),
                id: null,
                ids: null,
                industry: search.fields.industry.getValue()?.value,
                languages: search.fields.languages ? search.fields.languages.getValue() : [],
                name: search.fields.name.getValue(),
                surname: search.fields.surname.getValue(),
                org: null,
                origin: search.fields.origin.getValue()?.value,
                personId: activeScopeParam.name === 'onlyMy' ? $rootScope.me.userId : null,
                position: search.fields.position.getValue()?.value,
                roleLevels: search.fields.roleLevels.getValue()?.map((level) => level.value),
                regions: null,
                responsibleIds: search.fields.responsible.getValue(),
                interviewStates: search.fields.stages.value?.map(
                    (stage) => stage.customInterviewStateId || stage.value,
                ),
                salary: null,
                salaryTo: search.fields.salaryTo.getValue(),
                currency: search.fields.salaryTo.salaryCurrency.getValue() || null,
                searchFullTextType: search.fields.boolean.getValue() ? 'booleanSearch' : null,
                sex: search.fields.sex.getValue(),
                candidateSkills: candidateSkills,
                states,
                personalDataConsentStatus: gdprStatuses,
                scoreCard: search.fields.scorecards.value?.scoreCardId,
                withPersonalContacts: search.fields.withPersonalContacts.getValue(),
                words: _wrapWordsInBrackets(search.fields.boolean.getValue()),
                containsWord: search.fields.matchType.getValue(),
                customFields: customFieldsSearch ? customFieldsSearch.getValues() : [],
                comment: search.fields.comment.getValue(),
                vacancyId: search.fields.vacancyId.value?.vacancyId,
                currentWorkingPlace: search.fields.currentWorkingPlace.getValue(),
                dateDmFrom: search.fields.dateDmFrom.getValue(),
                dateDmTo: search.fields.dateDmTo.getValue(),
                dateDcFrom: search.fields.dateDcFrom.getValue(),
                dateDcTo: search.fields.dateDcTo.getValue(),
                searchByVacancyParams: $rootScope.isSearchActualCandidates ? true : undefined,
                searchNullValues: search.allNullFieldNames,
            };

            // if ($rootScope.isActualSearch && candidate.search.params.candidateSkills) {
            //     if (
            //         candidate.search.params.candidateSkills.experience == null ||
            //         candidate.search.params.candidateSkills[0].experience == null
            //     ) {
            //         candidate.search.params.candidateSkills = null;
            //     }
            // }

            // if (candidate.search.params.customFields && candidate.search.params.customFields.length) {
            //     candidate.search.params.customFields.forEach((item) => {
            //         if (item.field.type === 'select') {
            //             if (item.value === 'no_value') {
            //                 if (!$rootScope.searchNullCustomFields) {
            //                     $rootScope.searchNullCustomFields = [item.field.title];
            //                 } else $rootScope.searchNullCustomFields.push(item.field.title);
            //                 if ($rootScope.searchNullValues) {
            //                     $rootScope.searchNullValues.push('customFields');
            //                 } else {
            //                     $rootScope.searchNullValues = ['customFields'];
            //                 }
            //             } else {
            //                 if ($rootScope.searchNullCustomFields?.length && $rootScope.searchNullValues?.length) {
            //                     $rootScope.searchNullCustomFields = $rootScope.searchNullCustomFields.filter((item) => {
            //                         if (item.field) {
            //                             return item !== item.field.title;
            //                         }
            //                     });
            //                     if ($rootScope.searchNullCustomFields.length === 0)
            //                         $rootScope.searchNullValues = $rootScope.searchNullValues.filter(
            //                             (t) => t === 'customFields',
            //                         );
            //                 }
            //             }
            //         }
            //     });
            //     candidate.search.params.customFields = candidate.search.params.customFields.filter(
            //         (item) => item.value !== 'no_value',
            //     );
            // }

            // if ($rootScope.searchNullCustomFields)
            //     candidate.search.params.searchNullCustomFields = [...$rootScope.searchNullCustomFields];

            // if ($rootScope.searchNullValues && $rootScope.searchNullValues.length) {
            //     candidate.search.params.searchNullValues = $rootScope.searchNullValues;
            //     $rootScope.searchNullValues.forEach((field) => {
            //         if (field === 'salary') {
            //             candidate.search.params['currency'] = null;
            //             candidate.search.params['salaryTo'] = null;
            //         }
            //         if (field !== 'customFields') {
            //             candidate.search.params[field] = null;
            //         }
            //     });
            // } else if (candidate.search.params.searchNullValues) {
            //     candidate.search.params.searchNullValues = [];
            // }
            //set search value with breakets in search field
            search.fields.boolean.value = _wrapWordsInBrackets(search.fields.boolean.getValue());

            if (search) candidate.search.searchValues = angular.copy(search.fields);
            if (customFieldsSearch) candidate.search.customFieldsValues = angular.copy(customFieldsSearch.fields);
        };

        candidate.getSearchFieldsTriggered = function (search) {
            const searchFieldsTriggered = {};
            for (let param in candidate.search.params) {
                if (candidate.search.params[param]) {
                    let key =
                        param === 'candidateGroupIds'
                            ? 'candidateGroup'
                            : param === 'responsibleIds'
                            ? 'responsible'
                            : param === 'containsWord'
                            ? 'matchType'
                            : param === 'dateFrom' || param === 'dateTo'
                            ? 'date'
                            : param;
                    if (search.fields.hasOwnProperty(key)) {
                        searchFieldsTriggered[key] = search.fields[key];
                    }
                }
            }
            return searchFieldsTriggered;
        };

        candidate.setCandidateFilterValues = function (params) {
            return new Promise(function (resolve, reject) {
                candidate.saveCandidateFilterValues(
                    params,
                    function (resp) {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };

        candidate.getCandidateFilterTemplate = function () {
            return new Promise((resolve, reject) => {
                candidate.candidateFilterTemplate(
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };

        candidate.createExcelByStage = function (params) {
            return new Promise((resolve, reject) => {
                candidate.excelByStage(
                    params,
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => {
                        reject(error);
                    },
                );
            });
        };

        candidate.onChangeOpenStatus = function (candidateId, isOpened) {
            if (isOpened === 'N' || !isOpened) candidate.changeOpenStatus({ candidateId: candidateId }, (resp) => {});
        };

        candidate.excel = function (params) {
            return new Promise((resolve, reject) => {
                candidate.createExcel(
                    params,
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => {
                        reject(error);
                    },
                );
            });
        };

        candidate.onGetCandidateProperties = function (params) {
            return new Promise((resolve, reject) => {
                candidate.getCandidateProperties(
                    params,
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };

        candidate.onGetCV = function (params) {
            return new Promise((resolve, reject) => {
                candidate.getCV(
                    params,
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };

        candidate.onGetCvArchive = function (params) {
            return new Promise((resolve, reject) => {
                candidate.getCvArchive(
                    params,
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };

        candidate.onGetOneCV = new PromiseWrapper('getCV');
        candidate.onGetCvMany = new PromiseWrapper('getCvArchive');
        candidate.onCreateExcel = new PromiseWrapper('createExcel');

        candidate.candidateContacts = function () {
            const contacts = {
                skype: [],
                mphone: [],
                email: [],
                telegram: [],
                linkedin: [],
                facebook: [],
                homepage: [],
                github: [],
                behance: [],
                viber: [],
                whatsApp: [],
                djinni: [],
                other: [],
            };
            angular.forEach(Object.keys(contacts), (contact) => {
                if (contact === 'mphone' || contact === 'email' || contact === 'linkedin')
                    contacts[contact].push({
                        type: contact,
                        value: '',
                        default: false,
                    });
            });
            return contacts;
        };

        candidate.validateName = (name) => {
            if (name.length > 50) return name.slice(0, 50);
            else
                return name
                    .replace(regExpForName, '')
                    .replace(/\s\s+/g, ' ')
                    .replace(/-{2,}/g, '-')
                    .replace(/'{2,}/g, "'")
                    .replace(/\s?-\s?/g, '-')
                    .replace(/\s?'\s?/g, "'");
        };
        candidate.validatePosition = (name) => {
            if (name && name.length > 100) return name.slice(0, 100);
            else return name;
        };
        candidate.candidatesLoaded = true;

        candidate.setCandidatesLoaded = (condition) => {
            candidate.candidatesLoaded = condition;
        };

        candidate.getCandidatesLoaded = function () {
            return candidate.candidatesLoaded;
        };

        candidate.selectFavoriteContacts = ($scope, event, contact, index) => {
            const isContactDefaultSelected = !contact.default;
            Object.keys($scope.contacts).forEach((type) => {
                $scope.contacts[type].forEach((item, i) => {
                    if (i === index && item.value === contact.value && item.type === contact.type) {
                        item.default = !item.default;
                    } else if (
                        isContactDefaultSelected &&
                        ((contact.type === 'email' && item.type === 'email') || item.type !== 'email') &&
                        !(contact.type === 'email' && item.type !== 'email' && item.default)
                    ) {
                        item.default = false;
                    }
                });
            });
        };

        candidate.onSaveContacts = function (params) {
            return new Promise((resolve, reject) => {
                candidate.saveContacts(
                    params,
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };

        candidate.onAutocompleteAllOrigins = function (params) {
            return new Promise((resolve, reject) => {
                candidate.autocompleteAllOrigins(
                    params,
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };

        candidate.onMassChangeOrigin = new PromiseWrapper('massChangeOrigin');
        candidate.onMassChangePosition = new PromiseWrapper('massChangePosition');
        candidate.onMassChangeResponsibles = new PromiseWrapper('massChangeResponsibles');
        candidate.getAutocompleteCandidates = new PromiseWrapper('autocompleteCandidates');

        function PromiseWrapper(request) {
            return function (params) {
                return new Promise((resolve, reject) => {
                    candidate[request](
                        params,
                        (resp) => {
                            if (resp.status === 'ok') {
                                resolve(resp);
                            } else {
                                reject(resp);
                            }
                        },
                        (error) => reject(error),
                    );
                });
            };
        }

        return candidate;
    },
]);
