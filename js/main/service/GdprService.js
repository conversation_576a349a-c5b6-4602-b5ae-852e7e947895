angular.module('services.gdpr', ['ngResource', 'ngCookies']).factory('Gdpr', [
    '$resource',
    'serverAddress',
    'notificationService',
    '$rootScope',
    'Service',
    function ($resource, serverAddress, notificationService, $rootScope) {
        var gdpr = $resource(
            serverAddress + '/pdcs/:param',
            { param: '@param' },
            {
                getSettings: {
                    method: 'GET',
                    params: {
                        param: 'get',
                    },
                },
                changeAutomaticSending: {
                    method: 'POST',
                    params: {
                        param: 'changeBlockAutomaticSending',
                    },
                },
                changeBlockNotify: {
                    method: 'POST',
                    params: {
                        param: 'changeBlockNotify',
                    },
                },
                changeBlockExpire: {
                    method: 'POST',
                    params: {
                        param: 'changeBlockExpire',
                    },
                },
                getBlockTemplate: {
                    method: 'GET',
                    params: {
                        param: 'getUserTemplate',
                    },
                },
                changeBlockTemplate: {
                    method: 'POST',
                    params: {
                        param: 'changeTemplate',
                    },
                },
                changeBlockUserEmail: {
                    method: 'POST',
                    params: {
                        param: 'changeEmailSettings',
                    },
                },
                sendMassGdprRequest: {
                    method: 'POST',
                    params: {
                        param: 'sendMassPdcsRequest',
                    },
                },
            },
        );

        gdpr.getCurrentSettings = new PromiseWrapper('getSettings');
        gdpr.changeAutoSend = new PromiseWrapper('changeAutomaticSending');
        gdpr.changeNotify = new PromiseWrapper('changeBlockNotify');
        gdpr.changeExpire = new PromiseWrapper('changeBlockExpire');
        gdpr.getTemplate = new PromiseWrapper('getBlockTemplate');
        gdpr.changeTemplate = new PromiseWrapper('changeBlockTemplate');
        gdpr.changeUserEmail = new PromiseWrapper('changeBlockUserEmail');
        gdpr.sendMassGdprReq = new PromiseWrapper('sendMassGdprRequest');

        function PromiseWrapper(request) {
            return function (params) {
                return new Promise((resolve, reject) => {
                    gdpr[request](
                        params,
                        (resp) => {
                            if (resp.status === 'ok') {
                                resolve(resp);
                            } else {
                                notificationService.error(resp.message);
                                $rootScope.loading = false;
                                reject(resp);
                            }
                        },
                        (error) => {
                            $rootScope.loading = false;
                            reject(error);
                        },
                    );
                });
            };
        }
        return gdpr;
    },
]);
