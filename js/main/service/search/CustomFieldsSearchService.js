angular.module('services.search.customFieldsSearchService', []).factory('CustomFieldsSearchService', [
    'Service',
    '$filter',
    'searchField',
    'searchService',
    '$rootScope',
    function (Service, $filter, searchField, searchService, $rootScope) {
        class Field extends searchField {
            constructor({ id, name, type, state, data, placeholder, path }) {
                super({ id, name, type, state, data, placeholder, path });
                this.id = id;
            }
        }

        class SelectField extends Field {
            constructor({ id, name, type, state, data, placeholder, path }) {
                super({ id, name, type, state, data, placeholder, path });
            }

            getLabelValue() {
                if (this.noValue) return 'no_value';

                return this.value && this.value.fieldParamId
                    ? Service.getObjectPropertyValueByPath(this, this.path.label)
                    : null;
            }

            getValue() {
                return this.value && this.value.fieldParamId
                    ? Service.getObjectPropertyValueByPath(this, this.path.value)
                    : null;
            }
        }

        class StringField extends Field {
            constructor({ id, name, type, state, data, placeholder, path }) {
                super({ id, name, type, state, data, placeholder, path });
            }

            getLabelValue() {
                if (this.noValue) return 'no_value';
                return this.value;
            }

            resetCheckbox() {
                this.state.isActive = true;
            }
        }

        class DateField extends Field {
            constructor({ id, name, type, state, data, placeholder, path }) {
                super({ id, name, type, state, data, placeholder, path });
            }

            resetCheckbox() {
                this.state.isActive = true;
            }

            changeState({ name, callback }) {
                this.state.isSelected = !this.state.isSelected;
                this.reset({ name, callback });
            }

            getLabelValue() {
                if (this.noValue || this.value === 'no_value') return 'no_value';

                if (!this.value) return null;

                if (this.type === 'date' || this.type === 'datetime') {
                    return $filter(this.type === 'date' ? 'dateFormatRedesign' : 'dateTimeFormatRedesign')(
                        Service.getObjectPropertyValueByPath(this, this.path.label),
                        $rootScope.currentLang,
                    );
                }
            }

            getValue() {
                if (this.value && this.value !== 'no_value') {
                    if (this.type === 'date')
                        return this.value - this.value.getMinutes() * 60000 - this.value.getSeconds() * 1000;
                    else return this.value.getTime();
                } else return null;
            }
        }

        class CustomFields extends searchService {
            constructor() {
                super();
                this.fields = {};
            }

            getFieldsLength() {
                return Object.keys(this.fields).length;
            }

            addField({
                id,
                name,
                data,
                type,
                state = { isActive: true, isSelected: false },
                placeholder = '',
                path = { label: 'value', value: 'value' },
            }) {
                switch (type) {
                    case 'select':
                        this.fields[name] = new SelectField({
                            id,
                            name,
                            type,
                            state,
                            data,
                            placeholder,
                            path,
                        });
                        break;
                    case 'string':
                        this.fields[name] = new StringField({
                            id,
                            name,
                            type,
                            state,
                            data,
                            placeholder,
                            path,
                        });
                        break;
                    case 'date':
                    case 'datetime':
                        this.fields[name] = new DateField({
                            id,
                            name,
                            type,
                            state,
                            data,
                            placeholder,
                            path,
                        });
                        break;
                    default:
                        this.fields[name] = new Field({
                            id,
                            name,
                            type,
                            state,
                            data,
                            placeholder,
                            path,
                        });
                }
            }

            getFieldsSelectedState({ every, some }) {
                if (every) {
                    if (!Object.entries(this.fields).length) return false;
                    return Object.entries(this.fields).every(([name, field]) => {
                        return field.state.isSelected;
                    });
                } else {
                    return Object.entries(this.fields).some(([name, field]) => {
                        return field.state.isSelected;
                    });
                }
            }

            getValues() {
                return Object.entries(this.fields)
                    .filter(([name, field]) => {
                        return field.getValue() || field.noValue;
                    })
                    .map(([name, field]) => {
                        if (field.noValue) {
                            return {
                                field: {
                                    fieldId: field.id,
                                    title: field.placeholder,
                                    type: field.type,
                                },
                                noValue: true,
                            };
                        }

                        switch (field.type) {
                            case 'datetime':
                            case 'date':
                                return {
                                    field: {
                                        fieldId: field.id,
                                        title: field.placeholder,
                                        type: field.type,
                                    },
                                    dateTimeValue: field.getValue(),
                                };
                            default:
                                return {
                                    field: {
                                        fieldId: field.id,
                                        title: field.placeholder,
                                        type: field.type,
                                    },
                                    value: field.getValue(),
                                };
                        }
                    });
            }

            getFieldsState(every = true) {
                const fields = Object.entries(this.fields);

                if (every) {
                    return fields.every(([name, field]) => {
                        return field.state.isSelected;
                    });
                } else {
                    return fields.some(([name, field]) => {
                        return field.state.isSelected;
                    });
                }
            }
        }

        return CustomFields;
    },
]);
