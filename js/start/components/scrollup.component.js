class scrollupComponentCtrl {
    constructor(
        $uibModal,
        $translate,
        notificationService,
        $state,
        $rootScope,
        $scope,
        Candidate,
        Company,
        serverAddress,
        $filter,
        $document,
        $window,
    ) {
        this.$uibModal = $uibModal;
        this.$translate = $translate;
        this.notificationService = notificationService;
        this.$state = $state;
        this.$rootScope = $rootScope;
        this.$scope = $scope;
        this.candidateService = Candidate;
        this.companyService = Company;
        this.serverAddress = serverAddress;
        this.$filter = $filter;
        this.$document = $document;
        this.$window = $window;
    }

    $onInit() {
        this.initScroll();
    }

    $onDestroy() {
        this.$document.off('scroll', this.classHandler);
    }

    getPosition() {
        return window.pageYOffset || document.documentElement.scrollTop;
    }

    classHandler() {
        const offset = 100;
        const scrollUp = document.querySelector('.scroll-up');

        if (!scrollUp) return;

        if (this.$window.scrollY > offset) {
            scrollUp.classList.add('scroll-up_active');
        } else {
            scrollUp.classList.remove('scroll-up_active');
        }
    }

    initScroll() {
        this.$document.on('scroll', this.classHandler.bind(this));
    }

    scrollUp() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}
const scrollupComponentDefinition = {
    template: `<div class="scroll-up" ng-click="vm.scrollUp()"><img alt="Прокрутить вверх" src="/images/sprite/up_racket.png"></div>`,
    controller: scrollupComponentCtrl,
    controllerAs: 'vm',
    bindings: {},
};
component.component('scrollupComponent', scrollupComponentDefinition);
