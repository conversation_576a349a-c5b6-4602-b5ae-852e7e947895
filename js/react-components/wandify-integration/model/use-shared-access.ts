import { type APISuccess } from 'common/types/api/api';
import { useState } from 'react';
import { changeSharedAccessQuery, getSharedAccessQuery } from 'react-components/wandify-integration/api/queries';
import useSWR from 'swr';

interface ReturnType {
    isLoading: boolean;
    sharedAccess: boolean | null | undefined;
    changeSharedAccess: (state: boolean) => Promise<APISuccess | null>;
}

export const useSharedAccess = (): ReturnType => {
    const [isMutating, setIsMutating] = useState(false);

    const {
        isLoading,
        data: sharedAccess,
        mutate,
    } = useSWR('/shareAccess', getSharedAccessQuery, {
        revalidateOnFocus: false,
    });

    const changeSharedAccess = async (allow: boolean): Promise<APISuccess | null> => {
        setIsMutating(true);

        return await changeSharedAccessQuery(allow)
            .then(async (resp): Promise<APISuccess | null> => {
                await mutate();
                return resp;
            })
            .finally(() => {
                setIsMutating(false);
            });
    };

    return { isLoading: isLoading || isMutating, sharedAccess, changeSharedAccess };
};
