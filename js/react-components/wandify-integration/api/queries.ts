import axios, { AxiosError, type AxiosResponse } from 'axios';
import { ResponseStatuses } from 'common/enums/api';
import { displayNotification } from 'common/helpers/displayNotification';
import { type APISuccess, type BaseResponse } from 'common/types/api/api';
import { type ApiInfoType } from 'react-components/wandify-integration/lib/types/integration';
import { type UpdatePayloadType } from 'react-components/wandify-integration/lib/types/update-payload-type';
import { type UpdateResult } from 'react-components/wandify-integration/lib/types/update-result';
import { WandifyErrorCodes } from 'react-components/wandify-integration/lib/types/wandify-error-codes.enum';

const wandifyBaseUrl = '/hr/wandify';

const wandifyClient = axios.create({
    baseURL: wandifyBaseUrl,
});

wandifyClient.interceptors.response.use(
    async (response: AxiosResponse<BaseResponse>) => {
        if (response.data.status === ResponseStatuses.ERROR) {
            throw new AxiosError(response.data.message || `Error in API call: ${wandifyBaseUrl}`, response.data.code);
        }
        return response;
    },
    async (error: AxiosError) => {
        throw error;
    },
);

const handleError = (error: AxiosError): null => {
    if (error.code === WandifyErrorCodes.notIntegratedWandify) {
        return null;
    }

    if (error.code === WandifyErrorCodes.noAvailableCreditsOnWandify) {
        displayNotification(error.message, false);
        return null;
    }

    if (error.code === AxiosError.ERR_NETWORK) {
        displayNotification('You don’t have internet connection', false);
        return null;
    }

    if (error.message) {
        displayNotification(error.message, false);
        console.error(error.message);
    } else {
        console.error(`Unexpected error in API call ${wandifyBaseUrl}`, error);
    }

    return null;
};

const getIntegrationInfo = async (): Promise<ApiInfoType | null | undefined> => {
    return await wandifyClient
        .get<APISuccess<ApiInfoType> | null>('')
        .then((resp) => {
            if (resp.data?.object) {
                return resp.data.object;
            }
        })
        .catch(handleError);
};

const integrateAccountQuery = async (payload: { wandifyApiKey: string }): Promise<ApiInfoType | null | undefined> => {
    return await wandifyClient
        .post<APISuccess<ApiInfoType> | null>('/integrateAccount', { wandifyApiKey: payload.wandifyApiKey })
        .then((resp) => {
            if (resp.data?.object) {
                return resp.data.object;
            }
        })
        .catch(handleError);
};

const deactivateIntegrationQuery = async (): Promise<APISuccess | null> => {
    return await wandifyClient
        .get<APISuccess | null>('/deactivateIntegration')
        .then((resp) => resp.data)
        .catch(handleError);
};

const updateProfileQuery = async (payload: UpdatePayloadType): Promise<UpdateResult | null> => {
    return await wandifyClient
        .post<UpdateResult | null>('/candidate/updateProfile', payload)
        .then((resp) => resp.data)
        .catch((error: AxiosError) => {
            if (error.code === 'noAvailableCreditsOnWandify') {
                handleError(error);
                return { status: 'notUpdated', code: WandifyErrorCodes.noAvailableCreditsOnWandify };
            }

            return handleError(error);
        });
};

const updateContactsQuery = async (payload: UpdatePayloadType): Promise<UpdateResult | null> => {
    return await wandifyClient
        .post<UpdateResult | null>('/candidate/updateContacts', payload)
        .then((resp) => resp.data)
        .catch((error: AxiosError) => {
            if (error.code === 'noAvailableCreditsOnWandify') {
                handleError(error);
                return { status: 'notUpdated', code: WandifyErrorCodes.noAvailableCreditsOnWandify };
            }

            return handleError(error);
        });
};

const getSharedAccessQuery = async (): Promise<boolean | null> => {
    return await wandifyClient
        .get<APISuccess>('/shareAccess')
        .then((resp) => Boolean(resp.data.object))
        .catch(handleError);
};

const changeSharedAccessQuery = async (allow: boolean): Promise<APISuccess | null> => {
    return await wandifyClient
        .post<APISuccess | null>('/shareAccess', {}, { params: { allow } })
        .then((resp) => resp.data)
        .catch(handleError);
};

export {
    changeSharedAccessQuery,
    deactivateIntegrationQuery,
    getIntegrationInfo,
    getSharedAccessQuery,
    integrateAccountQuery,
    updateContactsQuery,
    updateProfileQuery,
};
