import type { AngularRootScope } from 'common/types/angular-types';
import CandidateProfileIcon from 'images/redesign/svg-icons/wandify/candidate-info.svg';
import React, { type FC, useState } from 'react';
import { FullPageLoader } from 'react-components/common/full-page-loader/full-page-loader';
import { showUpdateResultToast } from 'react-components/wandify-integration/lib/helpers/show-update-result-toast';
import { useUpdate } from 'react-components/wandify-integration/model/use-update';
import { RegistrationSuggestionModal } from 'react-components/wandify-integration/ui/registration-suggestion-modal/registration-suggestion-modal';
import { getService } from 'react-in-angularjs';

import classes from './styles.module.scss';

interface PropsType {
    candidateId: string;
    onSuccessUpdate: () => void;
}

export const WandifyOneCandidateUpdateProfile: FC<PropsType> = ({ candidateId, onSuccessUpdate }) => {
    const t = getService('$translate').instant as AngularRootScope['translate'];
    const { isLoading, updateProfile } = useUpdate();
    const [isModalOpened, setIsModalOpened] = useState(false);

    const handleButtonClick = (): void => {
        void updateProfile([candidateId]).then((resp) => {
            if (!resp) {
                setIsModalOpened(true);
                return;
            }

            showUpdateResultToast({
                type: 'profile',
                result: resp,
                candidatesIds: [candidateId],
            });
            if (resp?.status === 'ok') {
                onSuccessUpdate();
            }
        });
    };

    return (
        <div className={classes.container}>
            {isLoading && <FullPageLoader />}

            <CandidateProfileIcon
                title={t("Update candidate's data (Such data can be updated:")}
                data-testid="wandify-candidate-update-button"
                className={classes.buttonIcon}
                onClick={handleButtonClick}
            />

            <RegistrationSuggestionModal
                isOpened={isModalOpened}
                onClose={() => {
                    setIsModalOpened(false);
                }}
            />
        </div>
    );
};
