import clsx from 'clsx';
import { displayNotification } from 'common/helpers/displayNotification';
import type { AngularRootScope } from 'common/types/angular-types';
import CandidateProfileIcon from 'images/redesign/svg-icons/wandify/candidate-info.svg';
import React, { type FC, useState } from 'react';
import { IconButton } from 'react-components/common/buttons/icon-button/icon-button';
import { FullPageLoader } from 'react-components/common/full-page-loader/full-page-loader';
import { showUpdateResultToast } from 'react-components/wandify-integration/lib/helpers/show-update-result-toast';
import { WandifyButtonElement } from 'react-components/wandify-integration/lib/types/wandify-button-element';
import { useUpdate } from 'react-components/wandify-integration/model/use-update';
import { RegistrationSuggestionModal } from 'react-components/wandify-integration/ui/registration-suggestion-modal/registration-suggestion-modal';
import { getService } from 'react-in-angularjs';

import classes from './styles.module.scss';

interface PropsType {
    buttonType?: WandifyButtonElement;
    candidatesIds: string[];
}

export const WandifyCandidatesListUpdateProfile: FC<PropsType> = ({
    buttonType = WandifyButtonElement.BUTTON,
    candidatesIds,
}) => {
    const t = getService('$translate').instant as AngularRootScope['translate'];
    const { isLoading, updateProfile } = useUpdate();
    const [isModalOpened, setIsModalOpened] = useState(false);

    const handleButtonClick = (): void => {
        if (!candidatesIds?.length) {
            displayNotification('Choose Candidates', false);
            return;
        }

        void updateProfile(candidatesIds).then((resp) => {
            if (!resp) {
                setIsModalOpened(true);
                return;
            }

            showUpdateResultToast({
                type: 'profile',
                result: resp,
                candidatesIds,
            });
        });
    };

    return (
        <div>
            {isLoading && <FullPageLoader />}

            <div title={t("Update candidate's data (Such data can be updated:")}>
                {buttonType === WandifyButtonElement.BUTTON ? (
                    <IconButton
                        renderIcon={() => <CandidateProfileIcon className={classes.buttonIcon} />}
                        onClick={handleButtonClick}
                    />
                ) : (
                    <CandidateProfileIcon className={clsx(classes.icon)} onClick={handleButtonClick} />
                )}
            </div>

            <RegistrationSuggestionModal
                isOpened={isModalOpened}
                onClose={() => {
                    setIsModalOpened(false);
                }}
            />
        </div>
    );
};
