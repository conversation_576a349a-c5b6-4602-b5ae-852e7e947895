import { ButtonTypes } from 'common/enums/button';
import type { AngularRootScope } from 'common/types/angular-types';
import { type CandidateType } from 'common/types/candidate';
import IconSearch from 'images/redesign/svg-icons/search.svg';
import React, { type FC, useState } from 'react';
import { ButtonWithIcon } from 'react-components/common/buttons/button-with-icon/button-with-icon';
import { useApiData } from 'react-components/wandify-integration/model/use-api-data';
import { RegistrationSuggestionModal } from 'react-components/wandify-integration/ui/registration-suggestion-modal/registration-suggestion-modal';
import { getService } from 'react-in-angularjs';

import classes from './styles.module.scss';

interface PropsType {
    candidate: CandidateType;
}

const WandifyDetailsButton: FC<PropsType> = ({ candidate }) => {
    const t = getService('$translate').instant as AngularRootScope['translate'];
    const { apiData } = useApiData();
    const [isModalOpened, setIsModalOpened] = useState(false);

    const handleButtonClick = (e: React.MouseEvent): void => {
        if (!apiData?.wandifyApiKey) {
            e.preventDefault();
            setIsModalOpened(true);
        }
    };
    return (
        <div>
            <ButtonWithIcon
                text={t('More info')}
                renderIcon={() => <IconSearch className={classes.searchIcon} />}
                className={classes.button}
                type={ButtonTypes.SECONDARY}
                onClick={handleButtonClick}
            />

            {!apiData?.wandifyApiKey && (
                <RegistrationSuggestionModal
                    isOpened={isModalOpened}
                    onClose={() => {
                        setIsModalOpened(false);
                    }}
                />
            )}
        </div>
    );
};
export { WandifyDetailsButton };
