import '@testing-library/jest-dom';

import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { displayNotification } from 'common/helpers/displayNotification';
import React from 'react';
import { showUpdateResultToast } from 'react-components/wandify-integration/lib/helpers/show-update-result-toast';
import { useUpdate } from 'react-components/wandify-integration/model/use-update';
import { WandifyKanbanUpdateProfile } from 'react-components/wandify-integration/ui/wandify-kanban-update-profile/wandify-kanban-update-profile';
import { getService } from 'react-in-angularjs';

// Mock dependencies
jest.mock('common/helpers/displayNotification');
jest.mock('react-components/wandify-integration/lib/helpers/show-update-result-toast');
jest.mock('react-components/wandify-integration/model/use-update');
jest.mock('react-in-angularjs');

describe('WandifyKanbanUpdateProfile', () => {
    const mockTranslate = jest.fn((key) => `Translated: ${key}`);
    const mockUpdateProfile = jest.fn();

    // Create a reusable mock candidate object with all required properties
    const mockCandidate = {
        candidateId: '123',
    };

    beforeEach(() => {
        jest.clearAllMocks();

        // Mock getService
        (getService as jest.Mock).mockReturnValue({
            instant: mockTranslate,
        });

        // Mock useUpdate hook
        (useUpdate as jest.Mock).mockReturnValue({
            isLoading: false,
            updateProfile: mockUpdateProfile,
        });
    });

    it('renders with default props', () => {
        render(<WandifyKanbanUpdateProfile candidates={[]} selectedLength={0} />);

        expect(screen.getByText('Translated: Update profile')).toBeInTheDocument();
        expect(screen.queryByTestId('full-page-loader')).not.toBeInTheDocument();
    });

    it('blocks button with no candidates', () => {
        render(<WandifyKanbanUpdateProfile candidates={[]} selectedLength={0} />);

        const button = screen.getByText('Translated: Update profile');
        expect(button).toBeDisabled();

        fireEvent.click(button);

        expect(displayNotification).not.toHaveBeenCalled();
        expect(mockUpdateProfile).not.toHaveBeenCalled();
    });

    it('calls updateProfile when clicking button with candidates', async () => {
        mockUpdateProfile.mockResolvedValue({ success: true });

        render(<WandifyKanbanUpdateProfile candidates={[{ candidateId: mockCandidate }]} selectedLength={1} />);

        fireEvent.click(screen.getByText('Translated: Update profile'));

        expect(mockUpdateProfile).toHaveBeenCalledWith(['123']);
        await waitFor(() => {
            expect(showUpdateResultToast).toHaveBeenCalledWith({
                type: 'profile',
                result: { success: true },
                candidatesIds: ['123'],
            });
        });
    });

    it('opens registration modal when updateProfile returns falsy value', async () => {
        mockUpdateProfile.mockResolvedValue(null);

        render(<WandifyKanbanUpdateProfile candidates={[{ candidateId: mockCandidate }]} selectedLength={1} />);

        fireEvent.click(screen.getByText('Translated: Update profile'));

        await waitFor(() => {
            expect(screen.getByText("Translated: You don't have Wandify integration yet")).toBeInTheDocument();
        });
        expect(showUpdateResultToast).not.toHaveBeenCalled();
    });

    it('shows loader when isLoading is true', () => {
        (useUpdate as jest.Mock).mockReturnValue({
            isLoading: true,
            updateProfile: mockUpdateProfile,
        });

        render(<WandifyKanbanUpdateProfile candidates={[{ candidateId: mockCandidate }]} selectedLength={1} />);

        expect(screen.getByTestId('full-page-loader')).toBeInTheDocument();
    });

    it('has the correct title attribute', () => {
        render(<WandifyKanbanUpdateProfile candidates={[{ candidateId: mockCandidate }]} selectedLength={1} />);

        const titleElement = screen.getByTitle("Translated: Update candidate's data (Such data can be updated:");
        expect(titleElement).toBeInTheDocument();
    });
});
