import { angularizeDirectiveFixed } from 'common/helpers/angularize-directive-fixed';
import { SelectSortComponent } from 'react-components/vacancy/select-sort-component/select-sort-component';

angularizeDirectiveFixed(SelectSortComponent, 'selectSortComponent', angular.module('RecruitingApp'), {
    currentLang: '<',
    placeholder: '<',
    options: '<',
    onChange: '<',
    selectedValue: '<',
    translateOptions: '<',
    disabled: '<',
});
