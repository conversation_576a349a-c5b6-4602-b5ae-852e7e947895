import clsx from 'clsx';
import { SelectPosition } from 'common/enums/select';
import { angularizeDirectiveFixed } from 'common/helpers/angularize-directive-fixed';
import { variables } from 'common/styles/script-variables';
import { type LanguageTranslationType } from 'common/types/vacancy/languages';
import CalendarStartIcon from 'images/redesign/svg-icons/calendar-start.svg';
import RaceFlagIcon from 'images/redesign/svg-icons/race-flag.svg';
import React, { type FC } from 'react';
import { PersonAvatarComponent } from 'react-components/person-avatar-component/person-avatar-component';
import { VacancyLanguagesList } from 'react-components/vacancy/vacancy-languages-list/vacancy-languages-list';
import { VacancyPriority } from 'react-components/vacancy/vacancy-priority/vacancy-priority';
import { VacancySkillsList } from 'react-components/vacancy/vacancy-skills-list/vacancy-skills-list';
import { VacancyStatusDropdown } from 'react-components/vacancy/vacancy-status-dropdown/vacancy-status-dropdown';
// @ts-ignore
import { getService } from 'react-in-angularjs';
import { Tooltip } from 'react-tooltip';

import classes from './styles.module.scss';

interface PropsType {
    currentLang: string;
    vacanciesList: any[];
    onChangeStatus: (status: string, vacancy: any, modelIndex: number) => void;
    allLanguages: LanguageTranslationType[];
}

const VacanciesCardsListComponent: FC<PropsType> = (props: any) => {
    const { currentLang, vacanciesList, onChangeStatus, allLanguages, $root } = props;

    const translate = getService('$translate').instant;
    const $filter = getService('$filter');

    function getVacancyLocation(vacancy: any): string | undefined {
        let city;
        if (vacancy.region) {
            switch (currentLang) {
                case 'en':
                    city = vacancy.region?.googlePlaceId?.cityEn;
                    break;
                case 'ua':
                    city = vacancy.region?.googlePlaceId?.cityUa;
                    break;
                case 'pl':
                    city = vacancy.region?.googlePlaceId?.cityPl;
                    break;
                case 'ru':
                    city = vacancy.region?.googlePlaceId?.cityRu;
                    break;
            }
        }

        const translatedEmploymentType = translate(vacancy.employmentType);

        if (!vacancy.employmentType) {
            return;
        }

        if (vacancy.employmentType === 'remote' && !vacancy.region) {
            return translatedEmploymentType.charAt(0).toUpperCase() + translatedEmploymentType.slice(1);
        } else if (vacancy.region && vacancy.employmentType !== 'remote') {
            return city;
        } else if (vacancy.region?.city && vacancy.employmentType === 'remote') {
            return `${translatedEmploymentType.charAt(0).toUpperCase() + translatedEmploymentType.slice(1)}, ${city}`;
        } else return `${translatedEmploymentType.charAt(0).toUpperCase() + translatedEmploymentType.slice(1)}`;
    }

    if (!vacanciesList?.length) return null;
    return (
        <div className={classes.vacanciesList}>
            {vacanciesList.length && (
                <>
                    <div className={classes.customBorderTop}></div>
                    <div className={classes.overlapper}></div>
                </>
            )}
            {vacanciesList.length &&
                vacanciesList.map((vacancy: any, index: number) => (
                    <div className={classes.listItem} key={vacancy.vacancyId}>
                        <div className={classes.leftSection}>
                            <a href={`#/vacancies/${vacancy.localId}`} className={classes.vacancyTitle}>
                                {vacancy.position}
                            </a>
                            <div className={classes.vacancyLocation}>
                                <span>{getVacancyLocation(vacancy)}</span>
                                <div className={classes.dotDivider}></div>
                                {$root.me.personParams.clientAccessLevel !== 'hide' ? (
                                    <a className={classes.clientLink} href={`#/clients/${vacancy.clientId.localId}`}>
                                        {vacancy.clientId.name}
                                    </a>
                                ) : (
                                    <span>{vacancy.clientId.name}</span>
                                )}
                            </div>

                            {vacancy.languages?.length && (
                                <VacancyLanguagesList
                                    vacancyId={vacancy.vacancyId}
                                    currentLang={currentLang}
                                    allLanguages={allLanguages}
                                    languagesList={vacancy.languages}
                                />
                            )}

                            {vacancy.skills?.length && (
                                <VacancySkillsList vacancyId={vacancy.vacancyId} skillsList={vacancy.skills} />
                            )}
                        </div>
                        <div className={classes.rightSection}>
                            <div className={classes.topControl}>
                                <VacancyPriority priority={vacancy.priority} className={classes.priority} />
                                <VacancyStatusDropdown
                                    onChange={(value) => onChangeStatus(value.value, vacancy, index)}
                                    value={{ name: vacancy.status, value: vacancy.status }}
                                    className={clsx(classes.statusDropdown)}
                                    position={
                                        vacanciesList.length > 5 && index !== 0
                                            ? SelectPosition.ABSOLUTE
                                            : SelectPosition.FIXED
                                    }
                                />
                            </div>
                            <div className={classes.datesSection}>
                                <div className={classes.date}>
                                    <CalendarStartIcon width={18} height={18} fill={variables['semi-black']} />
                                    {$filter('dateFormatShort')(vacancy.dc)}
                                </div>
                                {vacancy.dateFinish && $root.visibilityDateFinishVacancy(vacancy.dateFinish) && (
                                    <div className={clsx(classes.date, $root.dateFuture === false && classes.date_red)}>
                                        <RaceFlagIcon width={18} height={18} fill={variables['semi-black']} />
                                        {vacancy.dateFinish && $root.visibilityDateFinishVacancy(vacancy.dateFinish)}
                                    </div>
                                )}
                            </div>

                            <div className={classes.responsiblesSection}>
                                {vacancy.responsiblesPerson?.length &&
                                    vacancy.responsiblesPerson.length <= 2 &&
                                    vacancy.responsiblesPerson.map(({ responsible }: any) => (
                                        <PersonAvatarComponent
                                            key={vacancy.vacancyId + responsible.personId}
                                            fullName={
                                                $root.useAmericanNameStyle
                                                    ? responsible.fullNameEn
                                                    : responsible.fullName
                                            }
                                            userId={responsible.personId}
                                            avatarId={responsible.avatarId}
                                        />
                                    ))}

                                {vacancy.responsiblesPerson?.length && vacancy.responsiblesPerson.length > 2 && (
                                    <>
                                        {vacancy.responsiblesPerson.slice(0, 2).map(({ responsible }: any) => (
                                            <PersonAvatarComponent
                                                key={vacancy.vacancyId + responsible.personId}
                                                fullName={
                                                    $root.useAmericanNameStyle
                                                        ? responsible.fullNameEn
                                                        : responsible.fullName
                                                }
                                                userId={responsible.personId}
                                                avatarId={responsible.avatarId}
                                            />
                                        ))}
                                        <span
                                            id="countOfRest"
                                            className={classes.restResponsible}
                                            data-tooltip-id={vacancy.vacancyId}
                                            data-tooltip-place="bottom"
                                        >
                                            +{vacancy.responsiblesPerson.length - 2}
                                        </span>
                                        <Tooltip
                                            id={vacancy.vacancyId}
                                            noArrow={true}
                                            clickable={true}
                                            opacity={1}
                                            positionStrategy={'fixed'}
                                            className={classes.restResponsibleTooltip}
                                        >
                                            <div className={classes.restResponsibleTooltipContent}>
                                                {vacancy.responsiblesPerson.slice(2).map(({ responsible }: any) => (
                                                    <PersonAvatarComponent
                                                        key={vacancy.vacancyId + responsible.personId}
                                                        fullName={
                                                            $root.useAmericanNameStyle
                                                                ? responsible.fullNameEn
                                                                : responsible.fullName
                                                        }
                                                        userId={responsible.personId}
                                                        avatarId={responsible.avatarId}
                                                    />
                                                ))}
                                            </div>
                                        </Tooltip>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                ))}
        </div>
    );
};

// @ts-ignore
angularizeDirectiveFixed(VacanciesCardsListComponent, 'vacanciesCardsListComponent', angular.module('RecruitingApp'), {
    currentLang: '<',
    vacanciesList: '<',
    onChangeStatus: '<',
    allLanguages: '<',
});

export { VacanciesCardsListComponent };
