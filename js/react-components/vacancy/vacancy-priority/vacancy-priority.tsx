import clsx from 'clsx';
import { angularizeDirectiveFixed } from 'common/helpers/angularize-directive-fixed';
import React, { type FC } from 'react';
import { getService } from 'react-in-angularjs';

import classes from './styles.module.scss';

interface VacancyPriorityPropsType {
    currentLang?: string;
    priority: string;
    className?: string;
}

const VacancyPriority: FC<VacancyPriorityPropsType> = ({ priority, className }) => {
    const translate = getService('$translate').instant;

    return (
        <div className={clsx(classes.priorityWrapper, classes[`${priority}`], className)}>{translate(priority)}</div>
    );
};

// @ts-ignore
angularizeDirectiveFixed(VacancyPriority, 'vacancyPriority', angular.module('RecruitingApp'), {
    currentLang: '<',
    priority: '<',
    className: '<',
});

export { VacancyPriority };
