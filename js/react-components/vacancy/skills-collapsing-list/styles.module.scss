@import 'common/styles/mixins';
@import 'common/styles/global-variables.module';

.container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
}

.noSkills {
    display: flex;
    align-items: center;
    align-self: center;
    justify-content: center;
    min-height: 30px;

    &:after {
        display: block;
        width: 24px;
        height: 1px;
        content: '';
        background-color: #777777;
    }
}

.skillItem {
    white-space: nowrap;
}

.icon {
    cursor: pointer;
    transition: 0.2s all;
    @include control-icon-size;

    &_reversed {
        transform: rotate(180deg);
    }

    &:hover {
        fill: $main-black;
    }
}
