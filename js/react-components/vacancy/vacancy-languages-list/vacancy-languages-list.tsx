import { type LanguageTranslationType } from 'common/types/vacancy/languages';
import React, { type FC, useEffect, useLayoutEffect, useRef, useState } from 'react';
// @ts-ignore
import { getService } from 'react-in-angularjs';

import classes from './styles.module.scss';

interface PropsType {
    currentLang: string;
    vacancyId: string;
    languagesList: any[];
    allLanguages: LanguageTranslationType[];
}

const VacancyLanguagesList: FC<PropsType> = ({ currentLang, vacancyId, languagesList, allLanguages }) => {
    const [sortedLanguages, setSortedLanguages] = useState(languagesList);
    const [languagesLimit, setLanguagesLimit] = useState(languagesList.length);
    const languagesContainerRef = useRef<HTMLDivElement>(null);
    const translate = getService('$translate').instant;

    useEffect(() => {
        if (languagesList.length > 1 && languagesList.some(({ name }) => name === 'english')) {
            const value = languagesList.find(({ name }) => name === 'english');
            if (value) {
                setSortedLanguages([value, ...languagesList.filter(({ name }) => name !== 'english')]);
            }
        }
    }, []);

    useLayoutEffect(() => {
        if (!languagesContainerRef.current) return;

        const { height } = languagesContainerRef.current.getBoundingClientRect();

        if (height > 20) {
            setLanguagesLimit(languagesLimit - 1);
        }
    }, [languagesLimit, languagesContainerRef.current]);

    const getTranslatedLanguage = (lang: any): undefined | string => {
        if (!allLanguages) return;

        let language = '';

        switch (currentLang) {
            case 'en':
                language = allLanguages[lang.name].english;
                break;
            case 'ua':
                language = allLanguages[lang.name].ukrainian;
                break;
            case 'pl':
                language = allLanguages[lang.name].polish;
                break;
            case 'ru':
                language = allLanguages[lang.name].russian;
                break;
        }

        return `${language.charAt(0).toUpperCase() + language.slice(1)} ${translate(lang.level)}`;
    };

    return (
        <div ref={languagesContainerRef}>
            {sortedLanguages.length &&
                sortedLanguages.slice(0, languagesLimit).map((lang: any, index: number) => (
                    <span key={lang.languageId}>
                        <span>{getTranslatedLanguage(lang)}</span>
                        {index !== languagesLimit - 1 && <div className={classes.dotDivider}></div>}
                    </span>
                ))}
        </div>
    );
};

export { VacancyLanguagesList };
