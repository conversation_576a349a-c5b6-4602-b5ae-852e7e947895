@import 'common/styles/global-variables.module';
@import 'common/styles/mixins';

.action {
    color: $semi-black;

    .stateWrapper {
        display: inline-flex;
        gap: 4px;

        span {
            padding: 2px 4px;
            border-radius: 5px;
        }
    }

    .date {
        margin: 0 6px;
        font-size: $small-font-size;
        white-space: nowrap;
    }

    .red {
        color: $red;
    }

    .status {
        color: #757d4e;
        background-color: #b7c37b;

        &.inwork {
            color: #76a563;
            background-color: #b5d6a8;
        }

        &.completed {
            color: #b3b3b3;
            background-color: #f1f1f1;
        }
    }

    .stage {
        color: $main-black;
        background-color: #ffd850b2;

        &.interview,
        &.hr_interview,
        &.tech_interview,
        &.interview_with_the_boss,
        &.interview_with_the_client {
            background-color: #fab266b2;
        }

        &.removed {
            background-color: #e2e2e2;
        }

        &.approved {
            background-color: #9fd889b2;
        }

        &.refuse,
        &.notafit,
        &.removed,
        &.deleted,
        &.canceled,
        &.archived,
        &.no_response,
        &.no_contacts,
        &.declinedoffer,
        &.offer_declined,
        &.is_not_looking_for_job,
        &.accepted_counter_offer,
        &.found_another_job,
        &.probation_failure {
            background-color: #fa6f66b2;
        }
    }

    .interview {
        background-color: #fab266b2;
    }

    .passed {
        text-decoration: line-through;
    }

    .chevron {
        margin-left: 4px;
        cursor: pointer;
        filter: $filter-semi-black;

        &:hover {
            filter: $filter-main-black;
        }
    }

    img {
        width: 18px;
        filter: $filter-semi-black;
    }

    a {
        @include link-green();
    }

    .description {
        a {
            @include link-blue();
        }
    }
}
