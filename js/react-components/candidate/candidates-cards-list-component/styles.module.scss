@import 'common/styles/global-variables.module';
@import 'common/styles/mixins';
@import 'sass/redesign/uib-tooltip';

.dotDivider {
    display: inline-block;
    width: 8px;
    height: 8px;
    padding: 4px;
    background-color: $dark-grey;
    border-radius: 50px;
}

.dotItems {
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
}

.candidatesList {
    overflow-y: auto;

    .tooltip {
        z-index: 1;
        max-width: 300px;
        max-height: 190px;
        padding: 8px;
        overflow-y: auto;
        font-size: $main-font-size;
        color: $secondary-black;
        background-color: $main-white;
        border: 1px solid $border-grey;
        border-radius: 4px;
        box-shadow: $main-box-shadow;
    }

    .tooltipContent {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        max-width: 125px;
    }

    .flameIconTooltip {
        @include tooltip-info-black;
    }
}

.candidateCard {
    position: relative;
    display: flex;
    gap: 16px;
    align-items: center;
    padding: 16px 16px 16px 16px;
    color: $secondary-black;
    background-color: $main-white;
    border: solid transparent;
    border-width: 0 1px 1px;

    &:not(:last-child) {
        border-bottom-color: $border-grey;
    }

    @media screen and (max-width: calc($mobile-width-max + 100px)) {
        flex-wrap: wrap;
    }

    &:hover {
        .closeIcon {
            pointer-events: all;
            opacity: 1;
        }
    }

    &__borderTop {
        border-top: 1px solid transparent;
    }

    &__selected {
        border-color: $main-green;
    }

    &__selectedBorderBottom {
        border-bottom: 1px solid $main-green;
    }
}

.leftSide {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    align-self: flex-start;
}

.shortInfo {
    display: grid;
    grid-template-rows: repeat(3, 1fr);
    align-items: center;
    justify-items: center;
}

.countActiveVacancy {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    aspect-ratio: 1/1;
}

.rightSide {
    display: flex;
    flex-shrink: 0;
    gap: 8px;
    align-items: center;
    align-self: flex-start;
    margin-right: 0;
    margin-left: auto;
}

.center {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-self: flex-start;
    justify-content: flex-start;
}

.activeVacancies {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    aspect-ratio: 1/1;
    font-size: $secondary-font-size;
    color: $main-white;
    background-color: $dark-grey;
    border-radius: 50%;
}

.candidateAvatar {
    flex-shrink: 0;
    width: 100px;
    font-size: $heading-font-size;
}

.candidateHead {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;

    &__item {
        display: flex;
        align-items: center;
    }
}

.candidateName {
    margin: 0;
    font-size: $heading-font-size;
    color: $main-black;
    text-decoration: none;
    transition: color 0.1s ease;

    &:hover {
        color: $main-green;
    }
}

.candidateExperience {
    display: flex;
    gap: 8px;
    align-items: center;
}

.closeIcon {
    position: absolute;
    right: 10px;
    bottom: 10px;
    pointer-events: none;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    @include control-icon-size;

    @media screen and (max-width: $mobile-width-max) {
        display: none;
    }

    &:hover {
        fill: $main-black;
    }
}

.openStatus {
    color: $semi-black;
}

.flameIcon {
    align-self: baseline;
    width: 16px;
}
