import clsx from 'clsx';
import { variables } from 'common/styles/script-variables';
import { type AngularRootScope } from 'common/types/angular-types';
import { type CandidateType } from 'common/types/candidate';
import CloseIcon from 'images/redesign/svg-icons/close.svg';
import React, { type FC } from 'react';
import {
    CandidateGroupsMemoized,
    CandidateLanguage,
    CandidateOrigin,
    CandidateSkills,
} from 'react-components/candidate';
import { CandidateLastAction } from 'react-components/candidate/candidate-last-action/candidate-last-action';
import { CandidateScorecard } from 'react-components/candidate/candidate-scorecard/candidate-scorecard';
import { AttachedFilesComponent } from 'react-components/common/attached-files-component/attached-files-component';
import { CandidatePreviewMemoized } from 'react-components/common/candidate-preview/candidate-preview';
import { CheckboxComponentMemoized } from 'react-components/common/checkbox-component/checkbox-component';
import { DateFormatBase } from 'react-components/common/date-components/date-format-base/date-format-base';
import { PersonAvatarComponentMemoized } from 'react-components/person-avatar-component/person-avatar-component';
import { Tooltip } from 'react-tooltip';

import classes from './styles.module.scss';

interface PropsType {
    showFilePreview: any;
    toSentPreview: any;
    toScorecard: any;
    toFullInformation: (event: React.MouseEvent<HTMLAnchorElement>, candidate: CandidateType) => void;
    candidatesList: CandidateType[];
    onSelect: (e: React.ChangeEvent, candidate: CandidateType) => void;
    onDeleteCandidate: (candidate: CandidateType) => void;
    $root: AngularRootScope;
    translateFunc: AngularRootScope['translate'];
    sliderId: string;
}

const CandidatesCardsListComponent: FC<PropsType> = ({
    toSentPreview,
    showFilePreview,
    toScorecard,
    toFullInformation,
    candidatesList,
    onSelect,
    translateFunc,
    $root,
    onDeleteCandidate,
    sliderId,
}) => {
    const onDeleteIconClick = (candidate: CandidateType): void => {
        onDeleteCandidate(candidate);
    };

    return (
        <div className={classes.candidatesList}>
            {candidatesList?.map((candidate, index) => (
                <div
                    className={clsx(
                        classes.candidateCard,
                        candidate.added && classes.candidateCard__selected,
                        index === 0 && classes.candidateCard__borderTop,
                        candidatesList[index + 1]?.added && classes.candidateCard__selectedBorderBottom,
                    )}
                    key={candidate.candidateId}
                >
                    <div className={classes.leftSide}>
                        <CheckboxComponentMemoized
                            isChecked={candidate.added || false}
                            onChange={(e) => {
                                onSelect(e, candidate);
                            }}
                        />

                        <div className={classes.shortInfo}>
                            <div className={classes.countActiveVacancy}>
                                {candidate.countActiveVacancy && (
                                    <div
                                        title={translateFunc('vacancies_applied_for')}
                                        className={classes.activeVacancies}
                                    >
                                        {candidate.countActiveVacancy}
                                    </div>
                                )}
                            </div>
                            <CandidatePreviewMemoized
                                className="hidden_laptop"
                                candidate={candidate}
                                $root={$root}
                                translateFunc={translateFunc}
                            />
                            {candidate.files?.length && (
                                <AttachedFilesComponent translateFunc={translateFunc} files={candidate.files} />
                            )}
                        </div>
                    </div>

                    <PersonAvatarComponentMemoized
                        userId={candidate.localId}
                        avatarId={candidate.photo}
                        linkTo={'candidates'}
                        fullName={$root.getFullNameDueToStyle(candidate)}
                        className={classes.candidateAvatar}
                    />

                    <div className={classes.center}>
                        <div className={classes.candidateHead}>
                            <a
                                onClick={(event) => {
                                    toFullInformation(event, candidate);
                                }}
                                href={`!#/candidates/slide/${sliderId}/${candidate.localId}`}
                                className={clsx(
                                    classes.candidateName,
                                    candidate.openStatus === 'Y' ? classes.openStatus : '',
                                )}
                            >
                                {$root.getFullNameDueToStyle(candidate) || '(no name)'}
                            </a>

                            {(candidate.isOpenToWork || candidate.candidateExternalIntegration?.isOpenToWork) && (
                                <img
                                    data-tooltip-id="candidate-card-tooltip"
                                    data-tooltip-content={translateFunc('This candidate is now looking for a job')}
                                    data-tooltip-class-name={classes.flameIconTooltip}
                                    data-tooltip-place={'bottom-start'}
                                    className={classes.flameIcon}
                                    src="images/sprite/candidate-flame.svg"
                                    alt=""
                                />
                            )}

                            {candidate.position.trim() && (
                                <>
                                    <div className={classes.dotDivider}></div>
                                    <div className={classes.candidateHead__item}>
                                        <span>{candidate.position.trim()}</span>
                                    </div>
                                </>
                            )}
                            {candidate.languages?.length && (
                                <>
                                    <div className={classes.candidateHead__item}>
                                        <CandidateLanguage
                                            candidateLanguages={candidate.languages}
                                            allLanguages={$root.allLanguages}
                                            currentLang={$root.currentLang}
                                            translateFunc={translateFunc}
                                        />
                                    </div>
                                </>
                            )}
                        </div>

                        {(candidate.expirence || candidate.scoreCardResultDto || $root.scorecardInSearch()) && (
                            <div className={classes.candidateExperience}>
                                {candidate.expirence &&
                                    `${translateFunc('experience_assoc.' + candidate.expirence)} ${
                                        candidate.expirence === 'e00_no_experience'
                                            ? ''
                                            : translateFunc('of experience')
                                    }`}

                                {!!(
                                    candidate.expirence &&
                                    ($root.scorecardInSearch() || candidate.scoreCardResultDto)
                                ) && <div className={classes.dotDivider}></div>}

                                <CandidateScorecard
                                    avgScore={candidate.avgScore}
                                    scorecard={candidate.scoreCardResultDto}
                                    $root={$root}
                                    translateFunc={translateFunc}
                                />
                            </div>
                        )}

                        {candidate.groups?.length && <CandidateGroupsMemoized groups={candidate.groups} />}
                        {candidate.candidateSkills?.length ? (
                            <CandidateSkills skills={candidate.candidateSkills} />
                        ) : null}
                        {candidate.lastAction && (
                            <CandidateLastAction
                                toSentPreview={toSentPreview}
                                showFilePreview={showFilePreview}
                                toScorecard={toScorecard}
                                user={candidate}
                                $root={$root}
                                translateFunc={translateFunc}
                            />
                        )}
                    </div>

                    <div className={classes.rightSide}>
                        {(candidate.origin || candidate.source) && (
                            <CandidateOrigin
                                origin={candidate.origin || candidate.source}
                                translateFunc={translateFunc}
                            />
                        )}

                        <DateFormatBase
                            translateFunc={translateFunc}
                            currentLang={$root.currentLang}
                            date={candidate.dc}
                            withHours={false}
                            fullMonth={false}
                        />

                        <PersonAvatarComponentMemoized
                            userId={candidate.responsibleId}
                            fullName={$root.getFullNameDueToStyle(candidate.responsible)}
                            avatarId={candidate.responsible.avatarId}
                            linkTo={'users'}
                        />
                    </div>
                    <div title={translateFunc('Remove the candidate')}>
                        <CloseIcon
                            fill={variables['semi-black']}
                            className={classes.closeIcon}
                            onClick={() => {
                                onDeleteIconClick(candidate);
                            }}
                        />
                    </div>
                </div>
            ))}
            <Tooltip
                id={'candidate-card-tooltip'}
                noArrow={true}
                clickable={true}
                opacity={1}
                place={'bottom'}
                positionStrategy={'fixed'}
                className={classes.tooltip}
            ></Tooltip>
        </div>
    );
};

export { CandidatesCardsListComponent };
