@import 'common/styles/global-variables.module';
@import 'common/styles/mixins';

.baseIcon {
    flex-shrink: 0;
    width: 18px;
    height: 18px;
    fill: $dark-grey;
}

.personIcon {
    fill: $dark-grey;
    transition: fill 0.1s ease;

    &:hover {
        fill: $semi-black;
    }
}

.redClose {
    width: 18px;
    height: 18px;
    filter: $filter-main-red;
}

.closeIcon {
    position: absolute;
    top: 6px;
    right: 5px;
    width: 18px;
    height: 18px;
    cursor: pointer;
    filter: $filter-semi-black;

    &:hover {
        filter: $filter-main-black;
    }
}

.closeTooltipIcon {
    position: absolute;
    top: 5px;
    right: 4px;
    width: 18px;
    height: 18px;
    cursor: pointer;
    filter: $filter-semi-black;
    opacity: 0;

    &:hover {
        filter: $filter-main-black;
    }
}

.CheckButton {
    width: max-content;
    height: 26px;
    padding: 0 8px 0 6px;
    margin: 0 auto;
    font-size: 14px;

    img {
        filter: $filter-main-green !important;
    }
}

.wrapperAdvice {
    display: flex;
    align-items: center;
    justify-content: center;

    .advicePreviewTooltip {
        z-index: 10;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        min-width: auto;
        max-width: 815px;
        padding: 12px;
        overflow: hidden;
        font-size: $main-font-size;
        color: $main-black;
        cursor: default;
        background-color: $main-white;
        border: 1px solid $blue;
        border-radius: 8px;
        box-shadow: $main-box-shadow;

        &__Adaptive {
            max-width: fit-content;
        }

        &:hover {
            .closeTooltipIcon {
                opacity: 1;
            }
        }
    }

    .defaultTooltip {
        z-index: 15;
        max-width: 300px;
        padding: 6px 8px;
        font-size: 12px;
        text-align: left;
        background-color: #2e3235;
        box-shadow: 0 0 10px 3px rgba(0, 0, 0, 0.1);
    }
}

.AdviceTitle {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: $main-black;
    cursor: pointer;
    transition: 0.3s all;

    .Orange {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        aspect-ratio: 1 / 1;
        font-size: $small-font-size;
        color: $main-white;
        background-color: $vova-color;
        border-radius: 50%;
    }

    &.RowPosition {
        flex-direction: row;
        gap: 2px;
        align-items: center;

        .Orange {
            //margin-bottom: 1px;
        }
    }

    &:hover {
        color: $blue;
    }

    &.DisableHover:hover {
        color: $main-black;
        cursor: default;
    }
}

.BluePulse {
    border-radius: 30%;
    animation: pulse-blue 1s infinite;
}

.RedPulse {
    border-radius: 30%;
    animation: pulse-red 1s infinite;
}

.bodyTitle {
    margin-bottom: 10px;
    font-size: $heading-small-font-size;
    text-align: center;
}

.bodyContainer {
    position: relative;
    display: flex;
    flex-direction: row;
    gap: 12px;
    align-items: flex-start;
    width: 100%;

    &__img {
        width: 62px;

        img {
            top: 0;
            width: 62px;
        }
    }

    &__main {
        display: flex;
        flex-direction: column;
        gap: 12px;
        width: 100%;
        //padding-right: 15px;
        transition: 0.3s all;
    }
}

.head {
    display: flex;
    gap: 12px;
    align-items: center;
    color: $main-black;
}

.matchesWrapper {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
    color: $secondary-black;
}

.wrapper {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;
    color: $secondary-black;
}

.itemsWrapper {
    position: relative;
    background-color: white;

    &-enter {
        opacity: 0;
    }

    &-enter-active {
        opacity: 1;
        transition: opacity 500ms ease-in;
    }

    &-exit {
        opacity: 1;
    }

    &-exit-active {
        opacity: 0;
        transition: opacity 500ms ease-in;
    }
}

.item-enter {
    opacity: 0;
}

.item-enter-active {
    opacity: 1;
    transition: opacity 500ms ease-in;
}

.item-exit {
    opacity: 1;
}

.item-exit-active {
    opacity: 0;
    transition: opacity 500ms ease-in;
}

.items {
    display: flex;
    flex-direction: row;
    gap: 5px;
    align-items: center;

    &-enter {
        opacity: 0;
    }

    &-enter-active {
        opacity: 1;
        transition: opacity 500ms ease-in;
    }

    &-exit {
        opacity: 1;
    }

    &-exit-active {
        opacity: 0;
        transition: opacity 500ms ease-in;
    }
}

.edit {
    position: fixed;
    z-index: 2;
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
    min-width: 250px;
    max-width: 250px;
    margin-top: 5px;

    &__wrapper {
        display: flex;
        flex-direction: column;
        gap: 8px;
        min-width: 250px;
        max-width: 250px;
    }

    &__salary {
        display: flex;
        flex-direction: row;
        gap: 8px;
    }

    input {
        margin: 0 !important;
    }
}

.noCursor {
    pointer-events: none !important;
    cursor: default !important;
}

.notEnoughClasses {
    display: flex;
    flex-direction: row;
    gap: 5px;
    align-items: center;
    padding: 2px 6px;
    cursor: pointer;
    border: 1px solid $border-grey;
    border-radius: 8px;

    &:hover {
        .penIcon {
            filter: $filter-main-black;
        }
    }

    .LongText {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
    }
}

.blur {
    pointer-events: none;
    filter: blur(4px);
}

.redItem {
    padding: 3px 4px;
    background-color: $light-red;
    border-radius: 5px;
}

.blueItem {
    padding: 3px 4px;
    background-color: $light-blue;
    border-radius: 5px;
}

.position {
    color: $secondary-black;
}

.checkIcon {
    width: 18px;
    height: 18px;
    filter: $filter-main-green;
}

.penIcon {
    min-width: 18px;
    max-width: 18px;
    min-height: 18px;
    max-height: 18px;
    filter: $filter-semi-black;
}

@keyframes pulse-blue {
    0% {
        box-shadow: 0 0 0 0 rgba(51, 146, 234, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(51, 146, 234, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(51, 146, 234, 0);
    }
}

@keyframes pulse-red {
    0% {
        box-shadow: 0 0 0 0 rgba(214, 115, 84, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(214, 115, 84, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(214, 115, 84, 0);
    }
}
