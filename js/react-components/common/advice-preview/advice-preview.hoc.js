import { angularizeDirectiveFixed } from 'common/helpers/angularize-directive-fixed';
import { AdvicePreview } from './advice-preview';

angularizeDirectiveFixed(AdvicePreview, 'advicePreview', angular.module('RecruitingApp'), {
    itsStages: '<',
    itsKanban: '<',
    advice: '<',
    $root: '<',
    translateFunc: '<',
    className: '<',
    state: '<',
    refresh: '<',
    angularTrigger: '<',
    animation: '<',
    isTooltipOpened: '<',
    index: '<',
});
