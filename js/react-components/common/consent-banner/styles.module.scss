.container {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    padding: 15px;
    font-size: 14px;
    color: black;
    text-align: center;
    background-color: #f8f9fa;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.title {
    margin: 0;
}

.button {
    display: inline-block;
    padding: 8px 16px;
    margin: 6px 4px;
    font-size: 14px;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    border: none;
    border-radius: 4px;

    &:hover {
        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);
    }

    &:active {
        opacity: 0.5;
    }
}

.btnSuccess {
    color: white;
    background-color: #47ab43;
}

.btnGrayscale {
    color: black;
    background-color: #dfe1e5;
}

.btnOutline {
    color: #47ab43;
    background-color: #e6f4ea;
}

.cookieConsentOptions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    justify-content: center;
}

.customCheckbox {
    margin: 0;
}

.customCheckbox > input {
    position: absolute;
    z-index: -1;
    opacity: 0;
}

.customCheckbox > span {
    display: inline-flex;
    align-items: center;
    user-select: none;
    font-weight: normal;
}

.customCheckbox > span::before {
    display: inline-block;
    flex-grow: 0;
    flex-shrink: 0;
    width: 1em;
    height: 1em;
    margin-right: 0.3em;
    content: '';
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 50% 50%;
    border: 1px solid #adb5bd;
    border-radius: 0.25em;
}

.customCheckbox > input:not(:disabled) + span {
    cursor: pointer;
}

.customCheckbox > input:not(:disabled):not(:checked) + span:hover::before {
    border-color: #47ab43;
}

.customCheckbox > input:not(:disabled):active + span::before {
    background-color: #82cb80;
    border-color: #82cb80;
}

.customCheckbox > input:focus + span::before {
    box-shadow: 0 0 0 0.2rem rgba(143, 203, 141, 0.51);
}

.customCheckbox > input:focus:not(:checked) + span::before {
    border-color: #82cb80;
}

.customCheckbox > input:checked + span::before {
    background-color: #47ab43;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e");
    border-color: #47ab43;
}

.customCheckbox > input:disabled + span::before {
    background-color: #c9c9c9;
    border-color: #c9c9c9;
}

.consentPrivacy {
    margin-top: 8px;
}

.consentLink {
    color: #47ab43;
    text-decoration: none;
}
