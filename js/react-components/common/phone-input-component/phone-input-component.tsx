import 'react-international-phone/style.css';

import clsx from 'clsx';
import CloseIcon from 'images/redesign/svg-icons/close.svg';
import React, { type FC, useState } from 'react';
import { type CountryIso2, PhoneInput } from 'react-international-phone';

import classes from './styles.module.scss';

interface PropsType {
    defaultCountry?: CountryIso2 | undefined;
    placeholder?: string;
    value: string | undefined;
    onChange: (value: string) => void;
    isError: boolean;
}

export const PhoneInputComponent: FC<PropsType> = ({
    defaultCountry = 'ua',
    placeholder,
    value,
    onChange,
    isError,
}) => {
    const [isFocused, setIsFocused] = useState(false);

    const handleClear = (): void => {
        onChange('');
    };

    return (
        <div
            className={clsx(classes.root, { [classes.root__error]: isError, [classes.root__focused]: isFocused })}
            onFocus={() => {
                setIsFocused(true);
            }}
            onBlur={() => {
                setIsFocused(false);
            }}
        >
            <PhoneInput
                className={classes.container}
                value={value}
                onChange={onChange}
                placeholder={placeholder}
                disableDialCodePrefill
                defaultCountry={defaultCountry}
                countrySelectorStyleProps={{
                    style: { position: 'static' },
                    flagClassName: classes.flag,
                    dropdownStyleProps: { className: classes.dropdown, listItemClassName: classes.listItem },
                }}
                inputProps={{ className: classes.input }}
            />

            <CloseIcon className={classes.clearButton} onClick={handleClear} fill={'#909090'} width={18} height={18} />
        </div>
    );
};
