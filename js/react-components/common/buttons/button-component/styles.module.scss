@import 'common/styles/global-variables.module';

.Button {
    display: inline-flex;
    gap: 12px;
    align-items: center;
    justify-content: center;
    width: fit-content;
    height: 42px;
    padding: 0 16px;
    font-family: $main-font-family, serif;
    font-size: $main-font-size;
    font-weight: 400;
    line-height: 1em;
    text-align: center;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s linear;

    &__primary {
        color: $main-white;
        background-color: $main-green;
        border: 1px solid $main-green;

        &:focus {
            color: $main-white;
            background-color: $main-green;
            border: 1px solid $main-green;
        }

        &:hover {
            color: $main-white;
            background-color: $dark-green;
        }

        &:active {
            background-color: $pitch-green;
        }

        &:disabled {
            pointer-events: none;
            cursor: default;
            background-color: $pale-green;
            border-color: $pale-green;
        }
    }

    &__secondary {
        color: $main-black;
        background-color: $main-white;
        border: 1px solid $border-grey;

        &:focus {
            color: $main-black;
            background-color: $main-white;
            border: 1px solid $border-grey;
        }

        &:hover {
            color: $main-black;
            border-color: $dark-grey;
        }

        &:active {
            background-color: $pale-grey;
            border-color: $semi-black;
        }

        &:disabled {
            color: $semi-black;
            cursor: default;
            background-color: $main-white;
            border: 1px solid $border-grey;

            &:hover {
                background-color: $main-white;
            }
        }
    }

    &__danger {
        color: $main-white;
        background-color: $red;
        border: 1px solid $red;

        &:hover {
            background-color: #e56d4b;
        }

        &:active {
            background-color: $pitch-green;
        }

        &:disabled {
            pointer-events: none;
            cursor: default;
            background-color: $pale-green;
            border-color: $pale-green;
        }
    }

    &__small {
        height: 30px;
        padding: 0 12px;
        font-size: $secondary-font-size;
    }
}

.Link {
    &__disabled {
        pointer-events: none;
        opacity: 0.5;
    }
}
