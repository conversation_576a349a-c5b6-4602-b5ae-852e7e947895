import clsx from 'clsx';
import { ButtonContainers, ButtonSizes, ButtonTypes } from 'common/enums/button';
import { type ButtonPropsType } from 'common/types/button';
import React, { type FC } from 'react';

import classes from './styles.module.scss';

const ButtonComponent: FC<ButtonPropsType> = (props) => {
    const {
        container = ButtonContainers.BUTTON,
        href,
        target = '_self',
        type = ButtonTypes.PRIMARY,
        size = ButtonSizes.MEDIUM,
        text,
        onClick,
        disabled = false,
        style,
        className,
    } = props;

    const handleClick = (e: React.MouseEvent<HTMLButtonElement> | React.MouseEvent<HTMLAnchorElement>): void => {
        onClick && onClick(e);
    };

    return (
        <>
            {container === ButtonContainers.BUTTON ? (
                <button
                    onClick={handleClick}
                    style={style}
                    className={clsx(
                        classes.Button,
                        size === ButtonSizes.SMALL && classes.Button__small,
                        type === ButtonTypes.PRIMARY
                            ? classes.Button__primary
                            : type === ButtonTypes.DANGER
                            ? classes.Button__danger
                            : classes.Button__secondary,
                        className,
                    )}
                    type="button"
                    disabled={disabled}
                >
                    {text}
                </button>
            ) : (
                <a
                    href={href}
                    target={target}
                    onClick={handleClick}
                    style={style}
                    className={clsx(
                        classes.Button,
                        size === ButtonSizes.SMALL && classes.Button__small,
                        type === ButtonTypes.PRIMARY ? classes.Button__primary : classes.Button__secondary,
                        disabled && classes.Link__disabled,
                        className,
                    )}
                >
                    {text}
                </a>
            )}
        </>
    );
};

export { ButtonComponent };
