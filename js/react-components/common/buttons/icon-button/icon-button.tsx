import clsx from 'clsx';
import { ButtonSizes, ButtonTypes } from 'common/enums/button';
import { type IconButtonPropsType } from 'common/types/button';
import React, { type FC, useEffect, useRef } from 'react';

import classes from './styles.module.scss';

const IconButton: FC<IconButtonPropsType> = (props) => {
    const {
        iconName,
        renderIcon,
        isOpen = false,
        type = ButtonTypes.SECONDARY,
        size = ButtonSizes.MEDIUM,
        onClick,
        className,
        colorfulIcon = false,
        disabled = false,
    } = props;
    const ref = useRef<HTMLButtonElement>(null);

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>): void => {
        onClick && onClick(e);
    };

    useEffect(() => {
        if (!ref.current) return;

        isOpen ? ref.current.focus() : ref.current.blur();
    }, [isOpen]);

    return (
        <button
            onClick={handleClick}
            ref={ref}
            className={clsx(
                classes.Button,
                size === ButtonSizes.SMALL && classes.Button__small,
                type === ButtonTypes.PRIMARY
                    ? classes.Button_primary
                    : type === ButtonTypes.DANGER
                    ? classes.Button_danger
                    : classes.Button_secondary,
                className,
            )}
            type="button"
            disabled={disabled}
        >
            {renderIcon?.({ buttonType: type, buttonSize: size })}
            {iconName && (
                <img
                    className={clsx(classes.icon, colorfulIcon && classes.disableFilter)}
                    src={`images/redesign/svg-icons/${iconName}.svg`}
                    alt=""
                />
            )}
        </button>
    );
};

export { IconButton };
