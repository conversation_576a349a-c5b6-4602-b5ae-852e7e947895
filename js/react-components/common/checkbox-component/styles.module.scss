@import 'common/styles/global-variables.module';

.checkboxWrapper {
    margin: 0;
    position: relative;
}

.input {
    position: absolute;
    opacity: 0;
    z-index: -100;
    &:disabled {
        pointer-events: none;
    }
}

.styledCheckbox {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    border: 2px solid $border-grey;
    border-radius: 4px;
    background-color: $main-white;
    cursor: pointer;
    transition: all 0.1s ease;

    &_focused {
        border-color: $semi-black;
    }

    &_checked {
        background-color: $main-green;
        border-color: $main-green;
    }

    &_disabled {
        cursor: default;
        opacity: 0.6;
        pointer-events: none;
    }
}
