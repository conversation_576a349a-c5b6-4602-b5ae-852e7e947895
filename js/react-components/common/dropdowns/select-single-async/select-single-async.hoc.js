import { angularizeDirectiveFixed } from 'common/helpers/angularize-directive-fixed';
import { SelectSingleAsync } from 'react-components/common/dropdowns/select-single-async/select-single-async';
import { SelectMenuHeight } from 'common/enums/select';

angularizeDirectiveFixed(SelectSingleAsync, 'selectSingleAsync', angular.module('RecruitingApp'), {
    currentLang: '<',
    placeholder: '<',
    options: '<',
    isGrouped: '<',
    isMulti: '<',
    isClearable: '<',
    translateOptions: '<',
    translateGroupLabel: '<',
    minMenuHeight: '<',
    maxMenuHeight: '<',
    selectedValue: '<',
    onChange: '<',
    disabled: '<',
    isError: '<',
    fetchOptions: '<',
    pathToLabel: '<',
    pathToValue: '<',
    position: '<',
    inputSearchThreshold: '<',
    initialInputValue: '<',
    filterMatchFrom: '<',
    additionalArg: '<',
});
