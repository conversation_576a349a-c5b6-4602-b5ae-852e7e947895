import clsx from 'clsx';
import { selectFilterConfig } from 'common/constants/select-filter-config';
import { SelectFilterMatchFrom, SelectPath, SelectPosition, SelectVirtualizedThreshold } from 'common/enums/select';
import { measureMaxVisibleOptions } from 'common/helpers/measure-max-visible-options';
import { useFollowBySelectHead, useOnClickOutside } from 'common/hooks';
import { selectVirtualizedBaseStyles } from 'common/selectVirtualizedBaseStyles';
import { variables } from 'common/styles/script-variables';
import { type SelectMultiAsyncPropsType, type SelectOptionType } from 'common/types/select';
import ChevronDownIcon from 'images/redesign/svg-icons/chevron-down.svg';
import ClearIcon from 'images/redesign/svg-icons/close.svg';
import React, { type FC, type ReactEventHandler, useLayoutEffect, useRef, useState } from 'react';
import {
    CustomClearIndicator,
    CustomInput,
    CustomMenuWithSelectAll,
    CustomOptionWithCheckbox,
    CustomValueContainer,
} from 'react-components/common/dropdowns/custom-react-select-components';
import { type ActionMeta, createFilter, type GroupBase, type InputActionMeta, type StylesConfig } from 'react-select';
import AsyncSelect from 'react-select/async';
import { Tooltip } from 'react-tooltip';

import classes from './styles.module.scss';

declare module 'react-select/base' {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export interface Props<Option, IsMulti extends boolean, Group extends GroupBase<Option>> {
        maxValuesToShow?: number;
        selectedLimit?: number;
        minLimit?: number;
    }
}

const SelectMultiAsync: FC<SelectMultiAsyncPropsType> = (props) => {
    const {
        translateFunc,
        placeholder,
        options,
        fetchOptions,
        translateOptions = false,
        isGrouped = false,
        isError = false,
        showSelectAll = true,
        translateGroupLabel = true,
        selectedValues,
        onChange,
        disabled = false,
        selectedLimit,
        minLimit,
        pathToShortLabel = SelectPath.SHORT,
        pathToLabel = SelectPath.LABEL,
        pathToValue = SelectPath.VALUE,
        pathToKey = SelectPath.ID,
        position = SelectPosition.ABSOLUTE,
        filterMatchFrom = SelectFilterMatchFrom.ANY,
        inputSearchThreshold = 0,
    } = props;

    const [selectIsOpened, setSelectIsOpened] = useState(false);
    const [inputValue, setInputValue] = useState('');
    const [maxValuesToShow, setMaxValuesToShow] = useState(0);

    const instanceId = useRef(window.crypto.randomUUID());
    const rootWrapper = useRef<HTMLDivElement>(null);
    const selectRef = useRef(null);
    const selectedValuesRef = useRef<HTMLDivElement>(null);

    useOnClickOutside(rootWrapper, () => {
        setSelectIsOpened(false);
    });

    const translatedPlaceholder = translateFunc(placeholder || '');

    const loadOptions = (inputValue: string, callback: (options: any[]) => void): void => {
        fetchOptions(inputValue, callback);
    };

    const onHeadClick = (): void => {
        setSelectIsOpened(!selectIsOpened);
    };

    const onRemoveSelected = (
        event: React.MouseEvent<SVGSVGElement, MouseEvent>,
        itemToRemove: SelectOptionType,
    ): void => {
        event.stopPropagation();
        onChange(selectedValues?.filter((selected) => selected[pathToKey] !== itemToRemove[pathToKey]) || null);
    };

    const getGroupHeadingPadding = (isGrouped: boolean, options: SelectOptionType[]): number | string => {
        if (isGrouped) {
            return options.flatMap((item) => item.options).length >= SelectVirtualizedThreshold ? 10 : 'initial';
        }
        return options.length >= SelectVirtualizedThreshold ? 10 : 'initial';
    };

    const selectStyles: StylesConfig<any, boolean, GroupBase<any>> = {
        ...selectVirtualizedBaseStyles,
        container: (baseStyles, state) => ({
            ...baseStyles,
            position: position === 'fixed' ? 'fixed' : 'absolute',
            width: position === 'fixed' ? rootWrapper.current?.offsetWidth : '100%',
            zIndex: 1,
        }),
        groupHeading: (baseStyles, state) => ({
            ...baseStyles,
            paddingTop: getGroupHeadingPadding(isGrouped, options),
        }),
    };

    const onChangeHandler = (newValue: SelectOptionType[], actionMeta: ActionMeta<SelectOptionType>): void => {
        if (actionMeta?.option?.value === 'select-*-all') {
            if (isGrouped) {
                onChange(selectedValues?.length === options.flatMap((item) => item.options).length ? [] : options);
            } else {
                onChange(selectedValues?.length === options?.length ? [] : options);
            }
        } else if (actionMeta?.option?.value === 'select-*-found') {
            if (isGrouped) {
                onChange(
                    // @ts-ignore
                    selectRef.current
                        .getCategorizedOptions()
                        .flatMap((item: any) => item.options) as SelectOptionType[],
                );
            } else {
                onChange(
                    // @ts-ignore
                    selectRef.current.getCategorizedOptions().map((option: any) => option.data) as SelectOptionType[],
                );
            }
        } else {
            onChange(newValue);
        }
    };

    const onClearAll: ReactEventHandler = (event) => {
        event.stopPropagation();
        onChange([]);
    };

    const onInputChange = (inputValue: string, { action, prevInputValue }: InputActionMeta): string => {
        if (action === 'input-change') {
            setInputValue(inputValue);
        }

        if (action === 'input-blur') {
            setInputValue(prevInputValue);
        }

        if (action === 'menu-close') {
            setInputValue(prevInputValue);
        }

        if (action === 'input-change') return inputValue;
        return prevInputValue;
    };

    const noOptionsMessageHandler = (): string => {
        if (inputSearchThreshold && !options && inputValue.length < inputSearchThreshold) {
            return `${translateFunc('Please enter')} ${inputSearchThreshold} ${translateFunc('or more characters')}`;
        }
        return translateFunc('No matches found');
    };

    useFollowBySelectHead(position, selectIsOpened, rootWrapper, selectRef);

    useLayoutEffect(() => {
        if (!selectedValues || !selectedValuesRef) return;
        const containerWidth = Number(selectedValuesRef.current?.offsetWidth) - 20;
        const maxValuesCount = measureMaxVisibleOptions(
            selectedValues,
            containerWidth,
            translateOptions,
            pathToShortLabel || pathToLabel,
        );
        setMaxValuesToShow(maxValuesCount === 0 ? 1 : maxValuesCount);
    }, [selectedValues]);

    return (
        <div ref={rootWrapper} className={classes.wrapper}>
            <div
                className={clsx(
                    classes.selectHead,
                    selectIsOpened && classes.selectHead_opened,
                    isError && classes.selectHead_error,
                )}
                onClick={onHeadClick}
            >
                <div ref={selectedValuesRef} className={classes.selectedValues}>
                    {!selectedValues?.length && (
                        <span className={clsx(classes.placeholder, disabled && classes.placeholder_disabled)}>
                            {translatedPlaceholder}
                        </span>
                    )}

                    {selectedValues?.length
                        ? selectedValues?.slice(0, maxValuesToShow).map((value) => (
                              <div className={classes.multiValueItem} key={value[pathToKey] || value[pathToLabel]}>
                                  <div className={classes.multiValueItem__text}>
                                      {translateOptions
                                          ? translateFunc(
                                                (value[pathToShortLabel]
                                                    ? value[pathToShortLabel]
                                                    : value[pathToLabel]) as string,
                                            )
                                          : value[pathToShortLabel]
                                          ? value[pathToShortLabel]
                                          : value[pathToLabel]}
                                  </div>
                                  {minLimit && selectedValues.length <= minLimit ? null : (
                                      <ClearIcon
                                          className={classes.multiValueItem__clearIcon}
                                          fill={variables['main-black']}
                                          width={16}
                                          height={16}
                                          onClick={(event) => {
                                              onRemoveSelected(event, value);
                                          }}
                                      />
                                  )}
                              </div>
                          ))
                        : null}

                    {selectedValues?.length && selectedValues.length > maxValuesToShow ? (
                        <span
                            className={classes.restSelectedNumber}
                            data-tooltip-id={'restOfSelected' + instanceId.current}
                            data-tooltip-place="bottom"
                        >
                            +{selectedValues.length - maxValuesToShow}
                        </span>
                    ) : null}
                </div>

                <div className={classes.headControls}>
                    {!minLimit && (
                        <ClearIcon
                            className={clsx(
                                classes.clearIndicator,
                                selectedValues?.length && classes.clearIndicator_visible,
                            )}
                            width={18}
                            height={18}
                            onClick={onClearAll}
                        />
                    )}
                    <ChevronDownIcon
                        className={clsx(classes.dropdownIndicator, selectIsOpened && classes.dropdownIndicator_rotated)}
                        width={18}
                        height={18}
                    />
                </div>
            </div>
            {selectIsOpened && (
                <AsyncSelect
                    instanceId={instanceId.current}
                    ref={selectRef}
                    inputValue={inputValue}
                    onInputChange={onInputChange}
                    autoFocus
                    menuIsOpen
                    menuShouldScrollIntoView={false}
                    maxMenuHeight={250}
                    maxValuesToShow={maxValuesToShow}
                    isMulti={true}
                    selectedLimit={selectedLimit}
                    minLimit={minLimit}
                    hideSelectedOptions={false}
                    menuPosition={position}
                    isSearchable={true}
                    filterOption={createFilter({ ...selectFilterConfig, matchFrom: filterMatchFrom })}
                    closeMenuOnSelect={false}
                    backspaceRemovesValue={false}
                    options={options}
                    loadOptions={loadOptions}
                    noOptionsMessage={noOptionsMessageHandler}
                    onChange={onChangeHandler}
                    getOptionLabel={(option: SelectOptionType) =>
                        translateOptions ? translateFunc(option[pathToLabel] as string) : option[pathToLabel]
                    }
                    getOptionValue={(option: any) => option[pathToValue]}
                    formatGroupLabel={(data: any) =>
                        translateGroupLabel ? translateFunc(data[pathToLabel] as string) : data[pathToLabel]
                    }
                    value={selectedValues}
                    placeholder={translatedPlaceholder}
                    className={'thinDropdownScrollbar'}
                    isDisabled={disabled}
                    styles={selectStyles}
                    defaultOptions={true}
                    components={{
                        ValueContainer: CustomValueContainer,
                        ...(showSelectAll ? { Menu: CustomMenuWithSelectAll } : {}),
                        ClearIndicator: CustomClearIndicator,
                        Input: CustomInput,
                        Option: CustomOptionWithCheckbox,
                    }}
                />
            )}

            <div className={classes.toolTipWrapper}>
                <Tooltip
                    id={'restOfSelected' + instanceId.current}
                    noArrow={true}
                    clickable={true}
                    opacity={1}
                    positionStrategy={'fixed'}
                    className={classes.restSelectedTooltip}
                >
                    {selectedValues?.length &&
                        selectedValues.slice(maxValuesToShow, selectedValues.length).map((item) => (
                            <div
                                key={'tooltip_' + item[pathToKey] || item[pathToLabel]}
                                className={classes.restSelectedItem}
                                title={
                                    translateOptions ? translateFunc(item[pathToLabel] as string) : item[pathToLabel]
                                }
                            >
                                <span className={classes.restSelectedItem__text}>
                                    {translateOptions ? translateFunc(item[pathToLabel] as string) : item[pathToLabel]}
                                </span>
                                {minLimit && selectedValues.length <= minLimit ? null : (
                                    <ClearIcon
                                        className={classes.restClearIcon}
                                        onClick={(event) => {
                                            onRemoveSelected(event, item);
                                        }}
                                        width={16}
                                        height={16}
                                        fill={variables['main-black']}
                                    />
                                )}
                            </div>
                        ))}
                </Tooltip>
            </div>
        </div>
    );
};

export { SelectMultiAsync };
