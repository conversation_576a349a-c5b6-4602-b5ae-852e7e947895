@import 'common/styles/global-variables.module';
@import 'common/styles/mixins';

.wrapper {
    position: relative;
    width: 100%;
}

.selectHead {
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: space-between;
    height: 42px;
    padding: 0 12px;
    background-color: $main-white;
    transition: all 100ms;
    @include border-small;

    &_opened {
        background-color: $pale-grey;
        border-color: $semi-black;
    }

    &_disabled {
        color: $dark-grey;
        pointer-events: none;
        background-color: $background-grey;
        fill: $dark-grey;
    }

    &_error {
        @include border-error;
    }

    &:hover {
        border-color: $dark-grey;

        .headControls {
            .clearIndicator {
                opacity: 1;
            }

            .dropdownIndicator {
                fill: $main-black;
            }
        }
    }

    &_error {
        @include border-error;
    }
}

.headControls {
    display: flex;
    gap: 12px;
    margin-right: 0;
    margin-left: auto;

    .dropdownIndicator,
    .clearIndicator {
        @include control-icon-size;
    }

    .dropdownIndicator {
        fill: $semi-black;
        transition: transform 0.2s ease;

        &_rotated {
            transform: rotate(180deg);
        }
    }

    .clearIndicator {
        pointer-events: none;
        cursor: pointer;
        opacity: 0;
        fill: transparent;

        &_visible {
            pointer-events: initial;
            fill: $semi-black;
        }

        &:hover {
            fill: $main-black;
        }
    }
}

.selectedValues {
    display: flex;
    flex-wrap: nowrap;
    gap: 4px;
    align-items: center;
    width: 100%;
    overflow: hidden;
    cursor: default;
}

.placeholder {
    overflow: hidden;
    color: $semi-black;
    text-overflow: ellipsis;
    white-space: nowrap;

    &_disabled {
        color: $dark-grey;
    }
}

.value {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.customSelectControl {
    padding: 12px;
}

.toolTipWrapper {
    .restSelectedTooltip {
        z-index: 2;
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        max-width: 300px;
        max-height: 200px;
        padding: 8px;
        overflow: auto;
        background: $main-white;
        border: 1px solid $border-grey;
        border-radius: 4px;
        box-shadow: $main-box-shadow;
    }

    .restSelectedItem {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 4px 8px;
        font-size: $main-font-size;
        color: $main-green;
        cursor: default;
        background-color: $light-green;
        border-radius: 4px;
    }

    .restClearIcon {
        cursor: pointer;
    }

    ::-webkit-scrollbar {
        width: 4px;
    }
}
