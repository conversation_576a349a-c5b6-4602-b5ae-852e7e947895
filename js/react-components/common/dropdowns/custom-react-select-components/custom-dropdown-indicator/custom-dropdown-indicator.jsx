import ChevronDown from 'images/redesign/svg-icons/chevron-down.svg';
import React from 'react';
import { components } from 'react-select';

export const CustomDropdownIndicator = (props) => {
    const { width, height } = props.getStyles('dropdownIndicator', props);
    return (
        <components.DropdownIndicator {...props}>
            <ChevronDown width={width} height={height} />
        </components.DropdownIndicator>
    );
};
