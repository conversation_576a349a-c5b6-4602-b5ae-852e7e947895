import React from 'react';
import { components, type SingleValueProps } from 'react-select';

export const CustomSingleValue = ({ children, ...props }: SingleValueProps): React.ReactElement => {
    const title = typeof children === 'string' ? children : '';

    return (
        <components.SingleValue {...props}>
            <span title={title}>{children}</span>
        </components.SingleValue>
    );
};
