import clsx from 'clsx';
import { selectFilterConfig } from 'common/constants/select-filter-config';
import { SelectFilter<PERSON>atch<PERSON>rom, SelectPath, SelectPosition } from 'common/enums/select';
import { useFollowBySelectHead, useOnClickOutside } from 'common/hooks';
import { selectVirtualizedBaseStyles } from 'common/selectVirtualizedBaseStyles';
import { type SelectOptionType, type SelectSingleAsyncPropsType } from 'common/types/select';
import ChevronDownIcon from 'images/redesign/svg-icons/chevron-down.svg';
import ClearIcon from 'images/redesign/svg-icons/close.svg';
import React, { type FC, useEffect, useRef, useState } from 'react';
import {
    CustomInputWithAddButton,
    CustomOptionWithCheckIcon,
    CustomValueContainer,
} from 'react-components/common/dropdowns/custom-react-select-components';
import { CustomClearIndicator } from 'react-components/common/dropdowns/custom-react-select-components/custom-clear-indicator/custom-clear-indicator';
// @ts-ignore
import { getService } from 'react-in-angularjs';
import { type ActionMeta, createFilter, type GroupBase, type InputActionMeta, type StylesConfig } from 'react-select';
import AsyncSelect from 'react-select/async';
import type {} from 'react-select/base';

import classes from './styles.module.scss';

declare module 'react-select/base' {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export interface Props<Option, IsMulti extends boolean, Group extends GroupBase<Option>> {
        maxValuesToShow?: number;
        isPlaceAbove?: any;
    }
}

const SelectSingleAsyncCreatable: FC<SelectSingleAsyncPropsType> = (props) => {
    const {
        placeholder,
        options,
        translateOptions = false,
        selectedValue,
        isClearable = true,
        onChange,
        disabled,
        isError = false,
        fetchOptions,
        pathToLabel = SelectPath.LABEL,
        pathToValue = SelectPath.VALUE,
        position = SelectPosition.ABSOLUTE,
        filterMatchFrom = SelectFilterMatchFrom.ANY,
        inputSearchThreshold = 0,
        initialInputValue,
        noOptionsMessage,
        additionalArg,
        openMenu,
        isPlaceAbove,
    } = props;

    const [selectIsOpened, setSelectIsOpened] = useState(false);
    const [inputValue, setInputValue] = useState(initialInputValue || '');
    const rootWrapper = useRef<HTMLDivElement>(null);
    const selectRef = useRef(null);
    const selectedValuesRef = useRef(null);

    const translate = getService('$translate').instant;
    const translatedPlaceholder = translate(placeholder);

    const selectStyles: StylesConfig<any, boolean, GroupBase<any>> = {
        ...selectVirtualizedBaseStyles,
        container: (baseStyles, state) => ({
            ...baseStyles,
            position: position === 'fixed' ? 'fixed' : 'absolute',
            width: position === 'fixed' ? rootWrapper.current?.offsetWidth : '100%',
            zIndex: 1,
        }),
    };

    useOnClickOutside(rootWrapper, () => {
        setSelectIsOpened(false);
    });

    const onHeadClick = (): void => {
        setSelectIsOpened(!selectIsOpened);
    };

    const onChangeHandler = (newValue: SelectOptionType | null, actionMeta?: ActionMeta<SelectOptionType>): void => {
        onChange(newValue, additionalArg, actionMeta, { setInputValue });
        setSelectIsOpened(false);
    };

    const loadOptions = (inputValue: string, callback: (options: any[]) => void): void => {
        fetchOptions(inputValue, callback, additionalArg);
    };

    const onInputChange = (inputValue: string, { action, prevInputValue }: InputActionMeta): string => {
        if (action === 'input-change') {
            setInputValue(inputValue);
        }

        if (action === 'input-blur') {
            setInputValue(prevInputValue);
        }

        if (action === 'menu-close') {
            setInputValue(prevInputValue);
        }

        if (action === 'input-change') return inputValue;
        return prevInputValue;
    };

    const noOptionsMessageHandler = (): string => {
        if (inputSearchThreshold && !options && inputValue?.length < inputSearchThreshold) {
            return `${translate('Please enter')} ${inputSearchThreshold} ${translate('or more characters')}`;
        }
        return translate(noOptionsMessage || 'No matches found');
    };

    useEffect(() => {
        if (openMenu) {
            setSelectIsOpened(true);
        }
    }, []);

    useFollowBySelectHead(position, selectIsOpened, rootWrapper, selectRef, isPlaceAbove);

    return (
        <div ref={rootWrapper} className={classes.wrapper}>
            <div
                className={clsx(
                    classes.selectHead,
                    selectIsOpened && classes.selectHead_opened,
                    disabled && classes.selectHead_disabled,
                    isError && classes.selectHead_error,
                )}
                onClick={onHeadClick}
            >
                <div ref={selectedValuesRef} className={classes.selectedValues}>
                    {!selectedValue?.[pathToValue] && (
                        <span className={clsx(classes.placeholder, disabled && classes.placeholder_disabled)}>
                            {translatedPlaceholder}
                        </span>
                    )}
                    {selectedValue?.[pathToValue] && (
                        <span className={classes.selectedValueText}>{selectedValue[pathToLabel]}</span>
                    )}
                </div>

                <div className={classes.headControls}>
                    {selectedValue?.[pathToValue] && isClearable ? (
                        <ClearIcon
                            className={clsx(classes.clearIndicator, selectedValue && classes.clearIndicator_visible)}
                            width={18}
                            height={18}
                            onClick={() => {
                                onChangeHandler(null);
                            }}
                        />
                    ) : null}
                    <ChevronDownIcon
                        className={clsx(classes.dropdownIndicator, selectIsOpened && classes.dropdownIndicator_rotated)}
                        width={18}
                        height={18}
                    />
                </div>
            </div>
            {selectIsOpened && (
                <AsyncSelect
                    inputValue={inputValue}
                    cacheOptions
                    loadOptions={loadOptions}
                    defaultOptions={true}
                    isClearable={isClearable}
                    backspaceRemovesValue={false}
                    ref={selectRef}
                    autoFocus
                    menuIsOpen
                    isPlaceAbove={isPlaceAbove}
                    maxMenuHeight={200}
                    menuPosition={position}
                    isDisabled={disabled}
                    closeMenuOnSelect={false}
                    options={options}
                    filterOption={createFilter({ ...selectFilterConfig, matchFrom: filterMatchFrom })}
                    loadingMessage={() => translate('Searching')}
                    noOptionsMessage={noOptionsMessageHandler}
                    onChange={onChangeHandler}
                    onInputChange={onInputChange}
                    pathToValue={pathToValue}
                    pathToLabel={pathToLabel}
                    getOptionLabel={(option) =>
                        translateOptions ? translate(option[pathToLabel]) : option[pathToLabel]
                    }
                    getOptionValue={(option) => option[pathToValue]}
                    value={selectedValue?.[pathToLabel] ? selectedValue : null}
                    placeholder={translatedPlaceholder}
                    components={{
                        ValueContainer: CustomValueContainer,
                        ClearIndicator: CustomClearIndicator,
                        Input: CustomInputWithAddButton,
                        Option: CustomOptionWithCheckIcon,
                    }}
                    className={'thinDropdownScrollbar'}
                    styles={selectStyles}
                />
            )}
        </div>
    );
};

export { SelectSingleAsyncCreatable };
