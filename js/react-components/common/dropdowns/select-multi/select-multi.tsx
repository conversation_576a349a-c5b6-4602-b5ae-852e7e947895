import { selectFilterConfig } from 'common/constants/select-filter-config';
import { SelectFilter<PERSON>atch<PERSON>rom, SelectPath, SelectPosition } from 'common/enums/select';
import { measureMaxVisibleOptions } from 'common/helpers/measure-max-visible-options';
import { selectBaseStyles } from 'common/selectBaseStyles';
import { variables } from 'common/styles/script-variables';
import { type SelectMultiPropsType, type SelectOptionType } from 'common/types/select';
import ClearIcon from 'images/redesign/svg-icons/close.svg';
import React, { type FC, useLayoutEffect, useRef, useState } from 'react';
import {
    CustomClearIndicator,
    CustomDropdownIndicator,
    CustomMenuWithSelectAllDefault,
    CustomMultiValue,
    CustomMultiValueRemove,
    CustomOptionWithCheckbox,
} from 'react-components/common/dropdowns/custom-react-select-components';
// @ts-ignore
import { getService } from 'react-in-angularjs';
import Select, {
    type ActionMeta,
    createFilter,
    type GroupBase,
    type SelectInstance,
    type StylesConfig,
} from 'react-select';
import { Tooltip } from 'react-tooltip';

import classes from './styles.module.scss';

declare module 'react-select/base' {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export interface Props<Option, IsMulti extends boolean, Group extends GroupBase<Option>> {
        maxValuesToShow?: number;
    }
}

const SelectMulti: FC<SelectMultiPropsType> = (props) => {
    const {
        placeholder,
        options,
        translateOptions = false,
        isClearable = true,
        isGrouped = false,
        isError = false,
        translateGroupLabel = true,
        selectedValues,
        onChange,
        disabled,
        pathToLabel = SelectPath.LABEL,
        pathToValue = SelectPath.VALUE,
        position = SelectPosition.ABSOLUTE,
        filterMatchFrom = SelectFilterMatchFrom.ANY,
        minMenuHeight,
        maxMenuHeight = 200,
        additionalArg,
    } = props;

    const instanceId = useRef(window.crypto.randomUUID());
    const selectRef = useRef<SelectInstance<SelectOptionType, true, GroupBase<SelectOptionType>>>(null);
    const [maxValuesToShow, setMaxValuesToShow] = useState(0);

    const translate = getService('$translate').instant;
    const translatedPlaceholder = translate(placeholder);

    const selectStyles: StylesConfig<any, boolean, GroupBase<any>> = {
        ...selectBaseStyles,
    };

    useLayoutEffect(() => {
        if (!selectedValues || !selectRef.current) return;

        const containerWidth = Number(selectRef?.current?.controlRef?.offsetWidth) - 90;
        setMaxValuesToShow(measureMaxVisibleOptions(selectedValues, containerWidth, translateOptions, pathToLabel));
    }, [selectedValues]);

    const onRemoveSelected = (itemToRemove: SelectOptionType): void => {
        selectedValues &&
            onChange(
                selectedValues.filter((selected) => selected[pathToValue] !== itemToRemove[pathToValue]),
                additionalArg,
                { action: 'remove-value', removedValue: itemToRemove },
            );
    };

    const onChangeHandler = (newValue: SelectOptionType[], actionMeta: ActionMeta<SelectOptionType>): void => {
        if (actionMeta?.option?.value === 'select-*-all') {
            if (isGrouped) {
                onChange(
                    selectedValues?.length === options.flatMap((item) => item.options).length ? [] : options,
                    additionalArg,
                    actionMeta,
                );
            } else {
                onChange(selectedValues?.length === options?.length ? [] : options, additionalArg, actionMeta);
            }
        } else {
            onChange(newValue, additionalArg, actionMeta);
        }
    };

    return (
        <>
            <div className={classes.toolTipWrapper}>
                <Tooltip
                    id={'restOfSelected' + instanceId.current}
                    noArrow={true}
                    clickable={true}
                    opacity={1}
                    positionStrategy={'fixed'}
                    className={classes.restSelectedTooltip}
                >
                    {selectedValues?.length &&
                        selectedValues.slice(maxValuesToShow, selectedValues.length).map((item) => (
                            <div key={item[pathToLabel]} className={classes.restSelectedItem}>
                                {translateOptions ? translate(item[pathToLabel]) : item[pathToLabel]}
                                <ClearIcon
                                    className={classes.restClearIcon}
                                    onClick={() => {
                                        onRemoveSelected(item);
                                    }}
                                    width={16}
                                    height={16}
                                    fill={variables['main-black']}
                                />
                            </div>
                        ))}
                </Tooltip>
            </div>

            <Select
                ref={selectRef}
                instanceId={instanceId.current}
                styles={selectStyles}
                className={'thinDropdownScrollbar'}
                maxValuesToShow={maxValuesToShow}
                placeholder={translatedPlaceholder}
                menuPosition={position}
                minMenuHeight={minMenuHeight}
                maxMenuHeight={maxMenuHeight}
                options={options}
                filterOption={createFilter({ ...selectFilterConfig, matchFrom: filterMatchFrom })}
                value={selectedValues}
                onChange={onChangeHandler}
                isMulti={true}
                isError={isError}
                classNamePrefix={'react-select-multi'}
                isClearable={isClearable}
                isSearchable={false}
                isDisabled={disabled}
                hideSelectedOptions={false}
                closeMenuOnSelect={false}
                getOptionLabel={(option) => (translateOptions ? translate(option[pathToLabel]) : option[pathToLabel])}
                getOptionValue={(option) => option[pathToValue]}
                formatGroupLabel={(data) => (translateGroupLabel ? translate(data.label) : data.label)}
                components={{
                    MultiValue: CustomMultiValue,
                    DropdownIndicator: CustomDropdownIndicator,
                    ClearIndicator: CustomClearIndicator,
                    Option: CustomOptionWithCheckbox,
                    MultiValueRemove: CustomMultiValueRemove,
                    Menu: CustomMenuWithSelectAllDefault,
                }}
            />
        </>
    );
};

export { SelectMulti };
