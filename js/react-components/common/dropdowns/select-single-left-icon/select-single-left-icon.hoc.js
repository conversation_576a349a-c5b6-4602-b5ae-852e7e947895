import { angularizeDirectiveFixed } from 'common/helpers/angularize-directive-fixed';
import { SelectSingleLeftIcon } from './select-single-left-icon';

angularizeDirectiveFixed(SelectSingleLeftIcon, 'selectSingleLeftIcon', angular.module('RecruitingApp'), {
    placeholder: '<',
    defaultValue: '<',
    options: '<',
    onChange: '<',
    selectedValue: '<',
    disabled: '<',
    isClearable: '<',
    isError: '<',
    translateGroupLabel: '<',
    translateOptions: '<',
    pathToLabel: '<',
    pathToValue: '<',
    position: '<',
    menuPlacement: '<',
    additionalArg: '<',
    classNamePrefix: '<',
});
