@import 'common/styles/global-variables.module';
@import 'common/styles/mixins';

.wrapper {
    position: relative;
    width: 100%;
}

.selectHead {
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: space-between;
    height: 42px;
    padding: 0 12px;
    cursor: pointer;
    background-color: $main-white;
    transition: all 100ms;
    @include border-small;

    &_opened {
        background-color: $pale-grey;
        border-color: $semi-black;
    }

    &_disabled {
        color: $dark-grey;
        pointer-events: none;
        background-color: $background-grey;
        fill: $dark-grey;
    }

    &_error {
        @include border-error;
    }

    &:hover {
        border-color: $dark-grey;

        .headControls {
            .clearIndicator {
                opacity: 1;
            }

            .dropdownIndicator {
                fill: $main-black;
            }
        }
    }
}

.headControls {
    display: flex;
    gap: 12px;
    margin-right: 0;
    margin-left: auto;

    .dropdownIndicator {
        fill: $semi-black;
        transition: transform 0.2s ease;

        &_rotated {
            transform: rotate(180deg);
        }
    }

    .clearIndicator {
        pointer-events: none;
        cursor: pointer;
        opacity: 0;
        fill: transparent;

        &_visible {
            pointer-events: initial;
            fill: $semi-black;
        }

        &:hover {
            fill: $main-black;
        }
    }
}

.placeholder {
    color: $semi-black;

    &_disabled {
        color: $dark-grey;
    }
}

.customSelectControl {
    padding: 12px;
}

.selectedValues {
    display: flex;
    flex-wrap: nowrap;
    gap: 4px;
    align-items: center;
    width: 100%;
}

.restSelectedNumber,
.multiValueItem {
    display: flex;
    flex-wrap: nowrap;
    gap: 8px;
    align-items: center;
    height: 25px;
    padding: 4px 8px;
    color: $main-black;
    text-overflow: unset;
    background-color: $pale-grey;
    border-radius: 4px;
}

.toolTipWrapper {
    .restSelectedTooltip {
        z-index: 2;
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        max-width: 300px;
        max-height: 200px;
        padding: 8px;
        overflow: auto;
        background: $main-white;
        border: 1px solid $border-grey;
        border-radius: 4px;
        box-shadow: $main-box-shadow;
    }

    .restSelectedItem {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 4px 8px;
        font-size: $main-font-size;
        color: $main-black;
        cursor: default;
        background-color: $pale-grey;
        border-radius: 4px;
    }

    .restClearIcon {
        cursor: pointer;
    }

    ::-webkit-scrollbar {
        width: 4px;
    }
}
