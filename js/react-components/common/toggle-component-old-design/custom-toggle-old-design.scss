@import 'common/styles/global-variables.module';

$old-green: #00b549;

.react-toggle__old-design {
    .react-toggle {
        position: relative;

        display: inline-flex;
        padding: 0;
        touch-action: pan-x;
        cursor: pointer;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-color: transparent;
        border: 0;

        -webkit-touch-callout: none;

        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        -webkit-tap-highlight-color: transparent;
    }

    .react-toggle-screenreader-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0 0 0 0);
        border: 0;
    }

    .react-toggle--disabled {
        cursor: not-allowed;
        opacity: 0.5;
        -webkit-transition: opacity 0.4s;
        transition: opacity 0.4s;
    }

    .react-toggle-track {
        width: 35px;
        height: 20px;
        padding: 0;
        background-color: #ddd;
        border: 1px solid #ddd;
        border-radius: 30px;
        -webkit-transition: all 0.2s ease;
        -moz-transition: all 0.2s ease;
        transition: all 0.2s ease;
    }

    .react-toggle:hover:not(.react-toggle--disabled) .react-toggle-track {
        background-color: #ddd;
    }

    .react-toggle--checked .react-toggle-track {
        background-color: $old-green;
        border-color: $old-green;
    }

    .react-toggle--checked:hover:not(.react-toggle--disabled) .react-toggle-track {
        background-color: $old-green;
    }

    .react-toggle-track-check {
        position: absolute;
        top: 0px;
        bottom: 0px;
        left: 8px;
        width: 14px;
        height: 10px;
        margin-top: auto;
        margin-bottom: auto;
        line-height: 0;
        opacity: 0;
        -webkit-transition: opacity 0.4s ease;
        -moz-transition: opacity 0.4s ease;
        transition: opacity 0.4s ease;
    }

    .react-toggle--checked .react-toggle-track-check {
        opacity: 1;
        -webkit-transition: opacity 0.4s ease;
        -moz-transition: opacity 0.4s ease;
        transition: opacity 0.4s ease;
    }

    .react-toggle-track-x {
        position: absolute;
        top: 0px;
        right: 10px;
        bottom: 0px;
        width: 10px;
        height: 10px;
        margin-top: auto;
        margin-bottom: auto;
        line-height: 0;
        opacity: 1;
        -webkit-transition: opacity 0.4s ease;
        -moz-transition: opacity 0.4s ease;
        transition: opacity 0.4s ease;
    }

    .react-toggle--checked .react-toggle-track-x {
        opacity: 0;
    }

    .react-toggle-thumb {
        position: absolute;
        top: 0;
        left: -4px;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        width: 20px;
        height: 20px;
        background-color: #fff;
        border-radius: 50%;
        -webkit-transition: all 0.4s ease;
        -moz-transition: all 0.4s ease;
        transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1) 0ms;
        transition: all 0.4s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    }

    .react-toggle--checked .react-toggle-thumb {
        left: 17px;
        border-color: $old-green;
    }

    .react-toggle--focus .react-toggle-thumb {
        //-webkit-box-shadow: 0px 0px 3px 2px #0099E0;
        //-moz-box-shadow: 0px 0px 3px 2px #0099E0;
        //box-shadow: 0px 0px 2px 3px #0099E0;
    }

    .react-toggle:active:not(.react-toggle--disabled) .react-toggle-thumb {
        //-webkit-box-shadow: 0px 0px 5px 5px #0099E0;
        //-moz-box-shadow: 0px 0px 5px 5px #0099E0;
        //box-shadow: 0px 0px 5px 5px #0099E0;
    }

    .alwaysCheckedToggleStyle.react-toggle {
        .react-toggle-track {
            background-color: $old-green;
            border-color: $old-green;
        }

        &:hover:not(.react-toggle--disabled) .react-toggle-track {
            background-color: $old-green;
            border-color: $old-green;
        }
    }
}
