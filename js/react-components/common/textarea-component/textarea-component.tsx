import clsx from 'clsx';
import { angularizeDirectiveFixed } from 'common/helpers/angularize-directive-fixed';
import { type InputPropsType } from 'common/types/input';
import React, { type FC, type FocusEventHandler, useRef, useState } from 'react';
// @ts-ignore
import { getService } from 'react-in-angularjs';

import classes from './styles.module.scss';

const TextareaComponent: FC<InputPropsType> = (props) => {
    const { value, onBlur, onChange, placeholder, className, iconName, disabled = false, isError = false } = props;

    const [isFocused, setIsFocused] = useState(false);
    const inputRef = useRef(null);

    const translate = getService('$translate');
    const translatedPlaceholder = translate.instant(placeholder);

    const changeHandler = (e: React.ChangeEvent<HTMLTextAreaElement>): void => {
        onChange && onChange(e.target.value);
    };

    const blurHandler: FocusEventHandler = (event) => {
        setIsFocused(false);
        onBlur && onBlur(event);
    };

    return (
        <div
            className={clsx(
                classes.textareaWrapper,
                disabled && classes.disabled,
                isFocused && classes.textareaWrapper_focused,
                isError && classes.textareaWrapper_error,
            )}
        >
            {iconName && <img className={classes.icon} src={`images/redesign/svg-icons/${iconName}.svg`} alt="" />}
            <textarea
                disabled={disabled}
                placeholder={translatedPlaceholder}
                value={value}
                onChange={changeHandler}
                ref={inputRef}
                className={clsx(classes.textarea, className)}
                onFocus={() => {
                    setIsFocused(true);
                }}
                onBlur={blurHandler}
            />
        </div>
    );
};

// @ts-ignore
angularizeDirectiveFixed(TextareaComponent, 'textareaComponent', angular.module('RecruitingApp'), {
    value: '<',
    onBlur: '<',
    onChange: '<',
    placeholder: '<',
    disabled: '<',
    isError: '<',
    className: '<',
    iconName: '<',
});

export { TextareaComponent };
