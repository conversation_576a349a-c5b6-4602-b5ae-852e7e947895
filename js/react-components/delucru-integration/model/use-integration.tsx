import { type APISuccess } from 'common/types/api/api';
import { useState } from 'react';
import { deactivateIntegrationQuery, integrateAccountQuery } from 'react-components/delucru-integration/api/queries';

interface ReturnType {
    isLoading: boolean;
    integrateAccount: (token: string) => Promise<APISuccess | null>;
    deactivateIntegration: () => Promise<APISuccess | null>;
}

export const useIntegration = (): ReturnType => {
    const [isLoading, setIsLoading] = useState(false);

    const integrateAccount = async (token: string): Promise<APISuccess | null> => {
        setIsLoading(true);
        return await integrateAccountQuery(token)
            .then((data) => {
                return data;
            })
            .finally(() => {
                setIsLoading(false);
            });
    };

    const deactivateIntegration = async (): Promise<APISuccess | null> => {
        setIsLoading(true);
        return await deactivateIntegrationQuery()
            .then((data) => {
                return data;
            })
            .finally(() => {
                setIsLoading(false);
            });
    };

    return { isLoading, integrateAccount, deactivateIntegration };
};
