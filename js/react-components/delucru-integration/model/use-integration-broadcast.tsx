import { useEffect, useRef } from 'react';

interface ReturnType {
    broadcastIntegration: (status: 'integrated' | 'disconnected') => void;
}

export const useIntegrationBroadcast = (afterGetMessage: () => void): ReturnType => {
    const delucruBCRef = useRef<BroadcastChannel | null>(null);

    useEffect(() => {
        delucruBCRef.current ??= new BroadcastChannel('delucru');

        delucruBCRef.current.onmessage = (event: MessageEvent<'integrated' | 'disconnected'>) => {
            if (['integrated', 'disconnected'].includes(event.data)) {
                afterGetMessage();
            }
        };

        return () => {
            delucruBCRef.current?.close();
        };
    }, []);

    const broadcastIntegration = (status: 'integrated' | 'disconnected'): void => {
        delucruBCRef.current?.postMessage(status);
        afterGetMessage();
    };

    return { broadcastIntegration };
};
