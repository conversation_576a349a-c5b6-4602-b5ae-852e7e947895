import { ResponseStatuses } from 'common/enums/api';
import { type APISuccess } from 'common/types/api/api';
import type { SelectOptionType } from 'common/types/select';
import { type Vacancy } from 'common/types/vacancy/vacancy';
import { useEffect, useReducer, useState } from 'react';
import {
    delucruBaseUrl,
    dictionaryOptionsQuery,
    editVacancyQuery,
    getSkillsByCategoryQuery,
    getVacancyFromDelucruQuery,
} from 'react-components/delucru-integration/api/queries';
import { validateFormFields } from 'react-components/delucru-integration/lib/helpers/form-validation';
import { transformToPayload } from 'react-components/delucru-integration/lib/helpers/vacancy-data-modifiers';
import { ActionTypes, type FormAction } from 'react-components/delucru-integration/lib/types/form-types';
import { type VacancyFieldsStateType } from 'react-components/delucru-integration/lib/types/vacancy-from-delucru-type';
import { delucruFormReducer, initialState } from 'react-components/delucru-integration/model/form-reducer';
import { type DelucruAllOptions } from 'react-components/delucru-integration/model/use-make-publication';
import useSWR from 'swr';
import useSWRImmutable from 'swr/immutable';

interface ReturnType {
    isLoading: boolean;
    vacancyState: VacancyFieldsStateType;
    options: DelucruAllOptions;
    dispatch: (action: FormAction) => void;
    submitForm: () => Promise<APISuccess | null>;
}

export const useEditPublication = (vacancy: Vacancy, fetchCondition = true): ReturnType => {
    const [editIsLoading, setEditIsLoading] = useState(false);
    const [options, setOptions] = useState<DelucruAllOptions>();
    const [vacancyState, dispatch] = useReducer(delucruFormReducer, initialState);

    const { isLoading: dictionaryIsLoading, data: delucruOptions } = useSWRImmutable(
        fetchCondition && `${delucruBaseUrl}/dictionary`,
        dictionaryOptionsQuery,
    );

    const { isLoading: skillsIsLoading, data: customSkills } = useSWR(
        fetchCondition && 'hr/vacancy/getSkillsByCategory',
        getSkillsByCategoryQuery,
        {
            revalidateOnFocus: false,
            keepPreviousData: true,
        },
    );

    const {
        isLoading: vacancyIsLoading,
        isValidating: vacancyIsValidating,
        mutate: refreshVacancy,
    } = useSWR(
        fetchCondition && `${delucruBaseUrl}/getVacancyFromDelucru/?vacancyId=${vacancy.vacancyId}`,
        getVacancyFromDelucruQuery,
        {
            revalidateOnFocus: false,
            revalidateIfStale: true,
            onSuccess: (data) => {
                data && dispatch({ type: ActionTypes.SET_INITIAL_STATE, payload: { ...data, fieldsErrors: [] } });
            },
        },
    );

    useEffect(() => {
        if (!customSkills?.length) return;
        const systemSkills =
            vacancy.category?.skills.map(({ skill, skillId }) => ({ value: skillId, label: skill })) || [];

        setOptions((prevState) => ({
            ...prevState,
            ...delucruOptions,
            skillsOptions: [
                {
                    label: 'systemSkillsGroup',
                    options: systemSkills,
                },
                {
                    label: 'customSkillsGroup',
                    options: customSkills,
                },
            ] as SelectOptionType[],
        }));
    }, [customSkills, delucruOptions, vacancy.category?.skills]);

    const submitForm = async (): Promise<APISuccess | null> => {
        if (!vacancyState) return null;

        const errors = validateFormFields(vacancyState);
        if (errors.length) {
            dispatch({ type: ActionTypes.SET_ERROR, payload: errors });
            return null;
        }

        setEditIsLoading(true);
        return await editVacancyQuery({ ...transformToPayload(vacancyState), vacancyId: vacancy.vacancyId })
            .then((resp) => {
                if (resp?.status === ResponseStatuses.OK && resp.message === 'Vacancy edited') {
                    void refreshVacancy();
                }
                return resp;
            })
            .catch(() => null)
            .finally(() => {
                setEditIsLoading(false);
            });
    };

    return {
        isLoading: dictionaryIsLoading || skillsIsLoading || vacancyIsLoading || vacancyIsValidating || editIsLoading,
        vacancyState,
        options,
        dispatch,
        submitForm,
    };
};
