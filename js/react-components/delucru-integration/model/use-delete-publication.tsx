import { type APISuccess } from 'common/types/api/api';
import { useState } from 'react';
import { deleteVacancyQuery, delucruBaseUrl } from 'react-components/delucru-integration/api/queries';
import { useSWRConfig } from 'swr';

interface ReturnType {
    isLoading: boolean;
    deleteVacancy: (token: string) => Promise<APISuccess | undefined>;
}

export const useDeletePublication = (): ReturnType => {
    const [isLoading, setIsLoading] = useState(false);
    const { mutate } = useSWRConfig();

    const refreshVacancyInfo = (vacancyId: string): void => {
        void mutate(`${delucruBaseUrl}/getVacancyInfo/?vacancyId=${vacancyId}`);
    };

    const deleteVacancy = async (vacancyId: string): Promise<APISuccess | undefined> => {
        setIsLoading(true);
        return await deleteVacancyQuery(vacancyId)
            .then((data) => {
                if (data) {
                    refreshVacancyInfo(vacancyId);
                    return data;
                }
            })
            .finally(() => {
                setIsLoading(false);
            });
    };

    return { isLoading, deleteVacancy };
};
