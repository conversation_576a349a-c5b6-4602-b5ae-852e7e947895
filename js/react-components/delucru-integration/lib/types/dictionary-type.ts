import { type SelectOptionType } from 'common/types/select';

interface DelucruDictionary {
    benefits: BaseObjType[];
    addresses: BaseObjType[];
    regions: BaseObjType & Array<{ parent_id: string }>;
    languages: BaseObjType[];
    salary_payment: BaseObjType[];
    phones: unknown[];
    educations: BaseObjType[];
    transport: BaseObjType[];
    experiences: BaseObjType[];
    type_contract: BaseObjType[];
    abilities: Abilities;
    qualification: BaseObjType[];
    jobtypes: BaseObjType[];
    categories: BaseObjType[];
    workplace: BaseObjType[];
    others: BaseObjType[];
}

type DelucruDictionaryOptions = {
    [K in keyof DelucruDictionary]: SelectOptionType[];
};

interface Abilities {
    hard: string[];
    soft: string[];
}

interface BaseObjType {
    id: string;
    title: string;
}

const isDelucruDictionaryTG = (obj: unknown): obj is DelucruDictionary =>
    typeof obj === 'object' &&
    obj !== null &&
    'benefits' in obj &&
    'addresses' in obj &&
    'regions' in obj &&
    'languages' in obj;

const isBaseObjTypeTG = (obj: BaseObjType): obj is BaseObjType =>
    typeof obj === 'object' && obj !== null && 'id' in obj && 'title' in obj;

export { isBaseObjTypeTG, isDelucruDictionaryTG };
export type { BaseObjType, DelucruDictionary, DelucruDictionaryOptions };
