import { type VacancyFieldsStateType } from 'react-components/delucru-integration/lib/types/vacancy-from-delucru-type';
import { type requiredFields } from 'react-components/delucru-integration/model/form-reducer';

enum ActionTypes {
    SET_INITIAL_STATE = 'SET_INITIAL_STATE',
    SET_VALUE = 'SET_VALUE',
    SET_ERROR = 'SET_ERROR',
    REMOVE_ERROR = 'REMOVE_ERROR',
}

type FormPayload = {
    [F in keyof VacancyFieldsStateType]: {
        field: F;
        value: VacancyFieldsStateType[F];
    };
}[keyof VacancyFieldsStateType];

type FormAction =
    | {
          type: ActionTypes.SET_VALUE;
          payload: FormPayload;
      }
    | { type: ActionTypes.SET_INITIAL_STATE; payload: VacancyFieldsStateType }
    | { type: ActionTypes.SET_ERROR | ActionTypes.REMOVE_ERROR; payload: Array<(typeof requiredFields)[number]> };

export { ActionTypes };
export type { FormAction, FormPayload };
