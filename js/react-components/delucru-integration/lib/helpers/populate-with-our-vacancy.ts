import { type AngularRootScope } from 'common/types/angular-types';
import type { SelectOptionType } from 'common/types/select';
import { type LanguageTranslationResponseType, type LanguageTranslationType } from 'common/types/vacancy/languages';
import { type Vacancy } from 'common/types/vacancy/vacancy';
import { delucruCurrenciesOptions } from 'react-components/delucru-integration/lib/delucru-extended-options';
import { type DelucruDictionaryOptions } from 'react-components/delucru-integration/lib/types/dictionary-type';
// @ts-ignore
import { getService } from 'react-in-angularjs';

const populateActive = (ourStatus: Vacancy['status']): 0 | 1 => {
    return ['inwork', 'open'].includes(ourStatus) ? 1 : 0;
};

const populateCurrency = (ourCurrency: Vacancy['currency']): string => {
    const delucruCurrenciesKeys = delucruCurrenciesOptions.map((currency) => currency.value);
    return ourCurrency && delucruCurrenciesKeys.includes(ourCurrency) ? ourCurrency : 'USD';
};

const populateSkills = (ourSkills: Vacancy['skills']): SelectOptionType[] => {
    return ourSkills?.map(({ skillName, skillId }) => ({ label: skillName, value: skillId })) || [];
};

const populateJobTypes = (
    ourEmployment: Vacancy['employmentType'],
    dictionary: Partial<DelucruDictionaryOptions> | undefined,
): SelectOptionType[] => {
    if (ourEmployment === 'fullEmployment') {
        return dictionary?.jobtypes?.filter(({ value }) => value === '306') || [];
    } else if (ourEmployment === 'underemployment') {
        return dictionary?.jobtypes?.filter(({ value }) => value === '307') || [];
    } else if (ourEmployment === 'projectWork') {
        return dictionary?.jobtypes?.filter(({ value }) => value === '864') || [];
    } else if (ourEmployment === 'shiftWork') {
        return dictionary?.jobtypes?.filter(({ value }) => value === '342') || [];
    }
    return [];
};

const populateWorkPlace = (
    ourEmployment: Vacancy['employmentType'],
    dictionary: Partial<DelucruDictionaryOptions> | undefined,
): SelectOptionType[] => {
    if (ourEmployment === 'remote') {
        return dictionary?.workplace?.filter(({ value }) => value === '861') || [];
    }
    return [];
};

const populateExperience = (
    ourExperience: Vacancy['experience'],
    dictionary: Partial<DelucruDictionaryOptions> | undefined,
): SelectOptionType[] => {
    if (!ourExperience) return [];

    if (ourExperience === 'e00_no_experience') {
        return dictionary?.experiences?.filter(({ value }) => value === '341') || [];
    } else if (['e01_less_than1year', 'e1_1year'].includes(ourExperience)) {
        return dictionary?.experiences?.filter(({ value }) => value === '315') || [];
    } else if (['e2_2years', 'e3_3years', 'e4_4years'].includes(ourExperience)) {
        return dictionary?.experiences?.filter(({ value }) => value === '336') || [];
    } else if (['e5_5years', 'e6_10years'].includes(ourExperience)) {
        return dictionary?.experiences?.filter(({ value }) => value === '317') || [];
    } else return [];
};

const populateRegion = (
    ourRegion: Vacancy['region'],
    dictionary: Partial<DelucruDictionaryOptions> | undefined,
): SelectOptionType[] => {
    if (!dictionary?.regions || !ourRegion?.city) return [];

    const regionKey = ourRegion.lang === 'ru' ? 'displayCity' : 'city';
    const matchedCity = dictionary?.regions?.find(({ label }) => label === ourRegion?.[regionKey]);

    return matchedCity ? [matchedCity] : [];
};

const populateLanguages = (
    ourLanguages: Vacancy['languages'],
    dictionary: Partial<DelucruDictionaryOptions> | undefined,
    ourLangDict: LanguageTranslationResponseType['objects'] | undefined,
): SelectOptionType[] => {
    if (!ourLanguages?.length || !dictionary?.languages || !ourLangDict?.length) return [];
    const { currentLang } = getService('$rootScope') as AngularRootScope;

    const langKey: keyof LanguageTranslationType = currentLang === 'ru' ? 'russian' : 'english';

    const ourLangKeys = ourLanguages.map(({ name }) => {
        const foundInOurDict = ourLangDict.find(({ key }) => key === name);
        return foundInOurDict?.translation?.[langKey];
    });

    return dictionary?.languages?.filter(({ label }) => ourLangKeys.includes(label.toLowerCase())) || [];
};

export {
    populateActive,
    populateCurrency,
    populateExperience,
    populateJobTypes,
    populateLanguages,
    populateRegion,
    populateSkills,
    populateWorkPlace,
};
