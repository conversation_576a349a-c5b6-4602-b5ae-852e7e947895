@import 'common/styles/global-variables.module';
@import 'common/styles/mixins';

.wrapper {
    display: flex;
    gap: 8px;
    align-items: center;
}

.infoIcon {
    cursor: pointer;
}

.header {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 52.8px;
    padding: 0 40px 0 var(--main-gap);
}

.closeIcon {
    position: absolute;
    right: var(--main-gap);
    cursor: pointer;
    transition: fill 0.3s ease;

    &:hover {
        fill: $main-black;
    }
}
