<div id="block-vacancy" class="block-vacancy">
    <div class="breadcrumbs pointer-events-none">
        <ol class="breadcrumb hidden-xs hidden-sm position-relative" style="margin-bottom: 0">
            <li>
                <a>{{ 'vacancies' | translate }}</a>
            </li>
            <li class="active" view="tooltip-white-hint" tooltip-move data-tooltip="{{ 'Tutorial Master' }}">
                {{ 'Tutorial Master' }}
            </li>
        </ol>
    </div>
    <h3 class="pointer-events-none" style="margin-top: 0">
        <div style="text-align: center">
            <span class="main-page-title">Tutorial Master,</span>
            <a class="link link-edit link-style" style="font-size: 18px">{{ 'Google Ukraine' }}</a>
        </div>

        <div class="vacancy-nav vacancy-nav-mobile">
            <div class="vacancy-nav-block">
                <a class="link-to-vacancy-edit-page">
                    <button class="btn_default btn_success btn_thin" style="margin-right: 30px">
                        <svg style="margin-right: 5px" width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M7.37333 4.01333L7.98667 4.62667L1.94667 10.6667H1.33333V10.0533L7.37333 4.01333ZM9.77333 0C9.60667 0 9.43333 0.0666666 9.30667 0.193333L8.08667 1.41333L10.5867 3.91333L11.8067 2.69333C12.0667 2.43333 12.0667 2.01333 11.8067 1.75333L10.2467 0.193333C10.1133 0.06 9.94667 0 9.77333 0ZM7.37333 2.12667L0 9.5V12H2.5L9.87333 4.62667L7.37333 2.12667Z"
                                fill="#ffffff"
                            />
                        </svg>
                        <span>{{ 'Edited_1' | translate }}</span>
                    </button>
                </a>
                <button class="btn_default btn_success btn_thin pointer-events-none">
                    <svg
                        style="margin-right: 5px"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        version="1.1"
                        width="18"
                        fill="white"
                        height="18"
                        viewBox="0 0 24 24"
                    >
                        <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
                    </svg>
                    <span>{{ 'add_candidate' | translate }}</span>
                </button>
            </div>
            <div
                ng-if="vm.vacancy && !vm.isITVacancyValid && (vm.vacancy.status === 'open' || vm.vacancy.status === 'onhold' || vm.vacancy.status === 'inwork')"
                class="vacancy-nav-block"
            >
                <div class="improve-vacancy">
                    <a ng-click="vm.toValidateVacancy(vm.vacancy.localId)" class="improve-vacancy__main-block">
                        <span class="improve-vacancy__text">
                            {{ 'Please improve this vacancy to attract candidates' | translate }}
                        </span>
                        <img style="height: 17px; margin-left: 1px; margin-bottom: -1px" src="images/emoji-dancer.png" alt="" />
                    </a>
                </div>
            </div>
        </div>
    </h3>
    <div class="clearfix"></div>
    <div class="container-fluid nav-vacancy pointer-events-none" style="margin-left: 15px; margin-right: 15px; border-radius: 5px">
        <div style="display: flex; justify-content: space-between; align-items: center">
            <ul class="nav navbar-nav mobile">
                <li class="dropdown pull-left">
                    <a class="margin-for-tab" translate="information"></a>
                </li>
                <li class="pull-left">
                    <a class="selected">
                        {{ 'candidates_2' | translate }}
                        <span class="counter">
                            {{ vm.tabsDataCount.candidates }}
                        </span>
                    </a>
                </li>
                <li class="pull-left">
                    <!-- OrgParams when add AdviceForOrg change !-->
                    <a>
                        {{ 'Suggested candidates' | translate }}
                        <span class="counter suggest">{{ vm.suggestionsLengthExact }} + {{ vm.suggestionsLengthSuitable }}</span>
                    </a>
                    <!--                    <a ng-if="!vm.descriptionLength" class="whithTooltips">-->
                    <!--                        {{'Suggested candidates'|translate}}-->
                    <!--                        <i class="info-icon" tooltip-placement="bottom" tooltip-class="tooltip-outer center-xs auto-width first"-->
                    <!--                           uib-tooltip-html="$root.tooltips.Add_description"-->
                    <!--                           aria-hidden="true"></i>-->
                    <!--                    </a>-->
                </li>
                <li class="pull-left">
                    <a>
                        {{ 'Tasks_v2' | translate }}
                        <span class="counter">{{ vm.tabsDataCount.tasks }}</span>
                    </a>
                </li>
                <li class="pull-left">
                    <a translate="history"></a>
                </li>
                <!--                <li ng-show="$root.hideTariff && $root.me.recrutRole != 'client'" class="hidden-xs pull-left">-->
                <!--                    <a  translate="Email templates vacancy">-->
                <!--                    </a>-->
                <!--                </li>-->
                <li class="pull-left">
                    <button
                        id="disabledBtn-2"
                        ng-show="!$root.hideTariff && $root.me.recrutRole != 'client'"
                        class="btn btn-default margin-left disabled"
                        type="button"
                        translate="Email templates vacancy"
                    ></button>
                </li>
                <li
                    ng-show="
                        $root.me.orgParams.enableMailing === 'true' &&
                        $root.me.personParams.enableMailing === 'true' &&
                        vm.isSavedMailing &&
                        ($root.me.recrutRole == 'recruter' || $root.me.recrutRole == 'admin')
                    "
                    class="pull-left hidden-xs hidden-sm"
                >
                    <a class="hidden-xs pull-left">
                        {{ 'My mailings' | translate }}
                    </a>
                </li>
            </ul>
            <ul class="nav navbar-nav navbar-right hidden-sm hidden-xs vacancy-report-btn">
                <li ng-show="$root.hideTariff && $root.me.recrutRole != 'researcher'">
                    <div>
                        <button class="btn_default btn_success btn_thin">
                            {{ 'Vacancy report' | translate }}
                        </button>
                    </div>
                </li>
                <button id="disabledBtn" ng-if="!$root.hideTariff" ng-click="$root.disabledBtnFunc();" class="btn-default btn_success" type="button">
                    {{ 'Vacancy report' | translate }}
                </button>
            </ul>
        </div>
    </div>

    <div class="row vacancy-statuses">
        <div ng-if="!vm.showSearchCandidate" class="col-lg-3 statuses no-padding-sm">
            <div ng-class="{ 'active-onboarding-action-stages': vm.showFirstPopup }" style="border-radius: 5px; padding-bottom: 0">
                <h4 class="pull-left">
                    <i class="fa fa-align-right" aria-hidden="true"></i>
                    {{ 'Stages' | translate }}
                </h4>
                <div class="pull-right settings">
                    <i
                        id="openSettings"
                        ng-click="vm.candidateInVacancy('extra_status',$event)"
                        class="fa fa-cog"
                        aria-hidden="true"
                        title="{{ 'Setting the stage' | translate }}"
                    ></i>
                </div>
                <div class="clearfix"></div>
                <div class="sortableOptions" style="position: relative">
                    <div ng-show="vm.showFirstPopup" class="gamification-popup-in-vacancy-stages onboarding">
                        <span
                            class="gamification-blue-title-secondary instruction-popup-wrapper-text instruction-popup-wrapper-text-stones"
                            translate="Here are the stages of closing a vacancy. There is a figure opposite each stage, it shows the number of candidates at this stage."
                        ></span>
                        <button ng-click="vm.showFirstPopup = false;vm.showSecondPopup = true;" class="btn_default btn_orange">
                            {{ 'Next_2' | translate }}
                        </button>
                    </div>
                    <div ng-show="vm.showSecondPopup" class="gamification-popup-in-vacancy-stages onboarding">
                        <span
                            class="gamification-blue-title-secondary instruction-popup-wrapper-text instruction-popup-wrapper-text-stones"
                            ng-bind-html="'We still have one candidate - Sandra. And shes now at the Longlist stage. Lets set up an interview for Sandra.' |translate"
                        ></span>
                        <button ng-click="vm.showSecondPopup = false;$root.onboardingShowStageSix = true" class="btn_default btn_orange">
                            {{ 'Next_2' | translate }}
                        </button>
                    </div>
                    <div
                        ng-click="vm.candidateInVacancy(status)"
                        ng-class="{
                            'status active': status.value === vm.activeStage,
                            'active-onboarding-action-stages': vm.showSecondPopup && status.value === 'longlist',
                            'active-onboarding-action-second-nav':
                                vm.activeStage === 'longlist' && $root.onboardingActiveStage === 'stage7' && status.value === 'interview',
                            'status approved': status.value === 'approved'
                        }"
                        style="padding: 10px"
                        ng-repeat="status in vm.VacancyStatusFiltered track by $index"
                        ng-context-menu="vm.menuOptionsStages"
                    >
                        <i ng-if="status.movable == true && vm.showMoveble == true" class="fa fa-arrows-v" aria-hidden="true"></i>
                        <a ng-if="!status.customInterviewStateId">{{ status.value | translate }}</a>
                        <a ng-if="status.customInterviewStateId">{{ status.value | translate }}</a>
                        <div class="counter inline-block-class notranslate">{{ status.count }}</div>
                        <div class="clearfix"></div>
                    </div>
                    <hr style="margin-top: 15px; margin-bottom: 0" />
                    <div
                        ng-click="vm.candidateInVacancy(status)"
                        ng-class="{
                            'status active': status.value === vm.activeStage,
                            'active-onboarding-action-stages': vm.showSecondPopup && status.value === 'longlist',
                            'active-onboarding-action-second-nav':
                                vm.activeStage === 'longlist' && $root.onboardingActiveStage === 'stage7' && status.value === 'interview',
                            'status approved': status.value === 'approved'
                        }"
                        style="padding: 10px"
                        ng-repeat="status in vm.VacancyStatusFilteredClosed track by $index"
                        ng-context-menu="vm.menuOptionsStages"
                    >
                        <i ng-if="status.movable == true && vm.showMoveble == true" class="fa fa-arrows-v" aria-hidden="true"></i>
                        <a ng-if="!status.customInterviewStateId" style="color: #202021">
                            {{ status.value | translate }}
                        </a>
                        <a ng-if="status.customInterviewStateId" style="color: #202021">
                            {{ status.value | translate }}
                        </a>
                        <div class="counter inline-block-class notranslate">{{ status.count }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div
            ng-show="(!vm.visible || $root.me.recrutRole !== 'client') && vm.activeName != 'extra_status'"
            class="col-lg-9 status-info no-padding-sm"
            ng-class="{ 'col-lg-12': vm.showSearchCandidate }"
        >
            <div ng-show="vm.tableParams.data.length > 1" class="sorting pull-left">
                <label>{{ 'Sort by' | translate }}:</label>
                <select ng-model="vm.sortValue">
                    <option selected value="addInVacancyDate">
                        {{ 'Added date to vacancy' | translate }}
                    </option>
                    <option value="lastActionInVacancyDate">
                        {{ 'Date of last action on this vacancy' | translate }}
                    </option>
                    <option value="addedByUser">
                        {{ 'By added user' | translate }}
                    </option>
                    <option value="alphabetical">
                        {{ 'Alphabetical order (A-Z)' | translate }}
                    </option>
                    <option value="overallScore">
                        {{ 'By overall score' | translate }}
                    </option>
                </select>
            </div>
            <div class="show-list pull-right hidden-xs hidden-sm" style="display: flex">
                <a
                    ng-if="!vm.showSearchCandidate"
                    ng-click="vm.showSearchCandidateFunc()"
                    class="btn btn-primary btn-yellow pointer-events-none"
                    style="height: 24px !important"
                    href=""
                    type="button"
                >
                    {{ 'Show a list' | translate }}
                </a>
                <a
                    id="excel-export"
                    ng-show="$root.me.recrutRole == 'admin' && vm.showSearchCandidate && $root.hideTariff"
                    ng-click="vm.exportToExcelByStage()"
                    class="excel-export"
                    title="{{ 'Export to Excel' | translate }}"
                >
                    {{ 'Export to Excel' | translate }}
                    <i class="fa fa-file-excel-o" aria-hidden="true"></i>
                </a>
                <a
                    ng-show="vm.showSearchCandidate"
                    ng-click="vm.hideSearchCandidateFunc()"
                    class="btn btn-primary btn-yellow pointer-events-none"
                    style="height: 24px !important"
                    href=""
                    type="button"
                >
                    {{ 'Сandidates in stages' | translate }}
                </a>
            </div>
            <button
                ng-show="vm.candidatesAddToVacancyIds.length > 0"
                ng-click="$event.stopPropagation();vm.openVacancyCandidateChangeStatus(vm.candidatesAddToVacancyIds);"
                class="btn_default btn_success btn_thin pull-right pointer-events-none"
                ng-class="{
                    'hidden-xs':
                        vm.showSearchCandidate ||
                        candidate.isInterview ||
                        candidate.state == 'interview' ||
                        candidate.state == 'tech_interview' ||
                        candidate.state == 'hr_interview' ||
                        candidate.state == 'interview_with_the_boss' ||
                        candidate.state == 'interview_with_the_client'
                }"
            >
                {{ 'change_status_1' | translate }} ({{ vm.candidatesAddToVacancyIds.length }})
            </button>
            <div class="clearfix"></div>
            <div ng-if="!vm.showSearchCandidate" class="row stage-info pointer-events-none">
                <div class="stage-title">
                    <h4 ng-show="!vm.activeCustomStageName && !vm.customStageOpenedInNewTab">
                        {{ vm.activeName | translate }}
                    </h4>
                    <h4 ng-show="vm.activeCustomStageName && !vm.customStageOpenedInNewTab">
                        {{ vm.activeCustomStageName | translate }}
                    </h4>

                    <h4 ng-show="!vm.pastStageIsCustom && vm.customStageOpenedInNewTab">
                        {{ vm.pastActiveName | translate }}
                    </h4>
                    <h4 ng-show="vm.pastStageIsCustom && vm.customStageOpenedInNewTab">
                        {{ vm.pastActiveCustomStageName | translate }}
                    </h4>
                </div>
                <div
                    class="stage-actions"
                    ng-hide="
                        (vm.showTable === 'not available' || (vm.showTable === undefined && vm.loadingCandidates == false)) && !vm.noCandidatesInThisVacancy
                    "
                >
                    <div
                        ng-if="$root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter'"
                        class="action mailing"
                        ng-class="{ disabled: vm.selectedCandidates.length > 15 }"
                    >
                        <a
                            ng-click="vm.toCreateMailing(vm.selectedCandidates.length > 15)"
                            class="mailing-icon"
                            toggle-element-visibility-hover="{{ vm.selectedCandidates.length > 15 }}"
                            backdrop-id="'block-vacancy'"
                            target-id="'mailing-info'"
                            aria-hidden="true"
                            tabindex="0"
                            ng-attr-title="{{ vm.selectedCandidates.length < 16 ? 'Send letter' : ('' | translate) }}"
                        >
                            <span class="new-label">New</span>
                            <img ng-class="{ show: vm.selectedCandidates.length < 16 }" src="/images/sprite/mass-mailing.svg" alt="" />
                            <img ng-class="{ show: vm.selectedCandidates.length > 15 }" src="/images/sprite/mass-mailing-disabled.svg" alt="" />
                            <i ng-if="vm.selectedCandidates.length > 15" class="info-icon mailing-info-icon"></i>
                        </a>
                        <div id="mailing-info" class="mailing-info hidden">
                            <span translate="Maximum number of recipients - 15"></span>
                        </div>
                    </div>

                    <div class="action action__mass-download">
                        <input id="fileTemplate" class="hidden" type="file" oi-file="optionsForTemplate" />
                        <a id="downloadDoc" style="display: none"></a>
                        <a ng-click="vm.downloadDoc()" title="{{ 'Download as docx' | translate }}">
                            <img src="/images/sprite/download.svg" alt="{{ 'Download as docx' | translate }}" />
                        </a>
                    </div>
                </div>
            </div>
            <div ng-show="vm.dataForVacancy.length !== 0" class="row header grey-header">
                <div class="col-lg-1 blockStage">
                    <div
                        class="vacancy-checkbox-wrapper"
                        ng-hide="
                            ((vm.showTable === 'not available' || (vm.showTable === undefined && vm.loadingCandidates == false)) &&
                                !vm.noCandidatesInThisVacancy) ||
                            vm.dataForVacancy.length < 2
                        "
                    >
                        <input id="checkAll" ng-click="vm.checkAllForMailing()" class="checkbox" ng-model="vm.allCandidatesChecked" type="checkbox" />
                        <label for="checkAll"></label>
                    </div>
                </div>
                <div class="col-lg-11 text-right links hidden-xs hidden-sm">
                    <div class="header-button">
                        <a ng-click="vm.addInVacancy()" title="{{ 'Move_candidates_to_another_vacancy' | translate }}">
                            {{ 'Transfer_to_vacancy' | translate }}
                            <img style="margin-left: 5px; width: 10px; height: 15.4px" src="../images/sprite/icon-transfer-vacancy.svg" />
                        </a>
                    </div>
                    <div ng-show="$root.me.recrutRole != 'researcher' && $root.me.recrutRole != 'freelancer'" class="header-button">
                        <a title="{{ 'Send test' | translate }}">
                            {{ 'Send test' | translate }}
                            <i class="fa fa-check-square-o" aria-hidden="true"></i>
                        </a>
                    </div>
                    <div
                        ng-show="
                            $root.me.recrutRole != 'researcher' &&
                            $root.me.recrutRole != 'freelancer' &&
                            (($root.me.recrutRole == 'admin' && $root.me.personParams.enableDownloadToExcel != 'N') ||
                                $root.me.personParams.enableDownloadToExcel == 'Y')
                        "
                        class="header-button pointer-events-none"
                    >
                        <a ng-show="$root.hideTariff" ng-click="vm.exportToExcelModal()" title="{{ 'Export to Excel' | translate }}">
                            {{ 'Export to Excel' | translate }}
                            <i class="fa fa-file-excel-o" aria-hidden="true"></i>
                        </a>
                        <button
                            id="disabledBtn-4"
                            ng-show="!$root.hideTariff"
                            ng-click="$root.disabledBtnFunc();"
                            class="btn btn-default disabled"
                            type="button"
                        >
                            {{ 'Export to Excel' | translate }}
                            <i class="fa fa-file-excel-o" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div ng-show="$root.me.recrutRole !== 'client'" class="header-button padding-zero pointer-events-none">
                        <a id="export_in_excel" class="hidden" href=""></a>
                        <a ng-click="vm.sendCandidatesToClient()">
                            {{ 'Send to the Client' | translate }}
                            <i class="fa fa-envelope" aria-hidden="true"></i>
                        </a>
                    </div>
                </div>
            </div>
            <table
                class="table"
                ng-hide="(vm.showTable === 'not available' || (vm.showTable === undefined && vm.loadingCandidates == false)) && !vm.noCandidatesInThisVacancy"
                ng-table="vm.tableParams"
                template-pagination="custom/pager"
            >
                <thead>
                    <tr>
                        <th>
                            №
                            <i class="fa fa-sort pull-right" aria-hidden="true"></i>
                        </th>
                        <th ng-if="$root.me.orgParams.videoInterview && ($root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter')">VCV</th>
                        <th>
                            {{ 'vacancy_save_1' | translate }}
                            <i class="fa fa-sort pull-right" aria-hidden="true"></i>
                        </th>
                        <th>
                            {{ 'region' | translate }}
                            <i class="fa fa-sort pull-right" aria-hidden="true"></i>
                        </th>
                        <th></th>
                        <th ng-if="!vm.showSearchCandidate">
                            {{ 'stage' | translate }}
                            <i class="fa fa-sort pull-right" aria-hidden="true"></i>
                        </th>
                        <th class="hidden-xs">
                            {{ 'source' | translate }}
                            <i class="fa fa-sort pull-right" aria-hidden="true"></i>
                        </th>
                        <th class="hidden-xs">
                            {{ 'comment' | translate }}
                            <i class="fa fa-sort pull-right" aria-hidden="true"></i>
                        </th>
                        <th>
                            {{ 'salary' | translate }}
                            <i class="fa fa-sort pull-right" aria-hidden="true"></i>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        ng-click="candidate.mailing = !candidate.mailing;$event.stopPropagation();vm.countPicked(candidate)"
                        ng-class="{
                            archived: candidate.candidateId.status == 'archived',
                            add_color: candidate.added == true,
                            checked_candidate: candidate.mailing
                        }"
                        ng-repeat-start="candidate in vm.dataForVacancy track by $index"
                    >
                        <td ng-if="candidate.candidateId.status != 'archived' && vm.activeName != null" class="checklist" style="padding-left: 13px">
                            <div ng-click="$event.stopPropagation();vm.countPicked(candidate)" style="padding-bottom: 24px">
                                <input id="{{ $index }}" class="checkbox" ng-model="candidate.mailing" type="checkbox" />
                                <label for="{{ $index }}"></label>
                            </div>
                        </td>
                        <td ng-if="candidate.candidateId.status == 'archived' && candidate.state != 'approved' && vm.activeName != null" class="checklist"></td>
                        <td
                            ng-click="$event.stopPropagation();"
                            class="preview text-center width-for-table-eye hidden-xs"
                            style="pointer-events: none"
                            priview=""
                            pageid="candidate"
                            candidate="candidate.candidateId"
                        >
                            <div>
                                <i class="fa fa-address-card-o text_change" title="{{ 'CV preview' | translate }}" aria-hidden="true"></i>
                            </div>
                        </td>
                        <td class="name" style="pointer-events: none" data-title="'Name'">
                            <a
                                ng-class="{ openStatus: candidate.candidateId.openStatus === 'Y' }"
                                ng-href="{{ vm.setHref($event, candidate.candidateId.localId, 'candidate', vm.sliderId) }}"
                                ng-mousedown="vm.routeOnCandidate($event,candidate.candidateId.localId,'candidate', vm.sliderId);$event.stopPropagation();"
                                ng-mouseover="vm.onCandidateMouseOver(candidate.candidateId.localId)"
                                ng-mouseleave="vm.onCandidateMouseLeave()"
                                ng-context-menu="vm.menuOptions"
                            >
                                {{ 'Clever' | translate }} {{ 'Sandra' | translate }}
                            </a>
                        </td>
                        <td ng-show="vm.activeName === 'applied_people'">
                            <div style="overflow: hidden; text-overflow: ellipsis; width: 350px" title="{{ candidate.allMatchedSkills }}">
                                <span
                                    style="color: #00b549; font-weight: 500; white-space: nowrap; text-overflow: ellipsis; -o-text-overflow: ellipsis"
                                    ng-repeat="skill in candidate.matchedSkills"
                                >
                                    {{ skill.skillName }}
                                    <img style="width: 15px; height: 15px; margin-bottom: 2px" src="images/sprite/ok_check_done-512.png" alt="" />
                                    <span style="margin-left: -3px" ng-hide="$last">,</span>
                                </span>
                            </div>
                        </td>
                        <td class="evaluate hidden-xs" style="pointer-events: none" ng-hide="vm.activeName === 'applied_people'" data-title="evaluate">
                            <div class="overall-score">
                                <div class="score">
                                    <div
                                        ng-class="{
                                            score_dislike: candidate.scoreCardResultDto.mainScore < 2,
                                            score_normal: candidate.scoreCardResultDto.mainScore === 2,
                                            score_like: candidate.scoreCardResultDto.mainScore > 2
                                        }"
                                    >
                                        <a
                                            ng-if="candidate.scoreCardResultDto.mainScore === undefined"
                                            ng-click="vm.routeOnCandidateEvaluation({'isFromVacancyToEvaluate': true, 'isFromVacancyToCandidate': vm.vacancy.vacancyId, 'sliderDataId': vm.sliderId, 'id': candidate.candidateId.localId, 'candidateObj': candidate, 'vacancyName': vm.vacancy.position})"
                                        >
                                            <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M11.1333 7.01463C11.1333 6.55164 10.7545 6.17284 10.2915 6.17284H5.93098L6.48656 3.49593L6.5034 3.30232C6.5034 3.04136 6.39396 2.80566 6.22561 2.62888L5.56059 1.96387L1.40213 6.12233C1.17484 6.34961 1.03174 6.66949 1.03174 7.01463V12.4863C1.03174 13.185 1.59574 13.749 2.29443 13.749H7.97654C8.49845 13.749 8.9446 13.4291 9.13821 12.9829L11.0407 8.52986C11.0996 8.38675 11.1333 8.22681 11.1333 8.06687V7.01463V7.01463ZM19.9721 10.3818H14.29C13.7681 10.3818 13.3219 10.7017 13.1283 11.1478L11.2259 15.6009C11.1669 15.744 11.1333 15.904 11.1333 16.0639V17.1162C11.1333 17.5791 11.5121 17.9579 11.9751 17.9579H16.3356L15.78 20.6349L15.7631 20.8369C15.7631 21.0978 15.8726 21.3335 16.0409 21.5103L16.7059 22.1669L20.8644 18.0085C21.0917 17.7812 21.2348 17.4613 21.2348 17.1162V11.6445C21.2348 10.9458 20.6708 10.3818 19.9721 10.3818Z"
                                                    fill="white"
                                                />
                                                <path
                                                    d="M10.2915 5.82284H6.36108L6.82926 3.56706L6.83346 3.54683L6.83525 3.52625L6.85208 3.33264L6.8534 3.31751V3.30232C6.8534 2.94067 6.7015 2.62108 6.47905 2.38751L6.47913 2.38743L6.47309 2.3814L5.80808 1.71638L5.56059 1.46889L5.3131 1.71638L1.15464 5.87484C0.864601 6.16488 0.681738 6.57267 0.681738 7.01463V12.4863C0.681738 13.3783 1.40244 14.099 2.29443 14.099H7.97654C8.64525 14.099 9.21316 13.6894 9.45929 13.1223L9.46007 13.1205L11.3625 8.66736L11.3626 8.66738L11.3643 8.66312C11.4398 8.47983 11.4833 8.27473 11.4833 8.06687V7.01463C11.4833 6.35834 10.9478 5.82284 10.2915 5.82284ZM12.8072 11.0085L12.8065 11.0103L10.904 15.4634L10.904 15.4634L10.9022 15.4677C10.8268 15.651 10.7833 15.8561 10.7833 16.0639V17.1162C10.7833 17.7724 11.3188 18.3079 11.9751 18.3079H15.9055L15.4373 20.5637L15.4329 20.5846L15.4312 20.6058L15.4143 20.8078L15.4131 20.8223V20.8369C15.4131 21.1985 15.565 21.5181 15.7875 21.7517L15.7874 21.7518L15.795 21.7594L16.46 22.416L16.7075 22.6603L16.9534 22.4144L21.1119 18.2559C21.4019 17.9659 21.5848 17.5581 21.5848 17.1162V11.6445C21.5848 10.7525 20.8641 10.0318 19.9721 10.0318H14.29C13.6213 10.0318 13.0534 10.4413 12.8072 11.0085Z"
                                                    stroke="black"
                                                    stroke-opacity="0.7"
                                                    stroke-width="0.7"
                                                />
                                                <rect
                                                    x="0.223633"
                                                    y="1.72729"
                                                    width="1.21218"
                                                    height="29.9005"
                                                    transform="rotate(-45 0.223633 1.72729)"
                                                    fill="#454545"
                                                />
                                            </svg>
                                        </a>
                                        <svg
                                            ng-if="candidate.scoreCardResultDto.mainScore === 0"
                                            width="31"
                                            height="18"
                                            viewBox="0 0 31 18"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                d="M12.167 7.96069V9.62736C12.167 10.544 12.917 11.294 13.8337 11.294H19.0837L18.2503 15.1274V15.3774C18.2503 15.7107 18.417 16.044 18.5837 16.294L19.5003 17.1274L25.0003 11.6274C25.3337 11.294 25.5003 10.8774 25.5003 10.4607V2.12736C25.5003 1.21069 24.7503 0.460693 23.8337 0.460693H16.3337C15.667 0.460693 15.0837 0.87736 14.8337 1.46069L12.3337 7.37736C12.167 7.54403 12.167 7.71069 12.167 7.96069Z"
                                                fill="#CCCCCC"
                                            />
                                            <path d="M30.5003 0.460693H27.167V10.4607H30.5003V0.460693Z" fill="#CCCCCC" />
                                            <path
                                                d="M2.49967 11.2941H7.74967L6.99967 15.1274V15.3774C6.99967 15.7108 7.16634 16.0441 7.33301 16.2941L8.24967 17.1274L12.6663 12.7108C11.4163 12.2108 10.4997 11.0441 10.4997 9.62744V7.96077C10.4997 7.54411 10.583 7.12744 10.7497 6.71077L13.2497 0.794108C13.2497 0.710775 13.333 0.710775 13.333 0.627441C13.083 0.460775 12.833 0.377441 12.4997 0.377441H4.99967C4.33301 0.377441 3.74967 0.794108 3.49967 1.37744L0.999675 7.29411C0.916341 7.54411 0.833008 7.71077 0.833008 7.96077V9.62744C0.833008 10.5441 1.58301 11.2941 2.49967 11.2941Z"
                                                fill="#CCCCCC"
                                            />
                                        </svg>

                                        <svg
                                            ng-if="candidate.scoreCardResultDto.mainScore === 1"
                                            width="20"
                                            height="18"
                                            viewBox="0 0 20 18"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                d="M12.4997 0.544189H4.99967C4.30801 0.544189 3.71634 0.960856 3.46634 1.56086L0.949674 7.43586C0.874674 7.62752 0.833008 7.82752 0.833008 8.04419V9.71086C0.833008 10.6275 1.58301 11.3775 2.49967 11.3775H7.75801L6.96634 15.1859L6.94134 15.4525C6.94134 15.7942 7.08301 16.1109 7.30801 16.3359L8.19134 17.2109L13.683 11.7192C13.983 11.4192 14.1663 11.0025 14.1663 10.5442V2.21086C14.1663 1.29419 13.4163 0.544189 12.4997 0.544189ZM15.833 0.544189V10.5442H19.1663V0.544189H15.833Z"
                                                fill="#CCCCCC"
                                            />
                                        </svg>

                                        <svg
                                            ng-if="candidate.scoreCardResultDto.mainScore === 2"
                                            width="20"
                                            height="21"
                                            viewBox="0 0 20 21"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                d="M10 5.04419C10 4.58586 9.625 4.21086 9.16667 4.21086H4.85L5.4 1.56086L5.41667 1.36919C5.41667 1.11086 5.30833 0.877523 5.14167 0.702523L4.48333 0.0441895L0.366667 4.16086C0.141667 4.38586 0 4.70252 0 5.04419V10.4609C0 11.1525 0.558333 11.7109 1.25 11.7109H6.875C7.39167 11.7109 7.83333 11.3942 8.025 10.9525L9.90833 6.54419C9.96667 6.40252 10 6.24419 10 6.08586V5.04419ZM18.75 8.37752H13.125C12.6083 8.37752 12.1667 8.69419 11.975 9.13586L10.0917 13.5442C10.0333 13.6859 10 13.8442 10 14.0025V15.0442C10 15.5025 10.375 15.8775 10.8333 15.8775H15.15L14.6 18.5275L14.5833 18.7275C14.5833 18.9859 14.6917 19.2192 14.8583 19.3942L15.5167 20.0442L19.6333 15.9275C19.8583 15.7025 20 15.3859 20 15.0442V9.62752C20 8.93586 19.4417 8.37752 18.75 8.37752Z"
                                                fill="#CCCCCC"
                                            />
                                        </svg>

                                        <svg
                                            ng-if="candidate.scoreCardResultDto.mainScore === 3"
                                            width="20"
                                            height="18"
                                            viewBox="0 0 20 18"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                d="M0.833008 17.5441H4.16634V7.54411H0.833008V17.5441ZM19.1663 8.37744C19.1663 7.46077 18.4163 6.71077 17.4997 6.71077H12.2413L13.033 2.90244L13.058 2.63577C13.058 2.29411 12.9163 1.97744 12.6913 1.75244L11.808 0.877441L6.32467 6.36911C6.01634 6.66911 5.83301 7.08577 5.83301 7.54411V15.8774C5.83301 16.7941 6.58301 17.5441 7.49967 17.5441H14.9997C15.6913 17.5441 16.283 17.1274 16.533 16.5274L19.0497 10.6524C19.1247 10.4608 19.1663 10.2608 19.1663 10.0441V8.37744Z"
                                                fill="#CCCCCC"
                                            />
                                        </svg>

                                        <svg
                                            ng-if="candidate.scoreCardResultDto.mainScore === 4"
                                            width="31"
                                            height="18"
                                            viewBox="0 0 31 18"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                d="M0.833008 17.5441H4.16634V7.54411H0.833008V17.5441ZM19.1663 8.37744C19.1663 7.46077 18.4163 6.71077 17.4997 6.71077H12.2497L12.9997 2.87744V2.62744C12.9997 2.29411 12.833 1.96077 12.6663 1.71077L11.7497 0.877441L6.33301 6.37744C5.99967 6.71077 5.83301 7.12744 5.83301 7.54411V15.8774C5.83301 16.7941 6.58301 17.5441 7.49967 17.5441H14.9997C15.6663 17.5441 16.2497 17.1274 16.4997 16.5441L18.9997 10.7108C19.1663 10.4608 19.1663 10.2941 19.1663 10.0441V8.37744Z"
                                                fill="#CCCCCC"
                                            />
                                            <path
                                                d="M28.7498 6.71077H23.4998L24.2498 2.87744V2.62744C24.2498 2.29411 24.0832 1.96077 23.9165 1.71077L22.9998 0.877441L18.5832 5.29411C19.8332 5.79411 20.7498 6.96077 20.7498 8.37744V10.0441C20.7498 10.4608 20.6665 10.8774 20.4998 11.2941L17.9998 17.2108C17.9998 17.2941 17.9165 17.2941 17.9165 17.3774C18.1665 17.5441 18.4165 17.6274 18.7498 17.6274H26.2498C26.9165 17.6274 27.4998 17.2108 27.7498 16.6274L30.2498 10.7108C30.3332 10.5441 30.3332 10.2941 30.3332 10.1274V8.46077C30.4165 7.46077 29.6665 6.71077 28.7498 6.71077Z"
                                                fill="#CCCCCC"
                                            />
                                        </svg>
                                    </div>
                                    <div ng-if="candidate.scoreCardResultDto.votersCount" class="scores-count">
                                        ({{ candidate.scoreCardResultDto.votersCount }})
                                    </div>
                                </div>
                                <a
                                    ng-if="candidate.scoreCardResultDto"
                                    ng-click="vm.routeOnCandidateEvaluation({'isFromVacancyToEvaluate': true, 'isFromVacancyToCandidate': vm.vacancy.vacancyId, 'sliderDataId': vm.sliderId, 'id': candidate.candidateId.localId, 'candidateObj': candidate, 'vacancyName': vm.vacancy.position})"
                                    target="_blank"
                                    translate="More info"
                                ></a>
                            </div>
                        </td>
                        <td ng-if="candidate.state == 'approved'" class="date text-center" ng-hide="vm.activeName === 'applied_people'">
                            {{ candidate.dateEmployee | dateFormat }}
                        </td>
                        <td
                            ng-if="candidate.candidateId.status != 'archived' && candidate.state != 'approved'"
                            class="date"
                            ng-hide="vm.activeName === 'applied_people'"
                        >
                            <span ng-if="candidate.dateInterview" ng-click="vm.openChangeVacancyInterviewDate(candidate);$event.stopPropagation();">
                                {{ candidate.dateInterview | dateFormat : true }}
                                <i class="fa fa-pencil" aria-hidden="true"></i>
                            </span>
                            <span
                                ng-click="vm.openChangeVacancyInterviewDate(candidate);$event.stopPropagation();"
                                class="set-interview"
                                ng-class="{
                                    show:
                                        !candidate.dateInterview &&
                                        (candidate.isInterview ||
                                            candidate.state == 'interview' ||
                                            candidate.state == 'tech_interview' ||
                                            candidate.state == 'hr_interview' ||
                                            candidate.state == 'interview_with_e_boss' ||
                                            candidate.state == 'interview_with_the_client')
                                }"
                            >
                                {{ 'Set interview time' | translate }}
                                <i class="fa fa-calendar" aria-hidden="true"></i>
                            </span>
                        </td>
                        <td
                            class="button-outer"
                            ng-class="{
                                'hidden-xs':
                                    vm.showSearchCandidate ||
                                    candidate.isInterview ||
                                    candidate.state == 'interview' ||
                                    candidate.state == 'tech_interview' ||
                                    candidate.state == 'hr_interview' ||
                                    candidate.state == 'interview_with_the_boss' ||
                                    candidate.state == 'interview_with_the_client'
                            }"
                        >
                            <div
                                ng-class="{
                                    'active-onboarding-action-btn':
                                        ($root.onboardingShowStageSix && $root.onboardingActiveStage === 'stage6') ||
                                        ($root.onboardingActiveStage === 'stage7' &&
                                            !$root.onboardingStageSevenBtnPressed &&
                                            !$root.onboardingStage7StatusPopupDone)
                                }"
                            >
                                <button
                                    ng-click="$event.stopPropagation();vm.openVacancyCandidateChangeStatus();"
                                    class="btn_default btn_success btn_thin"
                                    ng-disabled="!$root.onboardingShowStageSix && $root.onboardingActiveStage === 'stage6' "
                                >
                                    {{ 'change_status' | translate }}
                                </button>
                            </div>
                        </td>
                        <td ng-show="vm.activeName === 'applied_people'" style="width: 40px">
                            <span style="color: #666" ng-switch="candidate.candidateId.source">
                                <span ng-switch-when="add_from_email">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/Mail.svg"
                                        title="{{ 'This candidate was added from resume from email' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="update_from_email">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/Mail.svg"
                                        title="{{ 'This candidate was updated from resume from email' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="add_from_linkedin">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/linkedinLogo.svg"
                                        title="{{ 'This candidate was added from resume from linkedin' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="add_from_linkedinNew">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/linkedinLogo.svg"
                                        title="{{ 'This candidate was added from resume from linkedin' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="update_from_linkedin">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/linkedinLogo.svg"
                                        title="{{ 'This candidate was updated from resume from linkedin' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="update_from_linkedinNew">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/linkedinLogo.svg"
                                        title="{{ 'This candidate was updated from resume from linkedin' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="add_from_hh">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/hhLogo.svg"
                                        title="{{ 'This candidate was added from resume from headhunter' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="update_from_hh">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/hhLogo.svg"
                                        title="{{ 'This candidate was updated from resume from headhunter' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="add_from_workua">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/workuaLogo.svg"
                                        title="{{ 'This candidate was added from resume from work.ua' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="update_from_workua">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/workuaLogo.svg"
                                        title="{{ 'This candidate was updated from resume from work.ua' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="add_from_rabotaua">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/Rabota_ua_logo.svg"
                                        title="{{ 'This candidate was added from resume from rabota.ua' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="update_from_rabotaua">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/Rabota_ua_logo.svg"
                                        title="{{ 'This candidate was updated from resume from rabota.ua' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="add_from_superjob">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/superJob.svg"
                                        title="{{ 'This candidate was added from resume from super job' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="update_from_superjob">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/superJob.svg"
                                        title="{{ 'This candidate was updated from resume from super job' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="add_from_cvlv">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/favicon_cvlv.svg"
                                        title="{{ 'This candidate was added from resume from cvlv' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="add_from_jobstutby">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/jobs_tut_by.svg"
                                        title="{{ 'This candidate was added from resume from jobsTutBy' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="update_from_jobstutby">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/jobs_tut_by.svg"
                                        title="{{ 'This candidate was updated from resume from jobsTutBy' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="update_from_cvlv">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/favicon_cvlv.svg"
                                        title="{{ 'This candidate was updated from resume from cvlv' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="add_from_jobkg">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/jobkg.svg"
                                        title="{{ 'This candidate was added from resume from jobkg' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="update_from_jobkg">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/jobkg.svg"
                                        title="{{ 'This candidate was updated from resume from jobkg' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="add_manually">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/manually.svg"
                                        title="{{ 'This candidate was added manually' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="add_from_recall">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/response.svg"
                                        title="{{ 'This candidate was added from recall' | translate }}"
                                    />
                                </span>
                                <span ng-switch-when="add_from_recommendation">
                                    <img
                                        class="workuaSmallLogo"
                                        style="width: 24px; height: 24px"
                                        src="../images/sprite/recommend.svg"
                                        title="{{ 'This candidate was added from recall' | translate }}"
                                    />
                                </span>
                            </span>
                        </td>
                        <td
                            ng-if="candidate.candidateId.status != 'archived'"
                            ng-click="$event.stopPropagation()"
                            class="responsible hidden-xs"
                            ng-hide="vm.activeName === 'applied_people'"
                        >
                            <responsible-person
                                ng-show="responsible.userId && responsible.userId == candidate.creatorId"
                                ng-repeat="responsible in vm.persons"
                                avatar-id="responsible.avatarId"
                                full-name="responsible.fullName"
                                user-id="responsible.userId"
                            ></responsible-person>
                        </td>
                        <td
                            ng-if="candidate.candidateId.status != 'archived'"
                            ng-show="vm.showSearchCandidate"
                            ng-class="{ 'fixed-width': candidate.candidateId.status !== 'archived' }"
                        >
                            <span title="{{ candidate.state | translate }}">
                                {{ candidate.state | translate | limitTo : 16 }}{{ candidate.state.length > 16 ? '...' : '' }}
                            </span>
                        </td>
                        <td ng-if="candidate.candidateId.status != 'archived'" class="add-comment-text text-right hidden-xs" style="width: 220px">
                            <span
                                ng-show="vm.activeName != 'approved'"
                                ng-click="vm.showModalAddCommentToCandidate(candidate);$event.stopPropagation();"
                                class="comment text-left pull-left"
                                title="{{ candidate.dm | dateFormat2 }}"
                            >
                                {{ candidate.comment | limitTo : 85 }}
                                <span ng-show="candidate.comment.length > 53 && vm.activeName != 'approved'" class="comment">...</span>
                            </span>
                            <a
                                ng-show="
                                    vm.activeName == 'approved' && ($root.me.personParams.enableEmployee == 'Y' || $root.me.orgParams.enableEmployee == 'Y')
                                "
                                ng-click="$event.stopPropagation();"
                                class="btn btn-success"
                                href="#/company/employee/add/{{ candidate.candidateId.localId }}"
                            >
                                {{ 'our employee' | translate }}
                            </a>
                            <a
                                ng-show="
                                    vm.activeName == 'approved' && ($root.me.personParams.enableEmployee != 'Y' || $root.me.orgParams.enableEmployee != 'Y')
                                "
                                class="btn btn-success hidden"
                                type="button"
                                href="#/hr-module-info"
                            >
                                {{ 'our employee' | translate }}!
                            </a>
                        </td>
                        <td ng-if="candidate.candidateId.status == 'archived' && vm.activeName == null"></td>
                        <td
                            ng-if="candidate.candidateId.status == 'archived'"
                            class="candidate-deleted"
                            translate="Candidate has been removed from the database"
                            colspan="2"
                        ></td>
                        <td ng-if="candidate.candidateId.status == 'archived'" class="text-center hidden-xs" colspan="2">
                            <button
                                ng-click="vm.restoreCandidate(candidate.candidateId.candidateId, candidate.candidateId.localId, candidate.candidateId.fullName)"
                                class="restore-candidate"
                                translate="Restore"
                            ></button>
                        </td>
                        <td ng-if="candidate.candidateId.status == 'archived'" class="text-center hidden-xs" colspan="3">
                            <span
                                ng-click="vm.showDeleteInterview(candidate);$event.stopPropagation();"
                                class="remove-from-vacancy"
                                translate="Remove from this vacancy"
                            ></span>
                        </td>
                        <td class="text-right" style="width: 6%; pointer-events: none" sng-class="{'ng-hide': candidate.candidateId.status == 'archived'}">
                            <div>
                                <a
                                    ng-show="vm.activeName != 'approved'"
                                    ng-click="vm.showModalAddCommentToCandidate(candidate);$event.stopPropagation();"
                                    class="vacancy-stages-icons"
                                    style="margin-right: 10px"
                                    title="{{ 'Edit comment' | translate }}"
                                >
                                    <img src="../../images/sprite/pencil.svg" alt="edit" />
                                </a>
                                <a
                                    ng-click="vm.showDeleteInterview(candidate);$event.stopPropagation();"
                                    class="vacancy-stages-icons"
                                    title="{{ 'Remove Candidate' | translate }} {{ 'from vacancy' | translate }}"
                                >
                                    <img src="../../images/sprite/close-icon.svg" alt="" />
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr class="table-space" ng-repeat-end="" ng-hide="$last"></tr>
                </tbody>
            </table>
            <h4 ng-show="vm.dataForVacancy.length === 0" class="text-center" style="margin-top: -29px; margin-left: -29px">
                {{ 'No such candidates yet' | translate }}
            </h4>
            <h4 ng-show="vm.noCandidatesInThisVacancy && !vm.dataForVacancy.length" class="text-center">
                {{ "This vacanсy doesn't contain candidates now" | translate }}
            </h4>
        </div>

        <div ng-show="vm.activeName == 'extra_status'" class="col-lg-9 stages-option">
            <div class="stages">
                <div ng-show="vm.extraStatusObj.show" class="alert alert-danger col-lg-12" style="margin: 0 auto 10px">
                    <div ng-switch="vm.extraStatusObj.messageText">
                        <span ng-switch-when="deleteStatus" translate="You can not remove this status because it is there are candidates."></span>
                        <span ng-switch-when="deleteRefuse" translate="You can not remove this refuse because it is there are candidates."></span>
                        <span ng-switch-when="ItyoSimle" translate="You can not remove this status because it is there are candidates."></span>
                        <span ng-switch-when="SimpleToIt" translate="You can not remove this status because it is there are candidates."></span>
                        <span ng-switch-when="existsInOtherVacancy">
                            {{ 'This status is used in the vacancy' | translate }}
                            <a style="padding-left: 0" target="_blank" href="#/vacancies/{{ vm.existInVacancyErrorVacancy.localId }}">
                                {{ vm.existInVacancyErrorVacancy.position }}
                            </a>
                            , {{ 'it can not be removed' | translate }}.
                        </span>
                    </div>
                    <i ng-click="vm.extraStatusObj.show = false" class="fa fa-times pull-right" aria-hidden="true"></i>
                </div>
                <div class="stages-wrapper">
                    <div class="col-lg-4 resize stage-block">
                        <h4>{{ 'Additional stages' | translate }}</h4>

                        <div class="">
                            <div
                                ng-show="
                                    status.value != 'applied_people' &&
                                    status.value != 'no_contacts' &&
                                    status.value != 'notafit' &&
                                    status.value != 'declinedoffer' &&
                                    status.value != 'no_response' &&
                                    status.value != 'accepted_counter_offer' &&
                                    status.value != 'found_another_job' &&
                                    status.value != 'is_not_looking_for_job' &&
                                    status.value != 'offer_declined' &&
                                    status.value != 'probation_failure'
                                "
                                class="stage unlock"
                                ng-repeat="status in vm.VacancyStatus | vacancyStatusInCheckFilter:'simpleVacancy' track by $index"
                            >
                                <input
                                    id="{{ status.value }}"
                                    ng-click="vm.changeInterviewStatus(status, status.value, 'simpleVacancy', $event)"
                                    class="checkbox"
                                    type="checkbox"
                                    name="{{ status.value }}"
                                    ng-checked="status.added"
                                />
                                <label for="{{ status.value }}">
                                    {{ 'interview_status_assoc.' + status.value | translate }}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 resize stage-block">
                        <h4>{{ 'Company stages' | translate }}</h4>
                        <div class="">
                            <div ng-show="status.type != 'refuse'" class="stage unlock clearfix" ng-repeat="status in vm.customStages track by $index">
                                <input
                                    id="{{ status.customInterviewStateId }}"
                                    ng-click="vm.changeCustomInterviewStatus(status, status.value, 'simpleVacancy', $event)"
                                    class="checkbox"
                                    type="checkbox"
                                    name="{{ status.value }}"
                                    ng-checked="status.added"
                                />
                                <label style="display: inline-block" for="{{ status.customInterviewStateId }}">
                                    <i ng-show="status.type == 'interview'" class="fa fa-comments-o" aria-hidden="true"></i>
                                    {{ status.value | translate }}
                                </label>
                                <span>
                                    <i
                                        ng-show="$root.me.recrutRole == 'admin'"
                                        ng-click="vm.showEditStage(status);$event.stopPropagation()"
                                        class="fa fa-pencil"
                                        aria-hidden="true"
                                        title="{{ 'Edit candidate' | translate }}"
                                    ></i>
                                    <i
                                        ng-show="$root.me.recrutRole == 'admin'"
                                        ng-click="vm.deleteCustomStageFromCompany(status);$event.stopPropagation()"
                                        class="fa fa-times"
                                        aria-hidden="true"
                                        title="{{ 'Delete' | translate }}"
                                    ></i>
                                </span>
                            </div>
                        </div>
                        <div ng-show="$root.me.recrutRole == 'admin'" ng-click="vm.showAddStage()" class="add-new-stage-wrapper">
                            <img src="images/sprite/plus-icon.svg" alt="" />
                            <span class="add-new-stage-text">{{ 'Add new custom stage' | translate }}</span>
                        </div>
                    </div>
                    <div class="col-lg-4 resize stage-block">
                        <h4>{{ 'Reasons for refusal' | translate }}</h4>

                        <div class="">
                            <div
                                ng-show="
                                    status.value == 'no_contacts' ||
                                    status.value == 'notafit' ||
                                    status.value == 'declinedoffer' ||
                                    status.value == 'no_response' ||
                                    status.value == 'accepted_counter_offer' ||
                                    status.value == 'found_another_job' ||
                                    status.value == 'is_not_looking_for_job' ||
                                    status.value == 'offer_declined' ||
                                    status.value == 'probation_failure'
                                "
                                class="stage unlock clearfix"
                                ng-repeat="status in vm.VacancyStatus| vacancyStatusInCheckFilter:'simpleVacancy' track by $index"
                            >
                                <input
                                    id="{{ status.value }}"
                                    ng-click="vm.changeInterviewStatus(status, status.value, 'simpleVacancy', $event)"
                                    class="checkbox"
                                    type="checkbox"
                                    name="{{ status.value }}"
                                    ng-checked="status.added"
                                />
                                <label for="{{ status.value }}">
                                    {{ status.value | translate }}
                                </label>
                            </div>
                            <div ng-show="status.type == 'refuse'" class="stage unlock clearfix" ng-repeat="status in vm.customStages track by $index">
                                <input
                                    id="{{ status.customInterviewStateId }}"
                                    ng-click="vm.changeCustomInterviewStatus(status, status.value, 'simpleVacancy')"
                                    class="checkbox"
                                    type="checkbox"
                                    name="{{ status.value }}"
                                    ng-checked="status.added"
                                />
                                <label style="display: inline-block" for="{{ status.customInterviewStateId }}">
                                    <i ng-show="status.type == 'interview'" class="fa fa-comments-o" aria-hidden="true"></i>
                                    {{ status.value | translate }}
                                </label>
                                <span>
                                    <i
                                        ng-show="$root.me.recrutRole == 'admin'"
                                        ng-click="vm.showEditRefuseStage(status);$event.stopPropagation()"
                                        class="fa fa-pencil"
                                        aria-hidden="true"
                                        title="{{ 'Edit candidate' | translate }}"
                                    ></i>
                                    <i
                                        ng-show="$root.me.recrutRole == 'admin'"
                                        ng-click="vm.deleteCustomStageFromCompany(status);$event.stopPropagation()"
                                        class="fa fa-times"
                                        aria-hidden="true"
                                        title="{{ 'Delete' | translate }}"
                                    ></i>
                                </span>
                            </div>
                        </div>
                        <div ng-show="$root.me.recrutRole == 'admin'" ng-click="vm.showAddRefuseStage()" class="add-new-stage-wrapper">
                            <img src="images/sprite/plus-icon.svg" alt="" />
                            <span class="add-new-stage-text">{{ 'Add the cause of refuse' | translate }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="clearfix"></div>
            <div ng-show="vm.activeName == 'extra_status'" class="active-buttons" style="display: flex; justify-content: center">
                <div ng-if="$root.me.recrutRole === 'admin'" class="btn_default btn_success" style="width: 250px; margin-right: 30px">
                    <img style="width: 20px" src="/images/sprite/default.svg" alt="" />
                    <span
                        ng-click="vm.saveStagesForAllVacancies()"
                        type="button"
                        title="{{ 'Make the current set of stages as default for new vacancies' | translate }}"
                    >
                        {{ 'Make default' | translate }}
                    </span>
                </div>
                <div
                    ng-click="vm.candidateInVacancy('extra_status')"
                    class="btn_default btn-yellow"
                    style="width: 200px; height: 32px !important; cursor: pointer"
                >
                    <img class="save" style="width: 20px" src="/images/sprite/save.svg" alt="" />
                    <span type="button">{{ 'save' | translate }}</span>
                </div>
            </div>
        </div>
        <div ng-show="vm.noAccess && $root.me.recrutRole === 'client' && !vm.visible" class="not-access pull-left col-lg-9 clearfix">
            {{ 'You have no access to candidate list on this stage. Please contact the user with an Admin role to access the candidate list.' | translate }}
        </div>
        <div ng-show="vm.visible && $root.me.recrutRole === 'client'" class="not-access col-lg-9 clearfix">
            {{ 'You have no access to candidate list on this stage. Please contact the user with an Admin role to access the candidate list.' | translate }}
        </div>
    </div>
    <ng-include src="'partials/candidate-preview.html'"></ng-include>
</div>
<ng-include src="'partials/candidate-preview.html'"></ng-include>
