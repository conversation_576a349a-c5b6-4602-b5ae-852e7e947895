<div id="showOnboarding" style="display: none; position: relative">
    <div class="navbar navbar-static-top" style="z-index: inherit">
        <div class="container-fluid first-navbar">
            <div class="navbar-header pull-left" style="margin-right: 10px">
                <a class="navbar-brand hidden-mob">
                    <div class="logo-wrapper">
                        <span class="logo new-year-logo"></span>
                    </div>
                </a>
            </div>
            <div id="navbar-main" style="z-index: inherit">
                <ul class="nav navbar-nav navigation flex-ul navbar-tabs">
                    <li class="dropdown organizer">
                        <a
                            ng-class="{
                                active:
                                    $root.activePage == 'Activity' ||
                                    $root.activePage == 'Activity Calendar' ||
                                    $root.activePage == 'News' ||
                                    ($root.onboardingActiveStage === 'stage1' && vm.$state.current.name === 'onboarding.organizer')
                            }"
                        >
                            <i class="sprite sprite-calendar">
                                <svg
                                    id="calendar"
                                    style="enable-background: new 0 0 20.1 20"
                                    version="1.1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    x="0px"
                                    y="0px"
                                    width="22px"
                                    height="21px"
                                    viewBox="0 0 20.1 20"
                                    xml:space="preserve"
                                >
                                    <path
                                        class="st0"
                                        d="M19.2,0L19.2,0H0.8C0.3,0,0,0.3,0,0.8v18.5C0,19.7,0.3,20,0.8,20h18.5c0.4,0,0.8-0.3,0.8-0.8V0.8
                                        C20,0.3,19.7,0,19.2,0L19.2,0z M18.5,18.5L18.5,18.5h-17V6h16.9v12.5H18.5z M18.5,5.1L18.5,5.1h-17V1.5H3v1.1C3,3,3.3,3.4,3.8,3.4
                                        s0.8-0.3,0.8-0.8V1.5h2.6v1.1C7.2,3,7.5,3.4,8,3.4c0.4,0,0.8-0.3,0.8-0.8V1.5h2.6v1.1c0,0.4,0.3,0.8,0.8,0.8c0.4,0,0.8-0.3,0.8-0.8
                                        V1.5h2.6v1.1c0,0.4,0.3,0.8,0.8,0.8c0.4,0,0.8-0.3,0.8-0.8V1.5h1.5v3.6H18.5z"
                                    />
                                    <path
                                        class="st1"
                                        d="M7.3,9.7c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4c0.2-0.2,0.4-0.6,0.4-0.9c0-0.4-0.1-0.7-0.4-0.9l0,0
                                        C8.9,7.6,8.6,7.5,8.2,7.5c-0.4,0-0.7,0.1-0.9,0.4l0,0C7.1,8.1,6.9,8.4,6.9,8.8C6.9,9.1,7.1,9.4,7.3,9.7L7.3,9.7L7.3,9.7z"
                                    />
                                    <path
                                        class="st1"
                                        d="M8,8.5L8,8.5L8,8.5c0-0.1,0.1-0.1,0.2-0.1c0.1,0,0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
                                        c-0.1,0-0.2,0-0.3,0C8.1,9.1,8,9.1,8,9C7.9,9,7.8,8.9,7.8,8.8C7.8,8.7,7.9,8.6,8,8.5L8,8.5z"
                                    />
                                    <path
                                        class="st1"
                                        d="M7.3,13.2L7.3,13.2c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4l0,0c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0C8.9,11.2,8.5,11,8.2,11c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9
                                        C6.9,12.6,7.1,12.9,7.3,13.2L7.3,13.2L7.3,13.2z"
                                    />
                                    <path
                                        class="st1"
                                        d="M8,12L8,12L8,12c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3l0,0
                                        c-0.1,0.1-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1l0,0c-0.1-0.1-0.1-0.2-0.1-0.3C7.8,12.1,7.9,12,8,12L8,12z"
                                    />
                                    <path
                                        class="st1"
                                        d="M14.4,9.7L14.4,9.7c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0c-0.2-0.2-0.6-0.4-0.9-0.4s-0.7,0.1-0.9,0.4l0,0C14.2,8.1,14,8.4,14,8.8C14,9.1,14.2,9.4,14.4,9.7
                                        L14.4,9.7L14.4,9.7z"
                                    />
                                    <path
                                        class="st1"
                                        d="M15.1,8.5L15.1,8.5L15.1,8.5c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
                                        s-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1s-0.2-0.2-0.2-0.3C14.9,8.7,15,8.6,15.1,8.5L15.1,8.5z"
                                    />
                                    <path
                                        class="st1"
                                        d="M10.9,9.7L10.9,9.7c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0c-0.2-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9S10.6,9.4,10.9,9.7
                                        L10.9,9.7L10.9,9.7z"
                                    />
                                    <path
                                        class="st1"
                                        d="M11.5,8.5L11.5,8.5L11.5,8.5c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
                                        s-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1s-0.1-0.2-0.1-0.3S11.4,8.6,11.5,8.5L11.5,8.5z"
                                    />
                                    <path
                                        class="st1"
                                        d="M7.3,16.7L7.3,16.7C7.5,16.9,7.9,17,8.2,17c0.4,0,0.7-0.1,0.9-0.4l0,0c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0c-0.2-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9
                                        C6.9,16.1,7.1,16.4,7.3,16.7L7.3,16.7L7.3,16.7z"
                                    />
                                    <path
                                        class="st1"
                                        d="M8,15.5L8,15.5L8,15.5c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3l0,0
                                        c-0.1,0.1-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1l0,0c-0.1-0.1-0.1-0.2-0.1-0.3C7.8,15.6,7.9,15.5,8,15.5L8,15.5z"
                                    />
                                    <path
                                        class="st1"
                                        d="M10.9,16.7L10.9,16.7c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4l0,0c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0c-0.2-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9S10.6,16.4,10.9,16.7
                                        L10.9,16.7L10.9,16.7z"
                                    />
                                    <path
                                        class="st1"
                                        d="M11.5,15.5L11.5,15.5L11.5,15.5c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3
                                        s0,0.2-0.1,0.3l0,0c-0.1,0.1-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1l0,0c-0.1-0.1-0.1-0.2-0.1-0.3C11.4,15.6,11.4,15.5,11.5,15.5L11.5,15.5
                                        z"
                                    />
                                    <path
                                        class="st1"
                                        d="M3.8,13.2L3.8,13.2c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4l0,0c0.2-0.3,0.4-0.6,0.4-1s-0.1-0.7-0.4-0.9
                                        l0,0c-0.2-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9C3.4,12.6,3.5,12.9,3.8,13.2L3.8,13.2L3.8,13.2z
                                        "
                                    />
                                    <path
                                        class="st1"
                                        d="M4.4,12L4.4,12L4.4,12c0.1-0.1,0.2-0.1,0.3-0.1c0.1,0,0.2,0,0.3,0.1l0,0c0,0,0.1,0.1,0.1,0.2
                                        c0,0.1,0,0.2-0.1,0.3l0,0c-0.1,0.1-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.3-0.1l0,0c-0.1-0.1-0.1-0.2-0.1-0.3C4.3,12.1,4.3,12,4.4,12
                                        L4.4,12z"
                                    />
                                    <path
                                        class="st1"
                                        d="M3.8,16.7L3.8,16.7C4,16.9,4.3,17,4.7,17c0.4,0,0.7-0.1,0.9-0.4l0,0C5.8,16.4,6,16.1,6,15.7
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0c-0.2-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9
                                        C3.4,16.1,3.5,16.4,3.8,16.7L3.8,16.7L3.8,16.7z"
                                    />
                                    <path
                                        class="st1"
                                        d="M4.4,15.5L4.4,15.5L4.4,15.5c0.1-0.1,0.2-0.1,0.3-0.1c0.1,0,0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3
                                        s0,0.2-0.1,0.3l0,0c-0.1,0.1-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.3-0.1l0,0c-0.1-0.1-0.1-0.2-0.1-0.3C4.3,15.6,4.3,15.5,4.4,15.5
                                        L4.4,15.5z"
                                    />
                                    <path
                                        class="st1"
                                        d="M10.9,13.2L10.9,13.2c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4l0,0c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0c-0.2-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9
                                        C10.5,12.6,10.6,12.9,10.9,13.2L10.9,13.2L10.9,13.2z"
                                    />
                                    <path
                                        class="st1"
                                        d="M11.5,12L11.5,12L11.5,12c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
                                        l0,0c-0.1,0.1-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1l0,0c-0.1-0.1-0.1-0.2-0.1-0.3C11.4,12.1,11.4,12,11.5,12L11.5,12z"
                                    />
                                    <path
                                        class="st1"
                                        d="M14.4,13.2L14.4,13.2c0.2,0.2,0.6,0.4,0.9,0.4s0.7-0.1,0.9-0.4l0,0c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0C16,11.2,15.6,11,15.3,11s-0.7,0.1-0.9,0.4l0,0C14.2,11.6,14,12,14,12.3S14.2,12.9,14.4,13.2L14.4,13.2
                                        L14.4,13.2z"
                                    />
                                    <path
                                        class="st1"
                                        d="M15.1,12L15.1,12L15.1,12c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
                                        l0,0c-0.1,0.1-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1l0,0C15,12.5,15,12.4,15,12.3C14.9,12.1,15,12,15.1,12L15.1,12z"
                                    />
                                </svg>
                            </i>
                            <span translate="Organizer"></span>
                        </a>
                    </li>
                    <li
                        ng-if="
                            ($root.onboardingActiveStage === 'stage1' && vm.$state.is('onboarding.organizer')) ||
                            ($root.onboardingActiveStage === 'stage5' && vm.$state.is('onboarding.candidate'))
                        "
                        class="vacancies"
                    >
                        <a
                            ng-click="vm.chooseCurrentModal('vacancies')"
                            class="active-onboarding-action"
                            ng-class="{
                                active:
                                    $root.activePage == 'Vacancies' ||
                                    $root.activePage == 'Vacancy add' ||
                                    $root.activePage == 'Vacancy edit' ||
                                    $root.onboardingActiveStage === 'stage6'
                            }"
                            ui-sref="onboarding.vacancies"
                        >
                            <i
                                class="sprite sprite-vacancy"
                                ng-style="$root.onboardingActiveStage === 'stage1' ? { 'margin-top': '3px' } : { 'margin-top': '0px' }"
                            >
                                <svg
                                    version="1.1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    x="0px"
                                    y="0px"
                                    height="22px"
                                    width="24px"
                                    viewBox="0 0 25.9 20"
                                    xml:space="preserve"
                                >
                                    <path
                                        class="st0"
                                        d="M24.7,0H1C0.4,0,0,0.5,0,1v18c0,0.5,0.4,1,1,1h3.2c0.5,0,0.9-0.4,0.9-1v-1.6h3.2V19c0,0.5,0.5,1,1,1h7.2
                                        c0.5,0,0.8-0.5,0.8-1.1v-1.5h3.3v1.7c0,0.5,0.6,0.9,1.1,0.9h3.2c0.5,0,1-0.5,1-1.1V1C25.7,0.5,25.2,0,24.7,0z M3.9,16.5v2.3H1.3V1.2
                                        h23.1v17.5h-2.7v-2.3h-5.5l0,0l0,0v2.4H9.5v-2.3l0,0"
                                    />
                                    <path
                                        class="st1"
                                        d="M14.7,11.8h6.1c0.3,0,0.6-0.3,0.6-0.6s-0.3-0.6-0.6-0.6h-6.1c-0.3,0-0.6,0.3-0.6,0.6
                                        C14.1,11.5,14.4,11.8,14.7,11.8L14.7,11.8z"
                                    />
                                    <path
                                        class="st1"
                                        d="M14.7,8.7L14.7,8.7h6.1c0.3,0,0.6-0.3,0.6-0.6s-0.3-0.6-0.6-0.6h-6.1c-0.3,0-0.6,0.3-0.6,0.6
                                        C14.1,8.4,14.4,8.7,14.7,8.7L14.7,8.7z"
                                    />
                                    <path
                                        class="st1"
                                        d="M14.7,5.6L14.7,5.6h6.1c0.3,0,0.6-0.3,0.6-0.6c0-0.3-0.3-0.6-0.6-0.6h-6.1c-0.3,0-0.6,0.3-0.6,0.6
                                        C14.1,5.3,14.4,5.6,14.7,5.6L14.7,5.6z"
                                    />
                                    <path
                                        class="st1"
                                        d="M4.9,5.6L4.9,5.6H11c0.3,0,0.6-0.3,0.6-0.6c0-0.3-0.3-0.6-0.6-0.6H4.9C4.6,4.4,4.3,4.7,4.3,5S4.6,5.6,4.9,5.6
                                        L4.9,5.6z"
                                    />
                                    <path
                                        class="st1"
                                        d="M4.9,11.8L4.9,11.8H11c0.3,0,0.6-0.3,0.6-0.6s-0.3-0.6-0.6-0.6H4.9c-0.3,0-0.6,0.3-0.6,0.6S4.6,11.8,4.9,11.8
                                        L4.9,11.8z"
                                    />
                                    <path
                                        class="st1"
                                        d="M4.3,5.1L4.3,5.1v6.1c0,0.3,0.3,0.6,0.6,0.6c0.3,0,0.6-0.3,0.6-0.6V5c0-0.3-0.3-0.6-0.6-0.6
                                        C4.6,4.5,4.3,4.8,4.3,5.1L4.3,5.1z"
                                    />
                                    <path
                                        class="st1"
                                        d="M10.4,5.1L10.4,5.1v6.1c0,0.3,0.3,0.6,0.6,0.6s0.6-0.3,0.6-0.6V5c0-0.3-0.3-0.6-0.6-0.6
                                        C10.7,4.5,10.4,4.8,10.4,5.1L10.4,5.1z"
                                    />
                                </svg>
                            </i>
                            <span
                                ng-style="$root.onboardingActiveStage === 'stage1' ? { 'margin-top': '3px' } : { 'margin-top': '0px' }"
                                translate="Vacancies"
                            ></span>
                        </a>
                    </li>
                    <li
                        class="vacancies"
                        ng-hide="
                            ($root.onboardingActiveStage === 'stage1' && vm.$state.is('onboarding.organizer')) ||
                            ($root.onboardingActiveStage === 'stage5' && vm.$state.is('onboarding.candidate'))
                        "
                    >
                        <a
                            ng-click="vm.chooseCurrentModal('vacancies')"
                            ng-class="{
                                active:
                                    $root.activePage == 'Vacancies' ||
                                    $root.activePage == 'Vacancy add' ||
                                    $root.activePage == 'Vacancy edit' ||
                                    $root.onboardingActiveStage === 'stage1' ||
                                    $root.onboardingActiveStage === 'stage2' ||
                                    ($root.onboardingActiveStage === 'stage3' && !vm.$state.is('onboarding.candidates')) ||
                                    $root.onboardingActiveStage === 'stage5' ||
                                    $root.onboardingActiveStage === 'stage6' ||
                                    $root.onboardingActiveStage === 'stage7'
                            }"
                        >
                            <i class="sprite sprite-vacancy">
                                <svg
                                    version="1.1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    x="0px"
                                    y="0px"
                                    height="22px"
                                    width="24px"
                                    viewBox="0 0 25.9 20"
                                    xml:space="preserve"
                                >
                                    <path
                                        class="st0"
                                        d="M24.7,0H1C0.4,0,0,0.5,0,1v18c0,0.5,0.4,1,1,1h3.2c0.5,0,0.9-0.4,0.9-1v-1.6h3.2V19c0,0.5,0.5,1,1,1h7.2
                                        c0.5,0,0.8-0.5,0.8-1.1v-1.5h3.3v1.7c0,0.5,0.6,0.9,1.1,0.9h3.2c0.5,0,1-0.5,1-1.1V1C25.7,0.5,25.2,0,24.7,0z M3.9,16.5v2.3H1.3V1.2
                                        h23.1v17.5h-2.7v-2.3h-5.5l0,0l0,0v2.4H9.5v-2.3l0,0"
                                    />
                                    <path
                                        class="st1"
                                        d="M14.7,11.8h6.1c0.3,0,0.6-0.3,0.6-0.6s-0.3-0.6-0.6-0.6h-6.1c-0.3,0-0.6,0.3-0.6,0.6
                                        C14.1,11.5,14.4,11.8,14.7,11.8L14.7,11.8z"
                                    />
                                    <path
                                        class="st1"
                                        d="M14.7,8.7L14.7,8.7h6.1c0.3,0,0.6-0.3,0.6-0.6s-0.3-0.6-0.6-0.6h-6.1c-0.3,0-0.6,0.3-0.6,0.6
                                        C14.1,8.4,14.4,8.7,14.7,8.7L14.7,8.7z"
                                    />
                                    <path
                                        class="st1"
                                        d="M14.7,5.6L14.7,5.6h6.1c0.3,0,0.6-0.3,0.6-0.6c0-0.3-0.3-0.6-0.6-0.6h-6.1c-0.3,0-0.6,0.3-0.6,0.6
                                        C14.1,5.3,14.4,5.6,14.7,5.6L14.7,5.6z"
                                    />
                                    <path
                                        class="st1"
                                        d="M4.9,5.6L4.9,5.6H11c0.3,0,0.6-0.3,0.6-0.6c0-0.3-0.3-0.6-0.6-0.6H4.9C4.6,4.4,4.3,4.7,4.3,5S4.6,5.6,4.9,5.6
                                        L4.9,5.6z"
                                    />
                                    <path
                                        class="st1"
                                        d="M4.9,11.8L4.9,11.8H11c0.3,0,0.6-0.3,0.6-0.6s-0.3-0.6-0.6-0.6H4.9c-0.3,0-0.6,0.3-0.6,0.6S4.6,11.8,4.9,11.8
                                        L4.9,11.8z"
                                    />
                                    <path
                                        class="st1"
                                        d="M4.3,5.1L4.3,5.1v6.1c0,0.3,0.3,0.6,0.6,0.6c0.3,0,0.6-0.3,0.6-0.6V5c0-0.3-0.3-0.6-0.6-0.6
                                        C4.6,4.5,4.3,4.8,4.3,5.1L4.3,5.1z"
                                    />
                                    <path
                                        class="st1"
                                        d="M10.4,5.1L10.4,5.1v6.1c0,0.3,0.3,0.6,0.6,0.6s0.6-0.3,0.6-0.6V5c0-0.3-0.3-0.6-0.6-0.6
                                        C10.7,4.5,10.4,4.8,10.4,5.1L10.4,5.1z"
                                    />
                                </svg>
                            </i>
                            <span translate="Vacancies"></span>
                        </a>
                    </li>
                    <li ng-if="$root.onboardingActiveStage === 'stage3' && vm.$state.is('onboarding.vacancy')" class="candidates">
                        <a
                            ng-click="vm.chooseCurrentModal('candidates')"
                            class="active-onboarding-action"
                            ng-class="{
                                active:
                                    $root.activePage == 'Candidates' ||
                                    $root.activePage == 'Candidate add' ||
                                    $root.activePage == 'Candidates add from email' ||
                                    $root.activePage == 'Zip' ||
                                    $root.activePage == 'Excel History' ||
                                    $root.activePage == 'Candidate edit' ||
                                    $root.activePage == 'Tests and forms' ||
                                    $root.activePage == 'Send test candidate to email' ||
                                    $root.activePage == 'Test results' ||
                                    $root.activePage == 'Test page' ||
                                    $root.activePage == 'Candidate' ||
                                    $root.onboardingActiveStage === 'stage4'
                            }"
                            ui-sref="onboarding.candidates"
                        >
                            <i
                                class="sprite sprite-candidates"
                                ng-style="$root.onboardingActiveStage === 'stage3' ? { 'margin-top': '3px' } : { 'margin-top': '0px' }"
                            >
                                <svg
                                    style="enable-background: new 0 0 24.4 20.079"
                                    version="1.1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    x="0px"
                                    y="0px"
                                    width="24px"
                                    height="21px"
                                    viewBox="0 0 24.4 20.079"
                                    xml:space="preserve"
                                >
                                    <path
                                        class="st0"
                                        d="M24.1,14.2c-0.2-0.6-0.6-1.1-1-1.5l-1-1c0.7-0.8,1.2-1.8,1.2-2.9c0-1.2-0.5-2.3-1.3-3.1s-1.9-1.3-3.1-1.3
                                            c-0.4,0-0.9,0.1-1.3,0.2C17.2,2,14.9,0,12.2,0s-5,2-5.4,4.6C6.4,4.5,6,4.4,5.6,4.4c-1.2,0-2.3,0.5-3.1,1.3c-0.8,0.7-1.3,1.8-1.3,3
                                            c0,1.1,0.4,2.2,1.2,2.9l-1,1c-0.5,0.5-0.8,1-1,1.5l0,0C0.1,14.8,0,15.4,0,16v1.3c0,0.3,0.1,0.7,0.3,1s0.5,0.5,0.9,0.6h0.1h1v0.4
                                            c0,0.4,0.4,0.7,0.8,0.7h18.2c0.4,0,0.8-0.3,0.8-0.7v-0.4h1h0.1c0.4-0.1,0.7-0.3,0.9-0.6c0.2-0.3,0.3-0.6,0.3-1V16
                                            C24.4,15.4,24.3,14.8,24.1,14.2z M22.9,17.3h-1.4v-1.4c0-0.3-0.2-0.5-0.5-0.5s-0.5,0.2-0.5,0.5v2.5h-2.8c0.2-0.1,0.2-0.4,0.4-0.5
                                            c-0.8,0.4-1.3,0.5-1.8-0.2c-0.3,0-0.6-0.1-0.9-0.2c1.1,0.5-2.4,2.2-3,2.2c-1.9,0-2.9-1.5-4.6-1.7c-0.5,0.3-0.7,0.4-1.3,0.2
                                            c0.1,0,0.1,0.1,0.2,0.2H3.9v-2.5c0-0.3-0.2-0.5-0.5-0.5s-0.5,0.2-0.5,0.5v1.4H1.6V16c0-0.4,0.1-0.8,0.2-1.2l0,0l0,0
                                            c0.2-0.4,0.4-0.7,0.7-1l1.2-1.2C4.2,12.9,4.8,13,5.5,13v0.1c0.4-0.3,0.8-0.4,1.3-0.4C6.9,12.5,7,12.2,7.1,12
                                            c0.2-0.3,0.5-0.5,0.8-0.8c-0.4,0.1-0.8,0.1-1.3-0.1c0,0.1-0.1,0.1-0.1,0.1c-0.1,0-0.2,0.1-0.3,0.1l0,0c-0.2,0-0.3,0-0.5,0
                                            c-0.8,0-1.4-0.3-1.9-0.8l0,0C3.3,10,3,9.3,3,8.6c0-0.8,0.3-1.4,0.8-1.9C4.1,6.3,4.8,6,5.6,6c0.3,0,0.6,0,0.8,0.1
                                            c0.2,0,0.3,0.1,0.5,0.2l0,0c0.4,0,0.8,0.1,1.2,0.3C7.9,5.7,7.9,4.8,7.9,3.9c0-1.2,0.9-1.8,1.8-2c0.6-0.7,1.4-1,2.2-0.9
                                            c0.4-0.1,0.9-0.1,1.4,0.1c2,0.9,3.3,2,3.4,4.4c0,0.2,0,0.4,0,0.6c0.3,0.1,0.6,0.2,0.8,0.4l0,0l0,0l0,0l0,0V6.4
                                            c0.1-0.1,0.3-0.1,0.5-0.2C18.3,6,18.6,6,18.9,6c0.8,0,1.4,0.3,1.9,0.8s0.8,1.2,0.8,1.9c0,0.8-0.3,1.4-0.8,1.9l0,0
                                            c-0.5,0.5-1.2,0.8-1.9,0.8c-0.2,0-0.4,0-0.5,0l0,0c-0.1,0-0.2,0-0.3-0.1c0-0.1-0.1-0.1-0.1-0.2c-0.4,0.2-0.8,0.3-1.2,0.3
                                            c0.3,0.4,0.5,0.9,0.8,1.4c0.6-0.1,1.1,0,1.6,0.4c0-0.1,0-0.1,0-0.2c0.6,0,1.2-0.2,1.8-0.4l1.2,1.2c0.3,0.3,0.5,0.7,0.7,1l0,0l0,0
                                            c0.2,0.4,0.2,0.8,0.2,1.2v1.3H22.9z"
                                    />
                                    <path
                                        class="st1"
                                        d="M12.2,0c3,0,5.4,2.4,5.4,5.4c0,1.5-0.6,2.9-1.6,3.8l0,0l0,0l0,0l0,0c1.1,1,2.3,2,2.8,3.5l0,0
                                            c0.3,0.7,0.4,1.5,0.4,2.3v1.7c0,0.4-0.1,0.8-0.4,1.1c-0.2,0.3-0.6,0.6-1,0.6h-0.1h-1.6v0.7c0,0.4-0.4,0.9-0.8,0.9l0,0H9
                                            c-0.4,0-0.8-0.5-0.8-0.9l0,0v-0.7H6.6c-0.1,0-0.1,0-0.2,0c-0.4-0.1-0.8-0.3-1-0.6C5.2,17.5,5,17.1,5,16.7V15c0-0.8,0.1-1.6,0.4-2.3
                                            c0.6-1.4,1.8-2.4,2.8-3.5l0,0l0,0c-1-1-1.6-2.4-1.6-3.8C6.8,2.4,9.2,0,12.2,0L12.2,0z M8.8,16.9L8.8,16.9v-2.1
                                            c0-0.3,0.2-0.5,0.5-0.5s0.5,0.2,0.5,0.5v2.8v0.1v0.7h4.9v-0.7l0,0v-0.1v-2.8c0-0.3,0.2-0.5,0.5-0.5s0.5,0.2,0.5,0.5v2.1h2
                                            c0,0,0-0.1,0-0.2V15c0-0.6-0.1-1.2-0.3-1.7l0,0l0,0c-0.5-1.2-1.8-2.2-2.7-3.1c-0.8,0.4-1.6,0.6-2.5,0.6s-1.8-0.2-2.5-0.6
                                            c-0.9,0.9-2.2,1.9-2.7,3.1c-0.2,0.5-0.3,1.1-0.3,1.7v1.7c0,0.1,0,0.1,0,0.2L8.8,16.9L8.8,16.9z M12.2,1.6L12.2,1.6
                                            c-2.1,0-3.8,1.7-3.8,3.9c0,2.1,1.7,3.8,3.8,3.8S16,7.6,16,5.5C16.1,3.3,14.3,1.6,12.2,1.6L12.2,1.6z"
                                    />
                                </svg>
                            </i>
                            <span
                                ng-style="$root.onboardingActiveStage === 'stage3' ? { 'margin-top': '3px' } : { 'margin-top': '0px' }"
                                translate="candidates"
                            ></span>
                        </a>
                    </li>
                    <li class="candidates" ng-hide="$root.onboardingActiveStage === 'stage3' && vm.$state.is('onboarding.vacancy')">
                        <a
                            ng-class="{
                                active:
                                    vm.$state.current.name === 'onboarding.candidates' ||
                                    vm.$state.current.name === 'onboarding.candidate' ||
                                    $root.activePage == 'Candidates' ||
                                    $root.activePage == 'Candidate add' ||
                                    $root.activePage == 'Candidates add from email' ||
                                    $root.activePage == 'Zip' ||
                                    $root.activePage == 'Excel History' ||
                                    $root.activePage == 'Candidate edit' ||
                                    $root.activePage == 'Tests and forms' ||
                                    $root.activePage == 'Send test candidate to email' ||
                                    $root.activePage == 'Test results' ||
                                    $root.activePage == 'Test page' ||
                                    $root.activePage == 'Candidate' ||
                                    $root.onboardingActiveStage === 'stage4'
                            }"
                        >
                            <i class="sprite sprite-candidates">
                                <svg
                                    style="enable-background: new 0 0 24.4 20.079"
                                    version="1.1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    x="0px"
                                    y="0px"
                                    width="24px"
                                    height="21px"
                                    viewBox="0 0 24.4 20.079"
                                    xml:space="preserve"
                                >
                                    <path
                                        class="st0"
                                        d="M24.1,14.2c-0.2-0.6-0.6-1.1-1-1.5l-1-1c0.7-0.8,1.2-1.8,1.2-2.9c0-1.2-0.5-2.3-1.3-3.1s-1.9-1.3-3.1-1.3
                                            c-0.4,0-0.9,0.1-1.3,0.2C17.2,2,14.9,0,12.2,0s-5,2-5.4,4.6C6.4,4.5,6,4.4,5.6,4.4c-1.2,0-2.3,0.5-3.1,1.3c-0.8,0.7-1.3,1.8-1.3,3
                                            c0,1.1,0.4,2.2,1.2,2.9l-1,1c-0.5,0.5-0.8,1-1,1.5l0,0C0.1,14.8,0,15.4,0,16v1.3c0,0.3,0.1,0.7,0.3,1s0.5,0.5,0.9,0.6h0.1h1v0.4
                                            c0,0.4,0.4,0.7,0.8,0.7h18.2c0.4,0,0.8-0.3,0.8-0.7v-0.4h1h0.1c0.4-0.1,0.7-0.3,0.9-0.6c0.2-0.3,0.3-0.6,0.3-1V16
                                            C24.4,15.4,24.3,14.8,24.1,14.2z M22.9,17.3h-1.4v-1.4c0-0.3-0.2-0.5-0.5-0.5s-0.5,0.2-0.5,0.5v2.5h-2.8c0.2-0.1,0.2-0.4,0.4-0.5
                                            c-0.8,0.4-1.3,0.5-1.8-0.2c-0.3,0-0.6-0.1-0.9-0.2c1.1,0.5-2.4,2.2-3,2.2c-1.9,0-2.9-1.5-4.6-1.7c-0.5,0.3-0.7,0.4-1.3,0.2
                                            c0.1,0,0.1,0.1,0.2,0.2H3.9v-2.5c0-0.3-0.2-0.5-0.5-0.5s-0.5,0.2-0.5,0.5v1.4H1.6V16c0-0.4,0.1-0.8,0.2-1.2l0,0l0,0
                                            c0.2-0.4,0.4-0.7,0.7-1l1.2-1.2C4.2,12.9,4.8,13,5.5,13v0.1c0.4-0.3,0.8-0.4,1.3-0.4C6.9,12.5,7,12.2,7.1,12
                                            c0.2-0.3,0.5-0.5,0.8-0.8c-0.4,0.1-0.8,0.1-1.3-0.1c0,0.1-0.1,0.1-0.1,0.1c-0.1,0-0.2,0.1-0.3,0.1l0,0c-0.2,0-0.3,0-0.5,0
                                            c-0.8,0-1.4-0.3-1.9-0.8l0,0C3.3,10,3,9.3,3,8.6c0-0.8,0.3-1.4,0.8-1.9C4.1,6.3,4.8,6,5.6,6c0.3,0,0.6,0,0.8,0.1
                                            c0.2,0,0.3,0.1,0.5,0.2l0,0c0.4,0,0.8,0.1,1.2,0.3C7.9,5.7,7.9,4.8,7.9,3.9c0-1.2,0.9-1.8,1.8-2c0.6-0.7,1.4-1,2.2-0.9
                                            c0.4-0.1,0.9-0.1,1.4,0.1c2,0.9,3.3,2,3.4,4.4c0,0.2,0,0.4,0,0.6c0.3,0.1,0.6,0.2,0.8,0.4l0,0l0,0l0,0l0,0V6.4
                                            c0.1-0.1,0.3-0.1,0.5-0.2C18.3,6,18.6,6,18.9,6c0.8,0,1.4,0.3,1.9,0.8s0.8,1.2,0.8,1.9c0,0.8-0.3,1.4-0.8,1.9l0,0
                                            c-0.5,0.5-1.2,0.8-1.9,0.8c-0.2,0-0.4,0-0.5,0l0,0c-0.1,0-0.2,0-0.3-0.1c0-0.1-0.1-0.1-0.1-0.2c-0.4,0.2-0.8,0.3-1.2,0.3
                                            c0.3,0.4,0.5,0.9,0.8,1.4c0.6-0.1,1.1,0,1.6,0.4c0-0.1,0-0.1,0-0.2c0.6,0,1.2-0.2,1.8-0.4l1.2,1.2c0.3,0.3,0.5,0.7,0.7,1l0,0l0,0
                                            c0.2,0.4,0.2,0.8,0.2,1.2v1.3H22.9z"
                                    />
                                    <path
                                        class="st1"
                                        d="M12.2,0c3,0,5.4,2.4,5.4,5.4c0,1.5-0.6,2.9-1.6,3.8l0,0l0,0l0,0l0,0c1.1,1,2.3,2,2.8,3.5l0,0
                                            c0.3,0.7,0.4,1.5,0.4,2.3v1.7c0,0.4-0.1,0.8-0.4,1.1c-0.2,0.3-0.6,0.6-1,0.6h-0.1h-1.6v0.7c0,0.4-0.4,0.9-0.8,0.9l0,0H9
                                            c-0.4,0-0.8-0.5-0.8-0.9l0,0v-0.7H6.6c-0.1,0-0.1,0-0.2,0c-0.4-0.1-0.8-0.3-1-0.6C5.2,17.5,5,17.1,5,16.7V15c0-0.8,0.1-1.6,0.4-2.3
                                            c0.6-1.4,1.8-2.4,2.8-3.5l0,0l0,0c-1-1-1.6-2.4-1.6-3.8C6.8,2.4,9.2,0,12.2,0L12.2,0z M8.8,16.9L8.8,16.9v-2.1
                                            c0-0.3,0.2-0.5,0.5-0.5s0.5,0.2,0.5,0.5v2.8v0.1v0.7h4.9v-0.7l0,0v-0.1v-2.8c0-0.3,0.2-0.5,0.5-0.5s0.5,0.2,0.5,0.5v2.1h2
                                            c0,0,0-0.1,0-0.2V15c0-0.6-0.1-1.2-0.3-1.7l0,0l0,0c-0.5-1.2-1.8-2.2-2.7-3.1c-0.8,0.4-1.6,0.6-2.5,0.6s-1.8-0.2-2.5-0.6
                                            c-0.9,0.9-2.2,1.9-2.7,3.1c-0.2,0.5-0.3,1.1-0.3,1.7v1.7c0,0.1,0,0.1,0,0.2L8.8,16.9L8.8,16.9z M12.2,1.6L12.2,1.6
                                            c-2.1,0-3.8,1.7-3.8,3.9c0,2.1,1.7,3.8,3.8,3.8S16,7.6,16,5.5C16.1,3.3,14.3,1.6,12.2,1.6L12.2,1.6z"
                                    />
                                </svg>
                            </i>
                            <span translate="candidates"></span>
                        </a>
                    </li>
                    <li ng-if="$root.me != undefined && $root.me.recrutRole != 'researcher'" class="dropdown reports">
                        <a
                            ng-class="{
                                active:
                                    $root.activePage == 'Pipeline' ||
                                    $root.activePage == 'Statistics client' ||
                                    $root.activePage == 'Report all' ||
                                    $root.activePage == 'Vacancy report' ||
                                    $root.activePage == 'Reports' ||
                                    $root.activePage == 'Reports constructor' ||
                                    $root.activePage == 'Custom Reports' ||
                                    $root.activePage == 'Edit Reports'
                            }"
                            style="min-width: unset"
                        >
                            <i class="sprite sprite-reports">
                                <svg
                                    style="enable-background: new 0 0 20 20"
                                    version="1.1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    x="0px"
                                    y="0px"
                                    width="21px"
                                    height="21px"
                                    viewBox="0 0 20 20"
                                    xml:space="preserve"
                                >
                                    <path
                                        class="st0"
                                        d="M19.5,19H1V0.5C1,0.2,0.7,0,0.5,0C0.2,0,0,0.2,0,0.5v19l0,0C0,19.8,0.2,20,0.5,20l0,0h19c0.3,0,0.5-0.2,0.5-0.5
                                        C20,19.3,19.8,19,19.5,19L19.5,19z"
                                    />
                                    <path
                                        class="st0"
                                        d="M9.7,10.8H7.5C7.2,10.8,7,11,7,11.3v6c0,0.3,0.2,0.5,0.5,0.5h2.2c0.3,0,0.5-0.2,0.5-0.5v-6
                                        C10.2,11,10,10.8,9.7,10.8z M9.3,16.8H8v-5h1.3V16.8z"
                                    />
                                    <path
                                        class="st0"
                                        d="M19.1,13.8h-2.2c-0.3,0-0.5,0.2-0.5,0.5v3c0,0.3,0.2,0.5,0.5,0.5h2.2c0.3,0,0.5-0.2,0.5-0.5v-3
                                        C19.5,14,19.3,13.8,19.1,13.8z M18.6,16.8h-1.3v-2h1.3V16.8z"
                                    />
                                    <path
                                        class="st1"
                                        d="M5,2.3H2.7c-0.3,0-0.5,0.2-0.5,0.5v14.5c0,0.3,0.2,0.5,0.5,0.5H5c0.3,0,0.5-0.2,0.5-0.5V2.8
                                        C5.5,2.5,5.2,2.3,5,2.3z M4.5,16.8H3.2V3.2h1.3V16.8z"
                                    />
                                    <path
                                        class="st1"
                                        d="M14.5,4.9h-2.2c-0.3,0-0.5,0.2-0.5,0.5v11.8c0,0.3,0.2,0.5,0.5,0.5h2.2c0.3,0,0.5-0.2,0.5-0.5V5.4
                                        C15,5.1,14.8,4.9,14.5,4.9z M14,16.8h-1.3V5.9H14V16.8z"
                                    />
                                </svg>
                            </i>
                            <span translate="Reports"></span>
                        </a>
                    </li>
                    <li class="achievements">
                        <a
                            ng-class="{
                                active: $root.activePage == 'Results' || $root.activePage == 'Collection' || $root.activePage == 'Awards'
                            }"
                        >
                            <i class="sprite sprite-achievements" style="position: relative; right: 5px">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="22" viewBox="0 0 29 35">
                                    <metadata>
                                        <?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
                                        <x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c138 79.159824, 2016/09/14-01:09:01">
                                            <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
                                                <rdf:Description rdf:about="" />
                                            </rdf:RDF>
                                        </x:xmpmeta>
                                        <?xpacket end="w"?>
                                    </metadata>
                                    <image
                                        id="Vector_Smart_Object_copy_42"
                                        data-name="Vector Smart Object copy 42"
                                        width="40"
                                        height="40"
                                        xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB0AAAAjCAMAAABfPfHgAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAACLlBMVEUAAADxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmzlvxmHXpEHao0BvxmHqnTvxmznpnjtvxmFvxmHxmznxmznxmzlvxmFvxmGhtVHxmznxmzmmtFBvxmFvxmFvxmHhoD7snTvxmznsnTrioD5vxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmGhtVHxmzmitVFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmHxmznQpkPRpkPGqUbIqEZvxmGYuVTwmzmdt1MAAACOU9qJAAAAsHRSTlMAEScSU/3+VTtiNQEc7efyIjQ8T+Lg2tHfV6rjLCrx5K++WBZabx0bblsYUsBQyIT1Pjf3ggYDd91EAsKtZM5wCWZsz2dt2wTTchm7e/Rl+BWZa+pD7/qAftgF1mMHCMtqqMfGrIE2hQzx8o5dWVU0/S4o6wTPy9TVzXfzHiH55voiI/Zue3xvvNkvOTEm+Ih8ibteCQHAxQLsWGP5KgU2KR0QjMTvgdcHPD2YmSLmDxaVMy4AAAABYktHRACIBR1IAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAB3RJTUUH5QQPFB0SMESA/wAAAiRJREFUOMt1kmdDE0EQhicgGAiIIFgSgiWiqBQ1AnKKiogo9q6I2BVFBTVYCDYwKhCw0DuEagFfy4g/z83mEu4i7Ie7Z+aZvZ3bXSLtMISE0pxjXhjC588mjBGRpqhoLEDMQlNsRJxeLgIQn4DFhiVYGi94mc6aYQkDohPJmgSEL8cKnV2JVbbVyWsErU1Zt34DUnU2FWmaKA3pmigkYyM2aeLNsG9JVDnTLNpIz5KcvTVHPJVtIrM9U2ZygR07dwnI85blyAm784E90hZgb6H3nQf4LVHhPuyXUBSDDGkPZJsD9iAORfnoMI4c9dExvw2147jalnICJ4OsEacU8s89bZtzrlj3DAVZOutftwDniv+zWefVnkuA/JQLOluafBG4JFPFl8VvXlE0VrkqMtdK1Q9ar6v7rNobsN+0zmx72a2gM7pdpgnLf+CO7a483yJxvvfws3xGVlT+kncj6T49eCjuhgW/Kyv80lHFj0QblgQ8Njzx3aunXOVQbTU7a4y5kcqz53iBl69MsSVxNU6u9snaOn4twfVnGtN/30h+y3W1Et5xfUOjm6ipmd9/+MjNTUTuxoZ6/uSVLewdzta2du7opM4Obm9rdcpci2ipSxJ393Bvnyju6+Webl+qy0H9A4NDHhrmkVEekwuN8egID5NnaHCgX23bLUrHJyROjAt26247idRnFb+IQr0kF38N8Dd2BdnJKU+APVOTQZa+z8b/AJW13oc8i/aDAAAAAElFTkSuQmCC"
                                    />
                                </svg>
                            </i>
                            <span translate="Achievements"></span>
                        </a>
                    </li>
                    <li class="dropdown hamburger-item" style="margin-left: 0px; cursor: default">
                        <a class="hamburger">
                            <svg width="4" height="16" viewBox="0 0 4 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    fill-rule="evenodd"
                                    clip-rule="evenodd"
                                    d="M2 4C3.1 4 4 3.1 4 2C4 0.9 3.1 0 2 0C0.9 0 0 0.9 0 2C0 3.1 0.9 4 2 4ZM2 6C0.9 6 0 6.9 0 8C0 9.1 0.9 10 2 10C3.1 10 4 9.1 4 8C4 6.9 3.1 6 2 6ZM0 14C0 12.9 0.9 12 2 12C3.1 12 4 12.9 4 14C4 15.1 3.1 16 2 16C0.9 16 0 15.1 0 14Z"
                                    fill="white"
                                />
                            </svg>
                        </a>
                    </li>
                </ul>
                <span class="active-page">{{ $root.notFormatedTitle }}</span>
                <div class="right-block">
                    <div class="onboarding-lang">
                        <span
                            ng-click="$event.stopPropagation();vm.changeOnboardingLanguage('en')"
                            class="onboarding_lang_option"
                            ng-class="{ onboarding_currentLang: $root.currentLang === 'en' }"
                            style="font-size: 16px"
                        >
                            EN
                        </span>
                        <span
                            ng-click="$event.stopPropagation();vm.changeOnboardingLanguage('ua')"
                            class="onboarding_lang_option"
                            ng-class="{ onboarding_currentLang: $root.currentLang === 'ua' }"
                            style="font-size: 16px"
                        >
                            UA
                        </span>
                        <span
                            ng-click="$event.stopPropagation();vm.changeOnboardingLanguage('pl')"
                            class="onboarding_lang_option"
                            ng-class="{ onboarding_currentLang: $root.currentLang === 'pl' }"
                            style="font-size: 16px"
                        >
                            PL
                        </span>
                        <span
                            ng-if="vm.showLanguageByOrgParam['ru']"
                            ng-click="$event.stopPropagation();vm.changeOnboardingLanguage('ru')"
                            class="onboarding_lang_option"
                            ng-class="{ onboarding_currentLang: $root.currentLang === 'ru' }"
                            style="font-size: 16px"
                        >
                            RU
                        </span>
                    </div>
                    <div ng-if="digit > 0 && digit <= 14" class="trial">
                        <span class="trial-info" ng-mouseover="HoverBlockTrialShow()" ng-mouseleave="HoverBlockTrialHidden()">
                            <div ng-show="hoverBlockTrial" class="hoverExtension">
                                <p translate="All features are unlimited within your trial. You could invite unlimited number of users to test the system."></p>
                                <p
                                    class="no-margin"
                                    translate="After the trial period ends, you need to pay for each user of your account. Otherwise, it will be blocked."
                                ></p>
                            </div>
                            <i>
                                <svg
                                    style="enable-background: new 0 0 7.5 20"
                                    version="1.1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    x="0px"
                                    y="0px"
                                    width="18px"
                                    height="18px"
                                    viewBox="0 0 7.5 20"
                                    xml:space="preserve"
                                >
                                    <path class="st0" d="M6.1,6H1.7l0,0H0.1v2.5h1.6v9H0V20h1.7l0,0H6l0,0h1.4v-2.4H6.1V6z" />
                                    <path class="st0" d="M3.9,4.3c1.5,0,2.3-1,2.3-2.2S5.3,0,3.9,0S1.6,0.9,1.6,2.2C1.6,3.4,2.5,4.3,3.9,4.3L3.9,4.3z" />
                                </svg>
                            </i>
                        </span>
                        <span class="hidden-xs">
                            <span ng-show="digit != 1" style="margin-right: 0">{{ 'Only' | translate }}</span>
                            <span ng-show="digit == 1" style="margin-right: 0">{{ 'Only_1' | translate }}</span>
                        </span>
                        <span class="hidden-sm hidden-md hidden-lg">
                            <span ng-show="digit != 1" style="margin-right: 0">{{ 'Only mobile' | translate }}</span>
                            <span ng-show="digit == 1" style="margin-right: 0">{{ 'Only_1 mobile' | translate }}</span>
                        </span>
                        <span class="digit" ng-bind="digit"></span>
                        <span>
                            {{
                                digit >= 5 && digit <= 14
                                    ? 'days_1Trial'
                                    : digit < 5 && digit > 1
                                    ? 'daysTrial'
                                    : digit === 0
                                    ? 'dayTrial'
                                    : ('dayTrial' | translate)
                            }}
                        </span>
                    </div>
                    <ul class="nav information navbar-nav flex-ul">
                        <li
                            ng-if="
                                ($root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter') &&
                                !(digit > 0 && digit <= 14) &&
                                !(getMeObj['orgParams']['tarif'] === 'free' || tarif == 'free') &&
                                !$root.blockUser &&
                                paidFor >= 0
                            "
                            class="payMoney"
                        >
                            <a
                                ng-class="{
                                    'low-low-balance': paidFor !== undefined && paidFor <= 4 && getMeObj['orgParams']['tarif'] !== 'corporate',
                                    'low-balance': paidFor !== undefined && paidFor <= 7 && getMeObj['orgParams']['tarif'] !== 'corporate',
                                    'disable-button-for-recruter': $root.me.recrutRole === 'recruter',
                                    'payment-panel': $root.me.recrutRole === 'admin'
                                }"
                            >
                                <span class="coins">
                                    <span class="usd-icon">
                                        <svg
                                            style="enable-background: new 0 0 511.613 511.613"
                                            version="1.1"
                                            xmlns="http://www.w3.org/2000/svg"
                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                            x="0px"
                                            y="0px"
                                            width="20px"
                                            height="19px"
                                            viewBox="0 0 511.613 511.613"
                                            xml:space="preserve"
                                        >
                                            <g>
                                                <path
                                                    d="M385.261,311.475c-2.471-8.367-5.469-15.649-8.99-21.833c-3.519-6.19-8.559-12.228-15.13-18.134
                                    c-6.563-5.903-12.467-10.657-17.702-14.271c-5.232-3.617-12.419-7.661-21.557-12.137c-9.13-4.475-16.364-7.805-21.689-9.995
                                    c-5.332-2.187-13.045-5.185-23.134-8.992c-8.945-3.424-15.605-6.042-19.987-7.849c-4.377-1.809-10.133-4.377-17.271-7.71
                                    c-7.135-3.328-12.465-6.28-15.987-8.848c-3.521-2.568-7.279-5.708-11.277-9.419s-6.805-7.661-8.424-11.848
                                    c-1.615-4.188-2.425-8.757-2.425-13.706c0-12.94,5.708-23.507,17.128-31.689c11.421-8.182,26.174-12.275,44.257-12.275
                                    c7.99,0,16.136,1.093,24.41,3.284c8.274,2.191,15.365,4.659,21.266,7.421c5.906,2.762,11.471,5.808,16.707,9.137
                                    c5.235,3.332,8.945,5.852,11.136,7.565c2.189,1.714,3.576,2.855,4.141,3.427c2.478,1.903,5.041,2.568,7.706,1.999
                                    c2.854-0.19,5.045-1.715,6.571-4.567l23.13-41.684c2.283-3.805,1.811-7.422-1.427-10.85c-1.144-1.142-2.566-2.473-4.291-3.997
                                    c-1.708-1.524-5.421-4.283-11.136-8.282c-5.709-3.996-11.752-7.565-18.124-10.706c-6.379-3.138-14.661-6.328-24.845-9.562
                                    c-10.178-3.239-20.697-5.426-31.549-6.567V9.136c0-2.663-0.855-4.853-2.563-6.567C282.493,0.859,280.303,0,277.634,0h-38.546
                                    c-2.474,0-4.615,0.903-6.423,2.712s-2.712,3.949-2.712,6.424v51.391c-29.884,5.708-54.152,18.461-72.805,38.256
                                    c-18.651,19.796-27.98,42.823-27.98,69.092c0,7.803,0.812,15.226,2.43,22.265c1.616,7.045,3.616,13.374,5.996,18.988
                                    c2.378,5.618,5.758,11.136,10.135,16.562c4.377,5.424,8.518,10.088,12.419,13.988c3.903,3.899,8.995,7.945,15.274,12.131
                                    c6.283,4.19,11.66,7.571,16.134,10.139c4.475,2.56,10.422,5.52,17.843,8.843c7.423,3.333,13.278,5.852,17.561,7.569
                                    c4.283,1.711,10.135,4.093,17.561,7.132c10.277,3.997,17.892,7.091,22.84,9.281c4.952,2.19,11.231,5.235,18.849,9.137
                                    c7.611,3.898,13.176,7.468,16.7,10.705c3.521,3.237,6.708,7.234,9.565,11.991s4.288,9.801,4.288,15.133
                                    c0,15.037-5.853,26.645-17.562,34.823c-11.704,8.187-25.27,12.279-40.685,12.279c-7.036,0-14.084-0.757-21.124-2.279
                                    c-24.744-4.955-47.869-16.851-69.377-35.693l-0.571-0.571c-1.714-2.088-3.999-2.946-6.851-2.563
                                    c-3.046,0.38-5.236,1.523-6.567,3.43l-29.408,38.54c-2.856,3.806-2.663,7.707,0.572,11.704c0.953,1.143,2.618,2.86,4.996,5.14
                                    c2.384,2.289,6.81,5.852,13.278,10.715c6.47,4.856,13.513,9.418,21.128,13.706c7.614,4.281,17.272,8.514,28.98,12.703
                                    c11.708,4.182,23.839,7.131,36.402,8.843v49.963c0,2.478,0.905,4.617,2.712,6.427c1.809,1.811,3.949,2.711,6.423,2.711h38.546
                                    c2.669,0,4.859-0.855,6.57-2.566s2.563-3.901,2.563-6.571v-49.963c30.269-4.948,54.87-17.939,73.806-38.972
                                    c18.938-21.033,28.41-46.11,28.41-75.229C388.994,328.801,387.759,319.855,385.261,311.475z"
                                                />
                                            </g>
                                        </svg>
                                    </span>
                                </span>
                                <div ng-if="getMeObj['orgParams']['tarif'] !== 'corporate'">
                                    <span
                                        ng-show="!$root.paidTillDateBilling && $root.companyParams.paidTillDate && paidFor > 7 && !(digit > 0 && digit <= 14)"
                                    >
                                        <span class="hidden-xs" translate="up to"></span>
                                        <span class="hidden-lg hidden-md hidden-sm" style="margin-right: 5px" translate="up to mobile"></span>
                                        {{ $root.companyParams.paidTillDate }}
                                    </span>
                                    <span ng-show="$root.paidTillDateBilling && paidFor > 7">
                                        <span class="hidden-xs" translate="up to"></span>
                                        <span class="hidden-lg hidden-md hidden-sm" style="margin-right: 5px" translate="up to mobile"></span>
                                        {{ $root.paidTillDateBilling }}
                                    </span>
                                    <span ng-show="paidFor > 4 && paidFor <= 7" translate="Days left" translate-values="{days_left: paidFor}"></span>
                                    <span ng-show="paidFor <= 4 && paidFor > 1" translate="Days left!" translate-values="{days_left: paidFor}"></span>
                                    <span ng-show="paidFor == 1" translate="Day left!" translate-values="{days_left: paidFor}"></span>
                                    <span ng-show="paidFor == 0" translate="No Day left!" translate-values="{days_left: paidFor}"></span>
                                </div>
                                <div ng-if="getMeObj['orgParams']['tarif'] === 'corporate'">
                                    <span translate="up to"></span>
                                    <span>{{ $root.companyParams.paidTillDate }}</span>
                                </div>
                            </a>
                        </li>
                        <li
                            ng-show="
                                $root.me.recrutRole == 'admin' &&
                                ((digit > 0 && digit <= 14) || getMeObj['orgParams']['tarif'] === 'free' || tarif == 'free' || $root.blockUser || paidFor < 0)
                            "
                            class="payMoney activeTrial"
                        >
                            <a
                                class="payment-panel"
                                ng-class="{
                                    'low-low-balance': paidFor !== undefined && paidFor <= 4,
                                    'low-balance': paidFor !== undefined && paidFor <= 7
                                }"
                            >
                                <span class="coins">
                                    <span class="usd-icon">
                                        <svg
                                            style="enable-background: new 0 0 511.613 511.613"
                                            version="1.1"
                                            xmlns="http://www.w3.org/2000/svg"
                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                            x="0px"
                                            y="0px"
                                            width="20px"
                                            height="20px"
                                            viewBox="0 0 511.613 511.613"
                                            xml:space="preserve"
                                        >
                                            <g>
                                                <path
                                                    d="M385.261,311.475c-2.471-8.367-5.469-15.649-8.99-21.833c-3.519-6.19-8.559-12.228-15.13-18.134
                                    c-6.563-5.903-12.467-10.657-17.702-14.271c-5.232-3.617-12.419-7.661-21.557-12.137c-9.13-4.475-16.364-7.805-21.689-9.995
                                    c-5.332-2.187-13.045-5.185-23.134-8.992c-8.945-3.424-15.605-6.042-19.987-7.849c-4.377-1.809-10.133-4.377-17.271-7.71
                                    c-7.135-3.328-12.465-6.28-15.987-8.848c-3.521-2.568-7.279-5.708-11.277-9.419s-6.805-7.661-8.424-11.848
                                    c-1.615-4.188-2.425-8.757-2.425-13.706c0-12.94,5.708-23.507,17.128-31.689c11.421-8.182,26.174-12.275,44.257-12.275
                                    c7.99,0,16.136,1.093,24.41,3.284c8.274,2.191,15.365,4.659,21.266,7.421c5.906,2.762,11.471,5.808,16.707,9.137
                                    c5.235,3.332,8.945,5.852,11.136,7.565c2.189,1.714,3.576,2.855,4.141,3.427c2.478,1.903,5.041,2.568,7.706,1.999
                                    c2.854-0.19,5.045-1.715,6.571-4.567l23.13-41.684c2.283-3.805,1.811-7.422-1.427-10.85c-1.144-1.142-2.566-2.473-4.291-3.997
                                    c-1.708-1.524-5.421-4.283-11.136-8.282c-5.709-3.996-11.752-7.565-18.124-10.706c-6.379-3.138-14.661-6.328-24.845-9.562
                                    c-10.178-3.239-20.697-5.426-31.549-6.567V9.136c0-2.663-0.855-4.853-2.563-6.567C282.493,0.859,280.303,0,277.634,0h-38.546
                                    c-2.474,0-4.615,0.903-6.423,2.712s-2.712,3.949-2.712,6.424v51.391c-29.884,5.708-54.152,18.461-72.805,38.256
                                    c-18.651,19.796-27.98,42.823-27.98,69.092c0,7.803,0.812,15.226,2.43,22.265c1.616,7.045,3.616,13.374,5.996,18.988
                                    c2.378,5.618,5.758,11.136,10.135,16.562c4.377,5.424,8.518,10.088,12.419,13.988c3.903,3.899,8.995,7.945,15.274,12.131
                                    c6.283,4.19,11.66,7.571,16.134,10.139c4.475,2.56,10.422,5.52,17.843,8.843c7.423,3.333,13.278,5.852,17.561,7.569
                                    c4.283,1.711,10.135,4.093,17.561,7.132c10.277,3.997,17.892,7.091,22.84,9.281c4.952,2.19,11.231,5.235,18.849,9.137
                                    c7.611,3.898,13.176,7.468,16.7,10.705c3.521,3.237,6.708,7.234,9.565,11.991s4.288,9.801,4.288,15.133
                                    c0,15.037-5.853,26.645-17.562,34.823c-11.704,8.187-25.27,12.279-40.685,12.279c-7.036,0-14.084-0.757-21.124-2.279
                                    c-24.744-4.955-47.869-16.851-69.377-35.693l-0.571-0.571c-1.714-2.088-3.999-2.946-6.851-2.563
                                    c-3.046,0.38-5.236,1.523-6.567,3.43l-29.408,38.54c-2.856,3.806-2.663,7.707,0.572,11.704c0.953,1.143,2.618,2.86,4.996,5.14
                                    c2.384,2.289,6.81,5.852,13.278,10.715c6.47,4.856,13.513,9.418,21.128,13.706c7.614,4.281,17.272,8.514,28.98,12.703
                                    c11.708,4.182,23.839,7.131,36.402,8.843v49.963c0,2.478,0.905,4.617,2.712,6.427c1.809,1.811,3.949,2.711,6.423,2.711h38.546
                                    c2.669,0,4.859-0.855,6.57-2.566s2.563-3.901,2.563-6.571v-49.963c30.269-4.948,54.87-17.939,73.806-38.972
                                    c18.938-21.033,28.41-46.11,28.41-75.229C388.994,328.801,387.759,319.855,385.261,311.475z"
                                                />
                                            </g>
                                        </svg>
                                    </span>
                                </span>
                            </a>
                        </li>
                        <li class="dropdown hamburger-item" style="cursor: pointer; margin-left: 18px; margin-right: 13px" uib-dropdown>
                            <span class="hamburger" uib-dropdown-toggle>
                                <a class="notice-button" style="margin-right: 0px; width: 33px; height: 33px; padding-left: 2px">
                                    <span class="notices-icon" style="width: unset; height: unset">
                                        <span>
                                            <svg
                                                class="integration-icon"
                                                width="23"
                                                height="23"
                                                viewBox="0 0 25 25"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                                -->
                                                <path
                                                    d="M21.2619 11.9524H19.619V7.57143C19.619 6.36667 18.6333 5.38095 17.4286 5.38095H13.0476V3.7381C13.0476 2.22667 11.821 1 10.3095 1C8.7981 1 7.57143 2.22667 7.57143 3.7381V5.38095H3.19048C1.98571 5.38095 1.01095 6.36667 1.01095 7.57143V11.7333H2.64286C4.27476 11.7333 5.6 13.0586 5.6 14.6905C5.6 16.3224 4.27476 17.6476 2.64286 17.6476H1V21.8095C1 23.0143 1.98571 24 3.19048 24H7.35238V22.3571C7.35238 20.7252 8.67762 19.4 10.3095 19.4C11.9414 19.4 13.2667 20.7252 13.2667 22.3571V24H17.4286C18.6333 24 19.619 23.0143 19.619 21.8095V17.4286H21.2619C22.7733 17.4286 24 16.2019 24 14.6905C24 13.179 22.7733 11.9524 21.2619 11.9524Z"
                                                    stroke="white"
                                                    stroke-width="2"
                                                />
                                            </svg>
                                        </span>
                                    </span>
                                </a>
                            </span>
                        </li>
                        <li class="hidden-xs">
                            <a id="open-notice-button" class="notice-button" href="">
                                <span class="notices-icon">
                                    <span>
                                        <svg
                                            style="enable-background: new 0 0 16.4 20"
                                            version="1.1"
                                            xmlns="http://www.w3.org/2000/svg"
                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                            x="0px"
                                            y="0px"
                                            height="19px"
                                            viewBox="0 0 16.4 20"
                                            xml:space="preserve"
                                        >
                                            <path
                                                class="st0"
                                                d="M13.9,10.3c0.1,0.9,0.3,1.8,0.5,2.8l0,0l0,0c0.1,0.6,0.5,1,0.9,1.3l0,0c0.6,0.6,1.1,1.2,1.1,2.4
                                                c0,0.4-0.3,0.8-0.8,0.8l0,0h-4.9c-0.1,0.6-0.3,1.1-0.7,1.5C9.6,19.7,9,20,8.3,20s-1.4-0.3-1.8-0.7c-0.4-0.4-0.7-0.9-0.7-1.5h-5
                                                C0.4,17.8,0,17.4,0,17l0,0c0-1.4,0.7-1.9,1.4-2.5c0.4-0.3,0.8-0.7,0.9-1.2c0.2-1,0.4-1.9,0.5-2.8v-0.3C2.9,9.5,3,9,3.1,8.6
                                                c0.3-1.6,1-3.2,2.5-4.1C5.9,4.2,6.3,4,6.7,3.9V3.8C6.3,3.4,6,2.9,6,2.2C6,1.6,6.2,1,6.7,0.6C7.1,0.3,7.6,0,8.3,0
                                                c0.6,0,1.2,0.3,1.6,0.7c0.4,0.4,0.7,1,0.7,1.6s-0.2,1.2-0.7,1.6l0,0c0.5,0.1,0.9,0.3,1.3,0.6C13.3,5.8,13.6,8.1,13.9,10.3L13.9,10.3
                                                z M9.2,1.3L9.2,1.3C9,1.1,8.6,0.9,8.3,0.9C7.9,0.9,7.6,1,7.4,1.3C7.1,1.6,7,1.9,7,2.2s0.1,0.7,0.4,0.9C7.6,3.3,8,3.5,8.3,3.5
                                                c0.4,0,0.7-0.1,0.9-0.4c0.2-0.2,0.4-0.6,0.4-0.9S9.4,1.6,9.2,1.3L9.2,1.3z M12.4,10.6L12.4,10.6c-0.3-1.7-0.4-3.8-2-4.8
                                                c-0.6-0.4-1.3-0.6-2-0.6S7,5.4,6.4,5.8C5.3,6.5,4.9,7.6,4.6,8.9c-0.1,0.4-0.2,0.9-0.2,1.4l-0.1,0.3c-0.1,0.9-0.3,1.9-0.5,2.9
                                                c-0.3,1.1-0.9,1.6-1.4,2.1C2.2,15.8,2,16,1.8,16.2h12.9c-0.1-0.2-0.3-0.4-0.5-0.6l0,0c-0.5-0.5-1-1.1-1.3-2l0,0
                                                C12.7,12.5,12.5,11.5,12.4,10.6L12.4,10.6z M9.2,17.7L9.2,17.7H7.3c0,0.2,0.1,0.3,0.2,0.4c0.2,0.2,0.4,0.3,0.7,0.3
                                                c0.3,0,0.5-0.1,0.7-0.3C9.1,18,9.2,17.9,9.2,17.7L9.2,17.7z"
                                            />
                                        </svg>
                                    </span>
                                </span>
                                <span class="label label-danger"></span>
                            </a>
                        </li>
                        <li class="hidden-sm hidden-md hidden-lg">
                            <a class="notice-button" ng-class="{ haveUnreaded: $root.currentNoticeCount > 0 }" href="#/notices">
                                <span class="notices-icon">
                                    <span>
                                        <svg
                                            style="enable-background: new 0 0 16.4 20"
                                            version="1.1"
                                            xmlns="http://www.w3.org/2000/svg"
                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                            x="0px"
                                            y="0px"
                                            height="19px"
                                            viewBox="0 0 16.4 20"
                                            xml:space="preserve"
                                        >
                                            <path
                                                class="st0"
                                                d="M13.9,10.3c0.1,0.9,0.3,1.8,0.5,2.8l0,0l0,0c0.1,0.6,0.5,1,0.9,1.3l0,0c0.6,0.6,1.1,1.2,1.1,2.4
                                                c0,0.4-0.3,0.8-0.8,0.8l0,0h-4.9c-0.1,0.6-0.3,1.1-0.7,1.5C9.6,19.7,9,20,8.3,20s-1.4-0.3-1.8-0.7c-0.4-0.4-0.7-0.9-0.7-1.5h-5
                                                C0.4,17.8,0,17.4,0,17l0,0c0-1.4,0.7-1.9,1.4-2.5c0.4-0.3,0.8-0.7,0.9-1.2c0.2-1,0.4-1.9,0.5-2.8v-0.3C2.9,9.5,3,9,3.1,8.6
                                                c0.3-1.6,1-3.2,2.5-4.1C5.9,4.2,6.3,4,6.7,3.9V3.8C6.3,3.4,6,2.9,6,2.2C6,1.6,6.2,1,6.7,0.6C7.1,0.3,7.6,0,8.3,0
                                                c0.6,0,1.2,0.3,1.6,0.7c0.4,0.4,0.7,1,0.7,1.6s-0.2,1.2-0.7,1.6l0,0c0.5,0.1,0.9,0.3,1.3,0.6C13.3,5.8,13.6,8.1,13.9,10.3L13.9,10.3
                                                z M9.2,1.3L9.2,1.3C9,1.1,8.6,0.9,8.3,0.9C7.9,0.9,7.6,1,7.4,1.3C7.1,1.6,7,1.9,7,2.2s0.1,0.7,0.4,0.9C7.6,3.3,8,3.5,8.3,3.5
                                                c0.4,0,0.7-0.1,0.9-0.4c0.2-0.2,0.4-0.6,0.4-0.9S9.4,1.6,9.2,1.3L9.2,1.3z M12.4,10.6L12.4,10.6c-0.3-1.7-0.4-3.8-2-4.8
                                                c-0.6-0.4-1.3-0.6-2-0.6S7,5.4,6.4,5.8C5.3,6.5,4.9,7.6,4.6,8.9c-0.1,0.4-0.2,0.9-0.2,1.4l-0.1,0.3c-0.1,0.9-0.3,1.9-0.5,2.9
                                                c-0.3,1.1-0.9,1.6-1.4,2.1C2.2,15.8,2,16,1.8,16.2h12.9c-0.1-0.2-0.3-0.4-0.5-0.6l0,0c-0.5-0.5-1-1.1-1.3-2l0,0
                                                C12.7,12.5,12.5,11.5,12.4,10.6L12.4,10.6z M9.2,17.7L9.2,17.7H7.3c0,0.2,0.1,0.3,0.2,0.4c0.2,0.2,0.4,0.3,0.7,0.3
                                                c0.3,0,0.5-0.1,0.7-0.3C9.1,18,9.2,17.9,9.2,17.7L9.2,17.7z"
                                            />
                                        </svg>
                                    </span>
                                </span>
                                <span ng-if="$root.currentNoticeCount > 0" class="label label-danger" ng-bind="$root.newNoticeCount"></span>
                            </a>
                        </li>
                        <li>
                            <div class="btn-group">
                                <span class="profile-button-icon">
                                    <?xml version="1.0" encoding="utf-8"?>
                                    <!-- Generator: Adobe Illustrator 19.2.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
                                    <svg
                                        style="enable-background: new 0 0 20 20"
                                        version="1.1"
                                        xmlns="http://www.w3.org/2000/svg"
                                        xmlns:xlink="http://www.w3.org/1999/xlink"
                                        x="0px"
                                        y="0px"
                                        width="33px"
                                        height="33px"
                                        fill="white"
                                        viewBox="0 0 20 20"
                                        xml:space="preserve"
                                    >
                                        <g>
                                            <g>
                                                <path
                                                    class="st0"
                                                    d="M18.976,7.901l-1.404-0.238C17.426,7.19,17.238,6.735,17.009,6.3l0.827-1.157
			c0.351-0.493,0.297-1.166-0.134-1.592l-1.245-1.245c-0.234-0.234-0.543-0.363-0.873-0.363c-0.259,0-0.506,0.079-0.714,0.23
			L13.708,3c-0.451-0.238-0.923-0.435-1.412-0.581l-0.234-1.387C11.962,0.435,11.448,0,10.842,0H9.083
			c-0.606,0-1.12,0.435-1.22,1.032L7.621,2.452C7.153,2.599,6.697,2.791,6.263,3.025L5.114,2.198c-0.209-0.15-0.46-0.23-0.719-0.23
			c-0.33,0-0.643,0.13-0.873,0.363L2.273,3.576C1.847,4.003,1.788,4.675,2.139,5.168l0.836,1.174
			c-0.23,0.439-0.414,0.894-0.556,1.366L1.032,7.942C0.435,8.043,0,8.557,0,9.162v1.759c0,0.606,0.435,1.12,1.032,1.22l1.421,0.242
			c0.146,0.468,0.338,0.923,0.572,1.358l-0.823,1.145c-0.351,0.493-0.297,1.166,0.134,1.592l1.245,1.245
			c0.234,0.234,0.543,0.363,0.873,0.363c0.259,0,0.506-0.079,0.714-0.23l1.174-0.836c0.422,0.221,0.865,0.401,1.32,0.543
			l0.234,1.404C7.997,19.565,8.511,20,9.116,20h1.763c0.606,0,1.12-0.435,1.22-1.032l0.238-1.404C12.81,17.418,13.265,17.23,13.7,17
			l1.157,0.827c0.209,0.15,0.46,0.23,0.719,0.23l0,0c0.33,0,0.639-0.13,0.873-0.363l1.245-1.245
			c0.426-0.426,0.485-1.099,0.134-1.592L17,13.695c0.23-0.439,0.422-0.894,0.564-1.362l1.404-0.234
			c0.597-0.1,1.032-0.614,1.032-1.22V9.121C20.008,8.515,19.574,8.001,18.976,7.901z M18.88,10.879c0,0.054-0.038,0.1-0.092,0.109
			l-1.755,0.292c-0.221,0.038-0.397,0.201-0.451,0.414c-0.159,0.614-0.401,1.203-0.727,1.751c-0.113,0.192-0.104,0.43,0.025,0.614
			l1.032,1.454c0.029,0.042,0.025,0.104-0.013,0.142L15.655,16.9c-0.029,0.029-0.058,0.033-0.079,0.033
			c-0.025,0-0.046-0.008-0.063-0.021l-1.45-1.032c-0.18-0.13-0.422-0.138-0.614-0.025c-0.547,0.326-1.136,0.568-1.751,0.727
			c-0.217,0.054-0.38,0.234-0.414,0.451l-0.297,1.755c-0.008,0.054-0.054,0.092-0.109,0.092H9.121c-0.054,0-0.1-0.038-0.109-0.092
			l-0.292-1.755c-0.038-0.221-0.201-0.397-0.414-0.451c-0.597-0.155-1.174-0.393-1.713-0.702c-0.088-0.05-0.188-0.075-0.284-0.075
			c-0.113,0-0.23,0.033-0.326,0.104l-1.462,1.04c-0.021,0.013-0.042,0.021-0.063,0.021c-0.017,0-0.05-0.004-0.079-0.033
			l-1.245-1.245c-0.038-0.038-0.042-0.096-0.013-0.142l1.028-1.441c0.13-0.184,0.138-0.426,0.025-0.618
			c-0.326-0.543-0.577-1.132-0.735-1.746c-0.058-0.213-0.234-0.376-0.451-0.414L1.22,11.03c-0.054-0.008-0.092-0.054-0.092-0.109
			V9.162c0-0.054,0.038-0.1,0.092-0.109l1.742-0.292c0.221-0.038,0.401-0.201,0.455-0.418C3.572,7.729,3.81,7.136,4.132,6.589
			c0.113-0.192,0.1-0.43-0.029-0.61l-1.04-1.462C3.033,4.475,3.037,4.412,3.075,4.374L4.32,3.129C4.349,3.1,4.379,3.096,4.399,3.096
			c0.025,0,0.046,0.008,0.063,0.021l1.441,1.028C6.087,4.274,6.33,4.282,6.522,4.17c0.543-0.326,1.132-0.577,1.746-0.735
			C8.481,3.376,8.644,3.2,8.682,2.983l0.301-1.767c0.008-0.054,0.054-0.092,0.109-0.092h1.759c0.054,0,0.1,0.038,0.109,0.092
			l0.292,1.742c0.038,0.221,0.201,0.401,0.418,0.455c0.631,0.159,1.233,0.405,1.792,0.735c0.192,0.113,0.43,0.104,0.614-0.025
			l1.441-1.036c0.021-0.013,0.042-0.021,0.063-0.021c0.017,0,0.05,0.004,0.079,0.033l1.245,1.245
			c0.038,0.038,0.042,0.096,0.013,0.142l-1.032,1.45c-0.13,0.18-0.138,0.422-0.025,0.614c0.326,0.547,0.568,1.136,0.727,1.751
			c0.054,0.217,0.234,0.38,0.451,0.414l1.755,0.297c0.054,0.008,0.092,0.054,0.092,0.109v1.759H18.88z"
                                                />
                                                <path
                                                    class="st0"
                                                    d="M10.002,5.682c-2.381,0-4.316,1.934-4.316,4.316s1.934,4.316,4.316,4.316s4.316-1.934,4.316-4.316
			S12.384,5.682,10.002,5.682z M10.002,13.186c-1.759,0-3.188-1.429-3.188-3.188S8.243,6.81,10.002,6.81s3.188,1.429,3.188,3.188
			C13.19,11.757,11.761,13.186,10.002,13.186z"
                                                />
                                            </g>
                                        </g>
                                    </svg>
                                </span>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div id="navbar-bgc" style="background-color: #f8fafb">
            <div
                ng-if="vm.$state.is('onboarding.organizer')"
                class="container-fluid second-navbar hidden-sm hidden-xs pointer-events-none"
                style="padding-left: 15px; padding-right: 15px"
            >
                <div class="second-navbar">
                    <ul class="nav navbar-nav tabs-nav col-lg-7 col-md-7 col-sm-12 col-xs-12">
                        <li class="tab tab_0 dropdown" ng-class="{'selected': $root.onboardingActiveStage = 'stage1'}" style="z-index: inherit">
                            <a class="no-margin" href="#/organizer/dashboard">{{ 'Overview' | translate }}</a>
                        </li>
                        <li class="tab tab_0 dropdown" style="z-index: inherit">
                            <a class="no-margin" translate="My tasks"></a>
                        </li>
                        <li class="tab tab_1 dropdown" ng-class="{ selected: $root.activePage == 'Activity Calendar' }" style="z-index: inherit">
                            <a translate="calendar"></a>
                        </li>
                    </ul>
                    <ul class="nav col-lg-5 col-md-5 hidden-sm hidden-xs right-nav" style="justify-content: flex-end">
                        <li
                            ng-show="$root.me.recrutRole == 'admin' && $root.blockUser != true"
                            class="dropdown invite-button"
                            ng-class="{ 'ru-width': $root.currentLang !== 'en' }"
                        >
                            <button class="btn-animated invite_user">
                                <div>
                                    <svg
                                        style="fill: white"
                                        xmlns="http://www.w3.org/2000/svg"
                                        xmlns:xlink="http://www.w3.org/1999/xlink"
                                        version="1.1"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            d="M15,4C12.79,4 11,5.79 11,8C11,10.21 12.79,12 15,12C17.21,12 19,10.21 19,8C19,5.79 17.21,4 15,4M15,5.9C16.16,5.9 17.1,6.84 17.1,8C17.1,9.16 16.16,10.1 15,10.1C13.84,10.1 12.9,9.16 12.9,8C12.9,6.84 13.84,5.9 15,5.9M4,7V10H1V12H4V15H6V12H9V10H6V7H4M15,13C12.33,13 7,14.33 7,17V20H23V17C23,14.33 17.67,13 15,13M15,14.9C17.97,14.9 21.1,16.36 21.1,17V18.1H8.9V17C8.9,16.36 12,14.9 15,14.9Z"
                                        />
                                    </svg>
                                </div>
                                <span style="margin-left: 20px">
                                    {{ 'Invite User / Client' | translate }}
                                </span>
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
            <div
                ng-if="vm.$state.is('onboarding.vacancies') || vm.$state.is('onboarding.vacancyAdd') || vm.$state.is('onboarding.vacancy')"
                class="container-fluid second-navbar"
            >
                <div class="second-navbar">
                    <ul class="nav tabs-nav vacancy-second-header navbar-nav col-lg-7 col-md-7" style="z-index: 1">
                        <li
                            class="tab tab_0 dropdown pointer-events-none"
                            ng-class="{
                                selected:
                                    $root.activePage == 'Vacancies' ||
                                    vm.currentModal === 'vacancies' ||
                                    $root.onboardingNewCandidateName === 'vacancies' ||
                                    $root.onboardingActiveStage === 'stage3'
                            }"
                        >
                            <a translate="Active vacancies"></a>
                        </li>
                        <li
                            ng-click="vm.chooseCurrentModal('new vacancy');"
                            class="tab tab_1 active-onboarding-action-second-nav"
                            ng-class="{
                                displayNone: $root.onboardingActiveStage !== 'stage1',
                                selected: vm.currentModal === 'new vacancy'
                            }"
                        >
                            <a
                                ng-style="$root.onboardingActiveStage === 'stage1' ? { padding: '7px 16px 6px' } : { padding: '8px 16px 6px' }"
                                ui-sref="onboarding.vacancyAdd({stage: 2})"
                                translate="New vacancy"
                            ></a>
                        </li>
                        <li
                            class="tab tab_1 pointer-events-none"
                            ng-class="{
                                displayNone: $root.onboardingActiveStage === 'stage1',
                                selected: $root.onboardingActiveStage == 'stage2'
                            }"
                        >
                            <a translate="New vacancy"></a>
                        </li>
                        <li class="tab tab_2 pointer-events-none hidden-xs" ng-class="{ selected: $root.activePage == 'Mailings' }">
                            <a class="mailing-tab">
                                {{ 'Mass mailings' | translate }}
                            </a>
                        </li>
                        <li class="tab tab_3 pointer-events-none" ng-class="{ selected: $root.activePage == 'Vacancy application' }">
                            <a class="application-tab">
                                {{ 'Vacancy application' | translate }}
                            </a>
                        </li>
                    </ul>
                    <ul class="nav col-lg-5 col-md-5 right-nav hidden-sm pointer-events-none">
                        <li class="our-vacancies-button">
                            <a class="public-vacancy" ng-class="{ 'ru-width': $root.currentLang !== 'en' }">
                                <svg
                                    style="fill: white; margin-right: 5px"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    version="1.1"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        d="M4 6H2V20C2 21.11 2.9 22 4 22H18V20H4V6M18.5 14.25C18.5 12.75 15.5 12 14 12S9.5 12.75 9.5 14.25V15H18.5M14 10.25C15.24 10.25 16.25 9.24 16.25 8S15.24 5.75 14 5.75 11.75 6.76 11.75 8 12.76 10.25 14 10.25M20 2H8C6.9 2 6 2.9 6 4V16C6 17.11 6.9 18 8 18H20C21.11 18 22 17.11 22 16V4C22 2.89 21.1 2 20 2M20 16H8V4H20V16Z"
                                    />
                                </svg>
                                <span>{{ 'Open_vacancies_for_company' | translate }}</span>
                            </a>
                        </li>
                        <li
                            ng-if="
                                $root.onboardingActiveStage === 'stage1' ||
                                $root.onboardingActiveStage === 'stage3' ||
                                $root.onboardingActiveStage === 'stage5' ||
                                $root.onboardingActiveStage === 'stage6' ||
                                $root.onboardingActiveStage === 'stage7'
                            "
                            class="scope"
                            account-scope-panel
                        ></li>
                    </ul>
                </div>
            </div>
            <div
                ng-if="vm.$state.is('onboarding.candidates') || vm.$state.is('onboarding.candidateAdd') || vm.$state.is('onboarding.candidate')"
                class="container-fluid second-navbar"
            >
                <div class="second-navbar">
                    <ul class="nav navbar-nav tabs-nav col-lg-7 col-md-8 col-sm-12 col-xs-12 col-sm-offset-3 col-xs-offset-1 text-center">
                        <li
                            class="tab tab_0 dropdown hidden-sm hidden-xs"
                            ng-class="{
                                selected:
                                    (vm.currentModal == 'candidates' || $root.onboardingActiveStage === 'stage4' || $root.onboardingActiveStage === 'stage5') &&
                                    $root.onboardingNewCandidateName !== 'candidate'
                            }"
                            style="margin-left: 0"
                        >
                            <a class="no-margin">
                                {{ 'our_base' | translate }}
                            </a>
                        </li>
                        <li
                            ng-show="$root.me.recrutRole != 'client'"
                            class="tab tab_1 hidden-sm hidden-xs"
                            ng-class="{
                                'active-onboarding-action-second-nav': $root.onboardingActiveStage === 'stage3' && vm.$state.is('onboarding.candidates'),
                                selected: vm.currentModal === 'candidate' && $root.onboardingActiveStage === 'stage3'
                            }"
                            ng-hide="$root.onboardingActiveStage === 'stage4' || $root.onboardingActiveStage === 'stage5'"
                        >
                            <a
                                ng-click="vm.chooseCurrentModal('candidate')"
                                ng-style="$root.onboardingActiveStage === 'stage3' ? { 'margin-bottom': '3px' } : { 'margin-top': '0px' }"
                                ui-sref="onboarding.candidateAdd({stage: 4})"
                            >
                                {{ 'new' | translate }}
                                <span style="text-transform: lowercase">{{ 'candidate' | translate }}</span>
                            </a>
                        </li>
                        <li
                            ng-show="$root.onboardingActiveStage === 'stage4' || $root.onboardingActiveStage === 'stage5'"
                            class="tab tab_1 hidden-sm hidden-xs pointer-events-none"
                            ng-class="{
                                'active-onboarding-action-second-nav': $root.onboardingActiveStage === 'stage3' && vm.$state.is('onboarding.candidates'),
                                selected: $root.onboardingNewCandidateName === 'candidate' && $root.onboardingActiveStage === 'stage4'
                            }"
                            style="display: none"
                            ng-style="
                                $root.onboardingActiveStage === 'stage4' || $root.onboardingActiveStage === 'stage5'
                                    ? { display: 'block' }
                                    : { display: 'none' }
                            "
                        >
                            <a class="pointer-events-none">
                                {{ 'new' | translate }}
                                <span style="text-transform: lowercase">{{ 'candidate' | translate }}</span>
                            </a>
                        </li>
                        <li
                            ng-show="$root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter'"
                            class="tab tab_2 pointer-events-none"
                            ng-class="{ selected: $root.activePage == 'Tests and forms' }"
                        >
                            <a class="pointer-events-none">
                                {{ 'Tests and forms' | translate }}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div ui-view></div>
    <div
        class="state-quest-block"
        ng-class="{
            stage4: $root.onboardingActiveStage === 'stage4',
            stage5: $root.onboardingActiveStage === 'stage5',
            stage6: $root.onboardingActiveStage === 'stage6',
            stage7: $root.onboardingActiveStage === 'stage7',
            onVacancies: vm.currentModal === 'vacancies' && $root.onboardingActiveStage === 'stage5'
        }"
        ng-hide="
            ($root.onboardingActiveStage === 'stage1' && vm.$state.is('onboarding.vacancyAdd')) ||
            ($root.onboardingActiveStage === 'stage6' && !$root.onboardingShowStageSix)
        "
    >
        <div class="state-quest-block-header">
            <span class="state-quest-block-header-text">{{ 'Current quest' | translate }}</span>
            <span ng-if="$root.onboardingActiveStage === 'stage1'" class="state-quest-block-header-stage">
                {{ 'Find New vacancy tab' | translate }}
            </span>
            <span ng-if="$root.onboardingActiveStage === 'stage2'" class="state-quest-block-header-stage">
                {{ 'Create vacancy' | translate }}
            </span>
            <span ng-if="$root.onboardingActiveStage === 'stage3'" class="state-quest-block-header-stage">
                {{ 'Add resume' | translate }}
            </span>
            <span ng-if="$root.onboardingActiveStage === 'stage4'" class="state-quest-block-header-stage">
                {{ 'Add a candidate on a vacancy' | translate }}
            </span>
            <span ng-if="$root.onboardingActiveStage === 'stage5'" class="state-quest-block-header-stage">
                {{ 'Active vacancy overview' | translate }}
            </span>
            <span ng-if="$root.onboardingActiveStage === 'stage6'" class="state-quest-block-header-stage">
                {{ 'Invite Sandra for an interview' | translate }}
            </span>
            <span ng-if="$root.onboardingActiveStage === 'stage7'" class="state-quest-block-header-stage">
                {{ 'Close vacancy' | translate }}
            </span>
        </div>
        <div class="state-quest-block-status">
            <div ng-if="$root.onboardingActiveStage === 'stage1'" class="state-quest-block-status-items">
                <div class="flex-justify-align-center">
                    <div ng-if="vm.$state.is('onboarding.organizer')" class="state-quest-block-status-items-circle"></div>
                    <div ng-if="vm.$state.is('onboarding.vacancies')" class="state-quest-block-status-items-circle filled"></div>
                    <span class="gamification-blue-title-secondary">{{ 'Go to the Vacancies tab' | translate }}</span>
                </div>
                <div class="flex-justify-align-center margin-top-10">
                    <div class="state-quest-block-status-items-circle"></div>
                    <span class="gamification-blue-title-secondary">{{ 'Go to the New vacancy tab' | translate }}</span>
                </div>
            </div>
            <div ng-if="$root.onboardingActiveStage === 'stage2'" class="state-quest-block-status-items">
                <div class="flex-justify-align-center">
                    <div ng-if="$root.onboardingIndustry !== 'IT'" class="state-quest-block-status-items-circle"></div>
                    <div ng-if="$root.onboardingIndustry === 'IT'" class="state-quest-block-status-items-circle filled"></div>
                    <span class="gamification-blue-title-secondary">{{ 'Choose an IT industry' | translate }}</span>
                </div>
                <div class="flex-justify-align-center margin-top-10">
                    <div class="state-quest-block-status-items-circle"></div>
                    <span class="gamification-blue-title-secondary">{{ 'Save vacancy' | translate }}</span>
                </div>
            </div>
            <div ng-if="$root.onboardingActiveStage === 'stage3'" class="state-quest-block-status-items">
                <div class="flex-justify-align-center">
                    <div class="state-quest-block-status-items-circle" ng-hide="vm.$state.is('onboarding.candidateAdd')"></div>
                    <div ng-if="vm.$state.is('onboarding.candidateAdd')" class="state-quest-block-status-items-circle filled"></div>
                    <span class="gamification-blue-title-secondary">
                        {{ 'Go to the Candidates tab - New candidate' | translate }}
                    </span>
                </div>
                <div class="flex-justify-align-center margin-top-10">
                    <div class="state-quest-block-status-items-circle"></div>
                    <span class="gamification-blue-title-secondary">{{ 'add_candidate_2' | translate }}</span>
                </div>
            </div>
            <div ng-if="$root.onboardingActiveStage === 'stage4'" class="state-quest-block-status-items">
                <div class="flex-justify-align-center">
                    <div class="state-quest-block-status-items-circle" ng-hide="vm.$state.is('onboarding.candidateAdd')"></div>
                    <div ng-if="vm.$state.is('onboarding.candidateAdd')" class="state-quest-block-status-items-circle filled"></div>
                    <span class="gamification-blue-title-secondary">
                        {{ 'Add a candidate on a vacancy' | translate }}
                    </span>
                </div>
                <div class="flex-justify-align-center margin-top-10">
                    <div class="state-quest-block-status-items-circle"></div>
                    <span class="gamification-blue-title-secondary">
                        {{ 'Select Tutorial Master vacancy' | translate }}
                    </span>
                </div>
            </div>
            <div ng-if="$root.onboardingActiveStage === 'stage5'" class="state-quest-block-status-items">
                <div class="flex-justify-align-center">
                    <div class="state-quest-block-status-items-circle" ng-hide="vm.$state.is('onboarding.vacancies')"></div>
                    <div ng-if="vm.$state.is('onboarding.vacancies')" class="state-quest-block-status-items-circle filled"></div>
                    <span class="gamification-blue-title-secondary">{{ 'Go to the Vacancies tab' | translate }}</span>
                </div>
                <div class="flex-justify-align-center margin-top-10">
                    <div class="state-quest-block-status-items-circle"></div>
                    <span class="gamification-blue-title-secondary">
                        {{ 'Select Tutorial Master vacancy' | translate }}
                    </span>
                </div>
            </div>
            <div ng-if="$root.onboardingActiveStage === 'stage6'" class="state-quest-block-status-items">
                <div class="flex-justify-align-center">
                    <div class="state-quest-block-status-items-circle" ng-hide="$root.onboardingStageSixBtnPressed"></div>
                    <div ng-if="$root.onboardingStageSixBtnPressed" class="state-quest-block-status-items-circle filled"></div>
                    <span class="gamification-blue-title-secondary">{{ 'Click Change stage' | translate }}</span>
                </div>
                <div class="flex-justify-align-center margin-top-10">
                    <div class="state-quest-block-status-items-circle" ng-hide="$root.onboardingStagesCompleter.value === 'interview'"></div>
                    <div ng-if="$root.onboardingStagesCompleter.value === 'interview'" class="state-quest-block-status-items-circle filled"></div>
                    <span class="gamification-blue-title-secondary">{{ 'Select Interview stage' | translate }}</span>
                </div>
                <div class="flex-justify-align-center margin-top-10">
                    <div class="state-quest-block-status-items-circle"></div>
                    <span class="gamification-blue-title-secondary">
                        {{ 'Send Sandra an invitation letter' | translate }}
                    </span>
                </div>
            </div>
            <div ng-if="$root.onboardingActiveStage === 'stage7'" class="state-quest-block-status-items">
                <div class="flex-justify-align-center">
                    <div class="state-quest-block-status-items-circle" ng-hide="$root.onboardingSevenStageIsInterviewActive"></div>
                    <div ng-if="$root.onboardingSevenStageIsInterviewActive" class="state-quest-block-status-items-circle filled"></div>
                    <span class="gamification-blue-title-secondary">{{ 'Go to the Interview stage' | translate }}</span>
                </div>
                <div class="flex-justify-align-center margin-top-10">
                    <div
                        class="state-quest-block-status-items-circle"
                        ng-hide="$root.onboardingStageSevenBtnPressed || $root.onboardingStage7StatusPopupDone"
                    ></div>
                    <div
                        ng-if="$root.onboardingStageSevenBtnPressed || $root.onboardingStage7StatusPopupDone"
                        class="state-quest-block-status-items-circle filled"
                    ></div>
                    <span class="gamification-blue-title-secondary">{{ 'Click Change stage' | translate }}</span>
                </div>
                <div class="flex-justify-align-center margin-top-10">
                    <div class="state-quest-block-status-items-circle" ng-hide="$root.onboardingStage7StatusPopupDone"></div>
                    <div ng-if="$root.onboardingStage7StatusPopupDone" class="state-quest-block-status-items-circle filled"></div>
                    <span class="gamification-blue-title-secondary">
                        {{ 'Move Sandra to the stage Hired' | translate }}
                    </span>
                </div>
                <div class="flex-justify-align-center margin-top-10">
                    <div class="state-quest-block-status-items-circle" ng-hide="$root.onboardingVacancyClosed"></div>
                    <div ng-if="$root.onboardingVacancyClosed" class="state-quest-block-status-items-circle filled"></div>
                    <span ng-if="$root.me.recrutRole != 'client'" class="gamification-blue-title-secondary">
                        {{ 'Close_vacancy' | translate }}
                    </span>
                    <span ng-if="$root.me.recrutRole == 'client'" class="gamification-blue-title-secondary">
                        {{ 'Leave the vacancy open' | translate }}
                    </span>
                </div>
            </div>
            <div class="state-quest-block-status-award" ng-hide="$root.me.personParams.onboardingFinished === 'true'">
                <span ng-if="$root.me.recrutRole != 'client'">
                    <span
                        class="headline-orange-gamification"
                        style="font-size: 14px"
                        ng-hide="$root.onboardingActiveStage === 'stage6' || $root.onboardingActiveStage === 'stage7'"
                        translate="Reward_experience"
                        translate-values="{count:5}"
                    ></span>
                    <span
                        ng-show="$root.onboardingActiveStage === 'stage6'"
                        class="headline-orange-gamification"
                        style="font-size: 14px"
                        translate="Reward_experience"
                        translate-values="{count:10}"
                    ></span>
                    <span
                        ng-show="$root.onboardingActiveStage === 'stage7'"
                        class="headline-orange-gamification"
                        style="font-size: 14px"
                        translate="Reward_experience"
                        translate-values="{count:15}"
                    ></span>
                </span>
                <span ng-if="$root.me.recrutRole == 'client'">
                    <span
                        class="headline-orange-gamification"
                        style="font-size: 14px"
                        ng-hide="$root.onboardingActiveStage === 'stage6' || $root.onboardingActiveStage === 'stage7'"
                        translate="Reward_experience"
                        translate-values="{count:15}"
                    ></span>
                    <span
                        ng-show="$root.onboardingActiveStage === 'stage6'"
                        class="headline-orange-gamification"
                        style="font-size: 14px"
                        translate="Reward_experience"
                        translate-values="{count:15}"
                    ></span>
                    <span
                        ng-show="$root.onboardingActiveStage === 'stage7'"
                        class="headline-orange-gamification"
                        style="font-size: 14px"
                        translate="Reward_experience"
                        translate-values="{count:20}"
                    ></span>
                </span>
            </div>
        </div>
        <div ng-if="$root.onboardingActiveStage === 'stage1'" class="state-quest-block-footer">
            <span class="gamification-blue-title-secondary">
                {{ 'All your active vacancies will be displayed here.' | translate }}
            </span>
        </div>
    </div>
    <div class="skipOnboardingBlock">
        <span ng-click="vm.skipOnboarding()" class="skipOnboardingBlock-text">{{ 'Skip' | translate }}</span>
        <span>{{ 'You can return to training in the system chat' | translate }}</span>
        <span style="text-decoration: none" ng-hide="$root.me.personParams.onboardingFinished === 'true'">&nbsp{{ 'getPointsAfterFinish' | translate }}</span>
    </div>
</div>
