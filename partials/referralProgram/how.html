<div class="how">
    <div class="steps-legend hidden-lg">
        <span ng-click="vm.changeStep(1)" ng-class="{ active: vm.currentStep === 1 }"><span>1</span></span>
        <span ng-click="vm.changeStep(2)" ng-class="{ active: vm.currentStep === 2 }"><span>2</span></span>
        <span ng-click="vm.changeStep(3)" ng-class="{ active: vm.currentStep === 3 }"><span>3</span></span>
        <span ng-click="vm.changeStep(4)" class="step-legend_4" ng-class="{ active: vm.currentStep === 4 }">
            <span>4</span>
        </span>
        <span ng-click="vm.changeStep(5)" ng-class="{ active: vm.currentStep === 5 }"><span>5</span></span>
    </div>
    <div class="steps-wrapper hidden-xs">
        <div ng-if="vm.currentStep === 1" class="step-block">
            <div class="left-part">
                <img src="images/sprite/referral-program/hiw-1.png" alt="Hiw" />
            </div>
            <div class="right-part">
                <div class="step-number">
                    <span class="number" ng-style="vm.isSafari ? { 'line-height': '57px' } : { 'line-height': '62px' }" ng-bind="vm.currentStep"></span>
                    <span class="text" translate="STEP"></span>
                </div>
                <div class="expl-text">
                    <div class="expl-text-content">
                        <span translate="Find your personal referral link on the referral page in CleverStaff"></span>
                    </div>
                </div>
                <div class="buttons-block hidden-md hidden-sm hidden-xs">
                    <button ng-click="vm.changeStep(2)" class="btn-green" translate="Forward to step {step}" translate-values="{step: 2}"></button>
                </div>
            </div>
        </div>
        <div ng-if="vm.currentStep === 1" class="buttons-block hidden-lg">
            <button ng-click="vm.changeStep(2)" class="btn-green" translate="Forward to step {step}" translate-values="{step: 2}"></button>
        </div>

        <div ng-if="vm.currentStep === 2" class="step-block">
            <div class="left-part">
                <img src="images/sprite/referral-program/hiw-2.png" alt="Hiw" />
            </div>
            <div class="right-part">
                <div class="step-number">
                    <span class="number" ng-style="vm.isSafari ? { 'line-height': '57px' } : { 'line-height': '62px' }" ng-bind="vm.currentStep"></span>
                    <span class="text" translate="STEP"></span>
                </div>
                <div class="expl-text">
                    <div class="expl-text-content">
                        <span translate="Share this link with your mates or post it anywhere"></span>
                    </div>
                </div>
                <div class="buttons-block hidden-md hidden-sm hidden-xs">
                    <button ng-click="vm.changeStep(1)" class="back-btn" translate="Back to step {step}" translate-values="{step: 1}"></button>
                    <button ng-click="vm.changeStep(3)" class="btn-green" translate="Forward to step {step}" translate-values="{step: 3}"></button>
                </div>
            </div>
        </div>
        <div ng-if="vm.currentStep === 2" class="buttons-block hidden-lg">
            <button ng-click="vm.changeStep(1)" class="back-btn" translate="Back to step {step}" translate-values="{step: 1}"></button>
            <button ng-click="vm.changeStep(3)" class="btn-green" translate="Forward to step {step}" translate-values="{step: 3}"></button>
        </div>

        <div ng-if="vm.currentStep === 3" class="step-block">
            <div class="left-part">
                <img src="images/sprite/referral-program/hiw-3.png" alt="Hiw" />
            </div>
            <div class="right-part">
                <div class="step-number">
                    <span class="number" ng-style="vm.isSafari ? { 'line-height': '57px' } : { 'line-height': '62px' }" ng-bind="vm.currentStep"></span>
                    <span class="text" translate="STEP"></span>
                </div>
                <div class="expl-text">
                    <div class="expl-text-content">
                        <span translate="Sender uses your link to register in CleverStaff and makes the first payment"></span>
                    </div>
                </div>
                <div class="buttons-block hidden-md hidden-sm hidden-xs">
                    <button ng-click="vm.changeStep(2)" class="back-btn" translate="Back to step {step}" translate-values="{step: 2}"></button>
                    <button ng-click="vm.changeStep(4)" class="btn-green" translate="Forward to step {step}" translate-values="{step: 4}"></button>
                </div>
            </div>
        </div>
        <div ng-if="vm.currentStep === 3" class="buttons-block hidden-lg">
            <button ng-click="vm.changeStep(2)" class="back-btn" translate="Back to step {step}" translate-values="{step: 2}"></button>
            <button ng-click="vm.changeStep(4)" class="btn-green" translate="Forward to step {step}" translate-values="{step: 4}"></button>
        </div>

        <div ng-if="vm.currentStep === 4" class="step-block">
            <div class="left-part">
                <img src="images/sprite/referral-program/hiw-4.png" alt="Hiw" />
            </div>
            <div class="right-part">
                <div class="step-number">
                    <span class="number" ng-style="vm.isSafari ? { 'line-height': '57px' } : { 'line-height': '62px' }" ng-bind="vm.currentStep"></span>
                    <span class="text" translate="STEP"></span>
                </div>
                <div class="expl-text">
                    <div class="expl-text-content">
                        <span translate="You receive 100% of your referral's first payment, but no more than $500."></span>
                    </div>
                </div>
                <div class="buttons-block hidden-md hidden-sm hidden-xs">
                    <button ng-click="vm.changeStep(3)" class="back-btn" translate="Back to step {step}" translate-values="{step: 3}"></button>
                    <button ng-click="vm.changeStep(5)" class="btn-green" translate="Forward to step {step}" translate-values="{step: 5}"></button>
                </div>
            </div>
        </div>
        <div ng-if="vm.currentStep === 4" class="buttons-block hidden-lg">
            <button ng-click="vm.changeStep(3)" class="back-btn" translate="Back to step {step}" translate-values="{step: 3}"></button>
            <button ng-click="vm.changeStep(5)" class="btn-green" translate="Forward to step {step}" translate-values="{step: 5}"></button>
        </div>

        <div ng-if="vm.currentStep === 5" class="step-block">
            <div class="left-part">
                <img src="images/sprite/referral-program/hiw-5.png" alt="Hiw" />
            </div>
            <div class="right-part">
                <div class="step-number">
                    <span class="number" ng-style="vm.isSafari ? { 'line-height': '57px' } : { 'line-height': '62px' }" ng-bind="vm.currentStep"></span>
                    <span class="text" translate="STEP"></span>
                </div>
                <div class="expl-text">
                    <div class="expl-text-content">
                        <span
                            translate="You choose the way to get your rewards. You can either transfer money to your bank account or top up your CleverStaff account. Easy - Peasy!"
                        ></span>
                    </div>
                </div>
                <div class="buttons-block hidden-md hidden-sm hidden-xs">
                    <button ng-click="vm.changeStep(4)" class="back-btn" translate="Back to step {step}" translate-values="{step: 4}"></button>
                </div>
            </div>
        </div>
        <div ng-if="vm.currentStep === 5" class="buttons-block hidden-lg">
            <button ng-click="vm.changeStep(4)" class="back-btn" translate="Back to step {step}" translate-values="{step: 4}"></button>
        </div>
    </div>
    <div class="steps-wrapper mobile hidden-sm hidden-md hidden-lg" horizontal-swipe right-swipe="vm.stepForward" left-swipe="vm.stepBack" context="vm">
        <span class="slider-tip">
            <span class="slider-tip-wrapper">
                <span class="hand"></span>
                <span class="arrow"></span>
            </span>
        </span>
        <div ng-if="vm.currentStep === 1" class="step-block">
            <img src="images/sprite/referral-program/hiw-1_M.png" alt="Hiw" />
            <div class="right-part">
                <div class="step-number">
                    <span class="number" ng-style="vm.isSafari ? { 'line-height': '57px' } : { 'line-height': '62px' }" ng-bind="vm.currentStep"></span>
                    <span class="text" translate="STEP"></span>
                </div>
                <div class="expl-text">
                    <div class="expl-text-content">
                        <span translate="Find your personal referral link on the referral page in CleverStaff"></span>
                    </div>
                </div>
            </div>
        </div>

        <div ng-if="vm.currentStep === 2" class="step-block">
            <img src="images/sprite/referral-program/hiw-2_M.png" alt="Hiw" />
            <div class="right-part">
                <div class="step-number">
                    <span class="number" ng-style="vm.isSafari ? { 'line-height': '57px' } : { 'line-height': '62px' }" ng-bind="vm.currentStep"></span>
                    <span class="text" translate="STEP"></span>
                </div>
                <div class="expl-text">
                    <div class="expl-text-content">
                        <span translate="Share this link with your mates or post it anywhere"></span>
                    </div>
                </div>
            </div>
        </div>

        <div ng-if="vm.currentStep === 3" class="step-block">
            <img src="images/sprite/referral-program/hiw-3_M.png" alt="Hiw" />
            <div class="right-part">
                <div class="step-number">
                    <span class="number" ng-style="vm.isSafari ? { 'line-height': '57px' } : { 'line-height': '62px' }" ng-bind="vm.currentStep"></span>
                    <span class="text" translate="STEP"></span>
                </div>
                <div class="expl-text">
                    <div class="expl-text-content">
                        <span translate="Sender uses your link to register in CleverStaff and makes the first payment"></span>
                    </div>
                </div>
            </div>
        </div>

        <div ng-if="vm.currentStep === 4" class="step-block">
            <img src="images/sprite/referral-program/hiw-4_M.png" alt="Hiw" />
            <div class="right-part">
                <div class="step-number">
                    <span class="number" ng-style="vm.isSafari ? { 'line-height': '57px' } : { 'line-height': '62px' }" ng-bind="vm.currentStep"></span>
                    <span class="text" translate="STEP"></span>
                </div>
                <div class="expl-text">
                    <div class="expl-text-content">
                        <span translate="You receive 100% of your referral's first payment, but no more than $500."></span>
                    </div>
                </div>
            </div>
        </div>

        <div ng-if="vm.currentStep === 5" class="step-block">
            <img src="images/sprite/referral-program/hiw-5_M.png" alt="Hiw" />
            <div class="right-part">
                <div class="step-number">
                    <span class="number" ng-style="vm.isSafari ? { 'line-height': '57px' } : { 'line-height': '62px' }" ng-bind="vm.currentStep"></span>
                    <span class="text" translate="STEP"></span>
                </div>
                <div class="expl-text">
                    <div class="expl-text-content">
                        <span
                            translate="You choose the way to get your rewards. You can either transfer money to your bank account or top up your CleverStaff account. Easy - Peasy!"
                        ></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="steps-legend hidden-md hidden-sm hidden-xs">
        <span ng-click="vm.changeStep(1)" ng-class="{ active: vm.currentStep === 1 }"><span>1</span></span>
        <span ng-click="vm.changeStep(2)" ng-class="{ active: vm.currentStep === 2 }"><span>2</span></span>
        <span ng-click="vm.changeStep(3)" ng-class="{ active: vm.currentStep === 3 }"><span>3</span></span>
        <span ng-click="vm.changeStep(4)" class="step-legend_4" ng-class="{ active: vm.currentStep === 4 }">
            <span>4</span>
        </span>
        <span ng-click="vm.changeStep(5)" ng-class="{ active: vm.currentStep === 5 }"><span>5</span></span>
    </div>
</div>
