<ul class="subtabs-list">
    <li class="subtab" ng-class="{ subtab__selected: $root.$state.current.data.pageName == 'Results' }">
        <a class="subtab_link" href="#/achievements/results">{{ 'Result_tab' | translate }}</a>
    </li>
    <li class="subtab" ng-class="{ subtab__selected: $root.$state.current.data.pageName == 'Collection' }" style="position: relative">
        <a class="subtab_link" href="#/achievements/collections">{{ 'Collection' | translate }}</a>
        <div class="gamification-tab-counter hideGamification" ng-style="$root.collectionCounts > 0 ? { display: 'flex' } : { display: 'none' }">
            <span>{{ $root.collectionCounts }}</span>
        </div>
    </li>
    <li class="subtab" ng-class="{ subtab__selected: $root.$state.current.data.pageName == 'Awards' }">
        <a class="subtab_link" href="#/achievements/awards">{{ 'Awards' | translate }}</a>
    </li>
</ul>
