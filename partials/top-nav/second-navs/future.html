<div class="container-fluid second-navbar hidden-sm hidden-xs" style="padding-left: 15px; padding-right: 15px">
    <div class="second-navbar">
        <ul class="nav navbar-nav tabs-nav col-lg-7 col-md-7 col-sm-12 col-xs-12">
            <li
                ng-if="$root.me.recrutRole === 'admin' || $root.me.recrutRole === 'recruter'"
                ng-click='changeOrganizerTab("organizer/dashboard")'
                class="tab tab_0 dropdown"
                ng-class="{ selected: $root.activePage == 'Activity Dashboard' }"
            >
                <a class="no-margin" href="#/organizer/dashboard">{{ 'Overview' | translate }}</a>
            </li>
            <li ng-click='changeOrganizerTab("organizer")' class="tab tab_0 dropdown" ng-class="{ selected: $root.activePage == 'Activity' }">
                <a class="no-margin" href="#/organizer" translate="My tasks"></a>
            </li>
            <li class="tab tab_1 dropdown" ng-class="{ selected: $root.activePage == 'Activity Calendar' }">
                <a href="#/organizer/calendar" translate="calendar"></a>
            </li>
        </ul>
        <ul class="nav col-lg-5 col-md-5 hidden-sm hidden-xs right-nav" style="justify-content: flex-end">
            <li
                ng-show="$root.me.recrutRole == 'admin' && $root.blockUser != true"
                class="dropdown invite-button"
                ng-class="{ 'ru-width': $root.currentLang !== 'en' }"
            >
                <button ng-click="inviteUserClientModal();" class="btn-animated invite_user">
                    <div>
                        <svg
                            style="fill: white"
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            version="1.1"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                        >
                            <path
                                d="M15,4C12.79,4 11,5.79 11,8C11,10.21 12.79,12 15,12C17.21,12 19,10.21 19,8C19,5.79 17.21,4 15,4M15,5.9C16.16,5.9 17.1,6.84 17.1,8C17.1,9.16 16.16,10.1 15,10.1C13.84,10.1 12.9,9.16 12.9,8C12.9,6.84 13.84,5.9 15,5.9M4,7V10H1V12H4V15H6V12H9V10H6V7H4M15,13C12.33,13 7,14.33 7,17V20H23V17C23,14.33 17.67,13 15,13M15,14.9C17.97,14.9 21.1,16.36 21.1,17V18.1H8.9V17C8.9,16.36 12,14.9 15,14.9Z"
                            />
                        </svg>
                    </div>
                    <span ng-if="$root.currentLang !== 'pl'" style="margin-left: 20px">
                        {{ 'Invite User / Client' | translate }}
                    </span>
                    <span ng-if="$root.currentLang === 'pl'" style="margin-left: 20px">Zaproś użytkownika</span>
                </button>
            </li>
            <li ng-if="$state.is('organizer')" class="scope" style="max-width: 270px" account-scope-panel></li>
        </ul>
    </div>
</div>
