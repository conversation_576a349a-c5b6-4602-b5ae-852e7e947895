<ul class="tabs tabs-history" style="background-color: transparent">
    <div>
        <li ng-click="vm.changeTab(true)" class="tabs__item" ng-class="{ 'tabs__item-active': vm.isPaymentHistoryTab }">
            <span>{{ 'Payment history' | translate }}</span>
        </li>
        <li ng-click="vm.changeTab(false)" class="tabs__item" ng-class="{ 'tabs__item-active': !vm.isPaymentHistoryTab }">
            <span>{{ 'Transitions history' | translate }}</span>
        </li>
    </div>
    <button ng-click="vm.updatePaymentsStatus()" class="btn_default btn_thin btn_success pay__saving-btn update-btn" style="min-width: 173px">
        {{ 'Update status' | translate }}
    </button>
</ul>

<div class="payment-history__text">
    <p ng-if="vm.isPaymentHistoryTab">{{ 'Payment history on your account.' | translate }}</p>
    <p ng-if="!vm.isPaymentHistoryTab">{{ 'History of daily charges to your account.' | translate }}</p>
    <p ng-if="!vm.histories || !vm.histories.length">{{ 'No payment stories yet' | translate }}</p>
</div>

<table
    ng-show="vm.histories && vm.histories.length"
    class="payment-history"
    style="border-right: 1px solid rgba(204, 204, 204, 0.3); border-left: 1px solid rgba(204, 204, 204, 0.3)"
>
    <thead style="border-top: 1px solid rgba(204, 204, 204, 0.3)">
        <tr class="payment-history__head">
            <td class="payment-history__td payment-history__td-head">{{ 'date' | translate }}</td>
            <td ng-if="vm.isPaymentHistoryTab" class="payment-history__td payment-history__td-head payment-history__td__payment-type">
                {{ 'Payment type' | translate }}
            </td>
            <td class="payment-history__td payment-history__td-head">{{ 'Payments' | translate }}</td>
            <td ng-if="vm.isPaymentHistoryTab" class="payment-history__td payment-history__td-head">
                {{ 'Status' | translate }}
            </td>
            <td class="payment-history__td payment-history__td-head">{{ 'Amount' | translate }}</td>
            <td ng-if="!vm.isPaymentHistoryTab" class="payment-history__td payment-history__td-head">
                {{ 'Balance' | translate }}
            </td>
        </tr>
    </thead>
    <tbody>
        <tr class="payment-history__tr" ng-class="{ isBlur: vm.isBlur }" ng-repeat="payment in vm.histories | orderBy:'-dc'">
            <td class="payment-history__td">{{ (payment.dc | date : 'dd.MM.yyyy').trim() }}</td>
            <td ng-show="vm.isPaymentHistoryTab" class="payment-history__td payment-history__td__payment-type">
                <div ng-if="payment.type === 'payment'" class="payment-by-card">
                    <span ng-if="payment.cardNumber">****{{ payment.cardNumber }}</span>
                </div>
                <div ng-if="payment.type === 'adjustment'" class="payment-by-invoice">Invoice</div>
            </td>
            <td class="payment-history__td">
                <span ng-if="payment.type !== 'expense'">{{ payment.descr | translate }}</span>
                <span ng-if="payment.type === 'expense'" translate-values="{users: payment.peopleCount}" translate="charge_for"></span>
            </td>
            <td
                ng-if="vm.isPaymentHistoryTab"
                class="payment-history__td payment-history__td__payment-status"
                ng-class="{
                    'success-payment': vm.checkForPaymentStatus(payment.status) === 'successful' || payment.type === 'adjustment',
                    'failed-payment': vm.checkForPaymentStatus(payment.status) === 'failed',
                    'pending-payment': vm.checkForPaymentStatus(payment.status) === 'pending',
                    isBlur: vm.isBlur
                }"
            >
                <div
                    ng-if="payment.type === 'payment' && (payment.status === 'Declined' || payment.status === 'Declined (Regular payment)')"
                    ng-switch="payment.reasonCode"
                >
                    <span style="margin-right: -2px">Declined (</span>
                    <span ng-switch-when="1101">{{ 'Declined  To Card Issuer' }}</span>
                    <span ng-switch-when="1102">{{ 'Bad CVV2' }}</span>
                    <span ng-switch-when="1103">{{ 'Expired card' }}</span>
                    <span ng-switch-when="1104">{{ 'Insufficient Funds' }}</span>
                    <span ng-switch-when="1105">{{ 'Invalid Card' }}</span>
                    <span ng-switch-when="1106">{{ 'Exceed Withdrawal Frequency' }}</span>
                    <span ng-switch-when="1108">{{ 'Three Ds Fail' }}</span>
                    <span ng-switch-when="1109">{{ 'Format Error' }}</span>
                    <span ng-switch-when="1110">{{ 'Invalid Currency' }}</span>
                    <span ng-switch-when="1112">{{ 'Duplicate Order ID' }}</span>
                    <span ng-switch-when="1113">{{ 'Invalid signature' }}</span>
                    <span ng-switch-when="1114">{{ 'Fraud' }}</span>
                    <span ng-switch-when="1115">{{ 'Parameter is missing' }}</span>
                    <span ng-switch-when="1116">{{ 'Token not found' }}</span>
                    <span ng-switch-when="1117">{{ 'API Not Allowed' }}</span>
                    <span ng-switch-when="1118">{{ 'Merchant Restriction' }}</span>
                    <span ng-switch-when="1120">{{ 'Authentication unavailable' }}</span>
                    <span ng-switch-when="1121">{{ 'Account not found' }}</span>
                    <span ng-switch-when="1122">{{ 'Gate Declined' }}</span>
                    <span ng-switch-when="1123">{{ 'Refund Not Allowed' }}</span>
                    <span ng-switch-when="1124">{{ 'Cardholder session expired' }}</span>
                    <span ng-switch-when="1125">{{ 'Cardholder canceled the request' }}</span>
                    <span ng-switch-when="1126">{{ 'Illegal Order State' }}</span>
                    <span ng-switch-when="1127">{{ 'Order Not Found' }}</span>
                    <span ng-switch-when="1128">{{ 'Refund Limit Excended' }}</span>
                    <span ng-switch-when="1129">{{ 'ScriptError' }}</span>
                    <span ng-switch-when="1130">{{ 'Invalid Amount' }}</span>
                    <span ng-switch-when="1131">{{ 'Transaction in processing' }}</span>
                    <span ng-switch-when="1132">{{ 'Transaction Is Delayed' }}</span>
                    <span ng-switch-when="1133">{{ 'Invalid commission' }}</span>
                    <span ng-switch-when="1134">{{ 'Transaction is pending' }}</span>
                    <span ng-switch-when="1135">{{ 'Card limits failed' }}</span>
                    <span ng-switch-when="1136">{{ 'Merchant Balance Is Very Small' }}</span>
                    <span ng-switch-when="1137">{{ 'Invalid Confirmation Amount' }}</span>
                    <span ng-switch-when="1138">{{ 'Refund in processing' }}</span>
                    <span ng-switch-when="1139">{{ 'External decline while credit' }}</span>
                    <span ng-switch-when="1140">{{ 'Exceed Withdrawal Frequency While Credit' }}</span>
                    <span ng-switch-when="1141">{{ 'Partial void is not supported' }}</span>
                    <span ng-switch-when="1142">{{ 'Refused a credit' }}</span>
                    <span ng-switch-when="1143">{{ 'Invalid phone number' }}</span>
                    <span ng-switch-when="1144">{{ 'Transaction is awaiting delivery' }}</span>
                    <span ng-switch-when="1145">{{ 'Transaction is awaiting credit decision' | translate }}</span>
                    <span ng-switch-when="5100">{{ 'Wait 3ds data' | translate }}</span>
                    <span ng-switch-default>{{ 'Declined' | translate }}</span>
                    <span style="margin-left: -2px">)</span>
                </div>
                <span ng-if="payment.type === 'adjustment'">{{ 'Approved' }}</span>
                <span ng-if="payment.status === 'Pending'">{{ 'Pending...' }}</span>
                <span
                    ng-if="
                        payment.type === 'payment' &&
                        payment.status !== 'Declined' &&
                        payment.status !== 'Declined (Regular payment)' &&
                        payment.status !== 'Pending'
                    "
                >
                    {{ payment.status }}
                </span>
                <img
                    ng-if="vm.checkForPaymentStatus(payment.status) === 'successful' || payment.type === 'adjustment' || payment.type === 'pay'"
                    style="margin-bottom: 3px; margin-left: 3px"
                    src="images/sprite/check-mark.png"
                    alt=""
                />
            </td>
            <td
                class="payment-history__td payment-history__td-amount"
                ng-class="{
                    'success-payment-amount':
                        vm.checkForPaymentStatus(payment.status) === 'successful' || payment.type === 'adjustment' || payment.type === 'pay',
                    'failed-payment-amount': vm.checkForPaymentStatus(payment.status) === 'failed',
                    'pending-payment-amount': vm.checkForPaymentStatus(payment.status) === 'pending',
                    isBlur: vm.isBlur
                }"
            >
                <span ng-if="vm.checkForPaymentStatus(payment.status) === 'successful' || payment.type === 'adjustment' || payment.type === 'pay'">+</span>
                <span ng-if="payment.type === 'expense'">-</span>
                {{ vm.isPaymentHistoryTab ? payment.amount : payment.amountDouble }} $
            </td>
            <td ng-if="!vm.isPaymentHistoryTab" class="payment-history__td">{{ payment.balanceDouble }} $</td>
        </tr>
    </tbody>
</table>

<div
    ng-if="vm.isPaymentHistoryTab"
    class="payment-history__show-all-button"
    ng-class="{ border_show: vm.histories.length > 5, 'payment-history__show-all-button__isBlur': vm.isBlur }"
>
    <span ng-show="vm.isHistoryHidden && vm.histories.length > 5" ng-click="vm.toggleShowAllHistory()" class="payment-history__show-all-button__btn">
        {{ 'Show all' | translate }}
    </span>
    <span ng-show="!vm.isHistoryHidden && vm.histories.length > 5" ng-click="vm.toggleShowAllHistory()" class="payment-history__show-all-button__btn">
        {{ 'Hide' | translate }}
    </span>
</div>

<div
    ng-if="!vm.isPaymentHistoryTab"
    class="payment-history__show-all-button"
    ng-class="{ border_show: vm.histories.length > 5, 'payment-history__show-all-button__isBlur': vm.isBlur }"
>
    <span ng-show="vm.isHistoryHidden && vm.histories.length > 5" ng-click="vm.toggleShowAllHistory()" class="payment-history__show-all-button__btn">
        {{ 'Show all' | translate }}
    </span>
    <span ng-show="!vm.isHistoryHidden && vm.histories.length > 5" ng-click="vm.toggleShowAllHistory()" class="payment-history__show-all-button__btn">
        {{ 'Hide' | translate }}
    </span>
</div>
