<div class="controller-view">
    <div class="block-company-statistics">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12 no-padding">
                    <div class="statistics-outer">
                        <div style="display: flex; align-items: center; justify-content: center; margin-top: 20px; margin-bottom: 20px; position: relative">
                            <page-intro-component style="position: absolute; left: 0; top: -12px"></page-intro-component>
                            <div class="col-lg-4 no-sides-padding centered" style="display: flex; align-items: center; justify-content: center; width: 100%">
                                <h4 class="main-page-title">{{ 'User statistics' | translate }}</h4>
                                <i
                                    class="tooltip-icon"
                                    style="margin-left: 5px"
                                    title=""
                                    tooltip-placement="bottom-left"
                                    tooltip-class="tooltip-outer"
                                    uib-tooltip-html="$root.tooltips.statisticTooltip"
                                    aria-hidden="true"
                                ></i>
                            </div>
                        </div>
                        <div class="heading-wrapper">
                            <div class="time-interval-wrapper">
                                <div class="row header" style="clear: both; display: flex; justify-content: flex-start">
                                    <div class="col-lg-8 text-right links" style="padding-left: 0px">
                                        <div class="fields dateRange" style="margin-bottom: 0px">
                                            <div class="fields-title" style="justify-content: flex-start; max-width: fit-content; margin-right: 30px">
                                                <h4 class="form-title" style="margin-right: 15px; margin-bottom: 0; width: max-content">
                                                    {{ 'Time range' | translate }}
                                                </h4>
                                                <div style="max-width: 210px; min-width: 210px; width: 100%; position: relative; margin-right: 10px">
                                                    <custom-select-time-interval
                                                        method="vm.selectDateRange"
                                                        model="vm.selectRangeModel"
                                                        options="vm.dateRange"
                                                    ></custom-select-time-interval>
                                                </div>
                                            </div>
                                            <div class="wrap-datepicker">
                                                <div
                                                    class="field left-date"
                                                    ng-class="{ disabled: vm.disabledTimeRange }"
                                                    style="display: flex; align-items: center; justify-content: center; max-width: 100%"
                                                >
                                                    <span style="position: relative; left: 6px; font-weight: 400; margin-right: 0">
                                                        {{ 'date from' | translate }}
                                                    </span>
                                                    <mdp-date-picker
                                                        ng-click="vm.activateTimeRange()"
                                                        mdp-disabled="vm.disabledTimeRange"
                                                        mdp-format="DD/MM/YYYY"
                                                        mdp-max-date="vm.newDate"
                                                        mdp-min-date="'2014-01-01'"
                                                        mdp-open-on-click
                                                        mdp-placeholder="{{ vm.startDate | date : 'dd/MM/yyyy' }}"
                                                        ng-change="vm.checkDateTime(vm.startDate, 'startDate')"
                                                        ng-model="vm.startDate"
                                                    ></mdp-date-picker>
                                                </div>
                                                <div
                                                    class="field right-date"
                                                    ng-class="{ disabled: vm.disabledTimeRange }"
                                                    style="margin-left: 40px; display: flex; align-items: center; justify-content: center; max-width: 100%"
                                                >
                                                    <span style="position: relative; left: 6px; font-weight: 400; margin-right: 0">
                                                        {{ 'to-2' | translate }}
                                                    </span>
                                                    <mdp-date-picker
                                                        ng-click="vm.activateTimeRange()"
                                                        mdp-disabled="vm.disabledTimeRange"
                                                        mdp-format="DD/MM/YYYY"
                                                        mdp-max-date="vm.newDate"
                                                        mdp-min-date="'2014-01-01'"
                                                        mdp-open-on-click
                                                        mdp-placeholder="{{ vm.endDate | date : 'dd/MM/yyyy' }}"
                                                        ng-change="vm.checkDateTime(vm.endDate, 'endDate')"
                                                        ng-model="vm.endDate"
                                                    ></mdp-date-picker>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="action report-excel"
                                    ng-hide="
                                        ($root['me']['recrutRole'] == 'recruter' &&
                                            ($root.me.personParams.enableDownloadToExcel == 'N' || !$root.me.personParams.enableDownloadToExcel)) ||
                                        ($root['me']['recrutRole'] == 'admin' && $root.me.personParams.enableDownloadToExcel == 'N')
                                    "
                                >
                                    <a id="performance-download" class="hidden"></a>
                                    <a ng-click="vm.downloadPerformanceExcel()">
                                        <img src="/images/sprite/excel-history.svg" alt="" />
                                        <span translate="Export to Excel_2"></span>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="statistics-wrapper">
                            <table
                                class="table"
                                style="
                                    border-bottom-left-radius: 7px;
                                    border-bottom-right-radius: 7px;
                                    border-top-right-radius: 7px;
                                    border-top-left-radius: 7px;
                                "
                                ng-table="vm.tableParams"
                            >
                                <thead style="position: sticky; top: 0; z-index: 1">
                                    <tr>
                                        <th style="border-radius: 7px 0 0 0">
                                            <table width="100%">
                                                <tr class="inner-table-row">
                                                    <td class="inner-table-data">№</td>
                                                </tr>
                                            </table>
                                        </th>
                                        <th ng-click="vm.tableParams.sorting({'lastName': vm.tableParams.isSortBy('lastName', 'asc') ? 'desc' : 'asc'})">
                                            <table width="100%">
                                                <tr class="inner-table-row">
                                                    <td class="inner-table-data">
                                                        {{ 'user' | translate }}

                                                        <span
                                                            ng-class="vm.tableParams.isSortBy('lastName', 'asc') ? 'activeSort' : ''"
                                                            ng-hide="vm.tableParams.isSortBy('lastName', 'desc')"
                                                        >
                                                            <svg fill="#999999;" height="10" viewBox="0 0 8 10" width="8" xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M2.77613 0.994481V5.55915H0.498766C0.05125 5.55915 -0.167536 6.09617 0.150698 6.40446L3.42254 9.6763C3.62144 9.87519 3.92973 9.87519 4.12862 9.6763L7.40046 6.40446C7.70875 6.09617 7.48997 5.55915 7.0524 5.55915H4.76509V0.994481C4.76509 0.447516 4.31757 0 3.77061 0C3.22364 0 2.77613 0.447516 2.77613 0.994481Z"
                                                                    fill="#999999"
                                                                />
                                                            </svg>
                                                        </span>
                                                        <span ng-if="vm.tableParams.isSortBy('lastName', 'desc')">
                                                            <svg fill="#34d434;" height="10" viewBox="0 0 8 10" width="8" xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M4.76849 8.83099L4.76849 4.26632H7.04585C7.49337 4.26632 7.71215 3.7293 7.39392 3.42101L4.12208 0.149172C3.92318 -0.0497246 3.61489 -0.0497246 3.416 0.149172L0.144153 3.42101C-0.164136 3.7293 0.0546498 4.26632 0.492221 4.26632H2.77953L2.77953 8.83099C2.77953 9.37795 3.22704 9.82547 3.77401 9.82547C4.32097 9.82547 4.76849 9.37795 4.76849 8.83099Z"
                                                                    fill="#40a349"
                                                                />
                                                            </svg>
                                                        </span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </th>
                                        <th
                                            ng-click="vm.tableParams.sorting({'candidateCreatorStat.count': vm.tableParams.isSortBy('candidateCreatorStat.count', 'asc') ? 'desc' : 'asc'})"
                                        >
                                            <table width="100%">
                                                <tr class="inner-table-row">
                                                    <td class="inner-table-data">
                                                        {{ 'candidate_added' | translate }}

                                                        <span
                                                            ng-class="vm.tableParams.isSortBy('candidateCreatorStat.count', 'asc') ? 'activeSort' : ''"
                                                            ng-hide="vm.tableParams.isSortBy('candidateCreatorStat.count', 'desc')"
                                                        >
                                                            <svg fill="#999999;" height="10" viewBox="0 0 8 10" width="8" xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M2.77613 0.994481V5.55915H0.498766C0.05125 5.55915 -0.167536 6.09617 0.150698 6.40446L3.42254 9.6763C3.62144 9.87519 3.92973 9.87519 4.12862 9.6763L7.40046 6.40446C7.70875 6.09617 7.48997 5.55915 7.0524 5.55915H4.76509V0.994481C4.76509 0.447516 4.31757 0 3.77061 0C3.22364 0 2.77613 0.447516 2.77613 0.994481Z"
                                                                    fill="#999999"
                                                                />
                                                            </svg>
                                                        </span>
                                                        <span ng-if="vm.tableParams.isSortBy('candidateCreatorStat.count', 'desc')">
                                                            <svg fill="#34d434;" height="10" viewBox="0 0 8 10" width="8" xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M4.76849 8.83099L4.76849 4.26632H7.04585C7.49337 4.26632 7.71215 3.7293 7.39392 3.42101L4.12208 0.149172C3.92318 -0.0497246 3.61489 -0.0497246 3.416 0.149172L0.144153 3.42101C-0.164136 3.7293 0.0546498 4.26632 0.492221 4.26632H2.77953L2.77953 8.83099C2.77953 9.37795 3.22704 9.82547 3.77401 9.82547C4.32097 9.82547 4.76849 9.37795 4.76849 8.83099Z"
                                                                    fill="#40a349"
                                                                />
                                                            </svg>
                                                        </span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </th>
                                        <th
                                            ng-click="vm.tableParams.sorting({'vacancyCreatedInterview.count': vm.tableParams.isSortBy('vacancyCreatedInterview.count', 'asc') ? 'desc' : 'asc'})"
                                        >
                                            <table width="100%">
                                                <tr class="inner-table-row">
                                                    <td class="inner-table-data">
                                                        {{ 'Interviews_title' | translate }}

                                                        <span
                                                            ng-class="vm.tableParams.isSortBy('vacancyCreatedInterview.count', 'asc') ? 'activeSort' : ''"
                                                            ng-hide="vm.tableParams.isSortBy('vacancyCreatedInterview.count', 'desc')"
                                                        >
                                                            <svg fill="#999999;" height="10" viewBox="0 0 8 10" width="8" xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M2.77613 0.994481V5.55915H0.498766C0.05125 5.55915 -0.167536 6.09617 0.150698 6.40446L3.42254 9.6763C3.62144 9.87519 3.92973 9.87519 4.12862 9.6763L7.40046 6.40446C7.70875 6.09617 7.48997 5.55915 7.0524 5.55915H4.76509V0.994481C4.76509 0.447516 4.31757 0 3.77061 0C3.22364 0 2.77613 0.447516 2.77613 0.994481Z"
                                                                    fill="#999999"
                                                                />
                                                            </svg>
                                                        </span>
                                                        <span ng-if="vm.tableParams.isSortBy('vacancyCreatedInterview.count', 'desc')">
                                                            <svg fill="#34d434;" height="10" viewBox="0 0 8 10" width="8" xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M4.76849 8.83099L4.76849 4.26632H7.04585C7.49337 4.26632 7.71215 3.7293 7.39392 3.42101L4.12208 0.149172C3.92318 -0.0497246 3.61489 -0.0497246 3.416 0.149172L0.144153 3.42101C-0.164136 3.7293 0.0546498 4.26632 0.492221 4.26632H2.77953L2.77953 8.83099C2.77953 9.37795 3.22704 9.82547 3.77401 9.82547C4.32097 9.82547 4.76849 9.37795 4.76849 8.83099Z"
                                                                    fill="#40a349"
                                                                />
                                                            </svg>
                                                        </span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </th>
                                        <th
                                            ng-click="vm.tableParams.sorting({'vacancyCreatorStat.count': vm.tableParams.isSortBy('vacancyCreatorStat.count', 'asc') ? 'desc' : 'asc'})"
                                            style="width: 32%"
                                        >
                                            <table width="100%">
                                                <tr class="inner-table-row">
                                                    <td class="inner-table-data" colspan="3">
                                                        <div class="margin" style="display: block">
                                                            {{ 'vacancies' | translate }}

                                                            <span
                                                                ng-class="vm.tableParams.isSortBy('vacancyCreatorStat.count', 'asc') ? 'activeSort' : ''"
                                                                ng-hide="vm.tableParams.isSortBy('vacancyCreatorStat.count', 'desc')"
                                                            >
                                                                <svg
                                                                    fill="#999999;"
                                                                    height="10"
                                                                    viewBox="0 0 8 10"
                                                                    width="8"
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                >
                                                                    <path
                                                                        d="M2.77613 0.994481V5.55915H0.498766C0.05125 5.55915 -0.167536 6.09617 0.150698 6.40446L3.42254 9.6763C3.62144 9.87519 3.92973 9.87519 4.12862 9.6763L7.40046 6.40446C7.70875 6.09617 7.48997 5.55915 7.0524 5.55915H4.76509V0.994481C4.76509 0.447516 4.31757 0 3.77061 0C3.22364 0 2.77613 0.447516 2.77613 0.994481Z"
                                                                        fill="#999999"
                                                                    />
                                                                </svg>
                                                            </span>
                                                            <span ng-if="vm.tableParams.isSortBy('vacancyCreatorStat.count', 'desc')">
                                                                <svg
                                                                    fill="#34d434;"
                                                                    height="10"
                                                                    viewBox="0 0 8 10"
                                                                    width="8"
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                >
                                                                    <path
                                                                        d="M4.76849 8.83099L4.76849 4.26632H7.04585C7.49337 4.26632 7.71215 3.7293 7.39392 3.42101L4.12208 0.149172C3.92318 -0.0497246 3.61489 -0.0497246 3.416 0.149172L0.144153 3.42101C-0.164136 3.7293 0.0546498 4.26632 0.492221 4.26632H2.77953L2.77953 8.83099C2.77953 9.37795 3.22704 9.82547 3.77401 9.82547C4.32097 9.82547 4.76849 9.37795 4.76849 8.83099Z"
                                                                        fill="#40a349"
                                                                    />
                                                                </svg>
                                                            </span>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr class="inner-table-row">
                                                    <td class="inner-table-data" width="33%">
                                                        <span>{{ 'history_info.Added1' | translate }}</span>
                                                    </td>
                                                    <td class="inner-table-data" width="33%">
                                                        <span>{{ 'In work' | translate }}</span>
                                                    </td>
                                                    <td class="inner-table-data" width="33%">
                                                        {{ 'Closed' | translate }}
                                                    </td>
                                                </tr>
                                            </table>
                                        </th>
                                        <th
                                            ng-click="vm.tableParams.sorting({'vacancyResponsibleCompletedTimeStat.count': vm.tableParams.isSortBy('vacancyResponsibleCompletedTimeStat.count', 'asc') ? 'desc' : 'asc'})"
                                            style="border-radius: 0 7px 0 0"
                                        >
                                            <table width="100%">
                                                <tr class="inner-table-row">
                                                    <td class="inner-table-data">
                                                        {{ 'vacancies_filling_time' | translate }}

                                                        <span
                                                            ng-class="
                                                                vm.tableParams.isSortBy('vacancyResponsibleCompletedTimeStat.count', 'asc') ? 'activeSort' : ''
                                                            "
                                                            ng-hide="vm.tableParams.isSortBy('vacancyResponsibleCompletedTimeStat.count', 'desc')"
                                                        >
                                                            <svg fill="#999999;" height="10" viewBox="0 0 8 10" width="8" xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M2.77613 0.994481V5.55915H0.498766C0.05125 5.55915 -0.167536 6.09617 0.150698 6.40446L3.42254 9.6763C3.62144 9.87519 3.92973 9.87519 4.12862 9.6763L7.40046 6.40446C7.70875 6.09617 7.48997 5.55915 7.0524 5.55915H4.76509V0.994481C4.76509 0.447516 4.31757 0 3.77061 0C3.22364 0 2.77613 0.447516 2.77613 0.994481Z"
                                                                    fill="#999999"
                                                                />
                                                            </svg>
                                                        </span>
                                                        <span ng-if="vm.tableParams.isSortBy('vacancyResponsibleCompletedTimeStat.count', 'desc')">
                                                            <svg fill="#34d434;" height="10" viewBox="0 0 8 10" width="8" xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M4.76849 8.83099L4.76849 4.26632H7.04585C7.49337 4.26632 7.71215 3.7293 7.39392 3.42101L4.12208 0.149172C3.92318 -0.0497246 3.61489 -0.0497246 3.416 0.149172L0.144153 3.42101C-0.164136 3.7293 0.0546498 4.26632 0.492221 4.26632H2.77953L2.77953 8.83099C2.77953 9.37795 3.22704 9.82547 3.77401 9.82547C4.32097 9.82547 4.76849 9.37795 4.76849 8.83099Z"
                                                                    fill="#40a349"
                                                                />
                                                            </svg>
                                                        </span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </th>
                                    </tr>
                                    <tr class="total-row-wrapper">
                                        <td class="total-data">
                                            <strong>{{ 'total' | translate }}</strong>
                                        </td>
                                        <td class="total-data"></td>
                                        <td class="total-data">
                                            <strong>{{ vm.candidateCreatorStatCount }}</strong>
                                        </td>
                                        <td class="total-data">
                                            <strong>{{ vm.vacancyCreatedInterviewCount }}</strong>
                                        </td>
                                        <td class="total-data">
                                            <table width="100%">
                                                <tr class="inner-table-row">
                                                    <td class="inner-table-data" width="33%">
                                                        <strong>{{ vm.vacancyCreatorStatCount }}</strong>
                                                    </td>
                                                    <td class="inner-table-data" width="33%">
                                                        <strong>{{ vm.vacancyResponsibleInWorkStatCount }}</strong>
                                                    </td>
                                                    <td class="inner-table-data" width="33%">
                                                        <strong>{{ vm.vacancyResponsibleCompletedStatCount }}</strong>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td class="total-data">
                                            <span ng-if="vm.vacancyResponsibleCompletedTimeStatMeanValue">
                                                {{ vm.vacancyResponsibleCompletedTimeStatMeanValue | dayFormat | translate }}
                                            </span>
                                            <span ng-if="!vm.vacancyResponsibleCompletedTimeStatMeanValue">&#8212;</span>
                                        </td>
                                    </tr>
                                    <tr></tr>
                                </thead>
                                <tbody>
                                    <tr class="table-space"></tr>
                                    <tr ng-repeat-start="person in $data">
                                        <td class="table-page">
                                            {{ (vm.tableParams.page() - 1) * vm.tableParams.count() + $index + 1 }}
                                        </td>
                                        <td class="name" sortable="firstName">
                                            <span class="responsible-person-wrapper">
                                                <responsible-person
                                                    ng-if="!$root.useAmericanNameStyle"
                                                    avatar-id="person.avatarId"
                                                    full-name="person.fullName"
                                                    user-id="person.userId"
                                                ></responsible-person>
                                                <responsible-person
                                                    ng-if="$root.useAmericanNameStyle"
                                                    avatar-id="person.avatarId"
                                                    full-name="person.fullNameEn"
                                                    user-id="person.userId"
                                                ></responsible-person>
                                                <a ng-if="!$root.useAmericanNameStyle" href="#/users/{{ person.userId }}">
                                                    {{ person.fullName }}
                                                </a>
                                                <a ng-if="$root.useAmericanNameStyle" href="#/users/{{ person.userId }}">
                                                    {{ person.fullNameEn }}
                                                </a>
                                            </span>
                                        </td>
                                        <td data-title="'Candidate added'" sortable="candidateCreatorStat.count">
                                            <strong ng-if="vm.candWithoutContacts != 'loaded'">
                                                {{ person.candidateCreatorStat.count }}
                                            </strong>
                                            <span ng-if="vm.candWithoutContacts != 'loaded'" class="small-font">
                                                {{ person.candidateCreatorPercent }}
                                            </span>
                                            <strong ng-if="person.addedCandidatesWithoutNameOrContacts && vm.candWithoutContacts == 'loaded'" class="">
                                                {{ person.addedCandidatesWithoutNameOrContacts }}
                                            </strong>
                                            <span
                                                ng-if="person.percentOfAddedCandidatesWithoutNameOrContacts && vm.candWithoutContacts == 'loaded'"
                                                class="small-font"
                                            >
                                                {{ person.percentOfAddedCandidatesWithoutNameOrContacts }}
                                            </span>
                                            <strong ng-if="!person.addedCandidatesWithoutNameOrContacts && vm.candWithoutContacts == 'loaded'" class="">
                                                0
                                            </strong>
                                            <span
                                                ng-if="!person.percentOfAddedCandidatesWithoutNameOrContacts && vm.candWithoutContacts == 'loaded'"
                                                class="small-font"
                                            >
                                                0%
                                            </span>
                                        </td>
                                        <td>
                                            <strong>{{ person.vacancyCreatedInterview.count }}</strong>
                                            <span class="small-font">{{ person.vacancyCreatedInterviewPercent }}</span>
                                        </td>
                                        <td>
                                            <table width="100%">
                                                <tr class="inner-table-row">
                                                    <td class="inner-table-data" width="33%">
                                                        <strong>{{ person.vacancyCreatorStat.count }}</strong>
                                                        <span class="small-font">
                                                            {{ person.vacancyCreatorStatPercent }}
                                                        </span>
                                                    </td>
                                                    <td class="inner-table-data" width="33%">
                                                        <strong ng-if="person.vacancyResponsibleInWorkStat" class="second-value">
                                                            {{ person.vacancyResponsibleInWorkStat }}
                                                        </strong>
                                                        <strong ng-if="!person.vacancyResponsibleInWorkStat" class="second-value">0</strong>
                                                    </td>
                                                    <td class="inner-table-data" width="33%">
                                                        <strong>
                                                            {{ person.vacancyResponsibleCompletedStat.count }}
                                                        </strong>
                                                        <span class="small-font">
                                                            {{ person.vacancyResponsibleCompletedStatPercent }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td sortable="vacancyResponsibleCompletedTimeStat.count">
                                            <span ng-if="person.vacancyResponsibleCompletedTimeStat.count">
                                                {{ person.vacancyResponsibleCompletedTimeStat.count | dayFormat | translate }}
                                            </span>
                                            <span ng-if="!person.vacancyResponsibleCompletedTimeStat.count">&#8212;</span>
                                        </td>
                                    </tr>
                                    <tr class="table-space" ng-hide="$last" ng-repeat-end=""></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
