<div class="block-vacancies">
    <div id="vacancies-list-wrapper">
        <vacancies-search-component class="vacancies-search-component-wrapper" table-params="tableParams"></vacancies-search-component>

        <div
            ng-if="countVacancyEmpty === true"
            class="col-lg-12 empty-search"
            ng-class="{ displayNone: !countVacancyEmpty }"
            style="padding-bottom: 20px"
            advanced-search-directive
            target="vacancies-search"
        >
            <div class="count_empty" style="margin-bottom: 0">
                <p>{{ 'solrExceptionVacancy' | translate }}</p>
            </div>
        </div>
        <div
            id="vacanciesEmptyBlock"
            ng-show="flagForEmptyBlock && vacanciesSize === 0"
            class="col-lg-12 empty-search"
            advanced-search-directive
            target="clients-search"
        >
            <span class="count_empty">
                {{ 'found' | translate }}:
                <span class="bold">{{ vacanciesSize }} {{ 'vacancies_onSearch' | translate }}</span>
            </span>
            <!-- prettier-ignore -->
            <span class="action">
                <span translate="Empty_search.Use"></span>
                <span ng-click="emitEventAdvancedSearch()" class="use-advanced-search" translate="Empty_search.the advanced search"></span><span translate="Empty_search.,using more criteria to obtain the necessary data."></span>
            </span>
        </div>

        <vacancies-cards-list-component
            ng-if="vacanciesPageView === 'list' && vacanciesSize > 0"
            class="vacancies-cards-list-component"
            vacancies-list="vacancies"
            on-change-status="(showChangeStatusOfVacancy)"
            current-lang="$root.currentLang"
            all-languages="$root.allLanguages"
        ></vacancies-cards-list-component>

        <div
            ng-show="isVacancies && vacanciesSize > 0"
            class="vacancies-search-result vacancy-table vacancy-table-redesign"
            ng-style="vacanciesPageView === 'table' ? { display: 'initial' } : { display: 'none' }"
        >
            <div class="row">
                <div class="col-lg-12">
                    <table id="mainTable" ng-show="vacanciesFound" class="table" ng-table="tableParams" template-pagination="custom/pager">
                        <thead>
                            <tr>
                                <th style="white-space: nowrap">
                                    {{ 'vacancy' | translate }}
                                    <span
                                        ng-class="$root.vacancySortType.name === 'alphabetically' && $root.vacancySortType.sort === 'asc' ? 'activeSort' : ''"
                                        ng-hide="$root.vacancySortType.name === 'alphabetically' && $root.vacancySortType.sort === 'desc'"
                                    ></span>
                                    <span ng-if="$root.vacancySortType.name === 'alphabetically' && $root.vacancySortType.sort === 'desc'"></span>
                                </th>
                                <th class="priority">
                                    {{ 'Priority' | translate }}
                                    <span
                                        ng-class="$root.vacancySortType.name === 'priority' && $root.vacancySortType.sort === 'asc' ? 'activeSort' : ''"
                                        ng-hide="$root.vacancySortType.name === 'priority' && $root.vacancySortType.sort === 'desc'"
                                    ></span>
                                    <span ng-if="$root.vacancySortType.name === 'priority' && $root.vacancySortType.sort === 'desc'"></span>
                                </th>
                                <th class="location">
                                    {{ 'Location_2' | translate }}
                                    <span
                                        ng-class="$root.vacancySortType.name === 'city' && $root.vacancySortType.sort === 'asc' ? 'activeSort' : ''"
                                        ng-hide="$root.vacancySortType.name === 'city' && $root.vacancySortType.sort === 'desc'"
                                    ></span>
                                    <span ng-if="$root.vacancySortType.name === 'city' && $root.vacancySortType.sort === 'desc'"></span>
                                </th>
                                <th class="openingDate">
                                    {{ 'opening_date' | translate }}

                                    <span
                                        ng-class="
                                            $root.vacancySortType.name === 'dc' && $root.vacancySortType.sort === 'desc'
                                                ? 'activeSort openDateArrow'
                                                : 'openDateArrow'
                                        "
                                        ng-hide="$root.vacancySortType.name === 'dc' && $root.vacancySortType.sort === 'asc'"
                                    ></span>
                                    <span ng-if="$root.vacancySortType.name === 'dc' && $root.vacancySortType.sort === 'asc'" class="openDateArrow"></span>
                                </th>
                                <th class="dateFinish">
                                    {{ 'deadline_table' | translate }}
                                    <span
                                        ng-class="$root.vacancySortType.name === 'dateFinish' && $root.vacancySortType.sort === 'asc' ? 'activeSort' : ''"
                                        ng-hide="$root.vacancySortType.name === 'dateFinish' && $root.vacancySortType.sort === 'desc'"
                                    ></span>
                                    <span ng-if="$root.vacancySortType.name === 'dateFinish' && $root.vacancySortType.sort === 'desc'"></span>
                                </th>
                                <th class="responsibled">
                                    {{ 'responsible' | translate }}
                                </th>
                                <th class="responsibled">
                                    {{ 'Skills' | translate }}
                                </th>
                                <th class="clientName">
                                    {{ 'client' | translate }}
                                    <span
                                        ng-class="$root.vacancySortType.name === 'clientName' && $root.vacancySortType.sort === 'asc' ? 'activeSort' : ''"
                                        ng-hide="$root.vacancySortType.name === 'clientName' && $root.vacancySortType.sort === 'desc'"
                                    ></span>
                                    <span ng-if="$root.vacancySortType.name === 'clientName' && $root.vacancySortType.sort === 'desc'"></span>
                                </th>
                                <th class="status">
                                    {{ 'status' | translate }}
                                    <span
                                        ng-class="$root.vacancySortType.name === 'status' && $root.vacancySortType.sort === 'asc' ? 'activeSort' : ''"
                                        ng-hide="$root.vacancySortType.name === 'status' && $root.vacancySortType.sort === 'desc'"
                                    ></span>
                                    <span ng-if="$root.vacancySortType.name === 'status' && $root.vacancySortType.sort === 'desc'"></span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                hglocation-of-one-object="'/vacancies/'"
                                hgobject="vacancy"
                                hgtablecollspan="7"
                                highlights
                                ng-repeat="vacancy in vacancies track by vacancy.localId"
                                ng-init="isVacancyLastInList = $last"
                                tofullinformation="vacancies/{{ vacancy.localId }}"
                            >
                                <td ng-click="redirectToVacancy(vacancy.localId)" style="word-break: break-word" data-title="'Vacancy'" sortable="position">
                                    <div class="position-relative" style="display: flex; align-items: center">
                                        <a
                                            class="vacancy-name-link"
                                            href="#/vacancies/{{ vacancy.localId }}"
                                            ng-bind="vacancy.position |limitToEllipse:97"
                                            uib-tooltip="{{ vacancy.position }}"
                                            tooltip-append-to-body="true"
                                            tooltip-placement="bottom-left"
                                            tooltip-class="tooltip-info-black"
                                        ></a>
                                    </div>
                                </td>
                                <td class="priority" data-title="'Priority'">
                                    <vacancy-priority current-lang="$root.currentLang" priority="vacancy.priority"></vacancy-priority>
                                </td>
                                <td ng-click="redirectToVacancy(vacancy.localId); $event.stopPropagation();" class="clickable">
                                    <div style="display: flex; align-items: center; flex-wrap: wrap">
                                        <span>
                                            <span ng-if="vacancy.employmentType == 'remote' || vacancy.employmentType === 'telework'">
                                                {{ 'telework_1' | translate }}
                                            </span>
                                            <span ng-if="vacancy.region && (vacancy.employmentType !== 'remote' || vacancy.employmentType == 'telework')">
                                                {{ vacancy.region.googlePlaceId | translateCity }}
                                            </span>
                                        </span>

                                        <span
                                            ng-if="vacancy.employmentType === 'remote' && (vacancy.region.displayCity || vacancy.region.displayCountry)"
                                            style="margin-right: 4px"
                                        >
                                            ,
                                        </span>
                                        <span
                                            ng-if="vacancy.employmentType === 'remote' && !vacancy.region.displayCity && !vacancy.region.displayCountry"
                                            ng-bind="vacancy.regionShort"
                                        ></span>
                                        <span ng-if="vacancy.employmentType === 'remote' && vacancy.region.displayCity">
                                            {{ vacancy.region.googlePlaceId | translateCity }}
                                        </span>
                                        <span ng-if="vacancy.employmentType === 'remote' && vacancy.region.displayCountry && !vacancy.region.displayCity">
                                            {{ vacancy.region.googlePlaceId | translateCountry }}
                                        </span>
                                    </div>
                                </td>
                                <td ng-click="redirectToVacancy(vacancy.localId)" data-title="'Finish date'">
                                    <div>
                                        <span>{{ vacancy.dc | dateFormatShort | translate }}</span>
                                        <span title="{{ 'Days in work' | translate }}">({{ vacancy.daysInWork }})</span>
                                    </div>
                                </td>
                                <td ng-click="redirectToVacancy(vacancy.localId)" data-title="'Finish date'" sortable="dateFinish">
                                    <div
                                        ng-if="vacancy.status != 'completed' || vacancy.status == 'completed'"
                                        ng-style="$root.dateFuture === false ? { color: '#d67354' } : { color: '#202021' }"
                                    >
                                        {{ $root.visibilityDateFinishVacancy(vacancy.dateFinish) }}
                                    </div>
                                </td>
                                <td>
                                    <div class="responsibles">
                                        <person-avatar-component
                                            ng-if="vacancy.responsiblesPerson.length <= 2"
                                            ng-repeat="responsible in vacancy.responsiblesPerson track by $index"
                                            full-name="$root.useAmericanNameStyle ? responsible.responsible.fullNameEn : responsible.responsible.fullName"
                                            user-id="responsible.responsible.userId"
                                            avatar-id="responsible.responsible.avatarId"
                                        ></person-avatar-component>

                                        <div ng-if="vacancy.responsiblesPerson.length > 2" class="responsibles">
                                            <person-avatar-component
                                                ng-repeat="responsible in vacancy.responsiblesPerson.slice(0, 2) track by $index"
                                                full-name="$root.useAmericanNameStyle ? responsible.responsible.fullNameEn : responsible.responsible.fullName"
                                                user-id="responsible.responsible.userId"
                                                avatar-id="responsible.responsible.avatarId"
                                            ></person-avatar-component>
                                            <div
                                                class="rest"
                                                ng-init="popoverOpened=false"
                                                ng-mouseleave="popoverOpened=false"
                                                ng-mouseenter="popoverOpened=true"
                                            >
                                                <div
                                                    popover-class="multiple-resp-popover"
                                                    popover-is-open="popoverOpened"
                                                    popover-placement="{{ isVacancyLastInList ? 'top' : 'bottom' }}"
                                                    popover-trigger="none"
                                                    uib-popover-template="'partials/vacancy/popover-responsibles.html'"
                                                >
                                                    +{{ vacancy.responsiblesPerson.length - 2 }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="skills-list">
                                        <div class="skills-list_item" ng-repeat="skill in vacancy.skills.slice(0,2)">
                                            {{ skill.skillName }}
                                        </div>
                                        <div
                                            ng-if="vacancy.skills.length > 2"
                                            class="skills-list_item skills-list__rest-skills-button"
                                            ng-init="popoverOpened=false"
                                            ng-mouseleave="popoverOpened=false"
                                            ng-mouseenter="popoverOpened=true"
                                        >
                                            <div
                                                popover-class="multiple-resp-popover"
                                                popover-is-open="popoverOpened"
                                                popover-placement="auto"
                                                popover-trigger="none"
                                                uib-popover-template="'partials/vacancy/popover-skills.html'"
                                            >
                                                +{{ vacancy.skills.length - 2 }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td ng-click="redirectToVacancy(vacancy.localId)" class="client-name" data-title="'Client'" sortable="clientId.name">
                                    <div class="wrapper flex-wrap flex-space-between flex-align-center">
                                        <div class="client-name__location flex-align-center" style="flex-wrap: wrap">
                                            <a
                                                ng-if="$root.me.personParams.clientAccessLevel !== 'hide'"
                                                ng-click="redirectToClient(vacancy.clientId.localId); $event.stopPropagation();"
                                                class="link-style client-link"
                                                style="display: inline-block"
                                                href="#/clients/{{ vacancy.clientId.localId }}"
                                                title="{{ vacancy.clientId.name }}"
                                            >
                                                {{ vacancy.clientId.name }}
                                            </a>
                                            <span ng-if="$root.me.personParams.clientAccessLevel === 'hide'" style="display: inline-block">
                                                {{ vacancy.clientId.name }}
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td class="status" ng-class="'status_' + $root.currentLang + '-width'">
                                    <vacancy-status-dropdown-for-table
                                        current-lang="$root.currentLang"
                                        vacancy="vacancy"
                                        index="$index"
                                        value="{ name: vacancy.status, value: vacancy.status }"
                                        on-change="(showChangeStatusOfVacancy)"
                                        position="vacancies.length > 5 ? 'absolute' : 'fixed'"
                                    ></vacancy-status-dropdown-for-table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <pagination-component
            ng-if="$root.objectSize > 15"
            class="vacancies-pagination"
            translate-func="$root.translate"
            total-pages="totalPagesCount"
            total-elements="$root.objectSize"
            current-page="a.searchNumber"
            current-amount-of-elements="tableParams.count()"
            on-change-page="(changePage)"
            on-change-amount-of-elements="(changeAmountOfElements)"
            on-show-more="(showMore)"
            is-show-more-mode="isShowMore"
        ></pagination-component>
    </div>
</div>
