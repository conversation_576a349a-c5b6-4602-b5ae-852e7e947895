<div ng-if="$rootScope.me.recrutRole != 'client'" ng-click="$root.onOpenInstructionPopup()" class="link-style how-achievements-work">
    {{ 'How do achievements work?' | translate }}
</div>

<div ng-if="$root.me.orgParams.gamificationEnabled == 'true' || $root.me.orgParams.gamificationEnabled === undefined" class="awards">
    <div class="awards-simple">
        <div
            class="awards-item-wrapper"
            ng-class="{ thirdLineAward: award.originalName === 'sendMailsViaPersonalMailing' }"
            ng-repeat="award in vm.awards"
            name="{{ award.originalName }}"
        >
            <div ng-if="award.level !== 0" class="awards-item">
                <div class="awards-item-orange flex-justify-align-center">
                    <div ng-if="award.level !== 5" class="awards-item-orange-counter">
                        <span ng-if="award.level === 0" class="gamification-big-orange-title orange-title-small">10</span>
                        <span ng-if="award.level === 1" class="gamification-big-orange-title orange-title-small">20</span>
                        <span ng-if="award.level === 2" class="gamification-big-orange-title orange-title-small">30</span>
                        <span ng-if="award.level === 3" class="gamification-big-orange-title orange-title-small">40</span>
                        <span ng-if="award.level === 4" class="gamification-big-orange-title orange-title-small">50</span>
                        <img class="awards-item-orange-counter-image" src="images/sprite/achievements/crystals/color-stone.svg" alt="" />
                    </div>
                    <div ng-if="award.level === 5" class="awards-item-orange flex-justify-align-center">
                        <img class="orange-check-mark-img" src="images/sprite/achievements/check-mark.svg" alt="" />
                    </div>
                </div>
                <div class="awards-item-blue">
                    <div
                        class="gamification-blue-title"
                        ng-style="award.originalName === 'useCleverStaff' && $root.currentLang === 'pl' ? { 'font-size': '15px' } : { 'font-size': '16px' }"
                        ng-bind-html="award.name | translate"
                    ></div>
                    <span
                        class="gamification-blue-title-secondary awards-item-blue-title"
                        ng-style="award.name === 'achieve_title.sendMailsViaPersonalMailing' ? { 'margin-top': '10px' } : { 'margin-top': '20px' }"
                        translate="{{ award.description }}"
                        translate-values="{count:award.count}"
                    ></span>
                    <div class="awards-item-blue-counter flex-justify-align-center">
                        <span class="awards-item-blue-counter-count">{{ award.level }}/5</span>
                    </div>
                </div>
                <img ng-if="award.level === 0" class="awards-item-level-icon" src="images/sprite/achievements/level-0.svg" alt="" />
                <img ng-if="award.level === 1" class="awards-item-level-icon" src="images/sprite/achievements/level-1.svg" alt="" />
                <img ng-if="award.level === 2" class="awards-item-level-icon" src="images/sprite/achievements/level-2.svg" alt="" />
                <img ng-if="award.level === 3" class="awards-item-level-icon" src="images/sprite/achievements/level-3.svg" alt="" />
                <img ng-if="award.level === 4" class="awards-item-level-icon" src="images/sprite/achievements/level-4.svg" alt="" />
                <img ng-if="award.level === 5" class="awards-item-level-icon" src="images/sprite/achievements/level-5.svg" alt="" />
            </div>
            <div ng-if="award.level === 0" class="awards-item" style="opacity: 0.3">
                <div class="awards-item-orange flex-justify-align-center">
                    <div ng-if="award.level !== 5" class="awards-item-orange-counter">
                        <span ng-if="award.level === 0" class="gamification-big-orange-title orange-title-small">10</span>
                        <span ng-if="award.level === 1" class="gamification-big-orange-title orange-title-small">20</span>
                        <span ng-if="award.level === 2" class="gamification-big-orange-title orange-title-small">30</span>
                        <span ng-if="award.level === 3" class="gamification-big-orange-title orange-title-small">40</span>
                        <span ng-if="award.level === 4" class="gamification-big-orange-title orange-title-small">50</span>
                        <img class="awards-item-orange-counter-image" src="images/sprite/achievements/crystals/color-stone.svg" alt="" />
                    </div>
                    <div ng-if="award.level === 5" class="awards-item-orange flex-justify-align-center">
                        <img class="orange-check-mark-img" src="images/sprite/achievements/check-mark.svg" alt="" />
                    </div>
                </div>
                <div class="awards-item-blue" style="background: #e9e5e5">
                    <div
                        class="gamification-blue-title"
                        style="color: #343333"
                        ng-style="award.originalName === 'useCleverStaff' && $root.currentLang === 'pl' ? { 'font-size': '15px' } : { 'font-size': '16px' }"
                        ng-bind-html="award.name | translate"
                    ></div>
                    <span
                        class="gamification-blue-title-secondary awards-item-blue-title"
                        style="color: #343333"
                        ng-style="award.name === 'achieve_title.sendMailsViaPersonalMailing' ? { 'margin-top': '10px' } : { 'margin-top': '20px' }"
                        translate="{{ award.description }}"
                        translate-values="{count:award.count}"
                    ></span>
                    <div class="awards-item-blue-counter flex-justify-align-center" style="background: #ddd6d6">
                        <span class="awards-item-blue-counter-count" style="color: #343333">{{ award.level }}/5</span>
                    </div>
                </div>
                <img ng-if="award.level === 0" class="awards-item-level-icon" src="images/sprite/achievements/level-0.svg" alt="" />
                <img ng-if="award.level === 1" class="awards-item-level-icon" src="images/sprite/achievements/level-1.svg" alt="" />
                <img ng-if="award.level === 2" class="awards-item-level-icon" src="images/sprite/achievements/level-2.svg" alt="" />
                <img ng-if="award.level === 3" class="awards-item-level-icon" src="images/sprite/achievements/level-3.svg" alt="" />
                <img ng-if="award.level === 4" class="awards-item-level-icon" src="images/sprite/achievements/level-4.svg" alt="" />
                <img ng-if="award.level === 5" class="awards-item-level-icon" src="images/sprite/achievements/level-5.svg" alt="" />
            </div>
        </div>
    </div>
    <span class="headline-orange-gamification margin-auto" ng-class="{ showSecretAchieve: vm.showSecretAchivment }" style="display: none">
        {{ 'Secret awards' | translate }}
    </span>
    <div class="awards-secret">
        <div class="awards-simple">
            <div ng-repeat="award in vm.secretAwards">
                <div ng-if="award.done" class="awards-item awards-item-wrapper" style="margin-right: 40px; margin-bottom: 20px" name="{{ award.originalName }}">
                    <div class="awards-item-orange flex-justify-align-center">
                        <div ng-if="!award.done" class="awards-item-orange-counter">
                            <span class="gamification-big-orange-title orange-title-small">25</span>
                            <img class="awards-item-orange-counter-image" src="images/sprite/achievements/crystals/color-stone.svg" alt="" />
                        </div>
                        <div ng-if="award.done" class="awards-item-orange flex-justify-align-center">
                            <img class="orange-check-mark-img" src="images/sprite/achievements/check-mark.svg" alt="" />
                        </div>
                    </div>
                    <div class="awards-item-blue" ng-class="{ 'flex-justify-align-center': !award.done }">
                        <span ng-if="award.done" class="gamification-blue-title">{{ award.name | translate }}</span>
                        <span ng-if="award.done" class="gamification-blue-title-secondary awards-item-blue-title">
                            {{ award.description | translate }}
                        </span>
                        <span ng-if="!award.done" class="gamification-blue-title">
                            {{ 'Secret award' | translate }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
