<div class="feed-greeting">
    <div class="feed-greeting__avatar">
        <img src="/images/sprite/gamification/bird.png" alt="" />
    </div>

    <div class="feed-greeting__body">
        <div class="feed-greeting__body-title">
            <span ng-show="false">{{ 'tasks-feed-hi-update' | translate : { user: $root.me.firstName } }} 👋</span>
            <div ng-show="!vm.showNewVersion">
                <span ng-show="$root.todayEvents.length === 1">{{ 'tasks-feed-hi-1' | translate : { user: $root.me.firstName } }} 👋</span>
                <span ng-show="$root.todayEvents.length === 2 || $root.todayEvents.length === 3">
                    {{ 'tasks-feed-hi-2-3' | translate : { user: $root.me.firstName } }} 👋
                </span>
                <span ng-show="$root.todayEvents.length >= 4">{{ 'tasks-feed-hi-4' | translate : { user: $root.me.firstName } }} 👋</span>
                <span ng-show="$root.todayEvents.length === 0">{{ 'tasks-feed-hi-0' | translate : { user: $root.me.firstName } }} 👋</span>
            </div>
        </div>
        <div class="feed-greeting__body-text">
            <div ng-show="vm.showNewVersion">
                <div ng-show="$root.currentLang === 'ua'">
                    <span>
                        Друзі, час, на який ми так довго чекали, настав! Ви вже можете бачити перед собою наше оновлення інтерфейсу CleverStaff &#128512;
                    </span>
                    <br />
                    <span>
                        Ми дуже раді його вам презентувати, це результат дуже довгої і старанної праці всієї нашої команди і тому хочемо, щоб вас він так само
                        радував, як і нас &#128525;
                    </span>
                    <br />
                    <span>
                        Ви можете помітити, що деякі сторінки не зазнали змін, але будьте впевнені, що наша робота не закінчена і оновлення інтерфейсу будуть
                        виходити і далі.
                    </span>
                    <br />
                    <span>
                        З деталями редизайну ви можете ознайомитися
                        <span ng-click="vm.redirectToBlog('blog')" class="feed-greeting__body-text-link">{{ 'here' | translate }}.</span>
                    </span>
                    <br />
                    <span>
                        Також пропонуємо вам
                        <span ng-click="vm.redirectToBlog('youtube')" class="feed-greeting__body-text-link">запис нашого вебінара</span>
                        про зміни в інтерфейсі (вебінар українською мовою)
                    </span>
                </div>
                <div ng-show="$root.currentLang === 'ru'">
                    <span>
                        Друзья, время, которого мы так долго ждали, настало! Вы уже можете видеть перед собой наше обновление интерфейса CleverStaff &#128512;
                    </span>
                    <br />
                    <span>
                        Мы очень рады его вам презентовать, это результат очень долгого и усердного труда всей нашей команды и поэтому хотим, чтобы вас он так
                        же радовал, как и нас &#128525;
                    </span>
                    <br />
                    <span>
                        Вы можете заметить, что некоторые страницы не претерпели изменений, но будьте уверены, что наша работа не окончена и обновления
                        интерфейса будут выходить и дальше.
                    </span>
                    <br />
                    <span>
                        С деталями редизайна вы можете ознакомиться
                        <span ng-click="vm.redirectToBlog('blog')" class="feed-greeting__body-text-link">{{ 'here' | translate }}.</span>
                    </span>
                    <br />
                    <span>
                        Также предлагаем вам
                        <span ng-click="vm.redirectToBlog('youtube')" class="feed-greeting__body-text-link">запись нашего вебинара</span>
                        об изменениях в интерфейсе (вебинар на украинском языке)
                    </span>
                </div>
                <div ng-show="$root.currentLang === 'en'">
                    <span>Friends, the time has come! You can already see our CleverStaff UI update in front of you &#128512;</span>
                    <br />
                    <span>
                        We are very happy to present it to you. It is the result of very long and diligent work of our entire team. We strived to please you
                        &#128525;
                    </span>
                    <br />
                    <span>
                        You may notice that some pages have not undergone changes, but rest assured that our work is not finished and interface updates will
                        continue to come out.
                    </span>
                    <br />
                    <span>
                        Details of the redesign can be found
                        <span ng-click="vm.redirectToBlog('blog')" class="feed-greeting__body-text-link">{{ 'here' | translate }}.</span>
                    </span>
                    <br />
                    <span>
                        We also offer you a
                        <span ng-click="vm.redirectToBlog('youtube')" class="feed-greeting__body-text-link">video of our webinar</span>
                        out interface changes (webinar in Ukrainian)
                    </span>
                </div>
                <div ng-show="$root.currentLang === 'pl'">
                    <span>Drodzy, nadszedł czas, na który czekaliśmy! Już teraz możecie zobaczyć nowy interfejs CleverStaff &#128512;</span>
                    <br />
                    <span>
                        Z przyjemnością prezentujemy interfejs, nad którym cały nasz zespół długo i ciężko pracował. Chcemy, abyście się cieszyli nim tak samo
                        jak my &#128525;
                    </span>
                    <br />
                    <span>
                        Możecie zauważyć, że niektóre rzeczy pozostały bez zmian, ale możecie być pewni, że nasza praca się jeszcze nie kończy i będziemy nadal
                        kontynuować aktualizację interfejsu.
                    </span>
                    <br />
                    <span>
                        Szczegóły dotyczące aktualizacji interfejsu można znaleźć
                        <span ng-click="vm.redirectToBlog('blog')" class="feed-greeting__body-text-link">{{ 'here' | translate }}.</span>
                    </span>
                    <br />
                    <span>
                        Mamy też
                        <span ng-click="vm.redirectToBlog('youtube')" class="feed-greeting__body-text-link">nagranie naszego webinaru</span>
                        webinar poświęcony zmianom w interfejsie (webinar w języku ukraińskim)
                    </span>
                </div>
            </div>

            <div ng-show="!vm.showNewVersion">
                <div ng-show="$root.todayEvents.length === 1">
                    <span ng-show="$root.todayEvents[0].event.type === 'interview'">
                        {{ 'tasks-feed-text-1-v2' | translate : { time: ($root.todayEvents[0].event.date | dateFormatHours | translate) } }}
                    </span>
                    <span ng-show="$root.todayEvents[0].event.type !== 'interview'">{{ 'tasks-feed-text-1-v1' | translate }}</span>
                </div>
                <div ng-show="$root.todayEvents.length === 2 || $root.todayEvents.length === 3">
                    <span ng-show="$root.itsHotTask && $root.me.recrutRole !== 'client'">{{ 'tasks-feed-text-2-3-v1' | translate }}</span>
                    <span ng-show="!$root.itsHotTask && $root.me.recrutRole !== 'client'">{{ 'tasks-feed-text-2-3-v1-not' | translate }}</span>
                    <span ng-show="$root.me.recrutRole === 'client'">{{ 'tasks-feed-text-2-3-v1-hm' | translate }}</span>
                </div>
                <div ng-show="$root.todayEvents.length >= 4">
                    <span>{{ 'tasks-feed-text-4' | translate }}</span>
                </div>
                <div ng-show="$root.todayEvents && $root.todayEvents.length === 0">
                    <span>{{ 'tasks-feed-text-0' | translate }}</span>
                    <br ng-if="vm.screenSize" />
                    <span>{{ 'tasks-feed-text-0-end' | translate }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
