<div class="game-board">
    <div class="game-board__items">
        <div class="game-board__items__avatar">
            <img ng-src="{{ vm.serverAddress }}/gamification/getLevelImage?userId={{ $root.me.userId }}" alt="" />
        </div>
        <div class="game-board__items__info">
            <div class="game-board__items__info-title">
                <span>{{ $root.me.fullName }}</span>
                <span class="orange width">{{ vm.userLvl.userLevel }} {{ 'level' | translate }}</span>
            </div>
            <div class="game-board__items__info-level">
                <div class="game-board__items__info-level-count">
                    <span>{{ vm.userLvl.totalExperiencePoints }}</span>
                    <span>{{ vm.userLvl.nextLevelPoints }}</span>
                </div>
                <semicircle-progress-bar
                    gamification-mood="true"
                    tasks-mod="true"
                    exp="''"
                    percent="vm.userLvl.totalExperiencePoints/vm.userLvl.nextLevelPoints"
                ></semicircle-progress-bar>
                <div class="game-board__items__info-level-text">
                    <span>{{ vm.returnNameOfLevel(vm.userLvl.userLevel, $root.me.sex) | translate }}</span>
                </div>
            </div>
        </div>
    </div>
    <div ng-show="vm.monthRace.length" class="game-board__items">
        <div class="game-board__items__avatar">
            <img src="images/redesign/icons-dashboard/run.svg" alt="" />
        </div>
        <div class="game-board__items__info">
            <div class="game-board__items__info-title">
                <span>{{ 'colleagues-competitions' | translate }}</span>
            </div>
            <div class="race-wrapper">
                <div class="race-wrapper__users" ng-class="{ distance: vm.monthRace.length < 3 }">
                    <div class="race-wrapper__users-user" ng-repeat="user in vm.monthRace">
                        <div class="race-wrapper__users-user-stars">
                            <img ng-src="images/redesign/icons-dashboard/{{ user.userId === $root.me.userId ? 'star-green' : 'star-grey' }}.png" alt="" />
                            <span ng-class="{ 'star-green': user.userId === $root.me.userId, digits: user.place.toString().length > 1 }">
                                {{ user.place }}
                            </span>
                        </div>
                        <responsible-person
                            avatar-id="user.info.avatarId"
                            full-name="$root.useAmericanNameStyle ? user.info.fullNameEn : user.info.fullNameEn"
                            user-id="user.userId"
                        ></responsible-person>
                        <div class="race-wrapper__users-user-circles" ng-class="{ 'circle-green': user.userId === $root.me.userId }"></div>
                    </div>
                </div>
                <div class="race-wrapper__line"></div>
                <div class="race-wrapper__crystals" ng-class="{ distance: vm.monthRace.length < 3 }">
                    <div class="race-wrapper__crystals__items" ng-repeat="stone in vm.monthRace">
                        <span ng-show="stone.already" class="race-wrapper__crystals__items-already">{{ 'too' | translate }}</span>
                        <span>{{ stone.crystals }}</span>
                        <img src="images/sprite/achievements/crystals/color-stone.svg" alt="" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div ng-show="vm.monthResult" class="game-board__items">
        <div class="game-board__items__avatar">
            <img src="images/redesign/icons-dashboard/gym.svg" alt="" />
        </div>
        <div class="game-board__items__honors">
            <div class="game-board__items__honors-name">
                <span>
                    {{ 'surpass-month' | translate }}
                    {{
                        $root.currentLang === 'en'
                            ? ('month_MM.' + vm.monthResult.month | translate)
                            : ('month_MM.' + vm.monthResult.month | translate).toLocaleLowerCase()
                    }}{{ !vm.showYear ? '!' : '' }}
                    {{ vm.showYear ? vm.monthResult.year + '!' : '' }}
                </span>

                <span class="game-board__items__honors-name-text" ng-hide="vm.showForToday">
                    {{
                        'earn-stones'
                            | translate : { stones: vm.monthResult.goal - vm.monthResult.current, text: vm.monthResult.text, days: vm.monthResult.days }
                    }}
                </span>
                <span ng-show="vm.showForToday" class="game-board__items__honors-name-text">
                    {{ 'earn-stones for today' | translate : { stones: vm.monthResult.goal - vm.monthResult.current, text: vm.monthResult.text } }}
                </span>
            </div>
            <div class="game-board__items__honors-stones">
                <span class="orange">{{ vm.monthResult.goal }} {{ 'stones' | translate }}</span>
                <div class="stone">
                    <span>{{ vm.monthResult.current }}</span>
                    <img src="images/sprite/achievements/crystals/color-stone.svg" alt="" />
                </div>
            </div>
        </div>
    </div>
    <div ng-show="vm.achive && !vm.achive.hotTask" class="game-board__items">
        <div class="game-board__items__avatar">
            <img src="images/redesign/icons-dashboard/cup.svg" alt="" />
        </div>
        <div class="game-board__items__honors">
            <div class="game-board__items__honors-name">
                <span class="game-board__items__honors-name-title">
                    <!-- prettier-ignore -->
                    <span class='game-board__items__honors-name-title'>{{ 'Win an award' | translate }} "<span class='game-board__items__honors-name-title' ng-bind-html="'achieve_title.' + vm.achive.titleText | translate"></span>"</span>
                </span>
                <span ng-bind-html="'achieve_description.' + vm.achive.achievementType | translate : { count: vm.achive.countForNextLevel }"></span>
            </div>
            <div class="game-board__items__honors-stones">
                <div class="stone">
                    <span ng-if="vm.achive.level === 0">10</span>
                    <span ng-if="vm.achive.level === 1">20</span>
                    <span ng-if="vm.achive.level === 2">30</span>
                    <span ng-if="vm.achive.level === 3">40</span>
                    <span ng-if="vm.achive.level === 4">50</span>
                    <img src="images/sprite/achievements/crystals/color-stone.svg" alt="" />
                </div>
            </div>
        </div>
    </div>
    <div ng-show="vm.achive && vm.achive.hotTask" class="game-board__items">
        <div class="game-board__items__avatar">
            <img src="images/redesign/icons-dashboard/fire.svg" alt="" />
        </div>
        <div class="game-board__items__honors">
            <div class="game-board__items__honors-name">
                <span ng-bind-html="'achieve_title.' + vm.achive.titleText | translate"></span>
                <span>{{ 'hot_tasks.' + vm.achive.achievementType | translate : { count: vm.achive.decl, stones: vm.achive.countStones } }}</span>
            </div>
            <div class="game-board__items__honors-stones">
                <div class="stone">
                    <span ng-if="vm.achive.level === 0">10</span>
                    <span ng-if="vm.achive.level === 1">20</span>
                    <span ng-if="vm.achive.level === 2">30</span>
                    <span ng-if="vm.achive.level === 3">40</span>
                    <span ng-if="vm.achive.level === 4">50</span>
                    <img src="images/sprite/achievements/crystals/color-stone.svg" alt="" />
                </div>
            </div>
        </div>
    </div>
</div>
