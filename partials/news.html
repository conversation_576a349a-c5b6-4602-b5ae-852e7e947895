<div class="block-news">
    <div class="container-fluid">
        <div class="row">
            <div ng-show="$root.currentLang !== 'en'" class="col-lg-12 fbGroupPage">
                <div
                    class="fb-page"
                    data-href="https://www.facebook.com/cleverstaff.users.ru/"
                    data-tabs="timeline"
                    data-width="800"
                    data-height="645"
                    data-small-header="true"
                    data-adapt-container-width="false"
                    data-hide-cover="true"
                    data-show-facepile="true"
                >
                    <blockquote class="fb-xfbml-parse-ignore" cite="https://www.facebook.com/cleverstaff.users.ru/">
                        <a href="https://www.facebook.com/cleverstaff.users.ru/">Пользователи CleverStaff</a>
                    </blockquote>
                </div>
                <div class="info">
                    <button ng-click="CSNewsFeed('someRulesFbPosts');" class="btn_default btn_empty btn-user-news">
                        {{ 'Rules' | translate }}
                    </button>
                    <button ng-click="CSNewsFeed('sharesPostsFromCS');" class="btn_default btn_success">
                        {{ 'Info' | translate }}
                    </button>
                </div>
                <div class="sharesPostsFromCS">
                    <p>{{ 'CleverStaff News Feed shares posts from the CleverStaff Users facebook page. We created the news feed to' | translate }}:</p>
                    <ul>
                        <li>
                            <span>{{ 'Share important news, updates and improvements which happen at CleverStaff' | translate }}.</span>
                        </li>
                        <li>
                            <span>{{ 'Share questionnaires and understand which features to add first and which ones you like' | translate }}.</span>
                        </li>
                        <li>
                            <span>{{ 'Build the CleverStaff community where our users can meet, friend and cooperate' | translate }}.</span>
                        </li>
                    </ul>
                    <p>
                        {{
                            'You can freely interact with the news feed and post content, which will be published after a review to guarantee its quality'
                                | translate
                        }}.
                    </p>
                </div>
                <div class="someRulesFbPosts">
                    <p>{{ 'Here are some rules which make the news feed content useful and interesting' | translate }}:</p>
                    <ul>
                        <li>
                            <span>
                                {{ 'tech_or_support_requests_1' | translate }}
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                                {{ 'tech_or_support_requests_2' | translate }}
                            </span>
                            .
                        </li>
                        <li>
                            <span>
                                {{ 'to_share_feedback_1' | translate }}
                                <a href="#/feedback">{{ 'feedback form' | translate }}</a>
                                {{ 'to_share_feedback_2' | translate }}
                            </span>
                            .
                        </li>
                        <li>
                            <span>
                                {{
                                    'No panic if your post did not publish instantly: we moderate all content. Thats why you can not find any intrusive, annoying and unpleasant content'
                                        | translate
                                }}.
                            </span>
                        </li>
                        <li>
                            <span>{{ 'Please use English in group posts and conversations so that everyone could understand you' | translate }}.</span>
                        </li>
                    </ul>
                    <p>{{ 'Thank you for your understanding. We appreciate it' | translate }}</p>
                </div>
            </div>

            <div ng-show="$root.currentLang == 'en'" class="col-lg-12 fbGroupPage">
                <div
                    class="fb-page"
                    data-href="https://www.facebook.com/cleverstaff.users/"
                    data-tabs="timeline"
                    data-width="800"
                    data-height="645"
                    data-small-header="true"
                    data-adapt-container-width="false"
                    data-hide-cover="true"
                    data-show-facepile="true"
                >
                    <blockquote class="fb-xfbml-parse-ignore" cite="https://www.facebook.com/cleverstaff.users/">
                        <a href="https://www.facebook.com/cleverstaff.users/">Пользователи CleverStaff</a>
                    </blockquote>
                </div>
                <div class="info">
                    <button ng-click="CSNewsFeed('someRulesFbPosts');" class="btn_default btn_empty btn-user-news">
                        {{ 'Rules' | translate }}
                    </button>
                    <button ng-click="CSNewsFeed('sharesPostsFromCS');" class="btn_default btn_success">
                        {{ 'Info' | translate }}
                    </button>
                </div>
                <div class="sharesPostsFromCS">
                    <p>{{ 'CleverStaff News Feed shares posts from the CleverStaff Users facebook page. We created the news feed to' | translate }}:</p>
                    <ul>
                        <li>
                            <span>{{ 'Share important news, updates and improvements which happen at CleverStaff' | translate }}.</span>
                        </li>
                        <li>
                            <span>{{ 'Share questionnaires and understand which features to add first and which ones you like' | translate }}.</span>
                        </li>
                        <li>
                            <span>{{ 'Build the CleverStaff community where our users can meet, friend and cooperate' | translate }}.</span>
                        </li>
                    </ul>
                    <p>
                        {{
                            'You can freely interact with the news feed and post content, which will be published after a review to guarantee its quality'
                                | translate
                        }}.
                    </p>
                </div>
                <div class="someRulesFbPosts">
                    <p>{{ 'Here are some rules which make the news feed content useful and interesting' | translate }}:</p>
                    <ul>
                        <li>
                            <span>
                                {{ 'tech_or_support_requests_1' | translate }}
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                                {{ 'tech_or_support_requests_2' | translate }}
                            </span>
                            .
                        </li>
                        <li>
                            <span>
                                {{ 'to_share_feedback_1' | translate }}
                                <a href="#/feedback">{{ 'feedback form' | translate }}</a>
                                {{ 'to_share_feedback_2' | translate }}
                            </span>
                            .
                        </li>
                        <li>
                            <span>
                                {{
                                    'No panic if your post did not publish instantly: we moderate all content. Thats why you can not find any intrusive, annoying and unpleasant content'
                                        | translate
                                }}.
                            </span>
                        </li>
                        <li>
                            <span>{{ 'Please use English in group posts and conversations so that everyone could understand you' | translate }}.</span>
                        </li>
                    </ul>
                    <p>{{ 'Thank you for your understanding. We appreciate it' | translate }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
