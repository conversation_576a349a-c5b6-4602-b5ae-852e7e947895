<div class="custom-popover">
    <div>
        <span ng-if="candidate.scoreCardResultDto.nonFilledScoreCards.length > 1">{{ 'NoCardScore' | translate }}:</span>
        <span ng-if="candidate.scoreCardResultDto.nonFilledScoreCards.length === 1">{{ 'NoCardsScore' | translate }}:</span>
    </div>
    <span
        ng-show="card.name === 'Default' || card.name === 'Old Default'"
        ng-click="parentScope.routeOnCandidateEvaluation(parentScope.vacancy, parentScope.sliderId, candidate, card.scoreCardId); $event.stopPropagation()"
        class="cards"
        ng-repeat="card in candidate.scoreCardResultDto.nonFilledScoreCards"
    >
        {{ card.name | translate }}
        <br />
    </span>
    <span
        ng-click="parentScope.routeOnCandidateEvaluation(parentScope.vacancy, parentScope.sliderId, candidate, card.scoreCardId); $event.stopPropagation()"
        class="cards"
        ng-repeat="card in candidate.scoreCardResultDto.nonFilledScoreCards"
        ng-hide="card.name === 'Default' || card.name === 'Old Default'"
    >
        {{ card.name }}
        <br />
    </span>
</div>
