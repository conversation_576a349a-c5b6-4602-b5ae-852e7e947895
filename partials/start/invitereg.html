<div class="invite-reg-wrapper">
    <header class="header header--simplify">
        <div class="logo-banner">
            <img src="/external/images/logo-light.svg" alt="CleverStaff" />
        </div>
        <div ng-if="$root.inviteHasExpired === true;" class="inviteHasExpired" style="margin-top: 12rem; color: white; width: 38%; font-size: 1.6rem">
            {{ 'the invitation to the account' | translate }}
            "{{ registrationFormModel.orgName }}"
            {{ 'is_no_longer_valid' | translate }}
        </div>
        <div class="static-logo">
            <img src="/external/images/logo.svg" alt="CleverStaff" />
        </div>
    </header>
    <div class="container viewport-height">
        <div ng-if="$root.inviteState == 'allreadyInAccount'" class="col-lg-6 container-position alreadyAccountMargin text-center inviteConfirmed">
            <div>
                <h1 class="page-title page-title--early">
                    {{ 'You have already confirmed your registration at CleverStaff.' | translate }}
                </h1>
                <p>{{ 'If you want to log in, click the button  below:' | translate }}</p>
                <button ng-click="redirectToSigning()" class="btn btn--primary">
                    {{ 'Login to Your Account' | translate }}
                </button>
            </div>
        </div>
        <div ng-if="$root.inviteHasExpired === false" class="row">
            <div ng-show="$root.inviteState == 'unknown'" class="col-lg-6 container-position alreadyAccountMargin text-center inviteConfirmed">
                <div>
                    <h1 class="page-title page-title--early">
                        {{ 'This invitation link is incorrect or was sent by mistake.' | translate }}
                    </h1>
                    <p>{{ 'Please ask your account Admin to send a new one.' | translate }}</p>
                </div>
            </div>
            <div
                ng-show="$root.inviteState == 'activeInvite'"
                class="col-sm-10 offset-sm-1 col-md-8 offset-md-2 col-lg-6 offset-lg-5 col-xl-5 offset-xl-6 container-position"
            >
                <h2 class="container-title">
                    {{ 'Please fill the form below to complete the registration' | translate }}
                </h2>
                <form id="form-register" class="form form--long" name="registrationForm" autocomplete="nope">
                    <div class="form__group display-flex">
                        <div class="form-role">
                            <span class="form__label">{{ 'Your role in this account will be' | translate }}</span>
                            <div class="form__input">{{ registrationFormModel.role | registrationRole }}</div>
                        </div>
                        <div class="form-email">
                            <span class="form__label">{{ 'it will be your login in CleverStaff' | translate }}</span>
                            <div class="form__input">{{ registrationFormModel.login }}</div>
                        </div>
                    </div>
                    <fieldset class="form__group">
                        <label class="form__label" for="user-name">
                            {{ 'first_name' | translate }}
                            <span class="form__indicator">*</span>
                        </label>
                        <input
                            id="user-name"
                            class="form__input"
                            ng-class="{ 'error-field': registrationFormModel.firstName.isValid === false }"
                            name="firstName"
                            ng-model="registrationFormModel.firstName.value"
                            type="text"
                            maxlength="50"
                            ng-blur="onValidateName(registrationFormModel.firstName)"
                            ng-change="onCheckForEnableForm()"
                            ng-focus="onResetError(registrationFormModel.firstName)"
                            placeholder="{{ 'Enter your name' | translate }}"
                        />
                        <span class="error-text" ng-class="{ 'error-show': registrationFormModel.firstName.isValid === false }">
                            {{ 'wrong_name' | translate }}
                        </span>
                    </fieldset>
                    <fieldset class="form__group">
                        <label class="form__label" for="last-name">
                            {{ 'last_name' | translate }}
                            <span class="form__indicator">*</span>
                        </label>
                        <input
                            id="last-name"
                            class="form__input"
                            ng-class="{ 'error-field': registrationFormModel.lastName.isValid === false }"
                            name="lastName"
                            ng-model="registrationFormModel.lastName.value"
                            type="text"
                            maxlength="50"
                            ng-blur="onValidateName(registrationFormModel.lastName)"
                            ng-change="onCheckForEnableForm()"
                            ng-focus="onResetError(registrationFormModel.lastName)"
                            placeholder="{{ 'Enter your last name' | translate }}"
                        />
                        <span class="error-text" ng-class="{ 'error-show': registrationFormModel.lastName.isValid === false }">
                            {{ 'wrong_lastName' | translate }}
                        </span>
                    </fieldset>
                    <fieldset class="form__group">
                        <label class="form__label" for="user-name">
                            {{ 'Phone' | translate }}
                            <span class="form__indicator">*</span>
                        </label>
                        <div class="one-line-group">
                            <input
                                id="countryCustom"
                                ng-click="onValidateLength(registrationFormModel.phone, 15)"
                                class="countryCustom"
                                select2-phone-code
                                name="countryCustom"
                                type="hidden"
                            />
                            <input
                                id="user-tel"
                                class="form__input tel--input"
                                ng-class="{ 'error-field': registrationFormModel.phone.isValid === false }"
                                name="phone"
                                ng-model="registrationFormModel.phone.value"
                                ng-blur="onValidateLength(registrationFormModel.phone, 15)"
                                ng-focus="onResetError(registrationFormModel.phone)"
                                ng-change="onReplaceCharsToNumber(registrationFormModel.phone)"
                                placeholder="{{ 'Enter your phone number' | translate }}"
                            />
                        </div>
                        <span class="error-text phone-position" ng-class="{ 'error-show': registrationFormModel.phone.isValid === false }">
                            {{ 'enter_phone' | translate }}
                        </span>
                    </fieldset>
                    <div id="errorForRus">
                        <span>{{ "CleverStaff - it is a pure 100% Ukrainian product and we've" | translate }}</span>
                        <a href="{{ 'link to news' | translate }}" target="_blank">{{ 'refused' | translate }}</a>
                        <span>
                            {{ 'to cooperate with the clients from Russian Federation. Glory to Ukraine!' | translate }}
                        </span>
                    </div>
                    <fieldset ng-if="!info.samlRegistration" class="form__group form__group--plain">
                        <label class="form__label">
                            {{ 'Password' | translate }}
                            <span class="form__indicator">*</span>
                        </label>
                        <div class="display-flex">
                            <input
                                id="password"
                                class="form__input pass--input"
                                ng-class="{ 'error-field': registrationFormModel.password.isValid === false }"
                                type="{{ inputPassType }}"
                                autocomplete="new-password"
                                ng-blur="onValidatePassword(registrationFormModel.password, registrationFormModel.confirmPassword) && validatePassword()"
                                ng-focus="onResetError(registrationFormModel.password)"
                                ng-model="registrationFormModel.password.value"
                                placeholder="{{ 'Enter your password' | translate }}"
                            />
                            <input
                                id="password2"
                                class="form__input pass--input pass-input-confirm"
                                ng-class="{ 'error-field': registrationFormModel.confirmPassword.isValid === false }"
                                type="{{ inputPassType }}"
                                name="password2"
                                ng-model="registrationFormModel.confirmPassword.value"
                                ng-blur="onValidatePassword(registrationFormModel.password, registrationFormModel.confirmPassword) && validatePassword()"
                                ng-focus="onResetError(registrationFormModel.confirmPassword)"
                                placeholder="{{ 'Confirm password' | translate }}"
                            />
                            <div class="password-status">
                                <img ng-click="showPassword()" class="state-show" ng-hide="switchPassword" src="/images/password-status/eye_show.svg" alt="" />
                                <img ng-show="switchPassword" ng-click="showPassword()" class="state-hide" src="/images/password-status/eye_hide.svg" alt="" />
                            </div>
                        </div>
                        <span class="password-error" style="color: #e75a3a; padding-left: 20px; display: block; margin-top: 5px"></span>
                        <small>
                            {{ 'Password must contain at least 8 characters, include letters, numbers and special characters' | translate }}
                        </small>
                    </fieldset>
                    <fieldset>
                        <input
                            id="agreement"
                            ng-click="checkPrivacyPolicy()"
                            class="checkbox"
                            name="user-agreement"
                            ng-change="onCheckForEnableForm()"
                            ng-model="checkPrivacyOrTerms"
                            ng-checked="checkPrivacyOrTerms"
                            type="checkbox"
                        />
                        <label for="agreement">
                            {{ 'I have read and I accept the' | translate }}
                            <a href="https://cleverstaff.net/privacy.html" target="_blank">
                                {{ 'Privacy Policy' | translate }}
                            </a>
                            {{ 'and the' | translate }}
                            <a href="https://cleverstaff.net/terms.html" target="_blank">
                                {{ 'Terms and Conditions' | translate }}
                            </a>
                            <span class="form__indicator">*</span>
                        </label>
                    </fieldset>
                    <fieldset class="form__submit">
                        <button
                            id="inviteBtn"
                            ng-click="ngClickRegistration(registrationForm)"
                            class="btn btn--primary"
                            type="submit"
                            ng-disabled="disabledForm"
                        >
                            {{ 'Sign Up and Sign In' | translate }}
                        </button>
                    </fieldset>
                </form>
            </div>
        </div>
        <div class="banner banner--basic">
            <div class="banner__decoration">
                <div class="decoration-wrapper decoration--first">
                    <svg class="decoration" viewBox="0 0 14 14">
                        <path
                            d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                        ></path>
                        <path
                            d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                        ></path>
                    </svg>
                </div>
                <div class="decoration-wrapper decoration--second">
                    <svg class="decoration" viewBox="0 0 14 14">
                        <path
                            d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                        ></path>
                        <path
                            d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                        ></path>
                    </svg>
                </div>
                <div class="decoration-wrapper decoration--third">
                    <svg class="decoration" viewBox="0 0 14 14">
                        <path
                            d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                        ></path>
                        <path
                            d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                        ></path>
                    </svg>
                </div>
                <div class="decoration-wrapper decoration--fourth">
                    <svg class="decoration" viewBox="0 0 14 14">
                        <path
                            d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                        ></path>
                        <path
                            d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                        ></path>
                    </svg>
                </div>
            </div>
            <div ng-if="$root.inviteHasExpired == false" class="banner__content">
                <h2 class="page-title page-title--early">{{ 'Welcome to CleverStaff!' | translate }}</h2>
                <div>
                    <b>{{ registrationFormModel.inviter }}</b>
                    {{ 'invites you at the account of the company' | translate }}
                    <b>{{ registrationFormModel.orgName }}</b>
                    <br />
                    {{ 'in' | translate }} CleverStaff — {{ 'ATS & Recruitment Software' | translate }}
                </div>
            </div>
        </div>
    </div>
</div>
