<div class="restore-wrapper">
    <header class="header--back header header--simplify">
        <a class="logo logo--lg logo-banner" style="pointer-events: none">
            <img style="width: 250px" src="/images/sprite/logo-light-black.svg" alt="CleverStaff" />
        </a>
    </header>
    <div ng-show="showRestoreForm" class="container">
        <div class="row">
            <div class="col-sm-10 offset-sm-1 col-md-8 offset-md-2 col-lg-6 offset-lg-1 col-xl-5">
                <div class="restore-wrapper-title" translate="Set new password"></div>
                <form id="form-login" class="form">
                    <fieldset class="form__group form__group--plain">
                        <label class="form__label" translate="password"></label>
                        <div class="one-line-group">
                            <input
                                class="form__input form__input pass--input"
                                ng-class="{ errorInput: newPasswordError }"
                                type="{{ newPasswordType }}"
                                placeholder="{{ 'Enter your password' | translate }}"
                                ng-model="newPassword"
                                ng-change="validateForm()"
                                ng-blur="validateNewPassword()"
                                ng-focus="newPasswordError=false"
                            />
                            <div ng-show="!showNewPassword" ng-click="showPassword('newPassword')" class="eye-icon">
                                <svg width="18" height="15" viewBox="0 0 18 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M8.8725 4.75L11.25 7.12C11.25 7.0825 11.25 7.0375 11.25 7C11.25 5.755 10.245 4.75 9 4.75C8.955 4.75 8.9175 4.75 8.8725 4.75ZM5.6475 5.35L6.81 6.5125C6.7725 6.67 6.75 6.8275 6.75 7C6.75 8.245 7.755 9.25 9 9.25C9.165 9.25 9.33 9.2275 9.4875 9.19L10.65 10.3525C10.1475 10.6 9.5925 10.75 9 10.75C6.93 10.75 5.25 9.07 5.25 7C5.25 6.4075 5.4 5.8525 5.6475 5.35ZM1.5 1.2025L3.21 2.9125L3.5475 3.25C2.31 4.225 1.335 5.5 0.75 7C2.0475 10.2925 5.25 12.625 9 12.625C10.1625 12.625 11.2725 12.4 12.285 11.995L12.6075 12.31L14.7975 14.5L15.75 13.5475L2.4525 0.25L1.5 1.2025ZM9 3.25C11.07 3.25 12.75 4.93 12.75 7C12.75 7.48 12.6525 7.945 12.48 8.365L14.6775 10.5625C15.8025 9.625 16.7025 8.395 17.25 7C15.9525 3.7075 12.75 1.375 9 1.375C7.95 1.375 6.945 1.5625 6 1.9L7.6275 3.5125C8.055 3.3475 8.5125 3.25 9 3.25Z"
                                        fill="#333333"
                                    />
                                </svg>
                            </div>
                            <div ng-show="showNewPassword" ng-click="hidePassword('newPassword')" class="eye-icon">
                                <svg width="18" height="12" viewBox="0 0 18 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M8.96484 3.75C7.61197 3.75 6.51989 4.755 6.51989 6C6.51989 7.245 7.61197 8.25 8.96484 8.25C10.3177 8.25 11.4098 7.245 11.4098 6C11.4098 4.755 10.3177 3.75 8.96484 3.75ZM8.96484 9.75C6.71548 9.75 4.88991 8.07 4.88991 6C4.88991 3.93 6.71548 2.25 8.96484 2.25C11.2142 2.25 13.0398 3.93 13.0398 6C13.0398 8.07 11.2142 9.75 8.96484 9.75ZM8.96484 0.375C4.88991 0.375 1.40993 2.7075 0 6C1.40993 9.2925 4.88991 11.625 8.96484 11.625C13.0398 11.625 16.5198 9.2925 17.9297 6C16.5198 2.7075 13.0398 0.375 8.96484 0.375Z"
                                        fill="#00B549"
                                    />
                                </svg>
                            </div>
                        </div>
                    </fieldset>
                    <fieldset class="form__group form__group--plain">
                        <span class="password-error" style="color: red; display: block; margin: 0 0 5px 22px; position: relative; bottom: 5px"></span>
                        <label class="form__label" translate="Repeat password"></label>
                        <div class="one-line-group">
                            <input
                                class="form__input form__input pass--input pass-input-confirm"
                                ng-class="{ errorInput: confirmedPasswordError }"
                                type="{{ confirmedPasswordType }}"
                                placeholder="{{ 'Enter your password again' | translate }}"
                                ng-model="confirmedPassword"
                                ng-change="validateForm()"
                                ng-blur="validateConfirmedPassword()"
                                ng-focus="confirmedPasswordError=false"
                            />
                            <div ng-show="!showConfirmPassword" ng-click="showPassword('confirmedPassword')" class="eye-icon">
                                <svg width="18" height="15" viewBox="0 0 18 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M8.8725 4.75L11.25 7.12C11.25 7.0825 11.25 7.0375 11.25 7C11.25 5.755 10.245 4.75 9 4.75C8.955 4.75 8.9175 4.75 8.8725 4.75ZM5.6475 5.35L6.81 6.5125C6.7725 6.67 6.75 6.8275 6.75 7C6.75 8.245 7.755 9.25 9 9.25C9.165 9.25 9.33 9.2275 9.4875 9.19L10.65 10.3525C10.1475 10.6 9.5925 10.75 9 10.75C6.93 10.75 5.25 9.07 5.25 7C5.25 6.4075 5.4 5.8525 5.6475 5.35ZM1.5 1.2025L3.21 2.9125L3.5475 3.25C2.31 4.225 1.335 5.5 0.75 7C2.0475 10.2925 5.25 12.625 9 12.625C10.1625 12.625 11.2725 12.4 12.285 11.995L12.6075 12.31L14.7975 14.5L15.75 13.5475L2.4525 0.25L1.5 1.2025ZM9 3.25C11.07 3.25 12.75 4.93 12.75 7C12.75 7.48 12.6525 7.945 12.48 8.365L14.6775 10.5625C15.8025 9.625 16.7025 8.395 17.25 7C15.9525 3.7075 12.75 1.375 9 1.375C7.95 1.375 6.945 1.5625 6 1.9L7.6275 3.5125C8.055 3.3475 8.5125 3.25 9 3.25Z"
                                        fill="#333333"
                                    />
                                </svg>
                            </div>
                            <div ng-show="showConfirmPassword" ng-click="hidePassword('confirmedPassword')" class="eye-icon">
                                <svg width="18" height="15" viewBox="0 0 18 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M8.96484 3.75C7.61197 3.75 6.51989 4.755 6.51989 6C6.51989 7.245 7.61197 8.25 8.96484 8.25C10.3177 8.25 11.4098 7.245 11.4098 6C11.4098 4.755 10.3177 3.75 8.96484 3.75ZM8.96484 9.75C6.71548 9.75 4.88991 8.07 4.88991 6C4.88991 3.93 6.71548 2.25 8.96484 2.25C11.2142 2.25 13.0398 3.93 13.0398 6C13.0398 8.07 11.2142 9.75 8.96484 9.75ZM8.96484 0.375C4.88991 0.375 1.40993 2.7075 0 6C1.40993 9.2925 4.88991 11.625 8.96484 11.625C13.0398 11.625 16.5198 9.2925 17.9297 6C16.5198 2.7075 13.0398 0.375 8.96484 0.375Z"
                                        fill="#00B549"
                                    />
                                </svg>
                            </div>
                        </div>
                    </fieldset>
                    <fieldset>
                        <span
                            class="password-info"
                            translate="Password must contain at least 8 characters, include letters, numbers and special characters"
                        ></span>
                    </fieldset>
                    <fieldset class="form__submit">
                        <button
                            ng-click="restore()"
                            class="btn btn--primary"
                            type="submit"
                            translate="Change and sign in"
                            ng-disabled="disabledRestore"
                        ></button>
                    </fieldset>
                </form>
            </div>
        </div>
    </div>
    <div ng-if="!showRestoreForm" class="alert alert-success" style="width: 50%; margin-left: 100px">
        {{ 'You have already changed the password on this special link' | translate }}
        <span ng-click="redirectToLanding()" style="text-decoration: underline; color: blue; cursor: pointer">
            {{ 'by_link' | translate }}
        </span>
    </div>
    <div class="banner banner--basic banner--alt">
        <div class="banner__decoration">
            <div class="decoration-wrapper decoration--fifth">
                <svg class="decoration" viewBox="0 0 14 14">
                    <path
                        d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                    ></path>
                    <path
                        d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                    ></path>
                </svg>
            </div>
            <div class="decoration-wrapper decoration--six">
                <svg class="decoration" viewBox="0 0 14 14">
                    <path
                        d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                    ></path>
                    <path
                        d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                    ></path>
                </svg>
            </div>
            <div class="decoration-wrapper decoration--seven">
                <svg class="decoration" viewBox="0 0 14 14">
                    <path
                        d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                    ></path>
                    <path
                        d="M1.3586 12.6309C3.74874 14.9315 8.23024 14.2636 11.2926 11.1465C14.355 8.02949 14.8778 3.57656 12.5624 1.27588L11.8154 0.533728C11.2926 0.162651 10.7698 -0.0599957 10.1722 0.0142198C9.87346 0.0142198 9.72407 0.311082 9.72407 0.533728C9.72407 0.607944 9.79876 0.756375 9.87346 0.83059L10.0975 1.05324C11.1432 2.3149 11.5914 3.65078 11.2179 3.94764C10.8444 4.2445 9.64938 3.50235 8.67839 2.16647L8.6037 2.09225C8.45431 1.86961 8.15555 1.86961 7.93147 2.01804L7.85678 2.09225C6.88579 3.13127 6.06418 4.17029 5.09319 5.13509C4.1222 6.09989 3.1512 7.06469 2.10552 7.88106C1.88145 8.02949 1.88145 8.32635 2.03083 8.549L2.10552 8.62322C3.37528 9.66223 4.19689 10.7755 3.82343 11.2208C3.52466 11.666 2.18021 11.1465 0.910454 10.1075L0.761071 9.95909C0.536996 9.81066 0.238229 9.81066 0.0888458 10.0333C0.0141541 10.1075 0.0141541 10.1817 0.0141541 10.3302C-0.0605376 10.9239 0.163537 11.5918 0.611688 11.9629L1.3586 12.6309Z"
                    ></path>
                </svg>
            </div>
        </div>
    </div>
</div>
