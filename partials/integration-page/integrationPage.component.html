<div class="container" style="padding: 16px">
    <div
        ng-if="
            $root.me.personParams.showUaWorkSites === 'Y' ||
            $root.me.personParams.registrationCountry == 'Ukraine' ||
            $root.me.personParams.registrationCountry == 'Kazakhstan' ||
            $root.me.personParams.registrationCountry == 'Belarus' ||
            $root.me.personParams.registrationCountry == 'Moldova' ||
            $root.me.personParams.registrationCountry == 'Uzbekistan' ||
            $root.me.personParams.registrationCountry == 'Kyrgyzstan' ||
            $root.me.personParams.registrationCountry == 'Russia' ||
            !$root.me.personParams.registrationCountry
        "
        class="title main-page-title"
        style="border-radius: 10px"
        translate="Integrations"
    ></div>
    <div class="main-block" style="border-radius: 10px">
        <!--TODO: change to new design inside component-->
        <wandify-integration-component ng-if="vm.integrationPayload" class="wandify-old-design"></wandify-integration-component>

        <div ng-if="vm.integrationPayload" class="integrations">
            <alphasms-integration-component integrated="vm.integrationPayload.alphaSms.integrated"></alphasms-integration-component>
        </div>

        <div class="integrations">
            <bamboo-hr-integration-component
                ng-if="vm.integrationPayload"
                is-bamboo-integrated="vm.integrationPayload.bambooHr.integrated"
            ></bamboo-hr-integration-component>
        </div>

        <div ng-if="vm.integrationPayload && vm.delucruCountryPermission" class="integrations">
            <delucru-integration-component integrated="vm.integrationPayload.delucru"></delucru-integration-component>
        </div>

        <div
            ng-if="
                $root.me.personParams.showUaWorkSites === 'Y' ||
                $root.me.personParams.registrationCountry == 'Ukraine' ||
                $root.me.personParams.registrationCountry == 'Kazakhstan' ||
                $root.me.personParams.registrationCountry == 'Belarus' ||
                $root.me.personParams.registrationCountry == 'Moldova' ||
                $root.me.personParams.registrationCountry == 'Uzbekistan' ||
                $root.me.personParams.registrationCountry == 'Kyrgyzstan' ||
                $root.me.personParams.registrationCountry == 'Russia' ||
                !$root.me.personParams.registrationCountry
            "
            class="integrations"
        >
            <djinni-integration-component ng-if="vm.integrationPayload" integrated="vm.integrationPayload.djinni"></djinni-integration-component>
        </div>

        <div
            ng-if="
                $root.me.orgParams.enableHHForOrg === 'Y' ||
                $root.me.orgParams.registrationCountry === 'Belarus' ||
                $root.me.orgParams.registrationCountry === 'Kazakhstan' ||
                $root.me.orgParams.registrationCountry === 'Uzbekistan' ||
                $root.me.orgParams.registrationCountry === 'Tajikistan' ||
                $root.me.orgParams.registrationCountry === 'Kyrgyzstan'
            "
            class="integrations"
        >
            <hh-integration-component ng-if="vm.integrationPayload" integrated="vm.integrationPayload.hh"></hh-integration-component>
        </div>
        <!--        <div-->
        <!--            ng-if="-->
        <!--                $root.me.personParams.showUaWorkSites === 'Y' ||-->
        <!--                $root.me.personParams.registrationCountry == 'Ukraine' ||-->
        <!--                $root.me.personParams.registrationCountry == 'Kazakhstan' ||-->
        <!--                $root.me.personParams.registrationCountry == 'Belarus' ||-->
        <!--                $root.me.personParams.registrationCountry == 'Moldova' ||-->
        <!--                $root.me.personParams.registrationCountry == 'Uzbekistan' ||-->
        <!--                $root.me.personParams.registrationCountry == 'Kyrgyzstan' ||-->
        <!--                $root.me.personParams.registrationCountry == 'Russia' ||-->
        <!--                !$root.me.personParams.registrationCountry-->
        <!--            "-->
        <!--            class="integrations"-->
        <!--        >-->
        <!--            <grc-integration-component ng-if="vm.integrationPayload" integrated="vm.integrationPayload.grc"></grc-integration-component>-->
        <!--        </div>-->

        <div
            ng-if="
                $root.me.personParams.showUaWorkSites === 'Y' ||
                $root.me.personParams.registrationCountry == 'Ukraine' ||
                $root.me.personParams.registrationCountry == 'Kazakhstan' ||
                $root.me.personParams.registrationCountry == 'Belarus' ||
                $root.me.personParams.registrationCountry == 'Moldova' ||
                $root.me.personParams.registrationCountry == 'Uzbekistan' ||
                $root.me.personParams.registrationCountry == 'Kyrgyzstan' ||
                $root.me.personParams.registrationCountry == 'Russia' ||
                !$root.me.personParams.registrationCountry
            "
            class="integrations"
        >
            <work-ua-integration-component ng-if="vm.integrationPayload" integrated="vm.integrationPayload.workua"></work-ua-integration-component>
        </div>

        <div
            ng-if="
                $root.me.personParams.showUaWorkSites === 'Y' ||
                $root.me.personParams.registrationCountry == 'Ukraine' ||
                $root.me.personParams.registrationCountry == 'Kazakhstan' ||
                $root.me.personParams.registrationCountry == 'Belarus' ||
                $root.me.personParams.registrationCountry == 'Moldova' ||
                $root.me.personParams.registrationCountry == 'Uzbekistan' ||
                $root.me.personParams.registrationCountry == 'Kyrgyzstan' ||
                $root.me.personParams.registrationCountry == 'Russia' ||
                !$root.me.personParams.registrationCountry
            "
            class="integrations"
        >
            <rabota-ua-integration-component ng-if="vm.integrationPayload" integrated="vm.integrationPayload.rabotaua"></rabota-ua-integration-component>
        </div>

        <div class="integrations">
            <linkedin-integration-component ng-if="vm.integrationPayload" integrated="vm.integrationPayload.linkedin"></linkedin-integration-component>
        </div>

        <div class="integrations"></div>
    </div>
</div>
