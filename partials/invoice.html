<div class="block-public block-company-public-vacancies invoice-generation">
    <div class="container">
        <page-intro-component page-title="'Wire Transfer Invoice Generation'"></page-intro-component>

        <section class="section__block invoice__order-info">
            <h2 class="title" translate="Order Information"></h2>

            <table class="order-info__data">
                <thead>
                    <tr>
                        <th>
                            <span translate="Product"></span>
                        </th>
                        <th>
                            <span translate="Paid users"></span>
                        </th>
                        <th>
                            <span translate="Month"></span>
                        </th>
                        <th>
                            <span translate="Currency"></span>
                        </th>
                        <th>
                            <span translate="Total"></span>
                        </th>
                    </tr>
                </thead>

                <tr>
                    <td class="limit-width">
                        <span translate="The access to use Cleverstaff software"></span>
                    </td>
                    <td>
                        <span class="bold" translate="{{ invoice.users }}"></span>
                    </td>
                    <td>
                        <span class="bold">{{ invoice.months }}</span>
                    </td>
                    <td>
                        <div class="wrapper">
                            <custom-select-new options="currencies" model="invoice.currency"></custom-select-new>
                        </div>
                    </td>
                    <td>
                        <span class="bold">{{ invoice.price() }}</span>
                    </td>
                </tr>
            </table>
        </section>

        <section class="section__block invoice__customer-information">
            <span class="section__tooltip" translate="Please fill in all the fields below"></span>
            <h2 class="title" translate="Customer Information"></h2>
            <form name="customerForm">
                <div class="form-group">
                    <label for="fullName" translate="Representative Full Name"></label>
                    <input
                        id="fullName"
                        ng-class="{ error: customerForm.fullName.$invalid && validation.checking }"
                        ng-model="customer.fullName"
                        name="fullName"
                        type="text"
                        placeholder="{{ 'full_name' | translate }}"
                        required
                    />
                </div>
                <div class="form-group">
                    <label for="position" translate="Representative Position"></label>
                    <input
                        id="position"
                        ng-class="{ error: customerForm.position.$invalid && validation.checking }"
                        ng-model="customer.position"
                        name="position"
                        type="text"
                        placeholder="{{ 'position' | translate }}"
                        required
                    />
                </div>
                <div class="form-group">
                    <label for="companyName" translate="Company Name"></label>
                    <input
                        id="companyName"
                        ng-class="{ error: customerForm.companyName.$invalid && validation.checking }"
                        ng-model="customer.companyName"
                        name="companyName"
                        type="text"
                        placeholder="{{ 'Company Name' | translate }}"
                        required
                    />
                </div>
                <div class="form-group">
                    <label for="companyId" translate="Company Registration short"></label>
                    <input
                        id="companyId"
                        ng-class="{ error: customerForm.companyId.$invalid && validation.checking }"
                        ng-model="customer.companyId"
                        name="companyId"
                        type="text"
                        placeholder="{{ 'Company Registration' | translate }}"
                        ng-required="invoice.currency != 'UAH'"
                    />
                </div>
                <div ng-show="invoice.currency == 'UAH'" class="form-group">
                    <label for="companyId" translate="Company EDRPOU"></label>
                    <input
                        id="companyEDRPOU"
                        ng-class="{ error: customerForm.companyEDRPOU.$invalid && validation.checking }"
                        ng-model="customer.companyEDRPOU"
                        name="companyEDRPOU"
                        type="text"
                        placeholder="{{ 'EDRPOU placeholder' | translate }}"
                        required
                    />
                </div>
                <div class="form-group">
                    <label for="address" translate="Address"></label>
                    <input
                        id="address"
                        ng-class="{ error: customerForm.address.$invalid && validation.checking }"
                        ng-model="customer.address"
                        name="address"
                        type="text"
                        placeholder="{{ 'Address' | translate }}"
                        required
                    />
                </div>
                <div class="form-group">
                    <label for="city" translate="city"></label>
                    <input
                        id="city"
                        ng-class="{ error: customerForm.city.$invalid && validation.checking }"
                        ng-model="customer.city"
                        name="city"
                        type="text"
                        placeholder="{{ 'city' | translate }}"
                        required
                    />
                </div>
                <div class="form-group">
                    <label for="country" translate="country"></label>

                    <custom-select-new
                        id="country"
                        placeholder="'Choose a country first'"
                        options="countries"
                        name="country"
                        model="customer.country"
                        disabled="countryDisabled"
                    ></custom-select-new>
                </div>
                <div class="form-group">
                    <label for="postalCode" translate="Postal Code"></label>
                    <input
                        id="postalCode"
                        ng-class="{ error: customerForm.postalCode.$invalid && validation.checking }"
                        ng-model="customer.postalCode"
                        name="postalCode"
                        type="text"
                        placeholder="{{ 'Postal Code' | translate }}"
                        required
                    />
                </div>
                <div ng-show="customerForm.$invalid && validation.checking" class="form-group error">
                    <span
                        ng-show="validation.invalidFields.length === 1"
                        class="error_message"
                        translate="Please fill in the field"
                        translate-values="{ name: validation.invalidFields[0]}"
                    ></span>
                    <span ng-show="validation.invalidFields.length > 1" class="error_message" translate="Please fill all the fields"></span>
                </div>
            </form>
        </section>

        <section class="invoice__generate">
            <a id="downloadInvoice" style="display: none" href=""></a>
            <button ng-click="generateInvoice()" class="btn-green" translate="Generate invoice"></button>
        </section>
    </div>
</div>
