<div class="candidates-search-component">
    <div class="candidates-search-component_left-side">
        <div class="switcher-wrapper">
            <div class="switcher-hint">
                <i
                    class="hint-info-icon__grey"
                    tooltip-placement="bottom-left"
                    tooltip-class="tooltip-info-black"
                    uib-tooltip-html="$root.tooltips.boolSearchInfo"
                    aria-hidden="true"
                ></i>
                <label class="candidates-switcher-label" ng-class="{ active: !vm.search.fields.matchType.getValue() }">
                    {{ 'Search by full words' | translate }}
                </label>
            </div>

            <toggle-component
                class="search-type-toggle"
                always-checked-style="true"
                on-change="(vm.changeSearchType)"
                checked="vm.search.fields.matchType.value"
            ></toggle-component>

            <div class="switcher-hint">
                <i
                    class="hint-info-icon__grey"
                    tooltip-placement="bottom"
                    tooltip-class="tooltip-info-black"
                    uib-tooltip="{{ 'Search may take more than 10 seconds' | translate }}"
                    aria-hidden="true"
                ></i>
                <label class="candidates-switcher-label" ng-class="{ active: vm.search.fields.matchType.getValue() }">{{ 'Partial match' | translate }}</label>
            </div>
        </div>

        <div class="candidates-search-vertical-divider"></div>

        <form class="candidates-search-input-wrapper" ng-submit="vm.triggerSearch()">
            <span ng-click="vm.triggerSearch()" class="search-icon search-icon__search"></span>
            <input
                class="candidates-search-input"
                type="text"
                placeholder="{{ 'Search by candidates' | translate }}"
                ng-model="vm.search.fields.boolean.value"
            />
            <div ng-show="vm.search.fields.boolean.value" class="clear-input-icon-wrapper">
                <img
                    ng-if="vm.search.fields.boolean.value.length"
                    ng-click="vm.search.fields.boolean.value = '';vm.isTriggerSearchAll && vm.triggerSearch()"
                    class="clear-input-icon"
                    src="/images/redesign/svg-icons/close.svg"
                    alt=""
                />
            </div>
        </form>
    </div>

    <span class="candidates-search-total-found">{{ 'found' | translate }} {{ vm.candidatesCount }}</span>

    <div class="candidates-search-component_right-side">
        <button-with-icon
            ng-click="vm.toggleAdvancedSearch()"
            class-name="'search-button'"
            type="'secondary'"
            text="'Advanced search' | translate"
            icon-name="'filter'"
        ></button-with-icon>
    </div>
</div>

<div id="candidates-filters-section" ng-class="{ 'candidates-filters-border': vm.candidatesPageView === 'list' }">
    <div class="sort-section">
        <checkbox-component
            ng-if="vm.candidatesCount"
            class="select-all"
            on-change="(vm.$scope.$parent.pushAllCandidatesToVacancy)"
            is-checked="vm.$scope.$parent.checkAllCandidates"
            title="{{ 'Select all-v3' | translate }}"
        ></checkbox-component>
        <div ng-if="vm.$scope.$parent.candidatesAddToVacancyIds.length" class="action-buttons">
            <wandify-candidates-list-update-profile
                candidates-ids="vm.$scope.$parent.candidatesAddToVacancyIds"
                candidates-length="vm.$scope.$parent.candidatesAddToVacancyIds.length"
            ></wandify-candidates-list-update-profile>

            <wandify-candidates-list-update-contacts
                candidates-ids="vm.$scope.$parent.candidatesAddToVacancyIds"
                candidates-length="vm.$scope.$parent.candidatesAddToVacancyIds.length"
            ></wandify-candidates-list-update-contacts>

            <icon-button
                ng-if="$root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter'"
                ng-click="vm.$scope.$parent.toCreateSms()"
                class="hidden_laptop"
                icon-name="'sms'"
                colorful-icon="true"
                title="{{ $root.me.orgParams.alphaSms !== 'Y' ? ('alphasms' | translate) : ('Send SMS' | translate) }}"
            ></icon-button>

            <icon-button
                ng-if="$root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter'"
                ng-click="vm.$scope.$parent.toCreateMailing(vm.$scope.$parent.candidatesAddToVacancyIds.length > 15)"
                class="hidden_laptop"
                icon-name="'envelope-light'"
                title="{{ 'Send letter' | translate }}"
                disabled="vm.$scope.$parent.candidatesAddToVacancyIds.length > 15"
            ></icon-button>

            <icon-button
                ng-if="$root.me.recrutRole != 'client'"
                ng-click="vm.$scope.$parent.openGdprModal()"
                icon-name="'paper'"
                title="{{ 'Send mass gdpr-request' | translate }}"
            ></icon-button>

            <icon-button
                ng-click="vm.$scope.$parent.showModalAddCommentToCandidate();"
                icon-name="'comment'"
                title="{{ 'add_comment_candidates' | translate : { amount: vm.$scope.$parent.candidatesAddToVacancyIds.length } }}"
            ></icon-button>

            <icon-button
                ng-show="!showTagsForMass"
                ng-click="vm.$scope.$parent.showTagsForMassModal()"
                icon-name="'tag'"
                title="{{
                    vm.candidatesAddToVacancyIds.length === 1
                        ? ('Add tags to candidate1' | translate : { amount: vm.$scope.$parent.candidatesAddToVacancyIds.length })
                        : ('Add tags to candidate' | translate : { amount: vm.$scope.$parent.candidatesAddToVacancyIds.length })
                }}"
            ></icon-button>

            <icon-button
                ng-click="vm.$scope.$parent.showAddCandidatesInVacancy()"
                icon-name="'add-to-vacancy'"
                title="{{
                    (vm.candidatesAddToVacancyIds.length > 1 ? 'Add candidates to vacancy' : 'Add candidate to vacancy')
                        | translate
                            : {
                                  amount: vm.$scope.$parent.candidatesAddToVacancyIds.length,
                                  name: addCandidateChangeStage[0].fullName
                              }
                }}"
            ></icon-button>

            <icon-button
                ng-if="$root.me.recrutRole !== 'client'"
                ng-click="vm.$scope.$parent.openCandidatesChangeModal('responsible')"
                icon-name="'change-responsible'"
                title="{{ 'Change responsible' | translate }}"
            ></icon-button>

            <icon-button
                ng-if="$root.me.recrutRole !== 'client'"
                ng-click="vm.$scope.$parent.openCandidatesChangeModal('origin')"
                class="icon-without-filter"
                icon-name="'globe-arrow'"
                title="{{ 'Change the candidates source' | translate }}"
            ></icon-button>

            <icon-button
                ng-if="$root.me.recrutRole !== 'client'"
                ng-click="vm.$scope.$parent.openCandidatesChangeModal('position')"
                class="icon-without-filter"
                icon-name="'suitcase-arrow'"
                title="{{ 'Change the desired position' | translate }}"
            ></icon-button>

            <div id="mailing-info-сandidates" class="mailing-info hidden">
                <span>{{ 'Maximum number of recipients - 15' | translate }}</span>
            </div>

            <icon-button
                ng-if="$root.me.recrutRole != 'client'"
                ng-click="vm.$scope.$parent.deleteCandidatesModal()"
                icon-name="'trash'"
                title="{{ vm.getRemoveCandidatesBtnText(vm.$scope.$parent.candidatesAddToVacancyIds.length) }}"
            ></icon-button>
        </div>

        <div class="right-block">
            <select-single
                class="sort-select"
                placeholder="'Sort by'"
                options="vm.filterSort"
                on-change="(vm.changeSort)"
                selected-value="$root.candidatesSortType"
                path-to-label="'name'"
                path-to-value="'values'"
                translate-options="true"
                is-clearable="false"
                position="'fixed'"
            ></select-single>

            <div class="right-controls">
                <account-scope-toggle
                    translate-func="$root.translate"
                    state="$root.scopeActiveObject.name"
                    on-change="($root.changeScopeVisibility)"
                ></account-scope-toggle>

                <icon-button
                    ng-click="vm.toggleViewType()"
                    class="toggle-list-style-button"
                    icon-name="vm.candidatesPageView === 'table' ? 'rows' : 'square'"
                ></icon-button>
            </div>
        </div>

        <div ng-if="vm.isAnyAppliedFIlter()" class="applied-filters" ng-class="{ 'mock-class': vm.setFitWidth() }">
            <span class="applied-filters__title">{{ 'Search by criteria' | translate }}:</span>

            <div ng-if="$root.scopeActiveObject.name == 'onlyMy'" class="filter filter_border-grey scope-filter centered">
                <span class="filter-name">{{ 'scope' | translate }}: {{ 'only_me1' | translate }}</span>
            </div>

            <div ng-if="$root.scopeActiveObject.name == 'region'" class="filter filter_border-grey scope-filter centered">
                <span class="filter-name">
                    {{ 'scope' | translate }}: {{ 'only' | translate }}
                    <span>{{ $root.candidateActiveScopeParam.value.name }}</span>
                </span>
            </div>

            <div
                class="filter filter_border-grey single-item"
                ng-repeat="candidateState in vm.search.fields.state.value"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'status' | translate }}"
                tooltip-append-to-body="true"
            >
                <div class="centered">
                    <span class="filter-name">{{ candidateState.name | translate }}</span>
                    <img
                        ng-click="vm.resetSingleCandidateState(candidateState.value);vm.triggerSearch()"
                        class="remove-filter__icon"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>
            </div>

            <div
                class="filter filter_border-grey single-item"
                ng-repeat="tag in vm.search.fields.candidateGroup.value"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'Tag' | translate }}"
                tooltip-append-to-body="true"
            >
                <div class="centered">
                    <span class="filter-name">{{ tag.name }}</span>
                    <span ng-click="vm.resetSingleTag(tag.candidateGroupId); vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
            </div>

            <div
                ng-if="vm.search.fields.candidateGroup.noValue"
                class="filter filter_border-grey"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'Tag' | translate }}"
                tooltip-append-to-body="true"
            >
                {{ 'no_value' | translate }}
                <img
                    ng-click="vm.search.fields.candidateGroup.reset({name: 'candidateGroup'});vm.triggerSearch();"
                    class="remove-filter__icon"
                    src="/images/redesign/svg-icons/close.svg"
                    alt=""
                />
            </div>

            <div
                ng-if="!vm.search.fields.candidateGroup.state.isActive"
                ng-click="vm.toggleTagsNoValue(); vm.triggerSearch()"
                class="filter single-item"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'Tag' | translate }}"
                tooltip-append-to-body="true"
            >
                {{ 'no_value' | translate }}
            </div>

            <div
                class="filter filter_border-grey single-item"
                ng-repeat="level in vm.search.fields.roleLevels.value"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'Level' | translate }}"
                tooltip-append-to-body="true"
            >
                <div class="centered">
                    <span class="filter-name">{{ level.label | translate }}</span>
                    <span ng-click="vm.resetSingleRoleLevels(level);vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
            </div>

            <div
                ng-if="vm.search.fields.roleLevels.noValue"
                class="filter filter_border-grey"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'Level' | translate }}"
                tooltip-append-to-body="true"
            >
                {{ 'no_value' | translate }}
                <img
                    ng-click="vm.search.fields.roleLevels.reset({name: 'roleLevels'});vm.triggerSearch();"
                    class="remove-filter__icon"
                    src="/images/redesign/svg-icons/close.svg"
                    alt=""
                />
            </div>

            <div
                ng-if="!responsible.hasOwnProperty('isDivider')"
                class="filter filter_border-grey single-item"
                ng-repeat="responsible in vm.search.fields.responsible.selectedResponsibles"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'responsible' | translate }}"
                tooltip-append-to-body="true"
            >
                <div class="centered">
                    <span class="filter-name">{{ responsible.value.fullName }}</span>
                    <span ng-click="vm.resetSingleResponsibleValue(responsible.value.userId);vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
            </div>

            <div
                class="filter filter_border-grey single-item"
                ng-repeat="gdpr in vm.search.fields.gdpr.selectedStatuses"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ vm.search.fields.gdpr.placeholder | translate }}"
                tooltip-append-to-body="true"
            >
                <div class="centered">
                    <span class="filter-name">{{ gdpr.name | translate }}</span>
                    <span ng-click="vm.resetSingleGdprStatus(gdpr.value);vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
            </div>

            <div
                class="filter filter_border-grey single-item"
                ng-repeat="stage in vm.search.fields.stages.value track by $index"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'interview_status' | translate }}"
                tooltip-append-to-body="true"
            >
                <div class="centered">
                    <span ng-if="stage.customInterviewStateId" class="filter-name">{{ stage.name }}</span>
                    <span ng-if="!stage.customInterviewStateId" class="filter-name">{{ 'interview_status_assoc_full.' + stage.value | translate }}</span>
                    <span ng-click="vm.resetSingleStage($index);vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
            </div>

            <div
                ng-if="
                    field.name !== 'languages' &&
                    field.name !== 'responsible' &&
                    field.name !== 'state' &&
                    field.name !== 'candidateGroup' &&
                    field.name !== 'stages' &&
                    field.name !== 'country' &&
                    field.getLabelValue() &&
                    vm.checkFieldVisibility(field) &&
                    field.name !== 'dateDcTo' &&
                    field.name !== 'dateDmTo' &&
                    field.name !== 'skills' &&
                    field.name !== 'roleLevels'
                "
                class="filter filter_border-grey {{ field.type }}"
                ng-repeat="field in vm.search.fields track by $index"
                ng-switch="field.name"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ field.placeholder | translate }}"
                tooltip-append-to-body="true"
            >
                <div class="centered" ng-switch-when="dateDmFrom">
                    <span class="filter-name">{{ 'from' | translate }} {{ vm.search.fields.dateDmFrom.value | date : 'dd/MM/yyyy' }}</span>
                    <span ng-if="vm.search.fields.dateDmTo.value" class="filter-name">
                        {{ 'to date' | translate }} {{ vm.search.fields.dateDmTo.value | date : 'dd/MM/yyyy' }}
                    </span>
                    <span
                        ng-click="vm.search.fields.dateDmFrom.reset({name: vm.search.fields.dateDmFrom});vm.search.fields.dateDmTo.reset({name: vm.search.fields.dateDmTo}); vm.triggerSearch()"
                        class="remove-filter"
                    >
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
                <div ng-if="!vm.search.fields.dateDmFrom.value" class="centered" ng-switch-when="dateDmTo">
                    <span class="filter-name">{{ 'to date' | translate }} {{ vm.search.fields.dateDmTo.value | date : 'dd/MM/yyyy' }}</span>
                    <span ng-click="vm.search.fields.dateDmTo.reset({name: vm.search.fields.dateDmTo}); vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
                <div ng-if="vm.search.fields.dateDmFrom.value" class="centered" ng-switch-when="dateDmTo"></div>

                <div class="centered" ng-switch-when="dateDcFrom">
                    <span class="filter-name">{{ 'from' | translate }} {{ vm.search.fields.dateDcFrom.value | date : 'dd/MM/yyyy' }}</span>
                    <span ng-if="vm.search.fields.dateDcTo.value" class="filter-name">
                        {{ 'to date' | translate }} {{ vm.search.fields.dateDcTo.value | date : 'dd/MM/yyyy' }}
                    </span>
                    <span
                        ng-click="vm.search.fields.dateDcFrom.reset({name: vm.search.fields.dateDcFrom});vm.search.fields.dateDcTo.reset({name: vm.search.fields.dateDcTo}); vm.triggerSearch()"
                        class="remove-filter"
                    >
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
                <div ng-if="!vm.search.fields.dateDcFrom.value" class="centered" ng-switch-when="dateDcTo">
                    <span class="filter-name">{{ 'to date' | translate }} {{ vm.search.fields.dateDcTo.value | date : 'dd/MM/yyyy' }}</span>
                    <span ng-click="vm.search.fields.dateDcTo.reset({name: vm.search.fields.dateDcTo}); vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
                <div ng-if="vm.search.fields.dateDcFrom.value" class="centered" ng-switch-when="dateDcTo"></div>

                <div class="centered" ng-switch-when="boolean">
                    <span class="filter-name">{{ field.getLabelValue() }}</span>
                    <img ng-click="field.reset({});vm.triggerSearch()" class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                </div>

                <div class="centered" ng-switch-when="date">
                    <span ng-if="field.dateFrom.getLabelValue()" class="filter-name">{{ 'from' | translate }} {{ field.dateFrom.value.value }}</span>
                    <span ng-if="field.dateTo.getLabelValue()" class="filter-name">{{ 'to date' | translate }} {{ field.dateTo.value.value }}</span>
                    <span ng-click="field.reset({});vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>

                <div class="centered" ng-switch-when="industry">
                    <span ng-if="field.noValue">{{ field.getLabelValue() | translate }}</span>
                    <span ng-if="!field.noValue" class="filter-name">{{ 'industries_assoc.' + field.getLabelValue().value | translate }}</span>
                    <img
                        ng-click="field.reset({name: field.name});vm.triggerSearch()"
                        class="remove-filter__icon"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>

                <div class="centered" ng-switch-when="position">
                    <span class="filter-name">{{ field.getLabelValue().label }}</span>
                    <span ng-click="vm.resetPositionField(); vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>

                <div class="centered" ng-switch-when="scorecards">
                    <span class="filter-name">{{ field.getLabelValue().name }}</span>
                    <img
                        ng-click="field.reset({callback: vm.setScorecardsAutocompleterPlaceholderValue});$root.dropdownScoreCardId = null;vm.triggerSearch();"
                        class="remove-filter__icon"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>

                <div class="centered" ng-switch-when="gdpr">
                    <span class="filter-name">{{ field.getLabelValue() }}</span>
                    <span ng-click="field.reset({callback: vm.setGdprAutocompleterPlaceholderValue});vm.triggerSearch();" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>

                <div class="centered" ng-switch-when="origin">
                    <span ng-if="field.noValue" class="filter-name">{{ field.getLabelValue() | translate }}</span>
                    <span ng-if="!field.noValue" class="filter-name">{{ field.getLabelValue().label | translate }}</span>
                    <img
                        ng-click="field.reset({});vm.triggerSearch('scorecards')"
                        class="remove-filter__icon"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>
                <div class="centered" ng-switch-when="employmentType">
                    <span class="filter-name">{{ field.getLabelValue() | translate }}</span>
                    <img
                        ng-click="field.reset({callback: vm.getResetCallback(field)}); vm.triggerSearch()"
                        class="remove-filter__icon"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>
                <div class="centered" ng-switch-when="name">
                    <span class="filter-name">{{ field.getLabelValue() }}</span>
                    <span ng-click="field.reset({});vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>

                <div class="centered" ng-switch-when="surname">
                    <span class="filter-name">{{ field.getLabelValue() }}</span>
                    <span ng-click="field.reset({});vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>

                <div class="centered" ng-switch-when="salaryTo">
                    <span class="filter-name">{{ field.getLabelValue() | translate }} {{ field.salaryCurrency.getLabelValue() }}</span>
                    <span ng-if="!$root.searchNullValues.includes('salary')" ng-click="field.reset({});vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                    <span
                        ng-if="$root.searchNullValues.includes('salary')"
                        ng-click="field.reset({});vm.clearSalary();vm.triggerSearch()"
                        class="remove-filter"
                    >
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
                <div class="centered" ng-switch-when="currentWorkingPlace">
                    <span class="filter-name">{{ field.getLabelValue() | translate }}</span>
                    <img
                        ng-if="!$root.searchNullValues.includes('currentWorkingPlace')"
                        ng-click="field.reset({});vm.triggerSearch()"
                        class="remove-filter__icon"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                    <img
                        ng-if="$root.searchNullValues.includes('currentWorkingPlace')"
                        ng-click="field.reset({});vm.clearCurrentWorkingPlace();vm.triggerSearch()"
                        class="remove-filter__icon"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>
                <div class="centered" ng-switch-when="comment">
                    <span class="filter-name">{{ 'Search_by_comments' | translate }}: {{ field.getLabelValue() }}</span>
                    <span ng-click="field.reset({});vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>

                <div class="centered" ng-switch-when="vacancyId">
                    <span class="filter-name">{{ vm.search.fields.vacancyId.value.label }}</span>
                    <span ng-click="vm.resetVacancyId();field.reset({});vm.triggerSearch();" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>

                <div ng-if="(field.name != 'skills' || field.name !== 'stages') && field.name !== 'country'" class="centered" ng-switch-default>
                    <span class="filter-name">{{ field.getLabelValue() | translate }}</span>
                    <span ng-click="field.reset({callback: vm.getResetCallback(field)}); vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
            </div>

            <div
                ng-if="vm.search.fields.country.value.length"
                class="filter filter_border-grey personal"
                ng-repeat="country in vm.search.fields.country.value track by $index"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'country' | translate }}"
                tooltip-append-to-body="true"
            >
                <div class="centered">
                    <span class="filter-name">{{ country.showName }}</span>
                    <span
                        ng-click="vm.removeCountry($index);vm.search.fields.city.reset({});vm.triggerSearch();$event.stopPropagation();"
                        class="remove-filter"
                    >
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
            </div>

            <!-- prettier-ignore -->
            <div
                ng-if="language.name"
                class="filter filter_border-grey centered"
                ng-repeat="language in vm.search.fields.languages.appliedLanguages track by $index"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'Language' | translate }}"
                tooltip-append-to-body="true"
            >
                <span class="filter-name">{{ language.name }}</span> <span ng-if="language.level">({{ language.level | translate }})</span>
                <span ng-click="vm.removeLanguagesField($index);vm.triggerSearch()" class="remove-filter">
                            <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                        </span>
            </div>

            <div
                ng-if="skill.skill"
                class="filter filter_border-grey personal"
                ng-repeat="skill in vm.search.fields.skills.value track by $index"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'Skill' | translate }}"
                tooltip-append-to-body="true"
            >
                <div class="centered">
                    <span ng-if="skill.skill" class="filter-name">{{ skill.skill.label }}</span>
                    <span ng-if="skill.experience != 'e00_no_experience'" class="filter-name">&nbsp;- {{ skill.experience.label | translate }}</span>
                    <span ng-click="vm.removeSkillField($index);vm.triggerSearch()" class="remove-filter">
                        <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
            </div>

            <div class="filter filter_border-grey general centered" ng-repeat="language in vm.search.fields.languages.selectedLanguages track by $index">
                <span class="filter-name">{{ language.name }} ({{ language.level | translate }})</span>
                <span ng-click="vm.search.fields.languages.removeField($index);vm.triggerSearch($index)" class="remove-filter">
                    <img class="remove-filter__icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                </span>
            </div>

            <div
                ng-if="customField.getLabelValue()"
                class="filter filter_border-grey other"
                ng-repeat="customField in vm.customFieldsSearch.fields"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ customField.placeholder }}"
                tooltip-append-to-body="true"
            >
                <div class="centered">
                    <span ng-if="!customField.noValue" class="filter-name">{{ customField.getLabelValue() }}</span>
                    <span ng-if="customField.noValue" class="filter-name">{{ customField.getLabelValue() | translate }}</span>
                    <img
                        ng-click="customField.reset({name: customField.name});vm.resetCustomField(customField); vm.triggerSearch()"
                        class="item-close-icon"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>
            </div>

            <div
                ng-if="($root.scopeActiveObject.name !== 'region' && $root.scopeActiveObject.name !== 'onlyMy') || search.isAnyFilledField()"
                ng-click="vm.resetVacancyId();vm.removeAllLanguages();vm.removeAllSkills();vm.resetFields(autocompletes);"
            >
                <a class="remove-all-button" type="button">
                    <img class="remove-all-icon" src="/images/redesign/svg-icons/trash.svg" alt="" />
                    {{ 'Remove all' | translate }}
                </a>
            </div>

            <div ng-if="$root.isReducedNumberOfCandidates">
                <span ng-show="$root.currentLang == 'ru' && $root.isReducedNumberOfCandidates">
                    {{ 'reduced the number of the search criteria' | translate }}
                </span>
                <span ng-show="$root.currentLang == 'ua' && $root.isReducedNumberOfCandidates">
                    {{ 'reduced the number of the search criteria' | translate }}
                </span>
                <span ng-show="$root.currentLang == 'en' && $root.isReducedNumberOfCandidates">
                    {{ 'reduced the number of the search criteria' | translate }}
                </span>
                <span ng-show="$root.currentLang == 'pl' && $root.isReducedNumberOfCandidates">
                    {{ 'reduced the number of the search criteria' | translate }}
                </span>
            </div>
            <div ng-if="$root.candidatesNotFind">
                <span ng-show="$root.currentLang == 'en' && $root.candidatesNotFind">
                    {{ 'we could not find candidates in your database' | translate }}
                </span>
                <span ng-show="$root.currentLang == 'ua' && $root.candidatesNotFind">
                    {{ 'we could not find candidates in your database' | translate }}
                </span>
                <span ng-show="$root.currentLang == 'ru' && $root.candidatesNotFind">
                    {{ 'we could not find candidates in your database' | translate }}
                </span>
                <span ng-show="$root.currentLang == 'pl' && $root.candidatesNotFind">
                    {{ 'we could not find candidates in your database' | translate }}
                </span>
            </div>
        </div>
    </div>
</div>

<candidate-advanced-search-component
    id="advanced-search-component"
    ng-if="vm.isToggleAdvancedSearch"
    is-trigger-search-all="vm.isTriggerSearchAll"
    is-trigger-search="vm.isTriggerSearch"
    is-toggle-advanced-search="vm.isToggleAdvancedSearch"
    custom-fields-search="vm.customFieldsSearch"
    autocompletes="vm.autocompletes"
    clear-tags="vm.clearTags"
></candidate-advanced-search-component>
