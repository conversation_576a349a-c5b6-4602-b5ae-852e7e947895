<h4 class="adv-search-filters_column-head">{{ 'Personal br Information' | translate }}</h4>

<div id="personal-fields-column" ng-if="vm.search.getFieldsStateByType('personal', false) && vm.model.criteria.personal" class="adv-search-filters_column-body">
    <div ng-if="vm.search.fields.name.state.isSelected">
        <div class="title-with-hint">
            <div class="adv-search-filters_filter-label title-with-hint__title">{{ 'Name' | translate }}</div>
            <i
                class="hint-info-icon__grey"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'Name transliteration' | translate }}"
                aria-hidden="true"
            ></i>
        </div>
        <input-component
            placeholder="'Name'"
            on-change="(vm.onChangeInputField)"
            value="vm.search.fields.name.value || ''"
            additional-arg="vm.search.fields.name"
        ></input-component>
    </div>

    <div ng-if="vm.search.fields.surname.state.isSelected">
        <div class="title-with-hint">
            <div class="adv-search-filters_filter-label title-with-hint__title">{{ 'Surname' | translate }}</div>
            <i
                class="hint-info-icon__grey"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'Surname transliteration' | translate }}"
                aria-hidden="true"
            ></i>
        </div>
        <input-component
            placeholder="'Surname'"
            on-change="(vm.onChangeInputField)"
            value="vm.search.fields.surname.value || ''"
            additional-arg="vm.search.fields.surname"
        ></input-component>
    </div>

    <!--    Country-->
    <div ng-show="vm.search.fields.country.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'country' | translate }}</h5>
        <select-multi-virtualized
            current-lang="$root.currentLang"
            show-select-all="false"
            placeholder="'country'"
            options="vm.countries"
            selected-values="vm.search.fields.country.value"
            is-searchable="true"
            on-change="(vm.onChangeCountry)"
            position="'fixed'"
        ></select-multi-virtualized>
    </div>

    <!--    City-->
    <div ng-show="vm.search.fields.city.state.isSelected" class="field city-field">
        <h5 class="adv-search-filters_filter-label">{{ 'city' | translate }}</h5>
        <select-single-virtualized
            current-lang="$root.currentLang"
            placeholder="'city'"
            options="vm.filteredCities"
            selected-value="vm.search.fields.city.value"
            is-searchable="true"
            on-change="(vm.onChangeCity)"
            translate-options="false"
            disabled="!vm.search.fields.country.value || vm.search.fields.country.value.length > 1"
            position="'fixed'"
        ></select-single-virtualized>
    </div>

    <div ng-if="vm.sex && vm.search.fields.sex.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ vm.search.fields.sex.placeholder | translate }}</h5>
        <select-single
            placeholder="vm.search.fields.sex.placeholder"
            options="vm.sex"
            on-change="(vm.onChangeSelectField)"
            selected-value="vm.search.fields.sex.value"
            additional-arg="vm.search.fields.sex"
            path-to-label="'sex'"
            translate-options="true"
            position="'fixed'"
        ></select-single>
    </div>

    <!--    Age-->
    <div ng-if="vm.search.fields.date.state.isSelected && vm.ageSearchFrom && vm.ageSearchTo" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'Age' | translate }}</h5>
        <div class="age-block">
            <select-single
                placeholder="vm.search.fields.date.dateFrom.placeholder"
                options="vm.ageSearchFrom"
                on-change="(vm.changeAgeMin)"
                selected-value="vm.search.fields.date.dateFrom.value"
                position="'fixed'"
            ></select-single>

            <select-single
                placeholder="vm.search.fields.date.dateTo.placeholder"
                options="vm.ageSearchTo"
                on-change="(vm.changeAgeMax)"
                selected-value="vm.search.fields.date.dateTo.value"
                position="'fixed'"
            ></select-single>
        </div>
    </div>

    <div ng-if="vm.search.fields.withPersonalContacts.state.isSelected && $root.me.recrutRole !== 'client'" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'Contacts' | translate }}</h5>
        <select-single
            placeholder="'Contacts'"
            options="vm.personalContacts"
            on-change="(vm.onChangeSelectField)"
            selected-value="vm.search.fields.withPersonalContacts.value"
            additional-arg="vm.search.fields.withPersonalContacts"
            translate-options="true"
            position="'fixed'"
        ></select-single>
    </div>

    <div ng-if="vm.search.fields.state.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'Status' | translate }}</h5>
        <select-multi-virtualized
            placeholder="'status'"
            select-all-text="'Select all-v2' | translate"
            options="vm.status"
            translate-options="true"
            selected-values="vm.search.fields.state.value"
            on-change="(vm.onChangeSelectField)"
            additional-arg="vm.search.fields.state"
            path-to-label="'name'"
            path-to-key="'name'"
            position="'fixed'"
            is-searchable="false"
        ></select-multi-virtualized>
    </div>
</div>
