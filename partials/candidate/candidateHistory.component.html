<div class="history-outer">
    <div class="history-outer__body">
        <hr ng-show="vm.history.length" class="history-outer__body-divider" data-content="{{ vm.history[0].dm | dateFormatSimple2 | capitalize }}" />
        <div
            class="history-outer__body-items"
            ng-class="{ attachedComment: history.attached }"
            ng-repeat-start="history in vm.history track by $index"
            ng-switch="history.type"
        >
            <div class="hidden" ng-switch-default></div>
            <div class="action-wrapper" ng-switch-when="gpt_candidate_edit">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/gpt_candidate_edit.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_remove_candidate_link">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_remove_candidate_link.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="pdcs_email_sent">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/pdcs_email_sent.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="pdcs_email_not_sent">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/pdcs_email_not_sent.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_test_send">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_test_send.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_sent_email">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_sent_email.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_test_passed">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_test_passed.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_edit_from_recall">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_edit_from_recall.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_excel">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_excel.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_excel">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_excel.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_archive">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_archive.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_archive">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_archive.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_add_sent_email">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_add_sent_email.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_edit_sent_email">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_edit_sent_email.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_manually">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_manually.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_edit">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_edit.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_edit_ai_advice">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_edit_ai_advice.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_merge">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_merge.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_merge2">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_merge2.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_file">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_file.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_advice">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_advice.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_cvlv">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_cvlv.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_jobkg">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_jobkg.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_grc">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_grc.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_rabotauz">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_rabotauz.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_email">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_email.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_edit_from_email">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_edit_from_email.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_hh">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_hh.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_dou">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_dou.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_linkedin">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_linkedin.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_rabotaua">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_rabotaua.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_jobstutby">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_jobstutby.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_superjob">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_superjob.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_workua">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_workua.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_djinni">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_djinni.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_delucru">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_delucru.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_delucru">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_delucru.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_rabotamd">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_rabotamd.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_rabotamd">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_rabotamd.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_link">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_link.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_olx">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_olx.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_olx">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_olx.html'"></ng-include>
            </div>

            <div class="action-wrapper" ng-switch-when="candidate_merge_group">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_merge_group.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_edit_group">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_edit_group.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_to_group">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_to_group.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_change_state">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_change_state.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_message">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_message.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="attached_message">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/attached-message.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="unattached_message">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/unattached_message.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_message_for_hm">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_message.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="task_create">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/task_create.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="task_change">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/task_change.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_add_show_in_candidate">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_add_show_in_candidate.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_interview_remove">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_interview_remove.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_remove_file">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_remove_file.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="remove_candidate_group">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/remove_candidate_group.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_remove_from_group">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_remove_from_group.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_remove_link">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_remove_link.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_set_responsible">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_set_responsible.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_workua">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_workua.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_dou">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_dou.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_pracuj">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_pracuj.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_pracuj">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_pracuj.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_djinni">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_djinni.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_cvlv">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_cvlv.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_jobkg">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_jobkg.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_rabotauz">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_rabotauz.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_hh">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_hh.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_grc">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_grc.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_linkedin">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_linkedin.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_rabotaua">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_rabotaua.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_superjob">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_superjob.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_file">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_file.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_was_sent_to_client">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_was_sent_to_client.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidates_was_sent_to_client">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidates_was_sent_to_client.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="client_add">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/client_add.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="client_edit">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/client_edit.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="client_change_state">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/client_change_state.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="client_message">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/client_message.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="contact_client_add">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/contact_client_add.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="contact_client_edit">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/contact_client_edit.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_add">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_add.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_add_from_advice">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_add_from_advice.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_add_show_in_candidate">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_add_show_in_candidate.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_add_from_workUa">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_add_from_workUa.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_add_from_delucru">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_add_from_delucru.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_add_from_rabotaUa">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_add_from_rabotaUa.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_add_from_djinni">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_add_from_djinni.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_add_from_hh">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_add_from_hh.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_add_from_grc">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_add_from_grc.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_edit">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_edit.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_autoaction_test_send_failed">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_autoaction_test_send_failed.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_autoaction_test_send">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_autoaction_test_send.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_autoaction_mail_send_failed">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_autoaction_mail_send_failed.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_autoaction_mail_send">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_autoaction_mail_send.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_edit_date">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_edit_date.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_message">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_message.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" style="padding-right: 0" ng-switch-when="interview_remove">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_remove.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="sent_candidate_to_client">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/sent_candidate_to_client.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="set_interview_status">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/set_interview_status.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="task_change_status">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/task_change_status.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="vacancy_add">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/vacancy_add.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="vacancy_add_file">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/vacancy_add_file.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="vacancy_change_state">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/vacancy_change_state.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="vacancy_edit">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/vacancy_edit.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="vacancy_message">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/vacancy_message.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="vacancy_remove_file">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/vacancy_remove_file.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="vacancy_remove_responsible">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/vacancy_remove_responsible.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="vacancy_set_responsible">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/vacancy_set_responsible.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="vacancy_change_responsible">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/vacancy_change_responsible.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_edit_pdcs">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_edit_pdcs.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="send_mailing">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/mailing-sent-candidate.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="video_interview_sent">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/video_interview_sent.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="video_interview_pass">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/video_interview_pass.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="test_sent_after_recall">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/test_sent_after_recall.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="test_result_auto_sent">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/test_result_auto_sent.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="scorecard_result_save">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/scorecard_result_save.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="scorecard_result_delete">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/scorecard_result_delete.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_edit_contacts">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_edit_contacts.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_linkedinNew">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_linkedinNew.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="add_candidate_to_bamboo_hr">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/add_candidate_to_bamboo_hr.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidates_change_position">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidates_change_position.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidates_change_origin">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidates_change_origin.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="employee_deleted">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/employee_deleted.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_jobCannon">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_add_from_jobCannon.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_jobCannon">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_jobCannon.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_wandify">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/candidate/actions/wandify/candidate_add_from_wandify.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_add_from_wandify_recommendation">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/candidate/actions/wandify/candidate_add_from_wandify_recommendation.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_update_from_wandify">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_update_from_wandify.html'"></ng-include>
            </div>

            <div class="action-wrapper" ng-switch-when="candidate_profile_updated_wandify">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_profile_updated_wandify.html'"></ng-include>
            </div>

            <div class="action-wrapper" ng-switch-when="candidate_profile_not_updated_wandify">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_profile_not_updated_wandify.html'"></ng-include>
            </div>

            <div class="action-wrapper" ng-switch-when="candidate_contacts_updated_wandify">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_contacts_updated_wandify.html'"></ng-include>
            </div>

            <div class="action-wrapper" ng-switch-when="candidate_contacts_not_updated_wandify">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_contacts_not_updated_wandify.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="alpha_sms_success">
                <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                <ng-include src="'partials/history/candidate/alpha_sms_success.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="alpha_sms_success_add">
                <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                <ng-include src="'partials/history/candidate/alpha_sms_success_add.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="alpha_sms_success_edit">
                <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                <ng-include src="'partials/history/candidate/alpha_sms_success_edit.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="alpha_sms_error">
                <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                <ng-include src="'partials/history/candidate/alpha_sms_error.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="empty_phone">
                <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                <ng-include src="'partials/history/candidate/empty_phone.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="empty_phone_add">
                <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                <ng-include src="'partials/history/candidate/empty_phone_add.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="empty_phone_edit">
                <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                <ng-include src="'partials/history/candidate/empty_phone_edit.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_time_not_found">
                <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_time_not_found.html'"></ng-include>
            </div>
        </div>
        <hr
            ng-show="history.dateDivider"
            class="history-outer__body-divider"
            data-content="{{ history.dateDivider | dateFormatSimple2 | translate }}"
            ng-repeat-end=""
        />
    </div>
    <div class="history-outer__footer">
        <pagination-component
            ng-if="vm.objectSize > 15"
            translate-func="$root.translate"
            current-lang="$root.currentLang"
            total-pages="vm.allPageCount"
            total-elements="vm.objectSize"
            current-page="vm.pageNumber"
            current-amount-of-elements="vm.pageCount"
            on-change-page="(vm.changePage)"
            on-change-amount-of-elements="(vm.changeAmountOfElements)"
            on-show-more="(vm.showMore)"
            is-show-more-mode="vm.isShowMore"
        ></pagination-component>
    </div>
</div>
