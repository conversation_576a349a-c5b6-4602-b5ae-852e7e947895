<div class="history-outer">
    <div class="history-outer__body">
        <hr ng-show="vm.history.length" class="history-outer__body-divider" data-content="{{ vm.history[0].dm | dateFormatSimple2 | capitalize }}" />
        <div
            class="history-outer__body-items"
            ng-class="{ attachedComment: history.attached }"
            ng-repeat-start="history in vm.history track by $index"
            ng-switch="history.type"
        >
            <div class="hidden" ng-switch-default></div>
            <div class="action-wrapper" ng-switch-when="candidate_message">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_message.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="candidate_message_for_hm">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/candidate_message.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_message">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_message.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
            <div class="action-wrapper" ng-switch-when="interview_add">
                <ng-include src="'partials/history/full-history/personal.html'"></ng-include>
                <ng-include src="'partials/history/candidate/interview_add.html'"></ng-include>
                <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
            </div>
        </div>
        <hr
            ng-show="history.dateDivider"
            class="history-outer__body-divider"
            data-content="{{ history.dateDivider | dateFormatSimple2 | translate }}"
            ng-repeat-end=""
        />
    </div>
    <div class="history-outer__footer">
        <pagination-component
            ng-if="vm.objectSize > 15"
            class="vacancies-pagination"
            translate-func="$root.translate"
            current-lang="$root.currentLang"
            total-pages="vm.allPageCount"
            total-elements="vm.objectSize"
            current-page="vm.pageNumber"
            current-amount-of-elements="vm.pageCount"
            on-change-page="(vm.changePage)"
            on-change-amount-of-elements="(vm.changeAmountOfElements)"
            on-show-more="(vm.showMore)"
            is-show-more-mode="vm.isShowMore"
        ></pagination-component>
    </div>
</div>
