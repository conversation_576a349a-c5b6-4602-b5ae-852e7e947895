<div class="templates-component">
    <h3 ng-click="vm.setDefaultTemplate()" class="templates-component__title">
        {{ 'Return default template text' | translate }}
    </h3>
    <div style="position: relative">
        <div ng-if="template.type !== 'candidateCreateInterviewNotification'" ng-repeat="template in vm.emailTemplates track by $index">
            <div class="custom-radio-container" ng-class="{ 'custom-radio-container__last': $index === vm.emailTemplates.length - 1 }">
                <input ng-click="vm.setTemplate(template)" class="custom-radio" type="radio" name="candidateTemplate" />
                <span class="checkmark"></span>
                <span class="custom-radiobutton__text">{{ template.name | translate }}</span>
                <div class="" style="position: relative; display: inline-block">
                    <i class="info-icon tooltip-icon" ng-mouseover="vm.hoverInfoShow($index)" ng-mouseleave="vm.hoverInfoHidden()"></i>
                    <div
                        ng-show="vm.hoverItemIndex === $index && vm.visibility2"
                        class="hoverExtension hoverExtensionMailing"
                        ng-class="{
                            'hoverExtension-long': template.name !== 'Принят оффер' && $root.currentLang !== 'en',
                            hoverExtension_acceptOffer: template.name === 'Accept offer'
                        }"
                    >
                        {{ vm.message | translate }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
