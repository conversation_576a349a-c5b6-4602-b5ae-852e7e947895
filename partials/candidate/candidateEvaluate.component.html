<div class="candidate-evaluate-block wrappers">
    <div class="score-card first-block-on-page" ng-repeat="(vacancyId, vacancy) in vm.vacancies">
        <div class="score-card-item" data-vacancyId="{{ vacancy.vacancyId }}">
            <div class="form-title-main" style="display: flex; align-items: center">
                <a class="position" ng-class="{ long: vacancy.position.length > 25 }" href="#/vacancies/{{ vacancy.localId }}">{{ vacancy.position }}&nbsp;</a>
                <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})&nbsp;</span>
                <div class="averageResult">
                    <span ng-if="vacancy.averageResult" class="score">{{ vacancy.averageResult }}</span>
                    <img ng-if="vacancy.showFullStar && vacancy.averageResult" class="full" src="images/redesign/svg-icons/star-filled.svg" alt="" />
                    <img ng-if="!vacancy.showFullStar && vacancy.averageResult" class="half" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                    <img ng-if="!vacancy.averageResult" class="empty" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                </div>
            </div>
            <div
                ng-if="(vacancy.averageResult && $root.me.recrutRole === 'admin') || $root.me.recrutRole === 'recruter'"
                ng-click="vm.exportOneScorecard(vacancy)"
                class="export-scorecard hidden_laptop"
                tooltip-class="save-to-excel-tooltip"
                uib-tooltip="{{ 'scoreCards.exportOneScorecardsXls' | translate }}"
            >
                <img src="/images/sprite/excel-scorecard.png" alt="" />
            </div>
        </div>
        <div class="score-card-hr"></div>
        <div class="score-card-item-schemas">
            <div class="schemas" ng-repeat="card in vacancy.scorecards" data-cardId="{{ card.id }}" data-vacancyId="{{ vacancy.vacancyId }}">
                <div class="schemas__wrapper" ng-class="{ bottomBorder: !card.showInfo }">
                    <div class="schemas__name">
                        <span ng-show="card.name === 'Default' || card.name === 'Old Default'" class="name">
                            {{ card.name | translate }}
                        </span>
                        <span class="name" ng-hide="card.name === 'Default' || card.name === 'Old Default'">
                            {{ card.name }}
                        </span>
                        <div class="averageResult cardsScore">
                            <span ng-if="card.averageResult.mainScore || card.averageResult.votersCount" class="score">
                                {{ card.averageResult.mainScore.toString().substring(0, 3) }}
                            </span>
                            <img
                                ng-if="card.averageResult.mainScore || card.averageResult.votersCount"
                                class="full"
                                src="images/redesign/svg-icons/star-filled.svg"
                                alt=""
                            />
                            <img
                                ng-if="!card.averageResult.mainScore && !card.averageResult.votersCount"
                                class="empty"
                                src="images/redesign/svg-icons/star-outline.svg"
                                alt=""
                            />
                            <div
                                class="counter"
                                ng-style="{
                                    'min-width': !card.averageResult.votersCount && vm.windowWidth > 575 ? '115px' : null
                                }"
                            >
                                <span ng-if="!card.averageResult.votersCount" class="no-voters">({{ 'no evaluations' | translate }})</span>
                                <span ng-if="card.averageResult.votersCount" class="voters">({{ card.averageResult.votersCount }})</span>
                            </div>
                        </div>
                    </div>
                    <div class="schemas__buttons">
                        <span ng-if="card.results.iHaveAlreadyEvaluate" class="schemas__buttons__title">
                            {{ 'You have already evaluated this candidate' | translate }}
                        </span>
                        <div class="schemas__buttons__change">
                            <button-component
                                ng-click="vm.openEvaluatePopup(card.id, card.results.iHaveAlreadyEvaluate, card.vacancy)"
                                text="card.results.iHaveAlreadyEvaluate ? ('Change' | translate) : ('Evaluate' | translate)"
                                size="'small'"
                            ></button-component>
                            <img
                                id="card-schemas"
                                ng-click="vm.openScoreCardEvaluationInfo(card, $event)"
                                ng-class="{ rotate: card.showInfo }"
                                src="images/redesign/svg-icons/chevron-down.svg"
                                data-cardId="{{ card.id }}"
                                data-vacancyId="{{ card.vacancyId }}"
                                uib-tooltip="{{ !card.results || card.results.length === 0 ? ('WithoutScores' | translate) : '' }}"
                                tooltip-append-to-body="true"
                                tooltip-placement="top-right"
                                tooltip-class="tooltip-info-black evaluate-tip"
                                alt=""
                            />
                        </div>
                    </div>
                </div>

                <div ng-show="card.showInfo && card.results.length > 0" class="schemas__info">
                    <div class="first">
                        <div class="rowScard name">
                            <div class="score-name">
                                {{ 'Scorecards and criteria' | translate }}
                            </div>
                            <div class="score-schemas" ng-repeat-start="schema in card.schema.blocks">
                                <span ng-if="card.name === 'Default' || card.name === 'Old Default'" class="question" title="{{ schema.label }}">
                                    {{ schema.label | translate }}
                                </span>
                                <span ng-if="card.name !== 'Default' && card.name !== 'Old Default'" class="question" title="{{ schema.label }}">
                                    {{ schema.label }}
                                </span>
                            </div>
                            <div class="score-questions" ng-repeat-end ng-repeat="ques in schema.questions" data-id="{{ ques.id }}">
                                <div class="main-block">
                                    <span ng-if="card.name === 'Default' || card.name === 'Old Default'" class="question" title="{{ ques.label }}">
                                        {{ ques.label | translate }}
                                    </span>
                                    <span ng-if="card.name !== 'Default' && card.name !== 'Old Default'" class="question" title="{{ ques.label }}">
                                        {{ ques.label }}
                                    </span>
                                    <div ng-if="ques.comments.length > 0" class="score-comment">
                                        <span>{{ ques.comments.length }}</span>
                                        <img
                                            ng-click="ques.showComment = !ques.showComment; vm.openComments($event, ques.showComment)"
                                            ng-class="{ selected: ques.showComment }"
                                            src="images/redesign/svg-icons/comment.svg"
                                        />
                                    </div>
                                </div>
                                <div ng-show="ques.showComment" class="comment-block">
                                    <div class="comment-block__item" ng-repeat="comment in ques.comments">
                                        <div class="user-info">
                                            <div class="name">
                                                <a ng-if="$root.useAmericanNameStyle" href="#/users/{{ comment.userId }}">
                                                    {{ comment.fullNameEn }}
                                                </a>
                                                <a ng-if="!$root.useAmericanNameStyle" href="#/users/{{ comment.userId }}">
                                                    {{ comment.fullName }}
                                                </a>
                                            </div>
                                            <div class="date">
                                                <span title="{{ comment.editDate | dateFormatSimpleExcelHistory : true | translate }}">
                                                    {{ comment.editDate | dateTimeFrom | translate }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="comment-info">
                                            <span>{{ comment.comment }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="score-results">
                                <span>{{ 'GeneralScore' | translate }}</span>
                                <div ng-if="card.allComments.length > 0" class="score-comment">
                                    <span>{{ card.allComments.length }}</span>
                                    <img
                                        ng-click="card.showComment = !card.showComment;"
                                        ng-class="{ selected: card.showComment }"
                                        src="images/redesign/svg-icons/comment.svg"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="second">
                        <div class="second__wrapper">
                            <div class="rowScard">
                                <div class="score-name average">
                                    {{ 'MediumScore' | translate }}
                                </div>
                                <div class="score-schemas" ng-repeat-start="schema in card.results[0].schema.blocks">
                                    <span>{{ schema.fullAverage || '-' }}</span>
                                </div>
                                <div class="score-questions" ng-repeat-end ng-repeat="ques in schema.questions">
                                    <span>{{ ques.questionsAverage || '-' }}</span>
                                </div>
                                <div class="score-results">
                                    {{ card.averageResult.mainScore.toString().substring(0, 3) }}
                                </div>
                            </div>
                            <div class="rowScard users" ng-repeat="user in card.results">
                                <div class="score-name custom-response">
                                    <div class="custom">
                                        <responsible-person
                                            avatar-id="user.user.avatarId"
                                            full-name="$root.useAmericanNameStyle ? user.user.fullNameEn : user.user.fullName"
                                            user-id="user.user.userId"
                                        ></responsible-person>
                                    </div>
                                </div>
                                <div class="score-schemas" ng-repeat-start="schema in user.schema.blocks">
                                    <span>{{ schema.average || '-' }}</span>
                                </div>
                                <div class="score-questions" ng-repeat-end ng-repeat="ques in schema.questions">
                                    {{ ques.score || '-' }}
                                </div>
                                <div class="score-results">
                                    {{ user.averageOfUsers }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div ng-show="card.showComment" class="comment-block comment-block-main">
                    <div class="comment-block__item" ng-repeat="comment in card.allComments">
                        <div class="user-info">
                            <div class="name">
                                <a ng-if="$root.useAmericanNameStyle" href="#/users/{{ comment.userId }}">
                                    {{ comment.fullNameEn }}
                                </a>
                                <a ng-if="!$root.useAmericanNameStyle" href="#/users/{{ comment.userId }}">
                                    {{ comment.fullName }}
                                </a>
                            </div>
                            <div class="date">
                                <span title="{{ comment.editDate | dateFormatSimpleExcelHistory : true | translate }}">
                                    {{ comment.editDate | dateTimeFrom | translate }}
                                </span>
                            </div>
                        </div>
                        <div class="comment-info">
                            <span>{{ comment.comment }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
