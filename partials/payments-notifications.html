<div
    ng-if="
        ($root.me.personParams.accountBlockedInTwoDays === 'Y' || $root.me.personParams.accountBlockedInOneDay === 'Y') &&
        vm.$location.path() !== '/pay' &&
        vm.$location.path() !== '/pay-corporate' &&
        vm.$location.path() !== '/blocked-corporate'
    "
    class="payments-notifications"
>
    <div class="payments-notifications__container">
        <span translate="There are not enough funds on your balance."></span>
        <span ng-if="$root.me.personParams.accountBlockedInTwoDays === 'Y'" translate="Your account will be blocked after 2 days."></span>
        <span ng-if="$root.me.personParams.accountBlockedInOneDay === 'Y'" translate="Your account will be blocked tomorrow."></span>
        <span translate="It is necessary to replenish the balance in the"></span>
        <a ng-click="vm.redirectToPayPage()" class="redirect-link" translate="Payment Settings"></a>
    </div>
</div>
