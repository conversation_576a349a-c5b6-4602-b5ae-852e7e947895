<div id="payment" ng-show="$root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter'" class="payment">
    <div class="container-fluid wrapper">
        <div ng-if="$root.me.orgParams.block === 'Y'" class="block-msg">
            <img src="/images/sprite/danger-icon.jpg" alt="" />
            <span ng-show="$root.blockUserData.block_text">{{ $root.blockUserData.block_text | translate }}</span>
        </div>
        <h1 class="main-page-title" style="width: fit-content; margin: 2% auto">
            {{ 'Balance and payment' | translate }}
        </h1>

        <section class="payment__acc-info" ng-class="{ 'payment__pay-main-content-disabled': isEditingPaymentsActive }">
            <div class="acc-info__container">
                <div class="acc-info__item">
                    <h5 class="second-title">{{ 'Current balance' | translate }}</h5>
                    <span class="acc-info__item-value">{{ balance.amount }} $</span>
                </div>
                <div class="acc-info__item">
                    <h5 class="second-title">{{ 'Account is active until' | translate }}</h5>
                    <span class="acc-info__item-value">{{ paidTillDate }}</span>
                </div>
                <div class="acc-info__item">
                    <h5 class="second-title">{{ 'Paid Users' | translate }}</h5>
                    <span class="acc-info__item-value">{{ paidUsers }}</span>
                </div>
                <div class="acc-info__item">
                    <h5 class="second-title">{{ 'Your tariff' | translate }}</h5>
                    <span ng-if="customTariffValue && fixedTariff === true" class="acc-info__item-value">
                        {{ tarif | paymentTariff | translate }} - {{ customTariffValue }} $
                    </span>
                    <span ng-if="!fixedTariff" class="acc-info__item-value">{{ tarif | paymentTariff | translate }} - {{ tariffValue }} $</span>
                </div>
            </div>
        </section>

        <div class="flex-container">
            <div class="flex-column">
                <section id="section-pay" ng-show="showFreeTariffPayment || tarif !== 'free'" class="payment__pay">
                    <div>
                        <div class="form-title-main" style="margin: 0px auto 20px; width: fit-content" translate="Pay for cleverstaff usage"></div>
                        <span class="form-title-main" translate="Tariff_scale" ng-hide="customTariffValue && fixedTariff === true"></span>
                        <div class="scale_block" ng-hide="customTariffValue && fixedTariff === true">
                            <div ng-click="setSlider($event); changeEditingPaymentsState()" class="scale_block-bonuses">
                                <div ng-if="isOldAccount" class="scale_block-bonuses-percents">
                                    <div class="scale_block-bonuses-percents-perc" ng-style="{ color: colors.zero }">
                                        {{ payment.countPeople <= 10 ? 50 : 47 }} $
                                    </div>
                                    <div class="scale_block-bonuses-percents-perc" ng-style="{ color: colors.five }">
                                        {{ payment.countPeople <= 10 ? 45 : 42 }} $
                                    </div>
                                    <div class="scale_block-bonuses-percents-perc" ng-style="{ color: colors.ten }">
                                        {{ payment.countPeople <= 10 ? 40 : 38 }} $
                                    </div>
                                    <div class="scale_block-bonuses-percents-perc mr" ng-style="{ color: colors.fifteen }">
                                        {{ payment.countPeople <= 10 ? 38 : 35 }} $
                                    </div>
                                </div>
                                <div ng-if="!isOldAccount" class="scale_block-bonuses-percents">
                                    <div class="scale_block-bonuses-percents-perc" ng-style="{ color: colors.zero }">
                                        {{ payment.countPeople <= 10 ? 60 : 55 }} $
                                    </div>
                                    <div class="scale_block-bonuses-percents-perc" ng-style="{ color: colors.five }">
                                        {{ payment.countPeople <= 10 ? 55 : 50 }} $
                                    </div>
                                    <div class="scale_block-bonuses-percents-perc" ng-style="{ color: colors.ten }">
                                        {{ payment.countPeople <= 10 ? 45 : 42 }} $
                                    </div>
                                    <div class="scale_block-bonuses-percents-perc mr" ng-style="{ color: colors.fifteen }">
                                        {{ payment.countPeople <= 10 ? 40 : 38 }} $
                                    </div>
                                </div>
                                <div class="scale_block-bonuses-progress_bar">
                                    <div class="scale_block-bonuses-progress_bar-nick25" style="right: 8%"></div>
                                    <div class="scale_block-bonuses-progress_bar-nick" style="left: 17%"></div>
                                    <div class="scale_block-bonuses-progress_bar-nick" style="left: 42%"></div>
                                    <div
                                        class="scale_block-bonuses-progress_bar-filled"
                                        style="transition: unset"
                                        ng-style="change ? { width: myWidth } : { width: myWidth }"
                                    >
                                        <span id="slider" class="scale_block-bonuses-progress_bar-filled-slider"></span>
                                    </div>
                                </div>
                                <div class="months">
                                    <div class="month month1">
                                        1 — 2
                                        <span translate="Months_2"></span>
                                    </div>
                                    <div class="month month6" style="padding-left: 5%" ng-style="{ color: colors.five }">
                                        3 — 5
                                        <span translate="Months_1"></span>
                                    </div>
                                    <div class="month month6" style="padding-left: 5%" ng-style="{ color: colors.ten }">
                                        6 — 11
                                        <span translate="Months_1"></span>
                                    </div>
                                    <div class="month month12" ng-style="{ color: colors.fifteen }">
                                        12
                                        <span translate="Months_1"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="pay-data__wrapper">
                            <div class="pay__data">
                                <div class="data__item margin-xs">
                                    <span class="form-title form-title-mobile" translate="Users_1"></span>
                                    <div class="data">
                                        <custom-select-new
                                            ng-if="isEditingPaymentsActive"
                                            ng-hide="tarif === 'corporate'"
                                            options="paidUsersForSelect"
                                            model="payment.countPeople"
                                        ></custom-select-new>
                                        <span ng-if="!isEditingPaymentsActive" class="paid-user-padding">
                                            {{ payment.countPeople }}
                                        </span>
                                        <span ng-show="tarif === 'corporate'" class="bordered" translate="up to 25"></span>
                                    </div>
                                </div>
                                <div class="data__item margin-xs">
                                    <span class="form-title form-title-mobile" translate="Months_1"></span>
                                    <div class="data">
                                        <custom-select-new
                                            ng-if="isEditingPaymentsActive"
                                            options="months"
                                            model="payment.countMonth"
                                            is-restriction-for-option="restrictionForMonths"
                                        ></custom-select-new>
                                        <span ng-if="!isEditingPaymentsActive" class="paid-user-padding">
                                            {{ payment.countMonth }}
                                        </span>
                                    </div>
                                </div>
                                <div class="data__item">
                                    <span ng-if="tarif !== 'corporate'" class="form-title form-title-mobile border-xs" translate="Tariff"></span>
                                    <span ng-if="tarif === 'corporate'" class="title border-xs" translate="Discount"></span>
                                    <div class="data">
                                        <span ng-if="!fixedTariff">{{ payMentTariff }} $</span>
                                        <span ng-if="customTariffValue && fixedTariff === true" style="padding-left: 12px">{{ customTariffValue }} $</span>
                                    </div>
                                </div>
                                <div class="data__item center-xs">
                                    <span class="title title-total form-title-mobile border-xs" translate="Total"></span>
                                    <div class="data">
                                        <span class="total">
                                            <span ng-if="!fixedTariff" class="discount-part">
                                                <span class="bold">10%</span>
                                                {{ 'discount' | translate }}
                                            </span>
                                            <span ng-if="checkFirstPayment && !isReferralDiscount && !customTariffValue" class="price">{{ price }} $</span>
                                            <span ng-if="!checkFirstPayment && !isReferralDiscount && !customTariffValue" class="price">{{ price }} $</span>
                                            <span ng-if="checkFirstPayment && isReferralDiscount && !customTariffValue" class="price">{{ price }} $</span>
                                            <span ng-if="!checkFirstPayment && isReferralDiscount && !customTariffValue" class="price">
                                                {{ price - price * 0.1 }} $
                                            </span>
                                            <span ng-if="customTariffValue && fixedTariff === false" class="price">{{ price }} $</span>
                                            <span ng-if="customTariffValue && fixedTariff === true" class="price">
                                                {{ payment.countMonth * customTariffValue * payment.countPeople }} $
                                            </span>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div ng-if="isReferralDiscount" class="total-info">
                                <span
                                    ng-if="tarif !== 'corporate' && checkFirstPayment === false"
                                    translate="Will be credited total USD (bonus USD and discount)"
                                    translate-values="{total: priceWithBonus, bonus: bonusAmount, discount: 10}"
                                ></span>
                            </div>
                        </div>
                    </div>

                    <!--                    <div class="pay__total space-between">-->
                    <!--                        <span class="pay__total-text">{{'Total will be credited'|translate}}</span>-->
                    <!--                        <span ng-if="!fixedTariff" class="pay__total-value">{{price}} $</span>-->
                    <!--                        <span ng-if="customTariffValue && fixedTariff === true" class="pay__total-value">{{payment.countMonth * customTariffValue * payment.countPeople}} $</span>-->
                    <!--                    </div>-->

                    <div class="pay__saving space-between">
                        <p
                            ng-if="(isEditingPaymentsActive || showPaymentsSettings) && payment.countMonth > 4 && payment.countMonth < 21"
                            class="pay__saving-text"
                        >
                            <span
                                style="display: block"
                                translate="This amount will be charged every 7 months"
                                translate-values="{monthCount: payment.countMonth}"
                            ></span>
                            <span translate="You will receive the same bonus on every charge"></span>
                        </p>

                        <p ng-if="(isEditingPaymentsActive || showPaymentsSettings) && payment.countMonth === 1" class="pay__saving-text">
                            <span
                                style="display: block"
                                translate="This amount will be charged every 1 months"
                                translate-values="{monthCount: payment.countMonth}"
                            ></span>
                            <span translate="You will receive the same bonus on every charge"></span>
                        </p>

                        <p ng-if="(isEditingPaymentsActive || showPaymentsSettings) && payment.countMonth === 21" class="pay__saving-text">
                            <span
                                style="display: block"
                                translate="This amount will be charged every 21 months"
                                translate-values="{monthCount: payment.countMonth}"
                            ></span>
                            <span translate="You will receive the same bonus on every charge"></span>
                        </p>

                        <p
                            ng-if="
                                (isEditingPaymentsActive || showPaymentsSettings) &&
                                ((payment.countMonth > 1 && payment.countMonth < 5) || (payment.countMonth > 21 && payment.countMonth < 25))
                            "
                            class="pay__saving-text"
                        >
                            <span
                                style="display: block"
                                translate="This amount will be charged every 2 months"
                                translate-values="{monthCount: payment.countMonth}"
                            ></span>
                            <span translate="You will receive the same bonus on every charge"></span>
                        </p>

                        <div
                            ng-if="isEditingPaymentsActive || !showPaymentsSettings"
                            ng-click="changeEditingPaymentsState(payment.countMonth, payment.countPeople)"
                            class="btn_default btn_thin btn_success pay__saving-btn"
                        >
                            {{ 'save' | translate }}
                        </div>
                        <div
                            ng-if="showPaymentsSettings && !isEditingPaymentsActive"
                            ng-click="changeEditingPaymentsState()"
                            class="btn_default btn_thin btn_success pay__saving-btn"
                        >
                            {{ 'Change' | translate }}
                        </div>
                    </div>
                </section>

                <section
                    id="payment-settings"
                    ng-show="showPaymentsSettings"
                    class="payment__settings"
                    ng-class="{ 'payment__pay-main-content-disabled': isEditingPaymentsActive }"
                >
                    <payment-block is-blur="isEditingPaymentsActive" account-info="balance"></payment-block>
                </section>
            </div>

            <section class="payment-history-container" ng-class="{ 'payment__pay-main-content-disabled': isEditingPaymentsActive }">
                <div class="form-title-main" style="margin: 0px auto 20px; width: fit-content">
                    {{ 'payment history' | translate }}
                </div>
                <payment-history
                    ng-if="paymentsHistory.payments.length > 0"
                    class="payment-history-component"
                    is-blur="isEditingPaymentsActive"
                    payments-history="paymentsHistory"
                ></payment-history>
            </section>
        </div>
    </div>
</div>
