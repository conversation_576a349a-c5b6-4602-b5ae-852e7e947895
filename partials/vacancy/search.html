<div class="vacancies-search-component">
    <div class="vacancies-search-component_left-side">
        <div class="switcher-wrapper">
            <div class="switcher-hint">
                <i
                    class="hint-info-icon__grey"
                    tooltip-placement="bottom-left"
                    tooltip-class="tooltip-info-black"
                    uib-tooltip-html="$root.tooltips.boolSearchInfoVacancies"
                    aria-hidden="true"
                ></i>
                <label class="vacancies-switcher-label" ng-class="{ active: !vm.search.fields.matchType.getValue() }" translate="Search by full words"></label>
            </div>

            <toggle-component
                class="search-type-toggle"
                on-change="(vm.changeSearchType)"
                checked="vm.search.fields.matchType.value"
                always-checked-style="true"
            ></toggle-component>

            <div class="switcher-hint">
                <i
                    class="hint-info-icon__grey"
                    tooltip-placement="bottom"
                    tooltip-class="tooltip-info-black"
                    uib-tooltip="{{ 'Search may take more than 10 seconds' | translate }}"
                    aria-hidden="true"
                ></i>
                <label class="vacancies-switcher-label" ng-class="{ active: vm.search.fields.matchType.getValue() }" translate="Partial match"></label>
            </div>
        </div>

        <div class="vacancies-search-vertical-divider"></div>

        <form class="vacancies-search-input-wrapper" ng-submit="vm.triggerSearch()">
            <span ng-click="vm.triggerSearch()" class="search-icon search-icon__search"></span>
            <input class="vacancies-search-input" type="text" placeholder="{{ 'Search by vacancies' | translate }}" ng-model="vm.search.fields.boolean.value" />
            <div class="clear-input-icon-wrapper">
                <img
                    ng-if="vm.search.fields.boolean.value.length"
                    ng-click="vm.search.fields.boolean.value = '';vm.isTriggerSearchAll && vm.triggerSearch()"
                    class="clear-input-icon"
                    src="/images/redesign/svg-icons/close.svg"
                    alt=""
                />
            </div>
        </form>
    </div>

    <span class="vacancies-search-total-found">{{ 'found' | translate }} {{ $root.vacanciesSize }}</span>

    <div class="vacancies-search-component_right-side">
        <button-with-icon
            ng-click="vm.isToggleAdvancedSearch = true;"
            class-name="'search-button'"
            type="'secondary'"
            text="'Advanced search' | translate"
            icon-name="'filter'"
        ></button-with-icon>
        <!--        && ($root.me.recrutRole === 'admin' || $root.me.recrutRole === 'freelancer')-->
        <button-with-icon
            ng-if="!vm.isHideMenuButton"
            class-name="'search-button'"
            container="'link'"
            href="'!#/vacancy/add'"
            text="'add_vacancy' | translate"
            icon-name="'plus'"
        ></button-with-icon>
    </div>
</div>

<div id="filters-section">
    <div
        ng-if="
            $root.currentSelectScope !== 'company' ||
            (!vm.search.isAnyFilledField() && $root.currentSelectScope !== 'company') ||
            (vm.search.isAnyFilledField() && vm.isTriggerSearchAll) ||
            vm._isAnyFilledCustomField()
        "
        class="applied-filters"
    >
        <div class="applied-filters_items applied-filters_items-on-list">
            <span class="applied-filters_title">{{ 'Search by criteria' | translate }}:</span>

            <div ng-if="$root.scopeActiveObject.name == 'onlyMy'" class="filter filter_border-grey scope-filter centered">
                <span class="filter-name">{{ 'scope' | translate }}: {{ 'only_me1' | translate }}</span>
            </div>

            <div ng-if="$root.scopeActiveObject.name == 'region'" class="filter filter_border-grey scope-filter centered">
                <span class="filter-name">
                    {{ 'scope' | translate }}: {{ 'only' | translate }}
                    <span>{{ vm.activeScopeParam.value.name }}</span>
                </span>
            </div>

            <div
                class="filter filter_border-grey single-item"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'status' | translate }}"
                tooltip-append-to-body="true"
                ng-repeat="status in vm.search.fields.status.getValue()"
            >
                <div class="centered">
                    <span class="filter-name">{{ status | translate }}</span>
                    <span ng-click="vm.resetSingleStatusValue(status);vm.triggerSearch()" class="remove-filter">
                        <img class="item-close-icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
            </div>

            <div
                ng-if="!responsible.hasOwnProperty('isDivider')"
                class="filter filter_border-grey single-item"
                ng-repeat="responsible in vm.search.fields.responsible.selectedResponsibles"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'responsible' | translate }}"
                tooltip-append-to-body="true"
            >
                <div class="centered">
                    <span class="filter-name">{{ responsible.value.fullName }}</span>
                    <span ng-click="vm.resetSingleResponsibleValue(responsible.value.userId);vm.triggerSearch()" class="remove-filter">
                        <img class="item-close-icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
            </div>

            <div
                ng-if="field.name !== 'status' && field.name !== 'responsible' && field.name !== 'country' && field.getLabelValue()"
                class="filter filter_border-grey other {{ field.type }}"
                ng-repeat="field in vm.search.fields track by $index"
                ng-switch="field.name"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ field.placeholder | translate }}"
                tooltip-append-to-body="true"
            >
                <div class="centered" ng-switch-when="boolean">
                    <span class="filter-name">{{ field.getLabelValue() }}</span>
                    <span ng-click="field.reset({});vm.triggerSearch()" class="remove-filter">
                        <img class="item-close-icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>

                <div class="centered" ng-switch-when="client">
                    <span class="filter-name">{{ vm.search.fields.client.value.label }}</span>
                    <span ng-click="field.reset({callback: vm.setClientAutocompleterPlaceholderValue});vm.triggerSearch();" class="remove-filter">
                        <img class="item-close-icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>

                <div ng-if="field.value.name.length > 0" class="centered" ng-switch-when="city">
                    <span class="filter-name">{{ field.value.name }}</span>
                    <span ng-click="field.reset({callback: vm.setClientAutocompleterPlaceholderValue});vm.triggerSearch()" class="remove-filter">
                        <img class="item-close-icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>

                <div class="centered" ng-switch-default>
                    <span class="filter-name">{{ field.getLabelValue() | translate }}</span>
                    <span ng-click="field.reset({});vm.triggerSearch()" class="remove-filter">
                        <img class="item-close-icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
            </div>

            <div
                ng-if="vm.search.fields.country.value.length"
                class="filter filter_border-grey single-item"
                ng-repeat="country in vm.search.fields.country.value track by $index"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ 'country' | translate }}"
                tooltip-append-to-body="true"
            >
                <div class="centered">
                    <span class="filter-name">{{ country.showName }}</span>
                    <span
                        ng-click="vm.removeCountry($index);vm.search.fields.city.reset({});vm.triggerSearch();$event.stopPropagation();"
                        class="remove-filter"
                    >
                        <img class="item-close-icon" src="/images/redesign/svg-icons/close.svg" alt="" />
                    </span>
                </div>
            </div>

            <div
                ng-if="customField.getLabelValue()"
                class="filter filter_border-grey other"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{ customField.placeholder }}"
                tooltip-append-to-body="true"
                ng-repeat="customField in vm.customFieldsSearch.fields"
            >
                <div class="centered">
                    <span ng-if="!customField.noValue" class="filter-name">{{ customField.getLabelValue() }}</span>
                    <span ng-if="customField.noValue" class="filter-name">{{ customField.getLabelValue() | translate }}</span>
                    <img
                        ng-click="customField.reset({name: customField.name});vm.triggerSearch();"
                        class="item-close-icon"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>
            </div>

            <div
                ng-if="($root.scopeActiveObject.name !== 'region' && $root.scopeActiveObject.name !== 'onlyMy') || search.isAnyFilledField()"
                ng-click="vm.resetFields()"
                class="clear-btn"
            >
                <a class="remove-all-button" type="button">
                    <img class="remove-all-icon" src="/images/redesign/svg-icons/trash.svg" alt="" />
                    {{ 'Remove all' | translate }}
                </a>
            </div>
        </div>
    </div>

    <div class="sort-section">
        <select-sort-component
            class="sort-select"
            placeholder="'Sort by'"
            options="vm.translatedVacanciesSortOptions"
            on-change="(vm.changeSort)"
            selected-value="vm.selectedSortValue"
        ></select-sort-component>

        <account-scope-toggle
            translate-func="$root.translate"
            state="$root.scopeActiveObject.name"
            on-change="($root.changeScopeVisibility)"
        ></account-scope-toggle>

        <icon-button
            ng-click="vm.toggleViewType()"
            class="toggle-list-style-button"
            icon-name="vm.$scope.$parent.vacanciesPageView === 'table' ? 'rows' : 'square'"
        ></icon-button>
    </div>
</div>

<vacancy-advanced-search-component
    id="advanced-search-component"
    ng-if="vm.isToggleAdvancedSearch"
    search-fields-triggered="vm.searchFieldsTriggered"
    is-trigger-search-all="vm.isTriggerSearchAll"
    is-trigger-search="vm.isTriggerSearch"
    is-toggle-advanced-search="vm.isToggleAdvancedSearch"
    custom-fields-search="vm.customFieldsSearch"
    set-client-autocompleter-placeholder-value="vm.setClientAutocompleterPlaceholderValue"
></vacancy-advanced-search-component>
