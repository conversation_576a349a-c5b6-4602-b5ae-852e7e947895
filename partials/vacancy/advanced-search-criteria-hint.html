<div>
    <div ng-if="$root.me.recrutRole === 'admin'">
        <span
            ng-if="vm.customFieldsSearch.getFieldsLength()"
            translate="Search criteria for each additional custom field added to the vacancy will be listed here.  Add more custom fields and you'll be able to search your database as a Pro!"
        ></span>
        <span
            ng-if="!vm.customFieldsSearch.getFieldsLength()"
            translate="Here could be listed the search criteria for each custom field that you add to the vacancy. Firstly create your custom fields and you'll be able to search for the info stated there"
        ></span>
    </div>
    <div ng-if="$root.me.recrutRole !== 'admin'">
        <span
            ng-if="vm.customFieldsSearch.getFieldsLength()"
            translate="Search criteria for each additional custom field added to the vacancy will be listed here. Your account admin is able to add them"
        ></span>
        <span
            ng-if="!vm.customFieldsSearch.getFieldsLength()"
            translate="Here could be the search criteria for each custom field added to the vacancy. Your account admin is able to add them"
        ></span>
    </div>
</div>
