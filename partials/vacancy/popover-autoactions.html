<div
    ng-show="
        vm.autoActionSmsMap[status.value].smsTemplate && !vm.autoActionMap[status.value].mail.mailTemplate && !vm.autoActionMap[status.value].test.test.testName
    "
>
    <span>{{ 'For this stage sending SMS is set' | translate }} "{{ vm.autoActionSmsMap[status.value].smsTemplate.name }}"</span>
</div>
<div
    ng-show="
        vm.autoActionSmsMap[status.value].smsTemplate && !vm.autoActionMap[status.value].mail.mailTemplate && vm.autoActionMap[status.value].test.test.testName
    "
>
    <span>{{ 'For this stage sending SMS is set' | translate }} "{{ vm.autoActionSmsMap[status.value].smsTemplate.name }}"</span>
    <span>{{ 'and test' | translate }} "{{ vm.autoActionMap[status.value].test.test.testName | translate }}"</span>
</div>
<div
    ng-show="
        vm.autoActionSmsMap[status.value].smsTemplate && vm.autoActionMap[status.value].mail.mailTemplate && !vm.autoActionMap[status.value].test.test.testName
    "
>
    <span>{{ 'For this stage sending SMS is set' | translate }} "{{ vm.autoActionSmsMap[status.value].smsTemplate.name }}"</span>
    <span>{{ 'and letter' | translate }}</span>
    <span ng-if="vm.autoActionMap[status.value].mail.mailTemplate.type">"{{ vm.autoActionMap[status.value].mail.mailTemplate.type | translate }}"</span>
    <span ng-if="!vm.autoActionMap[status.value].mail.mailTemplate.type">"{{ vm.autoActionMap[status.value].mail.mailTemplate.name }}"</span>
</div>
<div
    ng-show="
        vm.autoActionSmsMap[status.value].smsTemplate && vm.autoActionMap[status.value].mail.mailTemplate && vm.autoActionMap[status.value].test.test.testName
    "
>
    <span>{{ 'For this stage sending SMS is set' | translate }} "{{ vm.autoActionSmsMap[status.value].smsTemplate.name }}",</span>
    <span>{{ 'and_letter' | translate }}</span>
    <span ng-if="vm.autoActionMap[status.value].mail.mailTemplate.type">"{{ vm.autoActionMap[status.value].mail.mailTemplate.type | translate }}"</span>
    <span ng-if="!vm.autoActionMap[status.value].mail.mailTemplate.type">"{{ vm.autoActionMap[status.value].mail.mailTemplate.name }}"</span>
    <span>{{ 'and test' | translate }} "{{ vm.autoActionMap[status.value].test.test.testName | translate }}"</span>
</div>

<div
    ng-show="
        !vm.autoActionSmsMap[status.value].smsTemplate && vm.autoActionMap[status.value].mail.mailTemplate && !vm.autoActionMap[status.value].test.test.testName
    "
>
    <span ng-show="vm.autoActionMap[status.value].mail.mailTemplate.type">
        {{ 'For this stage sending a letter is set' | translate }}
        {{ vm.autoActionMap[status.value].mail.mailTemplate.type | translate }}
    </span>
    <span ng-show="!vm.autoActionMap[status.value].mail.mailTemplate.type">
        {{ 'For this stage sending a letter is set' | translate }}
        {{ vm.autoActionMap[status.value].mail.mailTemplate.name | translate }}
    </span>
</div>
<div
    ng-show="
        !vm.autoActionSmsMap[status.value].smsTemplate && !vm.autoActionMap[status.value].mail.mailTemplate && vm.autoActionMap[status.value].test.test.testName
    "
>
    {{ 'For this stage sending a test is set' | translate }}
    {{ vm.autoActionMap[status.value].test.test.testName | translate }}
</div>
<div
    ng-show="
        !vm.autoActionSmsMap[status.value].smsTemplate && vm.autoActionMap[status.value].mail.mailTemplate && vm.autoActionMap[status.value].test.test.testName
    "
>
    <span ng-show="vm.autoActionMap[status.value].mail.mailTemplate.type">
        {{ 'For this stage sending a letter is set' | translate }} "{{ vm.autoActionMap[status.value].mail.mailTemplate.type | translate }}"
    </span>
    <span ng-show="!vm.autoActionMap[status.value].mail.mailTemplate.type">
        {{ 'For this stage sending a letter is set' | translate }} "{{ vm.autoActionMap[status.value].mail.mailTemplate.name | translate }}"
    </span>
    <span>{{ 'and test' | translate }} "{{ vm.autoActionMap[status.value].test.test.testName | translate }}"</span>
</div>
