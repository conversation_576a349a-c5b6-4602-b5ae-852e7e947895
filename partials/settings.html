<div class="container-fluid container-setting">
    <div class="row">
        <div class="col-lg-12">
            <header class="header">
                <page-intro-component page-title="'Account data visibility'" show-back-button="false"></page-intro-component>
            </header>
            <div class="block-settings col-md-5 col-xs-12 clearfix">
                <div class="text-left block-settings__title">
                    {{ 'Filter the information to be displayed in your interface by such criteria:' | translate }}
                </div>

                <div class="block-settings__wrapper">
                    <div
                        class="col-lg-12 scopeAccount"
                        ng-class="{
                            scope_background_color: scopeActiveObject.name === itemScope.name && scopeActiveObject.check
                        }"
                        ng-style="itemScope.id === 'scopeCheckmarkMe' ? { 'min-height': '32px' } : null"
                        ng-repeat="itemScope in dataChangeScopeAccount"
                    >
                        <div class="input-radio-wrapper">
                            <div ng-click="changeScope(itemScope.name, orgId , $event);">
                                <input
                                    id="single-{{ itemScope.title }}"
                                    class="custom-radio"
                                    type="radio"
                                    name="checkmark"
                                    ng-checked="scopeActiveObject.name == itemScope.name && scopeActiveObject.check"
                                />

                                <span class="checkmark"></span>
                            </div>

                            <label
                                ng-click="changeScope(itemScope.name, orgId , $event);"
                                class="{{
                                    $root.currentLang !== 'en' || ($root.currentLang == 'en' && itemScope.id !== 'scopeCheckmarkRegion') ? itemScope.id : null
                                }}"
                                ng-class="{
                                    'col-lg-12': itemScope.id === 'scopeCheckmarkMe',
                                    'size-label': itemScope.id === 'scopeCheckmarkOrg' || itemScope.id === 'scopeCheckmarkRegion',
                                    'single-company': orgs.length === 1 && itemScope.name === 'company'
                                }"
                                for="single-{{ itemScope.title }}"
                            >
                                <span
                                    class="wrapper"
                                    ng-class="{ 'wrapper-title': itemScope.title === 'only_me1 + only_me2' }"
                                    ng-style="{ 'max-width': itemScope.name === 'region' && $root.currentLang === 'pl' ? '125px' : 'none' }"
                                >
                                    {{ itemScope.title | translate }}
                                </span>
                            </label>
                        </div>

                        <span
                            ng-show="orgs.length === 1 && itemScope.name === 'company'"
                            class="single-account col-lg-8 col-md-8 col-xs-8 no-margin select-wrap-region"
                            ng-class="{ 'ru-width': $root.currentLang !== 'en' }"
                            ng-bind="orgs[0].orgName"
                        ></span>

                        <span ng-show="orgs.length > 1 && itemScope.name === 'company'" class="col-lg-8 col-md-8 col-xs-8 no-margin select-wrap-region">
                            <select-single-virtualized
                                placeholder="company"
                                options="_orgs"
                                selected-value="selectedCompany"
                                path-to-value="'value'"
                                path-to-label="'label'"
                                path-to-key="'accountId'"
                                is-clearable="false"
                                on-change="(changeScope)"
                            ></select-single-virtualized>
                        </span>

                        <span ng-show="itemScope.name === 'region'" class="col-lg-8 col-md-8 col-xs-8 no-margin select-wrap-region" style="padding-right: 0px">
                            <select-single-virtualized
                                placeholder="'Choose region'"
                                options="regionsArray"
                                selected-value="selectedRegion"
                                path-to-value="'value'"
                                path-to-label="'showName'"
                                path-to-key="'id'"
                                is-clearable="false"
                                on-change="(changeScopeForRegionSelect)"
                            ></select-single-virtualized>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
