<div class="controller-view">
    <div class="block-contacts">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12" style="position: relative">
                    <div class="breadcrumbs">
                        <ol class="breadcrumb" style="margin-bottom: 0">
                            <li>
                                <a href="#/clients">{{ 'Clients' | translate }}</a>
                            </li>
                            <li class="active">
                                <a href="#/clients/{{ contact.clientId.localId }}" once-text="contact.clientId.name"></a>
                            </li>
                        </ol>
                    </div>
                    <div class="contact-page-wrapper">
                        <div class="contact-wrapper flex-block">
                            <div class="contact-left-block">
                                <span ng-if="$root.useAmericanNameStyle" class="flex-block-title contact-left-block-name">
                                    {{ contact.firstName }} {{ contact.lastName }}
                                </span>
                                <span ng-if="!$root.useAmericanNameStyle" class="flex-block-title contact-left-block-name">
                                    {{ contact.lastName }} {{ contact.firstName }}
                                </span>
                                <div ng-show="contact.position" class="contact-left-block-item">
                                    <span class="second-title">{{ 'position' | translate }}</span>
                                    <span class="contact-left-block-item-name" once-text="contact.position"></span>
                                </div>
                                <div class="contact-left-block-item">
                                    <span class="second-title">{{ 'client' | translate }}</span>
                                    <span class="contactName" ng-switch="$root.me.personParams.clientAccessLevel">
                                        <span ng-switch-when="hide">{{ contact.clientId.name }}}</span>
                                        <span ng-switch-default>
                                            <a href="#/clients/{{ contact.clientId.localId }}" target="_blank" once-text="contact.clientId.name"></a>
                                        </span>
                                    </span>
                                </div>
                                <div class="contact-left-block-item">
                                    <span class="second-title">{{ 'status' | translate }}</span>
                                    <custom-select-new options="statuses" method="setStatus" model="status"></custom-select-new>
                                </div>
                            </div>
                            <div class="contact-right-block">
                                <div ng-show="contacts.email" class="contact-right-block-item">
                                    <div class="contact-icon-position" title="{{ 'Email' | translate }}">
                                        <svg width="16" height="16" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M1.6 0H14.4C15.28 0 16 0.675 16 1.5V10.5C16 11.325 15.28 12 14.4 12H1.6C0.712 12 0 11.325 0 10.5V1.5C0 0.6675 0.712 0 1.6 0ZM8 5.25L14.4 1.5H1.6L8 5.25ZM1.6 10.5H14.4V3.2775L8 7.02L1.6 3.2775V10.5Z"
                                                fill="#838287"
                                            />
                                        </svg>
                                    </div>
                                    <div class="contact-right-block-item-title">
                                        <a href="mailto:{{ contacts.email }}" once-text="contacts.email"></a>
                                    </div>
                                </div>
                                <div ng-show="contacts.mphone" class="contact-right-block-item">
                                    <div class="contact-icon-position" title="{{ 'mphone' | translate }}">
                                        <svg width="16" height="16" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M13.2222 9.72222C12.2889 9.72222 11.2778 9.56667 10.4222 9.25556C10.3444 9.25556 10.2667 9.25556 10.1889 9.25556C9.95556 9.25556 9.8 9.33333 9.64444 9.48889L7.93333 11.2C5.75556 10.0333 3.88889 8.24444 2.8 6.06667L4.51111 4.35556C4.74444 4.12222 4.82222 3.81111 4.66667 3.57778C4.43333 2.72222 4.27778 1.71111 4.27778 0.777778C4.27778 0.388889 3.88889 0 3.5 0H0.777778C0.388889 0 0 0.388889 0 0.777778C0 8.08889 5.91111 14 13.2222 14C13.6111 14 14 13.6111 14 13.2222V10.5C14 10.1111 13.6111 9.72222 13.2222 9.72222ZM1.55556 1.55556H2.72222C2.8 2.25556 2.95556 2.95556 3.11111 3.57778L2.17778 4.51111C1.86667 3.57778 1.63333 2.56667 1.55556 1.55556ZM12.4444 12.4444C11.4333 12.3667 10.4222 12.1333 9.48889 11.8222L10.4222 10.8889C11.0444 11.0444 11.7444 11.2 12.4444 11.2V12.4444Z"
                                                fill="#838287"
                                            />
                                        </svg>
                                    </div>
                                    <div class="contact-right-block-item-title" once-text="contacts.mphone"></div>
                                </div>
                                <div ng-show="contacts.skype" class="contact-right-block-item">
                                    <div class="contact-icon-position" title="{{ 'skype' | translate }}">
                                        <svg xmlns="http://www.w3.org/2000/svg" height="16px" viewBox="0 0 512 512" width="16px">
                                            <g>
                                                <path
                                                    class="active-path"
                                                    d="m498.078125 307.289062c3.582031-16.917968 5.394531-34.144531 5.394531-51.289062 0-66.101562-25.742187-128.246094-72.480468-174.988281-46.742188-46.742188-108.886719-72.484375-174.988282-72.484375-17.113281 0-34.339844 1.8125-51.285156 5.398437-19.136719-9.125-40.304688-13.925781-61.570312-13.925781-78.933594 0-143.148438 64.214844-143.148438 143.144531 0 21.257813 4.800781 42.425781 13.925781 61.574219-3.578125 16.914062-5.390625 34.136719-5.390625 51.28125 0 66.101562 25.742188 128.246094 72.480469 174.988281 46.738281 46.738281 108.882813 72.480469 174.988281 72.480469 17.113282 0 34.335938-1.816406 51.277344-5.394531 19.144531 9.125 40.3125 13.925781 61.578125 13.925781 78.929687 0 143.140625-64.214844 143.140625-143.140625 0-21.257813-4.800781-42.425781-13.921875-61.570313zm-129.21875 174.710938c-18.5625 0-36.269531-4.371094-52.636719-12.996094-2.171875-1.144531-4.574218-1.730468-6.992187-1.730468-1.144531 0-2.292969.132812-3.421875.398437-16.425782 3.847656-33.179688 5.796875-49.804688 5.796875-119.914062 0-217.46875-97.554688-217.46875-217.46875 0-16.65625 1.949219-33.410156 5.796875-49.796875.824219-3.519531.351563-7.21875-1.332031-10.417969-8.628906-16.378906-13-34.089844-13-52.640625 0-62.386719 50.757812-113.144531 113.148438-113.144531 18.5625 0 36.265624 4.371094 52.625 12.992188 3.199218 1.6875 6.898437 2.160156 10.417968 1.335937 16.429688-3.847656 33.1875-5.800781 49.8125-5.800781 119.914063 0 217.46875 97.558594 217.46875 217.472656 0 16.65625-1.949218 33.414062-5.796875 49.804688-.828125 3.519531-.355469 7.21875 1.328125 10.417968 8.625 16.375 12.996094 34.082032 12.996094 52.636719 0 62.386719-50.753906 113.140625-113.140625 113.140625zm0 0"
                                                    data-original="#000000"
                                                    data-old_color="#000000"
                                                    fill="#838287"
                                                />
                                                <path
                                                    class="active-path"
                                                    d="m256 140.035156c44.246094 0 81.632812 23.117188 81.632812 50.480469 0 8.285156 6.714844 15 15 15 8.28125 0 15-6.714844 15-15 0-22.621094-12.394531-43.472656-34.890624-58.71875-20.710938-14.035156-47.964844-21.765625-76.742188-21.765625s-56.03125 7.730469-76.742188 21.765625c-22.5 15.246094-34.890624 36.097656-34.890624 58.71875s12.390624 43.476563 34.890624 58.722656c20.707032 14.035157 47.960938 21.761719 76.742188 21.761719 44.246094 0 81.632812 23.117188 81.632812 50.484375 0 27.363281-37.386718 50.480469-81.632812 50.480469-44.25 0-81.632812-23.117188-81.632812-50.480469 0-8.285156-6.714844-15-15-15-8.28125 0-15 6.714844-15 15 0 22.617187 12.390624 43.472656 34.890624 58.71875 20.710938 14.03125 47.964844 21.761719 76.742188 21.761719s56.03125-7.730469 76.742188-21.761719c22.496093-15.246094 34.890624-36.101563 34.890624-58.71875 0-22.625-12.394531-43.476563-34.890624-58.722656-20.710938-14.03125-47.964844-21.761719-76.742188-21.761719-44.25 0-81.632812-23.117188-81.632812-50.484375 0-27.363281 37.382812-50.480469 81.632812-50.480469zm0 0"
                                                    data-original="#000000"
                                                    data-old_color="#000000"
                                                    fill="#838287"
                                                />
                                            </g>
                                        </svg>
                                    </div>
                                    <span
                                        ng-click="$root.openSkype(contacts.skype)"
                                        class="contact-item__text ellipsis link-text-style"
                                        style="margin-left: 10px"
                                    >
                                        {{ contacts.skype | limitToEllipse : 45 }}
                                    </span>
                                </div>
                                <div ng-show="contacts.facebook" class="contact-right-block-item">
                                    <div class="contact-icon-position" title="{{ 'facebook' | translate }}">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="-110 1 511 511.99996" width="16px" height="16px">
                                            <g>
                                                <path
                                                    class="active-path"
                                                    d="m180 512h-81.992188c-13.695312 0-24.835937-11.140625-24.835937-24.835938v-184.9375h-47.835937c-13.695313 0-24.835938-11.144531-24.835938-24.835937v-79.246094c0-13.695312 11.140625-24.835937 24.835938-24.835937h47.835937v-39.683594c0-39.347656 12.355469-72.824219 35.726563-96.804688 23.476562-24.089843 56.285156-36.820312 94.878906-36.820312l62.53125.101562c13.671875.023438 24.792968 11.164063 24.792968 24.835938v73.578125c0 13.695313-11.136718 24.835937-24.828124 24.835937l-42.101563.015626c-12.839844 0-16.109375 2.574218-16.808594 3.363281-1.152343 1.308593-2.523437 5.007812-2.523437 15.222656v31.351563h58.269531c4.386719 0 8.636719 1.082031 12.289063 3.121093 7.878906 4.402344 12.777343 12.726563 12.777343 21.722657l-.03125 79.246093c0 13.6875-11.140625 24.828125-24.835937 24.828125h-58.46875v184.941406c0 13.695313-11.144532 24.835938-24.839844 24.835938zm-76.8125-30.015625h71.632812v-193.195313c0-9.144531 7.441407-16.582031 16.582032-16.582031h66.726562l.027344-68.882812h-66.757812c-9.140626 0-16.578126-7.4375-16.578126-16.582031v-44.789063c0-11.726563 1.191407-25.0625 10.042969-35.085937 10.695313-12.117188 27.550781-13.515626 39.300781-13.515626l36.921876-.015624v-63.226563l-57.332032-.09375c-62.023437 0-100.566406 39.703125-100.566406 103.609375v53.117188c0 9.140624-7.4375 16.582031-16.578125 16.582031h-56.09375v68.882812h56.09375c9.140625 0 16.578125 7.4375 16.578125 16.582031zm163.0625-451.867187h.003906zm0 0"
                                                    data-original="#000000"
                                                    data-old_color="#000000"
                                                    fill="#838287"
                                                />
                                            </g>
                                        </svg>
                                    </div>
                                    <div class="contact-right-block-item-title">
                                        <a ng-href="{{ contacts.facebook | makeLink : true }}" target="_blank">
                                            {{ contacts.facebook | limitToEllipse : 30 }}
                                        </a>
                                    </div>
                                </div>
                                <div ng-show="contacts.linkedin" class="contact-right-block-item">
                                    <div class="contact-icon-position" title="{{ 'linkedin' | translate }}">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M4.37181 4.42857L4.37174 4.4285L4.3687 4.4316C4.16481 4.63894 3.89186 4.76583 3.60634 4.74768L3.60635 4.74748H3.6C3.32482 4.74748 3.03116 4.63484 2.83131 4.4316L2.83137 4.43153L2.82819 4.42857C2.61154 4.22659 2.50001 3.92928 2.50001 3.62714C2.50001 3.32765 2.60991 3.04783 2.83131 2.82268C3.03116 2.61943 3.30483 2.50679 3.61999 2.50679C3.89336 2.50679 4.16761 2.61819 4.3687 2.82268C4.5901 3.04783 4.7 3.32765 4.7 3.62714C4.7 3.92928 4.58847 4.22659 4.37181 4.42857ZM3.61999 1.9C3.1544 1.9 2.72833 2.07264 2.4087 2.39769L3.6 5.25425L3.6 5.35425H3.6H3.60001H3.60001H3.60001H3.60002H3.60002H3.60002H3.60003H3.60003H3.60004H3.60005H3.60005H3.60006H3.60007H3.60007H3.60008H3.60009H3.6001H3.60011H3.60012H3.60013H3.60014H3.60015H3.60017H3.60018H3.60019H3.6002H3.60022H3.60023H3.60024H3.60026H3.60028H3.60029H3.60031H3.60032H3.60034H3.60036H3.60038H3.60039H3.60041H3.60043H3.60045H3.60047H3.60049H3.60051H3.60053H3.60055H3.60058H3.6006H3.60062H3.60064H3.60067H3.60069H3.60072H3.60074H3.60077H3.60079H3.60082H3.60085H3.60087H3.6009H3.60093H3.60096H3.60098H3.60101H3.60104H3.60107H3.6011H3.60113H3.60116H3.60119H3.60123H3.60126H3.60129H3.60132H3.60136H3.60139H3.60142H3.60146H3.60149H3.60153H3.60156H3.6016H3.60163H3.60167H3.60171H3.60175H3.60178H3.60182H3.60186H3.6019H3.60194H3.60198H3.60202H3.60206H3.6021H3.60214H3.60218H3.60222H3.60227H3.60231H3.60235H3.60239H3.60244H3.60248H3.60249H3.60253H3.60257H3.60262H3.60266H3.60271H3.60275H3.6028H3.60285H3.60289H3.60294H3.60299H3.60304H3.60309H3.60314H3.60319H3.60324H3.60329H3.60334H3.60339H3.60344H3.60349H3.60354H3.60359H3.60365H3.6037H3.60375H3.6038H3.60386H3.60391H3.60397H3.60402H3.60408H3.60413H3.60419H3.60424H3.6043H3.60436H3.60442H3.60447H3.60453H3.60459H3.60465H3.60471H3.60476H3.60482H3.60488H3.60494H3.605H3.60506H3.60513H3.60519H3.60525H3.60531H3.60537H3.60544H3.6055H3.60556H3.60563H3.60569H3.60575H3.60582H3.60588H3.60595H3.60601H3.60608H3.60614H3.60621H3.60628H3.60634H3.60641H3.60648H3.60655H3.60661H3.60668H3.60675H3.60682H3.60689H3.60696H3.60703H3.6071H3.60717H3.60724H3.60731H3.60738H3.60745H3.60753H3.6076H3.60767H3.60774H3.60782H3.60789H3.60796H3.60804H3.60811H3.60818H3.60826H3.60833H3.60841H3.60849H3.60856H3.60864H3.60871H3.60879H3.60887H3.60894H3.60902H3.6091H3.60918H3.60925H3.60933H3.60941H3.60949H3.60957H3.60965H3.60973H3.60981H3.60989H3.60997H3.61005H3.61013H3.61021H3.61029H3.61038H3.61046H3.61054H3.61062H3.61071H3.61079H3.61087H3.61096H3.61104H3.61112H3.61121H3.61129H3.61138H3.61146H3.61155H3.61163H3.61172H3.6118H3.61189H3.61198H3.61206H3.61215H3.61224H3.61232H3.61241H3.6125H3.61259H3.61268H3.61276H3.61285H3.61294H3.61303H3.61312H3.61321H3.6133H3.61339H3.61348H3.61357H3.61366H3.61375H3.61384H3.61393H3.61403H3.61412H3.61421H3.6143H3.61439H3.61449H3.61458H3.61467H3.61477H3.61486H3.61495H3.61505H3.61514H3.61523H3.61533H3.61542H3.61552H3.61561H3.61571H3.6158H3.6159H3.616H3.61609H3.61619H3.61628H3.61638H3.61648H3.61658H3.61667H3.61677H3.61687H3.61697H3.61706H3.61716H3.61726H3.61736H3.61746H3.61756H3.61765H3.61775H3.61785H3.61795H3.61805H3.61815H3.61825H3.61835H3.61845H3.61855H3.61866H3.61876H3.61886H3.61896H3.61906H3.61916H3.61926H3.61937H3.61947H3.61957H3.61967H3.61978H3.61988H3.61998H3.62008H3.62019H3.62029H3.62039H3.6205H3.6206H3.62071H3.62081H3.62091H3.62102H3.62112H3.62123H3.62133H3.62144H3.62154H3.62165H3.62175H3.62186H3.62197H3.62207H3.62218H3.62228H3.62239H3.6225H3.6226H3.62271H3.62282H3.62293H3.62303H3.62314H3.62325H3.62335H3.62346H3.62357H3.62368H3.62379H3.62389H3.624H3.62411H3.62422H3.62433H3.62444H3.62455H3.62466H3.62477H3.62487H3.62498H3.62509H3.6252H3.62531H3.62542H3.62553H3.62564H3.62575H3.62586H3.62598H3.62609H3.6262H3.62631H3.62642H3.62653H3.62664H3.62675H3.62686H3.62697H3.62709H3.6272H3.62731H3.62742H3.62753H3.62765H3.62776H3.62787H3.62798H3.62809H3.62821H3.62832H3.62843H3.62855H3.62866H3.62877H3.62888H3.629H3.62911H3.62922H3.62934H3.62945H3.62956H3.62968H3.62979H3.62991H3.63002H3.63013H3.63025H3.63036H3.63048H3.63059H3.6307H3.63082H3.63093H3.63105H3.63116H3.63128H3.63139H3.63151H3.63162H3.63174H3.63185H3.63197H3.63208H3.6322H3.63231H3.63243H3.63254H3.63266H3.63277H3.63289H3.63301H3.63312H3.63324H3.63335H3.63347H3.63358H3.6337H3.63382H3.63393H3.63405H3.63416H3.63428H3.6344H3.63451H3.63463H3.63475H3.63486H3.63498H3.63509H3.63521H3.63533H3.63544H3.63556H3.63568H3.63579H3.63591H3.63603H3.63614H3.63626H3.63638H3.63649H3.63661H3.63673H3.63685H3.63696H3.63708H3.6372H3.63731H3.63743H3.63755H3.63766H3.63778H3.6379H3.63802H3.63813H3.63825H3.63837H3.63848H3.6386H3.63872H3.63884H3.63895H3.63907H3.63919H3.63931H3.63942H3.63954H3.63966H3.63977H3.63989H3.64001C4.06837 5.35425 4.47361 5.17964 4.79131 4.85656C5.10812 4.53437 5.29937 4.10368 5.3 3.62945C5.32069 3.17206 5.12786 2.7405 4.81243 2.41915C4.49279 2.07309 4.06617 1.9 3.61999 1.9Z"
                                                fill="#838287"
                                                stroke="#838287"
                                                stroke-width="0.2"
                                            />
                                            <path
                                                d="M4.39976 5.56107H2.77977C2.40079 5.56107 2.09977 5.87408 2.09977 6.27124V13.3899C2.09977 13.769 2.42297 14.1001 2.79976 14.1001H4.39976C4.7744 14.1001 5.09976 13.7712 5.09976 13.4103V6.27124C5.09976 5.89216 4.77656 5.56107 4.39976 5.56107ZM4.49975 13.4103C4.49975 13.427 4.4915 13.447 4.47113 13.4649C4.45043 13.4832 4.42353 13.4933 4.39976 13.4933H2.79976C2.78289 13.4933 2.75721 13.4837 2.73357 13.4597C2.71004 13.4357 2.69977 13.4089 2.69977 13.3899V6.27124C2.69977 6.24563 2.71033 6.21746 2.72838 6.19628C2.74624 6.17533 2.76517 6.16786 2.77977 6.16786H4.39976C4.41664 6.16786 4.44232 6.17749 4.46596 6.20153C4.48949 6.22547 4.49975 6.25234 4.49975 6.27124V13.4103Z"
                                                fill="#838287"
                                                stroke="#838287"
                                                stroke-width="0.2"
                                            />
                                            <path
                                                d="M10.78 5.35758C10.0608 5.35758 9.37419 5.64081 8.87998 6.10511V6.06775C8.87998 5.93413 8.81525 5.80845 8.72628 5.71798C8.63742 5.62761 8.5131 5.56096 8.37997 5.56096H6.37999C6.2621 5.56096 6.1392 5.61304 6.04618 5.69543C5.95207 5.77879 5.87998 5.90105 5.87998 6.0474V13.6339C5.87998 13.7813 5.95316 13.8995 6.04907 13.9782C6.14357 14.0557 6.26575 14.1 6.37999 14.1H8.57999C8.69423 14.1 8.8164 14.0557 8.9109 13.9782C9.00681 13.8995 9.07999 13.7813 9.07999 13.6339V9.24063C9.07999 8.616 9.5235 8.14062 10.08 8.14062C10.3743 8.14062 10.6482 8.25265 10.8487 8.4565L10.8486 8.4566L10.8525 8.46015C11.022 8.6153 11.1 8.86544 11.1 9.2203V13.5932C11.1 13.7268 11.1647 13.8525 11.2537 13.943C11.3425 14.0334 11.4669 14.1 11.6 14.1H13.6C13.7331 14.1 13.8574 14.0334 13.9463 13.943C14.0352 13.8525 14.1 13.7269 14.1 13.5932V8.34573C14.1 6.66639 12.8382 5.35758 11.2 5.35758H10.78ZM10.1 7.53384L10.0976 7.5339C9.1998 7.55563 8.49998 8.29502 8.49998 9.24066V13.4932H6.49998V6.16775H8.29996V7.36945V7.61377L8.47127 7.43956L9.01126 6.89041L9.01126 6.89041L9.03125 6.87008L9.03704 6.86419L9.04178 6.85744C9.42258 6.3153 10.0917 5.96437 10.8 5.96437H11.22C12.5005 5.96437 13.4999 7.01356 13.4999 8.34575V13.4932H11.7195L11.7 9.22032H11.6L11.7 9.21987L11.7 9.22007C11.6999 8.7157 11.5755 8.32065 11.2913 8.03157C10.9721 7.70698 10.5462 7.53387 10.1 7.53384Z"
                                                fill="#838287"
                                                stroke="#838287"
                                                stroke-width="0.2"
                                            />
                                        </svg>
                                    </div>
                                    <div class="contact-right-block-item-title">
                                        <a ng-href="{{ contacts.linkedin | makeLink : true }}" target="_blank">
                                            {{ contacts.linkedin | limitToEllipse : 30 }}
                                        </a>
                                    </div>
                                </div>
                                <div ng-show="contacts.homepage" class="contact-right-block-item">
                                    <div class="contact-icon-position" title="{{ 'homepage' | translate }}">
                                        <svg class="" xmlns="http://www.w3.org/2000/svg" height="16px" viewBox="0 0 512 512" width="16px">
                                            <g>
                                                <path
                                                    class="active-path"
                                                    d="m437.019531 74.980469c-48.351562-48.351563-112.636719-74.980469-181.015625-74.980469h-.007812c-68.378906 0-132.664063 26.628906-181.015625 74.980469-48.351563 48.351562-74.980469 112.640625-74.980469 181.019531s26.628906 132.667969 74.980469 181.019531c48.351562 48.351563 112.640625 74.980469 181.019531 74.980469s132.667969-26.628906 181.019531-74.980469c48.351563-48.351562 74.980469-112.640625 74.980469-181.019531s-26.628906-132.667969-74.980469-181.019531zm16.722657 290.335937h-74.925782c10.039063-29.414062 16.085938-61.261718 17.496094-94.316406h85.175781c-2.238281 34.03125-12.042969 66.023438-27.746093 94.316406zm-197.742188 114.464844c-31.929688-19-58.410156-48.449219-77.457031-84.464844h154.914062c-19.046875 36.015625-45.527343 65.464844-77.457031 84.464844zm-91.015625-114.464844c-11.03125-28.941406-17.71875-60.898437-19.265625-94.316406h220.5625c-1.546875 33.417969-8.238281 65.375-19.269531 94.316406zm-134.472656-94.316406h85.175781c1.410156 33.054688 7.457031 64.902344 17.496094 94.316406h-74.925782c-15.703124-28.292968-25.507812-60.285156-27.746093-94.316406zm27.746093-124.316406h74.925782c-10.039063 29.414062-16.085938 61.261718-17.496094 94.316406h-85.175781c2.238281-34.03125 12.042969-66.023438 27.746093-94.316406zm197.742188-114.464844c31.929688 19 58.410156 48.449219 77.457031 84.464844h-154.914062c19.046875-36.015625 45.527343-65.464844 77.457031-84.464844zm91.015625 114.464844c11.03125 28.941406 17.71875 60.898437 19.265625 94.316406h-220.5625c1.546875-33.417969 8.238281-65.375 19.269531-94.316406zm134.472656 94.316406h-85.175781c-1.410156-33.054688-7.457031-64.902344-17.496094-94.316406h74.925782c15.703124 28.292968 25.507812 60.285156 27.746093 94.316406zm-114.683593-124.316406c-14.257813-30.796875-33.226563-58.011719-56-79.949219 49.429687 12.359375 92.472656 41.027344 123.03125 79.949219zm-165.609376-79.949219c-22.773437 21.9375-41.742187 49.152344-56 79.949219h-67.03125c30.558594-38.921875 73.601563-67.589844 123.03125-79.949219zm-56 358.582031c14.257813 30.796875 33.226563 58.011719 56 79.949219-49.429687-12.359375-92.472656-41.027344-123.03125-79.949219zm165.609376 79.949219c22.773437-21.9375 41.742187-49.15625 56-79.949219h67.03125c-30.558594 38.921875-73.601563 67.589844-123.03125 79.949219zm0 0"
                                                    data-original="#000000"
                                                    data-old_color="#000000"
                                                    fill="#838287"
                                                />
                                            </g>
                                        </svg>
                                    </div>
                                    <div class="contact-right-block-item-title">
                                        <a ng-href="{{ contacts.homepage | makeLink : true }}" target="_blank">
                                            {{ contacts.homepage | limitToEllipse : 30 }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <a
                                ng-if="
                                    $root.me.recrutRole !== 'salesmanager' &&
                                    $root.me.recrutRole !== 'client' &&
                                    $root.me.recrutRole !== 'researcher' &&
                                    $root.me.recrutRole !== 'freelancer'
                                "
                                style="position: absolute; right: 10px; top: 10px"
                                href="#/contact/edit/{{ contact.localId }}"
                            >
                                <span class="pencilHover">
                                    <svg width="13" height="12" viewBox="0 0 13 12" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M7.37333 4.01333L7.98667 4.62667L1.94667 10.6667H1.33333V10.0533L7.37333 4.01333ZM9.77333 0C9.60667 0 9.43333 0.0666666 9.30667 0.193333L8.08667 1.41333L10.5867 3.91333L11.8067 2.69333C12.0667 2.43333 12.0667 2.01333 11.8067 1.75333L10.2467 0.193333C10.1133 0.06 9.94667 0 9.77333 0ZM7.37333 2.12667L0 9.5V12H2.5L9.87333 4.62667L7.37333 2.12667Z"
                                        />
                                    </svg>
                                </span>
                            </a>
                        </div>
                        <button
                            ng-if="$root.me.recrutRole === 'admin' && contacts.email && showInviteButton"
                            ng-click="inviteUserClientModal();"
                            class="invite-contact-button btn_default btn_success"
                            style="position: absolute; top: 35px; right: 50px"
                        >
                            <div>
                                <svg
                                    style="fill: white"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    version="1.1"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        d="M15,4C12.79,4 11,5.79 11,8C11,10.21 12.79,12 15,12C17.21,12 19,10.21 19,8C19,5.79 17.21,4 15,4M15,5.9C16.16,5.9 17.1,6.84 17.1,8C17.1,9.16 16.16,10.1 15,10.1C13.84,10.1 12.9,9.16 12.9,8C12.9,6.84 13.84,5.9 15,5.9M4,7V10H1V12H4V15H6V12H9V10H6V7H4M15,13C12.33,13 7,14.33 7,17V20H23V17C23,14.33 17.67,13 15,13M15,14.9C17.97,14.9 21.1,16.36 21.1,17V18.1H8.9V17C8.9,16.36 12,14.9 15,14.9Z"
                                    />
                                </svg>
                            </div>
                            <span style="margin-left: 10px">{{ 'Invite User / Client' | translate }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
