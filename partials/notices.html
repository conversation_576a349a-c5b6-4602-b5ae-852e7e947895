<div class="block-notifications">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <div class="history-outer">
                    <div class="row history-header last-actions-header global-header">
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <h3 class="notices-title">
                                <span class="main-page-title">{{ 'Notifications' | translate }}</span>
                                <a ng-show="$root.newNoticeCount > 0" ng-click="checkEverythingRead()" class="pull-right link">
                                    {{ 'Mark all as read' | translate }}
                                </a>
                            </h3>
                        </div>
                    </div>
                </div>
                <div class="history-outer" ng-repeat="notice in notices[0].object">
                    <div
                        ng-show="notices[0].object[$index - 1].dateCreationMonth != notice.dateCreationMonth"
                        class="row history-header last-actions-header month"
                        style="border-top-right-radius: 7px; border-top-left-radius: 7px"
                    >
                        <div class="col-lg-10">
                            <span class="header-of-table" translate="month_MM.{{ notice.dm | date : 'MM' }}"></span>
                        </div>
                    </div>
                    <!---------------------------------------------- to repeat-->
                    <div class="row history last-action" ng-switch="notice.type">
                        <div class="col-lg-1 col-xs-3 date">
                            <span bo-html="notice.dm|dateFormat6:true | translate"></span>
                        </div>
                        <div class="col-lg-11 col-xs-9 notices">
                            <div class="notice" ng-switch-when="add_sms_template">
                                {{ 'user_notice' | translate }}
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'created SMS template' | translate }}
                                <span ng-if="notice.value">"{{ notice.value }}"</span>
                            </div>
                            <div class="notice" ng-switch-when="edit_sms_template">
                                {{ 'user_notice' | translate }}
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'edited SMS template' | translate }}
                                <span ng-if="notice.value">"{{ notice.value }}"</span>
                            </div>
                            <div class="notice" ng-switch-when="remove_sms_template">
                                {{ 'user_notice' | translate }}
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'deleted SMS template' | translate }}
                                <span ng-if="notice.value">"{{ notice.value }}"</span>
                            </div>
                            <div class="notice" ng-switch-when="job_cannon_add_candidate">
                                <ng-include ng-if="notice.candidates.length === 1" src="'partials/notices/job-cannon/found-one.html'"></ng-include>
                                <ng-include ng-if="notice.candidates.length > 1" src="'partials/notices/job-cannon/found-many.html'"></ng-include>
                            </div>

                            <div class="notice" ng-switch-when="wandify_add_candidate">
                                <ng-include ng-if="notice.candidates.length === 1" src="'partials/notices/wandify/found-one.html'"></ng-include>
                                <ng-include ng-if="notice.candidates.length > 1" src="'partials/notices/wandify/found-many.html'"></ng-include>
                            </div>

                            <div class="notice" ng-switch-when="candidate_profile_updated_wandify">
                                <ng-include src="'partials/notices/wandify/candidate_profile_updated_wandify.html'"></ng-include>
                            </div>

                            <div class="notice" ng-switch-when="candidate_profile_not_updated_wandify">
                                <ng-include src="'partials/notices/wandify/candidate_profile_not_updated_wandify.html'"></ng-include>
                            </div>

                            <div class="notice" ng-switch-when="candidate_contacts_updated_wandify">
                                <ng-include src="'partials/notices/wandify/candidate_contacts_updated_wandify.html'"></ng-include>
                            </div>

                            <div class="notice" ng-switch-when="candidate_contacts_not_updated_wandify">
                                <ng-include src="'partials/notices/wandify/candidate_contacts_not_updated_wandify.html'"></ng-include>
                            </div>

                            <div class="notice" ng-switch-when="candidate_profiles_updated_wandify">
                                <ng-include src="'partials/notices/wandify/candidate_profiles_updated_wandify.html'"></ng-include>
                            </div>

                            <div class="notice" ng-switch-when="candidate_profiles_not_updated_wandify">
                                <ng-include src="'partials/notices/wandify/candidate_profiles_not_updated_wandify.html'"></ng-include>
                            </div>

                            <div class="notice" ng-switch-when="addDelucruRecall">
                                <div ng-include="'partials/notices/delucru/add-delucru-recall.html'"></div>
                            </div>

                            <div class="notice" ng-switch-when="scorecardResultSave">
                                {{ 'user_notice' | translate }}
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'appreciated_the_candidate' | translate }}
                                <a
                                    ng-if="notice.candidate.status !== 'deleted'"
                                    ng-click="toCandidate(notice.candidate.localId)"
                                    ng-href="#/candidates/{{::notice.candidate.localId}}"
                                >
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                </a>
                                <span ng-if="notice.candidate.status === 'deleted'" style="color: red">{{ notice.candidate.fullName }}</span>
                                {{ 'in_the_scorecard' | translate }}
                                <span ng-if="notice.scoreCard.name">
                                    <a ng-if="notice.scoreCard.name === 'Default' || notice.scoreCard.name === 'Old Default'" ng-click="toScorecard(notice)">
                                        {{ notice.scoreCard.name | translate }}
                                    </a>
                                    <a ng-if="notice.scoreCard.name !== 'Default' && notice.scoreCard.name !== 'Old Default'" ng-click="toScorecard(notice)">
                                        {{ notice.scoreCard.name }}
                                    </a>
                                </span>
                                <span ng-if="!notice.scoreCard.name">
                                    <span ng-if="notice.scoreCard.name === 'Default' || notice.scoreCard.name === 'Old Default'">
                                        {{ notice.value | translate }}
                                    </span>
                                    <span ng-if="notice.scoreCard.name !== 'Default' && notice.scoreCard.name !== 'Old Default'">
                                        {{ notice.value | translate }}
                                    </span>
                                </span>
                                {{ 'scorecard-vacancy' | translate }}
                                <a ng-click="toVacancy(notice.vacancy.localId)" ng-href="#/vacancies/{{ notice.vacancy.localId }}">
                                    {{ notice.vacancy.position }}
                                </a>
                            </div>
                            <div class="notice" ng-switch-when="change_default_score_card">
                                {{ 'user_notice' | translate }}
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'change default scorecard' | translate }} {{ notice.value }}
                            </div>
                            <div class="notice" style="word-break: break-word" ng-switch-when="candidatePdcsEmailExpired">
                                <a ng-click="routeOnCandidates(notice.candidateIds)">({{ notice.value }})</a>

                                <span ng-show="+notice.value === 1">{{ 'candidate did not respond to a request' | translate }}</span>
                                <span ng-show="+notice.value > 1 && +notice.value <= 4">{{ 'candidates did not respond to a request' | translate }}</span>
                                <span ng-show="+notice.value >= 5">{{ 'candidates did not respond to a request-2' | translate }}</span>
                            </div>

                            <div class="notice" style="word-break: break-word" ng-switch-when="candidatePdcsExpired">
                                <span ng-hide="$root.currentLang === 'en'">У</span>
                                <a ng-click="routeOnCandidates(notice.candidateIds)">({{ notice.value }})</a>

                                <span ng-show="+notice.value === 1">{{ 'candidate have expired their permission' | translate }}</span>
                                <span ng-show="+notice.value > 1">{{ 'candidates have expired their permission' | translate }}</span>
                            </div>
                            <div class="notice" style="word-break: break-word" ng-switch-when="create_default_mail_template">
                                {{ 'user_notice' | translate }}
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'created a template' | translate }}
                                <span ng-if="!notice.source">"{{ notice.value }}"</span>
                                <span ng-if="notice.source">"{{ notice.source | translate }}"</span>
                            </div>
                            <div class="notice" style="word-break: break-word" ng-switch-when="edit_default_mail_template">
                                {{ 'user_notice' | translate }}
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'edited template' | translate }}
                                <span ng-if="!notice.source">"{{ notice.value }}"</span>
                                <span ng-if="notice.source">"{{ notice.source | translate }}"</span>
                            </div>
                            <div class="notice" style="word-break: break-word" ng-switch-when="remove_template_for_auto_action">
                                {{ 'user_notice' | translate }}
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'removed the email template that was used for the step' | translate }}
                                <span ng-if="notice.generalStates.length + notice.customInterviewStates.length > 1">{{ 'notice_stages' | translate }}</span>
                                <span ng-if="notice.generalStates.length + notice.customInterviewStates.length <= 1">{{ 'notice_stage' | translate }}</span>

                                <span ng-if="notice.generalStates.length > 0 && notice.customInterviewStates.length > 0">
                                    "
                                    <span ng-repeat="general in notice.generalStates">{{ general | translate }},</span>
                                    <span style="display: inline-flex" ng-repeat="custom in notice.customInterviewStates">
                                        {{ custom.value }}
                                        <span ng-if="!$last">,</span>
                                        <span ng-if="$last">."</span>
                                    </span>
                                </span>

                                <span ng-if="notice.generalStates.length > 0 && notice.customInterviewStates.length === 0">
                                    "
                                    <span style="display: inline-flex" ng-repeat="general in notice.generalStates">
                                        {{ general | translate }}
                                        <span ng-if="!$last">,&nbsp;</span>
                                        <span ng-if="$last">."</span>
                                    </span>
                                </span>

                                <span ng-if="notice.customInterviewStates.length > 0 && notice.generalStates.length === 0">
                                    "
                                    <span style="display: inline-flex" ng-repeat="custom in notice.customInterviewStates">
                                        {{ custom.value }}
                                        <span ng-if="!$last">,&nbsp;</span>
                                        <span ng-if="$last">."</span>
                                    </span>
                                </span>
                                <span ng-if="notice.generalStates.length + notice.customInterviewStates.length <= 1">
                                    {{ 'Automatic mail sending is disabled at this stage.' | translate }}
                                </span>
                                <span ng-if="notice.generalStates.length + notice.customInterviewStates.length > 1">
                                    {{ 'Automatic mail sending is disabled at these stages.' | translate }}
                                </span>
                            </div>
                            <div class="notice" style="word-break: break-word" ng-switch-when="remove_test_for_auto_action">
                                {{ 'user_notice' | translate }}
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'deleted the test template that was used for' | translate }}
                                <span ng-if="notice.generalStates.length + notice.customInterviewStates.length > 1">{{ 'notice_stages' | translate }}</span>
                                <span ng-if="notice.generalStates.length + notice.customInterviewStates.length <= 1">{{ 'notice_stage' | translate }}</span>

                                <span ng-if="notice.generalStates.length > 0 && notice.customInterviewStates.length > 0">
                                    "
                                    <span ng-repeat="general in notice.generalStates">{{ general | translate }},</span>
                                    <span style="display: inline-flex" ng-repeat="custom in notice.customInterviewStates">
                                        {{ custom.value }}
                                        <span ng-if="!$last">,&nbsp;</span>
                                        <span ng-if="$last">."</span>
                                    </span>
                                </span>

                                <span ng-if="notice.generalStates.length > 0 && notice.customInterviewStates.length === 0">
                                    "
                                    <span style="display: inline-flex" ng-repeat="general in notice.generalStates">
                                        {{ general | translate }}
                                        <span ng-if="!$last">,&nbsp;</span>
                                        <span ng-if="$last">."</span>
                                    </span>
                                </span>

                                <span ng-if="notice.customInterviewStates.length > 0 && notice.generalStates.length === 0">
                                    "
                                    <span style="display: inline-flex" ng-repeat="custom in notice.customInterviewStates">
                                        {{ custom.value }}
                                        <span ng-if="!$last">,&nbsp;</span>
                                        <span ng-if="$last">."</span>
                                    </span>
                                </span>
                                <span ng-if="notice.generalStates.length + notice.customInterviewStates.length <= 1">
                                    {{ 'Automatic test submission is disabled at this stage.' | translate }}
                                </span>
                                <span ng-if="notice.generalStates.length + notice.customInterviewStates.length > 1">
                                    {{ 'Automatic test submission is disabled for these stages.' | translate }}
                                </span>
                            </div>
                            <div class="notice" style="word-break: break-word" ng-switch-when="add_auto_action_test">
                                {{ 'user_notice' | translate }}
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'set automatic test submission for the stage' | translate }}
                                <span ng-if="notice.generalStates.length > 0" style="display: inline-flex">"{{ notice.generalStates[0] | translate }}"</span>
                                <span ng-if="notice.customInterviewStates.length > 0">"{{ notice.customInterviewStates[0].value }}"</span>
                                <span ng-if="notice.vacancy">
                                    {{ 'for vacancy' | translate }}
                                    <a href="#/vacancies/{{ notice.vacancy.localId }}">
                                        <span>
                                            {{
                                                notice.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300
                                            }}
                                        </span>
                                    </a>
                                    <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ notice.vacancy.clientId.localId }}">
                                        ({{ notice.vacancy.clientId.name }})
                                    </a>
                                </span>
                                <span ng-if="!notice.vacancy">
                                    {{ 'for all vacancies' | translate }}
                                </span>
                            </div>
                            <div class="notice" style="word-break: break-word" ng-switch-when="edit_auto_action_test">
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'edited the automatic sending of the test for the stage' | translate }}
                                <span ng-if="notice.generalStates.length > 0" style="display: inline-flex">"{{ notice.generalStates[0] | translate }}"</span>
                                <span ng-if="notice.customInterviewStates.length > 0">"{{ notice.customInterviewStates[0].value }}"</span>
                                <span ng-if="notice.vacancy">
                                    {{ 'for vacancy' | translate }}
                                    <a href="#/vacancies/{{ notice.vacancy.localId }}">
                                        <span>
                                            {{
                                                notice.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300
                                            }}
                                        </span>
                                    </a>
                                    <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ notice.vacancy.clientId.localId }}">
                                        ({{ notice.vacancy.clientId.name }})
                                    </a>
                                </span>
                                <span ng-if="!notice.vacancy">
                                    {{ 'for all vacancies' | translate }}
                                </span>
                            </div>
                            <div class="notice" style="word-break: break-word" ng-switch-when="delete_auto_action_test">
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'canceled automatic sending of a test for a stage' | translate }}
                                <span ng-if="notice.generalStates.length > 0" style="display: inline-flex">"{{ notice.generalStates[0] | translate }}"</span>
                                <span ng-if="notice.customInterviewStates.length > 0">"{{ notice.customInterviewStates[0].value }}"</span>
                                <span ng-if="notice.vacancy">
                                    {{ 'for vacancy' | translate }}
                                    <a href="#/vacancies/{{ notice.vacancy.localId }}">
                                        <span>
                                            {{
                                                notice.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300
                                            }}
                                        </span>
                                    </a>
                                    <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ notice.vacancy.clientId.localId }}">
                                        ({{ notice.vacancy.clientId.name }})
                                    </a>
                                </span>
                                <span ng-if="!notice.vacancy">
                                    {{ 'for all vacancies' | translate }}
                                </span>
                            </div>
                            <div class="notice" style="word-break: break-word" ng-switch-when="add_auto_action_mail">
                                {{ 'user_notice' | translate }}
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'set automatic sending of a letter for the stage' | translate }}
                                <span ng-if="notice.generalStates.length > 0" style="display: inline-flex">"{{ notice.generalStates[0] | translate }}"</span>
                                <span ng-if="notice.customInterviewStates.length > 0">"{{ notice.customInterviewStates[0].value }}"</span>
                                <span ng-if="notice.vacancy">
                                    {{ 'for vacancy' | translate }}
                                    <a href="#/vacancies/{{ notice.vacancy.localId }}">
                                        <span>
                                            {{
                                                notice.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300
                                            }}
                                        </span>
                                    </a>
                                    <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ notice.vacancy.clientId.localId }}">
                                        ({{ notice.vacancy.clientId.name }})
                                    </a>
                                </span>
                                <span ng-if="!notice.vacancy">
                                    {{ 'for all vacancies' | translate }}
                                </span>
                            </div>
                            <div class="notice" style="word-break: break-word" ng-switch-when="edit_auto_action_mail">
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'edited the automatic sending of a letter for the stage' | translate }}
                                <span ng-if="notice.generalStates.length > 0" style="display: inline-flex">"{{ notice.generalStates[0] | translate }}"</span>
                                <span ng-if="notice.customInterviewStates.length > 0">"{{ notice.customInterviewStates[0].value }}"</span>
                                <span ng-if="notice.vacancy">
                                    {{ 'for vacancy' | translate }}
                                    <a href="#/vacancies/{{ notice.vacancy.localId }}">
                                        <span>
                                            {{
                                                notice.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300
                                            }}
                                        </span>
                                    </a>
                                    <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ notice.vacancy.clientId.localId }}">
                                        ({{ notice.vacancy.clientId.name }})
                                    </a>
                                </span>
                                <span ng-if="!notice.vacancy">
                                    {{ 'for all vacancies' | translate }}
                                </span>
                            </div>
                            <div class="notice" style="word-break: break-word" ng-switch-when="delete_auto_action_mail">
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'canceled automatic sending of a letter for a stage' | translate }}
                                <span ng-if="notice.generalStates.length > 0" style="display: inline-flex">"{{ notice.generalStates[0] | translate }}"</span>
                                <span ng-if="notice.customInterviewStates.length > 0">"{{ notice.customInterviewStates[0].value }}"</span>
                                <span ng-if="notice.vacancy">
                                    {{ 'for vacancy' | translate }}
                                    <a href="#/vacancies/{{ notice.vacancy.localId }}">
                                        <span>
                                            {{
                                                notice.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300
                                            }}
                                        </span>
                                    </a>
                                    <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ notice.vacancy.clientId.localId }}">
                                        ({{ notice.vacancy.clientId.name }})
                                    </a>
                                </span>
                                <span ng-if="!notice.vacancy">
                                    {{ 'for all vacancies' | translate }}
                                </span>
                            </div>
                            <div class="notice" style="word-break: break-word" ng-switch-when="remove_default_mail_template">
                                {{ 'user_notice' | translate }}
                                <a href="#/users/{{ notice.person.userId }}">
                                    <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                    <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                </a>
                                {{ 'deleted template' | translate }}
                                <span ng-if="!notice.source">"{{ notice.value }}"</span>
                                <span ng-if="notice.source">"{{ notice.source | translate }}"</span>
                            </div>
                            <div class="notice" ng-switch-when="gpt_candidate_edit">
                                <div ng-if="notice.countCandidate === 1">
                                    {{ 'Voithos AI updated' | translate }}
                                    <!-- prettier-ignore -->
                                    <span ng-show="notice.value" ng-repeat="field in notice.value.split(',')">
                                        <span ng-if="$root.currentLang === 'ua'">
                                            <span  ng-if="field === 'position'">Посаду<span ng-hide="$last">,</span></span>
                                            <span ng-if="field === 'education'">Освіту<span ng-hide="$last">,</span></span>
                                            <span ng-if="field === 'experience'">Освіту<span ng-hide="$last">,</span></span>
                                            <span ng-if="field !== 'position' && field !== 'education'">{{ field === "experience" ? ('Common experience' | translate) : (field | translate) }}<span ng-hide="$last">,</span></span>
                                        </span>
                                        <span ng-if="$root.currentLang !== 'ua'">{{ field === "experience" ? ('Common experience' | translate) : (field | translate) }}<span ng-hide="$last">,</span></span>
                                    </span>
                                    {{ 'for_candidate' | translate }}
                                    <a href="#/candidates/{{ notice.candidates[0].localId }}">
                                        <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                        <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                    </a>
                                </div>

                                <div ng-if="notice.countCandidate > 1">
                                    {{ 'Voithos AI updated' | translate }}
                                    <a ng-click="notice.showCandidates = !notice.showCandidates">{{ notice.countCandidate }}</a>
                                    <span ng-if="$root.currentLang === 'pl'">
                                        {{ [2, 3, 4, 22, 23, 24].includes(notice.countCandidate) ? 'kandydata' : 'kandydatów' }}
                                    </span>
                                    <span ng-if="$root.currentLang === 'ua'">
                                        {{ [2, 3, 4, 22, 23, 24].includes(notice.countCandidate) ? 'кандидата' : 'кандидатів' }}
                                    </span>
                                    <span ng-if="$root.currentLang === 'ru'">
                                        {{ [2, 3, 4, 22, 23, 24].includes(notice.countCandidate) ? 'кандидата' : 'кандидатов' }}
                                    </span>
                                    <span ng-if="$root.currentLang === 'en'">
                                        {{ 'candidates' }}
                                    </span>
                                    <br />
                                    <span ng-show="notice.showCandidates" ng-repeat="candidate in notice.candidates">
                                        <!-- prettier-ignore -->
                                        <a style="display: inline-flex" href="#/candidates/{{ candidate.localId }}">
                                            <span ng-if="$root.useAmericanNameStyle">{{ candidate.fullNameEn }}<span ng-hide="$last">,</span></span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ candidate.fullName }}<span ng-hide="$last">,</span></span>
                                        </a>
                                    </span>
                                    <a ng-show="notice.showCandidates && notice.candidates.length > 9" href="#/candidates">{{ 'and other' | translate }}</a>
                                </div>
                            </div>
                            <div class="notice" ng-switch-when="userMentioned">
                                <a ng-click="toUser(notice.creatorId)" ng-href="#/users/{{ notice.creatorId }}">
                                    {{ $root.useAmericanNameStyle ? notice.person.fullNameEn : notice.person.fullName }}
                                </a>
                                <span>{{ 'mentioned you' | translate }}</span>
                                <span ng-if="notice.value === 'candidate'">
                                    {{ 'page on candidate' | translate }}
                                    <a ng-click="toCandidate(notice.candidate.localId)" ng-href="#/candidates/{{ notice.candidate.localId }}">
                                        {{ $root.useAmericanNameStyle ? notice.candidate.fullNameEn : notice.candidate.fullName }}
                                    </a>
                                </span>
                                <span ng-if="notice.value === 'vacancy'">
                                    {{ 'page on vacancy' | translate }}
                                    <a ng-click="toVacancy(notice.vacancy.localId)" ng-href="#/vacancies/{{ notice.vacancy.localId }}">
                                        {{ notice.vacancy.position }}
                                    </a>
                                </span>
                                <span ng-if="notice.value === 'vacancy_stage'">
                                    {{ 'page on candidate' | translate }}
                                    <a ng-click="toCandidate(notice.candidate.localId)" ng-href="#/candidates/{{ notice.candidate.localId }}">
                                        {{ $root.useAmericanNameStyle ? notice.candidate.fullNameEn : notice.candidate.fullName }}
                                    </a>
                                    <span>{{ 'in the vacancy' | translate }}</span>
                                    <a ng-click="toVacancy(notice.vacancy.localId)" ng-href="#/vacancies/{{ notice.vacancy.localId }}">
                                        {{ notice.vacancy.position }}
                                    </a>
                                </span>
                                <span ng-if="notice.value === 'client'">
                                    {{ 'page on client' | translate }}
                                    <a ng-click="toClient(notice.client.localId)" ng-href="#/clients/{{ notice.client.localId }}">{{ notice.client.name }}</a>
                                </span>
                            </div>
                            <div class="notice" ng-switch-when="vacancyTemplateEdit">
                                {{ 'Application form was edited' | translate }}
                                <span
                                    ng-if="$root.useAmericanNameStyle"
                                    translate="by user"
                                    translate-values="{href: '#/users/{{ notice.person.personId }}', username: '{{ notice.person.fullNameEn }}'}"
                                ></span>
                                <span
                                    ng-if="!$root.useAmericanNameStyle"
                                    translate="by user"
                                    translate-values="{href: '#/users/{{ notice.person.personId }}', username: '{{ notice.person.fullName }}'}"
                                ></span>
                            </div>
                            <div class="notice" ng-switch-when="vacancyApplicationEdit">
                                {{ 'Vacancy_application' | translate }}
                                <span>"{{ notice.vacancyApplication.position }}"</span>
                                {{ 'was edited' | translate }}
                                <span
                                    ng-if="$root.useAmericanNameStyle"
                                    translate="by user"
                                    translate-values="{href: '#/users/{{ notice.person.personId }}', username: '{{ notice.person.fullNameEn }}'}"
                                ></span>
                                <span
                                    ng-if="!$root.useAmericanNameStyle"
                                    translate="by user"
                                    translate-values="{href: '#/users/{{ notice.person.personId }}', username: '{{ notice.person.fullName }}'}"
                                ></span>
                            </div>
                            <div class="notice" ng-switch-when="showAttachmentsHm">
                                <a ng-href="#/users/{{ notice.person.userId }}" once-text="notice.person.fullName"></a>
                                <span ng-if="notice.value === 'Y'">{{ 'opened access to the candidates attachments for' | translate }}</span>
                                <span ng-if="notice.value === 'N'">{{ 'closed access to the candidates attachments for' | translate }}</span>
                                <a ng-href="#/users/{{ notice.targetPerson.userId }}" once-text="notice.targetPerson.fullName"></a>
                            </div>
                            <div class="notice" ng-switch-when="vacancyApplicationDelete">
                                {{ 'Vacancy_application' | translate }}
                                <span>"{{ notice.value }}"</span>
                                {{ 'was deleted' | translate }}
                                <span
                                    ng-if="$root.useAmericanNameStyle"
                                    translate="by user"
                                    translate-values="{href: '#/users/{{ notice.person.personId }}', username: '{{ notice.person.fullNameEn }}'}"
                                ></span>
                                <span
                                    ng-if="!$root.useAmericanNameStyle"
                                    translate="by user"
                                    translate-values="{href: '#/users/{{ notice.person.personId }}', username: '{{ notice.person.fullName }}'}"
                                ></span>
                            </div>
                            <div class="notice" ng-switch-when="vacancyApplicationAdd">
                                {{ 'New application was created' | translate }}
                                <a ng-click="toVacancyApplication($event,notice.vacancyApplication.localId)" href="">
                                    {{ notice.vacancyApplication.position }}
                                </a>
                                <span
                                    ng-if="$root.useAmericanNameStyle"
                                    translate="by user"
                                    translate-values="{href: '#/users/{{ notice.person.personId }}', username: '{{ notice.person.fullNameEn }}'}"
                                ></span>
                                <span
                                    ng-if="!$root.useAmericanNameStyle"
                                    translate="by user"
                                    translate-values="{href: '#/users/{{ notice.person.personId }}', username: '{{ notice.person.fullName }}'}"
                                ></span>
                            </div>
                            <div class="notice" ng-switch-when="vacancyApplicationChangeStatus">
                                <span ng-if="notice.value === 'inwork'">
                                    {{ 'Vacancy_application' | translate }}
                                    <span>"{{ notice.vacancyApplication.position }}"</span>
                                    {{ 'transferred to vacancy' | translate }}
                                    <a ng-click="toVacancy(notice.vacancyApplication.vacancyLocalId)">{{ notice.vacancyApplication.vacancyName }}</a>
                                </span>
                                <span ng-if="notice.value === 'canceled'">
                                    {{ 'Vacancy_application' | translate }}
                                    <span>"{{ notice.vacancyApplication.position }}"</span>
                                    {{ 'has been moved to Canceled' | translate }}
                                </span>
                                <span ng-if="notice.value === 'fresh'">
                                    {{ 'Vacancy_application' | translate }}
                                    <span>"{{ notice.vacancyApplication.position }}"</span>
                                    {{ 'was restored' | translate }}
                                </span>
                                <span
                                    ng-if="$root.useAmericanNameStyle"
                                    translate="by user"
                                    translate-values="{href: '#/users/{{ notice.person.personId }}', username: '{{ notice.person.fullNameEn }}'}"
                                ></span>
                                <span
                                    ng-if="!$root.useAmericanNameStyle"
                                    translate="by user"
                                    translate-values="{href: '#/users/{{ notice.person.personId }}', username: '{{ notice.person.fullName }}'}"
                                ></span>
                            </div>

                            <div class="notice" ng-switch-when="regularPaymentSuccess">
                                <span translate="Successful regular payment" translate-values="{price:notice.value}"></span>
                            </div>
                            <div class="notice" ng-switch-when="regularPaymentError">
                                {{ 'Regular Payment Error' | translate }}
                                <span class="red">{{ 'Card Id' | translate }}{{ notice.value }}</span>
                                . {{ 'Please check mail' | translate }}{{ notice.email }}
                            </div>
                            <div class="notice" ng-switch-when="addNewInboxMails">
                                <span ng-if="notice.value == 1">
                                    {{ 'new letter from' | translate }}
                                    <a ng-click="toCandidate(notice.candidate.localId)" ng-href="#/candidates/{{::notice.candidate.localId}}">
                                        <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                        <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                    </a>
                                </span>
                                <span ng-if="notice.value > 1">
                                    <span ng-if="getLettersEnd(notice.value) == 0">
                                        {{ notice.value }} {{ 'new letters from_1' | translate }}
                                        <a ng-click="toCandidate(notice.candidate.localId)" ng-href="#/candidates/{{::notice.candidate.localId}}">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                        </a>
                                    </span>
                                    <span ng-if="getLettersEnd(notice.value) == 1">
                                        {{ notice.value }} {{ 'new letters from_2' | translate }}
                                        <a ng-click="toCandidate(notice.candidate.localId)" ng-href="#/candidates/{{::notice.candidate.localId}}">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                        </a>
                                    </span>
                                    <span ng-if="getLettersEnd(notice.value) == 2">
                                        {{ notice.value }} {{ 'new letters from_3' | translate }}
                                        <a ng-click="toCandidate(notice.candidate.localId)" ng-href="#/candidates/{{::notice.candidate.localId}}">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                        </a>
                                    </span>
                                </span>
                            </div>
                            <div ng-switch-when="parserEmailIncorrectPassword">{{ 'You entered an incorrect password' | translate }} {{ notice.value }}</div>
                            <div class="notice" ng-switch-when="wrongPromocode">
                                <span>{{ 'This promo code is not valid' | translate }}</span>
                            </div>

                            <div class="notice" ng-switch-when="addWorkUaRecall">
                                <div ng-if="$root.currentLang !== 'en'">
                                    <span ng-if="notice.countCandidateToUpdate > 0">{{ 'to_in' | translate }}</span>
                                    <span ng-if="notice.countCandidateToUpdate > 0">{{ 'history_info.stage of vacancy' | translate }}</span>
                                    <span ng-if="notice.countCandidateToUpdate <= 0">{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                    <a ng-click="toVacancy(notice.vacancy.localId)">
                                        {{
                                            notice.vacancy.position +
                                                ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                        }}
                                    </a>

                                    <div ng-if="notice.countCandidateToUpdate > 0">
                                        <span ng-if="notice.countCandidateToUpdate === 1">
                                            <span>{{ 'Recalls.updated_and_added_one' | translate }}</span>
                                            <span>{{ 'Recalls.candidate_1' | translate }}</span>
                                            <a
                                                ng-if="notice.candidatesToUpdate[0].status != 'deleted'"
                                                ng-click="toCandidate(notice.candidatesToUpdate[0].localId)"
                                                ng-href="#/candidates/{{::notice.candidatesToUpdate[0].localId}}"
                                            >
                                                <span ng-if="$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullNameEn }}</span>
                                                <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullName }}</span>
                                            </a>
                                            <span
                                                ng-if="notice.candidatesToUpdate[0].status == 'deleted'"
                                                class="red"
                                                once-text="notice.candidatesToUpdate[0].fullName"
                                            ></span>
                                            <span>{{ 'was_add_from_workua' | translate }}</span>
                                            <span ng-if="notice.countCandidate <= 0">.</span>
                                        </span>
                                        <span ng-if="notice.countCandidateToUpdate !== 1">
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 0">
                                                {{ 'updated1' | translate }} {{ 'Recalls.and_added1' | translate }}
                                            </span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 2">
                                                {{ 'updated' | translate }} {{ 'Recalls.and_added' | translate }}
                                            </span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 1">
                                                {{ 'updated' | translate }} {{ 'Recalls.and_added' | translate }}
                                            </span>
                                            <span>({{ notice.countCandidateToUpdate }})</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 0">{{ 'Recalls.candidate_1' | translate }}</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 1">{{ 'Recalls.candidate_2' | translate }}</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 2">{{ 'Recalls.candidate_3' | translate }}</span>
                                            <span ng-if="!notice.countCandidate">{{ 'was_add_from_workua' | translate }}</span>
                                            <span ng-if="notice.countCandidateToUpdate > 0 && notice.countCandidate === 0">.</span>
                                        </span>
                                        <span ng-if="notice.countCandidate > 0">{{ 'and' | translate }}</span>
                                    </div>
                                </div>

                                <div ng-if="$root.currentLang == 'en'" style="display: block">
                                    <span ng-if="notice.countCandidateToUpdate === 1">
                                        <a
                                            ng-if="notice.candidatesToUpdate[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidatesToUpdate[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidatesToUpdate[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullName }}</span>
                                        </a>
                                        <span>was added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span ng-if="notice.countCandidateToUpdate > 0">
                                            and updated and added new ({{ notice.countCandidate }}) candidates
                                        </span>
                                        <!-- prettier-ignore -->
                                        <span>
                                            <span>{{ 'was_add_from_workua' | translate }}</span><span ng-if="notice.countCandidate <= 0">.</span>
                                        </span>
                                        <!--<span ng-if="notice.countCandidate > 0">{{ 'and' | translate }}</span>-->
                                    </span>
                                    <span ng-if="notice.countCandidateToUpdate > 2">
                                        <span>{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        was
                                        <span ng-if="notice.countCandidate > 2">added new ({{ notice.countCandidate }})</span>
                                        <span ng-if="notice.countCandidate > 2">and</span>
                                        <span>updated and added ({{ notice.countCandidateToUpdate }}) candidates</span>
                                        <span>{{ 'was_add_from_workua' | translate }}</span>
                                        <span ng-if="notice.countOfMerged">.</span>
                                    </span>
                                </div>

                                <div ng-if="notice.updateCandidate" style="display: flex; flex-direction: column">
                                    <span class="menuCandidate" ng-repeat="candidate in notice.candidatesToUpdate">
                                        <a ng-if="$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                                            {{ candidate.fullNameEn }}
                                            <span class="comma">{{ $last ? '' : ', ' }}</span>
                                        </a>
                                        <a ng-if="!$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                                            {{ candidate.fullName }}
                                            <span class="comma">{{ $last ? '' : ', ' }}</span>
                                        </a>
                                    </span>
                                    <a ng-if="notice.candidatesToUpdate.length > 9" href="#/candidates">{{ 'and other' | translate }}</a>
                                </div>

                                <div ng-if="notice.countCandidate > 0 && $root.currentLang !== 'en'">
                                    <span ng-if="notice.countCandidate === 1">
                                        <span>{{ 'Recalls.added_1' | translate }}</span>
                                        <span>{{ 'Recalls.candidate_1' | translate }}</span>
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span ng-if="notice.candidates[0].status == 'deleted'" class="red" once-text="notice.candidates[0].fullName"></span>
                                        <span>{{ 'was_add_from_workua' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                    </span>
                                    <span ng-if="notice.countCandidate !== 1">
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.added_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.added_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.added_2' | translate }}</span>
                                        <span>{{ 'Recalls.new' | translate }}</span>
                                        <span style="color: black">({{ notice.countCandidate }})</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.candidate_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.candidate_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.candidate_3' | translate }}</span>
                                        <!-- prettier-ignore -->
                                        <span>
                                            {{ 'was_add_from_workua' | translate }}
                                            <span ng-if="notice.countOfMerged > 0">.</span>
                                        </span>
                                    </span>
                                </div>

                                <div ng-if="notice.showAllCandidates" style="display: flex; flex-direction: column">
                                    <span class="menuCandidate" ng-repeat="candidate in notice.candidates">
                                        <a ng-if="$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                                            {{ candidate.fullNameEn }}
                                            <span class="comma">{{ $last ? '' : ', ' }}</span>
                                        </a>
                                        <a ng-if="!$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                                            {{ candidate.fullName }}
                                            <span class="comma">{{ $last ? '' : ', ' }}</span>
                                        </a>
                                    </span>
                                    <a ng-if="notice.candidates.length > 9" href="#/candidates">{{ 'and other' | translate }}</a>
                                </div>

                                <div ng-if="$root.currentLang == 'en' && notice.countCandidateToUpdate <= 0">
                                    <span ng-if="notice.countCandidate === 1">
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span>was added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span>{{ 'was_add_from_workua' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                    </span>
                                    <span ng-if="notice.countCandidate !== 1">
                                        <span>{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span>was added ({{ notice.countCandidate }})</span>
                                        <span>{{ 'was_add_from_workua' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                    </span>
                                </div>

                                <div ng-if="notice.countOfMerged > 0">
                                    <span>
                                        {{
                                            'We noticed duplicate candidates in the reviews for this vacancy and did not create separate profiles for them, but combined them. Number of such candidates'
                                                | translate
                                        }}
                                    </span>
                                    <span>({{ notice.countOfMerged }}).</span>
                                    <span>
                                        {{
                                            'Therefore, there are a lot of vacancies for the vacancies themselves, and we can challenge them. In this case, it is necessary to take up duplicates at the same time for each vacancy.'
                                                | translate
                                        }}
                                    </span>
                                </div>
                            </div>

                            <div class="notice" ng-switch-when="addHHRecall">
                                <div ng-if="$root.currentLang !== 'en'">
                                    <span>{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                    <a ng-click="toVacancy(notice.vacancy.localId)">
                                        {{
                                            notice.vacancy.position +
                                                ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' + ' (' + notice.vacancy.clientId.name + ')' : '')
                                        }}
                                    </a>
                                    <span ng-if="notice.countCandidate === 1">
                                        <span>{{ 'Recalls.added_1' | translate }}</span>
                                        <span>{{ 'Recalls.candidate_1' | translate }}</span>
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span ng-if="notice.candidates[0].status == 'deleted'" class="red" once-text="notice.candidates[0].fullName"></span>
                                        <span>{{ 'из HeadHunter' | translate }}</span>
                                    </span>
                                    <span ng-if="notice.countCandidate !== 1">
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.added_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.added_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.added_2' | translate }}</span>
                                        <span>({{ notice.countCandidate }})</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.candidate_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.candidate_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.candidate_3' | translate }}</span>
                                        <span>{{ 'из HeadHunter' | translate }}</span>
                                    </span>
                                </div>
                                <div ng-if="$root.currentLang == 'en'">
                                    <span ng-if="notice.countCandidate === 1">
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span>was added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' + ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span>{{ 'from HeadHunter' | translate }}</span>
                                    </span>
                                    <span ng-if="notice.countCandidate !== 1">
                                        <span>({{ notice.countCandidate }})</span>
                                        <span>applicants were added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span>{{ 'from HeadHunter' | translate }}</span>
                                    </span>
                                </div>
                            </div>

                            <div class="notice" ng-switch-when="addDjinniRecall">
                                <div ng-if="$root.currentLang !== 'en'">
                                    <span ng-if="notice.countCandidateToUpdate > 0">{{ 'to_in' | translate }}</span>
                                    <span ng-if="notice.countCandidateToUpdate > 0">{{ 'history_info.stage of vacancy' | translate }}</span>
                                    <span ng-if="notice.countCandidateToUpdate <= 0">{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                    <a ng-click="toVacancy(notice.vacancy.localId)">
                                        {{
                                            notice.vacancy.position +
                                                ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                        }}
                                    </a>

                                    <div ng-if="notice.countCandidateToUpdate > 0">
                                        <span ng-if="notice.countCandidateToUpdate === 1">
                                            <span>{{ 'Recalls.updated_and_added_one' | translate }}</span>
                                            <span>{{ 'Recalls.candidate_1' | translate }}</span>
                                            <a
                                                ng-if="notice.candidatesToUpdate[0].status != 'deleted'"
                                                ng-click="toCandidate(notice.candidatesToUpdate[0].localId)"
                                                ng-href="#/candidates/{{::notice.candidatesToUpdate[0].localId}}"
                                            >
                                                <span ng-if="$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullNameEn }}</span>
                                                <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullName }}</span>
                                            </a>
                                            <span
                                                ng-if="notice.candidatesToUpdate[0].status == 'deleted'"
                                                class="red"
                                                once-text="notice.candidatesToUpdate[0].fullName"
                                            ></span>
                                            <span ng-if="notice.countCandidate <= 0">.</span>
                                        </span>
                                        <span ng-if="notice.countCandidateToUpdate !== 1">
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 0">
                                                {{ 'updated1' | translate }} {{ 'Recalls.and_added1' | translate }}
                                            </span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 2">
                                                {{ 'updated' | translate }} {{ 'Recalls.and_added' | translate }}
                                            </span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 1">
                                                {{ 'updated' | translate }} {{ 'Recalls.and_added' | translate }}
                                            </span>
                                            <span>({{ notice.countCandidateToUpdate }})</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 0">{{ 'Recalls.candidate_1' | translate }}</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 1">{{ 'Recalls.candidate_2' | translate }}</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 2">{{ 'Recalls.candidate_3' | translate }}</span>
                                            <span ng-if="!notice.countCandidate">{{ 'was_add_from_rabotaua' | translate }}</span>
                                            <span ng-if="notice.countCandidateToUpdate > 0 && notice.countCandidate === 0">.</span>
                                        </span>
                                        <span ng-if="notice.countCandidate > 0">{{ 'and' | translate }}</span>
                                    </div>
                                </div>

                                <div ng-if="$root.currentLang == 'en'" style="display: block">
                                    <span ng-if="notice.countCandidateToUpdate === 1">
                                        <a
                                            ng-if="notice.candidatesToUpdate[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidatesToUpdate[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidatesToUpdate[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullName }}</span>
                                        </a>
                                        <span>was added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span ng-if="notice.countCandidateToUpdate > 0">
                                            and updated and added new ({{ notice.countCandidate }}) candidates
                                        </span>
                                        <!-- prettier-ignore -->
                                        <span>
                                            <span>{{ 'was_add_from_djinni' | translate }}</span><span ng-if='notice.countOfMerged > 0'>.</span>
                                        </span>
                                        <!--<span ng-if="notice.countCandidate > 0">{{ 'and' | translate }}</span>-->
                                    </span>
                                    <!-- prettier-ignore -->
                                    <span ng-if="notice.countCandidateToUpdate > 1">
                                        <span>{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{ notice.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '') }}
                                        </a>
                                        was
                                        <span ng-if="notice.countCandidate > 1">added new ({{ notice.countCandidate }})</span>
                                        <span ng-if="notice.countCandidate > 1">and</span>
                                        <span>updated and added ({{ notice.countCandidateToUpdate }}) candidates</span>
                                        <span>{{ 'was_add_from_djinni' | translate }}</span><span ng-if="notice.countOfMerged">.</span>
                                    </span>
                                </div>

                                <div ng-if="notice.updateCandidate" style="display: flex; flex-direction: column">
                                    <span class="menuCandidate" ng-repeat="candidate in notice.candidatesToUpdate">
                                        <a ng-if="$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                                            {{ candidate.fullNameEn }}
                                            <span class="comma">{{ $last ? '' : ', ' }}</span>
                                        </a>
                                        <a ng-if="!$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                                            {{ candidate.fullName }}
                                            <span class="comma">{{ $last ? '' : ', ' }}</span>
                                        </a>
                                    </span>
                                    <a ng-if="notice.candidatesToUpdate.length > 9" href="#/candidates">{{ 'and other' | translate }}</a>
                                </div>

                                <div ng-if="notice.countCandidate > 0 && $root.currentLang !== 'en'">
                                    <span ng-if="notice.countCandidate === 1">
                                        <span>{{ 'Recalls.added_1' | translate }}</span>
                                        <span>{{ 'Recalls.candidate_1' | translate }}</span>
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span ng-if="notice.candidates[0].status == 'deleted'" class="red" once-text="notice.candidates[0].fullName"></span>
                                        <span>{{ 'was_add_from_djinni' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                    </span>
                                    <span ng-if="notice.countCandidate !== 1">
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.added_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.added_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.added_2' | translate }}</span>
                                        <span>{{ 'Recalls.new' | translate }}</span>
                                        <span>({{ notice.countCandidate }})</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.candidate_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.candidate_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.candidate_3' | translate }}</span>
                                        <!-- prettier-ignore -->
                                        <span>
                                            {{ 'was_add_from_djinni' | translate }}<span ng-if="notice.countOfMerged > 0">.</span>
                                        </span>
                                    </span>
                                </div>

                                <div ng-if="notice.showAllCandidates" style="display: flex; flex-direction: column">
                                    <span class="menuCandidate" ng-repeat="candidate in notice.candidates">
                                        <a ng-if="$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                                            {{ candidate.fullNameEn }}
                                            <span class="comma">{{ $last ? '' : ', ' }}</span>
                                        </a>
                                        <a ng-if="!$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                                            {{ candidate.fullName }}
                                            <span class="comma">{{ $last ? '' : ', ' }}</span>
                                        </a>
                                    </span>
                                    <a ng-if="notice.candidates.length > 9" href="#/candidates">{{ 'and other' | translate }}</a>
                                </div>

                                <div ng-if="$root.currentLang == 'en' && notice.countCandidateToUpdate <= 0">
                                    <span ng-if="notice.countCandidate === 1">
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span>was added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span>{{ 'was_add_from_djinni' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                    </span>

                                    <span ng-if="notice.countCandidate !== 1">
                                        <span>{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span>was added ({{ notice.countCandidate }})</span>
                                        <span>{{ 'was_add_from_djinni' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                    </span>
                                </div>

                                <div ng-if="notice.countOfMerged > 0">
                                    <span>
                                        {{
                                            'We noticed duplicate candidates in the reviews for this vacancy and did not create separate profiles for them, but combined them. Number of such candidates'
                                                | translate
                                        }}
                                    </span>
                                    <span>({{ notice.countOfMerged }}).</span>
                                    <span>
                                        {{
                                            'Therefore, there are a lot of vacancies for the vacancies themselves, and we can challenge them. In this case, it is necessary to take up duplicates at the same time for each vacancy.'
                                                | translate
                                        }}
                                    </span>
                                </div>
                            </div>

                            <div class="notice" ng-switch-when="addRabotaUaRecall">
                                <div ng-if="$root.currentLang !== 'en'">
                                    <span ng-if="notice.countCandidateToUpdate > 0">{{ 'to_in' | translate }}</span>
                                    <span ng-if="notice.countCandidateToUpdate > 0">{{ 'history_info.stage of vacancy' | translate }}</span>
                                    <span ng-if="notice.countCandidateToUpdate <= 0">{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                    <a ng-click="toVacancy(notice.vacancy.localId)">
                                        {{
                                            notice.vacancy.position +
                                                ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                        }}
                                    </a>

                                    <div ng-if="notice.countCandidateToUpdate > 0">
                                        <span ng-if="notice.countCandidateToUpdate === 1">
                                            <span>{{ 'Recalls.updated_and_added_one' | translate }}</span>
                                            <span>{{ 'Recalls.candidate_1' | translate }}</span>
                                            <a
                                                ng-if="notice.candidatesToUpdate[0].status != 'deleted'"
                                                ng-click="toCandidate(notice.candidatesToUpdate[0].localId)"
                                                ng-href="#/candidates/{{::notice.candidatesToUpdate[0].localId}}"
                                            >
                                                <span ng-if="$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullNameEn }}</span>
                                                <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullName }}</span>
                                            </a>
                                            <span
                                                ng-if="notice.candidatesToUpdate[0].status == 'deleted'"
                                                class="red"
                                                once-text="notice.candidatesToUpdate[0].fullName"
                                            ></span>
                                            <span ng-if="notice.countCandidate <= 0">.</span>
                                        </span>
                                        <span ng-if="notice.countCandidateToUpdate !== 1">
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 0">
                                                {{ 'updated1' | translate }} {{ 'Recalls.and_added1' | translate }}
                                            </span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 2">
                                                {{ 'updated' | translate }} {{ 'Recalls.and_added' | translate }}
                                            </span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 1">
                                                {{ 'updated' | translate }} {{ 'Recalls.and_added' | translate }}
                                            </span>
                                            <span>({{ notice.countCandidateToUpdate }})</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 0">{{ 'Recalls.candidate_1' | translate }}</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 1">{{ 'Recalls.candidate_2' | translate }}</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 2">{{ 'Recalls.candidate_3' | translate }}</span>
                                            <span ng-if="!notice.countCandidate">{{ 'was_add_from_rabotaua' | translate }}</span>
                                            <span ng-if="notice.countCandidateToUpdate > 0 && notice.countCandidate === 0">.</span>
                                        </span>
                                        <span ng-if="notice.countCandidate > 0">{{ 'and' | translate }}</span>
                                    </div>
                                </div>

                                <div ng-if="$root.currentLang == 'en'" style="display: block">
                                    <span ng-if="notice.countCandidateToUpdate === 1">
                                        <a
                                            ng-if="notice.candidatesToUpdate[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidatesToUpdate[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidatesToUpdate[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullName }}</span>
                                        </a>
                                        <span>was added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span ng-if="notice.countCandidateToUpdate > 0">
                                            and updated and added new ({{ notice.countCandidate }}) candidates
                                        </span>
                                        <!-- prettier-ignore -->
                                        <span>
                                            <span>{{ 'was_add_from_rabotaua' | translate }}</span><span ng-if='notice.countOfMerged > 0'>.</span>
                                        </span>
                                        <!--<span ng-if="notice.countCandidate > 0">{{ 'and' | translate }}</span>-->
                                    </span>
                                    <!-- prettier-ignore -->
                                    <span ng-if="notice.countCandidateToUpdate > 1">
                                        <span>{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{ notice.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '') }}
                                        </a>
                                        was
                                        <span ng-if="notice.countCandidate > 1">added new ({{ notice.countCandidate }})</span>
                                        <span ng-if="notice.countCandidate > 1">and</span>
                                        <span>updated and added ({{ notice.countCandidateToUpdate }}) candidates</span>
                                        <span>{{ 'was_add_from_rabotaua' | translate }}</span><span ng-if="notice.countOfMerged">.</span>
                                    </span>
                                </div>

                                <div ng-if="notice.updateCandidate" style="display: flex; flex-direction: column">
                                    <span class="menuCandidate" ng-repeat="candidate in notice.candidatesToUpdate">
                                        <a ng-if="$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                                            {{ candidate.fullNameEn }}
                                            <span class="comma">{{ $last ? '' : ', ' }}</span>
                                        </a>
                                        <a ng-if="!$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                                            {{ candidate.fullName }}
                                            <span class="comma">{{ $last ? '' : ', ' }}</span>
                                        </a>
                                    </span>
                                    <a ng-if="notice.candidatesToUpdate.length > 9" href="#/candidates">{{ 'and other' | translate }}</a>
                                </div>

                                <div ng-if="notice.countCandidate > 0 && $root.currentLang !== 'en'">
                                    <span ng-if="notice.countCandidate === 1">
                                        <span>{{ 'Recalls.added_1' | translate }}</span>
                                        <span>{{ 'Recalls.candidate_1' | translate }}</span>
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span ng-if="notice.candidates[0].status == 'deleted'" class="red" once-text="notice.candidates[0].fullName"></span>
                                        <span>{{ 'was_add_from_rabotaua' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                    </span>
                                    <span ng-if="notice.countCandidate !== 1">
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.added_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.added_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.added_2' | translate }}</span>
                                        <span>{{ 'Recalls.new' | translate }}</span>
                                        <span>({{ notice.countCandidate }})</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.candidate_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.candidate_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.candidate_3' | translate }}</span>
                                        <!-- prettier-ignore -->
                                        <span>
                                            {{ 'was_add_from_rabotaua' | translate }}<span ng-if="notice.countOfMerged > 0">.</span>
                                        </span>
                                    </span>
                                </div>

                                <div ng-if="notice.showAllCandidates" style="display: flex; flex-direction: column">
                                    <span class="menuCandidate" ng-repeat="candidate in notice.candidates">
                                        <a ng-if="$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                                            {{ candidate.fullNameEn }}
                                            <span class="comma">{{ $last ? '' : ', ' }}</span>
                                        </a>
                                        <a ng-if="!$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                                            {{ candidate.fullName }}
                                            <span class="comma">{{ $last ? '' : ', ' }}</span>
                                        </a>
                                    </span>
                                    <a ng-if="notice.candidates.length > 9" href="#/candidates">{{ 'and other' | translate }}</a>
                                </div>

                                <div ng-if="$root.currentLang == 'en' && notice.countCandidateToUpdate <= 0">
                                    <span ng-if="notice.countCandidate === 1">
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span>was added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span>{{ 'was_add_from_rabotaua' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                    </span>

                                    <span ng-if="notice.countCandidate !== 1">
                                        <span>{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span>was added ({{ notice.countCandidate }})</span>
                                        <span>{{ 'was_add_from_rabotaua' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                    </span>
                                </div>

                                <div ng-if="notice.countOfMerged > 0">
                                    <span>
                                        {{
                                            'We noticed duplicate candidates in the reviews for this vacancy and did not create separate profiles for them, but combined them. Number of such candidates'
                                                | translate
                                        }}
                                    </span>
                                    <span>({{ notice.countOfMerged }}).</span>
                                    <span>
                                        {{
                                            'Therefore, there are a lot of vacancies for the vacancies themselves, and we can challenge them. In this case, it is necessary to take up duplicates at the same time for each vacancy.'
                                                | translate
                                        }}
                                    </span>
                                </div>
                            </div>

                            <div class="notice" ng-switch-when="addHHRecall">
                                <div ng-if="$root.currentLang !== 'en'">
                                    <span>{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                    <a ng-click="toVacancy(notice.vacancy.localId)">
                                        {{
                                            notice.vacancy.position +
                                                ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                        }}
                                    </a>
                                    <span ng-if="notice.countCandidate === 1">
                                        <span>{{ 'Recalls.added_1' | translate }}</span>
                                        <span>{{ 'Recalls.candidate_1' | translate }}</span>
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span ng-if="notice.candidates[0].status == 'deleted'" class="red" once-text="notice.candidates[0].fullName"></span>
                                        <span>{{ 'from hh' | translate }}</span>
                                    </span>
                                    <span ng-if="notice.countCandidate !== 1">
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.added_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.added_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.added_2' | translate }}</span>
                                        <span>({{ notice.countCandidate }})</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.candidate_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.candidate_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.candidate_3' | translate }}</span>
                                        <span>{{ 'from hh' | translate }}</span>
                                    </span>
                                </div>
                                <div ng-if="$root.currentLang == 'en'">
                                    <span ng-if="notice.countCandidate === 1">
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span>was added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span>{{ 'from hh' | translate }}</span>
                                    </span>
                                    <span ng-if="notice.countCandidate !== 1">
                                        <span>({{ notice.countCandidate }})</span>
                                        <span>applicants were added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span>{{ 'from hh' | translate }}</span>
                                    </span>
                                </div>
                            </div>

                            <div class="notice" ng-switch-when="addGrcUaRecall">
                                <div ng-if="$root.currentLang !== 'en'">
                                    <span ng-if="notice.countCandidateToUpdate > 0">{{ 'to_in' | translate }}</span>
                                    <span ng-if="notice.countCandidateToUpdate > 0">{{ 'history_info.stage of vacancy' | translate }}</span>
                                    <span ng-if="notice.countCandidateToUpdate <= 0">{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                    <a ng-click="toVacancy(notice.vacancy.localId)">
                                        {{
                                            notice.vacancy.position +
                                                ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                        }}
                                    </a>

                                    <div ng-if="notice.countCandidateToUpdate > 0">
                                        <span ng-if="notice.countCandidateToUpdate === 1">
                                            <span>{{ 'updated' | translate }}</span>
                                            <span>{{ 'Recalls.candidate_1' | translate }}</span>
                                            <a
                                                ng-if="notice.candidatesToUpdate[0].status != 'deleted'"
                                                ng-click="toCandidate(notice.candidatesToUpdate[0].localId)"
                                                ng-href="#/candidates/{{::notice.candidatesToUpdate[0].localId}}"
                                            >
                                                <span ng-if="$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullNameEn }}</span>
                                                <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullName }}</span>
                                            </a>
                                            <span
                                                ng-if="notice.candidatesToUpdate[0].status == 'deleted'"
                                                class="red"
                                                once-text="notice.candidatesToUpdate[0].fullName"
                                            ></span>
                                            <span>{{ 'from grc' | translate }}</span>
                                            <span ng-if="notice.countCandidate <= 0">.</span>
                                        </span>
                                        <span ng-if="notice.countCandidateToUpdate !== 1">
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 0">{{ 'updated1' | translate }}</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 2">{{ 'updated' | translate }}</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 1">{{ 'updated' | translate }}</span>
                                            <span>{{ notice.countCandidateToUpdate }}</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 0">{{ 'Recalls.candidate_1' | translate }}</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 1">{{ 'Recalls.candidate_2' | translate }}</span>
                                            <span ng-if="getLettersEnd(notice.countCandidateToUpdate) == 2">{{ 'Recalls.candidate_3' | translate }}</span>
                                            <span>{{ 'from grc' | translate }}</span>
                                            <span ng-if="notice.countCandidateToUpdate > 0 && notice.countCandidate === 0">.</span>
                                        </span>
                                        <span ng-if="notice.countCandidate > 0">{{ 'and' | translate }}</span>
                                    </div>
                                    <div ng-if="$root.currentLang == 'en'">
                                        <span ng-if="notice.countCandidateToUpdate === 1">
                                            <a
                                                ng-if="notice.candidatesToUpdate[0].status != 'deleted'"
                                                ng-click="toCandidate(notice.candidatesToUpdate[0].localId)"
                                                ng-href="#/candidates/{{::notice.candidatesToUpdate[0].localId}}"
                                            >
                                                <span ng-if="$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullNameEn }}</span>
                                                <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidatesToUpdate[0].fullName }}</span>
                                            </a>
                                            <span>was added on the vacancy</span>
                                            <a ng-click="toVacancy(notice.vacancy.localId)">
                                                {{
                                                    notice.vacancy.position +
                                                        ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                                }}
                                            </a>
                                            <span>{{ 'from grc' | translate }}</span>
                                            <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                        </span>
                                        <span ng-if="notice.countCandidateToUpdate !== 1">
                                            <span>{{ notice.countCandidateToUpdate }}</span>
                                            <span>applicants were added on the vacancy</span>
                                            <a ng-click="toVacancy(notice.vacancy.localId)">
                                                {{
                                                    notice.vacancy.position +
                                                        ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                                }}
                                            </a>
                                            <span>{{ 'from grc' | translate }}</span>
                                            <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                        </span>
                                    </div>
                                </div>

                                <div ng-if="notice.countCandidate > 0">
                                    <span ng-if="notice.countCandidate === 1">
                                        <span>{{ 'Recalls.added_1' | translate }}</span>
                                        <span>{{ 'Recalls.candidate_1' | translate }}</span>
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span ng-if="notice.candidates[0].status == 'deleted'" class="red" once-text="notice.candidates[0].fullName"></span>
                                        <span>{{ 'from grc' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                    </span>
                                    <span ng-if="notice.countCandidate !== 1">
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.added_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.added_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.added_2' | translate }}</span>
                                        <span>({{ notice.countCandidate }})</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.candidate_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.candidate_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.candidate_3' | translate }}</span>
                                        <span>{{ 'from grc' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0">.</span>
                                    </span>
                                </div>
                                <div ng-if="$root.currentLang == 'en'">
                                    <span ng-if="notice.countCandidate === 1">
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span>was added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span>{{ 'from grc' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0 || notice.countOfMerged > 0">.</span>
                                    </span>
                                    <span ng-if="notice.countCandidate !== 1">
                                        <span>({{ notice.countCandidate }})</span>
                                        <span>applicants were added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                        <span>{{ 'from grc' | translate }}</span>
                                        <span ng-if="notice.countCandidateToUpdate > 0 || notice.countOfMerged > 0" style="position: relative; right: 3px">
                                            .
                                        </span>
                                    </span>
                                </div>

                                <div ng-if="notice.countOfMerged > 0">
                                    <span>
                                        {{
                                            'We noticed duplicate candidates in the reviews for this vacancy and did not create separate profiles for them, but combined them. Number of such candidates'
                                                | translate
                                        }}
                                    </span>
                                    <span>({{ notice.countOfMerged }}).</span>
                                    <span>
                                        {{
                                            'Therefore, there are a lot of vacancies for the vacancies themselves, and we can challenge them. In this case, it is necessary to take up duplicates at the same time for each vacancy.'
                                                | translate
                                        }}
                                    </span>
                                </div>
                            </div>

                            <div ng-switch-when="parserEditCandidate">
                                {{ 'New resumes has been received from email' | translate }} {{ notice.value }}. {{ 'Updated candidates' | translate }}:
                                <!-- prettier-ignore -->
                                <span ng-if="$index < 10" ng-repeat="candidate in notice.candidates track by $index">
                                    <span ng-if="candidate.status == 'deleted'" class="red" once-text="candidate.fullName"></span>
                                    <a ng-if="candidate.status != 'deleted'" href="#/candidates/{{ candidate.localId }}" once-text="candidate.fullName"></a><span ng-show="notice.candidates.length != $index + 1">,</span>
                                </span>
                                <a ng-if="notice.candidates.length > 9" href="#/candidates">{{ 'and other' | translate }}</a>
                            </div>

                            <div class="notice pdcs-notice" ng-switch-when="candidateEditPdcs">
                                <span ng-if="notice.candidates[0].status == 'deleted'" class="red" once-text="notice.candidates[0].fullName"></span>
                                <span ng-if="notice.candidates[0].status != 'deleted'" translate="Candidate"></span>
                                <a ng-if="notice.candidates[0].status != 'deleted'" href="#/candidates/{{ notice.candidates[0].localId }}">
                                    <span once-text="notice.candidates[0].fullName"></span>
                                </a>
                                <span ng-if="notice.value === 'rejected'" translate="refused to give his consent for processing his personal data"></span>
                                <span ng-if="notice.value === 'confirmed'" translate="has given his consent for processing his personal data"></span>
                            </div>
                            <div ng-switch-when="testPassed">
                                <span ng-repeat="candidate in notice.candidates track by $index">
                                    <a
                                        ng-if="candidate.status != 'deleted' && $root.useAmericanNameStyle"
                                        href="#/candidates/{{ candidate.localId }}"
                                        once-text="candidate.fullNameEn"
                                    ></a>
                                    <a
                                        ng-if="candidate.status != 'deleted' && !$root.useAmericanNameStyle"
                                        href="#/candidates/{{ candidate.localId }}"
                                        once-text="candidate.fullName"
                                    ></a>
                                    <span
                                        ng-if="candidate.status == 'deleted' && $root.useAmericanNameStyle"
                                        class="red"
                                        once-text="notice.candidates[0].fullNameEn"
                                    ></span>
                                    <span
                                        ng-if="candidate.status == 'deleted' && !$root.useAmericanNameStyle"
                                        class="red"
                                        once-text="notice.candidates[0].fullName"
                                    ></span>
                                    <span ng-show="notice.candidates.length != $index + 1">,</span>
                                </span>
                                {{ 'history_info.Candidate test passed' | translate }} "{{ notice.value }}"
                                {{ 'history_info.test' | translate }}
                            </div>
                            <div ng-switch-when="parserAddCandidate">
                                <span>
                                    <span>
                                        {{ 'New resumes has been received from email' | translate }}
                                        {{ notice.value }}. {{ 'Added new candidates' | translate }}:
                                    </span>
                                    <span ng-repeat="candidate in notice.candidates track by $index">
                                        <span ng-if="$index < 10" style="display: inline-flex">
                                            <a href="#/candidates/{{ candidate.localId }}">{{ candidate.fullName }}</a>
                                            <span ng-hide="$last">,</span>
                                        </span>
                                    </span>
                                    <a ng-if="notice.candidates.length > 9" href="#/candidates">{{ 'and other' | translate }}</a>
                                </span>
                            </div>

                            <div ng-switch-when="parserValidEmail">{{ 'parserValidEmail_1' | translate }} {{ notice.value }} {{ 'parserValidEmail_2' }}</div>

                            <div ng-switch-when="addRecall">
                                <div ng-if="$root.currentLang !== 'en'">
                                    <span>{{ 'Recalls.at_the_vacancy' | translate }}</span>
                                    <a ng-click="toVacancy(notice.vacancy.localId)">
                                        {{
                                            notice.vacancy.position +
                                                ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' + ' (' + notice.vacancy.clientId.name + ')' : '')
                                        }}
                                    </a>
                                    <span ng-if="notice.countCandidate === 1">
                                        <span>{{ 'Recalls.added_1' | translate }}</span>
                                        <span>{{ 'Recalls.candidate_1' | translate }}</span>
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span ng-if="notice.candidates[0].status == 'deleted'" class="red" once-text="notice.candidates[0].fullName"></span>
                                    </span>
                                    <span ng-if="notice.countCandidate !== 1">
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.added_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.added_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.added_2' | translate }}</span>
                                        <span>({{ notice.countCandidate }})</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 0">{{ 'Recalls.candidate_1' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 1">{{ 'Recalls.candidate_2' | translate }}</span>
                                        <span ng-if="getLettersEnd(notice.countCandidate) == 2">{{ 'Recalls.candidate_3' | translate }}</span>
                                    </span>
                                </div>
                                <div ng-if="$root.currentLang == 'en'">
                                    <span ng-if="notice.countCandidate === 1">
                                        <a
                                            ng-if="notice.candidates[0].status != 'deleted'"
                                            ng-click="toCandidate(notice.candidates[0].localId)"
                                            ng-href="#/candidates/{{::notice.candidates[0].localId}}"
                                        >
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidates[0].fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidates[0].fullName }}</span>
                                        </a>
                                        <span>was added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' + ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                    </span>
                                    <span ng-if="notice.countCandidate !== 1">
                                        <span>({{ notice.countCandidate }})</span>
                                        <span>applicants were added on the vacancy</span>
                                        <a ng-click="toVacancy(notice.vacancy.localId)">
                                            {{
                                                notice.vacancy.position +
                                                    ($root.me.personParams.clientAccessLevel !== 'hide' ? ' (' + notice.vacancy.clientId.name + ')' : '')
                                            }}
                                        </a>
                                    </span>
                                </div>
                            </div>
                            <div ng-switch-when="referralBonus">
                                <span translate="Your referral bonus is already available to you" translate-values="{bonusAmount: notice.value}"></span>
                                <a translate="Receive bonus" ui-sref="referral.earnings"></a>
                            </div>
                            <div class="responsible" ng-switch-when="addResponsible">
                                <span ng-if="$root.currentLang !== 'en'">
                                    <a ng-if="notice.person.status != 'D'" ng-href="#/users/{{ notice.person.userId }}">
                                        <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                        <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                    </a>
                                    <span ng-if="notice.person.status == 'D'" ng-class="{ red: notice.person.status == 'D' }">
                                        <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                        <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                    </span>
                                    <span ng-if="notice.task">
                                        {{ 'were appointed as responsible for the task' | translate }} "
                                        <span>{{ notice.task.title }}</span>
                                        "
                                    </span>
                                    <span ng-if="notice.vacancy">
                                        <span class="lowercase">
                                            {{ 'were appointed as responsible' | translate }} {{ 'by_1' | translate }} {{ 'vacancy' | translate }}
                                        </span>
                                        <a ng-href="#/vacancies/{{ notice.vacancy.localId }}">
                                            <span>
                                                {{
                                                    notice.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '')
                                                        | limitToEllipse : 300
                                                }}
                                            </span>
                                        </a>
                                        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ notice.vacancy.clientId.localId }}">
                                            <span>({{ notice.vacancy.clientId.name }})</span>
                                        </a>
                                    </span>
                                    <span ng-if="notice.candidate">
                                        <span class="lowercase">{{ 'were appointed as responsible' | translate }} {{ 'by candidate' | translate }}</span>
                                        <a ng-if="notice.candidate.status != 'deleted'" ng-href="#/candidates/{{ notice.candidate.localId }}">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                        </a>
                                        <span ng-if="notice.candidate.status == 'deleted'" ng-class="{ red: notice.candidate.status == 'deleted' }">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                        </span>
                                    </span>
                                    <span ng-if="notice.client">
                                        <span class="lowercase">{{ 'were appointed as responsible' | translate }} {{ 'by client' | translate }}</span>
                                        <a ng-href="#/clients/{{ notice.client.localId }}">{{ notice.client.name }}</a>
                                    </span>
                                </span>
                                <span ng-if="$root.currentLang == 'en'">
                                    <span ng-if="notice.task">
                                        {{ 'You were appointed as responsible for the task' | translate }}
                                        "
                                        <span>{{ notice.task.title }}</span>
                                        " {{ 'by_1' | translate }}
                                        <a ng-if="notice.person.status != 'D'" ng-href="#/users/{{ notice.person.userId }}">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                        </a>
                                        <span ng-if="notice.person.status == 'D'" ng-class="{ red: notice.person.status == 'D' }">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                        </span>
                                    </span>
                                    <span ng-if="notice.vacancy">
                                        <span translate="You were appointed as responsible"></span>
                                        {{ 'on' | translate }}
                                        <span class="lowercase">{{ 'vacancy' | translate }}</span>
                                        <a ng-href="#/vacancies/{{ notice.vacancy.localId }}">
                                            <span>
                                                {{
                                                    notice.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '')
                                                        | limitToEllipse : 300
                                                }}
                                            </span>
                                        </a>
                                        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ notice.vacancy.clientId.localId }}">
                                            <span>({{ notice.vacancy.clientId.name }})</span>
                                        </a>
                                    </span>
                                    <span ng-if="notice.candidate">
                                        <span translate="You were appointed as responsible"></span>
                                        {{ 'on' | translate }}
                                        <span class="lowercase">{{ 'candidate' | translate }}</span>
                                        <a ng-if="notice.candidate.status != 'deleted'" ng-href="#/candidates/{{ notice.candidate.localId }}">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                        </a>
                                        <span ng-if="notice.candidate.status == 'deleted'" ng-class="{ red: notice.candidate.status == 'deleted' }">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                        </span>
                                    </span>
                                    <span ng-if="notice.client">
                                        <span translate="You were appointed as responsible"></span>
                                        {{ 'on' | translate }}
                                        <span class="lowercase">{{ 'client' | translate }}</span>
                                        <a ng-href="#/clients/{{ notice.client.localId }}">{{ notice.client.name }}</a>
                                    </span>
                                </span>
                            </div>
                            <div class="notice" ng-switch-when="candidatesDeleteLimit">
                                <div ng-if="$root.me.userId === notice.person.personId">
                                    <span>{{ 'You_have_reached_the_daily_limit_for_removing_candidates_You_can_continue_tomorrow' | translate }}</span>
                                </div>
                                <div ng-if="$root.me.userId !== notice.person.personId">
                                    <span>{{ 'deleteLimit.user' | translate }}</span>
                                    <span>
                                        <a ng-if="notice.person.status != 'D'" ng-href="#/users/{{ notice.person.userId }}">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                        </a>
                                        <span ng-if="notice.person.status == 'D'" ng-class="{ red: notice.person.status == 'D' }">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                        </span>
                                    </span>
                                    <span>{{ 'deleteLimit.removed' | translate }}</span>
                                    <span>{{ notice.value }}</span>
                                    <span>{{ 'deleteLimit.candidates_within_one_day_and_reached_the_daily_limit_for_removing_candidates' | translate }}</span>
                                </div>
                            </div>
                            <div class="notice" ng-switch-when="usersDeleteLimit">
                                <div ng-if="$root.me.userId === notice.person.personId">
                                    <span>{{ 'You_have_reached_the_daily_limit_for_removing_users_You_can_continue_tomorrow' | translate }}</span>
                                </div>
                                <div ng-if="$root.me.userId !== notice.person.personId">
                                    <span>{{ 'deleteLimit.user' | translate }}</span>
                                    <span>
                                        <a ng-if="notice.person.status != 'D'" ng-href="#/users/{{ notice.person.userId }}">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                        </a>
                                        <span ng-if="notice.person.status == 'D'" ng-class="{ red: notice.person.status == 'D' }">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                        </span>
                                    </span>
                                    <span>{{ 'deleteLimit.removed' | translate }}</span>
                                    <span>{{ notice.value }}</span>
                                    <span>{{ 'deleteLimit.users_within_one_day_and_reached_the_daily_limit_for_removing_users' | translate }}</span>
                                </div>
                            </div>
                            <div class="taskChangeStatus" ng-switch-when="taskChangeStatus">
                                <div ng-if="notice.newStatus == 'Cancelled'">
                                    <a ng-href="#/users/{{ notice.person.userId }}">
                                        <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                        <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                    </a>
                                    {{ 'has marked the task' | translate }}
                                    "
                                    <span>{{ notice.task.title }}</span>
                                    "
                                    <span ng-if="$root.currentLang == 'en'">{{ 'as not actual' | translate }}</span>
                                    {{ 'for the' | translate }}
                                    <span ng-if="notice.vacancy">
                                        <span class="lowercase">{{ 'vacancy' | translate }}</span>
                                        <a ng-href="#/vacancies/{{ notice.vacancy.localId }}">{{ notice.vacancy.position }}</a>
                                    </span>
                                    <span ng-if="notice.candidate">
                                        <span class="lowercase">{{ 'candidate_1' | translate }}</span>
                                        <a ng-if="notice.candidate.status != 'deleted'" ng-href="#/candidates/{{ notice.candidate.localId }}">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                        </a>
                                        <span ng-if="notice.candidate.status == 'deleted'" ng-class="{ red: notice.candidate.status == 'deleted' }">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                        </span>
                                    </span>
                                    <span ng-if="notice.client">
                                        <span class="lowercase">{{ 'client_1' | translate }}</span>
                                        <a ng-href="#/clients/{{ notice.client.localId }}">{{ notice.client.name }}</a>
                                    </span>
                                </div>
                                <div ng-if="notice.newStatus == 'open'">
                                    <a ng-href="#/users/{{ notice.person.userId }}">
                                        <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                        <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                    </a>
                                    <span>{{ 'changed the status of the task' | translate }}</span>
                                    "
                                    <span>{{ notice.task.title }}</span>
                                    "
                                    <span ng-if="notice.vacancy">
                                        {{ 'for the' | translate }}
                                        <span class="lowercase">{{ 'vacancy' | translate }}</span>
                                        <a ng-href="#/vacancies/{{ notice.vacancy.localId }}">{{ notice.vacancy.position }}</a>
                                    </span>
                                    <span>{{ "to 'Not completed'" | translate }}</span>
                                </div>
                                <div ng-if="notice.newStatus == 'completed'">
                                    <a ng-href="#/users/{{ notice.person.userId }}">
                                        <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                        <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                    </a>
                                    {{ 'has completed the task' | translate }}
                                    "
                                    <span>{{ notice.task.title }}</span>
                                    "
                                    {{ 'for the' | translate }}
                                    <span ng-if="notice.vacancy">
                                        <span class="lowercase">{{ 'vacancy' | translate }}</span>
                                        <a ng-href="#/vacancies/{{ notice.vacancy.localId }}">{{ notice.vacancy.position }}</a>
                                    </span>
                                    <span ng-if="notice.candidate">
                                        <span class="lowercase">{{ 'candidate_1' | translate }}</span>
                                        <a ng-if="notice.candidate.status != 'deleted'" ng-href="#/candidates/{{ notice.candidate.localId }}">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                        </a>
                                        <span ng-if="notice.candidate.status == 'deleted'" ng-class="{ red: notice.candidate.status == 'deleted' }">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                        </span>
                                    </span>
                                    <span ng-if="notice.client">
                                        <span class="lowercase">{{ 'by client' | translate }}</span>
                                        <a ng-href="#/clients/{{ notice.client.localId }}">{{ notice.client.name }}</a>
                                    </span>
                                </div>
                                <div ng-if="notice.newStatus == 'deleted'">
                                    <a ng-href="#/users/{{ notice.person.userId }}">
                                        <span ng-if="$root.useAmericanNameStyle">{{ notice.person.fullNameEn }}</span>
                                        <span ng-if="!$root.useAmericanNameStyle">{{ notice.person.fullName }}</span>
                                    </a>
                                    {{ 'deleted the task' | translate }}
                                    "
                                    <span>{{ notice.task.title }}</span>
                                    "
                                    {{ 'for the' | translate }}
                                    <span ng-if="notice.vacancy">
                                        <span class="lowercase">{{ 'vacancy' | translate }}</span>
                                        <a ng-href="#/vacancies/{{ notice.vacancy.localId }}">{{ notice.vacancy.position }}</a>
                                    </span>
                                    <span ng-if="notice.candidate">
                                        <span class="lowercase">{{ 'candidate_1' | translate }}</span>
                                        <a ng-if="notice.candidate.status != 'deleted'" ng-href="#/candidates/{{ notice.candidate.localId }}">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                        </a>
                                        <span ng-if="notice.candidate.status = 'deleted'" ng-class="{ red: notice.candidate.status == 'deleted' }">
                                            <span ng-if="$root.useAmericanNameStyle">{{ notice.candidate.fullNameEn }}</span>
                                            <span ng-if="!$root.useAmericanNameStyle">{{ notice.candidate.fullName }}</span>
                                        </span>
                                    </span>
                                    <span ng-if="notice.client">
                                        <span class="lowercase">{{ 'by client' | translate }}</span>
                                        <a ng-href="#/clients/{{ notice.client.localId }}">{{ notice.client.name }}</a>
                                    </span>
                                </div>
                            </div>
                            <div ng-switch-when="videoInterviewPass">
                                <span
                                    translate="Candidate {candidate_name} has taken the video interview on VCV vacancy {vacancy_name_VCV}"
                                    translate-values="{candidate_name: notice.candidate.fullName, candidate_href: '#/candidates/' + notice.candidate.localId,
                                                         vacancy_VCV: notice.value}"
                                ></span>
                            </div>
                            <i
                                ng-show="notice.read"
                                ng-click="readNotice(notice)"
                                class="fa fa-check-circle-o hidden-xs"
                                ng-class="{ green: notice.read != false }"
                                aria-hidden="true"
                                ng-attr-title="{{ (notice.read != false && ('Viewed' | translate)) || ('Not viewed' | translate) }}"
                            ></i>
                            <a
                                ng-show="!notice.read"
                                class="btn btn-success view-letter-button"
                                ng-switch-when="addNewInboxMails"
                                ui-sref="candidate-slide({'isFromNotificationsLetters': true, 'sliderDataId': sliderId, 'id': notice.candidate.localId, 'candidateObj': notice.candidate})"
                            >
                                {{ 'To view' | translate }}
                            </a>
                        </div>
                    </div>
                </div>
                <div ng-show="notices[0].object.length > 0 && totalPages > 1" class="pagination-block">
                    <div ng-show="searchNumber > 1" ng-click="changePage(-1)" style="margin-right: 25px">
                        <img alt="" src="images/sprite/arrowLeft.svg" />
                        <span style="color: #999999; margin-left: 10px" translate="_Previous"></span>
                    </div>
                    {{ 'page' | translate }}
                    <input ng-change="changeInputPage(params,searchNumber)" ng-model="searchNumber" type="text" />
                    {{ 'from_3' | translate }} {{ totalPages }}
                    <div ng-show="searchNumber < totalPages" ng-click="changePage(+1)" style="margin-left: 25px">
                        <span style="color: #999999; margin-right: 10px" translate="_Next"></span>
                        <img alt="" src="images/sprite/arrowRight.svg" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
