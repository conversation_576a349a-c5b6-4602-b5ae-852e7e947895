<div class="block-client">
    <div class="container-fluid">
        <div class="breadcrumbs hidden-sm hidden-xs">
            <ol class="breadcrumb">
                <li>
                    <a href="#/clients">{{ 'clients' | translate }}</a>
                </li>
                <li class="active">{{ client.name }}</li>
            </ol>
        </div>
        <div class="main-page-title">
            {{ client.name }}
            <span ng-show="client.site">
                ,&nbsp;
                <a href="http://{{ client.site }}" target="_blank">{{ client.site | cutName : true : 30 }}</a>
            </span>
            <input id="fileTemplate" class="hidden" type="file" oi-file="optionsForTemplate" />
        </div>

        <div class="main-content">
            <div class="block-client__left-block">
                <div class="flex-row flex-block flex-column">
                    <div ng-show="client.logoId" class="logo flex-justify-center flex-align-center">
                        <input id="file" style="display: none" type="file" oi-file="options" />
                        <img ng-src="{{ serverAddress }}/getapp?id={{ client.logoId }}&d={{ $root.me.personId }}" alt="" />
                    </div>
                    <div class="status">
                        <div class="form-title">{{ 'Status' | translate }}</div>
                        <custom-select-new-status
                            class="status__select"
                            style="width: 100%; position: relative; min-height: 32px; height: 32px; display: block"
                            model="selectedStatus"
                            ng-init="selectedStatus = client.state"
                            path="'name'"
                            options="statusNames"
                            placeholder="'status'"
                            custom-status-styles="true"
                            hide-placeholder-from-options="true"
                            method="showChangeStatusOfClient"
                        ></custom-select-new-status>
                    </div>
                    <div
                        ng-show="$root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter' || $root.me.recrutRole == 'salesmanager'"
                        class="responsibles"
                    >
                        <span class="form-title">{{ 'Responsible for a client' | translate }}</span>
                        <custom-select-for-profiles
                            ng-show="$root.me.recrutRole != 'client'"
                            style="position: relative; display: block"
                            model="'responsible'"
                            placeholder="'responsible'"
                            hide-placeholder-from-options="true"
                            method="showAddResponsibleUser"
                            options="persons"
                            path="'fullName'"
                        ></custom-select-for-profiles>
                        <div class="responsible__item" style="word-break: break-all" ng-repeat="responsible in client.responsiblesPerson">
                            <a class="inline" href="#users/{{ responsible.responsible.userId }}" title="{{ responsible.responsible.fullName }}">
                                <span ng-if="!$root.useAmericanNameStyle">{{ responsible.responsible.fullName }}</span>
                                <span ng-if="$root.useAmericanNameStyle">{{ responsible.responsible.fullNameEn }}</span>
                            </a>
                            <img
                                ng-click="showRemoveResponsibleUser(responsible.responsible);"
                                style="width: 10px; margin-left: 3px"
                                src="images/sprite/close-icon.svg"
                                alt=""
                            />
                        </div>
                    </div>
                </div>
                <div class="flex-row flex-block flex-column active-vacancies section">
                    <div class="form-title-main">{{ 'Active vacancies' | translate }}</div>
                    <div class="vacancies">
                        <div
                            ng-show="vacancy.status != 'deleted' && vacancy.status != 'completed' && vacancy.status != 'canceled'"
                            class="vacancy row"
                            ng-repeat="vacancy in client.vacancies.objects | limitTo: 3"
                        >
                            <div class="col-lg-8 position-relative" style="padding: 0">
                                <a
                                    style="word-break: break-all"
                                    view="tooltip-white-hint"
                                    tooltip-move
                                    data-tooltip="{{ vacancy.position }}"
                                    href="#/vacancies/{{ vacancy.localId }}"
                                >
                                    {{ vacancy.position | limitToEllipse : 151 }}
                                </a>
                                <br />
                                <span>
                                    <span ng-if="vacancy.employmentType !== 'remote'">
                                        {{ vacancy.region.displayFullName }}
                                    </span>
                                    <!-- prettier-ignore -->
                                    <span ng-if="vacancy.employmentType === 'remote'">
                                        {{ 'remote_1' | translate }}<span ng-if="vacancy.region.country || vacancy.region.city">,</span>
                                        <span ng-if="!vacancy.region.city">
                                            {{ vacancy.region.displayCountry }}
                                        </span>
                                        <span ng-if="vacancy.region.country && vacancy.region.city">
                                            {{ vacancy.region.displayCity }}
                                        </span>
                                    </span>
                                </span>
                            </div>
                            <div
                                class="col-lg-4 padding-right-0"
                                ng-class="{
                                    greenStatus:
                                        vacancy.status === 'open' ||
                                        vacancy.status === 'inwork' ||
                                        vacancy.status === 'completed' ||
                                        vacancy.status === 'future' ||
                                        vacancy.status === 'in_work' ||
                                        vacancy.status === 'all_done',
                                    yellowStatus:
                                        vacancy.status !== 'open' &&
                                        vacancy.status !== 'inwork' &&
                                        vacancy.status !== 'completed' &&
                                        vacancy.status !== 'future' &&
                                        vacancy.status !== 'in_work' &&
                                        vacancy.status !== 'all_done'
                                }"
                            >
                                {{ 'vacancy_status_assoc.' + vacancy.status | translate }}
                            </div>
                        </div>
                    </div>
                    <a
                        ng-show="client.vacancies.total > 0 && client.vacancies.total != inactiveVacancies.length"
                        ng-click="$root.allClientsVacancies = true"
                        class="all-vacancy"
                        href="#/vacancies?clientId={{ client.clientId }}"
                    >
                        {{ 'All_active_clients_vacancies' | translate }}
                    </a>
                    <div class="flex-justify-center">
                        <a
                            ng-click="toAddVacancy(client)"
                            class="btn_default btn_thin btn_success"
                            style="color: white"
                            type="button"
                            href="#vacancy/add?{{ client.clientId }}"
                        >
                            {{ 'add_vacancy' | translate }}
                        </a>
                    </div>
                </div>
                <div ng-if="isInactive(client.vacancies.objects)" class="flex-row flex-block flex-column inactive-vacancies section">
                    <div class="form-title-main">{{ 'Inactive vacancies' | translate }}</div>
                    <div class="vacancies">
                        <div
                            ng-show="vacancy.status == 'deleted' || vacancy.status == 'completed' || vacancy.status == 'canceled'"
                            class="vacancy row"
                            ng-repeat="vacancy in inactiveVacancies |  limitTo: 3"
                        >
                            <div class="col-lg-8" style="padding: 0">
                                <a href="#/vacancies/{{ vacancy.localId }}">
                                    {{ vacancy.position }}
                                </a>
                                <br />
                                <span>{{ vacancy.region.displayFullName }}</span>
                            </div>
                            <div
                                class="col-lg-4 padding-right-0"
                                ng-class="{
                                    greenStatus:
                                        vacancy.status === 'open' ||
                                        vacancy.status === 'inwork' ||
                                        vacancy.status === 'completed' ||
                                        vacancy.status === 'future' ||
                                        vacancy.status === 'in_work' ||
                                        vacancy.status === 'all_done',
                                    yellowStatus:
                                        vacancy.status !== 'open' &&
                                        vacancy.status !== 'inwork' &&
                                        vacancy.status !== 'completed' &&
                                        vacancy.status !== 'future' &&
                                        vacancy.status !== 'in_work' &&
                                        vacancy.status !== 'all_done'
                                }"
                            >
                                {{ 'vacancy_status_assoc.' + vacancy.status | translate }}
                            </div>
                        </div>
                    </div>
                    <a
                        ng-if="client.vacancies.total > 0"
                        ng-click="$root.allClientsVacancies = true"
                        class="all-vacancy"
                        href="#/vacancies?clientId={{ client.clientId }}&states=completed,canceled,deleted"
                    >
                        {{ 'All_inactive_clients_vacancies' | translate }}
                    </a>
                </div>
                <div class="flex-row flex-block flex-column attachments not-for-print" uib-dropdown is-open="status.isopen">
                    <div class="form-title-main">{{ 'Attachments' | translate }}</div>
                    <div
                        ng-show="client.files"
                        class="flex-align-center flex-space-between flex-item-block file-item attachments__item"
                        ng-repeat="file in client.files| orderBy:'-url'"
                    >
                        <div ng-show="file.showEditFileName" class="editFileName">
                            <h5 ng-show="file.showEditFileName" class="form-title">
                                {{ 'Change file name' | translate }}
                            </h5>
                            <div style="display: flex; justify-content: space-between; align-items: center">
                                <div style="display: flex; align-items: center">
                                    <img ng-show="!file.url" style="margin-right: 5px" src="images/sprite/clip.svg?b2" />
                                    <input
                                        id="showEditFile"
                                        ng-show="file.showEditFileName"
                                        class="form-control"
                                        ng-model="file.fileName"
                                        type="text"
                                        name="name"
                                        value="{{ file.fileName | limitTo : 15 }}"
                                    />
                                </div>
                                <div style="margin-left: 10px">
                                    <button ng-show="file.showEditFileName" ng-click="editFileName(file)" class="btn_default btn_thin btn_success">
                                        {{ 'save' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div ng-show="file.fileId" class="fileName">
                            <a ng-show="!file.url && !file.showEditFileName" ng-click="showModalResume(file)" title="{{ file.fileName }}">
                                <img style="cursor: pointer" src="images/sprite/clip.svg?b2" />
                                <span style="color: #666666; cursor: pointer; word-break: break-word">
                                    {{ file.fileName | fileNameCut : 0 : 25 }}
                                </span>
                            </a>
                            <a ng-show="file.url && !file.showEditFileName" href="{{ file.url }}" target="_blank" title="{{ file.fileName }}">
                                <img style="height: 13px; width: 13px; cursor: pointer" src="images/sprite/link.svg" />
                                <span style="color: #666666; cursor: pointer; word-break: break-word">
                                    {{ file.fileName | limitTo : 20 }}
                                </span>
                                <span ng-show="file.fileName.length > 20">...</span>
                            </a>
                        </div>
                        <i
                            ng-show="!file.showEditFileName"
                            ng-click="MenuEdDelFile(file)"
                            class="fa fa-chevron-down pull-right"
                            style="font-size: 11px; color: black"
                            aria-hidden="true"
                        >
                            <div ng-show="file.showMenuEdDelFile" class="flex-item-block editFileMenu">
                                <div class="ui middle aligned selection list">
                                    <a ng-show="!file.url" ng-click="onDownloadFile(file)" class="text-decoration-none" style="cursor: pointer">
                                        <svg
                                            id="Capa_1"
                                            style="enable-background: new 0 0 477.867 477.867"
                                            fill="#00B549"
                                            height="13px"
                                            width="13px"
                                            version="1.1"
                                            xmlns="http://www.w3.org/2000/svg"
                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                            x="0px"
                                            y="0px"
                                            viewBox="0 0 477.867 477.867"
                                            xml:space="preserve"
                                        >
                                            <g>
                                                <g>
                                                    <path
                                                        d="M443.733,307.2c-9.426,0-17.067,7.641-17.067,17.067v102.4c0,9.426-7.641,17.067-17.067,17.067H68.267
                                                                    c-9.426,0-17.067-7.641-17.067-17.067v-102.4c0-9.426-7.641-17.067-17.067-17.067s-17.067,7.641-17.067,17.067v102.4
                                                                    c0,28.277,22.923,51.2,51.2,51.2H409.6c28.277,0,51.2-22.923,51.2-51.2v-102.4C460.8,314.841,453.159,307.2,443.733,307.2z"
                                                    />
                                                </g>
                                            </g>
                                            <g>
                                                <g>
                                                    <path
                                                        d="M335.947,295.134c-6.614-6.387-17.099-6.387-23.712,0L256,351.334V17.067C256,7.641,248.359,0,238.933,0
                                                                    s-17.067,7.641-17.067,17.067v334.268l-56.201-56.201c-6.78-6.548-17.584-6.36-24.132,0.419c-6.388,6.614-6.388,17.099,0,23.713
                                                                    l85.333,85.333c6.657,6.673,17.463,6.687,24.136,0.031c0.01-0.01,0.02-0.02,0.031-0.031l85.333-85.333
                                                                    C342.915,312.486,342.727,301.682,335.947,295.134z"
                                                    />
                                                </g>
                                            </g>
                                        </svg>
                                        <span class="editFileMenuTitle">{{ 'Download file' | translate }}</span>
                                    </a>
                                    <div ng-click="showEditFileNameFunc(file)" class="item" style="border-radius: 5px; padding: 1px 0px">
                                        <div class="content">
                                            <div ng-show="!file.url" ng-click="editFile(file.fileId)" class="header pencilHover">
                                                <svg width="13" height="12" fill="#00B549" viewBox="0 0 13 12" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.37333 4.01333L7.98667 4.62667L1.94667 10.6667H1.33333V10.0533L7.37333 4.01333ZM9.77333 0C9.60667 0 9.43333 0.0666666 9.30667 0.193333L8.08667 1.41333L10.5867 3.91333L11.8067 2.69333C12.0667 2.43333 12.0667 2.01333 11.8067 1.75333L10.2467 0.193333C10.1133 0.06 9.94667 0 9.77333 0ZM7.37333 2.12667L0 9.5V12H2.5L9.87333 4.62667L7.37333 2.12667Z"
                                                        fill="#00B549"
                                                    />
                                                </svg>
                                                <span class="editFileMenuTitle">
                                                    {{ 'Change file name' | translate }}
                                                </span>
                                            </div>
                                            <div ng-show="file.url" ng-click="editFile(file.fileId)" class="header pencilHover">
                                                <svg width="13" fill="#00B549" height="12" viewBox="0 0 13 12" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.37333 4.01333L7.98667 4.62667L1.94667 10.6667H1.33333V10.0533L7.37333 4.01333ZM9.77333 0C9.60667 0 9.43333 0.0666666 9.30667 0.193333L8.08667 1.41333L10.5867 3.91333L11.8067 2.69333C12.0667 2.43333 12.0667 2.01333 11.8067 1.75333L10.2467 0.193333C10.1133 0.06 9.94667 0 9.77333 0ZM7.37333 2.12667L0 9.5V12H2.5L9.87333 4.62667L7.37333 2.12667Z"
                                                        fill="#00B549"
                                                    />
                                                </svg>
                                                <span class="editFileMenuTitle">
                                                    {{ 'Change link name' | translate }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div ng-click="removeFile(file)" class="item" style="border-radius: 5px; padding: 1px 0px">
                                        <div class="content">
                                            <div ng-show="!file.url" class="header">
                                                <svg width="12" height="12" viewBox="0 0 12 12" fill="#00B549" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M11.25 1.8075L10.1925 0.75L6 4.9425L1.8075 0.75L0.75 1.8075L4.9425 6L0.75 10.1925L1.8075 11.25L6 7.0575L10.1925 11.25L11.25 10.1925L7.0575 6L11.25 1.8075Z"
                                                        fill="#00B549"
                                                    />
                                                </svg>
                                                <span class="editFileMenuTitle">{{ 'Delete file' | translate }}</span>
                                            </div>
                                            <div ng-show="file.url" class="header">
                                                <svg width="12" height="12" viewBox="0 0 12 12" fill="#00B549" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M11.25 1.8075L10.1925 0.75L6 4.9425L1.8075 0.75L0.75 1.8075L4.9425 6L0.75 10.1925L1.8075 11.25L6 7.0575L10.1925 11.25L11.25 10.1925L7.0575 6L11.25 1.8075Z"
                                                        fill="#00B549"
                                                    />
                                                </svg>
                                                <span class="editFileMenuTitle">{{ 'Delete link' | translate }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </i>
                    </div>
                    <div class="text-center" style="display: flex; justify-content: flex-start; align-items: center">
                        <img style="margin-right: 5px" src="images/sprite/clip.svg?b2" alt="" />
                        <a class="text-center" style="color: #c0c0c0" href="" uib-dropdown-toggle>
                            {{ 'Attach file and link' | translate }}
                        </a>
                    </div>
                    <ul class="dropdown-menu" uib-dropdown-menu role="menu" aria-labelledby="single-button">
                        <label class="dropdown-menu__item" style="margin: 0" role="menuitem" for="file">
                            <img style="cursor: pointer; padding-left: 9px; vertical-align: middle" src="images/sprite/clip.svg?b2" alt="" />
                            <span class="dropdown-menu__title" style="margin-left: 2px">
                                {{ 'Attach file' | translate }}
                            </span>
                        </label>
                        <li class="divider"></li>
                        <label ng-click="showAddLinkFunc()" class="dropdown-menu__item" style="margin: 0" role="menuitem">
                            <img style="height: 13px; width: 13px; margin-left: 9px" src="images/sprite/link.svg" alt="" />
                            <span class="dropdown-menu__title" style="margin-left: 2px">
                                {{ 'Attach link' | translate }}
                            </span>
                        </label>
                    </ul>
                    <div ng-show="showAddLink" class="col-lg-12 addLinkFormForLink" style="margin-top: 10px">
                        <div style="margin-bottom: 10px">
                            <form name="addLinkForm">
                                <label for="nameLink">
                                    {{ 'Title' | translate }}
                                    <span class="form-title-star">*</span>
                                </label>
                                <br />
                                <input
                                    id="nameLink"
                                    class="form-control"
                                    ng-class="{ errorInput: addLinkErrorShow && addLinkToClient.name.length == 0 }"
                                    ng-model="addLinkToClient.name"
                                    name="name"
                                    type="text"
                                />
                                <br />
                                <label style="margin-top: 5px" for="urlLink">
                                    URL
                                    <span class="form-title-star">*</span>
                                </label>
                                <br />
                                <input
                                    id="urlLink"
                                    class="form-control"
                                    ng-class="{
                                        errorInput: (addLinkErrorShow && addLinkToClient.url.length == 0) || addLinkForm.url.$error.url
                                    }"
                                    ng-model="addLinkToClient.url"
                                    name="url"
                                    type="text"
                                />
                            </form>
                        </div>
                        <div style="margin-top: 10px; display: flex; justify-content: space-between; width: 100%">
                            <button ng-click="closeAddLinkFunc()" class="btn_empty btn_default btn_thin" style="min-width: 100px">
                                {{ 'cancel' | translate }}
                            </button>
                            <button ng-click="addLinkInClient()" class="btn_success btn_default btn_thin" style="min-width: 100px">
                                {{ 'add' | translate }}
                            </button>
                        </div>
                    </div>
                </div>
                <div ng-show="$root.me.recrutRole != 'researcher'" class="flex-row flex-block flex-column contacts">
                    <div class="form-title-main">{{ 'Contacts' | translate }}</div>
                    <div class="contact-items">
                        <div class="contact-item" ng-repeat="contact in client.contactClientListActive">
                            <div class="flex-column">
                                <a
                                    ng-if="$root.useAmericanNameStyle"
                                    href="#/contacts/{{ contact.localId }}"
                                    bo-text="contact.firstName +' '+contact.lastName"
                                ></a>
                                <a
                                    ng-if="!$root.useAmericanNameStyle"
                                    href="#/contacts/{{ contact.localId }}"
                                    bo-text="contact.lastName +' '+contact.firstName"
                                ></a>
                                <span ng-if="contact.position" class="contact-item__position" title="{{ contact.position }}">
                                    {{ contact.position }}
                                </span>
                            </div>
                            <div class="flex-column" ng-repeat="contact in contact.contacts" ng-switch="contact.type">
                                <span class="ellipsis" ng-switch-when="mphone">
                                    <i class="fa fa-phone" aria-hidden="true"></i>
                                    {{ contact.value }}
                                </span>
                                <span class="ellipsis" ng-switch-when="skype">
                                    <i class="fa fa-skype" aria-hidden="true"></i>
                                    <span
                                        ng-click="$root.openSkype(contact.value)"
                                        class="contact-item__text ellipsis link-text-style"
                                        style="margin-left: 5px; color: #47ab43"
                                    >
                                        {{ contact.value | limitToEllipse : 45 }}
                                    </span>
                                </span>
                                <span class="ellipsis" ng-switch-when="email">
                                    <i class="fa fa-envelope" aria-hidden="true"></i>
                                    {{ contact.value }}
                                </span>
                            </div>
                        </div>
                        <div ng-show="!showNotActive && client.contactClientListNotActive.length > 0" class="contact-item__switcher">
                            <span ng-click="showNotActive = true">{{ 'Show those who not work' | translate }}</span>
                            <i ng-click="showNotActive = true" class="fa fa-chevron-down" aria-hidden="true"></i>
                        </div>
                        <div ng-show="showNotActive" class="contact-item__switcher">
                            <span ng-click="showNotActive = false">{{ 'Hide those who not work' | translate }}</span>
                            <i ng-click="showNotActive = false" class="fa fa-chevron-up" aria-hidden="true"></i>
                        </div>
                        <div ng-show="showNotActive" class="contact-item" ng-repeat="contact in client.contactClientListNotActive">
                            <div class="flex-column">
                                <a href="#/contacts/{{ contact.localId }}" bo-text="contact.lastName + ' '+contact.firstName"></a>
                                <span ng-if="contact.position" class="contact-item__position" title="{{ contact.position }}">
                                    {{ contact.position }}
                                </span>
                            </div>
                            <div class="flex-column" ng-repeat="contact in contact.contacts" ng-switch="contact.type">
                                <span class="ellipsis" ng-switch-when="mphone">
                                    <i class="fa fa-phone" aria-hidden="true"></i>
                                    {{ contact.value }}
                                </span>
                                <span class="ellipsis" ng-switch-when="skype">
                                    <i class="fa fa-skype" aria-hidden="true"></i>
                                    <span
                                        ng-click="$root.openSkype(contact.value)"
                                        class="contact-item__text ellipsis link-text-style"
                                        style="margin-left: 5px; color: #47ab43"
                                    >
                                        {{ contact.value | limitToEllipse : 45 }}
                                    </span>
                                </span>
                                <span class="ellipsis" ng-switch-when="email">
                                    <i class="fa fa-envelope" aria-hidden="true"></i>
                                    {{ contact.value }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div
                        ng-if="$root.me.recrutRole !== 'client' && $root.me.recrutRole !== 'researcher' && $root.me.recrutRole !== 'freelancer'"
                        class="flex-justify-center"
                    >
                        <button ng-click="toAddContact(client.localId)" class="btn_default btn_thin btn_success">
                            {{ 'add_contact' | translate }}
                        </button>
                    </div>
                </div>
            </div>

            <div class="block-client__right-block">
                <div class="flex-row flex-block flex-space-between navigation not-for-print">
                    <ul>
                        <li
                            ng-show="($root.me.recrutRole != 'researcher' && $root.me.recrutRole != 'client') || client.creator.fullName === $root.me.fullName"
                            class="hidden_laptop"
                        >
                            <a href="#/client/edit/{{ client.localId }}" title="{{ 'edit_profile' | translate }}">
                                <img src="../images/sprite/edit.svg" alt="{{ 'edit_profile' | translate }}" />
                            </a>
                        </li>
                        <li ng-show="$root.me.recrutRole != 'researcher'">
                            <a ng-click="showCandidateSentEmail()" title="{{ 'Send email' | translate }}">
                                <img
                                    class="mail"
                                    style="height: 30px; width: 32px; top: 0"
                                    src="../images/sprite/mass-mailing.svg"
                                    alt="{{ 'Send email' | translate }}"
                                />
                            </a>
                        </li>
                    </ul>
                    <a ng-click="showModalAddCommentToClient()" class="pull-right" href="" title="{{ 'add_comment' | translate }}">
                        <img src="../images/sprite/addMessage.svg" alt="{{ 'add_comment' | translate }}" />
                    </a>
                </div>
                <div ng-show="client.region || client.industry || client.creator" class="general-info flex-row flex-block flex-column">
                    <div class="general-info__title form-title-main">{{ 'General information' | translate }}</div>
                    <div class="general-info__items">
                        <div ng-show="client.region" class="general-info__item">
                            <div class="second-title">{{ 'location' | translate }}</div>
                            <div class="item__value-wrapper">
                                <img class="item__icon" src="images/sprite/location-grey.svg" alt="" />
                                <div class="item__value">{{ client.region.googlePlaceId | translateLocation }}</div>
                            </div>
                        </div>
                        <div ng-show="client.industry" class="general-info__item">
                            <div class="second-title">{{ 'industry' | translate }}</div>
                            <div class="item__value-wrapper">
                                <img class="item__icon" src="images/sprite/flag.svg" alt="" />
                                <div class="item__value">{{ 'industries_assoc.' + client.industry | translate }}</div>
                            </div>
                        </div>
                        <div ng-show="$root.me.recrutRole != 'researcher'" class="general-info__item">
                            <div class="second-title">{{ 'added_by' | translate }}</div>
                            <div class="item__value-wrapper">
                                <img class="item__icon" src="images/sprite/added.svg" alt="" />
                                <div class="item__value">
                                    {{ (client.dc | dateFormatSimple).trim() }}
                                    <a href="#/users/{{ client.creator.userId }}">
                                        <span ng-if="$root.useAmericanNameStyle">{{ client.creator.fullNameEn }}</span>
                                        <span ng-if="!$root.useAmericanNameStyle">{{ client.creator.fullName }}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div ng-if="customFieldsIsExists" class="custom-fields flex-row flex-block flex-column">
                    <div class="form-title-main">{{ 'Custom fields' | translate }}</div>
                    <div ng-show="fieldValue.fieldValue != undefined" class="custom-fields__item" ng-repeat="fieldValue in client.customFields">
                        <div ng-show="fieldValue.state == 'A'" class="second-title">{{ fieldValue.title }}:</div>
                        <span
                            ng-show="fieldValue.type == 'string' && fieldValue.state == 'A'"
                            class="ellipsis"
                            style="max-width: 100%; overflow-wrap: break-word"
                            dd-text-collapse
                            dd-text-collapse-max-length="330"
                            dd-text-collapse-text="{{ fieldValue.fieldValue.value }}"
                        ></span>
                        <span
                            ng-show="fieldValue.type == 'select' && fieldValue.state == 'A'"
                            class="ellipsis"
                            style="max-width: 100%; overflow-wrap: break-word"
                        >
                            {{ fieldValue.fieldValue.value }}
                        </span>
                        <span ng-show="fieldValue.type == 'date' && fieldValue.state == 'A'">
                            {{ fieldValue.fieldValue.dateTimeValue | dateFormat : false : true }}
                        </span>
                        <span ng-show="fieldValue.type == 'datetime' && fieldValue.state == 'A'">
                            {{ fieldValue.fieldValue.dateTimeValue | dateFormat2 : true }}
                        </span>
                    </div>
                </div>
                <div ng-show="client.descr" class="flex-row flex-block flex-column description">
                    <div class="form-title-main">{{ 'description' | translate }}</div>
                    <span id="descr" class="content-description" description-treatment description="client.descr"></span>
                </div>
                <div class="col-lg-12 taskForClient not-for-print no-padding-sm">
                    <div class="last-actions-header grey-header" style="display: flex; align-items: center; border-radius: 5px">
                        <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                            <span class="title-header">
                                {{ 'Tasks_v2' | translate }}
                            </span>
                            <button ng-click="showModalAddTaskToCandidate('md');" class="btn_default btn_thin btn_success" style="width: 150px" type="button">
                                {{ 'Add task' | translate }}
                            </button>
                        </div>
                    </div>
                    <div class="row event last-action" style="border-radius: 10px">
                        <div
                            id="{{ task.taskId }}"
                            ng-show="$root.clientTasks.length > 0"
                            ng-click="showModalEditTaskToCandidate(task)"
                            class="task_wrapp"
                            ng-class="{ finishedTask: task.targetDate < todayDate && task.status != 'completed' }"
                            style="min-height: 60px; display: flex; align-items: center"
                            ng-repeat-start="task in $root.clientTasks"
                            title="{{ 'View more' | translate }}"
                        >
                            <div class="dateTasks pull-left col-lg-2 col-md-2 col-sm-2 col-xs-3">
                                <div ng-if="$root.currentLang == 'ru'" bo-html="task.targetDate | dateFormat9"></div>
                                <div ng-if="$root.currentLang == 'en'" bo-html="task.targetDate | dateFormat9"></div>
                                <div ng-if="$root.currentLang == 'ua'" bo-html="task.targetDate | dateFormat9"></div>
                                <div ng-if="$root.currentLang == 'pl'" bo-html="task.targetDate | dateFormat9"></div>
                            </div>
                            <div class="fullNameResponsibleOfTasks pull-left col-lg-3 col-md-2 col-sm-2 col-xs-4">
                                <responsible-person
                                    ng-if="!$root.useAmericanNameStyle"
                                    ng-repeat="responsible in task.responsiblesPerson"
                                    avatar-id="responsible.responsible.avatarId"
                                    full-name="responsible.responsible.fullName"
                                    user-id="responsible.responsible.userId"
                                ></responsible-person>
                                <responsible-person
                                    ng-if="$root.useAmericanNameStyle"
                                    ng-repeat="responsible in task.responsiblesPerson"
                                    avatar-id="responsible.responsible.avatarId"
                                    full-name="responsible.responsible.fullNameEn"
                                    user-id="responsible.responsible.userId"
                                ></responsible-person>
                            </div>
                            <div class="titleOfTasks pull-left col-lg-5 col-md-5 col-sm-5 col-xs-5">
                                <span ng-show="task.type == 'Meeting'" class="pull-left task">
                                    {{ 'Meeting' | translate }}
                                </span>
                                <span ng-show="task.type == 'Call'" class="pull-left task">
                                    {{ 'Call' | translate }}
                                </span>
                                <span ng-show="task.type == 'Task'" class="pull-left task">
                                    {{ 'Task_v2' | translate }}
                                </span>
                                <span class="pull-left" style="word-break: break-all">"{{ task.title }}"</span>
                                <div class="clearfix"></div>
                                <span class="taskText">{{ task.text | limitTo : 150 }}{{ task.text.length > 150 ? '...' : '' }}</span>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-4 statuses hidden-xs" style="width: 180px; padding: 0; margin-bottom: 0px !important">
                                <custom-select-new-status
                                    id="clientTaskStatus"
                                    ng-click="$event.stopPropagation()"
                                    style="width: 180px; min-width: 180px; height: 32px"
                                    hide-placeholder-from-options="true"
                                    custom-status-styles="true"
                                    method="$root.changeTaskState"
                                    model="task.status"
                                    options="task.editableTaskOptions"
                                ></custom-select-new-status>
                            </div>
                        </div>
                        <div class="table-space" ng-repeat-end="" ng-hide="$last"></div>
                    </div>
                </div>
                <ng-include src="'partials/history/client/actions_table.html'"></ng-include>
            </div>
        </div>
    </div>
</div>
