<div class="col-lg-2" title="{{ history.dm | dateFormat : true }}">
    <div class="time-comment">
        {{ (history.dm | dateFormatSimple).trim() }}
    </div>
    <a class="text-left user" href="#/users/{{ history.person.userId }}">
        <span ng-if="!history.person.fullName && !history.person.contacts.length" class="remove-user-in-history">User removed</span>
        <span ng-if="!$root.useAmericanNameStyle">
            <span once-text="history.person.lastName"></span>
            <span once-text="history.person.firstName"></span>
        </span>
        <span ng-if="$root.useAmericanNameStyle">
            <span once-text="history.person.firstName"></span>
            <span once-text="history.person.lastName"></span>
        </span>
    </a>
</div>
<div ng-if="$root.currentLang != 'en'" class="col-lg-10">
    {{ 'user_notice' | translate }}
    <a href="#/users/{{ history.person.userId }}">
        <span ng-if="$root.useAmericanNameStyle">{{ history.person.fullNameEn }}</span>
        <span ng-if="!$root.useAmericanNameStyle">{{ history.person.fullName }}</span>
    </a>
    {{ 'canceled automatic sending of a test for a stage' | translate }}
    <span ng-if="history.generalStates.length > 0" class="stage {{ history.generalStates[0] }}" style="display: inline-flex">
        {{ history.generalStates[0] | translate }}
    </span>
    <span ng-if="history.customInterviewStates.length > 0" class="stage {{ history.customInterviewStates[0].stateNew }}">
        {{ history.customInterviewStates[0].value }}
    </span>
    <span ng-if="history.vacancy">
        {{ 'history_info.on_vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            ({{ history.vacancy.clientId.name }})
        </a>
    </span>
    <span ng-if="!history.vacancy">
        {{ 'for all vacancies' | translate }}
    </span>
</div>
<div ng-if="$root.currentLang == 'en'" class="col-lg-10">
    <a href="#/users/{{ history.person.userId }}">
        <span ng-if="$root.useAmericanNameStyle">{{ history.person.fullNameEn }}</span>
        <span ng-if="!$root.useAmericanNameStyle">{{ history.person.fullName }}</span>
    </a>
    canceled the automatic test sending at the
    <span ng-if="history.generalStates.length > 0" class="stage {{ history.generalStates[0] }}" style="display: inline-flex">
        {{ history.generalStates[0] | translate }}
    </span>
    <span ng-if="history.customInterviewStates.length > 0" class="stage {{ history.customInterviewStates[0].stateNew }}">
        {{ history.customInterviewStates[0].value }}
    </span>
    stage for
    <span ng-if="history.vacancy">
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            ({{ history.vacancy.clientId.name }})
        </a>
        vacancy
    </span>
    <span ng-if="!history.vacancy">
        {{ 'for all vacancies' | translate }}
    </span>
</div>
