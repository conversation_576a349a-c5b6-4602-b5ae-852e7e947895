<div class="col-lg-10">
    <span ng-if="$root.currentLang === 'ua'">
        {{ $root.getVerbByGender(history.person.sex, { male: 'надіслав', female: 'надіслала' }) }} SMS
        {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} кандидату
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span>{{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}</span>
        </a>
        при додаванні на етап
        <span class="stage {{ history.stateNewName ? history.stateNewName : history.stateNew }}">
            {{ history.stateNewName ? (history.stateNewName | translate) : (history.stateNew | translate) }}
        </span>
    </span>

    <span ng-if="$root.currentLang === 'en'">
        sent SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} to candidate
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span>{{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}</span>
        </a>
        when adding to the
        <span class="stage {{ history.stateNewName ? history.stateNewName : history.stateNew }}">
            {{ history.stateNewName ? (history.stateNewName | translate) : (history.stateNew | translate) }}
        </span>
        stage
    </span>

    <span ng-if="$root.currentLang === 'pl'">
        {{ $root.getVerbByGender(history.person.sex, { male: 'wysłał', female: 'wysłała' }) }} SMS'a
        {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} do kandydata
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span>{{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}</span>
        </a>
        podczas dodawania do etapu
        <span class="stage {{ history.stateNewName ? history.stateNewName : history.stateNew }}">
            {{ history.stateNewName ? (history.stateNewName | translate) : (history.stateNew | translate) }}
        </span>
    </span>

    <span ng-if="$root.currentLang === 'ru'">
        {{ $root.getVerbByGender(history.person.sex, { male: 'отправил', female: 'отправила' }) }} SMS
        {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} кандидату
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span>{{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}</span>
        </a>
        при добавлении на етап
        <span class="stage {{ history.stateNewName ? history.stateNewName : history.stateNew }}">
            {{ history.stateNewName ? (history.stateNewName | translate) : (history.stateNew | translate) }}
        </span>
    </span>
</div>
