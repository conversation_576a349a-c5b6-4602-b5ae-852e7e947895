<div class="col-lg-10 margin-top interview_add">
    <span ng-if="history.candidatesToUpdate.length">
        <span ng-if="history.candidatesToUpdate.length == 1">
            {{ 'history_info.the candidate' | translate }}
            <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ history.candidatesToUpdate[0].localId }}">
                {{ history.candidatesToUpdate[0].fullNameEn }}
            </a>
            <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ history.candidatesToUpdate[0].localId }}">
                {{ history.candidatesToUpdate[0].fullName }}
            </a>
            {{ 'was updated and' | translate }}
            {{ 'history_info.added_in' | translate }}
            <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
                {{ history.stateNew | translate }}
            </span>
            <span ng-if="history.candidates.length">{{ 'and2' | translate }}</span>
        </span>

        <span ng-if="history.candidatesToUpdate.length == 2">
            {{ 'history_info.candidates' | translate }}
            <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ history.candidatesToUpdate[0].localId }}">
                {{ history.candidatesToUpdate[0].fullNameEn }}
            </a>
            <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ history.candidatesToUpdate[0].localId }}">
                {{ history.candidates[0].fullName }}
            </a>
            {{ 'and' | translate }}
            <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ history.candidatesToUpdate[1].localId }}">
                {{ history.candidatesToUpdate[1].fullNameEn }}
            </a>
            <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ history.candidatesToUpdate[1].localId }}">
                {{ history.candidatesToUpdate[1].fullName }}
            </a>
            {{ 'were updated and' | translate }}
            {{ 'history_info.addeds' | translate }}
            <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
                {{ history.stateNew | translate }}
            </span>
            <span ng-if="history.candidates.length">{{ 'and2' | translate }}</span>
        </span>

        <span ng-if="history.candidatesToUpdate.length > 2">
            {{ 'Some of the candidates who responded were already in your database, so we updated them' | translate }}
            <span ng-click="$root.openMenuWithCandidates(history, true)">
                <span class="blueLink">({{ history.candidatesToUpdate.length }})</span>
            </span>
            {{ 'profiles and add them to the stage' | translate }}
            <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
                {{ history.stateNew | translate }}
            </span>
            <span ng-if="history.candidates.length">{{ 'and2' | translate }}</span>
        </span>
        <span ng-if="history.candidatesToUpdate.length > 2" class="manyCandidates">
            <div ng-if="history.updateCandidate">
                <!-- prettier-ignore -->
                <span class="menuCandidate" ng-repeat="candidate in history.candidatesToUpdate">
                    <a ng-if="$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                        {{ candidate.fullNameEn }}<span class="comma">{{ $last ? '' : ', ' }}</span>
                    </a>
                    <a ng-if="!$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                        {{ candidate.fullName }}<span class="comma">{{ $last ? '' : ', ' }}</span>
                    </a>
                </span>
            </div>
        </span>
    </span>
    <span ng-if="!history.candidates && history.candidate">
        {{ 'history_info.the_candidate' | translate }}
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span ng-if="$root.useAmericanNameStyle">{{ history.candidate.fullNameEn }}</span>
            <span ng-if="!$root.useAmericanNameStyle">{{ history.candidate.fullName }}</span>
        </a>
        {{ 'history_info.added_in' | translate }}
        <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
    </span>

    <span ng-if="history.candidates.length == 1">
        <span ng-if="history.candidatesToUpdate.length">{{ 'history_info.the_candidate_lowercase' | translate }}</span>
        <span ng-if="!history.candidatesToUpdate.length">{{ 'history_info.the candidate' | translate }}</span>

        <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ history.candidates[0].localId }}">
            {{ history.candidates[0].fullNameEn }}
        </a>
        <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ history.candidates[0].localId }}">
            {{ history.candidates[0].fullName }}
        </a>
        {{ 'history_info.added_in' | translate }}
        <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
    </span>

    <span ng-if="history.candidates.length == 2">
        <span ng-if="history.candidatesToUpdate.length">{{ 'history_info.candidates1' | translate }}</span>
        <span ng-if="!history.candidatesToUpdate.length">{{ 'history_info.candidates' | translate }}</span>

        <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ history.candidates[0].localId }}">
            {{ history.candidates[0].fullNameEn }}
        </a>
        <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ history.candidates[0].localId }}">
            {{ history.candidates[0].fullName }}
        </a>
        {{ 'and' | translate }}
        <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ history.candidates[1].localId }}">
            {{ history.candidates[1].fullNameEn }}
        </a>
        <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ history.candidates[1].localId }}">
            {{ history.candidates[1].fullName }}
        </a>
        {{ 'history_info.addeds' | translate }}
        <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
    </span>

    <span ng-if="history.candidates.length > 2">
        <span ng-if="history.candidatesToUpdate.length">{{ 'history_info.candidates1' | translate }}</span>
        <span ng-if="!history.candidatesToUpdate.length">{{ 'history_info.candidates' | translate }}</span>

        <span ng-click="$root.openMenuWithCandidates(history)">
            <span class="blueLink">({{ history.countCandidate }})</span>
        </span>
        {{ 'history_info.addeds' | translate }}
        <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
    </span>
    <span ng-if="history.candidates.length > 2" class="manyCandidates">
        <div ng-if="history.showAllCandidates">
            <!-- prettier-ignore -->
            <span class="menuCandidate" ng-repeat="candidate in history.candidates">
                <a ng-if="$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                    {{ candidate.fullNameEn }}<span class="comma">{{ $last ? '' : ', ' }}</span>
                </a>
                <a ng-if="!$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                    {{ candidate.fullName }}<span class="comma">{{ $last ? '' : ', ' }}</span>
                </a>
            </span>
        </div>
    </span>

    {{ 'history_info.on_vacancy' | translate }}
    <a href="#/vacancies/{{ history.vacancy.localId }}">
        <span>{{ history.vacancy.position + ', ' | limitToEllipse : 300 }}</span>
    </a>
    <a href="#/clients/{{ history.vacancy.clientId.localId }}">
        <span>({{ history.vacancy.clientId.name }})</span>
    </a>
    <!-- prettier-ignore -->
    <span>
         {{ 'was_add_from_rabotaua' | translate }}<span ng-if="history.countOfMerged">.</span>
    </span>
    <span ng-if="history.countOfMerged > 0">
        <span>
            {{
                'We noticed duplicate candidates in the reviews for this vacancy and did not create separate profiles for them, but combined them. Number of such candidates'
                    | translate
            }}
        </span>
        <span>({{ history.countOfMerged }}).</span>
        <span>
            {{
                'Therefore, there are a lot of vacancies for the vacancies themselves, and we can challenge them. In this case, it is necessary to take up duplicates at the same time for each vacancy.'
                    | translate
            }}
        </span>
    </span>
</div>
