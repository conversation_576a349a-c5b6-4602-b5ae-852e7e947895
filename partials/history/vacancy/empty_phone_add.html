<div class="col-lg-10">
    <span ng-if="$root.currentLang === 'ua'">
        SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} не відправлено кандидату
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span>{{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}</span>
        </a>
        при додаванні на етап
        <span class="stage {{ history.stateNewName ? history.stateNewName : history.stateNew }}">
            {{ history.stateNewName ? (history.stateNewName | translate) : (history.stateNew | translate) }}
        </span>
        через відсутність у нього номера телефону
    </span>

    <span ng-if="$root.currentLang === 'en'">
        SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} was not sent to candidate
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span>{{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}</span>
        </a>
        when adding to the
        <span class="stage {{ history.stateNewName ? history.stateNewName : history.stateNew }}">
            {{ history.stateNewName ? (history.stateNewName | translate) : (history.stateNew | translate) }}
        </span>
        stage due to the absence of a phone number
    </span>

    <span ng-if="$root.currentLang === 'pl'">
        SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} nie został wysłany do kandydata
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span>{{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}</span>
        </a>
        podczas dodawania do etapu
        <span class="stage {{ history.stateNewName ? history.stateNewName : history.stateNew }}">
            {{ history.stateNewName ? (history.stateNewName | translate) : (history.stateNew | translate) }}
        </span>
        z powodu braku numeru telefonu
    </span>

    <span ng-if="$root.currentLang === 'ru'">
        SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} не отправлено кандидату
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span>{{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}</span>
        </a>
        при добавлении на етап
        <span class="stage {{ history.stateNewName ? history.stateNewName : history.stateNew }}">
            {{ history.stateNewName ? (history.stateNewName | translate) : (history.stateNew | translate) }}
        </span>
        из-за отсутствия у него номера телефона
    </span>
</div>
