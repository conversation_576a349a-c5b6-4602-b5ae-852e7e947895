<div class="comment-container col-lg-10" style="overflow-wrap: anywhere">
    <span ng-show="history.dateEdit" class="date" style="margin: 0" style="float: none">
        ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})
    </span>
    <span>{{ 'removed' | translate }}</span>
    <span class="comment">{{ 'history_info.coment' | translate }}</span>
    <span ng-show="!history.editCommentFlag"><span ng-bind-html="history.descr|textForNotice:false"></span></span>
    <a href="#/candidates/{{ history.candidate.localId }}">
        <span once-text="history.candidate.fullName"></span>
    </a>

    {{ 'history_info.in_vacancy' | translate }}
    <a href="#/vacancies/{{ history.vacancy.localId }}" once-text="history.vacancy.position |limitToEllipse:300"></a>
    :
    <a ng-if="clients.length > 1" href="#/clients/{{ history.client.localId }}">({{ history.client.name }})</a>
    <br />
    <span class="ui tiny header"></span>
    <div ng-if="history.descr" style="padding-right: 40px">
        <span class="text-in-block" ng-bind-html="history.descr|textForNotice:false"></span>
    </div>
</div>
