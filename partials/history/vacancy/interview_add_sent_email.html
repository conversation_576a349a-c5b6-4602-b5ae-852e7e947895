<div class="col-lg-10 margin-top">
    <span>
        <span ng-if="history.candidate">
            {{ 'history_info.candidate' | translate }}
            <a ng-click="routeOnCandidate($event, history.candidate.localId, 'history')" href="#/candidates/{{ history.candidate.localId }}">
                <span ng-if="$root.useAmericanNameStyle">{{ history.candidate.fullNameEn }}</span>
                <span ng-if="!$root.useAmericanNameStyle">{{ history.candidate.fullName }}</span>
            </a>
            {{ 'history_info.added_in' | translate }}
        </span>
        <span ng-if="history.candidates.length == 2">
            <a ng-href="#/candidates/{{ history.candidates[0].localId }}">
                {{ history.candidates[0].fullName }}
            </a>
            {{ 'and' | translate }}
            <a ng-if="$root.useAmericanNameStyle" ng-href="#/candidates/{{ history.candidates[1].localId }}">
                {{ history.candidates[1].fullNameEn }}
            </a>
            <a ng-if="!$root.useAmericanNameStyle" ng-href="#/candidates/{{ history.candidates[1].localId }}">
                {{ history.candidates[1].fullName }}
            </a>
            {{ 'history_info.added_in_2' | translate }}
        </span>
        <span ng-if="history.candidates.length > 2" class="manyCandidates">
            <span ng-click="vm.openMenuWithCandidates(history)">
                {{ 'history_info.User added' | translate }}
                <span class="blueLink">({{ history.candidates.length }})</span>
            </span>
            {{ 'history_info.candidates to' | translate }}
        </span>
        <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
        <span ng-show="history.stateNewName" class="stage {{ history.stateNew }}">
            <span ng-if="history.customStateNew">{{ history.stateNewName }}</span>
            <span ng-if="!history.customStateNew">{{ history.stateNewName | translate }}</span>
        </span>
        {{ 'history_info.on_vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position | limitToEllipse : 300 }}.</span>
        </a>
        {{ 'history_info.full in combination with the email sent' | translate }}
        <a ng-if="clients.length > 1" href="#/clients/{{ history.client.localId }}">({{ history.client.name }})</a>
    </span>
    <br />
    <span ng-show="history.targetDate">{{ 'interview_times' | translate }}: {{ history.targetDate | dateFormat : true }}.&nbsp;</span>

    <span ng-show="history.descr">
        <br />
        <span ng-if="!history.dateEdit" class="date" style="margin: 0" style="float: none">
            ({{ 'history_info.added' | translate }} {{ history.dc | dateFormatSimpleAction : true | translate }})
        </span>
        <span ng-if="history.dateEdit" class="date" style="margin: 0" style="float: none">
            ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})
        </span>
        {{ 'comment' | translate }}:
        <br />
        <span ng-bind-html="history.descr|textForNotice:false"></span>
    </span>

    <span ng-if="history.candidates.length > 2" class="manyCandidates">
        <div ng-show="history.showAllCandidates">
            <span class="menuCandidate" ng-repeat="candidate in history.candidates">
                <a ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                    {{ candidate.fullName }}
                </a>
            </span>
        </div>
    </span>
</div>
