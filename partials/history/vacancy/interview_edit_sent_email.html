<div class="col-lg-10">
    <!-- prettier-ignore -->
    <span>
        {{ 'Candidate' | translate }}
        <a ng-href="#/candidates/{{ history.candidate.localId }}">
            <span ng-if="$root.useAmericanNameStyle">{{ history.candidate.fullNameEn }}</span>
            <span ng-if="!$root.useAmericanNameStyle">{{ history.candidate.fullName }}</span>
        </a>
        {{ 'history_info.was moved from' | translate }}
        <span ng-if="history.stateOldName" class="stage  passed {{ history.stateOld }}">
            <span ng-if="history.customStateOld">{{ history.stateOldName }}</span>
            <span ng-if="!history.customStateOld">{{ history.stateOldName | translate }}</span>
        </span>
        <span ng-if="!history.stateOldName" class="stage  passed {{ history.stateOld }}">
            {{ history.stateOld | translate }}
        </span>
        {{ 'history_info.stage to' | translate }}
        <span ng-if="history.stateNewName" class="stage {{ history.stateNew }}">
            <span ng-if="history.customStateNew">{{ history.stateNewName }}</span>
            <span ng-if="!history.customStateNew">{{ history.stateNewName | translate }}</span>
        </span>
        <span ng-if="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>.
        <span ng-style="$root.currentLang == 'ru' ? { 'margin-left': '-2px' } : { 'margin-left': '0px' }">
            {{ 'history_info.stage in combination with the email sent' | translate }}
        </span>
    </span>

    <span ng-show="history.descr">
        <br />
        <span ng-if="!history.dateEdit" class="date" style="margin: 0" style="float: none">
            ({{ 'history_info.added' | translate }} {{ history.dc | dateFormatSimpleAction : true | translate }})
        </span>
        <span ng-if="history.dateEdit" class="date" style="margin: 0" style="float: none">
            ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})
        </span>
        {{ 'comment' | translate }}:
        <br />
    </span>
    <div class="comment-container">
        <span ng-bind-html="history.descr|textForNotice:false"></span>
    </div>
</div>
