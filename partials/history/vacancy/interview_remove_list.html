<div class="col-lg-10">
    <span>{{ 'history_info.candidates' | translate }}</span>

    <div style="display: inline" ng-repeat="candidate in history.candidates track by $index">
        <div style="display: inline-flex">
            <a href="#/candidates/{{ candidate.localId }}">
                <span ng-if="$root.useAmericanNameStyle" once-text="candidate.fullNameEn"></span>
                <span ng-if="!$root.useAmericanNameStyle" once-text="candidate.fullName"></span>
            </a>
            <span ng-show="history.candidates.length != $index + 1">,</span>
        </div>
    </div>
    <!-- prettier-ignore -->
    <span style="color: #ff00ff">
        <span style="padding: 4px; border-radius: 5px; color: #202021; background-color: #e2e2e2">{{ 'candidate removed' | translate }}</span>
    </span>
    <span>{{ 'history_info.from vacancy' | translate }}</span>
    <a href="#/vacancies/{{ history.vacancy.localId }}" once-text="history.vacancy.position |limitToEllipse:300"></a>
    <span class="ui tiny header"></span>
    <span bo-show="history.targetDate">
        <span bo-text="'interview_times'|translate">:</span>
        <span bo-text="history.targetDate|dateFormat:true"></span>
    </span>
    <span ng-show="history.descr">
        <br />
        <span ng-show="history.dateEdit" class="pull-left date">
            ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})&nbsp;
        </span>
        {{ 'history_info.message' | translate }}:
        <br />
        <span ng-bind-html="history.descr|textForNotice:false"></span>
    </span>
</div>
