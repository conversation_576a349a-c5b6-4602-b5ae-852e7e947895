<div class="col-lg-10">
    <a ng-show="history.targetPerson.fullName" href="#/users/{{ history.targetPerson.userId }}">
        {{ $root.useAmericanNameStyle ? history.targetPerson.fullNameEn : history.targetPerson.fullName }}
    </a>
    <span ng-show="!history.targetPerson.fullName" style="color: #d67354">{{ 'Candidate Removed' | translate }}</span>

    <span ng-if="history.candidates.length === 1">{{ 'history_info.candidates_set_responsible_0' | translate }}</span>
    <span ng-if="history.candidates.length > 1">{{ 'history_info.candidates_set_responsible_1' | translate }}</span>
    <div ng-show="history.candidates.length <= 2" style="margin-right: 5px; display: inline-block" ng-repeat="candidate in history.candidates">
        <a ng-show="candidate.status !== 'deleted'" href="#/candidates/{{ candidate.localId }}">
            {{ $root.useAmericanNameStyle ? candidate.fullNameEn : candidate.fullName }}
        </a>
        <span ng-show="candidate.status === 'deleted'" style="color: #d67354">{{ 'Candidate Removed' | translate }}</span>
        <span ng-hide="$last">{{ 'and' | translate }}</span>
    </div>
    <a ng-show="history.candidates.length > 2" ng-click="toggleCandidates(history)">({{ history.candidates.length }})</a>
    <div ng-show="history.candidates.length > 2">
        <span ng-show="history.candidatesToShow" ng-repeat="candidate in history.candidates">
            <div style="display: inline-flex">
                <a ng-show="candidate.status !== 'deleted'" href="#/candidates/{{ candidate.localId }}">
                    {{ $root.useAmericanNameStyle ? candidate.fullNameEn : candidate.fullName }}
                    <div ng-hide="$last">,</div>
                </a>
                <span ng-show="candidate.status === 'deleted'" style="color: #d67354">
                    {{ 'Candidate Removed' | translate }}
                    <div ng-hide="$last">,</div>
                </span>
            </div>
        </span>
    </div>

    <span ng-show="history.descr">
        <span ng-if="!history.dateEdit" class="date" style="margin: 0" style="float: none">
            ({{ 'history_info.added' | translate }} {{ history.dc | dateFormatSimpleAction : true | translate }})
        </span>
        <span ng-show="history.dateEdit" class="pull-left date">
            ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})&nbsp;
        </span>
        {{ 'history_info.message' | translate }}:
        <br />
        <span ng-bind-html="history.descr|textForNotice:false"></span>
    </span>
</div>
