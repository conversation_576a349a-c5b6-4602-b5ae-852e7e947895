<div class="col-lg-10 margin-top-1">
    <span ng-show="history.dateEdit" class="date" style="float: none">({{ 'edited' | translate }} {{ history.dateEdit | dateFormat4 : true }})</span>
    <br />
    {{ 'Task_v2' | translate }}
    "{{ history.task.title }}"
    {{ 'Edited' | translate }}
    <span ng-if="$root.currentLang === 'pl'">
        <span ng-show="history.client">
            w kliencie
            <a href="#/clients/{{ history.client.localId }}">{{ history.client.name }}</a>
        </span>
        <span ng-show="history.candidate">
            dotyczące kandydata
            <a href="#/candidates/{{ history.candidate.localId }}" once-text="history.candidate.fullName"></a>
        </span>
    </span>
</div>
