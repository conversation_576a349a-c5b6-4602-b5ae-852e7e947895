<div class="col-lg-10 margin-top interview_add">
    <span ng-if="!history.candidates && history.candidate">
        {{ 'history_info.the candidate' | translate }}
        <a
            ng-show="history.candidate.status !== 'deleted'"
            ng-click="$root.closeTaskModal();"
            class="action-footer-user"
            href="#/candidates/{{ history.candidate.localId }}"
        >
            {{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }},
        </a>
        <span ng-show="history.candidate.status === 'deleted'" style="color: #d67354">{{ 'Candidate Removed' | translate }}</span>
        {{ 'history_info.added_in' | translate }}
        <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
        <span ng-show="history.stateNewName" class="stage {{ history.stateNew }}">
            <span ng-if="history.customStateNew">{{ history.stateNewName }}</span>
            <span ng-if="!history.customStateNew">{{ history.stateNewName | translate }}</span>
        </span>
        {{ 'history_info.on_vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            ({{ history.vacancy.clientId.name }})
        </a>
    </span>
    <span ng-if="history.candidates.length == 1">
        {{ 'history_info.the candidate' | translate }}
        <a href="#/candidates/{{ history.candidates[0].localId }}">
            {{ $root.useAmericanNameStyle ? history.candidates[0].fullNameEn : history.candidates[0].fullName }}
            {{ !history.candidates[0].fullName ? history.candidates[0].firstName : '' }}
        </a>
        {{ 'history_info.added_in' | translate }}
        <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
        <span ng-show="history.stateNewName" class="stage {{ history.stateNew }}">
            <span ng-if="history.customStateNew">{{ history.stateNewName }}</span>
            <span ng-if="!history.customStateNew">{{ history.stateNewName | translate }}</span>
        </span>
        {{ 'history_info.on_vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            ({{ history.vacancy.clientId.name }})
        </a>
    </span>

    <span ng-if="history.candidates.length == 2">
        {{ 'history_info.candidates' | translate }}
        <a href="#/candidates/{{ history.candidates[0].localId }}">
            {{ $root.useAmericanNameStyle ? history.candidates[0].fullNameEn : history.candidates[0].fullName }}
            {{ !history.candidates[0].fullName ? history.candidates[0].firstName : '' }}
        </a>
        {{ 'and' | translate }}
        <a href="#/candidates/{{ history.candidates[1].localId }}">
            {{ $root.useAmericanNameStyle ? history.candidates[1].fullNameEn : history.candidates[1].fullName }}
            {{ !history.candidates[1].fullName ? history.candidates[1].firstName : '' }}
        </a>
        {{ 'history_info.addeds' | translate }}
        <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
        <span ng-show="history.stateNewName" class="stage {{ history.stateNew }}">
            <span ng-if="history.customStateNew">{{ history.stateNewName }}</span>
            <span ng-if="!history.customStateNew">{{ history.stateNewName | translate }}</span>
        </span>
        {{ 'history_info.on_vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            ({{ history.vacancy.clientId.name }})
        </a>
    </span>

    <span ng-if="history.candidates.length > 2">
        {{ 'history_info.candidates' | translate }}
        <!-- prettier-ignore -->
        <span ng-click="openMenuWithCandidates(history)">
            <span class="blueLink green-link">({{ history.candidates.length }})</span>
        </span>
        {{ 'history_info.addeds' | translate }}
        <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
        <span ng-show="history.stateNewName" class="stage {{ history.stateNew }}">
            <span ng-if="history.customStateNew">{{ history.stateNewName }}</span>
            <span ng-if="!history.customStateNew">{{ history.stateNewName | translate }}</span>
        </span>
        {{ 'history_info.on_vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            ({{ history.vacancy.clientId.name }})
        </a>
        <div ng-if="history.showAllCandidates">
            <!-- prettier-ignore -->
            <span class="menuCandidate" ng-repeat="candidate in history.candidates">
                <a ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                    {{ $root.useAmericanNameStyle ? candidate.fullNameEn : candidate.fullName }}
                    {{ !candidate.fullName ? candidate.firstName : '' }}
                    <span class="comma">{{ $last ? '' : ', ' }}</span>
                </a>
            </span>
        </div>
    </span>

    <div>
        <span ng-show="history.descr">
            <span ng-if="!history.dateEdit" class="date" style="margin: 0; float: none">
                ({{ 'history_info.added' | translate }} {{ history.dc | dateFormatSimpleAction : true | translate }})
            </span>
            <span ng-if="history.dateEdit" class="date" style="margin: 0; float: none">
                ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})
            </span>
            {{ 'comment' | translate }}:
            <br />
            <div style="padding-right: 40px">
                <span ng-bind-html="history.descr|textForNotice:false"></span>
            </div>
        </span>
    </div>
</div>
