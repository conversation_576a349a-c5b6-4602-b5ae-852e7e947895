<div class="col-lg-10">
    <span ng-show="$root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter'">
        <span
            translate="{{
                'The test testname has been passed admin'
                    | translate
                        : {
                              testName: history.descr,
                              candidateName: $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName,
                              href: '#/candidates/' + history.candidate.localId,
                              testHref: '#/candidate/test/' + history.testId
                          }
            }}"
        ></span>
    </span>
    <span ng-show="$root.me.recrutRole == 'freelancer' || $root.me.recrutRole == 'researcher' || $root.me.recrutRole == 'client'">
        <span
            translate="{{
                'The test testname has been passed'
                    | translate
                        : {
                              testName: history.descr,
                              candidateName: $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName,
                              href: '#/candidates/' + history.candidate.localId
                          }
            }}"
        ></span>
    </span>
</div>
