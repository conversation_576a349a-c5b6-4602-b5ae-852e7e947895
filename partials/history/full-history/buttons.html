<div class="action-buttons-wrapper">
    <div
        ng-show="($root.me.fullName === history.person.fullName || $root.me.recrutRole === 'admin') && history.type.includes('message')"
        ng-click="showDeleteComment(history) || vm.showDeleteComment(history)"
        class="remove-action"
        uib-tooltip="{{ 'Delete comment' | translate }}"
        tooltip-append-to-body="true"
        tooltip-placement="bottom-right"
        tooltip-class="tooltip-info-black"
        tooltip-popup-delay="700"
    ></div>
    <div
        ng-show="$root.me.fullName == history.person.fullName"
        ng-click="changeCommentFlag(history) || vm.changeCommentFlag(history); editComment = history.descr"
        class="edit-action"
        uib-tooltip="{{ 'comment' | translate }}"
        tooltip-append-to-body="true"
        tooltip-placement="bottom-right"
        tooltip-class="tooltip-info-black"
        tooltip-popup-delay="700"
    ></div>
    <div
        ng-show="
            $root.me.recrutRole !== 'client' &&
            $root.navName === 'candidates' &&
            (history.type === 'candidate_message' || history.type === 'candidate_message_for_hm')
        "
        ng-click="!history.attached ? $root.pinComment(history, true) : $root.pinComment(history, false)"
        class="pin-action"
        uib-tooltip="{{ !history.attached ? ('Pin comment' | translate) : ('Unpin comment' | translate) }}"
        tooltip-append-to-body="true"
        tooltip-placement="bottom-right"
        tooltip-class="tooltip-info-black"
        tooltip-popup-delay="700"
    ></div>
    <div
        ng-show="
            history.type === 'candidate_message_for_hm' &&
            (!$root.me.orgParams.hasOwnProperty('hideCommentFromHm') || $root.me.orgParams.hideCommentFromHm === 'Y') &&
            $root.me.recrutRole !== 'client' &&
            history.vacancy.position &&
            $root.navName === 'candidates'
        "
        class="eye-action"
        uib-tooltip="{{ 'The comment is visible to the HM on the vacancy' | translate }}&nbsp;&quot;{{ history.vacancy.position }}&quot;"
        tooltip-append-to-body="true"
        tooltip-placement="bottom-right"
        tooltip-class="tooltip-info-black"
        tooltip-popup-delay="700"
    ></div>
</div>

<div ng-if="$root.navName === 'candidates' && history.attached" class="pin-message">
    <img src="/images/redesign/svg-icons/pin.svg" alt="" />
</div>
