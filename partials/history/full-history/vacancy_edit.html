<div class="col-lg-10">
    <span>
        {{ 'history_info.edited_vacancy_short' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            ({{ history.vacancy.clientId.name }})
        </a>
    </span>
    <span>{{ '_edited' | translate }}</span>
    <br />
</div>
