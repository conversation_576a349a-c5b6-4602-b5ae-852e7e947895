<div class="col-lg-10">
    <a ng-if="$root.useAmericanNameStyle" href="#/users/{{ history.targetPerson.personId }}">
        {{ history.targetPerson.fullNameEn }}
    </a>
    <a ng-if="!$root.useAmericanNameStyle" href="#/users/{{ history.targetPerson.personId }}">
        {{ history.targetPerson.fullName }}
    </a>
    <span ng-if="history.clients.length === 1">{{ 'history_info.clients_set_responsible_0' | translate }}</span>
    <span ng-if="history.clients.length > 1">{{ 'history_info.clients_set_responsible_1' | translate }}</span>
    <div ng-show="history.clients.length <= 2" style="margin-right: 5px; display: inline-block" ng-repeat="client in history.clients">
        <a href="#/clients/{{ client.localId }}">
            <span once-text="client.name"></span>
        </a>
        <span ng-hide="$last">{{ 'and' | translate }}</span>
    </div>
    <a ng-show="history.clients.length > 2" ng-click="toggleCandidates(history)">({{ history.clients.length }})</a>
    <div ng-show="history.clients.length > 2">
        <span ng-show="history.candidatesToShow" ng-repeat="client in history.clients">
            <a style="display: inline-flex" href="#/clients/{{ client.localId }}">
                <div once-text="client.name"></div>
                <div ng-hide="$last">,</div>
            </a>
        </span>
    </div>
    <span ng-show="history.dateEdit" class="date" style="margin: 0" style="float: none">
        <br />
        ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})
    </span>
    <br />
    <span ng-bind-html="history.descr|textForNotice:false"></span>
</div>
