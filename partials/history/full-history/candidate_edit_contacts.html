<div class="col-lg-10">
    {{ 'Candidate contacts' | translate }}
    <span ng-show="history.candidate">
        <a ng-if="!$root.useAmericanNameStyle" class="not-user" href="#/candidates/{{ history.candidate.localId }}" once-text="history.candidate.fullName"></a>
        <a ng-if="$root.useAmericanNameStyle" class="not-user" href="#/candidates/{{ history.candidate.localId }}" once-text="history.candidate.fullNameEn"></a>
    </span>
    {{ 'history_info.had been edited' | translate }}
</div>
