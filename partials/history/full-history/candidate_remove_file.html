<div class="col-lg-10">
    {{ 'history_info.removed_file' | translate }}
    <span style="color: #daa10f">{{ history.descr | fileNameCut : 0 : 50 }}</span>
    {{ 'history_info.in_candidate' | translate }}
    <a href="#/candidates/{{ history.candidate.localId }}">
        <span ng-if="$root.useAmericanNameStyle" once-text="history.candidate.fullNameEn"></span>
        <span ng-if="!$root.useAmericanNameStyle" once-text="history.candidate.fullName"></span>
    </a>
</div>
