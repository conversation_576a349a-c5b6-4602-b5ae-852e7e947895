<div class="col-lg-10">
    <span class="pull-left">
        <a ng-if="!$root.useAmericanNameStyle" class="user" href="#/users/{{ history.targetPerson.userId }}" once-text="history.targetPerson.fullName"></a>
        <a ng-if="$root.useAmericanNameStyle" class="user" href="#/users/{{ history.targetPerson.userId }}" once-text="history.targetPerson.fullNameEn"></a>
        {{ 'history_info.remove2' | translate }}
        {{ 'history_info.from_responsible_in_vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            ({{ history.vacancy.clientId.name }})
        </a>
    </span>

    <span ng-show="history.descr">
        <br />
        <span ng-if="!history.dateEdit" class="date" style="margin: 0" style="float: none">
            ({{ 'history_info.added' | translate }} {{ history.dc | dateFormatSimpleAction : true | translate }})
        </span>
        <span ng-show="history.dateEdit" class="pull-left date">
            ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})&nbsp;
        </span>
        {{ 'history_info.message' | translate }}:
        <br />
        <span ng-bind-html="history.descr|textForNotice:false"></span>
    </span>
</div>
