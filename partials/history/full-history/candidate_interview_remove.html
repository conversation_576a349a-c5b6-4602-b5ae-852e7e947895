<div class="col-lg-10">
    <span style="color: #ff00ff">
        <span style="padding: 4px; border-radius: 5px; color: #202021; background-color: #e2e2e2">
            {{ 'history_info.removed' | translate }}
        </span>
    </span>
    <span>{{ 'history_info.from vacancy' | translate }}</span>
    <a href="#/vacancies/{{ history.vacancy.localId }}" once-text="history.vacancy.position |limitToEllipse:300"></a>
    <a ng-if="clients.length > 1" href="#/clients/{{ history.vacancy.clientId.localId }}">({{ history.vacancy.clientId.name }})</a>
    <br />
    <span class="ui tiny header"></span>
    <span bo-show="history.targetDate">
        <span bo-text="'interview_times'|translate">:</span>
        <span bo-text="history.targetDate|dateFormat:true"></span>
    </span>
    <div><span ng-bind-html="history.descr|textForNotice:false"></span></div>
</div>
