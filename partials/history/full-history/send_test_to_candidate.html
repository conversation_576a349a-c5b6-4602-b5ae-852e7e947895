<div class="col-lg-10 margin-top" style="word-break: break-word">
    <span ng-if="history.candidatesSend && $root.currentLang != 'en'">
        <span>
            {{ 'Test' | translate }}
            <a target="_blank" ng-href="!#/candidate/test/{{ history.test.id }}">{{ history.test.testName }}</a>
            {{ 'was sent to the candidate_test' | translate }}
            <a href="#/candidates/{{ history.candidatesSend[0].localId }}">
                <span ng-if="$root.useAmericanNameStyle" once-text="history.candidatesSend[0].fullNameEn"></span>
                <span ng-if="!$root.useAmericanNameStyle" once-text="history.candidatesSend[0].fullName"></span>
            </a>
        </span>
        {{ 'while being transferred to the' | translate }}
        <span ng-if="history.customInterviewStates.length > 0" class="stage {{ history.customInterviewStates[0].stateNew }}">
            {{ history.customInterviewStates[0].value }}
        </span>
        <span ng-if="history.generalStates.length > 0" class="stage {{ history.generalStates[0] }}">
            {{ history.generalStates[0] | translate }}
        </span>
        <span>
            {{ 'history_info.on_vacancy' | translate }}
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                ({{ history.vacancy.clientId.name }})
            </a>
        </span>
    </span>
    <span ng-if="history.candidatesSend && $root.currentLang == 'en'">
        The
        <a target="_blank" ng-href="!#/candidate/test/{{ history.test.id }}">{{ history.test.testName }}</a>
        test was sent to the candidate
        <a href="#/candidates/{{ history.candidatesSend[0].localId }}">
            <span ng-if="$root.useAmericanNameStyle" once-text="history.candidatesSend[0].fullNameEn"></span>
            <span ng-if="!$root.useAmericanNameStyle" once-text="history.candidatesSend[0].fullName"></span>
        </a>
        while being transferred to the
        <span ng-if="history.customInterviewStates.length > 0" class="stage {{ history.customInterviewStates[0].stateNew }}">
            {{ history.customInterviewStates[0].value }}
        </span>
        <span ng-if="history.generalStates.length > 0" class="stage {{ history.generalStates[0] }}">
            {{ history.generalStates[0] | translate }}
        </span>
        stage for
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            ({{ history.vacancy.clientId.name }})
        </a>
        vacancy
    </span>
    <span ng-if="history.candidatesNotSend && $root.currentLang != 'en'">
        <span>
            {{ 'Test' | translate }}
            <a target="_blank" ng-href="!#/candidate/test/{{ history.test.id }}">{{ history.test.testName }}</a>
            {{ 'was not sent to the candidate' | translate }}
            <a href="#/candidates/{{ history.candidatesNotSend[0].localId }}">
                <span ng-if="$root.useAmericanNameStyle" once-text="history.candidatesNotSend[0].fullNameEn"></span>
                <span ng-if="!$root.useAmericanNameStyle" once-text="history.candidatesNotSend[0].fullName"></span>
            </a>
            {{ 'when moving to the stage' | translate }}
        </span>
        <span ng-if="history.customInterviewStates.length > 0" class="stage {{ history.customInterviewStates[0].stateNew }}">
            {{ history.customInterviewStates[0].value }}
        </span>
        <span ng-if="history.generalStates.length > 0" class="stage {{ history.generalStates[0] }}">
            {{ history.generalStates[0] | translate }}
        </span>
        {{ 'history_info.on_vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            ({{ history.vacancy.clientId.name }})
        </a>
        {{ 'due to lack of email' | translate }}
    </span>
    <span ng-if="history.candidatesNotSend && $root.currentLang == 'en'">
        <span>
            The
            <a target="_blank" ng-href="!#/candidate/test/{{ history.test.id }}">{{ history.test.testName }}</a>
            test was not sent to the candidate
            <a href="#/candidates/{{ history.candidatesNotSend[0].localId }}">
                <span ng-if="$root.useAmericanNameStyle" once-text="history.candidatesNotSend[0].fullNameEn"></span>
                <span ng-if="!$root.useAmericanNameStyle" once-text="history.candidatesNotSend[0].fullName"></span>
            </a>
            while being transferred to the
            <span ng-if="history.customInterviewStates.length > 0" class="stage {{ history.customInterviewStates[0].stateNew }}">
                {{ history.customInterviewStates[0].value }}
            </span>
            <span ng-if="history.generalStates.length > 0" class="stage {{ history.generalStates[0] }}">
                {{ history.generalStates[0] | translate }}
            </span>
            stage for
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                ({{ history.vacancy.clientId.name }})
            </a>
            vacancy due to lack of email
        </span>
    </span>
</div>
