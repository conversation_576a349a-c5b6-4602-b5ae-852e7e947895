<div class="col-lg-10 margin-top interview_add">
    <span ng-if="!history.candidates && history.candidate">
        {{ 'history_info.the candidate' | translate }}
        <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ history.candidate.localId }}">
            {{ history.candidate.fullNameEn }}
        </a>
        <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ history.candidate.localId }}">
            {{ history.candidate.fullName }}
        </a>
        {{ 'history_info.added_in' | translate }}
        <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
        <span ng-show="history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNewName | translate }}
        </span>
        {{ 'history_info.on_vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="clients.length > 1 && $root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.client.localId }}">
            ({{ history.client.name }})
        </a>
    </span>
    <span ng-if="history.candidates.length == 1">
        {{ 'history_info.the_candidate' | translate }}
        <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ history.candidates[0].localId }}">
            {{ history.candidates[0].fullNameEn }}
        </a>
        <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ history.candidates[0].localId }}">
            {{ history.candidates[0].fullName }}
        </a>
        {{ 'single_copy.copied from vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancyFrom.localId }}">
            <span>{{ history.vacancyFrom.position }}</span>
        </a>
        {{ 'to vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position | limitToEllipse : 300 }}</span>
        </a>
    </span>

    <span ng-if="history.candidates.length == 2">
        {{ 'history_info.candidates' | translate }}
        <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ history.candidates[0].localId }}">
            {{ history.candidates[0].fullNameEn }}
        </a>
        <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ history.candidates[0].localId }}">
            {{ history.candidates[0].fullName }}
        </a>
        {{ 'and' | translate }}
        <a href="#/candidates/{{ history.candidates[1].localId }}">
            {{ history.candidates[1].fullName }}
        </a>
        {{ 'copied from vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancyFrom.localId }}">
            <span>{{ history.vacancyFrom.position }}</span>
        </a>
        {{ 'to vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position | limitToEllipse : 300 }}</span>
        </a>
    </span>

    <span ng-if="history.candidates.length > 2">
        {{ 'history_info.candidates' | translate }}
        <span ng-click="openMenuWithCandidates(history)">
            <span class="blueLink">({{ history.candidates.length }})</span>
        </span>
        {{ 'copied from vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancyFrom.localId }}">
            <span>{{ history.vacancyFrom.position }}</span>
        </a>
        {{ 'to vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position | limitToEllipse : 300 }}</span>
        </a>
    </span>

    <span ng-show="history.targetDate">
        {{ 'interview_times' | translate }}:
        {{ history.targetDate | dateFormat : true }}
    </span>

    <span ng-show="history.descr">
        <br />
        {{ 'history_info.message' | translate }}:
        <br />
        <span ng-bind-html="history.descr|textForNotice:false"></span>
    </span>

    <span ng-if="history.candidates.length > 2" class="manyCandidates">
        <div ng-if="history.showAllCandidates">
            <!-- prettier-ignore -->
            <span class="menuCandidate" ng-repeat="candidate in history.candidates">
                <a ng-if="$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                    {{ candidate.fullNameEn }}<span class="comma">{{ $last ? '' : ', ' }}</span>
                </a>
                <a ng-if="!$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                    {{ candidate.fullName }}<span class="comma">{{ $last ? '' : ', ' }}</span>
                </a>
            </span>
        </div>
    </span>
</div>
