<div class="col-lg-10">
    {{ (history.cards.length > 1 ? 'Score cards' : 'Removed scorecard') | translate }}
    <span ng-repeat="item in history.cards" ng-switch on="item">
        <span>{{ item === 'Default' || item === 'Old Default' ? (item | translate) : item }}{{ $last ? '' : ',' }}</span>
    </span>
    <span
        ng-bind-html="(history.cards.length > 1 ? 'was deleted from vacancy (plural)' : 'was deleted from vacancy (singular)') | translate:{ 'vacancy': history.vacancy }"
    ></span>
    <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'">
        <a href="#/clients/{{ history.vacancy.clientId.localId }}">({{ history.vacancy.clientId.name }})</a>
    </span>
</div>
