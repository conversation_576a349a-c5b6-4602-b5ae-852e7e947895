<div class="col-lg-10 margin-top">
    <span translate="Task status edited" translate-values="{task_name: history.task.title}"></span>
    <span ng-show="history.vacancy">
        {{ 'history_info.in_vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}" once-text="history.vacancy.position |limitToEllipse:300"></a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            ({{ history.vacancy.clientId.name }})
        </a>
    </span>
    <span ng-show="history.candidate">
        {{ 'by candidate' | translate }}
        <a href="#/candidates/{{ history.candidate.localId }}" once-text="history.candidate.fullName"></a>
    </span>
    <span ng-show="$root.currentLang === 'pl' && history.client">
        w kliencie
        <a href="#/clients/{{ history.client.localId }}">{{ history.client.name }}</a>
    </span>
    <span class="status  passed {{ history.stateOld }}">
        {{ history.stateOld | translate }}
    </span>
    <i class="arrow-right-icon" aria-hidden="true"></i>
    <span class="status {{ history.stateNew }}">
        {{ history.stateNew | translate }}
    </span>
</div>
