<div class="col-lg-10 margin-top-status" style="display: flex; flex-wrap: wrap">
    <span>{{ 'Tag' | translate }}</span>
    <span
        class="ellipsis"
        style="border-radius: 5px; margin: 0 5px; padding-left: 4px; padding-right: 4px; color: white; background-color: #6ecff5"
        title="{{ history.descr.oldTagNames.join(',') }}"
    >
        {{ history.descr.oldTagNames.join(',') }}
    </span>
    <span>{{ 'edited to' | translate }}</span>
    <span
        class="ellipsis"
        style="border-radius: 5px; padding-left: 4px; margin: 0 5px; padding-right: 4px; color: white; background-color: #6ecff5"
        title="{{ history.descr.newTagName }}"
    >
        {{ history.descr.newTagName }}
    </span>
    <span ng-if="history.candidates.length === 1">{{ 'from candidate' | translate }}</span>
    <span ng-if="history.candidates.length > 1">{{ 'from candidates' | translate }}</span>
    <div ng-show="history.candidates.length <= 2" style="margin-left: 5px" ng-repeat="candidate in history.candidates">
        <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ candidate.localId }}">
            <span once-text="candidate.fullNameEn"></span>
        </a>
        <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ candidate.localId }}">
            <span once-text="candidate.fullName"></span>
        </a>
        <span ng-hide="$last">{{ 'and' | translate }}</span>
    </div>
    <a ng-show="history.candidates.length > 2" ng-click="toggleCandidates(history)" class="green-link" style="margin-left: 4px">
        ({{ history.candidates.length }})
    </a>
    <div ng-show="history.candidatesToShow" style="margin-left: 5px" ng-repeat="candidate in history.candidates">
        <span style="display: flex">
            <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ candidate.localId }}">
                <span once-text="candidate.fullNameEn"></span>
            </a>
            <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ candidate.localId }}">
                <span once-text="candidate.fullName"></span>
            </a>
            <span ng-hide="$last">,</span>
        </span>
    </div>
</div>
