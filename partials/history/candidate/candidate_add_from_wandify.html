<div class="col-lg-10">
    <span ng-if="$root.currentLang === 'ua'">{{ $root.getVerbByGender(history.person.sex, { male: 'додав', female: 'додала' }) }} кандидата</span>
    <span ng-if="$root.currentLang === 'en'">added candidate</span>
    <span ng-if="$root.currentLang === 'pl'">{{ $root.getVerbByGender(history.person.sex, { male: 'dodał', female: 'dodała' }) }} kandydata</span>
    <span ng-if="$root.currentLang === 'ru'">{{ $root.getVerbByGender(history.person.sex, { male: 'добавил', female: 'добавила' }) }} кандидата</span>

    <a href="#/candidates/{{ history.candidate.localId }}">
        <span once-text="$root.getFullNameDueToStyle(history.candidate)"></span>
    </a>

    <span ng-if="$root.currentLang === 'ua'">з Wandify</span>
    <span ng-if="$root.currentLang === 'en'">from Wandify</span>
    <span ng-if="$root.currentLang === 'pl'">z Wandify</span>
    <span ng-if="$root.currentLang === 'ru'">из Wandify</span>
</div>
