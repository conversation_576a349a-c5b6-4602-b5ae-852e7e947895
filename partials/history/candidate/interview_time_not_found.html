<div class="col-lg-10">
    <span ng-if="$root.currentLang === 'ua'">
        SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} не відправлено кандидату
        <span ng-if="history.vacancy">
            по вакансії
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                <span>{{ history.vacancy.clientId.name }}</span>
            </a>
        </span>
        через відсутність часу співбесіди
    </span>

    <span ng-if="$root.currentLang === 'en'">
        SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} was not sent to candidate
        <span ng-if="history.vacancy">
            for the
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                <span>{{ history.vacancy.clientId.name }}</span>
            </a>
            vacancy due to the absence of an interview time
        </span>
    </span>

    <span ng-if="$root.currentLang === 'pl'">
        SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} nie został wysłany do kandydata
        <span ng-if="history.vacancy">
            na stanowisko
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                <span>{{ history.vacancy.clientId.name }}</span>
            </a>
        </span>
        z powodu braku terminu rozmowy kwalifikacyjnej
    </span>

    <span ng-if="$root.currentLang === 'ru'">
        SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} не отправлено кандидату
        <span ng-if="history.vacancy">
            по вакансии
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                <span>{{ history.vacancy.clientId.name }}</span>
            </a>
        </span>
        из-за отсутствия времени собеседования
    </span>
</div>
