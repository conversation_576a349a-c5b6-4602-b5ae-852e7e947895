<div ng-if="$root.currentLang != 'en'" class="col-lg-10" style="word-break: break-word">
    <span>
        {{ 'Test' | translate }}
        <a target="_blank" ng-href="!#/candidate/test/{{ history.test.id }}">{{ history.test.testName }}</a>
        {{ 'was not sent to the candidate_test' | translate }}
        {{ 'when moving to the stage' | translate }}
    </span>
    <span ng-if="history.customInterviewStates.length > 0" class="stage {{ history.customInterviewStates[0].stateNew }}">
        {{ history.customInterviewStates[0].value }}
    </span>
    <span ng-if="history.generalStates.length > 0" class="stage {{ history.generalStates[0] }}">
        {{ history.generalStates[0] | translate }}
    </span>
    {{ 'history_info.on_vacancy' | translate }}
    <a href="#/vacancies/{{ history.vacancy.localId }}">
        <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300 }}</span>
    </a>
    <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
        ({{ history.vacancy.clientId.name }})
    </a>
    {{ 'due to lack of email' | translate }}
</div>
<div ng-if="$root.currentLang == 'en'" class="col-lg-10" style="word-break: break-word">
    <span>
        The
        <a target="_blank" ng-href="!#/candidate/test/{{ history.test.id }}">{{ history.test.testName }}</a>
        test was not sent to the candidate while being transferred to the
        <span ng-if="history.customInterviewStates.length > 0" class="stage {{ history.customInterviewStates[0].stateNew }}">
            {{ history.customInterviewStates[0].value }}
        </span>
        <span ng-if="history.generalStates.length > 0" class="stage {{ history.generalStates[0] }}">
            {{ history.generalStates[0] | translate }}
        </span>
        stage for
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ',' : '') | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            ({{ history.vacancy.clientId.name }})
        </a>
        vacancy due to lack of email
    </span>
</div>
