<div class="col-lg-10">
    {{ 'history_info.the_candidate' | translate }}
    <span ng-if="history.stateOld != history.stateNew">
        <br />
        {{ 'history_info.changed_the_status_of_candidate' | translate }}
        <span style="color: #ff00ff" status-color-new old="history.stateOld" new="history.stateNew" statusarr="candidate_status_assoc"></span>
    </span>
    <a href="#/candidates/{{ history.candidate.localId }}">
        <span ng-if="$root.useAmericanNameStyle" once-text="history.candidate.fullNameEn"></span>
        <span ng-if="!$root.useAmericanNameStyle" once-text="history.candidate.fullName"></span>
    </a>
    <span>{{ 'was updated from djinni' | translate }}</span>
</div>
