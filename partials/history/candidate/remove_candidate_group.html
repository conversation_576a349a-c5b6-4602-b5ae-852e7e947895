<div class="col-lg-10" style="display: flex; flex-wrap: wrap">
    <span>{{ 'Tag' | translate }}</span>
    <span class="danger" style="max-width: 500px; text-overflow: ellipsis; overflow: hidden; margin-left: 5px; margin-right: 5px" title="{{ history.descr }}">
        {{ history.descr }}
    </span>
    <span>{{ 'was removed' | translate }}</span>
    <span ng-if="history.candidates.length === 1" style="margin-left: 5px; margin-right: 5px">
        {{ 'from candidate' | translate }}
    </span>
    <span ng-if="history.candidates.length > 1" style="margin-left: 5px; margin-right: 5px">{{ 'from candidates' | translate }} :</span>
    <a
        ng-show="history.candidates.length <= 2 || history.candidatesToShow"
        ng-repeat="candidate in history.candidates"
        href="#/candidates/{{ candidate.localId }}"
    >
        <span once-text="candidate.fullName"></span>
        <span ng-hide="$last">,</span>
    </a>
    <a ng-show="!history.candidatesToShow && history.candidates.length > 2" ng-click="toggleCandidates(history)">
        {{ 'candidates' | translate }}&nbsp;({{ history.candidates.length }})
    </a>
</div>
