<div class="col-lg-10">
    <span ng-if="$root.currentLang === 'ua'">
        {{ $root.getVerbByGender(history.person.sex, { male: 'оновив', female: 'оновила' }) }} дані кандидата за допомогою Wandify. Оновлені
    </span>

    <span ng-if="$root.currentLang === 'en'">updated candidate's data using Wandify. Updated</span>

    <span ng-if="$root.currentLang === 'pl'">
        {{ $root.getVerbByGender(history.person.sex, { male: 'zaktu<PERSON><PERSON><PERSON><PERSON>', female: 'zaktualizowała' }) }} dane kandydata za pomocą Wandify. Zaktualizowano
    </span>

    <span ng-if="$root.currentLang === 'ru'">
        {{ $root.getVerbByGender(history.person.sex, { male: 'обновил', female: 'обновила' }) }} данные кандидата с помощью Wandify. Обновлены
    </span>
    <!-- prettier-ignore -->
    <span ng-show="history.descr" ng-repeat="field in history.descr.split(',')">
        <span>{{ vm.updatedFieldsDict[field] | translate }}</span><span ng-hide="$last">,</span>
    </span>
</div>
