<div class="col-lg-10 margin-top interview_add">
    <span ng-if="!history.candidates && history.candidate">
        {{ 'history_info.the_candidate' | translate }}
        <a href="#/candidates/{{ history.candidate.localId }}">
            {{ history.candidate.fullName }}
        </a>
        {{ 'history_info.added_in' | translate }}
        <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
        {{ 'history_info.on_vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
        </a>
        <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
            <span>({{ history.vacancy.clientId.name }})</span>
        </a>
        {{ 'was_add_from_rabotaua' | translate }}
    </span>
    <span ng-if="history.candidates.length == 1">
        {{ 'history_info.the candidate' | translate }}
        <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ history.candidates[0].localId }}">
            {{ history.candidates[0].fullNameEn }}
        </a>
        <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ history.candidates[0].localId }}">
            {{ history.candidates[0].fullName }}
        </a>
        {{ 'history_info.added_in' | translate }}
        <span ng-show="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
        {{ 'history_info.on_vacancy' | translate }}
        <a href="#/vacancies/{{ history.vacancy.localId }}">
            <span>{{ history.vacancy.position | limitToEllipse : 300 }}</span>
        </a>
        {{ 'was_add_from_rabotaua' | translate }}
    </span>
</div>
