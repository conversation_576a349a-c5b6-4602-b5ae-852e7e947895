<div class="candidate-message-wrapper" style="position: relative">
    <div class="col-lg-10 show-for-print" style="display: flex; justify-content: space-between; align-items: center">
        <div class="comment-container">
            <span ng-show="history.dateEdit" class="date" style="margin: 0" style="float: none">
                ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})
            </span>
            <span class="comment">{{ 'comment' | translate }}</span>
            <span class="comment">{{ 'history_info.by_candidate' | translate }}</span>
            <span>
                <!-- prettier-ignore -->
                <span ng-if="$root.useAmericanNameStyle">
                    <a ng-href="#/candidates/{{ history.candidate.localId }}">{{ history.candidate.fullNameEn }}</a><span ng-show="history.descr">:</span>
                </span>
                <!-- prettier-ignore -->
                <span ng-if="!$root.useAmericanNameStyle">
                    <a ng-href="#/candidates/{{ history.candidate.localId }}">{{ history.candidate.fullName }}</a><span ng-show="history.descr">:</span>
                </span>
            </span>
            <br />
            <span ng-show="history.descr" class="text-in-block" ng-bind-html="history.descr|textForNotice:false"></span>
            <br />
        </div>
    </div>
</div>
