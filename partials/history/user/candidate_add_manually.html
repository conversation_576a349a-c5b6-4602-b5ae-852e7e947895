<div class="col-lg-10">
    <div ng-if="$root.currentLang === 'ua' || $root.currentLang === 'ru'">
        {{ 'history_info.added_candidate' | translate }}
        <a ng-if="history.candidate.status !== 'deleted'" href="#/candidates/{{ history.candidate.localId }}">
            <span ng-if="$root.useAmericanNameStyle" once-text="history.candidate.fullNameEn"></span>
            <span ng-if="!$root.useAmericanNameStyle" once-text="history.candidate.fullName"></span>
        </a>
        <span ng-if="history.candidate.status == 'deleted'" style="color: #d67354">
            <span ng-if="$root.useAmericanNameStyle">{{ 'Candidate Removed' | translate }}</span>
            <span ng-if="!$root.useAmericanNameStyle">{{ 'Candidate Removed' | translate }}</span>
        </span>
    </div>
    <div ng-if="$root.currentLang === 'en' || $root.currentLang === 'pl'">
        {{ 'candidate' | translate }}
        <a ng-if="history.candidate.status !== 'deleted'" href="#/candidates/{{ history.candidate.localId }}">
            <span ng-if="$root.useAmericanNameStyle" once-text="history.candidate.fullNameEn"></span>
            <span ng-if="!$root.useAmericanNameStyle" once-text="history.candidate.fullName"></span>
        </a>
        <span ng-if="history.candidate.status === 'deleted'" style="color: #ff0000">
            <span once-text="history.candidate.fullName"></span>
        </span>
        {{ 'history_info.added_lowercase' | translate }}
    </div>
</div>
