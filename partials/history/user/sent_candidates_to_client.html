<div class="col-lg-10">
    <span ng-if="history.candidates.length == 1">{{ 'Candidate' | translate }}</span>
    <span ng-if="history.candidates.length > 1">{{ 'candidates' | translate }}</span>
    <span class="comma">
        <!-- prettier-ignore -->
        <span ng-repeat="candidate in history.candidates">
            <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ candidate.localId }}">
                {{ candidate.fullName }}
            </a><a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ candidate.localId }}">
                {{ candidate.fullNameEn }}
            </a>{{ $last ? '' : ', ' }}
        </span>
    </span>
    {{ 'from_vacancy' | translate }}
    <a href="#/vacancies/{{ history.vacancy.localId }}">
        <span>{{ history.vacancy.position | limitToEllipse : 300 }}</span>
    </a>
    <span ng-if="history.candidates.length == 1">{{ 'was sent to the Client_1' | translate }}</span>
    <span ng-if="history.candidates.length > 1">{{ 'were sent to the Client' | translate }}</span>
    <span ng-switch="$root.me.personParams.clientAccessLevel">
        <span ng-switch-when="hide">{{ history.client.name }}</span>
        <span ng-switch-default>
            <a href="#/clients/{{ history.client.localId }}">{{ history.client.name }}</a>
        </span>
    </span>
    <span class="comma">
        {{ 'email us at' | translate }}
        <span ng-repeat="mail in (history.descr|stringToJson)">{{ mail }}</span>
    </span>
</div>
