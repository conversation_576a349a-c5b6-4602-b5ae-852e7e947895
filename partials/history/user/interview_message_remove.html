<div class="col-lg-10 candidate_message" style="overflow-wrap: anywhere; position: relative">
    <span ng-show="history.dateEdit" class="date" style="margin: 0" style="float: none">
        ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})
    </span>
    <span>{{ 'removed' | translate }}</span>
    <span class="comment">{{ 'history_info.coment' | translate }}</span>
    <span ng-show="!history.editCommentFlag">
        "
        <span ng-bind-html="history.descr|textForNotice:false"></span>
        "
    </span>
    <span ng-if="history.candidate">
        {{ 'history_info.by_candidate' | translate }}
        <a ng-href="#/candidates/{{ history.candidate.localId }}">{{ history.candidate.fullName }}</a>
        {{ 'history_info.in__vacancy' | translate }}
        <a ng-href="#/vacancies/{{ history.vacancy.localId }}">{{ history.vacancy.position }}</a>
    </span>
    <span ng-if="history.candidates.length == 1">
        {{ 'history_info.by_candidate' | translate }}
        <a ng-href="#/candidates/{{ history.candidates[0].localId }}">{{ history.candidates[0].fullName }}</a>
    </span>
    <span ng-if="history.candidates.length == 2">
        {{ 'history_info.by_candidates' | translate }}
        <a ng-href="#/candidates/{{ history.candidates[0].localId }}">
            {{ history.candidates[0].fullName }}
        </a>
        {{ 'and' | translate }}
        <a ng-href="#/candidates/{{ history.candidates[1].localId }}">
            {{ history.candidates[1].fullName }}
        </a>
    </span>
    <span ng-if="history.candidates.length > 2">
        <span ng-click="openMenuWithCandidates(history)">
            {{ 'history_info.by_1' | translate }} {{ 'candidate_3' | translate }}&nbsp;
            <span class="blueLink">({{ history.candidates.length }})</span>
        </span>
    </span>
    <br />
    <span ng-if="history.candidates.length > 2" class="manyCandidates">
        <div ng-if="history.showAllCandidates">
            <span class="menuCandidate" ng-repeat="candidate in history.candidates">
                <a ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                    {{ candidate.fullName }}
                    <span class="comma">{{ $last ? '' : ', ' }}</span>
                </a>
            </span>
        </div>
    </span>
</div>
