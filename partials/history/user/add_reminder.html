<div class="col-lg-10">
    <span ng-if="$root.currentLang === 'ua'">
        {{ $root.getVerbByGender(history.person.sex, { male: 'встановив', female: 'встановила' }) }} автоматичне надсилання SMS для етапу
        <span ng-if="history.generalStates.length > 0" class="stage {{ history.generalStates[0] }}" style="display: inline-flex">
            {{ history.generalStates[0] | translate }}
        </span>
        <span ng-if="history.customInterviewStates.length > 0" class="stage {{ history.customInterviewStates[0].stateNew }}">
            {{ history.customInterviewStates[0].value }}
        </span>
        <span ng-if="history.vacancy">
            вакансії
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                <span>{{ history.vacancy.clientId.name }}</span>
            </a>
        </span>
    </span>
    <span ng-if="$root.currentLang === 'en'">
        set up automatic SMS sending for the
        <span ng-if="history.generalStates.length > 0" class="stage {{ history.generalStates[0] }}" style="display: inline-flex">
            {{ history.generalStates[0] | translate }}
        </span>
        <span ng-if="history.customInterviewStates.length > 0" class="stage {{ history.customInterviewStates[0].stateNew }}">
            {{ history.customInterviewStates[0].value }}
        </span>
        stage of the
        <span ng-if="history.vacancy">
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                <span>{{ history.vacancy.clientId.name }}</span>
            </a>
        </span>
        vacancy
    </span>
    <span ng-if="$root.currentLang === 'pl'">
        {{ $root.getVerbByGender(history.person.sex, { male: 'ustawił', female: 'ustawiła' }) }} automatyczne wysyłanie SMS dla etapu
        <span ng-if="history.generalStates.length > 0" class="stage {{ history.generalStates[0] }}" style="display: inline-flex">
            {{ history.generalStates[0] | translate }}
        </span>
        <span ng-if="history.customInterviewStates.length > 0" class="stage {{ history.customInterviewStates[0].stateNew }}">
            {{ history.customInterviewStates[0].value }}
        </span>
        <span ng-if="history.vacancy">
            dla projektu
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                <span>{{ history.vacancy.clientId.name }}</span>
            </a>
        </span>
    </span>
    <span ng-if="$root.currentLang === 'ru'">
        {{ $root.getVerbByGender(history.person.sex, { male: 'установил', female: 'установила ' }) }} автоматическую отправку SMS для этапа
        <span ng-if="history.generalStates.length > 0" class="stage {{ history.generalStates[0] }}" style="display: inline-flex">
            {{ history.generalStates[0] | translate }}
        </span>
        <span ng-if="history.customInterviewStates.length > 0" class="stage {{ history.customInterviewStates[0].stateNew }}">
            {{ history.customInterviewStates[0].value }}
        </span>
        <span ng-if="history.vacancy">
            вакансии
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                <span>{{ history.vacancy.clientId.name }}</span>
            </a>
        </span>
    </span>
</div>
