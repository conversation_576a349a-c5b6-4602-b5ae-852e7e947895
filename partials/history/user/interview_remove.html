<div class="col-lg-10">
    <span class="pull-left">{{ 'history_info.candidate' | translate }}</span>
    <a style="margin-left: 4px" href="#/candidates/{{ history.candidate.localId }}">
        <span ng-if="$root.useAmericanNameStyle" once-text="history.candidate.fullNameEn"></span>
        <span ng-if="!$root.useAmericanNameStyle" once-text="history.candidate.fullName"></span>
    </a>
    <!-- prettier-ignore -->
    <span style="color: #ff00ff">
        <span style="padding: 4px; border-radius: 5px; color: #202021; background-color: #e2e2e2">{{ 'history_info.removed' | translate }}</span>
    </span>
    <span>{{ 'history_info.from vacancy' | translate }}</span>
    <a href="#/vacancies/{{ history.vacancy.localId }}" once-text="history.vacancy.position |limitToEllipse:300"></a>
    <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
        ({{ history.vacancy.clientId.name }})
    </a>
    <br />
    <span class="ui tiny header"></span>
    <span bo-show="history.targetDate">
        <span>{{ 'interview_times' | translate }}:</span>
        <span bo-text="history.targetDate|dateFormat:true"></span>
    </span>

    <span ng-show="history.descr">
        <span ng-show="history.dateEdit" class="pull-left date">
            ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})&nbsp;
        </span>
        {{ 'history_info.message' | translate }}:
        <br />
        <span ng-bind-html="history.descr|textForNotice:false"></span>
    </span>
</div>
