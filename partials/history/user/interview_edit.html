<div class="col-lg-10 margin-top">
    <span>
        <span ng-if="history.candidate">
            {{ 'history_info.candidate' | translate }}
            <a ng-href="#/candidates/{{ history.candidate.localId }}">
                {{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}
                {{ !history.candidate.fullName ? history.candidate.firstName : '' }}
            </a>
        </span>
        <span ng-if="history.candidates.length == 2">
            <a ng-href="#/candidates/{{ history.candidates[0].localId }}">
                {{ history.candidates[0].fullName }}
                {{ !history.candidates[0].fullName ? history.candidates[0].firstName : '' }}
            </a>
            {{ 'and' | translate }}
            <a ng-href="#/candidates/{{ history.candidates[1].localId }}">
                {{ $root.useAmericanNameStyle ? history.candidates[1].fullNameEn : history.candidates[1].fullName }}
                {{ !history.candidates[1].fullName ? history.candidates[1].firstName : '' }}
            </a>
        </span>
        <span ng-if="history.candidates.length > 2" class="manyCandidates">
            <!-- prettier-ignore -->
            <span ng-click="openMenuWithCandidates(history)">
                {{ 'history_info.candidates' | translate }} <span class="blueLink">({{ history.candidates.length }})</span>
            </span>
        </span>
        {{ (history.candidates.length > 1 ? 'history_info.were moved from' : 'history_info.was moved from') | translate }}
        <span ng-if="history.stateOldName" class="stage passed {{ history.stateOld }}">
            <span ng-if="history.customStateOld">{{ history.stateOldName }}</span>
            <span ng-if="!history.customStateOld">{{ history.stateOldName | translate }}</span>
        </span>
        <span ng-if="!history.stateOldName" class="stage passed {{ history.stateOld }}">
            {{ history.stateOld | translate }}
        </span>
        {{ 'history_info.stage to' | translate }}
        <span ng-if="history.stateNewName" class="stage {{ history.stateNew }}">
            <span ng-if="history.customStateNew">{{ history.stateNewName }}</span>
            <span ng-if="!history.customStateNew">{{ history.stateNewName | translate }}</span>
        </span>
        <span ng-if="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
        {{ 'history_info.on_vacancy' | translate }}
        <span ng-if="!history.client">
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
        </span>

        <span ng-if="history.client">
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
        </span>
        <a ng-if="history.client && $root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.client.localId }}">
            ({{ history.client.name }})
        </a>

        <span ng-if="history.fromVoithos">{{ 'by the Voithos AI suggestion' | translate }}</span>
    </span>

    <span ng-show="history.descr">
        <br />
        <span ng-if="!history.dateEdit" class="date" style="margin: 0" style="float: none">
            ({{ 'history_info.added' | translate }} {{ history.dc | dateFormatSimpleAction : true | translate }})
        </span>
        <span ng-if="history.dateEdit" class="date" style="margin: 0" style="float: none">
            ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})
        </span>
        {{ 'comment' | translate }}:
        <br />
    </span>
    <span ng-if="history.candidates.length > 2" class="manyCandidates">
        <div ng-show="history.showAllCandidates">
            <span class="menuCandidate" ng-repeat="candidate in history.candidates">
                <a ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                    {{ candidate.fullName }}
                </a>
            </span>
        </div>
    </span>
    <div class="comment-container" style="padding-right: 40px">
        <span ng-bind-html="history.descr.trim()|textForNotice:false"></span>
        <span ng-if="history.dateEmployee" style="margin: 0; display: block">
            {{ 'Hiring date' | translate }}: {{ (history.dateEmployee | dateFormatSimple2).trim() }}
        </span>
        <span ng-if="history.probationPeriod" style="margin: 0; display: block">
            {{ 'Probation period' | translate }}: {{ history.probationPeriod | translate }}
        </span>
    </div>
</div>
