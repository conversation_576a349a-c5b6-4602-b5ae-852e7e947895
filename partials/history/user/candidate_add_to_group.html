<!-- prettier-ignore -->
<div class="col-lg-10">
    {{ 'history_info.Tag' | translate }}
    <span
        class="ellipsis"
        style="border-radius: 5px; padding-left: 4px; padding-right: 4px; color: white; background-color: #6ecff5"
        title="{{ history.descr }}"
    >{{ history.descr }}</span>
    <span ng-if="history.candidates.length === 1">{{ 'history_info.candidate_add_to_group' | translate }}</span>
    <span ng-if="history.candidates.length > 1">{{ 'history_info.candidate_add_to_groups' | translate }}</span>
    <div ng-show="history.candidates.length <= 2" style="margin-right: 5px; display: inline-block" ng-repeat="candidate in history.candidates">
        <a href="#/candidates/{{ candidate.localId }}">
            <span once-text="candidate.fullName"></span>
        </a>
        <span ng-hide="$last">{{ 'and' | translate }}</span>
    </div>
    <a class="green-link" ng-show="history.candidates.length > 2" ng-click="toggleCandidates(history)">({{ history.candidates.length }})</a>
    <div ng-show="history.candidates.length > 2">
        <span ng-show="history.candidatesToShow" ng-repeat="candidate in history.candidates">
            <a style="display: inline-flex" href="#/candidates/{{ candidate.localId }}">
                <div once-text="candidate.fullName"></div>
                <div ng-hide="$last">,</div>
            </a>
        </span>
    </div>
</div>
