<div class="col-lg-10">
    <span ng-if="$root.currentLang === 'ua'">
        SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} не відправлено кандидату
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span>{{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}</span>
        </a>
        при переведенні на етап
        <span class="stage {{ history.stateNewName ? history.stateNewName : history.stateNew }}">
            {{ history.stateNewName ? (history.stateNewName | translate) : (history.stateNew | translate) }}
        </span>
        <span ng-if="history.vacancy">
            вакансії
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                <span>{{ history.vacancy.clientId.name }}</span>
            </a>
        </span>
        через відсутність у нього номера телефону
    </span>

    <span ng-if="$root.currentLang === 'en'">
        SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} was not sent to candidate
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span>{{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}</span>
        </a>
        when moving to the
        <span class="stage {{ history.stateNewName ? history.stateNewName : history.stateNew }}">
            {{ history.stateNewName ? (history.stateNewName | translate) : (history.stateNew | translate) }}
        </span>
        stage of the
        <span ng-if="history.vacancy">
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                <span>{{ history.vacancy.clientId.name }}</span>
            </a>
            vacancy due to the absence of a phone number
        </span>
    </span>

    <span ng-if="$root.currentLang === 'pl'">
        SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} nie został wysłany do kandydata
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span>{{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}</span>
        </a>
        podczas przeniesienia do etapu
        <span class="stage {{ history.stateNewName ? history.stateNewName : history.stateNew }}">
            {{ history.stateNewName ? (history.stateNewName | translate) : (history.stateNew | translate) }}
        </span>
        <span ng-if="history.vacancy">
            dla projektu
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                <span>{{ history.vacancy.clientId.name }}</span>
            </a>
        </span>
        z powodu braku numeru telefonu
    </span>

    <span ng-if="$root.currentLang === 'ru'">
        SMS {{ history.smsTemplate ? '"' + history.smsTemplate.name + '"' : '' }} не отправлено кандидату
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span>{{ $root.useAmericanNameStyle ? history.candidate.fullNameEn : history.candidate.fullName }}</span>
        </a>
        при переводе на етап
        <span class="stage {{ history.stateNewName ? history.stateNewName : history.stateNew }}">
            {{ history.stateNewName ? (history.stateNewName | translate) : (history.stateNew | translate) }}
        </span>
        <span ng-if="history.vacancy">
            вакансии
            <a href="#/vacancies/{{ history.vacancy.localId }}">
                <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
            </a>
            <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
                <span>{{ history.vacancy.clientId.name }}</span>
            </a>
        </span>
        из-за отсутствия у него номера телефона
    </span>
</div>
