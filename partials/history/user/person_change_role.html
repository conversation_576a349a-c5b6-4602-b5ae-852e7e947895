<div class="col-lg-10">
    {{ 'user2' | translate }}
    <a ng-if="!$root.useAmericanNameStyle" href="#/users/{{ history.targetPerson.userId }}" once-text="history.targetPerson.fullName"></a>
    <a ng-if="$root.useAmericanNameStyle" href="#/users/{{ history.targetPerson.userId }}" once-text="history.targetPerson.fullNameEn"></a>
    <span ng-if="true">{{ 'history_info.person change role' | translate }}</span>
    <span ng-if="::history.stateNew == 'client'">Hiring manager</span>
    <span ng-if="::history.stateNew != 'client'" translate="{{ history.stateNew }}">Hiring manager</span>
</div>
