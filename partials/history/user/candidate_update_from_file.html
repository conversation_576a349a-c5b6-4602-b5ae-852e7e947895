<div class="col-lg-10">
    {{ 'Information about the candidate' | translate }}
    <a href="#/candidates/{{ history.candidate.localId }}">
        <span ng-if="$root.useAmericanNameStyle" once-text="history.candidate.fullNameEn"></span>
        <span ng-if="!$root.useAmericanNameStyle" once-text="history.candidate.fullName"></span>
    </a>
    {{ 'of the updated file' | translate }}
    <span ng-click="showFilePreview(history)" class="prevFileStyles" style="color: #daa10f">{{ history.descr | fileNameCut : 0 : 50 }}</span>
</div>
