<div class="col-lg-10">
    <span ng-if="$root.currentLang === 'en'">{{ 'The user changed the source to' | translate }}</span>
    <span ng-if="$root.currentLang !== 'en'">
        {{ (history.person.sex ? 'The user changed the source to' : 'The user changed the source to (woman)') | translate }}
    </span>
    {{ history.stateNew | translate }}
    <span ng-if="history.candidates.length === 1">
        {{ 'for the candidate' | translate }}
        <a href="#/candidates/{{ history.candidates[0].localId }}">{{ history.candidates[0].fullName }}</a>
    </span>
    <span ng-if="history.candidates.length > 1">
        {{ 'for the candidates' | translate }}
        <a ng-click="toggleCandidates(history)" class="green-link">({{ history.candidates.length }})</a>
        <br />
        <span ng-show="history.candidatesToShow" ng-repeat="candidate in history.candidates">
            <!-- prettier-ignore -->
            <a style="display: inline-flex" href="#/candidates/{{ candidate.localId }}">
                <span once-text="candidate.fullName"></span><span ng-hide="$last">,</span>
            </a>
        </span>
    </span>
</div>
