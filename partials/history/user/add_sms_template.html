<div class="col-lg-10">
    <span ng-if="$root.currentLang === 'ua'">
        {{ $root.getVerbByGender(history.person.sex, { male: 'створив', female: 'створила' }) }} шаблон SMS
        <span ng-if="history.smsTemplate">"{{ history.smsTemplate.name }}"</span>
    </span>
    <span ng-if="$root.currentLang === 'en'">
        created an SMS template
        <span ng-if="history.smsTemplate">"{{ history.smsTemplate.name }}"</span>
    </span>
    <span ng-if="$root.currentLang === 'pl'">
        {{ $root.getVerbByGender(history.person.sex, { male: 'utworzył', female: 'utworzyła' }) }} szablon SMS
        <span ng-if="history.smsTemplate">"{{ history.smsTemplate.name }}"</span>
    </span>
    <span ng-if="$root.currentLang === 'ru'">
        {{ $root.getVerbByGender(history.person.sex, { male: 'создал', female: 'создала' }) }} шаблон SMS
        <span ng-if="history.smsTemplate">"{{ history.smsTemplate.name }}"</span>
    </span>
</div>
