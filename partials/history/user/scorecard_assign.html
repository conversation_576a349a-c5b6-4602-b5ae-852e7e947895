<div class="col-lg-10">
    {{ (history.cards.length > 1 ? 'Score cards' : 'Score card') | translate }}
    <span ng-repeat="item in history.cards" ng-switch on="item">
        <span>{{ item === 'Default' || item === 'Old Default' ? (item | translate) : item }}{{ $last ? '' : ',' }}</span>
    </span>
    <span
        ng-bind-html="(history.cards.length > 1 ? 'was assign to vacancy (plural)' : 'was assign to vacancy (singular)') | translate:{ 'vacancy': history.vacancy }"
    ></span>
    <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'">
        <a href="#/clients/{{ history.vacancy.clientId.localId }}">({{ history.vacancy.clientId.name }})</a>
    </span>
</div>
