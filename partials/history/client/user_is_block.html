<div class="col-lg-10">
    <span ng-if="history.stateNew == 'N'">{{ 'user2' | translate }}</span>
    <span ng-if="history.stateNew == 'A'">{{ 'user2' | translate }}</span>
    <a ng-if="!$root.useAmericanNameStyle" href="#/users/{{ history.targetPerson.userId }}" once-text="history.targetPerson.fullName"></a>
    <a ng-if="$root.useAmericanNameStyle" href="#/users/{{ history.targetPerson.userId }}" once-text="history.targetPerson.fullNameEn"></a>
    <span ng-if="history.stateNew == 'N'">{{ 'history_info.has blocked' | translate }}</span>
    <span ng-if="history.stateNew == 'A'">{{ 'history_info.has unblocked' | translate }}</span>
</div>
