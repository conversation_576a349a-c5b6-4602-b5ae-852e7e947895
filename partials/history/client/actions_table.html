<div class="col-lg-12 history-outer">
    <table id="mainTable" class="table" ng-table="tableParamsClientHistory" template-pagination="custom/pager">
        <thead>
            <tr class="last-actions-header grey-header" style="display: contents">
                <th class="title-header border flex-space-between flex-align-center" style="background: #f8f8f8">
                    <div class="title-header-wrapper">
                        <span class="pull-left">{{ 'Activity_Log' | translate }}</span>
                        <div class="switch">
                            <custom-switcher
                                switcher-id="'candidate'"
                                first-label="'Show comments only'"
                                second-label="'Show details'"
                                background="true"
                                checked="onlyComments"
                                method="showCommentsSwitch"
                            ></custom-switcher>
                        </div>
                    </div>
                    <img
                        ng-click="showModalAddCommentToClient()"
                        class="pull-right"
                        src="../images/sprite/addMessage.svg"
                        alt="{{ 'add_comment' | translate }}"
                        title="{{ 'add_comment' | translate }}"
                    />
                </th>
            </tr>
        </thead>
        <tbody>
            <tr
                class="event last-action border"
                ng-class="{ 'not-for-print': history.type != 'candidate_message' }"
                style="min-height: 54px; word-break: break-all"
                ng-repeat-start="history in history"
                add-directive-removed-person
            >
                <td class="border" ng-switch="history.type">
                    <div class="col-lg-12 hidden" ng-switch-default></div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="interview_add_from_copy_candidates">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_add_from_copy_candidates.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="interview_edit">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_edit.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="vacancy_edit">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/vacancy_edit.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="vacancy_add">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/vacancy_add.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="vacancy_change_state">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/vacancy_change_state.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="interview_add">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_add.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="interview_edit_date">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_edit_date.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="client_change_state">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/client_change_state.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="contact_client_add">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/contact_client_add.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="contact_client_edit">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/contact_client_edit.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="client_add">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/client_add.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="client_sent_email">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/client_sent_email.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="client_edit">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/client_edit.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="client_add_file">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/client_add_file.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="client_add_link">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/client_add_link.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="client_remove_file">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/client_remove_file.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="client_remove_link">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/client_remove_link.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="vacancy_set_responsible">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/vacancy_set_responsible.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="candidate_add">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/candidate_add.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="candidate_add_file">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/candidate_add_file.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="candidate_change_state">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/candidate_change_state.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="interview_edit_sent_email">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_edit_sent_email.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="candidate_edit">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/candidate_edit.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="candidate_message">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/candidate_message.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="candidate_remove_file">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/candidate_remove_file.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="candidate_set_responsible">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/candidate_set_responsible.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="client_message">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/client_message.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="interview_message">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_message.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="interview_remove">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_remove.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="interviews_remove">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interviews_remove.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="sent_candidate_to_client">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/sent_candidate_to_client.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="sent_candidates_to_client">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/sent_candidates_to_client.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="change_org_name">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/user_change_org_name.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="user_is_block">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/user_is_block.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="vacancy_add_file">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/vacancy_add_file.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="vacancy_message">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/vacancy_message.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="vacancy_remove_file">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/vacancy_remove_file.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="vacancy_remove_responsible">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/vacancy_remove_responsible.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="vacancy_change_responsible">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/vacancy_change_responsible.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="task_create">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/task_create.html'"></ng-include>
                    </div>
                    <div class="col-lg-12" ng-switch-when="task_change">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/task_change.html'"></ng-include>
                    </div>
                    <div class="col-lg-12" ng-switch-when="task_change_status">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/task_change_status.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="interview_add_from_advice">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_add_from_advice.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12" ng-switch-when="client_set_responsible">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/client_set_responsible.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12 col-md-12 col-xs-12" ng-switch-when="client_change_responsible">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/client_change_responsible.html'"></ng-include>
                    </div>
                    <div class="col-lg-12" ng-switch-when="client_remove_responsible">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/client_remove_responsible.html'"></ng-include>
                    </div>
                    <div class="col-lg-12" ng-switch-when="interview_add_sent_email">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_add_sent_email.html'"></ng-include>
                        <ng-include src="'partials/history/full-history/buttons.html'"></ng-include>
                    </div>
                    <div class="col-lg-12" ng-switch-when="interview_add_from_workUa">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_add_from_workUa.html'"></ng-include>
                    </div>
                    <div class="col-lg-12" ng-switch-when="interview_add_from_delucru">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_add_from_delucru.html'"></ng-include>
                    </div>
                    <div class="col-lg-12" ng-switch-when="interview_add_from_rabotaUa">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_add_from_rabotaUa.html'"></ng-include>
                    </div>
                    <div class="col-lg-12" ng-switch-when="interview_add_from_djinni">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_add_from_djinni.html'"></ng-include>
                    </div>
                    <div class="col-lg-12" ng-switch-when="interview_add_from_hh">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_add_from_hh.html'"></ng-include>
                    </div>
                    <div class="col-lg-12" ng-switch-when="interview_add_from_grc">
                        <ng-include src="'partials/history/full-history/personal-with-date.html'"></ng-include>
                        <ng-include src="'partials/history/client/interview_add_from_grc.html'"></ng-include>
                    </div>
                </td>
            </tr>
            <tr class="table-space" ng-hide="$last" ng-repeat-end=""></tr>
        </tbody>
    </table>

    <pagination-component
        ng-if="paginationParams.totalCount > 15"
        class-name="'client-pagination'"
        translate-func="$root.translate"
        total-pages="paginationParams.allPageCount"
        total-elements="paginationParams.totalCount"
        current-page="paginationParams.currentPage+1"
        current-amount-of-elements="tableParamsClientHistory.count()"
        on-change-page="(changePage)"
        on-change-amount-of-elements="(changeAmountOfElements)"
        on-show-more="(showMore)"
        is-show-more-mode="isShowMore"
    ></pagination-component>
</div>
