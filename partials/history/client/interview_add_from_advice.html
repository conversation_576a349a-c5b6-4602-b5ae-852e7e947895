<div class="col-lg-10">
    <a href="#/candidates/{{ history.candidate.localId }}">
        {{ history.candidate.fullName }}
    </a>
    <span class="pull-left">{{ 'history_info.added_in' | translate }}</span>
    <br />
    <span ng-if="history.stateNewName" class="stage {{ history.stateNew }}">
        {{ history.stateNewName | translate }}
    </span>
    {{ 'history_info.by recommendation of CleverStaff' | translate }}

    <span ng-show="history.descr">
        <span ng-show="history.dateEdit" class="pull-left date">
            ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})&nbsp;
        </span>
        {{ 'history_info.message' | translate }}:
        <br />
        <span ng-bind-html="history.descr|textForNotice:false"></span>
    </span>
</div>
