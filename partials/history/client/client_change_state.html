<div class="col-lg-10">
    <span ng-if="$root.currentLang !== 'en'">{{ 'history_info.Changed the status of the client' | translate }}</span>
    <span ng-if="$root.currentLang == 'en'">{{ 'history_info.The status of the client' | translate }}</span>
    <span ng-switch="$root.me.personParams.clientAccessLevel">
        <span ng-switch-when="hide">{{ history.client.name }}</span>
        <span ng-switch-default>
            <a href="#/clients/{{ history.client.localId }}">{{ history.client.name }}</a>
            <span ng-if="$root.currentLang == 'en'">{{ 'history_info.was changed' | translate }}</span>
        </span>
    </span>
    <span class="status  passed {{ history.stateOld }}">
        {{ history.stateOld | translate }}
    </span>
    <i class="arrow-right-icon" aria-hidden="true"></i>
    <span class="status {{ history.stateNew }}">
        {{ history.stateNew | translate }}
    </span>

    <span ng-show="history.descr">
        <br />
        <span ng-if="!history.dateEdit" class="date" style="margin: 0" style="float: none">
            ({{ 'history_info.added' | translate }} {{ history.dc | dateFormatSimpleAction : true | translate }})
        </span>
        <span ng-show="history.dateEdit" class="pull-left date">
            ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})&nbsp;
        </span>
        {{ 'history_info.message' | translate }}:
        <br />
        <span ng-bind-html="history.descr|textForNotice:false"></span>
    </span>
</div>
