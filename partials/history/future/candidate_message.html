<div class="col-lg-12">
    <a class="user" href="#/vacancies/{{ vacancy.localId }}">
        <span once-text="vacancy.position |limitToEllipse:300"></span>
    </a>
    <span class="coma">, {{ vacancy.clientId.name }},</span>
    {{ 'history_info.message_to_candidate' | translate }}
    <a href="#/candidates/{{ vacancy.actions.objects[0].candidate.localId }}">
        <span ng-if="$root.useAmericanNameStyle" once-text="vacancy.actions.objects[0].candidate.fullNameEn"></span>
        <span ng-if="!$root.useAmericanNameStyle" once-text="vacancy.actions.objects[0].candidate.fullName"></span>
    </a>
    :
    <br />
    <span ng-bind-html="vacancy.actions.objects[0].descr|textForNotice:false"></span>
</div>
