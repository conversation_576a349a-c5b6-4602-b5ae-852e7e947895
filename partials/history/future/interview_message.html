<div class="col-lg-12">
    <span ng-if="$root.currentLang !== 'en'">{{ 'history_info.add_message' | translate }} {{ 'history_info.by_candidate_2' | translate }}</span>
    <span ng-if="$root.currentLang === 'en'">The comment was added for the candidate</span>

    <a href="#/candidates/{{ vacancy.actions.objects[0].candidate.localId }}">
        <span ng-if="$root.useAmericanNameStyle" once-text="vacancy.actions.objects[0].candidate.fullNameEn"></span>
        <span ng-if="!$root.useAmericanNameStyle" once-text="vacancy.actions.objects[0].candidate.fullName"></span>
    </a>

    <span ng-if="$root.currentLang !== 'en'">{{ 'history_info.in_vacancy' | translate }}</span>
    <span ng-if="$root.currentLang === 'en'">of the</span>

    <!-- prettier-ignore -->
    <span>
    <a class="user" href="#/vacancies/{{ vacancy.localId }}"><span once-text="vacancy.position |limitToEllipse:300"></span></a><span ng-if="$root.currentLang !== 'en' && $root.me.personParams.clientAccessLevel === 'hide'">:</span>
        <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span><span ng-if="$root.currentLang !== 'en' && $root.me.personParams.clientAccessLevel !== 'hide'">:</span><span ng-if="$root.currentLang === 'en'"> vacancy:</span>
    </span>

    <span class="ui tiny header"></span>

    <span
        ng-if="vacancy.actions.objects[0].descrLength <= 69"
        class="stylesForTags"
        ng-bind-html="vacancy.actions.objects[0].descr | textForNotice:false"
    ></span>

    <span ng-if="vacancy.actions.objects[0].descrLength >= 70">
        <span
            ng-if="!vacancy.actions.objects[0].readMore"
            class="stylesForTags"
            ng-bind-html="vacancy.actions.objects[0].cutDescr | textForNotice:false"
        ></span>
        <span ng-if="vacancy.actions.objects[0].readMore" class="stylesForTags" ng-bind-html="vacancy.actions.objects[0].descr | textForNotice:false"></span>
    </span>

    <span ng-if="vacancy.actions.objects[0].descrLength > 70">
        <span ng-if="!vacancy.actions.objects[0].readMore" class="arrow-down-read-more">
            <img ng-click="$root.changeMoreAndLess(vacancy.actions.objects[0])" src="/images/arrow-down.svg" alt="down-arrow" />
        </span>
    </span>

    <div ng-if="vacancy.actions.objects[0].descrLength > 70">
        <span ng-if="vacancy.actions.objects[0].readMore" class="arrow-up-read-more">
            <img ng-click="$root.changeMoreAndLess(vacancy.actions.objects[0])" src="/images/arrow-up.svg" alt="down-arrow" />
        </span>
    </div>
</div>
