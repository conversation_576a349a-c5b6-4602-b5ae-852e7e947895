<div class="col-lg-12">
    <span ng-if="$root.currentLang !== 'en'">{{ 'The sequence of stages of the vacancy' | translate }}</span>
    <span ng-if="$root.currentLang === 'en'">The stages of the</span>

    <a class="user" href="#/vacancies/{{ vacancy.localId }}">
        <span once-text="vacancy.position |limitToEllipse:300"></span>
    </a>
    <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span>
    <span ng-if="$root.currentLang === 'en'">vacancy</span>

    <a href="#/candidates/{{ vacancy.actions.objects[0].candidate.localId }}">
        <span ng-if="!$root.useAmericanNameStyle">{{ vacancy.actions.objects[0].candidate.fullName }}</span>
        <span ng-if="$root.useAmericanNameStyle">{{ vacancy.actions.objects[0].candidate.fullNameEn }}</span>
    </a>

    <span ng-if="$root.currentLang !== 'en'">{{ '_edited_2' | translate }}</span>
    <span ng-if="$root.currentLang === 'en'">was changed</span>
</div>
