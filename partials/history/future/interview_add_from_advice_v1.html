<div class="col-lg-12">
    <!-- prettier-ignore -->
    <a class="user" href="#/vacancies/{{ vacancy.localId }}">
        <span once-text="vacancy.position |limitToEllipse:300"></span><span ng-if="$root.me.personParams.clientAccessLevel === 'hide'">,</span>
    </a>
    <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="coma">, {{ vacancy.clientId.name }},</span>
    <span>
        <span ng-if="!vacancy.actions.objects[0].candidate && vacancy.actions.objects[0].candidates && vacancy.actions.objects[0].candidates.length > 2">
            {{ vacancy.actions.objects[0].candidateIds.split(',').length }}
        </span>
        <span ng-if="!!vacancy.actions.objects[0].candidate">
            <span ng-if="getLettersEnd(vacancy.actions.objects[0].candidateIds.split(',').length) === 2">
                {{ 'candidate_2' | translate }}
            </span>
        </span>
        <span ng-if="!vacancy.actions.objects[0].candidate">
            <span ng-if="getLettersEnd(vacancy.actions.objects[0].candidateIds.split(',').length) === 0">
                {{ 'candidate' | translate }}
            </span>
            <span ng-if="getLettersEnd(vacancy.actions.objects[0].candidateIds.split(',').length) === 1 && vacancy.actions.objects[0].candidates.length !== 2">
                {{ 'candidate_2' | translate }}
            </span>
            <span ng-if="getLettersEnd(vacancy.actions.objects[0].candidateIds.split(',').length) === 2">
                {{ 'history_info.candidates_1' | translate }}
            </span>
            <span ng-if="getLettersEnd(vacancy.actions.objects[0].candidateIds.split(',').length) === 2 && vacancy.actions.objects[0].candidates.length == 1">
                {{ 'history_info.candidates_1' | translate }}
            </span>
        </span>

        <span ng-if="vacancy.actions.objects[0].candidate && !vacancy.actions.objects[0].candidates">
            <a href="#/candidates/{{ vacancy.actions.objects[0].candidate.localId }}">
                <span ng-if="!$root.useAmericanNameStyle">{{ vacancy.actions.objects[0].candidate.fullName }}</span>
                <span ng-if="$root.useAmericanNameStyle">{{ vacancy.actions.objects[0].candidate.fullNameEn }}</span>
            </a>
        </span>
        <span ng-if="!vacancy.actions.objects[0].candidate && vacancy.actions.objects[0].candidates && vacancy.actions.objects[0].candidates.length === 1">
            <a href="#/candidates/{{ vacancy.actions.objects[0].candidate.localId }}">
                {{ vacancy.actions.objects[0].candidates[0].fullName }}
            </a>
        </span>
        <span ng-if="!vacancy.actions.objects[0].candidate && vacancy.actions.objects[0].candidates && vacancy.actions.objects[0].candidates.length === 2">
            {{ 'history_info.candidates1' | translate }}
            <a href="#/candidates/{{ vacancy.actions.objects[0].candidates[0].localId }}">
                {{ vacancy.actions.objects[0].candidates[0].fullName }}
            </a>
            {{ 'and' | translate }}
            <a href="#/candidates/{{ vacancy.actions.objects[0].candidates[1].localId }}">
                {{ vacancy.actions.objects[0].candidates[1].fullName }}
            </a>
            {{ 'history_info.addeds' | translate }}
        </span>

        <span ng-if="getLettersEnd(vacancy.actions.objects[0].candidateIds.split(',').length) === 0">
            {{ 'history_info.added_in' | translate }}
        </span>
        <span ng-if="getLettersEnd(vacancy.actions.objects[0].candidateIds.split(',').length) !== 0 && vacancy.actions.objects[0].candidates.length !== 2">
            {{ 'history_info.added_in_3' | translate }}
        </span>

        <span class="stage longlist">
            {{ 'long_list' | translate }}
        </span>
        {{ 'history_info.on_vacancy' | translate }}
    </span>
    <span>{{ 'history_info.by recommendation of CleverStaff' | translate }}.</span>
</div>
