<div class="col-lg-12">
    <span ng-if="vacancy.actions.objects[0].candidatesSend && $root.currentLang != 'en'">
        <span>
            {{ 'Test' | translate }} "{{ vacancy.actions.objects[0].test.testName }}"
            {{ 'was sent to the candidate_test' | translate }}
            <a href="#/candidates/{{ vacancy.actions.objects[0].candidatesSend[0].localId }}">
                <span ng-if="$root.useAmericanNameStyle" once-text="vacancy.actions.objects[0].candidatesSend[0].fullNameEn"></span>
                <span ng-if="!$root.useAmericanNameStyle" once-text="vacancy.actions.objects[0].candidatesSend[0].fullName"></span>
            </a>
        </span>
        {{ 'while being transferred to the' | translate }}
        <span
            ng-if="vacancy.actions.objects[0].customInterviewStates.length > 0"
            class="stage {{ vacancy.actions.objects[0].customInterviewStates[0].stateNew }}"
        >
            {{ vacancy.actions.objects[0].customInterviewStates[0].value }}
        </span>
        <span ng-if="vacancy.actions.objects[0].generalStates.length > 0" class="stage {{ vacancy.actions.objects[0].generalStates[0] }}">
            {{ vacancy.actions.objects[0].generalStates[0] | translate }}
        </span>
        <span>
            {{ 'history_info.on_vacancy' | translate }}
            <a href="#/vacancies/{{ vacancy.actions.objects[0].vacancy.localId }}">
                <span>{{ vacancy.actions.objects[0].vacancy.position | limitToEllipse : 300 }}</span>
            </a>
            <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" style="color: #828282">
                ({{ vacancy.actions.objects[0].vacancy.clientId.name }})
            </span>
        </span>
    </span>
    <span ng-if="vacancy.actions.objects[0].candidatesSend && $root.currentLang == 'en'">
        The "{{ vacancy.actions.objects[0].test.testName }}" test was sent to the candidate
        <a href="#/candidates/{{ vacancy.actions.objects[0].candidatesSend[0].localId }}">
            <span ng-if="$root.useAmericanNameStyle" once-text="vacancy.actions.objects[0].candidatesSend[0].fullNameEn"></span>
            <span ng-if="!$root.useAmericanNameStyle" once-text="vacancy.actions.objects[0].candidatesSend[0].fullName"></span>
        </a>
        while being transferred to the
        <span
            ng-if="vacancy.actions.objects[0].customInterviewStates.length > 0"
            class="stage {{ vacancy.actions.objects[0].customInterviewStates[0].stateNew }}"
        >
            {{ vacancy.actions.objects[0].customInterviewStates[0].value }}
        </span>
        <span ng-if="vacancy.actions.objects[0].generalStates.length > 0" class="stage {{ vacancy.actions.objects[0].generalStates[0] }}">
            {{ vacancy.actions.objects[0].generalStates[0] | translate }}
        </span>
        stage for
        <a href="#/vacancies/{{ vacancy.actions.objects[0].vacancy.localId }}">
            <span>{{ vacancy.actions.objects[0].vacancy.position | limitToEllipse : 300 }}</span>
        </a>
        <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" style="color: #828282">({{ vacancy.actions.objects[0].vacancy.clientId.name }})</span>
        vacancy
    </span>
    <span ng-if="vacancy.actions.objects[0].candidatesNotSend && $root.currentLang != 'en'">
        <span>
            {{ 'Test' | translate }} "{{ vacancy.actions.objects[0].test.testName }}"
            {{ 'was not sent to the candidate' | translate }}
            <a href="#/candidates/{{ vacancy.actions.objects[0].candidatesNotSend[0].localId }}">
                <span ng-if="$root.useAmericanNameStyle" once-text="vacancy.actions.objects[0].candidatesNotSend[0].fullNameEn"></span>
                <span ng-if="!$root.useAmericanNameStyle" once-text="vacancy.actions.objects[0].candidatesNotSend[0].fullName"></span>
            </a>
            {{ 'when moving to the stage' | translate }}
        </span>
        <span
            ng-if="vacancy.actions.objects[0].customInterviewStates.length > 0"
            class="stage {{ vacancy.actions.objects[0].customInterviewStates[0].stateNew }}"
        >
            {{ vacancy.actions.objects[0].customInterviewStates[0].value }}
        </span>
        <span ng-if="vacancy.actions.objects[0].generalStates.length > 0" class="stage {{ vacancy.actions.objects[0].generalStates[0] }}">
            {{ vacancy.actions.objects[0].generalStates[0] | translate }}
        </span>
        {{ 'history_info.on_vacancy' | translate }}
        <a href="#/vacancies/{{ vacancy.actions.objects[0].vacancy.localId }}">
            <span>{{ vacancy.actions.objects[0].vacancy.position | limitToEllipse : 300 }}</span>
        </a>
        <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" style="color: #828282">({{ vacancy.actions.objects[0].vacancy.clientId.name }})</span>
        {{ 'due to lack of email' | translate }}
    </span>

    <span ng-if="vacancy.actions.objects[0].candidatesNotSend && $root.currentLang == 'en'">
        <span>
            The "{{ vacancy.actions.objects[0].test.testName }}" test was not sent to the candidate
            <a href="#/candidates/{{ vacancy.actions.objects[0].candidatesNotSend[0].localId }}">
                <span ng-if="$root.useAmericanNameStyle" once-text="vacancy.actions.objects[0].candidatesNotSend[0].fullNameEn"></span>
                <span ng-if="!$root.useAmericanNameStyle" once-text="vacancy.actions.objects[0].candidatesNotSend[0].fullName"></span>
            </a>
            while being transferred to the
            <span
                ng-if="vacancy.actions.objects[0].customInterviewStates.length > 0"
                class="stage {{ vacancy.actions.objects[0].customInterviewStates[0].stateNew }}"
            >
                {{ vacancy.actions.objects[0].customInterviewStates[0].value }}
            </span>
            <span ng-if="vacancy.actions.objects[0].generalStates.length > 0" class="stage {{ vacancy.actions.objects[0].generalStates[0] }}">
                {{ vacancy.actions.objects[0].generalStates[0] | translate }}
            </span>
            stage for
            <a href="#/vacancies/{{ vacancy.actions.objects[0].vacancy.localId }}">
                <span>{{ vacancy.actions.objects[0].vacancy.position | limitToEllipse : 300 }}</span>
            </a>
            <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" style="color: #828282">
                ({{ vacancy.actions.objects[0].vacancy.clientId.name }})
            </span>
            vacancy due to lack of email
        </span>
    </span>
</div>
