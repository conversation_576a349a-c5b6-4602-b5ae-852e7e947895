<div class="col-lg-12">
    <span>
        <span>{{ 'The vacancy' | translate }}</span>
        <a href="#/vacancies/{{ vacancy.actions.objects[0].vacancy.localId }}">
            <span>{{ vacancy.actions.objects[0].vacancy.position | limitToEllipse : 300 }}</span>
        </a>
        <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span>
        <span translate="was published on" translate-values="{portal: 'Work.ua'}"></span>
    </span>
    <br />
</div>
