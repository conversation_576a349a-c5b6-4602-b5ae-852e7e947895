<div class="col-lg-12">
    <span ng-if="vacancy.actions.objects[0].candidate || vacancy.actions.objects[0].candidates.length === 1">
        {{ 'history_info.the_candidate' | translate }}
    </span>
    <span ng-if="$root.currentLang !== 'en' && !vacancy.actions.objects[0].candidate && vacancy.actions.objects[0].candidates.length > 1">
        {{ 'history_info.candidates' | translate }} ({{ vacancy.actions.objects[0].candidates.length }})
    </span>
    <span ng-if="$root.currentLang === 'en' && !vacancy.actions.objects[0].candidate && vacancy.actions.objects[0].candidates.length > 1">
        The candidates ({{ vacancy.actions.objects[0].candidates.length }})
    </span>

    <span>
        <span ng-if="vacancy.actions.objects[0].candidate && !vacancy.actions.objects[0].candidates">
            <a href="#/candidates/{{ vacancy.actions.objects[0].candidate.localId }}">
                <span ng-if="!$root.useAmericanNameStyle">
                    <span ng-if="!$root.useAmericanNameStyle">{{ vacancy.actions.objects[0].candidate.fullName }}</span>
                    <span ng-if="$root.useAmericanNameStyle">
                        {{ vacancy.actions.objects[0].candidate.fullNameEn }}
                    </span>
                </span>
                <span ng-if="$root.useAmericanNameStyle">{{ vacancy.actions.objects[0].candidate.fullNameEn }}</span>
            </a>
        </span>
        <span ng-if="!vacancy.actions.objects[0].candidate && vacancy.actions.objects[0].candidates.length === 1">
            <a href="#/candidates/{{ vacancy.actions.objects[0].candidates[0].localId }}">
                <span ng-if="!$root.useAmericanNameStyle">
                    <span ng-if="!$root.useAmericanNameStyle">
                        {{ vacancy.actions.objects[0].candidates[0].fullName }}
                    </span>
                    <span ng-if="$root.useAmericanNameStyle">
                        {{ vacancy.actions.objects[0].candidates[0].fullNameEn }}
                    </span>
                </span>
                <span ng-if="$root.useAmericanNameStyle">
                    {{ vacancy.actions.objects[0].candidates[0].fullNameEn }}
                </span>
            </a>
        </span>

        <span ng-if="!vacancy.actions.objects[0].candidates || vacancy.actions.objects[0].candidates.length === 1">
            {{ 'history_info.added_in' | translate }}
        </span>
        <span ng-if="vacancy.actions.objects[0].candidates.length > 1">{{ 'history_info.addeds' | translate }}</span>

        <span ng-if="$root.currentLang === 'en'">the</span>
        <span ng-show="!vacancy.actions.objects[0].stateNewName" class="stage {{ vacancy.actions.objects[0].stateNew }}">
            {{ vacancy.actions.objects[0].stateNew | translate }}
        </span>
        <span ng-show="vacancy.actions.objects[0].stateNewName" class="stage {{ vacancy.actions.objects[0].stateNew }}">
            <span ng-if="vacancy.actions.objects[0].customStateNew">{{ vacancy.actions.objects[0].stateNewName }}</span>
            <span ng-if="!vacancy.actions.objects[0].customStateNew">{{ vacancy.actions.objects[0].stateNewName | translate }}</span>
        </span>
        <span ng-if="$root.currentLang !== 'en'">{{ 'history_info.in_vacancy' | translate }}</span>
        <span ng-if="$root.currentLang === 'en'">stage of the</span>

        <a class="user" href="#/vacancies/{{ vacancy.localId }}">
            <span once-text="vacancy.position |limitToEllipse:300"></span>
        </a>
        <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span>
    </span>

    <span ng-if="$root.currentLang === 'en'">vacancy</span>

    <span ng-show="vacancy.actions.objects[0].targetDate">
        <br />
        {{ 'interview_times' | translate }}:
        {{ vacancy.actions.objects[0].targetDate | dateFormatSimpleExcelHistory : true | translate }}
    </span>

    <span ng-show="history.descr || vacancy.actions.objects[0].descr">
        <br />
        {{ 'comment' | translate }}:
        <br />

        <span
            ng-if="vacancy.actions.objects[0].descrLength <= 69"
            class="stylesForTags clear-link-line-height"
            ng-bind-html="vacancy.actions.objects[0].descr | textForNotice:false"
        ></span>

        <span ng-if="vacancy.actions.objects[0].descrLength >= 70">
            <span
                ng-if="!vacancy.actions.objects[0].readMore"
                class="stylesForTags"
                ng-bind-html="vacancy.actions.objects[0].cutDescr | textForNotice:false"
            ></span>
            <span
                ng-if="vacancy.actions.objects[0].readMore"
                class="stylesForTags"
                ng-bind-html="vacancy.actions.objects[0].descr | textForNotice:false"
            ></span>
        </span>

        <span ng-if="vacancy.actions.objects[0].descrLength > 70">
            <span ng-if="!vacancy.actions.objects[0].readMore" class="arrow-down-read-more">
                <img
                    ng-click="$root.changeMoreAndLess(vacancy.actions.objects[0]); changeActionsBlockHeight('more')"
                    src="/images/arrow-down.svg"
                    alt="down-arrow"
                />
            </span>
        </span>

        <div ng-if="vacancy.actions.objects[0].descrLength > 70">
            <span ng-if="vacancy.actions.objects[0].readMore" class="arrow-up-read-more">
                <img
                    ng-click="$root.changeMoreAndLess(vacancy.actions.objects[0]); changeActionsBlockHeight('less')"
                    src="/images/arrow-up.svg"
                    alt="down-arrow"
                />
            </span>
        </div>
    </span>
</div>
