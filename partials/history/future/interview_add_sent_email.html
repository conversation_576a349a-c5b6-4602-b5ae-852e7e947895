<div class="col-lg-12">
    <span ng-if="!vacancy.actions.objects[0].candidateIds">
        <span>{{ 'history_info.the_candidate' | translate }}</span>
    </span>

    <a href="#/candidates/{{ vacancy.actions.objects[0].candidate.localId }}">
        <span ng-if="!$root.useAmericanNameStyle">{{ vacancy.actions.objects[0].candidate.fullName }}</span>
        <span ng-if="$root.useAmericanNameStyle">{{ vacancy.actions.objects[0].candidate.fullNameEn }}</span>
    </a>

    <span class="greyColor">{{ 'history_info.added_in' | translate }}</span>
    <span ng-if="$root.currentLang === 'en'">the</span>
    <span ng-if="vacancy.actions.objects[0].stateNewName" class="stage {{ vacancy.actions.objects[0].stateNew }}">
        <span ng-if="vacancy.actions.objects[0].customStateNew">{{ vacancy.actions.objects[0].stateNewName }}</span>
        <span ng-if="!vacancy.actions.objects[0].customStateNew">{{ vacancy.actions.objects[0].stateNewName | translate }}</span>
    </span>
    <span ng-if="!vacancy.actions.objects[0].stateNewName" class="stage {{ vacancy.actions.objects[0].stateNew }}">
        {{ vacancy.actions.objects[0].stateNew | translate }}
    </span>

    <span ng-if="$root.currentLang !== 'en'">{{ 'history_info.in_vacancy' | translate }}</span>
    <span ng-if="$root.currentLang === 'en'">stage of the</span>

    <!-- prettier-ignore -->
    <span>
        <a class="user" href="#/vacancies/{{ vacancy.localId }}"><span once-text="vacancy.position"></span></a><span ng-if="$root.currentLang !== 'en' && $root.me.personParams.clientAccessLevel === 'hide'">.</span>
        <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span><span ng-if="$root.currentLang !== 'en' && $root.me.personParams.clientAccessLevel !== 'hide'">.</span>
    </span>

    <span ng-if="$root.currentLang === 'en'">vacancy</span>

    <span ng-if="$root.currentLang !== 'en'">
        {{ 'history_info.The email has been sent to the candidate' | translate }}
    </span>

    <span ng-if="$root.currentLang === 'en'">in combination with the email sent</span>

    <br />
    <span ng-show="vacancy.actions.objects[0].targetDate">
        {{ 'interview_times' | translate }}:
        {{ vacancy.actions.objects[0].targetDate | dateFormatSimpleExcelHistory : true | translate }}
    </span>

    <span ng-show="history.descr || vacancy.actions.objects[0].descr">
        <br />
        {{ 'comment' | translate }}:
        <br />
        <span ng-bind-html="history.descr|textForNotice:false"></span>
    </span>
    <span
        ng-if="vacancy.actions.objects[0].descrLength <= 69"
        class="stylesForTags"
        ng-bind-html="vacancy.actions.objects[0].descr | textForNotice:false"
    ></span>

    <span ng-if="vacancy.actions.objects[0].descrLength >= 70">
        <span
            ng-if="!vacancy.actions.objects[0].readMore"
            class="stylesForTags"
            ng-bind-html="vacancy.actions.objects[0].cutDescr | textForNotice:false"
        ></span>
        <span ng-if="vacancy.actions.objects[0].readMore" class="stylesForTags" ng-bind-html="vacancy.actions.objects[0].descr | textForNotice:false"></span>
    </span>

    <span ng-if="vacancy.actions.objects[0].descrLength > 70">
        <span ng-if="!vacancy.actions.objects[0].readMore" class="arrow-down-read-more">
            <img ng-click="$root.changeMoreAndLess(vacancy.actions.objects[0])" src="/images/arrow-down.svg" alt="down-arrow" />
        </span>
    </span>

    <div ng-if="vacancy.actions.objects[0].descrLength > 70">
        <span ng-if="vacancy.actions.objects[0].readMore" class="arrow-up-read-more">
            <img ng-click="$root.changeMoreAndLess(vacancy.actions.objects[0])" src="/images/arrow-up.svg" alt="down-arrow" />
        </span>
    </div>
</div>
