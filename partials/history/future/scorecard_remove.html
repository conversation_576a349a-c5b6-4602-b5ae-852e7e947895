<div class="col-lg-12">
    <span>{{ 'Score card' | translate }}</span>
    <span>{{ vacancy.actions.objects[0].descr }}</span>
    <span>{{ 'was deleted from vacancy' | translate }}</span>

    <a class="user" href="#/vacancies/{{ vacancy.localId }}">
        <span once-text="vacancy.position | limitToEllipse:300"></span>
    </a>
    <span class="client">({{ vacancy.clientId.name }})</span>

    <span ng-if="$root.currentLang === 'en'">vacancy</span>
</div>
