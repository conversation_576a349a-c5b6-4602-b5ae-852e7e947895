<div class="col-lg-12">
    {{ 'candidates' | translate }}
    <span ng-show="$root.currentLang == 'ru' || $root.currentLang == 'ua' || $root.currentLang == 'pl'">{{ 'history_info.in_vacancy' | translate }}</span>
    <span ng-show="$root.currentLang == 'en'">from vacancy</span>
    <span class="comma">
        <span ng-repeat="candidate in history.candidates">
            <a href="#/candidates/{{ vacancy.actions.objects[0].candidate.localId }}">
                <span ng-if="!$root.useAmericanNameStyle">{{ vacancy.actions.objects[0].candidate.fullName }}</span>
                <span ng-if="$root.useAmericanNameStyle">{{ vacancy.actions.objects[0].candidate.fullNameEn }}</span>
            </a>
        </span>
    </span>
    <a href="#/vacancies/{{ vacancy.localId }}">{{ vacancy.position | limitToEllipse : 250 }}</a>
    <span ng-show="$root.currentLang !== 'pl'">{{ 'were sent to the Client' | translate }}</span>
    <span ng-show="$root.currentLang === 'pl'">zostali wysłani do klienta</span>
    <span class="client">({{ vacancy.clientId.name }})</span>
    <span ng-show="$root.currentLang !== 'pl'">{{ 'email us at' | translate }}</span>
    <span ng-show="$root.currentLang === 'pl'">z e-mail</span>
    <span class="comma">
        <span ng-repeat="mail in (vacancy.actions.objects[0].descr|stringToJson)">{{ mail }}</span>
    </span>
</div>
