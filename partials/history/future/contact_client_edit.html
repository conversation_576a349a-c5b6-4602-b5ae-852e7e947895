<div class="col-lg-12">
    <a class="user" href="#/vacancies/{{ vacancy.localId }}">
        <span once-text="vacancy.position |limitToEllipse:300"></span>
    </a>
    <span class="coma">, {{ vacancy.clientId.name }},</span>
    <span ng-switch="vacancy.actions.objects[0].stateNew">
        <span ng-switch-when="R">{{ 'history_info.deleted_contact' | translate }}</span>
        <span ng-switch-default>{{ 'history_info.edited_contact' | translate }}</span>
    </span>
</div>
