<div ng-if="$root.currentLang !== 'en'" class="col-lg-12">
    {{ 'vacancy change status' | translate }}
    <a class="user" href="#/vacancies/{{ vacancy.localId }}">
        <span once-text="vacancy.position |limitToEllipse:300"></span>
    </a>
    <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span>

    <span class="status  passed {{ vacancy.actions.objects[0].stateOld }}">
        {{ vacancy.actions.objects[0].stateOld | translate }}
    </span>
    <i class="fa fa-angle-right" aria-hidden="true"></i>
    <span class="status {{ vacancy.actions.objects[0].stateNew }}">
        {{ vacancy.actions.objects[0].stateNew | translate }}
    </span>
    <span ng-show="history.descr || vacancy.actions.objects[0].descr">
        <br />
        {{ 'history_info.message' | translate }}:
        <br />
        <span ng-bind-html="(history.descr|translateDescr).trim()|textForNotice:false"></span>

        <span
            ng-if="vacancy.actions.objects[0].descrLength <= 69"
            class="stylesForTags"
            ng-bind-html="vacancy.actions.objects[0].descr | textForNotice:false"
        ></span>

        <span ng-if="vacancy.actions.objects[0].descrLength >= 70">
            <span
                ng-if="!vacancy.actions.objects[0].readMore"
                class="stylesForTags"
                ng-bind-html="vacancy.actions.objects[0].cutDescr | textForNotice:false"
            ></span>
            <span
                ng-if="vacancy.actions.objects[0].readMore"
                class="stylesForTags"
                ng-bind-html="vacancy.actions.objects[0].descr | textForNotice:false"
            ></span>
        </span>

        <span ng-if="vacancy.actions.objects[0].descrLength > 70">
            <span ng-if="!vacancy.actions.objects[0].readMore" class="arrow-down-read-more">
                <img
                    ng-click="$root.changeMoreAndLess(vacancy.actions.objects[0]); changeActionsBlockHeight('more')"
                    src="/images/arrow-down.svg"
                    alt="down-arrow"
                />
            </span>
        </span>

        <div ng-if="vacancy.actions.objects[0].descrLength > 70">
            <span ng-if="vacancy.actions.objects[0].readMore" class="arrow-up-read-more">
                <img
                    ng-click="$root.changeMoreAndLess(vacancy.actions.objects[0]); changeActionsBlockHeight('less')"
                    src="/images/arrow-up.svg"
                    alt="down-arrow"
                />
            </span>
        </div>
    </span>
</div>
<div ng-if="$root.currentLang === 'en'" class="col-lg-12">
    The status of the
    <a class="user" href="#/vacancies/{{ vacancy.localId }}">
        <span once-text="vacancy.position |limitToEllipse:300"></span>
    </a>
    <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span>
    vacancy has been changed

    <span class="status  passed {{ vacancy.actions.objects[0].stateOld }}">
        {{ vacancy.actions.objects[0].stateOld | translate }}
    </span>
    <i class="fa fa-angle-right" aria-hidden="true"></i>
    <span class="status {{ vacancy.actions.objects[0].stateNew }}">
        {{ vacancy.actions.objects[0].stateNew | translate }}
    </span>
</div>
