<div ng-if="$root.currentLang !== 'en'" class="col-lg-12">
    {{ 'history_info.removed_file' | translate }}
    <span style="color: #daa10f">{{ vacancy.actions.objects[0].descr | fileNameCut : 0 : 15 }}</span>
    {{ 'history_info.in_vacancy' | translate }}
    <a class="user" href="#/vacancies/{{ vacancy.localId }}">
        <span once-text="vacancy.position |limitToEllipse:300"></span>
    </a>
    <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span>
</div>
<div ng-if="$root.currentLang === 'en'" class="col-lg-12">
    The file
    <span style="color: #daa10f">{{ vacancy.actions.objects[0].descr | fileNameCut : 0 : 15 }}</span>
    was removed from the
    <a class="user" href="#/vacancies/{{ vacancy.localId }}">
        <span once-text="vacancy.position |limitToEllipse:300"></span>
    </a>
    <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span>
    vacancy
</div>
