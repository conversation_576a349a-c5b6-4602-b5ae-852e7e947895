<div class="col-lg-12 margin-top interview_add">
    <span ng-if="!vacancy.actions.objects[0].candidates && vacancy.actions.objects[0].candidate">
        {{ 'history_info.the_candidate' | translate }}
        <a href="#/candidates/{{ vacancy.actions.objects[0].candidate.localId }}">
            <span ng-if="!$root.useAmericanNameStyle">{{ vacancy.actions.objects[0].candidate.fullName }}</span>
            <span ng-if="$root.useAmericanNameStyle">{{ vacancy.actions.objects[0].candidate.fullNameEn }}</span>
        </a>
        {{ 'history_info.added_in' | translate }}
        <span ng-if="$root.currentLang === 'en'">the</span>
        <span ng-show="!vacancy.actions.objects[0].stateNewName" class="stage {{ vacancy.actions.objects[0].stateNew }}">
            {{ vacancy.actions.objects[0].stateNew | translate }}
        </span>
        <span ng-if="$root.currentLang !== 'en'">{{ 'history_info.in_vacancy' | translate }}</span>
        <span ng-if="$root.currentLang === 'en'">stage of the</span>

        <a href="#/vacancies/{{ vacancy.localId }}">
            <span>{{ vacancy.position | limitToEllipse : 300 }}</span>
        </a>
        <span class="client">({{ vacancy.clientId.name }})</span>
        <span ng-if="$root.currentLang === 'en'">vacancy</span>
        {{ 'was_add_from_hh' | translate }}
    </span>

    <span ng-if="vacancy.actions.objects[0].candidates.length == 1">
        {{ 'history_info.the_candidate' | translate }}
        <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ vacancy.actions.objects[0].candidates[0].localId }}">
            {{ vacancy.actions.objects[0].candidates[0].fullName }}
        </a>
        <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ vacancy.actions.objects[0].candidates[0].localId }}">
            {{ vacancy.actions.objects[0].candidates[0].fullNameEn }}
        </a>
        {{ 'history_info.added_in' | translate }}
        <span ng-show="!vacancy.actions.objects[0].stateNewName" class="stage {{ vacancy.actions.objects[0].stateNew }}">
            {{ vacancy.actions.objects[0].stateNew | translate }}
        </span>
        <span ng-if="$root.currentLang !== 'en'">{{ 'history_info.in_vacancy' | translate }}</span>
        <span ng-if="$root.currentLang === 'en'">stage of the</span>
        <a href="#/vacancies/{{ vacancy.localId }}">
            <span>{{ vacancy.position | limitToEllipse : 300 }}</span>
        </a>
        <span class="client">({{ vacancy.clientId.name }})</span>
        <span ng-if="$root.currentLang === 'en'">vacancy</span>
        {{ 'was_add_from_workua' | translate }}
    </span>

    <span ng-if="vacancy.actions.objects[0].candidates.length == 2">
        {{ 'history_info.candidates' | translate }}
        <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ vacancy.actions.objects[0].candidates[0].localId }}">
            {{ vacancy.actions.objects[0].candidates[0].fullName }}
        </a>
        <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ vacancy.actions.objects[0].candidates[0].localId }}">
            {{ vacancy.actions.objects[0].candidates[0].fullNameEn }}
        </a>
        {{ 'and' | translate }}
        <a ng-if="!$root.useAmericanNameStyle" href="#/candidates/{{ vacancy.actions.objects[0].candidates[1].localId }}">
            {{ vacancy.actions.objects[0].candidates[1].fullName }}
        </a>
        <a ng-if="$root.useAmericanNameStyle" href="#/candidates/{{ vacancy.actions.objects[0].candidates[1].localId }}">
            {{ vacancy.actions.objects[0].candidates[1].fullNameEn }}
        </a>
        {{ 'history_info.addeds' | translate }}
        <span ng-show="!vacancy.actions.objects[0].stateNewName" class="stage {{ vacancy.actions.objects[0].stateNew }}">
            {{ vacancy.actions.objects[0].stateNew | translate }}
        </span>
        {{ 'history_info.in_vacancy' | translate }}
        <a href="#/vacancies/{{ vacancy.localId }}">
            <span>{{ vacancy.position | limitToEllipse : 300 }}</span>
        </a>
        {{ 'was_add_from_workua' | translate }}
    </span>

    <span ng-if="vacancy.actions.objects[0].candidates.length > 2">
        {{ 'history_info.candidates' | translate }}
        (
        <span>{{ vacancy.actions.objects[0].candidates.length }}</span>
        )
        {{ 'history_info.addeds' | translate }}
        <span ng-show="!vacancy.actions.objects[0].stateNewName" class="stage {{ vacancy.actions.objects[0].stateNew }}">
            {{ vacancy.actions.objects[0].stateNew | translate }}
        </span>
        {{ 'history_info.stage_hist' | translate }}
        {{ 'history_info.in_vacancy' | translate }}
        <a href="#/vacancies/{{ vacancy.localId }}">
            <span>{{ vacancy.position | limitToEllipse : 300 }}</span>
        </a>
        {{ 'was_add_from_workua' | translate }}
    </span>
    <span ng-if="history.candidates.length > 2" class="manyCandidates">
        <div ng-if="history.showAllCandidates">
            <span class="menuCandidate" ng-repeat="candidate in history.candidates">
                <a ng-if="$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                    {{ candidate.fullNameEn }}
                    <span class="comma">{{ $last ? '' : ', ' }}</span>
                </a>
                <a ng-if="!$root.useAmericanNameStyle" ng-href="#/candidates/{{ candidate.localId }}" title="{{ candidate.fullName }}">
                    {{ candidate.fullName }}
                    <span class="comma">{{ $last ? '' : ', ' }}</span>
                </a>
            </span>
        </div>
    </span>
</div>
