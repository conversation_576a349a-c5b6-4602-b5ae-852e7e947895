<div ng-if="$root.currentLang != 'en'" class="col-lg-12">
    {{ 'The automatic test sending at the' | translate }}
    <span
        ng-if="vacancy.actions.objects[0].generalStates.length > 0"
        class="stage {{ vacancy.actions.objects[0].generalStates[0] }}"
        style="display: inline-flex"
    >
        {{ vacancy.actions.objects[0].generalStates[0] | translate }}
    </span>
    <span ng-if="vacancy.actions.objects[0].customInterviewStates.length > 0" class="stage {{ vacancy.actions.objects[0].customInterviewStates[0].stateNew }}">
        {{ vacancy.actions.objects[0].customInterviewStates[0].value }}
    </span>
    <span ng-if="vacancy.actions.objects[0].vacancy">
        {{ 'history_info.on_vacancy' | translate }}
        <a href="#/vacancies/{{ vacancy.actions.objects[0].vacancy.localId }}">
            <span>{{ vacancy.actions.objects[0].vacancy.position | limitToEllipse : 300 }}</span>
        </a>
        <a
            ng-if="$root.me.personParams.clientAccessLevel !== 'hide'"
            style="color: #828282"
            href="#/clients/{{ vacancy.actions.objects[0].vacancy.clientId.localId }}"
        >
            <span>({{ vacancy.actions.objects[0].vacancy.clientId.name }})</span>
        </a>
    </span>
    <span ng-if="!vacancy.actions.objects[0].vacancy">
        {{ 'for all vacancies' | translate }}
    </span>
    {{ 'was canceled' | translate }}
</div>
<div ng-if="$root.currentLang == 'en'" class="col-lg-12">
    The automatic test sending at the
    <span
        ng-if="vacancy.actions.objects[0].generalStates.length > 0"
        class="stage {{ vacancy.actions.objects[0].generalStates[0] }}"
        style="display: inline-flex"
    >
        {{ vacancy.actions.objects[0].generalStates[0] | translate }}
    </span>
    <span ng-if="vacancy.actions.objects[0].customInterviewStates.length > 0" class="stage {{ vacancy.actions.objects[0].customInterviewStates[0].stateNew }}">
        {{ vacancy.actions.objects[0].customInterviewStates[0].value }}
    </span>
    stage for
    <span ng-if="vacancy.actions.objects[0].vacancy">
        <a href="#/vacancies/{{ vacancy.actions.objects[0].vacancy.localId }}">
            <span>{{ vacancy.actions.objects[0].vacancy.position | limitToEllipse : 300 }}</span>
        </a>
        <a
            ng-if="$root.me.personParams.clientAccessLevel !== 'hide'"
            style="color: #828282"
            href="#/clients/{{ vacancy.actions.objects[0].vacancy.clientId.localId }}"
        >
            <span>({{ vacancy.actions.objects[0].vacancy.clientId.name }})</span>
        </a>
        vacancy
    </span>
    <span ng-if="!vacancy.actions.objects[0].vacancy">
        {{ 'for all vacancies' | translate }}
    </span>
    was canceled
</div>
