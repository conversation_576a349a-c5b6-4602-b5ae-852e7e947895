<div ng-if="$root.currentLang !== 'en'" class="col-lg-12">
    {{ 'Task status' | translate }}
    "{{ vacancy.actions.objects[0].task.title }}"

    <span ng-if="$root.currentLang !== 'en'">{{ 'history_info.in_vacancy' | translate }}</span>

    <a class="user" href="#/vacancies/{{ vacancy.localId }}">
        <span once-text="vacancy.position |limitToEllipse:300"></span>
    </a>
    <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span>

    {{ 'changed' | translate }}

    <span class="status  passed {{ vacancy.actions.objects[0].stateOld }}">
        {{ vacancy.actions.objects[0].stateOld | translate }}
    </span>
    <i class="fa fa-angle-right" aria-hidden="true"></i>
    <span class="status {{ vacancy.actions.objects[0].stateNew }}">
        {{ vacancy.actions.objects[0].stateNew | translate }}
    </span>
</div>
<div ng-if="$root.currentLang === 'en'" class="col-lg-12">
    The status of the "{{ vacancy.actions.objects[0].task.title }}" task for the
    <a class="user" href="#/vacancies/{{ vacancy.localId }}">
        <span once-text="vacancy.position |limitToEllipse:300"></span>
    </a>
    <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span>
    vacancy has been changed
    <span class="status  passed {{ vacancy.actions.objects[0].stateOld }}">
        {{ vacancy.actions.objects[0].stateOld | translate }}
    </span>
    <i class="fa fa-angle-right" aria-hidden="true"></i>
    <span class="status {{ vacancy.actions.objects[0].stateNew }}">
        {{ vacancy.actions.objects[0].stateNew | translate }}
    </span>
</div>
