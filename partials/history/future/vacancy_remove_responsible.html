<div class="col-lg-12">
    <a
        ng-if="$root.useAmericanNameStyle"
        href="#/users/{{ vacancy.actions.objects[0].targetPerson.userId }}"
        once-text="vacancy.actions.objects[0].targetPerson.fullNameEn"
    ></a>
    <a
        ng-if="!$root.useAmericanNameStyle"
        href="#/users/{{ vacancy.actions.objects[0].targetPerson.userId }}"
        once-text="vacancy.actions.objects[0].targetPerson.fullName"
    ></a>

    <span ng-if="$root.currentLang !== 'en'">{{ 'history_info.remove2' | translate }} {{ 'history_info.from_responsible_in_vacancy' | translate }}</span>
    <span ng-if="$root.currentLang === 'en'">removed from responsible in the</span>

    <a class="user" href="#/vacancies/{{ vacancy.localId }}">
        <span once-text="vacancy.position |limitToEllipse:300"></span>
    </a>
    <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span>
    <span ng-if="$root.currentLang === 'en'">vacancy</span>
    <span ng-show="history.descr || vacancy.actions.objects[0].descr">
        <br />
        {{ 'history_info.message' | translate }}:
        <br />
        <span ng-bind-html="(history.descr|translateDescr).trim()|textForNotice:false"></span>

        <span
            ng-if="vacancy.actions.objects[0].descrLength <= 69"
            class="stylesForTags"
            ng-bind-html="vacancy.actions.objects[0].descr | textForNotice:false"
        ></span>

        <span ng-if="vacancy.actions.objects[0].descrLength >= 70">
            <span
                ng-if="!vacancy.actions.objects[0].readMore"
                class="stylesForTags"
                ng-bind-html="vacancy.actions.objects[0].cutDescr | textForNotice:false"
            ></span>
            <span
                ng-if="vacancy.actions.objects[0].readMore"
                class="stylesForTags"
                ng-bind-html="vacancy.actions.objects[0].descr | textForNotice:false"
            ></span>
        </span>

        <span ng-if="vacancy.actions.objects[0].descrLength > 70">
            <span ng-if="!vacancy.actions.objects[0].readMore" class="arrow-down-read-more">
                <img
                    ng-click="$root.changeMoreAndLess(vacancy.actions.objects[0]); changeActionsBlockHeight('more')"
                    src="/images/arrow-down.svg"
                    alt="down-arrow"
                />
            </span>
        </span>

        <div ng-if="vacancy.actions.objects[0].descrLength > 70">
            <span ng-if="vacancy.actions.objects[0].readMore" class="arrow-up-read-more">
                <img
                    ng-click="$root.changeMoreAndLess(vacancy.actions.objects[0]); changeActionsBlockHeight('less')"
                    src="/images/arrow-up.svg"
                    alt="down-arrow"
                />
            </span>
        </div>
    </span>
</div>
