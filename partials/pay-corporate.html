<div class="corporate-payment">
    <div ng-show="!$root.loading && ($root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter')" class="payment">
        <div class="container-fluid wrapper">
            <page-intro-component page-title="'Payment page'"></page-intro-component>

            <section class="payment__acc-info">
                <div ng-show="tarif !== 'free'" class="info__item">
                    <span class="into__title" translate="Your account is active until"></span>
                    <span class="info__value">
                        {{ $root.companyParams.paidTillDate }}
                    </span>
                </div>
                <div ng-show="tarif !== 'free' && tarif !== 'corporate'" class="info__item">
                    <span class="into__title" translate="Paid users amount"></span>
                    <span class="info__value">{{ paidUsers.length }}</span>
                </div>
                <div class="info__item">
                    <span class="into__title" translate="Current tariff"></span>
                    <span class="info__value">{{ tarif | paymentTariff }}</span>
                </div>
            </section>

            <section id="section-pay" ng-show="showFreeTariffPayment || tarif !== 'free'" class="payment__pay">
                <div class="section__intro">
                    <h4 class="form-title-main" translate="Pay for cleverstaff usage"></h4>
                </div>
                <span class="text" translate="Scale"></span>
                <div class="scale_block">
                    <div class="scale_block-bonuses">
                        <div class="scale_block-bonuses-percents">
                            <div class="scale_block-bonuses-percents-perc" ng-style="{ color: colors.zero }">0%</div>
                            <div class="scale_block-bonuses-percents-perc" ng-style="{ color: colors.ten }">10%</div>
                            <div class="scale_block-bonuses-percents-perc mr" ng-style="{ color: colors.fifteen }">15%</div>
                            <div class="scale_block-bonuses-percents-perc" ng-style="{ color: colors.twenty }">20%</div>
                        </div>
                        <div ng-click="setSlider($event)" class="scale_block-bonuses-progress_bar">
                            <div class="scale_block-bonuses-progress_bar-nick25"></div>
                            <div class="scale_block-bonuses-progress_bar-nick"></div>
                            <div class="scale_block-bonuses-progress_bar-filled" ng-style="{ width: myWidth }">
                                <span id="slider" class="scale_block-bonuses-progress_bar-filled-slider"></span>
                            </div>
                        </div>
                        <div class="months">
                            <div class="month month1">
                                1-5
                                <span translate="Months_1"></span>
                            </div>
                            <div class="month month6">
                                6
                                <span translate="Months_1"></span>
                            </div>
                            <div class="month month12">
                                12
                                <span translate="Months_1"></span>
                            </div>
                            <div class="month month24">
                                24
                                <span translate="Months_2"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="section__intro">
                    <p
                        ng-if="isReferralDiscount"
                        class="intro__descr accent fixed-height referral"
                        translate="Pay till {discount_end_date} and receive a 10% discount"
                        translate-values="{discountEndDate: getReferralDiscountEndDate}"
                    ></p>
                </div>
                <div class="pay-data__wrapper">
                    <div class="pay__data">
                        <div class="data__item margin-xs">
                            <span class="title" translate="Users_1"></span>
                            <div class="data">
                                <span ng-show="tarif === 'corporate'" class="bordered" translate="up to 25"></span>
                            </div>
                        </div>
                        <div class="data__item margin-xs">
                            <span class="title" translate="Months_1"></span>
                            <div class="data">
                                <custom-select-new options="months" model="payment.countMonth"></custom-select-new>
                                <i
                                    ng-if="hintPayment"
                                    class="info-icon"
                                    style="position: relative; left: 20px; height: 15px !important"
                                    tooltip-placement="bottom-right"
                                    tooltip-class="tooltip-outer"
                                    uib-tooltip="{{ 'Minimum months to pay - 3' | translate }}"
                                ></i>
                                <i
                                    ng-if="!hintPayment && payment.countPeople === 1"
                                    class="info-icon"
                                    tooltip-placement="bottom-right"
                                    tooltip-class="tooltip-outer"
                                    uib-tooltip="{{ 'Minimum months to pay - 2' | translate }}"
                                ></i>
                            </div>
                        </div>
                        <div class="data__item">
                            <span ng-if="tarif !== 'corporate'" class="title border-xs" translate="Bonus"></span>
                            <span ng-if="tarif === 'corporate'" class="title border-xs" translate="Discount"></span>
                            <div class="data">
                                <span class="bonus">{{ bonus }}%</span>
                                <img ng-if="tarif !== 'corporate'" style="width: 24px; height: 24px" src="/images/sprite/referral-program/giftbox.svg" />
                            </div>
                        </div>
                        <div class="data__item center-xs">
                            <span class="title border-xs" translate="Total"></span>
                            <div class="data">
                                <span class="total">
                                    <span class="discount-part">
                                        <span class="bold">10%</span>
                                        {{ 'discount' | translate }}
                                    </span>
                                    <span ng-if="!isReferralDiscount" class="price">{{ price }} USD</span>
                                    <span ng-if="isReferralDiscount" class="price">{{ price - price * 0.1 }} USD</span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div ng-if="!isReferralDiscount" class="total-info">
                        <span
                            ng-if="tarif !== 'corporate'"
                            translate="Will be credited total USD (bonus USD)"
                            translate-values="{total: priceWithBonus, bonus: bonusAmount}"
                        ></span>
                    </div>
                    <div ng-if="isReferralDiscount" class="total-info">
                        <span
                            ng-if="tarif !== 'corporate'"
                            translate="Will be credited total USD (bonus USD and discount)"
                            translate-values="{total: priceWithBonus, bonus: bonusAmount, discount: 10}"
                        ></span>
                    </div>
                </div>
                <div class="pay__types">
                    <a class="pay__type">
                        <span ng-click="peopleModalShow ? freePaidUsers('pay_card') : $root.payWayForPayClick()" translate="Pay by card"></span>
                        <div class="type__image">
                            <img src="/images/sprite/visa-mastercard-block.png" />
                        </div>
                    </a>
                    <a ng-click="peopleModalShow ? freePaidUsers('pay_invoice') : $root.goToInvoice()" class="pay__type invoice" href="">
                        <span translate="Pay by invoice"></span>
                        <div class="type__image invoice-payment">
                            <img src="/images/sprite/invoice-payment.jpg" />
                        </div>
                    </a>
                </div>
                <div class="pay_faq">
                    <span translate="Payment FAQ"></span>
                </div>
                <img class="security" style="width: 112px; height: 33px" src="/images/sprite/referral-program/SSl.svg" />
            </section>

            <section ng-show="tarif !== 'free'" class="payment__history" ng-class="{ 'no-borders': !paymentHistory.payment && !paymentHistory.transitions }">
                <div class="payment__toggle" ng-class="{ 'align-left': tarif === 'corporate' }">
                    <div class="history-toggle">
                        <label translate="Payment history" for="payment"></label>
                        <div class="toggle-content">
                            <label ng-click="paymentHistory.payment = false" ng-class="{ active: !paymentHistory.payment }" translate="Off"></label>
                            <input
                                id="payment"
                                class="cmn-toggle cmn-toggle-round"
                                ng-change="togglePaymentHistory({payment: true})"
                                ng-checked="paymentHistory.payment"
                                type="checkbox"
                                ng-model="paymentHistory.payment"
                            />
                            <label class="sides-margin" for="payment"></label>
                            <label
                                ng-click="paymentHistory.payment = true;paymentHistory.transitions = false"
                                ng-class="{ active: paymentHistory.payment }"
                                translate="On"
                            ></label>
                        </div>
                    </div>
                </div>
                <div ng-show="paymentHistory.payment || paymentHistory.transitions" class="section__intro center">
                    <h4
                        ng-show="paymentHistory.payment && payments.length"
                        class="form-title-main"
                        style="font-weight: normal; font-size: 18px"
                        translate="Your payment history"
                    ></h4>
                    <h4 ng-show="paymentHistory.transitions && expenses.length" class="form-title-main" translate="Your transition history"></h4>
                    <h4
                        ng-show="paymentHistory.payment && !payments.length"
                        class="form-title-main"
                        style="font-weight: normal; font-size: 18px"
                        translate="You didn't make any payments yet"
                    ></h4>
                    <h4 ng-show="paymentHistory.transitions && !expenses.length" class="form-title-main" translate="You didn't make any transitions yet"></h4>
                </div>
                <div ng-show="(paymentHistory.payment && payments.length) || (paymentHistory.transitions && expenses.length)" class="history-data">
                    <table ng-show="paymentHistory.payment && payments.length > 0" class="payment-history">
                        <thead>
                            <tr>
                                <th translate="Payment created"></th>
                                <th translate="Payment-received"></th>
                                <th translate="Sum"></th>
                                <th translate="Purpose"></th>
                            </tr>
                            <tr
                                ng-class="{ lastPayment: $index == 0 && fromPayment == true }"
                                ng-repeat="payment in payments | orderBy:'-dc'"
                                hgtablecollspan="7"
                            >
                                <td>{{ (payment.dc | dateFormatSimple2 : true).trim() }}</td>
                                <td>
                                    <span
                                        ng-if="
                                            payment.status === 'success' ||
                                            payment.status === 'Approved' ||
                                            payment.status === 'sandbox' ||
                                            payment.status === 'SALE' ||
                                            payment.status === 'Approved (Regular payment)'
                                        "
                                    >
                                        <i class="fa fa-check" aria-hidden="true"></i>
                                        {{ (payment.dm | dateFormatSimple2 : true).trim() }}
                                    </span>
                                    <div
                                        ng-if="
                                            payment.status != 'success' &&
                                            payment.status != 'Approved' &&
                                            payment.status != 'sandbox' &&
                                            payment.status != 'SALE' &&
                                            payment.status !== 'Approved (Regular payment)'
                                        "
                                    >
                                        <span class="paymentStatus danger">{{::payment.status}}</span>
                                        <i
                                            class="tooltip-icon"
                                            tooltip-placement="top-left"
                                            uib-tooltip="{{ 'Payment was not confirmed via SMS' | translate }}"
                                            tooltip-class="tooltip-outer"
                                            aria-hidden="true"
                                        ></i>
                                    </div>
                                </td>
                                <td>{{::payment.amount}} {{::payment.currency}}</td>
                                <td ng-if="payment.descr === 'CleverStaff usage (Regular payment)' || payment.status === 'Approved (Regular payment)'">
                                    {{ 'Regular Payment' | translate }}
                                </td>
                                <td ng-if="payment.descr !== 'CleverStaff usage (Regular payment)' && payment.status !== 'Approved (Regular payment)'">
                                    <div ng-if="payment.params.users && payment.params.months">
                                        {{ 'For' | translate }} {{::payment.params.users}}
                                        <span ng-if="payment.params.users == 1" translate="User2"></span>
                                        <span ng-if="payment.params.users > 1" translate="Users2"></span>
                                        {{ 'For2' | translate }} {{::payment.params.months}}
                                        <span ng-if="payment.params.months == 1" translate="Month2"></span>
                                        <span ng-if="payment.params.months <= 4 && payment.params.months > 1" translate="Months3"></span>
                                        <span ng-if="payment.params.months > 4" translate="Months2"></span>
                                    </div>
                                    <div ng-hide="payment.params.users && payment.params.months">
                                        {{ 'CleverStaff_usage' | translate }}
                                    </div>
                                </td>
                                <td class="delete-icon">
                                    <i
                                        ng-if="payment.status == 'new' || payment.status == 'Expired' || payment.status == 'Declined (Regular payment)'"
                                        ng-click="deletePayment(payment)"
                                        class="fa fa-times"
                                        title="{{ 'Delete' | translate }}"
                                        aria-hidden="true"
                                    ></i>
                                </td>
                            </tr>
                        </thead>
                    </table>
                    <table ng-show="paymentHistory.transitions && expenses.length" class="write-offs-history">
                        <thead>
                            <tr>
                                <th translate="date"></th>
                                <th translate="Payment"></th>
                                <th translate="Sum"></th>
                                <th translate="Balance"></th>
                            </tr>
                        </thead>
                        <tr ng-repeat="expense in expenses">
                            <td>{{ (expense.targetDate | dateFormatSimple2 : false).trim() }}</td>
                            <td ng-show="!expense.descr && expense.type == 'expense'">
                                <span>{{ 'Charge for' | translate }}{{ ' ' + expense.peopleCount }}</span>
                                <span ng-if="expense.peopleCount == 1">{{ 'User2' | translate }}</span>
                                <span ng-if="expense.peopleCount > 1">{{ 'Users2' | translate }}</span>
                            </td>
                            <td ng-show="!expense.descr && expense.type == 'pay'">
                                <span translate="Reffilling the balance"></span>
                            </td>
                            <td
                                ng-show="
                                    expense.descr && (expense.descr === 'Mailing expense' || expense.descr === 'Refilling the balance with referral bonus')
                                "
                            >
                                <span translate="{{::expense.descr}}"></span>
                            </td>
                            <td ng-show="expense.descr && expense.descr === 'Regular Payment'">
                                <span translate="{{::expense.descr}}"></span>
                            </td>
                            <td
                                ng-show="
                                    expense.descr &&
                                    expense.descr !== 'Mailing expense' &&
                                    expense.descr !== 'Refilling the balance with referral bonus' &&
                                    expense.descr !== 'Regular Payment'
                                "
                            >
                                {{::expense.descr}}
                            </td>
                            <td>{{::expense.amountDouble + ' '}} USD</td>
                            <td>{{::expense.balanceDouble + ' '}}USD</td>
                        </tr>
                    </table>
                </div>
            </section>
        </div>
    </div>
</div>
