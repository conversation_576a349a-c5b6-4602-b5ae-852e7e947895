<div class="referral-bonus-content">
    <div class="modal-header">
        <span ng-show="!vm.requestSuccessful" translate="Withdraw my rewards"></span>
        <span ng-show="vm.requestSuccessful" translate="Withdraw request successful title"></span>
        <div ng-click="vm.$uibModalInstance.close()" class="close-btn"></div>
    </div>
    <div ng-show="!vm.requestSuccessful" class="modal-body">
        <div class="withdraw-target">
            <button ng-click="vm.changeTarget('cs')" class="to-cs-account" ng-class="{ active: vm.target === 'cs' }">
                <span class="logo">
                    <img src="/images/sprite/favicon.png" alt="Logo" />
                </span>
                <span translate="Top up my CleverStaff account"></span>
            </button>
            <span class="or">{{ 'Or' | translate | uppercase }}</span>
            <button ng-click="vm.changeTarget('bank-account')" class="to-bank-account" ng-class="{ active: vm.target === 'bank-account' }">
                <span class="logo">
                    <img src="/images/sprite/referral-program/card.svg" alt="card" />
                </span>
                <span translate="Withdraw money to my bank account"></span>
            </button>
        </div>
        <div class="withdraw-info">
            <div ng-show="vm.target === 'cs'" class="text">
                <div translate="Are you sure you want to top up your CleverStaff?" translate-values="{rewardAmount: vm.withdrawParams.totalWithdrawn}"></div>
            </div>
            <div id="emailWrapper" ng-show="vm.target === 'bank-account'" class="text">
                <div
                    translate="Are you sure you want to withdraw ${total_reward_amount} to your bank account?"
                    translate-values="{rewardAmount: vm.withdrawParams.totalWithdrawn}"
                ></div>
                <label for="email" translate="Please provide your actual email address"></label>
                <input id="email" class="form-control" type="text" placeholder="<EMAIL>" ng-model="vm.email" />
                <span class="empty" translate="Please fill in the email field"></span>
                <span class="not-valid" translate="Please provide correct email address"></span>
            </div>
        </div>
    </div>
    <div ng-show="vm.requestSuccessful" class="modal-body success">
        <span translate="Withdraw request successful text"></span>
        :
        <a href="mailto:<EMAIL>"><EMAIL></a>
    </div>
    <div class="modal-footer">
        <button ng-click="vm.$uibModalInstance.close()" class="btn_default btn_empty btn_thin" translate="Close"></button>
        <button ng-show="!vm.requestSuccessful" ng-click="vm.withdraw()" class="btn_default btn_success btn_thin">
            <span ng-show="vm.target === 'bank-account'" translate="Yes, proceed with transfer"></span>
            <span ng-show="vm.target === 'cs'" translate="Yes, top it up"></span>
        </button>
    </div>
</div>
