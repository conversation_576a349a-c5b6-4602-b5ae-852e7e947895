<div class="remove-mailbox-block">
    <div class="modal-body" style="display: block; text-align: unset; margin-top: 10px" style="flex-direction: column">
        <div class="modal-body__content" style="font-weight: normal; padding-right: 1%">
            <h2 style="margin-top: 0; text-align: center">{{ 'Recruiting funnel' | translate }}</h2>
            <span class="modal-body__content">{{ 'text_for_modal_on_funnel_report.report_display' | translate }}</span>
            <p style="font-weight: 500">{{ 'text_for_modal_on_funnel_report.effective_funnel' | translate }}</p>
            <p>{{ 'text_for_modal_on_funnel_report.funnel_based' | translate }}</p>
            <span>
                {{ 'text_for_modal_on_funnel_report.display_total_number' | translate }}
            </span>
            <p>{{ 'text_for_modal_on_funnel_report.will_not_display' | translate }}</p>
            <p>{{ 'text_for_modal_on_funnel_report.general_rules' | translate }}</p>
            <ul style="margin-bottom: 0px">
                <li>{{ 'text_for_modal_on_funnel_report.rule_first' | translate }}</li>
                <li>{{ 'text_for_modal_on_funnel_report.rule_second' | translate }}</li>
                <li>{{ 'text_for_modal_on_funnel_report.rule_third' | translate }}</li>
                <li>{{ 'text_for_modal_on_funnel_report.rule_four' | translate }}</li>
                <li>{{ 'text_for_modal_on_funnel_report.rule_five' | translate }}</li>
            </ul>

            <h2 style="margin-top: 20px; text-align: center">{{ 'Histogram of stages' | translate }}</h2>
            <p style="margin-bottom: 20px">
                {{ 'The histogram of stages differs from the funnel because' | translate }}
            </p>

            <p>{{ 'text_for_modal_on_funnel_report.please_note' | translate }}</p>
        </div>
    </div>
    <div class="modal-footer">
        <button ng-click="$root.closeModal()" class="btn_thin btn_default btn_success" style="margin-right: 0px">
            {{ 'acquainted' | translate }}
        </button>
    </div>
</div>
