<div class="invite-new-user">
    <div class="modal-header">
        <div ng-click="$root.modalInstance.close()" class="close-btn"></div>
        <h4>{{ 'invitation_user' | translate }}</h4>
    </div>

    <div class="modal-body">
        <div class="item">
            <span class="item-title">
                {{ 'Enter_new_users_email_2' | translate }}
                <span class="form-title-star">*</span>
            </span>
            <div>
                <input-component
                    placeholder="'enter_email'"
                    value="$root.inviteUser.email"
                    on-change="($root.onChangeUserEmail)"
                    is-error="$root.inviteUserErrorEmail"
                    icon-name="'envelope-light'"
                ></input-component>
                <span ng-show="$root.inviteUserErrorEmail" class="error-message">{{ 'wrong_email' | translate }}</span>
            </div>
        </div>

        <div class="item">
            <span class="item-title">
                {{ 'Select the role of the new user' | translate }}
                <span class="form-title-star">*</span>
            </span>
            <div class="item item-borders" ng-repeat="roles in $root.userRoles track by $index">
                <span class="item-borders-roles">{{ roles.type | translate }}</span>
                <div ng-click="selectUserRole(role)" class="item" ng-repeat="role in roles.roles track by $index">
                    <div class="item-row">
                        <radio-component
                            style="height: 20px"
                            value="$root.inviteUser.role"
                            name="invite-types"
                            on-change="(selectUserRole)"
                            checked="$root.inviteUser.role === role.value"
                        ></radio-component>
                        <span ng-click="selectUserRole(role)">{{ role.name | translate }}</span>
                        <i
                            class="hint-info-icon__grey"
                            tooltip-placement="top-left"
                            uib-tooltip-html="$root.tooltips.userInvite.{{ role.value }}"
                            aria-hidden="true"
                            tooltip-append-to-body="true"
                        ></i>
                    </div>
                    <div ng-if="role.value === 'client' && $root.inviteUser.role === 'client'" class="item-row">
                        <select-single-async
                            class="dropdown-full"
                            current-lang="$root.currentLang"
                            placeholder="'Select vacancies'"
                            fetch-options="($root.getAllVacancies)"
                            on-change="($root.selectVacancy)"
                            selected-value="$root.selectedVacancy"
                            is-сlearable="false"
                            path-to-label="'label'"
                            path-to-value="'label'"
                            position="'fixed'"
                            max-menu-height="120"
                            min-menu-height="60"
                        ></select-single-async>
                        <i class="hint-info-icon__grey" tooltip-placement="top-right" uib-tooltip-html="$root.tooltips.hmInvite" aria-hidden="true"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="item info">
            <img style="width: 20px" src="images/redesign/svg-icons/info.svg" alt="" />
            <span>{{ 'the invitation is valid for' | translate }}</span>
        </div>
    </div>
    <div class="modal-footer">
        <button-component ng-click="$root.modalInstance.close()" type="'secondary'" text="'cancel_2'  | translate"></button-component>
        <button-component
            ng-click="$root.addInvite()"
            text="'invite' | translate"
            disabled="!(!inviteForm.email.$error.email && !inviteForm.email.$error.required) || !($root.inviteUser.role == 'admin' || $root.inviteUser.role == 'recruter'
               || $root.inviteUser.role == 'freelancer' ||
               $root.inviteUser.role == 'researcher' || ($root.inviteUser.role == 'client' && $root.hideTariff))"
        ></button-component>
    </div>
</div>
