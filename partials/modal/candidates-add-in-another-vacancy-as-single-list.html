<div class="candidate-add-in-vacancy">
    <div class="modal-header">
        <div ng-click="$root.closeModal()" class="close-btn"></div>
        <h4 class="modal-header-title">{{ 'add_to_vacancy' | translate }}</h4>
        <span ng-show="vm.selectedData.length > 0">{{ vm.selectedData.length }}</span>
        <span ng-show="vm.selectedData.length == 0">{{ $root.totalCandidatesInStages }}</span>
        <span ng-show="vm.selectedData.length == 1">{{ 'candidate_2' | translate }}</span>
        <span ng-show="vm.selectedData.length > 1">{{ 'candidates__' | translate }}</span>
        <span ng-show="vm.selectedData.length == 0">{{ 'candidates__' | translate }}</span>
    </div>
    <div class="modal-body modal-body">
        <div class="item mrgBottom">
            <input id="candidatesAddToVacancy" ng-change="errorMessageForAddCandidateInVacancy.show = false" ng-model="test" vacancy-autocompleter />
        </div>
        <span class="text-center addedToLongList">
            {{ 'They will be added to the Long List stage, except of candidates previously added for this vacancy' | translate }}
        </span>
    </div>
    <div class="modal-footer">
        <button ng-click="$root.closeModal()" class="btn_default btn_empty btn_thin" type="button">
            {{ 'cancel' | translate }}
        </button>
        <button ng-click="$root.addCandidatesToVacancyAsSingleList()" class="btn_default btn_success btn_thin" type="button">
            {{ 'add' | translate }}
        </button>
    </div>
</div>
