<div class="send-reject-by-email">
    <div class="modal-header">
        <i ng-click="$root.closeModal()" class="fa fa-times-white pull-right"></i>
        <h4 ng-show="$root.$state.current.data.pageName != 'Recalls Info'" class="modal-header-title">
            {{ 'Send by email' | translate }}
        </h4>
        <h4 ng-show="$root.$state.current.data.pageName == 'Recalls Info'" class="modal-header-title">
            {{ 'send_failure' | translate }}
        </h4>
    </div>
    <div class="modal-body">
        <p ng-show="$root.me.emails.length == 0">
            {{ 'To send a letter from your mailbox, you need to' | translate }},
            <a href="#/email-integration" target="_blank">{{ 'add integration with email' | translate }}</a>
        </p>
        <div class="item mail-checkbox">
            <div
                ng-show="$root.me.emails.length > 1"
                ng-click="$root.addEmailFromWhatSendInDescription(email)"
                class="checkbox"
                ng-repeat="email in $root.me.emails"
            >
                <label>
                    <input type="checkbox" ng-checked="email.email == $root.emailTemplateInModal.email" />
                    {{ email.email }}
                </label>
            </div>
            <div ng-show="$root.me.emails.length == 1" class="field">
                <label>{{ 'Send from' | translate }} {{ $root.me.emails[0].email }}</label>
            </div>
        </div>
        <span>{{ "Candidate's email" | translate }}</span>
        <input class="form-control" type="email" ng-model="$root.sendEmailTemplate.toEmails" required />
        <div ng-show="!$root.sendEmailTemplate.toEmails" class="error">
            <span>{{ 'wrong_email' | translate }}</span>
        </div>
        <span>{{ 'Letter subject' | translate }}</span>
        <input class="form-control" type="text" ng-model="$root.sendEmailTemplate.template.title" />
        <div class="tinyMCE-block">
            <textarea id="sendVacancyModalMCE" name="sendVacancyModalMCE"></textarea>
            <label ng-show="$root.fileForSave.length == 0" for="file">
                <span>{{ 'Attach file' | translate }}</span>
            </label>
            <span class="item" ng-repeat="file in $root.fileForSave">
                <a style="color: #000000" ng-href="{{ serverAddress }}/getapp/{{ file.fileId }}/{{ file.fileResolution }}" title="{{ file.fileName }}">
                    {{ file.fileName | fileNameCut : 0 : 30 }}
                </a>
                <i ng-click="removeFile(file.attId);$event.stopPropagation();" class="fa fa-times"></i>
            </span>
        </div>
    </div>
    <div class="modal-footer">
        <div class="text-center button">
            <a ng-click="$root.closeModal()" class="btn btn-primary cancel" type="button">{{ 'cancel' | translate }}</a>
            <a ng-show="$root.me.emails.length > 0" ng-click="$root.sendEmailTemplateFunc()" class="btn btn-primary accept" type="button">
                {{ 'Send' | translate }}
            </a>
            <a ng-show="$root.me.emails.length == 0" class="btn btn-primary accept" type="button">
                {{ 'Send' | translate }}
            </a>
        </div>
    </div>
</div>
