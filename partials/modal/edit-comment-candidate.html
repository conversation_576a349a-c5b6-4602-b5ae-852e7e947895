<div class="delete-comment-candidate">
    <div class="modal-header">
        <div ng-click="$root.closeCandidateComment()" class="close-btn"></div>
        <h4 class="modal-header-title">
            {{ 'Edit comment' | translate }}
        </h4>
    </div>
    <div class="modal-body">
        <textarea id="ckEditorCandidateEdit" class="customTextarea" ckeditor options="$root.ckEditorCandidateEdit"></textarea>
        <select-single-virtualized
            ng-if="vm.options.length && $root.me.recrutRole !== 'client' && !$root.isCandidateInVacancy"
            placeholder="'Show to Hiring Managers in vacancy' | translate"
            options="vm.options"
            position="'fixed'"
            path-to-label="'position'"
            path-to-value="'vacancyId'"
            selected-value="$root.vacancyWithShowingComment"
            on-change="(vm.onVacancyChange)"
        ></select-single-virtualized>
    </div>
    <div class="modal-footer">
        <button-component ng-click="$root.closeCandidateComment()" type="'secondary'" text="'cancel' | translate"></button-component>
        <button-component ng-click="$root.changeComment(vm);$root.modalInstance.close()" text="'save' | translate"></button-component>
    </div>
</div>
