<div class="invite-hiring-manager">
    <div class="modal-header">
        <div ng-click="$root.closeModal()" class="close-btn"></div>
        <h4 class="modal-header-title">{{ 'Invite user with a role' | translate }} Hiring Manager</h4>
    </div>
    <div class="modal-body">
        <p>
            {{ 'Enter_new_users_email_1' | translate }}
            <span class="bold">Hiring Manager.</span>
        </p>

        <div class="email">
            <input
                class="form-control user-email"
                ng-class="{ inviteUserErrorEmail: $root.inviteUserErrorEmail }"
                type="text"
                ng-model="$root.inviteUser.email"
                required
                placeholder="{{ 'email_invitation_user_2' | translate }}"
            />
            <span
                ng-show="$root.inviteUser.email && $root.inviteUserErrorEmail"
                ng-click="$root.inviteUser.email = '';$root.inviteUserErrorEmail = false"
                class="close_icon"
            ></span>
            <span ng-show="$root.inviteUserErrorEmail" class="invalid__email">{{ 'wrong_email' | translate }}</span>
        </div>
        <div class="vacancy-autocompleter">
            <input id="vacancyToAddAutocompleaterModal" class="vacancy-autocompleter" type="hidden" vacancy-autocompleter />
            <i
                class="info-icon tooltip-icon"
                tooltip-placement="top-right"
                tooltip-class="tooltip-outer"
                uib-tooltip-html="$root.tooltips.hmInvite"
                aria-hidden="true"
            ></i>
        </div>
    </div>
    <div class="modal-footer">
        <button ng-click="$root.closeModal()" class="btn_default btn_thin btn_empty">
            {{ 'cancel_2' | translate }}
        </button>
        <button
            id="btn_invite_hm"
            ng-show="
                $root.inviteUser.role == 'admin' ||
                $root.inviteUser.role == 'recruter' ||
                $root.inviteUser.role == 'freelancer' ||
                $root.inviteUser.role == 'researcher' ||
                ($root.inviteUser.role == 'client' && $root.hideTariff)
            "
            ng-click="$root.addInvite()"
            class="btn_default btn_thin btn_success"
            ng-disabled="!(!inviteForm.email.$error.email && !inviteForm.email.$error.required) || isDisabledHM"
        >
            {{ 'invite' | translate }}
        </button>
    </div>
</div>
