<div class="evaluate-candidate-popup">
    <div class="modal-header">
        <h4 ng-show="vm.evaluateCardModel.name === 'Default' || vm.evaluateCardModel.name === 'Old Default'" class="modal-header-title">
            {{ vm.evaluateCardModel.name | translate }}
        </h4>
        <h4 class="modal-header-title" ng-hide="vm.evaluateCardModel.name === 'Default' || vm.evaluateCardModel.name === 'Old Default'">
            {{ vm.evaluateCardModel.name }}
        </h4>
        <div ng-click="vm.closeModal()" class="close-btn"></div>
    </div>
    <div class="modal-body evaluation">
        <form class="evaluation" name="evaluation">
            <div class="sections">
                <div class="section" ng-repeat="section in vm.evaluateCardModel.schema.blocks track by $index">
                    <div ng-if="vm.evaluateCardModel.name === 'Default' || vm.evaluateCardModel.name === 'Old Default'" class="section-name">
                        {{ section.label | translate }}
                    </div>
                    <div ng-if="vm.evaluateCardModel.name !== 'Default' && vm.evaluateCardModel.name !== 'Old Default'" class="section-name">
                        {{ section.label }}
                    </div>
                    <div class="item" ng-repeat="sectionItem in section.questions track by $index">
                        <div class="item-wrap">
                            <div ng-if="vm.evaluateCardModel.name === 'Default' || vm.evaluateCardModel.name === 'Old Default'" class="item-name">
                                <span>{{ sectionItem.label | translate }}</span>
                                <span ng-if="!sectionItem.hasOwnProperty('required') || sectionItem.required" class="form-title-star">*</span>
                                <div class="score-comment for-mobile" ng-hide="vm.evaluateCardModel.name === 'old_default'">
                                    <img
                                        ng-click="sectionItem.showComment = !sectionItem.showComment"
                                        ng-class="{ selected: sectionItem.showComment }"
                                        src="../../images/sprite/score-cards/score-comment.svg"
                                        alt=""
                                    />
                                </div>
                            </div>
                            <div ng-if="vm.evaluateCardModel.name !== 'Default' && vm.evaluateCardModel.name !== 'Old Default'" class="item-name">
                                <span>{{ sectionItem.label }}</span>
                                <span ng-if="!sectionItem.hasOwnProperty('required') || sectionItem.required" class="form-title-star">*</span>
                                <div class="score-comment for-mobile" ng-hide="vm.evaluateCardModel.name === 'old_default'">
                                    <img
                                        ng-click="sectionItem.showComment = !sectionItem.showComment"
                                        ng-class="{ selected: sectionItem.showComment }"
                                        src="../../images/sprite/score-cards/score-comment.svg"
                                        alt=""
                                    />
                                </div>
                            </div>
                            <div class="score-btns">
                                <div
                                    ng-click="vm.selectScore($event, $parent.$index, $index)"
                                    class="score-btn"
                                    ng-class="sectionItem.score >= 0 ? 'selected' : 'noselected'"
                                    data-value="0"
                                >
                                    <img src="{{ vm.imgSrc }}" alt="" />
                                </div>
                                <div
                                    ng-click="vm.selectScore($event, $parent.$index, $index)"
                                    class="score-btn"
                                    ng-class="sectionItem.score >= 1 ? 'selected' : 'noselected'"
                                    data-value="1"
                                >
                                    <img src="{{ vm.imgSrc }}" alt="" />
                                </div>
                                <div
                                    ng-click="vm.selectScore($event, $parent.$index, $index)"
                                    class="score-btn"
                                    ng-class="sectionItem.score >= 2 ? 'selected' : 'noselected'"
                                    data-value="2"
                                >
                                    <img src="{{ vm.imgSrc }}" alt="" />
                                </div>
                                <div
                                    ng-click="vm.selectScore($event, $parent.$index, $index)"
                                    class="score-btn"
                                    ng-class="sectionItem.score >= 3 ? 'selected' : 'noselected'"
                                    data-value="3"
                                >
                                    <img src="{{ vm.imgSrc }}" alt="" />
                                </div>
                                <div
                                    ng-click="vm.selectScore($event, $parent.$index, $index)"
                                    class="score-btn"
                                    ng-class="sectionItem.score >= 4 ? 'selected' : 'noselected'"
                                    data-value="4"
                                >
                                    <img src="{{ vm.imgSrc }}" alt="" />
                                </div>
                                <div class="score-comment for-full" ng-hide="vm.evaluateCardModel.name === 'old_default'">
                                    <img
                                        ng-click="sectionItem.showComment = !sectionItem.showComment"
                                        ng-class="{ selected: sectionItem.showComment }"
                                        src="../../images/sprite/score-cards/score-comment.svg"
                                        alt=""
                                    />
                                </div>
                            </div>
                        </div>
                        <div ng-show="sectionItem.showComment" class="score-comments">
                            <textarea
                                id="score-comment"
                                cols="30"
                                rows="10"
                                ng-model="sectionItem.comment"
                                placeholder="{{ 'Start typing' | translate }}"
                            ></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="general">
                <div class="general-item">
                    <label class="item-title" for="comment">{{ 'General impression' | translate }}</label>
                    <textarea
                        id="comment"
                        name="comment"
                        cols="30"
                        rows="10"
                        ng-model="vm.evaluateCardModel.comment"
                        placeholder="{{ 'Scorecard placeholder' | translate }}"
                    ></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button-component ng-click="vm.closeModal()" type="'secondary'" text="'cancel' | translate"></button-component>

        <button-component ng-if="vm.iHaveAlreadyEvaluateCandidate" ng-click="vm.resetScore()" type="'danger'" text="'Delete' | translate"></button-component>

        <button-component ng-click="vm.sendScore()" text="'save' | translate"></button-component>
    </div>
</div>
