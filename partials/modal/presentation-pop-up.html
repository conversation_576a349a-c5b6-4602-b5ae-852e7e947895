<div class="modal-body" ng-mouseenter="showCloseIcon=true" ng-mouseleave="showCloseIcon=false">
    <div ng-show="showCloseIcon" ng-click="closePopup()" class="close-icon"></div>
    <img class="modal-img" ng-src="images/presentation-pop-up/{{ presentationId }}/{{ slideNumber }}.png" alt="" />
    <div class="modal-info">
        <p class="modal-msg" ng-bind-html="presentationPopupData['notificationTexts'][slideNumber][$root.currentLang]"></p>
        <button-component
            ng-show="slideNumber === presentationPopupData.slidesTotal"
            ng-click="closePopup($event)"
            text="'Get it' | translate"
        ></button-component>
        <pagination-component
            class="vacancies-pagination"
            display-settings="paginationSettings"
            translate-func="$root.translate"
            total-pages="presentationPopupData.slidesTotal"
            current-page="slideNumber"
            on-change-page="(onChangeSlide)"
            current-amount-of-elements="presentationPopupData.slidesTotal"
        ></pagination-component>
    </div>
</div>
