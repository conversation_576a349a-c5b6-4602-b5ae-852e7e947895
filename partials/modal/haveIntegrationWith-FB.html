<div class="notHaveIntegrationWith-FB">
    <div class="modal-header">
        <div ng-click="vm.closeModal()" class="close-btn"></div>
        <h4 class="modal-header-title">{{ 'Add_to_Facebook_pages_Jobs' | translate }}</h4>
    </div>
    <div class="modal-body">
        <p>{{ 'You_add_this_vacancy_to_the_jobs_list' | translate }}</p>
        <p>{{ 'Choose_company_page' | translate }}</p>
        <div ng-show="tab.status != 'D' && tab.status == 'A'" ng-repeat="tab in $root.fbPages">
            <div class="pull-left">
                <label>
                    <input ng-checked="tab.vacancyAdded" type="checkbox" value="" />
                    <span>{{ tab.name }}</span>
                </label>
            </div>
            <div class="publish-vacancy-to-FB pull-right">
                <a ng-show="tab.vacancyAdded" class="btn btn-default btn-sm" type="button">
                    {{ 'Already_published' | translate }}
                </a>
                <a ng-show="!tab.vacancyAdded" ng-click="vm.addVacancyToFacebook(tab);" class="btn btn-primary btn-sm" type="button">
                    {{ 'Share_on_the_company_page_in_Facebook' | translate }}
                </a>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button-component ng-click="vm.closeModal();" type="'secondary'" text="'cancel' | translate "></button-component>
    </div>
</div>
