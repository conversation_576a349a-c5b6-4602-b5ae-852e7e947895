<div class="corporate-email-not-integrated">
    <div class="modal-header info">
        <div ng-click="vm.closeModal()" class="close-btn"></div>
        <p ng-if="vm.noOneIntegratedCorpEmail">{{ 'Your corporate mailbox is not connected' | translate }}</p>
        <p ng-if="vm.noOneEmailIntegrated">{{ 'Your mailbox is not connected' | translate }}</p>
    </div>

    <div class="modal-body info">
        <div ng-if="vm.isMassMailing">
            <p ng-if="!vm.noOneIntegratedCorpEmail && !vm.emailIsEnabled">
                {{ 'You have not turned on sending mass mailing from the corporate mailbox' | translate }}
            </p>
            <p ng-if="vm.noOneIntegratedCorpEmail">
                {{ 'Please connect your corporate email to use mass-mailing' | translate }}
            </p>
            <p ng-if="!vm.noOneIntegratedCorpEmail && !vm.emailIsEnabled">
                {{ 'To use mass mailings you must enable sending emails from the integrated corporate mailbox in the CleverStaff system' | translate }}
            </p>
        </div>
        <div ng-if="vm.isPersonalMailing" class="none-integration">
            <p ng-if="!vm.noOneEmailIntegrated && !vm.emailIsEnabled">
                {{ 'You have not turned on sending personal mailing from this mailbox' | translate }}
            </p>
            <p ng-if="vm.noOneEmailIntegrated">{{ 'To use personal mailing you must connect a mailbox in the СleverStaff system' | translate }}</p>
            <p ng-if="!vm.noOneEmailIntegrated && !vm.emailIsEnabled">
                {{ 'To use personal mailing you must enable sending emails from the integrated mailbox in the CleverStaff system' | translate }}
            </p>
        </div>
    </div>

    <div class="modal-footer info">
        <button-component ng-click="vm.closeModal()" class="dropdown-full" type="'secondary'" text="'Close' | translate"></button-component>
        <a class="dropdown-full" href="#/email-integration">
            <button-component class="dropdown-full" text="'Connect' | translate"></button-component>
        </a>
    </div>
</div>
