<div class="adding-responsible">
    <div class="modal-header">
        <div ng-click="vm.closeAddingResponsileForVacancyModal()" class="close-btn"></div>
        <h4 class="modal-header-title">
            {{ 'add new responsible' | translate }}
            {{ $root.changeResponsibleInVacancy.name }}
        </h4>
    </div>
    <div class="modal-body" style="padding: 15px; margin: 0 20px">
        <div class="item taskText">
            <textarea
                class="form-control"
                placeholder="{{ 'write_a_comment_why_do_you_add_responsible_user' | translate }}"
                ng-model="$root.changeResponsibleInVacancy.comment"
            ></textarea>
        </div>
    </div>
    <div class="modal-footer">
        <button ng-click="vm.closeAddingResponsileForVacancyModal()" class="btn_default btn_thin btn_empty" type="button">
            {{ 'cancel' | translate }}
        </button>
        <button ng-click="vm.saveResponsibleUserInVacancy()" class="btn_default btn_thin btn_success" type="button">
            {{ 'save' | translate }}
        </button>
    </div>
</div>
