<div class="send-vacancy-by-email">
    <div class="modal-header">
        <div ng-click="vm.closeModal()" class="close-btn"></div>
        <h4 class="modal-header-title">
            {{ $root.$state.current.data.pageName != 'Recalls Info' ? ('Send by email' | translate) : ('send_failure' | translate) }}
        </h4>
    </div>
    <div class="modal-body">
        <div ng-show="$root.me.emails.length === 0 && !vm.noAllowedMails" class="alert-danger">
            <img src="images/redesign/svg-icons/info.svg" alt="" />
            <div>
                <span>{{ 'To send a letter from your mailbox, you need to' | translate }}</span>
                <a href="#/email-integration" target="_blank">{{ 'add integration with email' | translate }}</a>
            </div>
        </div>
        <div ng-show="$root.me.emails.length === 0 && vm.noAllowedMails" class="alert-danger">
            <span translate="To send a letter from your mailbox,allow sending mails from the email integration page"></span>
        </div>
        <div class="item">
            <div class="item-title">
                <span>{{ "Candidate's email" | translate }}</span>
                <span ng-show="$root.me.emails.length == 1" class="send-from">{{ 'Send from' | translate }} {{ $root.me.emails[0].email }}</span>
            </div>
            <input-component
                current-lang="$root.currentLang"
                placeholder="'enter_email_candidate'"
                value="vm.sendEmailTemplate.toEmails"
                is-error="vm.emptyCandidateEmail"
                on-change="(vm.changeCandidateEmail)"
            ></input-component>
        </div>
        <div ng-show="$root.me.emails.length > 1" class="item">
            <span class="item-title">{{ 'Select email' | translate }}</span>
            <select-single
                current-lang="$root.currentLang"
                on-change="(vm.addEmailFromWhatSendInDescription)"
                options="$root.me.emails"
                selected-value="vm.selectedEmail"
                placeholder="'Select email'"
                path-to-label="'email'"
                path-to-value="'email'"
                is-clearable="false"
                position="'fixed'"
            ></select-single>
        </div>
        <div class="item">
            <span class="item-title">{{ 'Letter subject' | translate }}</span>
            <input-component
                current-lang="$root.currentLang"
                placeholder="'Letter subject'"
                value="vm.sendEmailTemplate.template.title"
                on-change="(vm.changeLetterSubject)"
            ></input-component>
        </div>
        <div class="item">
            <textarea
                id="sendVacancyModalMCE"
                ckeditor
                name="sendVacancyModalMCE"
                options="vm.ckEditorOptions"
                ng-model="vm.sendEmailTemplate.template.text"
            ></textarea>
            <!--            <mail-to-->
            <!--                ng-if="$root.me.orgParams.mailto === 'true'"-->
            <!--                email="vm.sendEmailTemplate.toEmails"-->
            <!--                editor-id="sendVacancyModalMCE"-->
            <!--                subject="vm.sendEmailTemplate.template.title"-->
            <!--            ></mail-to>-->
            <span class="item" ng-repeat="file in vm.fileForSave">
                <a style="color: #000000" ng-href="{{ serverAddress }}/getapp/{{ file.fileId }}/{{ file.fileResolution }}" title="{{ file.fileName }}">
                    {{ file.fileName | fileNameCut : 0 : 30 }}
                </a>
                <i ng-click="removeFile(file.attId);$event.stopPropagation();" class="fa fa-times"></i>
            </span>
            <template-file-attach
                io-file="vm.optionsForTemplate"
                files="$root.fileForSave"
                max-files-count="3"
                remove-file="vm.removeFile(fileId)"
            ></template-file-attach>
        </div>
    </div>
    <div class="modal-footer">
        <button-component ng-click="vm.closeModal()" type="'secondary'" text="'cancel' | translate"></button-component>
        <button-component on-click="(vm.sendEmailTemplateFunc)" text="'Send' | translate" disabled="$root.me.emails.length === 0"></button-component>
    </div>
</div>
