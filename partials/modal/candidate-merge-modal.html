<div class="candidate-merge">
    <div class="modal-header">
        <div ng-click="$root.closeModal()" class="close-btn"></div>
        <h4 class="modal-header-title">{{ 'Merge this profile with another candidate' | translate }}</h4>
    </div>
    <div class="modal-body">
        <div class="item">
            <select-single-async
                fetch-options="(onFetchMergeCandidates)"
                placeholder="'select candidate' | translate"
                on-change="(onChangeCandidateForMerge)"
                selected-value="candidateForMerge"
                path-to-value="'id'"
                position="'fixed'"
                input-search-threshold="2"
            ></select-single-async>
            <!-- prettier-ignore -->
            <div ng-show="candidateForMerge">
                <a href="#/candidates/{{ candidateForMerge.localId }}" target="_blank">{{ candidateForMerge.fullName }}</a>, <span>{{ candidateForMerge.position }}</span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button-component ng-click="$root.closeModal()" current-lang="$root.currentLang" type="'secondary'" text="'cancel' | translate"></button-component>
        <button-component
            ng-click="candidateForMerge && $root.toMerge(mergedCandidate)"
            disabled="!candidateForMerge"
            current-lang="$root.currentLang"
            text="'Merge' | translate"
        ></button-component>
    </div>
</div>
