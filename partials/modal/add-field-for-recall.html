<div class="new-recall-field">
    <div class="modal-header">
        <div ng-click="$root.closeModal()" class="close-btn"></div>
        <h4 class="modal-title">
            {{ 'Create field' | translate }}
        </h4>
    </div>
    <div class="modal-body">
        <div class="modal-body__name">
            <span class="modal-body__name-title">
                {{ 'Field name:' | translate }}
            </span>
            <input
                class="form-control modal-body__name-input"
                ng-class="{ error: nameError }"
                ng-model="editRecallField.name"
                ng-change="checkInput(editRecallField.name)"
                placeholder="{{ 'Enter the field title' | translate }}"
                type="text"
            />
        </div>
        <span ng-show="nameError" style="color: red">{{ 'Fill a field' | translate }}</span>
        <div class="modal-body__others">
            <div class="custom-field">
                <span class="title">
                    {{ disabledField ? ('Field type(not editable):' | translate) : ('field type' | translate) }}
                </span>
                <custom-select-new
                    id="custom-field"
                    class="custom-field-select"
                    ng-class="{ disabled: disabledField }"
                    model="editRecallField.type"
                    options="customFields"
                    path='"translate"'
                    hide-placeholder-from-options="true"
                ></custom-select-new>
            </div>
            <div
                ng-show="editRecallField.type.value == 'textFile' || editRecallField.type.value === 'file' || editRecallField.type.value === 'selectFile'"
                class="count-files"
            >
                <span class="title">
                    {{ 'Limit-count-files' | translate }}
                </span>
                <custom-select-new
                    id="file-count"
                    model="editRecallField.count"
                    placeholder="editRecallField.count"
                    options="filesCount"
                    hide-placeholder-from-options="true"
                ></custom-select-new>
            </div>
            <div ng-show="editRecallField.type.value === 'select' || editRecallField.type.value === 'selectFile'" class="dropdown-list">
                <label class="title" for="customFullField">{{ 'Name of items in the drop-down list' | translate }}</label>
                <custom-field-input id="customFullField" style="width: 100%" error-input="emptyDropDown" model="selectedTags"></custom-field-input>

                <div ng-if="selectedTags.length" class="selected-values">
                    <ul>
                        <li ng-repeat="tag in selectedTags">
                            <span>{{ tag.text }}</span>
                            <a ng-click="clearSelectedTag(tag)" class="select2-search-choice-close remove"></a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer" style="">
        <button ng-click="$root.closeModal()" class="btn_default btn_empty btn_thin" type="button">
            {{ 'cancel' | translate }}
        </button>
        <button ng-click="addCustomField()" class="btn_default btn_thin btn_success" type="button">
            {{ 'save' | translate }}
        </button>
    </div>
</div>
