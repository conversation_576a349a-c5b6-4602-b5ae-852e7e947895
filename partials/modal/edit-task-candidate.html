<div class="add-and-edit-task">
    <div class="modal-header">
        <div ng-click="$root.closeTaskModal()" class="close-btn"></div>
        <h4>{{ 'Edit-field' | translate }}</h4>
    </div>

    <div class="modal-body">
        <div class="item">
            <span ng-if="$root.editableTask.candidate">
                <span class="grey-theme">{{ 'Candidate' | translate }}</span>
                <a class="user-link" ng-href="#/candidates/{{ $root.editableTask.candidate.localId }}" target="_blank">
                    {{ $root.useAmericanNameStyle ? $root.editableTask.candidate.fullNameEn : $root.editableTask.candidate.fullName }}
                </a>
            </span>

            <span ng-if="$root.editableTask.client">
                <span class="grey-theme">{{ 'Client' | translate }}</span>
                <a class="user-link" ng-href="#/clients/{{ $root.editableTask.client.localId }}" target="_blank">
                    {{ $root.editableTask.client.name }}
                </a>
            </span>

            <span>
                <span ng-if="!$root.isToday($root.editableTask.dc)" class="grey-theme">{{ 'Created' | translate }}</span>
                <span ng-if="$root.isToday($root.editableTask.dc)" class="grey-theme">{{ 'created' | translate | capitalize }}</span>
                <date-format-base
                    translate-func="$root.translate"
                    with-today="true"
                    current-lang="$root.currentLang"
                    date="$root.editableTask.dc"
                    full-month="true"
                ></date-format-base>
                <span ng-if="$root.currentLang === 'en'" class="grey-theme">by</span>

                <span ng-if="!$root.editableTask.creator.fullName" class="removed-user">User removed</span>
                <a class="user-link" ng-href="#/users/{{ $root.editableTask.creator.userId }}" target="_blank">
                    {{ $root.useAmericanNameStyle ? $root.editableTask.creator.fullNameEn : $root.editableTask.creator.fullName }}
                </a>
            </span>
        </div>

        <div class="full-width">
            <span class="item-title">
                {{ 'Header text' | translate }}
                <span class="form-title-star">*</span>
            </span>
            <input-component
                class="add-task__name"
                placeholder="'Header text'"
                value="$root.editableTask.title"
                on-change="($root.changeEditableTaskName)"
            ></input-component>
        </div>

        <div class="item-row">
            <div class="full-width">
                <span class="item-title">{{ 'type' | translate }}</span>
                <select-single
                    options="$root.typeOfTasks"
                    placeholder="'Select task' | translate"
                    translate-options="true"
                    on-change="($root.changeTabOnTaskForEditableTask)"
                    selected-value="$root.editableTask.type"
                    path-to-label="'value'"
                    path-to-value="'value'"
                    is-clearable="false"
                    position="'fixed'"
                ></select-single>
            </div>
            <div class="full-width">
                <span class="item-title">
                    {{
                        $root.editableTask.type.value === 'Task'
                            ? $root.currentLang === 'en'
                                ? 'Deadline'
                                : ('Run on' | translate)
                            : ('Date and time' | translate)
                    }}
                    <span class="form-title-star">*</span>
                </span>
                <mdp-date-time-picker
                    class="date-field-picker"
                    ng-class="{ 'date-field-picker_error': taskFieldsErrors.targetDate }"
                    mdp-auto-switch="true"
                    mdp-format="{{ $root.currentLang === 'en' ? 'M/DD/YYYY h:mm A' : 'DD/MM/YYYY HH:mm' }}"
                    mdp-open-on-click
                    ng-model="$root.editableTask.targetDate"
                >
                    <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                </mdp-date-time-picker>
            </div>
        </div>

        <div class="full-width">
            <span class="item-title">{{ 'status' | translate }}</span>
            <select-single
                options="$root.editableTask.editableTaskOptions"
                path-to-value="'status'"
                path-to-label="'status'"
                selected-value="$root.editableTask.status"
                on-change="($root.onChangeTaskStatus)"
                translate-options="true"
                is-clearable="false"
                position="'fixed'"
            ></select-single>
        </div>

        <div>
            <span class="item-title">
                {{ 'Add perfomer' | translate }}
                <i
                    class="hint-info-icon__grey"
                    tooltip-placement="top-left"
                    tooltip-class="tooltip-info-black"
                    uib-tooltip="{{ 'A maximum of 5 responsible' | translate }}"
                    aria-hidden="true"
                ></i>
            </span>
            <select-multi-virtualized
                placeholder="'Add perfomer' | translate"
                options="$root.persons"
                show-select-all="false"
                selected-limit="5"
                selected-values="$root.responsiblePersons"
                path-to-value="$root.useAmericanNameStyle ? 'fullNameEn' : 'fullName'"
                path-to-key="'userId'"
                on-change="($root.onChangeEditableResponsiblePersons)"
                translate-options="false"
                position="'fixed'"
            ></select-multi-virtualized>
        </div>

        <div class="full-width">
            <span class="item-title">{{ 'Task descr' | translate }}</span>
            <textarea
                class="full-width"
                ng-change="$root.editedTaskText($root.editableTask.text)"
                ng-model="$root.editableTask.text"
                placeholder="{{ 'Task descr' | translate }}"
            ></textarea>
        </div>
    </div>

    <div class="modal-footer">
        <button-component ng-click="$root.closeTaskModal()" type="'secondary'" text="'cancel' | translate"></button-component>

        <button-component
            ng-if="$root.editableTask.creator.userId === $root.me.userId"
            ng-click="$root.deleteTask(vm)"
            type="'danger'"
            text="'Delete task' | translate"
        ></button-component>
        <button-component ng-click="$root.saveEditableTask($root.editableTask)" text="'save' | translate"></button-component>
    </div>
</div>
