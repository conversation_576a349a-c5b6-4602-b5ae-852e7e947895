<div class="add-photo">
    <div class="modal-header">
        <div ng-click="$root.closeModal()" class="close-btn"></div>
        <span class="modal-body__title" style="font-size: 22px; color: #212120; font-family: 'Helvetica-6-Medium'">
            {{ 'Add a photo' | translate }}
        </span>
    </div>
    <div class="modal-body">
        <div class="item" style="display: flex; align-items: flex-end; margin-bottom: 15px">
            <span style="margin-right: 10px; font-weight: 500; font-size: 14px">
                {{ 'You can select a photo on your computer' | translate }}
            </span>
            <label style="cursor: pointer; margin: 0" for="photoFile">
                <img style="height: 24px" src="images/sprite/download-photo.svg" alt="" />
            </label>
        </div>
        <div class="item">
            <span style="margin-right: 10px; font-weight: 500; font-size: 14px">
                {{ 'Or provide a link to photos on the internet' | translate }}
            </span>
            <div class="item" style="display: flex; margin-top: 5px">
                <input
                    style="height: 24px; margin-right: 10px"
                    placeholder="{{ 'For example, https://example.png' | translate }}"
                    ng-model="photoUrl"
                    type="text"
                />
            </div>

            <div class="formats" style="margin-top: 15px">
                <span class="supported-formats">
                    {{ 'Supported formats' | translate }}: JPG, PNG.
                    <span class="max-size">{{ 'Maximum size' | translate }}: 5 MB</span>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button ng-click="$root.closeModal()" class="btn_thin btn_default btn_empty">{{ 'cancel' | translate }}</button>
        <button ng-click="addPhotoByReference(photoUrl)" class="btn_success btn_thin btn_default" ng-disabled="photoUrl.length < 1 || !photoUrl">
            <span>{{ 'upload_photo' | translate }}</span>
        </button>
    </div>
</div>
