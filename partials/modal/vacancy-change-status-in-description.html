<div class="add-coment">
    <div class="modal-header">
        <div ng-click="vm.closeCommentVacancyModal()" class="close-btn"></div>
        <h4>
            <span ng-show="vm.changeStateObject.status === 'completed'">{{ 'Close vacancy' | translate }}?</span>
            <span ng-hide="vm.changeStateObject.status === 'completed'" ng-switch="vm.changeStateObject.status">
                <span ng-switch-when="deleted">{{ 'Are you sure want delete vacancy' | translate }} {{ vm.changeStateObject.position }}?</span>
                <span ng-switch-default>
                    <span>{{ 'vacancy_change_status' | translate }}</span>
                    <br />
                    {{ 'vacancy_status_assoc.' + vm.changeStateObject.status_old | translate }}
                </span>
                <img class="ico-arrow-right" src="images/redesign/svg-icons/arrow-down-1.svg" alt="" />
                <span>{{ 'vacancy_status_assoc.' + vm.changeStateObject.status | translate }}</span>
            </span>
        </h4>
    </div>
    <div class="modal-body">
        <div class="item">
            <textarea class="form-control" placeholder="{{ vm.changeStateObject.placeholder }}" ng-model="vm.changeStateObject.comment"></textarea>
        </div>
    </div>
    <div class="modal-footer">
        <button-component
            ng-click="vm.closeCommentVacancyModal()"
            ng-hide="vm.changeStateObject.status === 'completed'"
            type="'secondary'"
            text="'cancel' | translate"
        ></button-component>
        <button-component
            ng-show="vm.changeStateObject.status === 'completed'"
            ng-click="vm.saveVacancyStatus()"
            type="'secondary'"
            text="'Ok' | translate"
        ></button-component>
        <button-component ng-click="vm.saveVacancyStatus()" ng-hide="vm.changeStateObject.status === 'completed'" text="'save' | translate"></button-component>
    </div>
</div>
