<div class="auth-work-ua">
    <div class="modal-header">
        <h4>{{ 'Work.ua intergation setup' | translate }}</h4>
        <div ng-click="vm.closeModal()" class="close-btn"></div>
    </div>
    <div class="modal-body">
        <div class="row_wrapper">
            <div class="item">
                <span class="item-title">{{ 'login_2' | translate }}</span>
                <input-component
                    current-lang="$root.currentLang"
                    placeholder="'Enter your work.ua login'"
                    value="vm.work_login"
                    on-change="(vm.onChangeLogin)"
                ></input-component>
            </div>
            <div class="item">
                <span class="item-title">{{ 'password' | translate }}</span>
                <input-component
                    current-lang="$root.currentLang"
                    placeholder="'Enter your work.ua password'"
                    value="vm.work_pass"
                    type="'password'"
                    on-change="(vm.onChangePass)"
                ></input-component>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button-component ng-click="vm.authWorkUA()" text="'Connect' | translate"></button-component>
    </div>
</div>
