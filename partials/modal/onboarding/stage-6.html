<div class="instruction-popup-wrapper">
    <span class="gamification-blue-title popup-header">{{ 'This is a success!' | translate }}</span>
    <span
        class="gamification-blue-title-secondary instruction-popup-wrapper-text instruction-popup-wrapper-text-stones"
        style="margin-bottom: 20px"
        translate="Let's take a closer look at what is needed for what. After all, the main work takes place here, right up to the moment the vacancy is closed."
    ></span>
    <div class="display-flex" style="width: 100%; margin-top: 20px; padding-right: 10px; padding-left: 10px; justify-content: space-between">
        <button ng-click="vm.closeModal();$root.redirectToMain()" class="btn_default btn_grey">
            {{ 'Skip' | translate }}
        </button>
        <button ng-click="vm.closeModal();" class="btn_default btn_orange">
            {{ 'Continue' | translate }}
            <img style="width: 10px; margin-left: 15px" src="/images/sprite/achievements/popup-icons/arrow.svg" alt="" />
        </button>
    </div>
</div>

<div class="gamification-popup-image-wrapper">
    <img class="gamification-popup-image" src="/images/sprite/achievements/popup-icons/check.svg" alt="" />
</div>
<div class="gamification-popup-close">
    <img ng-click="vm.closeModal()" src="/images/sprite/close-orange.svg" alt="" />
</div>
<div class="gamification-popup-dots">
    <div class="gamification-popup-dots-item done">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
    <div class="gamification-popup-dots-item done">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
    <div ng-if="$root.me.recrutRole != 'client'" class="gamification-popup-dots-item done">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
    <div ng-if="$root.me.recrutRole != 'client'" class="gamification-popup-dots-item done">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
    <div ng-if="$root.me.recrutRole != 'client'" class="gamification-popup-dots-item done">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
    <div class="gamification-popup-dots-item active">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
    <div class="gamification-popup-dots-item" style="margin-right: 0">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
</div>
<div style="color: #c0c0c0; font-size: 12px; text-align: center">
    {{ 'You can return to the tutorial in the system chat' | translate }}
    <img style="width: 12px; margin-bottom: 5px" src="/images/sprite/achievements/popup-icons/lamp.svg" alt="" />
</div>
