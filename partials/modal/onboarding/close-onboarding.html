<div class="instruction-popup-wrapper">
    <span class="gamification-blue-title popup-header">{{ 'You did it!' | translate }}</span>
    <span
        class="gamification-blue-title-secondary instruction-popup-wrapper-text instruction-popup-wrapper-text-stones"
        style="margin-bottom: 20px"
        ng-bind-html="'Do not forget to complete quests further, so you can significantly increase the efficiency of your work and truly appreciate all the benefits of CleverStaff.' | translate"
    ></span>
    <div class="display-flex" style="width: 100%; margin-top: 20px; padding-right: 10px; padding-left: 10px; justify-content: center">
        <button ng-click="vm.closeOnboarding()" class="btn_default btn_orange">{{ 'Finish' | translate }}</button>
    </div>
</div>

<div class="gamification-popup-image-wrapper">
    <img class="gamification-popup-image" src="/images/sprite/achievements/popup-icons/two-flags.svg" alt="" />
</div>
<div class="gamification-popup-close">
    <img ng-click="vm.closeOnboarding()" src="/images/sprite/close-orange.svg" alt="" />
</div>
<div class="gamification-popup-dots">
    <div class="gamification-popup-dots-item done">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
    <div class="gamification-popup-dots-item done">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
    <div ng-if="$root.me.recrutRole != 'client'" class="gamification-popup-dots-item done">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
    <div ng-if="$root.me.recrutRole != 'client'" class="gamification-popup-dots-item done">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
    <div ng-if="$root.me.recrutRole != 'client'" class="gamification-popup-dots-item done">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
    <div class="gamification-popup-dots-item done">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
    <div class="gamification-popup-dots-item done" style="margin-right: 0">
        <div class="gamification-popup-dots-item-fill"></div>
    </div>
</div>
