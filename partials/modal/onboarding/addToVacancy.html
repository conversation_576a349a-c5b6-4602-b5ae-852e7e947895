<div class="candidate-add-in-vacancy">
    <div class="modal-header">
        <i ng-click="vm.closeModal()" class="close-btn"></i>
        <h4 class="modal-header-title" style="font-size: 22px !important; color: #202021">
            {{ 'adding_candidate_to_vacancy' | translate }}
        </h4>
    </div>
    <div class="modal-body" style="padding-top: 10px">
        <div ng-if="$root.currentLang == 'en'" class="item">
            <input
                id="candidateAddToVacancy"
                ng-class="{ 'active-onboarding-action-btn': !$root.vacancyInOnboardingOpened }"
                ng-change="errorMessageForAddCandidateInVacancy.show = false"
                placeholder="Enter job title"
                ng-model="test"
                vacancy-autocompleter-onboarding="true"
            />
        </div>
        <div ng-if="$root.currentLang == 'ru'" class="item">
            <input
                ng-class="{ 'active-onboarding-action-btn': !$root.vacancyInOnboardingOpened }"
                ng-change="errorMessageForAddCandidateInVacancy.show = false"
                placeholder="Введите название вакансии"
                ng-model="test"
                vacancy-autocompleter-onboarding="true"
            />
        </div>
        <div ng-if="$root.currentLang == 'ua'" class="item">
            <input
                ng-class="{ 'active-onboarding-action-btn': !$root.vacancyInOnboardingOpened }"
                ng-change="errorMessageForAddCandidateInVacancy.show = false"
                placeholder="Введіть назву вакансії"
                ng-model="test"
                vacancy-autocompleter-onboarding="true"
            />
        </div>
        <div class="item" style="position: relative">
            <textarea
                class="form-control"
                ng-keypress="$root.VacancyStatusFiltered? $root.addVacancyInCandidate(null, null, $event):null"
                ng-model="$root.addCandidateInVacancy.comment"
            ></textarea>
            <!--            <div style="margin-top: 5px; font-size: 12px; color: #999">-->
            <!--                {{ 'Press CTRL+ENTER to send messages' | translate }}-->
            <!--            </div>-->
        </div>
    </div>
    <div class="new-modal-footer" style="background-color: white; justify-content: space-around">
        <button ng-click="vm.closeModal()" class="btn_thin btn_default btn_empty">{{ 'cancel' | translate }}</button>
        <div ng-class="{ 'active-onboarding-action-btn': $root.vacancyInOnboardingSelected }">
            <button
                ng-click="vm.onAddCandidateToVacancy();vm.closeModal()"
                class="btn_thin btn_default btn_success"
                ng-disabled="!$root.vacancyInOnboardingSelected"
            >
                {{ 'add' | translate }}
            </button>
        </div>
    </div>
</div>
