<div ng-if="$root.currentSlide === 1" class="instruction-popup-wrapper">
    <div class="new-pop-up-body">
        <div class="left-block" style="position: relative; top: 37px; left: -20px; height: 400px">
            <img style="width: 400px; position: relative; left: 65px" src="../../../images/sprite/gamification/artemida.png" alt="Artemida" />
        </div>

        <div class="right-block" style="margin-top: 40px">
            <p translate="greetings-i-am-artemis"></p>

            <p style="position: inherit; right: 55px">{{ 'I used to be the best hunter in the world,' | translate }}</p>
            <p style="position: relative; right: -27px; top: -10px">
                {{ 'but now hunting is not as relevant to' | translate }}
            </p>
            <p style="position: relative; right: -62px; top: -20px">
                {{ 'people as it used to be. In modern' | translate }}
            </p>
            <p style="position: relative; right: -80px; top: -30px">
                {{ 'conditions, I embody the art of' | translate }}
            </p>
            <p style="position: relative; top: -38px">{{ 'headhunting for talents. Now Im the' | translate }}</p>
            <p ng-show="$root.currentLang === 'en'" style="position: relative; top: -45px">patroness of recruiting!</p>

            <p style="position: relative; top: -30px">{{ 'I want recruiting to' | translate }}</p>

            <p style="position: relative; top: -23px">{{ 'The CleverStaff team implemented it' | translate }}</p>
        </div>
    </div>

    <div
        class="display-flex"
        style="width: 100%; padding-right: 10px; padding-left: 10px; justify-content: center"
        ng-style="$root.currentLang != 'ru' ? { 'margin-top': '14px' } : { 'margin-top': '0px' }"
    >
        <button ng-click="$root.currentSlide = 2" class="btn_default btn_orange">
            {{ 'Next_2' | translate }}
            <img style="width: 10px; margin-left: 15px" src="/images/sprite/achievements/popup-icons/arrow.svg" alt="" />
        </button>
    </div>
</div>

<div class="gamification-popup-close">
    <img ng-click="$root.closeModal()" src="/images/sprite/close-orange.svg" alt="" />
</div>

<div ng-if="currentSlide === 2" class="instruction-popup-wrapper">
    <div class="new-pop-up-body">
        <div class="left-block" style="position: relative; top: 37px; left: -20px; height: 400px">
            <img style="width: 400px; position: relative; left: 65px" src="../../../images/sprite/gamification/artemida.png" alt="Artemida" />

            <div style="margin-top: 15px; position: relative; left: 20px">
                <span>{{ 'happy_hunts' | translate }}</span>
                <span>{{ 'happy_hunting_my_recruits' | translate }}</span>
            </div>
        </div>

        <div class="right-block" style="margin-top: 32px; padding-right: 30px">
            <p ng-show="$root.currentLang === 'en'" style="position: inherit; right: 55px; margin-top: 10px; top: -21px">
                {{ 'Me and my faithful falcon' | translate }}
            </p>
            <p ng-show="$root.currentLang === 'ua'" style="position: inherit; right: 55px; margin-top: 10px; top: -21px">
                Тут я і мій вірний сокіл відзначатимемо твої успіхи та зростання твого
                <br />
                досвіду.
            </p>
            <p ng-show="$root.currentLang === 'ru'" style="position: inherit; right: 55px; margin-top: 10px; top: -21px">
                Здесь я и мой верный сокол будем отмечать твои успехи и рост твоего
                <br />
                опыта.
            </p>

            <p style="position: inherit; right: 55px; top: -21px">
                {{ 'For your successes you will receive' | translate }}
            </p>
            <p style="position: relative; right: -30px; top: -11px">
                {{ 'Prosperity Stones. Each stone adds' | translate }}
            </p>
            <p style="position: relative; right: -62px; top: -22px">{{ '+1 to one of the five main' | translate }}</p>

            <p style="position: relative; right: -80px; top: -30px">
                {{ 'characteristics of a successful' | translate }}
            </p>
            <p style="position: relative; top: -39px">{{ 'recruiter: Knowledge, Experience,' | translate }}</p>
            <p style="position: relative; top: -47px">{{ 'Empathy, Passion and Charisma!' | translate }}</p>

            <p style="position: relative; top: -35px">
                {{ 'i-will-turn-all-your' | translate }}
                <span ng-if="$root.me.sex">{{ 'god' | translate }}</span>
                <span ng-if="!$root.me.sex">{{ 'goddess' | translate }}</span>
                {{ 'i-will-turn-all-your-sex' | translate }}
            </p>
            <p ng-show="$root.currentLang === 'ua'" style="position: relative; left: 350px; top: -70px">😉</p>

            <p style="position: relative; top: -25px" ng-style="$root.currentLang == 'ua' ? { top: '-55px' } : { top: '-25px' }">
                {{ 'See you soon! I will support you.' | translate }}
            </p>
        </div>
    </div>

    <div
        class="display-flex"
        style="width: 100%; margin-top: 12px; padding-right: 10px; padding-left: 10px; justify-content: center"
        ng-style="$root.currentLang == 'ua' ? { 'margin-top': '-32px' } : { 'margin-top': '12px' }"
    >
        <button ng-click="$root.closeModal();" class="btn_default btn_orange">
            {{ 'Next_2' | translate }}
            <img style="width: 10px; margin-left: 15px" src="/images/sprite/achievements/popup-icons/arrow.svg" alt="" />
        </button>
    </div>
</div>
