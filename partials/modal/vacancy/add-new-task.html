<div class="add-new-task">
    <div class="modal-header">
        <div ng-click="vm.closeModal()" class="close-btn"></div>
        <h4 class="modal-header-title">{{ 'Adding a task' | translate }}</h4>
    </div>

    <div class="modal-body">
        <div class="item">
            <span class="item-title">
                {{ 'Header text' | translate }}
                <span class="form-title-star">&nbsp;*</span>
            </span>
            <input-component
                class="add-task__name"
                current-lang="$root.currentLang"
                placeholder="'Header text'"
                value="vm.newTask.title"
                on-change="(vm.changeTaskName)"
            ></input-component>
        </div>
        <div class="item-row">
            <div class="item width-50">
                <span class="item-title">{{ 'type' | translate }}</span>
                <select-single
                    current-lang="$root.currentLang"
                    options="vm.typeOfTasks"
                    placeholder="'Select task'"
                    translate-options="true"
                    on-change="(vm.changeTabOnTaskForNewTask)"
                    selected-value="vm.typeOfTasksModel"
                    path-to-label="'value'"
                    path-to-value="'value'"
                    is-clearable="false"
                    position="'fixed'"
                ></select-single>
            </div>
            <div class="item width-50">
                <span class="item-title">
                    {{
                        vm.typeOfTasksModel.value === 'Task'
                            ? $root.currentLang === 'en'
                                ? 'Deadline'
                                : ('Run on' | translate)
                            : ('Date and time' | translate)
                    }}
                    <span class="form-title-star">&nbsp;*</span>
                </span>
                <mdp-date-time-picker
                    class="date-field-picker"
                    mdp-auto-switch="true"
                    mdp-format="{{ $root.currentLang === 'en' ? 'M/DD/YYYY h:mm A' : 'DD/MM/YYYY HH:mm' }}"
                    mdp-open-on-click
                    mdp-placeholder="{{ 'Choose date and time' | translate }}"
                    ng-change="vm.checkDateTime(vm.changeDateNewTask, 'changeDateNewTask')"
                    ng-model="vm.changeDateNewTask"
                >
                    <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                </mdp-date-time-picker>
            </div>
        </div>

        <div class="item">
            <span class="item-title">
                {{ 'Add perfomer' | translate }}
                <i
                    class="hint-info-icon__grey"
                    tooltip-placement="top-left"
                    tooltip-class="tooltip-info-black"
                    uib-tooltip="{{ 'A maximum of 5 responsible' | translate }}"
                    aria-hidden="true"
                ></i>
            </span>
            <select-multi-virtualized
                current-lang="$root.currentLang"
                placeholder="'Add perfomer'"
                options="$root.persons"
                show-select-all="false"
                selected-limit="5"
                selected-values="vm.responsiblePersonsEdit"
                path-to-value="$root.useAmericanNameStyle ? 'fullNameEn' : 'fullName'"
                path-to-key="'userId'"
                on-change="(vm.selectResponsible)"
                translate-options="false"
                position="'fixed'"
            ></select-multi-virtualized>
        </div>
        <div class="item">
            <span class="item-title">{{ 'Task descr' | translate }}</span>
            <textarea class="meetComment" ng-model="vm.newTask.text" placeholder="{{ 'Task descr' | translate }}"></textarea>
        </div>
    </div>

    <div class="modal-footer">
        <button-component ng-click="vm.closeModal()" current-lang="$root.currentLang" type="'secondary'" text="'cancel' | translate"></button-component>
        <button-component current-lang="$root.currentLang" text="'Add task' | translate" on-click="(vm.saveNewTask)"></button-component>
    </div>
</div>
