<div class="task-preview">
    <div class="modal-body">
        <div class="task-preview__header">
            <div class="task-preview__header-avatar">
                <kanban-avatar event="vm.previewTask" type="'vacancy-task'"></kanban-avatar>
            </div>
            <div class="task-preview__header-other">
                <div class="task-preview__header-other-title">
                    {{ vm.previewTask.title }}
                </div>
                <div class="task-preview__header-other-time">
                    <mdp-date-time-picker
                        class="date-field-picker item__data-picker item__data-picker_size-short"
                        ng-change="vm.changeDateEditTask()"
                        mdp-auto-switch="true"
                        mdp-format="DD/MM/YYYY HH:mm"
                        mdp-open-on-click
                        mdp-placeholder="{{ vm.previewTask.targetDate | date : 'dd/MM/yyyy HH:mm' }}"
                        ng-model="vm.previewTask.timeModel"
                    >
                        <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                    </mdp-date-time-picker>
                </div>
            </div>
        </div>

        <div class="task-preview__additional">
            <div class="task-preview__additional-items">
                <img src="images/redesign/svg-icons/suitcase.svg" alt="" />
                <span>{{ vm.previewTask.vacancy.position }}</span>
            </div>
        </div>

        <div class="task-preview__type">
            <span class="task-preview__type-type">{{ vm.previewTask.type | translate }}</span>
            <task-status-dropdown-kanban
                ng-click="$event.stopPropagation()"
                class-name="'task-status-dropdown'"
                current-lang="$root.currentLang"
                path-to-value="'status'"
                path-to-label="'status'"
                options="vm.previewTask.vacancyTaskOptions"
                value="vm.previewTask.status"
                on-change="(vm.changeTaskState)"
                additional-arg="vm.previewTask"
            ></task-status-dropdown-kanban>
            <!--            <span class="task-preview__type-circle"></span>-->
            <!--            <span>{{ vm.previewTask.status.value | translate }}</span>-->
        </div>

        <div ng-show="vm.previewTask.text" class="task-preview__descr">
            <span class="task-preview__descr-title">{{ 'Task descr' | translate }}</span>
            <span>{{ vm.previewTask.text }}</span>
        </div>

        <div ng-show="vm.firstComment" class="task-preview__lastComment" ng-class="{ addBorder: !vm.previewTask.text }">
            <div class="task-preview__lastComment-title">
                <responsible-person-pipeline
                    full-name="$root.useAmericanNameStyle ? vm.firstComment.person.fullNameEn : vm.firstComment.person.fullName"
                    user-id="vm.firstComment.person.userId"
                    avatar-id="vm.firstComment.person.avatarId"
                ></responsible-person-pipeline>
                <span>
                    {{ $root.useAmericanNameStyle ? vm.firstComment.person.fullNameEn : vm.firstComment.person.fullName }}
                </span>
            </div>
            <div class="task-preview__lastComment-comment">{{ vm.firstComment.text }}</div>
        </div>

        <div class="task-preview__comment" ng-class="{ addBorder: !vm.firstComment && !vm.previewTask.text }">
            <div ng-click="vm.showComment = !vm.showComment" class="task-preview__comment-title">
                <span>{{ 'comment' | translate }}</span>
                <img ng-class="{ up: vm.showComment }" src="images/redesign/svg-icons/chevron-down.svg" alt="" />
            </div>
            <div ng-show="vm.showComment" class="task-preview__comment-add">
                <textarea ng-model="vm.addComment.comment" placeholder="{{ 'comment' | translate }}"></textarea>
                <button-component
                    ng-click="vm.addCommentForTask(vm.previewTask)"
                    size="'small'"
                    type="'secondary'"
                    text="'add_comment' | translate"
                    current-lang="$root.currentLang"
                ></button-component>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button-with-icon
            ng-click="vm.taskActions(vm.previewTask.taskId)"
            class="dropdown-half"
            type="'secondary'"
            size="'small'"
            icon-name="'text'"
            text="'All actions-v2' | translate"
            current-lang="$root.currentLang"
        ></button-with-icon>
        <button-with-icon
            ng-click="vm.showModalEditTaskToCandidate(vm.previewTask)"
            class="dropdown-half"
            type="'secondary'"
            size="'small'"
            icon-name="'pencil'"
            text="'Edited_1' | translate"
            current-lang="$root.currentLang"
        ></button-with-icon>
    </div>
</div>
