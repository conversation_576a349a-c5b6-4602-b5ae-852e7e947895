<div class="vacancy-application-modal">
    <div class="modal-header">
        <div ng-click="vm.closeModal()" class="close-btn"></div>
        <h4 ng-if="!vm.isEditModal" class="modal-header-title">{{ 'Fill out a vacancy application' | translate }}</h4>
        <h4 ng-if="vm.isEditModal" class="modal-header-title">{{ 'Edit vacancy application' | translate }}</h4>
    </div>

    <div class="modal-body">
        <div>
            <div class="field-block">
                <p class="field-block__title">
                    {{ 'vacancy_name' | translate }}
                    <span class="form-title-star">*</span>
                </p>
                <input-component
                    placeholder="'enter job title'"
                    value="vm.vacancy.position"
                    on-change="(vm.onChangeVacancyName)"
                    on-blur="(vm.onBlurVacancyNameInput)"
                    is-error="vm.errorFields.position"
                ></input-component>
                <span ng-if="vm.errorFields.position && vm.isRequiredFieldEmpty(vm.vacancy.position, 'position')" class="error-text">
                    {{ 'enter job title' | translate }}
                </span>
                <span ng-if="vm.errorFields.position && !vm.isRequiredFieldEmpty(vm.vacancy.position, 'position')" class="error-text">
                    {{ 'text_should_be_no_longer_than_300_characters' | translate }}
                </span>
            </div>
        </div>

        <div ng-if="!vm.typeVacancy" class="fd-row">
            <div class="field-block">
                <div class="industry-radio">
                    <div class="industry-radio__item">
                        <radio-component value="'IT'" on-change="(vm.onChangeIndustry)" checked="vm.industry === 'IT'"></radio-component>
                        <span>{{ 'Tech vacancy' | translate }}</span>
                    </div>

                    <div class="industry-radio__item">
                        <radio-component value="'noIT'" on-change="(vm.onChangeIndustry)" checked="vm.industry === 'noIT'"></radio-component>
                        <span>{{ 'Non-tech' | translate }}</span>
                    </div>
                </div>

                <span ng-if="vm.errorFields.industry" class="error-text">{{ 'select industry' | translate }}</span>
            </div>
        </div>

        <div ng-if="vm.industry === 'IT' && vm.categories.length" class="fd-row">
            <div class="field-block">
                <p class="field-block__title">
                    {{ 'category' | translate }}
                    <span class="form-title-star">*</span>
                </p>
                <select-single
                    placeholder="'Select category'"
                    options="vm.categories"
                    selected-value="vm.vacancy.category"
                    on-change="(vm.onChangeCategory)"
                    is-clearable="false"
                    is-error="vm.errorFields.category"
                    position="vm.vacancy.category ? 'absolute' : 'fixed'"
                ></select-single>
                <span ng-if="vm.errorFields.category" class="error-text">{{ 'choose_category' | translate }}</span>
            </div>

            <div class="field-block">
                <p class="field-block__title">{{ 'role' | translate }}</p>
                <select-single
                    placeholder="'Select role'"
                    options="vm.allRoles"
                    translate-options="true"
                    selected-value="vm.vacancy.role"
                    on-change="(vm.onChangeRole)"
                    is-error="vm.errorFields.role"
                    position="vm.vacancy.category ? 'absolute' : 'fixed'"
                ></select-single>
            </div>
        </div>

        <div ng-if="(vm.industry && vm.industry !== 'IT') || (vm.industry === 'IT' && vm.vacancy.category)" class="blockIT">
            <div class="fd-row">
                <div ng-if="vm.allFieldsForApplication.includes('experience')" class="field-block">
                    <p class="field-block__title">
                        {{ 'Required experience' | translate }}
                        <span ng-if="vm.requiredFieldsForApplication.includes('experience')" class="form-title-star">*</span>
                    </p>
                    <select-single
                        placeholder="'Select experience'"
                        options="vm.experiences"
                        translate-options="true"
                        selected-value="vm.vacancy.experience"
                        on-change="(vm.onChangeExperience)"
                        is-error="vm.errorFields.experience"
                    ></select-single>
                    <span ng-if="vm.errorFields.experience" class="error-text">{{ 'Empty mandatory field' | translate }}</span>
                </div>

                <div ng-if="vm.allFieldsForApplication.includes('salary')" class="field-block">
                    <p class="field-block__title">
                        {{ 'salary' | translate }}
                        <span ng-if="vm.requiredFieldsForApplication.includes('salary')" class="form-title-star">*</span>
                    </p>
                    <div id="salaryBlock" class="salary-block">
                        <div class="salary-block__input-group" ng-class="{ 'salary-block__input-group_error': vm.salaryError }">
                            <input
                                class="salary-block__input"
                                placeholder="{{ 'from' | translate }}"
                                ng-model="vm.vacancy.salaryFrom"
                                ng-change="vm.vacancy.salaryFrom = vm.numberOnlyInputHandler(vm.vacancy.salaryFrom)"
                            />
                            <div class="salary-block__vertical-divider"></div>
                            <input
                                class="salary-block__input"
                                placeholder="{{ 'age to' | translate }}"
                                ng-model="vm.vacancy.salaryTo"
                                ng-change="vm.vacancy.salaryTo = vm.numberOnlyInputHandler(vm.vacancy.salaryTo)"
                            />
                        </div>

                        <select-single
                            class="salary-block__currency-select"
                            placeholder="''"
                            options="vm.currencies"
                            translate-options="true"
                            selected-value="vm.vacancy.currency"
                            on-change="(vm.onChangeCurrency)"
                            is-clearable="false"
                        ></select-single>
                    </div>

                    <p ng-if="vm.vacancy.salaryFrom >= 999999999 || vm.vacancy.salaryTo >= 999999999" class="error-text">
                        {{ 'Unacceptably large number' | translate }}
                    </p>
                    <p ng-if="vm.vacancy.salaryFrom === 0 || vm.vacancy.salaryTo === 0" class="error-text">
                        {{ 'enter valid data greater than zero' | translate }}
                    </p>

                    <div ng-if="vm.errorFields.salary" class="error-text">
                        <span ng-if="vm.isRequiredFieldEmpty(vm.vacancy.salary, 'salary')">{{ 'Empty mandatory field' | translate }}</span>
                    </div>
                </div>
            </div>

            <div ng-if="vm.allFieldsForApplication.includes('skills')" class="fields-group">
                <p class="text-secondary">
                    {{ 'Skills' | translate }}
                    <span ng-if="vm.requiredFieldsForApplication.includes('skills')" class="form-title-star">*</span>
                </p>

                <div class="multi-control-block" ng-repeat="skillField in vm.skillsModel track by $index">
                    <div class="multi-control-block__dropdowns-wrapper">
                        <div class="field-block">
                            <p ng-if="$index === 0" class="field-block__title">{{ 'Skill' | translate }}</p>
                            <select-single-virtualized-creatable
                                placeholder="'Select or create skill'"
                                options="vm.skillsOptions"
                                selected-value="skillField.skill"
                                is-searchable="true"
                                is-grouped="true"
                                on-change="(vm.onChangeSkill)"
                                additional-arg="$index"
                                no-options-message="'You have no skills. You can add them.'"
                            ></select-single-virtualized-creatable>
                        </div>

                        <div class="field-block">
                            <div ng-if="$index === 0" class="field-block__title">{{ 'Skill experience' | translate }}</div>
                            <select-single
                                placeholder="'Select role'"
                                translate-options="true"
                                is-clearable="false"
                                default-value="vm.skillExperienceOptions[0]"
                                selected-value="skillField.experience"
                                options="vm.skillExperienceOptions"
                                on-change="(vm.onChangeSkillExperience)"
                                additional-arg="$index"
                            ></select-single>
                        </div>
                    </div>
                    <div class="multi-control-block__toggle" ng-style="$index === 0 && { 'margin-top': '24px' }">
                        <span ng-click="skillField.willBePlus = false" class="multi-control-block__toggle-label">
                            {{ 'Required' | translate }}
                        </span>
                        <toggle-component
                            ng-if="skillField"
                            class="toggle-component-global"
                            on-change="(vm.onChangeSkillMustHave)"
                            checked="!skillField.mustHave"
                            additional-arg="$index"
                            always-checked-style="true"
                        ></toggle-component>
                        <span ng-click="skillField.willBePlus = true" class="multi-control-block__toggle-label">
                            {{ 'Will be a plus' | translate }}
                        </span>
                    </div>
                    <img
                        ng-click="vm.removeSkillField($index)"
                        class="multi-control-block__remove-icon"
                        ng-style="$index === 0 && { 'margin-top': '24px' }"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>
                <button-with-icon
                    class-name="'full-width'"
                    on-click="(vm.addNewSkillField)"
                    type="'secondary'"
                    text="'add_new_skill' | translate"
                    icon-name="'plus'"
                ></button-with-icon>

                <span ng-if="vm.errorFields.skills" class="error-text">{{ 'add_skill' | translate }}</span>
            </div>

            <div ng-if="vm.allFieldsForApplication.includes('languages')" class="fields-group">
                <p class="text-secondary">
                    {{ 'languages' | translate }}
                    <span ng-if="vm.requiredFieldsForApplication.includes('languages')" class="form-title-star">*</span>
                </p>

                <div class="multi-control-block" ng-repeat="languageField in vm.languages track by $index">
                    <div class="field-block">
                        <p ng-if="$index === 0" class="field-block__title">{{ 'Language' | translate }}</p>
                        <select-single
                            placeholder="'Select language'"
                            options="vm.languagesOptions"
                            selected-value="languageField.name"
                            on-change="(vm.onChangeLanguage)"
                            additional-arg="$index"
                        ></select-single>
                    </div>
                    <div class="field-block">
                        <p ng-if="$index === 0" class="field-block__title">{{ 'level_lang' | translate }}</p>
                        <select-single
                            placeholder="'selectLevel'"
                            translate-options="true"
                            is-clearable="false"
                            selected-value="languageField.level"
                            options="vm.languageLevelOptions"
                            on-change="(vm.onChangeLanguageLevel)"
                            additional-arg="$index"
                        ></select-single>
                    </div>
                    <img
                        ng-click="vm.removeLanguageField($index)"
                        class="multi-control-block__remove-icon"
                        ng-style="$index === 0 && { 'margin-top': '24px' }"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>

                <button-with-icon
                    class-name="'full-width'"
                    on-click="(vm.addNewLanguageField)"
                    type="'secondary'"
                    text="'add_new_lang' | translate"
                    icon-name="'plus'"
                ></button-with-icon>

                <span ng-if="vm.errorFields.languages" class="error-text">{{ 'Empty mandatory field' | translate }}</span>
            </div>

            <div ng-if="vm.allFieldsForApplication.includes('employmentType') || vm.allFieldsForApplication.includes('numberOfPositions')" class="fd-row">
                <div ng-if="vm.allFieldsForApplication.includes('employmentType')" class="field-block">
                    <p class="field-block__title">
                        {{ 'employment_type' | translate }}
                        <span ng-if="vm.requiredFieldsForApplication.includes('employmentType')" class="form-title-star">*</span>
                    </p>
                    <select-single
                        placeholder="'Select employment type'"
                        options="vm.employmentTypeOptions"
                        translate-options="true"
                        selected-value="vm.vacancy.employmentType"
                        on-change="(vm.onChangeEmploymentType)"
                        is-clearable="false"
                        is-error="vm.errorFields.category"
                    ></select-single>
                    <span ng-if="vm.errorFields.employmentType" class="error-text">{{ 'Empty mandatory field' | translate }}</span>
                </div>

                <div ng-if="vm.allFieldsForApplication.includes('numberOfPositions')" class="field-block">
                    <p class="field-block__title">
                        {{ 'Number of positions' | translate }}
                        <span ng-if="vm.requiredFieldsForApplication.includes('numberOfPositions')" class="form-title-star">*</span>
                    </p>
                    <input-component
                        placeholder="'Select number of positions'"
                        value="vm.vacancy.numberOfPositions"
                        on-change="(vm.onChangeNumberOfPositions)"
                        is-error="vm.errorFields.numberOfPositions"
                    ></input-component>
                    <div ng-if="vm.errorFields.numberOfPositions" class="error-text">
                        <span ng-if="!vm.isRequiredFieldEmpty(vm.vacancy.numberOfPositions, 'numberOfPositions')">
                            {{ 'The number of positions should not exceed 100' | translate }}
                        </span>
                        <span ng-if="vm.isRequiredFieldEmpty(vm.vacancy.numberOfPositions, 'numberOfPositions')">
                            {{ 'Empty mandatory field' | translate }}
                        </span>
                    </div>
                </div>
            </div>

            <div ng-if="vm.allFieldsForApplication.includes('regionId')" class="fd-row">
                <div class="field-block">
                    <p class="field-block__title">
                        {{ 'Country' | translate }}
                        <span
                            ng-if="vm.requiredFieldsForApplication.includes('regionId') || vm.vacancy.employmentType.value !== 'remote'"
                            class="form-title-star"
                        >
                            *
                        </span>
                    </p>
                    <select-single-virtualized
                        placeholder="'Choose the country'"
                        options="vm.countriesOptions"
                        selected-value="vm.vacancy.country"
                        is-searchable="true"
                        is-error="vm.errorFields.country"
                        on-change="(vm.onChangeCountry)"
                    ></select-single-virtualized>
                    <span ng-if="errorFields.country" class="error-text">{{ 'Choose the country' | translate }}</span>
                </div>
                <div class="field-block">
                    <p class="field-block__title">
                        {{ 'City' | translate }}
                        <span
                            ng-if="vm.requiredFieldsForApplication.includes('regionId') || vm.vacancy.employmentType.value !== 'remote'"
                            class="form-title-star"
                        >
                            *
                        </span>
                    </p>
                    <select-single-virtualized
                        placeholder="'Choose the city'"
                        options="vm.citiesOptions"
                        selected-value="vm.vacancy.city"
                        is-searchable="true"
                        is-error="vm.errorFields.city"
                        on-change="(vm.onChangeCity)"
                        disabled="!vm.vacancy.country"
                        filter-match-from="'start'"
                    ></select-single-virtualized>
                    <span ng-if="errorFields.city" class="error-text">{{ 'Choose the city' | translate }}</span>
                </div>
            </div>

            <div ng-if="vm.allFieldsForApplication.includes('dateFinish') || vm.allFieldsForApplication.includes('datePayment')" class="fd-row">
                <div ng-if="vm.allFieldsForApplication.includes('dateFinish')" class="field-block">
                    <p class="field-block__title">
                        {{ 'deadline' | translate }}
                        <span ng-if="vm.requiredFieldsForApplication.includes('dateFinish')" class="form-title-star">*</span>
                    </p>
                    <div class="adv-search-filters_date-field">
                        <mdp-date-picker
                            class="date-field-picker"
                            mdp-format="{{ $root.currentLang === 'en' ? 'M/DD/YYYY' : 'DD/MM/YYYY' }}"
                            mdp-open-on-click
                            mdp-placeholder="{{ 'Choose date' | translate }}"
                            ng-model="vm.dateFinish"
                            ng-change="vm.errorFields.dateFinish = false"
                        >
                            <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                            <img
                                ng-if="vm.dateFinish"
                                ng-click="vm.dateFinish = null"
                                class="date-field-picker__clear-icon"
                                alt=""
                                src="/images/redesign/svg-icons/close.svg"
                            />
                        </mdp-date-picker>
                    </div>
                    <span ng-if="vm.errorFields.dateFinish" class="error-text">{{ 'Empty mandatory field' | translate }}</span>
                </div>

                <div ng-if="vm.allFieldsForApplication.includes('datePayment')" class="field-block">
                    <p class="field-block__title">
                        {{ 'payment_date' | translate }}
                        <span ng-if="vm.requiredFieldsForApplication.includes('datePayment')" class="form-title-star">*</span>
                    </p>

                    <div class="adv-search-filters_date-field">
                        <mdp-date-picker
                            class="date-field-picker"
                            mdp-format="{{ $root.currentLang === 'en' ? 'M/DD/YYYY' : 'DD/MM/YYYY' }}"
                            mdp-open-on-click
                            mdp-placeholder="{{ 'Choose date' | translate }}"
                            ng-model="vm.datePayment"
                            ng-change="vm.errorFields.datePayment = false"
                        >
                            <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                            <img
                                ng-if="vm.datePayment"
                                ng-click="vm.datePayment = null"
                                class="date-field-picker__clear-icon"
                                alt=""
                                src="/images/redesign/svg-icons/close.svg"
                            />
                        </mdp-date-picker>
                    </div>
                    <span ng-if="vm.errorFields.datePayment" class="error-text">{{ 'Empty mandatory field' | translate }}</span>
                </div>
            </div>

            <div ng-if="vm.allFieldsForApplication.includes('budget') || vm.allFieldsForApplication.includes('priority')" class="fd-row">
                <div ng-if="vm.allFieldsForApplication.includes('budget')" class="field-block">
                    <p class="field-block__title">
                        {{ 'budget' | translate }}
                        <span ng-if="vm.requiredFieldsForApplication.includes('budget')" class="form-title-star">*</span>
                    </p>
                    <input-component
                        type="'number'"
                        placeholder="'Enter budget of vacancy'"
                        value="vm.vacancy.budget"
                        on-change="(vm.onChangeBudget)"
                        is-error="vm.errorFields.budget"
                        on-blur="(vm.checkFieldError)"
                        additional-arg="[vm.vacancy.budget, 'budget']"
                    ></input-component>

                    <div ng-if="vm.errorFields.budget" class="error-text">
                        <span ng-if="!vm.isRequiredFieldEmpty(vm.vacancy.budget, 'budget')">
                            {{ 'text_should_be_no_longer_than_50_characters' | translate }}
                        </span>
                        <span ng-if="vm.isRequiredFieldEmpty(vm.vacancy.budget, 'budget')">{{ 'Empty mandatory field' | translate }}</span>
                    </div>
                </div>

                <div ng-if="vm.allFieldsForApplication.includes('priority')" class="field-block">
                    <p class="field-block__title">
                        {{ 'Priority of vacancy' | translate }}
                        <span ng-if="vm.requiredFieldsForApplication.includes('priority')" class="form-title-star">*</span>
                    </p>
                    <select-single
                        placeholder="'Select priority of vacancy'"
                        options="vm.prioritiesOptions"
                        default-value="vm.prioritiesOptions[1]"
                        translate-options="true"
                        selected-value="vm.vacancy.priority"
                        on-change="(vm.onChangePriority)"
                        is-error="vm.errorFields.priority"
                    ></select-single>
                    <span ng-if="vm.errorFields.priority" class="error-text">{{ 'Empty mandatory field' | translate }}</span>
                </div>
            </div>

            <div ng-if="vm.vacancy.customFields.length" class="custom-fields-section">
                <div ng-show="customField.state == 'A'" ng-repeat="customField in vm.vacancy.customFields track by customField.fieldId">
                    <div class="field-block__title">
                        <p>
                            {{ customField.title }}
                            <span ng-if="customField.mandatory" class="form-title-star">*</span>
                        </p>
                    </div>

                    <textarea
                        ng-if="customField.type == 'string'"
                        class="form-control"
                        cf="true"
                        placeholder="{{ 'Character Restriction' | translate }}"
                        ng-change="vm.onChangeStringCustomField(customField.value, $index)"
                        ng-model="customField.value"
                        ckeditor
                        options="ckEditorOptions"
                    ></textarea>

                    <select-single-virtualized
                        ng-if="customField.type == 'select'"
                        is-searchable="customField.params.length > 10"
                        placeholder="'Select option'"
                        options="customField.params"
                        path-to-label="'value'"
                        on-change="(vm.onChangeSelectCustomField)"
                        selected-value="customField.value"
                        additional-arg="$index"
                    ></select-single-virtualized>

                    <mdp-date-picker
                        ng-if="customField.type == 'date'"
                        class="date-field-picker"
                        mdp-format="{{ $root.currentLang === 'en' ? 'M/DD/YYYY' : 'DD/MM/YYYY' }}"
                        mdp-open-on-click
                        mdp-placeholder="{{ 'date' | translate }}"
                        ng-model="customField.value"
                        ng-change="vm.onChangeDateCustomField(customField, customField.value)"
                    >
                        <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                        <img
                            ng-if="customField.value"
                            ng-click="customField.value = null;datepickerOfCustomEdit = null; vm.checkDateTime(customField.fieldId, customField.fieldValue.fieldValueId, null, 'datepickerOfCustomEdit')"
                            class="date-field-picker__clear-icon"
                            alt=""
                            src="/images/redesign/svg-icons/close.svg"
                        />
                    </mdp-date-picker>

                    <mdp-date-time-picker
                        ng-if="customField.type == 'datetime'"
                        class="date-field-picker"
                        mdp-auto-switch="true"
                        mdp-placeholder="{{ 'Choose date and time' | translate }}"
                        mdp-format="{{ $root.currentLang === 'en' ? 'M/DD/YYYY h:mm A' : 'DD/MM/YYYY HH:mm' }}"
                        mdp-open-on-click
                        mdp-placeholder="{{ customField.placeholder }}"
                        name="{{ customField.fieldId }}"
                        ng-model="customField.value"
                        ng-change="vm.onChangeDateCustomField(customField, customField.value)"
                    >
                        <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                        <img
                            ng-if="customField.value"
                            ng-click="customField.value = null; datepickerOfCustomEditTime = null; vm.checkDateTime(customField.fieldId, customField.fieldValue.fieldValueId, null, 'datepickerOfCustomEditTime')"
                            class="date-field-picker__clear-icon"
                            alt=""
                            src="/images/redesign/svg-icons/close.svg"
                        />
                    </mdp-date-time-picker>

                    <span ng-if="customField.mandatory && customField.error" class="error-text">{{ 'Empty mandatory field' | translate }}</span>
                </div>
            </div>

            <div ng-if="vm.allFieldsForApplication.includes('responsibilities')" class="">
                <p class="field-block__title">
                    {{ 'Job description' | translate }}
                    <span ng-if="vm.requiredFieldsForApplication.includes('responsibilities')" class="form-title-star">*</span>
                </p>
                <textarea
                    ckeditor
                    ng-blur="vm.checkFieldError(vm.vacancy.responsibilities,'responsibilities')"
                    ng-model="vm.vacancy.responsibilities"
                    ng-change="vm.errorFields.responsibilities = false"
                ></textarea>
                <span ng-if="vm.errorFields.responsibilities" class="error-text">{{ 'Empty mandatory field' | translate }}</span>
            </div>

            <div ng-if="vm.allFieldsForApplication.includes('companyDescription')" class="">
                <p class="field-block__title">
                    {{ 'companyDescription' | translate }}
                    <span ng-if="vm.requiredFieldsForApplication.includes('companyDescription')" class="form-title-star">*</span>
                </p>
                <textarea
                    ckeditor
                    ng-blur="vm.checkFieldError(vm.vacancy.companyDescription,'companyDescription')"
                    ng-model="vm.vacancy.companyDescription"
                    ng-change="vm.errorFields.companyDescription = false"
                ></textarea>
                <span ng-if="vm.errorFields.companyDescription" class="error-text">{{ 'Empty mandatory field' | translate }}</span>
            </div>

            <div ng-if="vm.allFieldsForApplication.includes('offer')" class="">
                <p class="field-block__title">
                    {{ 'offer' | translate }}
                    <span ng-if="vm.requiredFieldsForApplication.includes('offer')" class="form-title-star">*</span>
                </p>
                <textarea
                    ckeditor
                    ng-blur="vm.checkFieldError(vm.vacancy.offer,'offer')"
                    ng-model="vm.vacancy.offer"
                    ng-change="vm.errorFields.offer = false"
                ></textarea>
                <span ng-if="vm.errorFields.offer" class="error-text">{{ 'Empty mandatory field' | translate }}</span>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button-component ng-click="vm.closeModal()" type="'secondary'" text="'cancel' | translate"></button-component>
        <button-component ng-click="vm.saveVacancyApplication()" text="'save' | translate"></button-component>
    </div>
</div>
