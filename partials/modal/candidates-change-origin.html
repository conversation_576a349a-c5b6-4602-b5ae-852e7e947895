<div class="candidate-add-in-vacancy">
    <div class="modal-header">
        <div ng-click="$root.closeModal()" class="close-btn"></div>
        <h4 class="modal-header-title">
            {{ 'Change the candidates source' | translate }}
        </h4>
    </div>
    <div class="modal-body modal-body">
        <div class="item mrgBottom">
            <select-single-virtualized
                placeholder="'source_is'"
                options="originsOptions"
                selected-value="newCandidatesOrigin"
                on-change="(candidatesChangeOriginModel)"
                is-searchable="true"
                position="'fixed'"
            ></select-single-virtualized>
        </div>
    </div>
    <div class="modal-footer">
        <button-component ng-click="$root.closeModal()" type="'secondary'" text="'cancel' | translate "></button-component>
        <button-component ng-click="$parent.candidatesChangeOrigin()" text="'save' | translate "></button-component>
    </div>
</div>
