<div class="modal-header">
    <span>{{ 'Export to Excel' | translate }}</span>
    <span ng-if="$root.me.recrutRole == 'admin' && !vm.loadingExcel">({{ 'up to 30 000 profiles' | translate }})</span>
    <span ng-if="$root.me.recrutRole == 'recruter' && !vm.loadingExcel">({{ 'up to 10 000 profiles' | translate }})</span>
    <div ng-click="vm.closeModal() || $root.closeModal()" class="close-btn"></div>
</div>
<div class="modal-body">
    <div class="excel-popup-wrapper">
        <div class="export-history" ng-hide="vm.loadingExcel">
            <div class="export-image">
                <img src="/images/sprite/excel-history.svg" alt="{{ 'Export to Excel' | translate }}" />
            </div>
            <a ng-click="vm.viewExcelHistory()" href="#/excelHistory">{{ 'Export History' | translate }}</a>
        </div>
        <div class="export">
            <div ng-show="vm.loadingExcel" class="exporting">
                <span>
                    {{ 'Preparing the candidates for the export, you can download it in Export history when the export completes' | translate }}
                    <a href="{{ '#/excelHistory' }}">{{ 'Export history' | translate }}</a>
                </span>
            </div>

            <div class="export-type" ng-hide="vm.loadingExcel" ng-init="excelExportType = 'candidates'">
                <span class="export-type-title">{{ 'Please choose the candidate database export option' | translate }}</span>
                <div class="export-type-item">
                    <radio-component
                        style="height: 20px"
                        value="'candidates'"
                        name="excel-types"
                        on-change="(vm.onChangeRadio)"
                        checked="$root.excelExportType === 'candidates'"
                    ></radio-component>
                    <span ng-click="vm.onChangeRadio('candidates')">{{ 'Candidates' | translate }}</span>
                </div>
                <div class="export-type-item">
                    <radio-component
                        style="height: 20px"
                        value="'all'"
                        name="excel-types"
                        on-change="(vm.onChangeRadio)"
                        checked="$root.excelExportType === 'all'"
                    ></radio-component>
                    <span ng-click="vm.onChangeRadio('all')">{{ 'Candidates + comments + history' | translate }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button-component
        ng-click="(!vm.loadingExcel)? vm.exportToExcel() : (vm.closeModal() || $root.closeModal())"
        text="!vm.loadingExcel ? ('export' | translate) : ('Close' | translate)"
    ></button-component>
</div>
