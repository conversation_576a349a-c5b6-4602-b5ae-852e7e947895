<div class="delete-vacancy">
    <div class="modal-header">
        <div ng-click="$root.closeModal()" class="close-btn"></div>
        <h4 class="modal-header-title">
            <span>{{ deleteFromSystem | displayDeletedCandidatesNames : selectedCandidatesName }}</span>
        </h4>
    </div>
    <div class="modal-body">
        <textarea class="form-control" placeholder="{{ 'write_a_comment' | translate }}" ng-model="deleteCandidatesComment"></textarea>

        <div ng-if="$root.me.recrutRole === 'admin' || $root.me.recrutRole === 'recruter'" class="delete-candidates">
            <checkbox-component on-change="(onChangeDeleteFromSystem)" is-checked="deleteFromSystem"></checkbox-component>
            <span ng-click="onChangeDeleteFromSystem({target: {checked: !deleteFromSystem}})" class="delete-candidate-checkbox-text">
                {{ selectedCandidatesName.length > 1 ? ('Remove candidates from a system' | translate) : ('Remove candidate from a system' | translate) }}
                <span class="gdpr-label">GDPR</span>
            </span>
            <i
                class="hint-info-icon__grey"
                tooltip-placement="bottom-left"
                tooltip-class="tooltip-info-black"
                uib-tooltip-html="$root.tooltips.deleteCandidatesFromSystem"
                tooltip-append-to-body="true"
                aria-hidden="true"
            ></i>
        </div>
    </div>
    <div class="modal-footer">
        <button-component ng-click="$root.closeModal()" type="'secondary'" text="'cancel' | translate "></button-component>
        <button-component
            ng-click="deleteCandidates(deleteFromSystem, deleteCandidatesComment)"
            type="'danger'"
            text="'Delete' | translate "
        ></button-component>
    </div>
</div>
