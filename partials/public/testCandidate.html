<div class="container-fluid block-company-public-vacancy block-public-test block-public">
    <div id="toggle-company-block" ng-show="currentTab === 'start_test' && checkPreviousAnswers" class="row companyInfo" toggle-company-block>
        <img ng-if="companyInfo.logo" class="logo" ng-src="{{ serverAddress }}/getlogo?id={{ companyInfo.logo }}" alt="" />
        <div class="info">
            <h2 class="cutElementTextWidth">{{ companyInfo.name }}</h2>

            <div ng-show="companyInfo.website" class="info--site cutElementTextWidth">
                <img src="/images/sprite/public-vacancy/website.svg" alt="" />
                <a href="{{ companyPublicInfo.companyWebSite }}" target="_blank">{{ companyInfo.website }}</a>
            </div>

            <div ng-show="companyInfo.fb" class="info--site cutElementTextWidth">
                <img src="/images/sprite/public-vacancy/facebook.svg" alt="" />
                <a href="{{ companyPublicInfo.fb }}" target="_blank">{{ companyInfo.fb }}</a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <div class="block-intro">
                <div class="text-center intro-text">
                    <span class="vacancy">{{ 'Test' | translate }}</span>
                    <h2>{{ getTestCandidate.testName }}</h2>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div ng-show="checkPreviousAnswers" class="col-md-12">
            <div ng-show="currentTab == 'start_test' && showFirstTest && !firstTestQuestion && !showEndMessage" class="block-info answers">
                <div ng-show="getTestCandidate.description" class="block-accent">
                    <span class="content-description">{{ getTestCandidate.description }}</span>
                </div>

                <div class="info-additional">
                    <span ng-show="getTestCandidate.timeLimit > 0" class="time-limit">
                        <span class="bold">{{ 'Time limit' | translate }}:</span>
                        <span ng-show="getTestCandidate.timeLimit" class="allTime">{{ getTestCandidate.timeLimit | timeDeclination }}.</span>
                        <i
                            class="tooltip-icon"
                            tooltip-class="tooltip-outer"
                            uib-tooltip="{{
                                'The timer will be displayed on the test page.In case you do not finish the test before the deadline, the test page will be closed, all the data entered will be saved and sent to the recruiter'
                                    | translate
                            }}"
                            aria-hidden="true"
                        ></i>
                    </span>
                    <span class="warning">
                        {{ 'Once you pass the test, your answers will be sent to a recruiter. You have only one attempt to pass the test' | translate }}
                    </span>
                    <span class="warning-triangle">{{ 'For correct work of tests module' | translate }}</span>
                </div>

                <button ng-click="startTestFunc('first_test')" class="btn-primary-new apply small send-test">
                    {{ 'Start the test' | translate }}
                </button>
            </div>

            <div ng-show="currentTab == 'first_test' && hideTest && firstTestQuestion && !showEndMessage" class="block-info question">
                <div class="question-header">
                    <div ng-show="initialCountdown > 0" class="time-limit">
                        <span class="disabled-color">{{ 'Time left' | translate }}:</span>
                        <span ng-if="currentLang == 'ru' && countdown" class="time">
                            {{ countdown | secondsToDateTime | date : "HH 'ч.' mm 'мин.' ss 'сек'" }}.
                        </span>
                        <span ng-if="currentLang == 'ua' && countdown" class="time">
                            {{ countdown | secondsToDateTime | date : "HH 'г.' mm 'хв.' ss 'сек'" }}.
                        </span>
                        <span ng-if="currentLang == 'en' && countdown" class="time">
                            {{ countdown | secondsToDateTime | date : "HH 'hr' mm 'min' ss 'sec'" }}.
                        </span>
                    </div>
                    <div class="questions-amount">
                        <span>{{ 'Question' | translate }}&nbsp{{ firstPage }}/{{ firstTestQuestion.allPageCount }}</span>
                    </div>
                </div>

                <h3 ng-if="firstTestQuestion.object.question.text" class="question-name">
                    {{ firstTestQuestion.object.question.text }}
                </h3>

                <div
                    class="question-answers block-accent"
                    ng-class="{
                        center: firstTestQuestion.object.question.answerType === 'task_question' && !firstTestQuestion.object.question.imageId,
                        blocked: isQuestionLoading
                    }"
                >
                    <div class="multi-answers" ng-switch="firstTestQuestion.object.question.answerType">
                        <p ng-switch-when="one_answer">{{ 'Choose one option' | translate }}</p>
                        <span
                            ng-click="!isQuestionLoading && checkOneAnswer(selectAnswer, $index);$event.preventDefault();"
                            ng-switch-when="one_answer"
                            ng-repeat="selectAnswer in firstTestQuestion.object.question.variantsArray track by $index"
                        >
                            <input
                                id="single-{{ selectAnswer }}"
                                class="custom"
                                type="radio"
                                ng-disabled="isQuestionLoading"
                                name="answers"
                                ng-checked="checkAnswerText === selectAnswer"
                            />
                            <label for="single-{{ selectAnswer }}">{{ selectAnswer }}</label>
                        </span>

                        <p ng-switch-when="few_answers">{{ 'Choose multiple answers' | translate }}</p>
                        <span
                            ng-click="!isQuestionLoading && checkFewAnswer(selectAnswer, $index);$event.stopPropagation();$event.preventDefault();"
                            ng-switch-when="few_answers"
                            ng-checked="variantsAnswer"
                            ng-repeat="selectAnswer in firstTestQuestion.object.question.variantsArray track by $index"
                        >
                            <input
                                id="few-{{ selectAnswer }}"
                                class="custom"
                                type="checkbox"
                                ng-disabled="isQuestionLoading"
                                ng-checked="variantsAnswer.indexOf(selectAnswer) != -1"
                            />
                            <label for="few-{{ selectAnswer }}">{{ selectAnswer }}</label>
                        </span>

                        <textarea
                            id="answersText"
                            class="form-control"
                            ng-switch-when="task_question"
                            ng-disabled="isQuestionLoading"
                            ng-change="!isQuestionLoading && textAnswer(textAnswers)"
                            ng-model="textAnswers"
                            placeholder="{{ 'Enter your answer' | translate }}"
                            cols="50"
                            rows="10"
                        ></textarea>
                    </div>
                    <div ng-show="firstTestQuestion.object.question.imageId" class="question-image">
                        <img ng-click="showModalImage()" class="image-to-zoom" ng-src="{{ imageId }}" alt="" />
                        <span>
                            <img ng-click="showModalImage()" class="zoom" src="/images/sprite/public-test/zoom.svg" alt="" />
                            {{ 'Click on the picture to enlarge' | translate }}
                        </span>
                    </div>
                </div>
                <div class="buttons-wrap">
                    <button
                        ng-if="firstPage && firstPage != 1"
                        ng-click="nextTestQuestion('prev')"
                        class="btn-primary-new cancel next-question"
                        ng-class="{ 'left-side': currentLang === 'ru', 'disabled noHover': isQuestionLoading }"
                        ng-disabled="isQuestionLoading"
                    >
                        {{ 'Go back' | translate }}
                    </button>
                    <div title="{{ (isNextButtonDisabled || isQuestionLoading) && 'To continue, you need to answer the question' | translate }}">
                        <button
                            ng-click="nextTestQuestion('next')"
                            class="btn-primary-new small next-question next-question-for-test"
                            ng-class="{ 'disabled noHover': isNextButtonDisabled || isQuestionLoading }"
                            ng-disabled="isNextButtonDisabled || isQuestionLoading"
                        >
                            <span ng-show="firstPage && firstPage === firstTestQuestion.allPageCount">
                                {{ 'Finish the test' | translate }}
                            </span>
                            <span ng-show="firstPage && firstPage !== firstTestQuestion.allPageCount">
                                {{ 'Next question' | translate }}
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <div ng-show="saveAnswersTest" class="block-info finish-test">
                <h3>{{ 'Do you want to finish the test?' | translate }}</h3>

                <div class="block-accent small">
                    <img src="/images/sprite/public-test/placeholder.svg" alt="danger-icon" />
                    <span class="warning">
                        {{ 'Once you pass the test, your answers will be sent to a recruiter. You have only one attempt to pass the test' | translate }}
                    </span>
                </div>

                <div class="buttons-wrap">
                    <button ng-click="previousTestQuestion()" class="btn-primary-new cancel next-question">
                        {{ 'Go back' | translate }}
                    </button>
                    <button ng-click="endTestTimeOrSubmit('done')" class="btn-primary-new small next-question last-question">
                        {{ 'Save and finish the test' | translate }}
                    </button>
                </div>
            </div>

            <div ng-show="showEndMessage" class="block-info thanks-message">
                <div ng-show="endTestMsg" class="block-accent">
                    <span>{{ endTestMsg }}</span>
                </div>
                <div ng-show="!endTestMsg" class="block-accent text-center success">
                    <h3>{{ 'Thank you_1' | translate }}</h3>
                    <span>
                        {{ 'Your answers are saved and sent to the recruiter' | translate }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="row absolute">
        <div class="col-xs-12 text-center">
            <div class="cleverstaff">
                <span>Powered by</span>
                <a href="/" target="_self">
                    <img src="/images/sprite/favicon.png" alt="CleverStaff recruitment software" />
                    <span>CleverStaff</span>
                </a>
            </div>
        </div>
    </div>

    <div id="question-modal" class="question-modal-image">
        <span ng-click="closeModalImage()" class="close">&times;</span>
        <img class="modal-content" ng-src="{{ imageId }}" />
    </div>
</div>
