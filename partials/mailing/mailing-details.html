<div class="mailing-detail">
    <div class="mailing-detail__header">
        <div class="mailing-detail__header-name item-wrapper item">
            <span class="item-title">
                {{ 'Internal mailing name' | translate }}
                <span class="form-title-star">&nbsp;*</span>
            </span>
            <input-component
                class="input-width"
                current-lang="$root.currentLang"
                placeholder="'Enter the name of the newsletter'"
                value="internalName"
                is-error="notValidName"
                on-change="(updateInternal)"
            ></input-component>
        </div>
        <div class="mailing-detail__header-vacancies item-wrapper item">
            <span class="item-title">
                {{ 'Adding recipients' | translate }}
                <span class="form-title-star">&nbsp;*</span>
            </span>
            <div class="item-row">
                <select-single-async
                    class="dropdown-width"
                    current-lang="$root.currentLang"
                    placeholder="'Select vacancies'"
                    fetch-options="(getAllVacancies)"
                    on-change="(onSelectVacancy)"
                    selected-value="selectedVacancy"
                    isClearable="false"
                    path-to-label="'label'"
                    path-to-value="'label'"
                ></select-single-async>
                <select-single
                    class="dropdown-width"
                    current-lang="$root.currentLang"
                    placeholder="'Choose the stage'"
                    on-change="(fetchCandidates)"
                    options="stagesFiltered"
                    translate-options="true"
                    selected-value="stageModel"
                    disabled="!stagesFiltered"
                    path-to-label="'label'"
                    path-to-value="'label'"
                    is-clearable="false"
                ></select-single>
            </div>
        </div>
    </div>

    <div ng-show="emptyEmails.count > 0" class="mailing-detail__messages">
        <div ng-show="emptyEmails.count > 0" class="empty-emails alert alert-danger">
            <span ng-show="emptyEmails.translateType === 0" translate="There are candidate in the list without email"></span>
            <span
                ng-show="emptyEmails.translateType === 1"
                translate="There are candidates in the list without email"
                translate-values="{count: emptyEmails.count}"
            ></span>
            <span
                ng-show="emptyEmails.translateType === 2"
                translate="There are candidates in the list without email _1"
                translate-values="{count: emptyEmails.count}"
            ></span>
            <a ng-show="emptyEmails.translateType === 0" ng-click="deleteCandidatesWithoutEmails()" href="" translate="Delete him"></a>
            <a ng-show="emptyEmails.translateType !== 0" ng-click="deleteCandidatesWithoutEmails()" href="" translate="Delete them"></a>
        </div>
    </div>

    <div class="mailing-detail__table" ng-hide="!candidatesForMailing || candidatesForMailing.length === 0">
        <div class="outer-block">
            <table id="mainTable" class="table">
                <thead>
                    <tr class="table-header">
                        <th class="full-name">{{ 'full_name' | translate }}</th>
                        <th class="email">{{ 'email' | translate }}</th>
                        <th class="send"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        ng-if="candidatesForMailing && candidatesForMailing.length > 0"
                        ng-class="{
                            wrongEmail: candidate.wrongEmail,
                            changesNotSaved: candidate.editable && changesNotSaved,
                            duplicates: candidate.email && !candidate.wrongEmail && candidate.duplicateGroupId
                        }"
                        ng-repeat="candidate in candidatesForMailing track by $index"
                    >
                        <td class="recipient" ng-class="{ 'editable-vertical-align': candidate.editable }">
                            <div class="recipient__info">
                                <candidate-preview
                                    ng-if="!candidate.editable"
                                    ng-click="fetchCandidateFullInfo(candidate)"
                                    candidate="candidate.fullInfo"
                                    translate-func="$root.translate"
                                    $root="$root"
                                ></candidate-preview>

                                <a
                                    ng-if="!candidate.editable"
                                    class="recipient__link"
                                    title="{{  }}"
                                    href="!#/candidates/{{ candidate.localId }}"
                                    target="_blank"
                                >
                                    {{ $root.getFullNameDueToStyle(candidate) }}
                                </a>
                            </div>

                            <div ng-if="candidate.editable" class="recipient__edit" ng-class="{ 'recipient__edit_american-order': $root.useAmericanNameStyle }">
                                <div class="field-block" ng-class="{ 'recipient__edit__last-name_american-style': $root.useAmericanNameStyle }">
                                    <p class="field-block__title">
                                        {{ 'last_name' | translate }}
                                        <span class="form-title-star">*</span>
                                    </p>
                                    <input-component
                                        current-lang="$root.currentLang"
                                        placeholder="'last_name'"
                                        on-change="(changeCandidateLastName)"
                                        value="candidate.lastNameModel"
                                        is-clearable="false"
                                        additional-arg="candidate"
                                        is-error="!candidate.lastNameModel.length"
                                    ></input-component>
                                </div>

                                <div class="field-block">
                                    <p class="field-block__title">
                                        {{ 'first_name' | translate }}
                                        <span class="form-title-star">*</span>
                                    </p>
                                    <input-component
                                        current-lang="$root.currentLang"
                                        placeholder="'first_name'"
                                        on-change="(changeCandidateFirstName)"
                                        value="candidate.firstNameModel"
                                        is-clearable="false"
                                        additional-arg="candidate"
                                        is-error="!candidate.firstNameModel.length"
                                    ></input-component>
                                </div>

                                <div class="field-block">
                                    <p class="field-block__title">{{ 'middle_name' | translate }}</p>
                                    <input-component
                                        current-lang="$root.currentLang"
                                        placeholder="'middle_name'"
                                        on-change="(changeCandidateMiddleName)"
                                        value="candidate.middleNameModel"
                                        additional-arg="candidate"
                                    ></input-component>
                                </div>
                            </div>
                        </td>

                        <td class="email" ng-class="{ 'editable-vertical-align': candidate.editable }">
                            <span ng-if="!candidate.editable && (candidate.newEmail || candidate.email)" ng-click="showEditCandidate(candidate)">
                                {{ candidate.newEmail.value || candidate.email.value }}
                            </span>
                            <span
                                ng-if="!candidate.editable && !(candidate.newEmail || candidate.email)"
                                ng-click="showEditCandidate(candidate)"
                                class="no-email"
                            >
                                {{ 'Email is not specified' | translate }}
                            </span>
                            <input-component
                                ng-if="candidate.editable && !candidate.email"
                                current-lang="$root.currentLang"
                                placeholder="'enter_email_candidate'"
                                on-change="(changeCandidateEmail)"
                                value="candidate.emailModel"
                                additional-arg="candidate"
                                is-error="candidateEmailError"
                            ></input-component>
                            <select-single
                                ng-if="candidate.editable && candidate.email"
                                current-lang="$root.currentLang"
                                on-change="(selectCandidateEmail)"
                                options="candidate.emails"
                                selected-value="candidate.newEmail"
                                path-to-label="'value'"
                                path-to-value="'value'"
                                is-clearable="false"
                                additional-arg="candidate"
                            ></select-single>
                        </td>

                        <td class="send" ng-class="{ 'editable-vertical-align': candidate.editable }">
                            <div ng-if="candidate.editable" class="edit-buttons">
                                <button-component
                                    ng-click="cancelSavingCandidateContacts(candidate)"
                                    type="'secondary'"
                                    text="'cancel' | translate"
                                ></button-component>
                                <button-component
                                    ng-click="saveCandidateContacts(candidate, candidate.newEmail, candidate.firstNameModel, candidate.lastNameModel, candidate.middleNameModel)"
                                    text="'save' | translate"
                                ></button-component>
                            </div>

                            <div ng-if="!candidate.editable" class="send__icons">
                                <img
                                    ng-click="showEditCandidate(candidate);"
                                    src="/images/redesign/svg-icons/pencil.svg"
                                    alt=""
                                    title="{{ 'Edit' | translate }}"
                                    role="button"
                                />
                                <img
                                    ng-click="confirmDelete(candidate)"
                                    src="/images/redesign/svg-icons/trash.svg"
                                    alt=""
                                    title="{{ 'Delete' | translate }}"
                                    role="button"
                                />
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div ng-show="!candidatesForMailing || candidatesForMailing.length === 0" class="candidates-no-found">
        <p>{{ 'No candidates selected yet' | translate }}</p>
    </div>
    <div class="mailing-detail__btn">
        <button-component history-back type="'secondary'" text="'Go back' | translate"></button-component>
        <button-component ng-click="toTheEditor()" text="'Next' | translate"></button-component>
    </div>
</div>
<!--*****************Candidate preview*******************-->
<div ng-include="'partials/candidate-preview.html'"></div>
