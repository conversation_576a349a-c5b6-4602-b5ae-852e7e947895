<div class="payments-settings regular-payments">
    <div class="payments-settings__title-wrapper">
        <h3 class="form-title-main" style="margin: 0">{{ 'Setting up regular payments' | translate }}</h3>
        <div class="tooltip-wrapper" style="top: 2px">
            <i class="info-icon" tooltip-placement="top" tooltip-class="tooltip-outer tooltip-payment" uib-tooltip-html="'cardPaymentTooltip'| translate"></i>
        </div>
    </div>
    <div class="payments-settings__container" ng-class="{ isBlur: vm.isBlur }">
        <div ng-show="vm.integratedCard.length > 0" class="payments-settings__with-bottom-line">
            <p class="payment-history__text">
                <span ng-show="vm.integratedCard.length === 1">{{ 'You have one card attached' | translate }}</span>
                <span ng-show="vm.integratedCard.length > 1" translate="You have cards attached" translate-values="{cards: vm.integratedCard.length}"></span>
                {{ 'You can change the default card at any time.' | translate }}
            </p>

            <ul class="regular-payments__cards">
                <li ng-click="vm.setActiveCard(card.orderId, card.status)" class="regular-payments__card" ng-repeat="card in vm.integratedCard">
                    <img ng-show="$index !== vm.activeCardIndex" class="regular-payments__card-marker" src="../images/sprite/lists/list_marker.svg" alt="" />
                    <img
                        ng-show="$index === vm.activeCardIndex"
                        class="regular-payments__card-marker"
                        ng-class="{ 'regular-payments__card-active': $index === vm.activeCardIndex }"
                        src="../images/sprite/lists/list_marker_selected.svg"
                        alt=""
                    />
                    <div class="regular-payments__card-container">
                        <p ng-show="$index === vm.activeCardIndex" class="payment-history__text">
                            {{ 'Default' | translate }}
                        </p>
                        <div class="regular-payments__card-item" ng-class="{ 'regular-payments__card-item-active': $index === vm.activeCardIndex }">
                            <div class="regular-payments__card-item-row">
                                <span ng-if="!vm.getBankImage(card.bank)" class="regular-payments__card-bank-default">
                                    {{ card.bank }}
                                </span>
                                <img
                                    ng-if="vm.getBankImage(card.bank)"
                                    class="regular-payments__card-bank"
                                    ng-src="{{ vm.getBankImage(card.bank) }}"
                                    alt="{{ card.bank }}"
                                />
                                <div class="card-edits">
                                    <div
                                        ng-show="$index === vm.activeCardIndex"
                                        ng-click="vm.removeCard(card.orderId, card.status)"
                                        class="card-edits__icon card-edits__remove"
                                    ></div>
                                </div>
                            </div>
                            <div class="regular-payments__card-item-row">
                                <span class="regular-payments__card-number">{{ card.cardNumber }}</span>
                                <img ng-show="card.cardType.toLowerCase() === 'visa'" src="../images/sprite/payments/visa.svg" alt="Visa" />
                                <img ng-show="card.cardType.toLowerCase() === 'mastercard'" src="../images/sprite/payments/masterCard.svg" alt="MasterCard" />
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div ng-if="!vm.integratedCard.length" class="payments-settings__with-bottom-line">
            <p translate="There are not integrated cards"></p>
        </div>

        <div class="regular-payments__actions">
            <div style="display: flex">
                <button
                    ng-click="vm.toPayByCard()"
                    class="btn_default btn_success regular-payments__save-btn"
                    type="button"
                    ng-disabled="!vm.integratedCard.length || vm.$rootScope.transactionsInPending"
                >
                    {{ 'to_pay' | translate }}
                </button>
                <i
                    ng-if="vm.$rootScope.transactionsInPending"
                    class="info-icon"
                    style="margin-left: 10px"
                    tooltip-placement="bottom-right"
                    tooltip-class="tooltip-outer"
                    uib-tooltip="{{ 'You have payment in progress' | translate }}"
                ></i>
            </div>
            <div ng-click="vm.addCard()" class="regular-payments__actions_add">
                <img class="add-card_icon" src="../images/sprite/payments/credit-card.svg" alt="add card" />
                {{ 'Add Card' | translate }}
            </div>
        </div>

        <div ng-show="vm.removingCardTimer >= 0" class="regular-payments__removing-block">
            <div class="removing-block__main-container">
                <div class="removing-block__timer">
                    <span class="removing-block__timer-text">{{ vm.removingCardTimer }}</span>
                    <svg class="removing-block__timer-svg">
                        <circle class="removing-block__timer-circle" r="18" cx="20" cy="20"></circle>
                        <circle class="removing-block__timer-circle-base" r="18" cx="20" cy="20"></circle>
                    </svg>
                </div>
                <p class="removing-block__text">
                    {{ 'Card removed from list' | translate }}
                </p>
            </div>
            <a ng-click="vm.cancelRemovingCard()" class="removing-block__cancel-btn" href="">
                {{ 'cancel' | translate }}
            </a>
        </div>
    </div>
</div>
