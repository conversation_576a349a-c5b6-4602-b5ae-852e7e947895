<div class="block-cloud-admin-account-information">
    <div class="container-fluid wrapper">
        <section class="payment__intro">
            <a ng-click="goBack();" class="btn btn-transparent" href="#/cloud-admin">
                <span class="hidden-xs">Go back</span>
            </a>
            <h2 class="title">Account information</h2>
        </section>

        <section class="payment__acc-info">
            <div class="info__item">
                <span class="into__title">Account</span>
                <span class="info__value">{{ balance.account }}</span>
            </div>
            <div class="info__item" ng-hide="tarif === 'corporate'">
                <span class="into__title">Account balance</span>
                <span class="info__value">{{ balance.amount }} USD</span>
            </div>
            <div ng-show="tarif !== 'free'" class="info__item">
                <span class="into__title">Paid till date</span>
                <span class="info__value">
                    {{ paidTillDate }}
                </span>
            </div>
            <div ng-show="tarif !== 'free' && tarif !== 'corporate'" class="info__item">
                <span class="into__title">Paid users amount</span>
                <span class="info__value">{{ paidUsers.length }}</span>
            </div>
            <div class="info__item">
                <span class="into__title">Current tariff</span>
                <span class="info__value">{{ tarif | paymentTariff }}</span>
            </div>
        </section>

        <section ng-show="tarif !== 'free' && expenses.length" class="payment__history">
            <div class="section__intro center">
                <h4 class="form-title-main">Daily payments history of your company account</h4>
            </div>
            <div class="history-data">
                <table class="write-offs-history">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Payment</th>
                            <th>Sum</th>
                            <th>Balance</th>
                        </tr>
                    </thead>
                    <tr ng-repeat="expense in expenses">
                        <td>{{ expense.targetDate | dateFormat }}</td>
                        <td ng-show="!expense.descr && expense.type == 'expense'">
                            <span>Charge for {{ ' ' + expense.peopleCount }}</span>
                            <span ng-if="expense.peopleCount == 1">User</span>
                            <span ng-if="expense.peopleCount > 1">users</span>
                        </td>
                        <td ng-show="!expense.descr && expense.type == 'pay'">
                            <span>Reffilling the balance</span>
                        </td>
                        <td ng-show="expense.descr && expense.descr === 'Mailing expense'">
                            {{::expense.descr}}
                        </td>
                        <td ng-show="expense.descr && expense.descr !== 'Mailing expense'">
                            {{::expense.descr}}
                        </td>
                        <td>{{::expense.amountDouble + ' '}} USD</td>
                        <td>{{::expense.balanceDouble + ' '}}USD</td>
                    </tr>
                </table>
            </div>
        </section>
        <section class="user-data hidden-sm hidden-xs">
            <table class="user-table-history">
                <thead>
                    <tr>
                        <th>User name</th>
                        <th>Role</th>
                        <th>Login</th>
                        <th>Status</th>
                        <th>Date of registration</th>
                        <th>Date of deactivation of the user</th>
                        <th>Date of the last authorization</th>
                        <th>User ID</th>
                        <th>Referral bank payment sent</th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-class="{ referral: referralId === accountUsers.userId }" ng-repeat="accountUsers in getAccountUsers">
                        <td title="{{ accountUsers.usr }}">
                            <span ng-if="accountUsers.usr && (accountUsers.withSpace == false || accountUsers.withSpace == undefined) && innerWidth >= 1366">
                                {{ accountUsers.usr | limitTo : 13 }}{{ accountUsers.usr.length > 13 ? '...' : '' }}
                            </span>
                            <span
                                ng-if="
                                    accountUsers.usr &&
                                    (accountUsers.withSpace == false || accountUsers.withSpace == undefined) &&
                                    innerWidth >= 1300 &&
                                    innerWidth < 1366
                                "
                            >
                                {{ accountUsers.usr | limitTo : 12 }}{{ accountUsers.usr.length > 12 ? '...' : '' }}
                            </span>
                            <span
                                ng-if="
                                    accountUsers.usr &&
                                    (accountUsers.withSpace == false || accountUsers.withSpace == undefined) &&
                                    innerWidth >= 1200 &&
                                    innerWidth < 1300
                                "
                            >
                                {{ accountUsers.usr | limitTo : 10 }}{{ accountUsers.usr.length > 10 ? '...' : '' }}
                            </span>
                            <span
                                ng-if="
                                    accountUsers.usr &&
                                    (accountUsers.withSpace == false || accountUsers.withSpace == undefined) &&
                                    innerWidth >= 1135 &&
                                    innerWidth < 1200
                                "
                            >
                                {{ accountUsers.usr | limitTo : 8 }}{{ accountUsers.usr.length > 8 ? '...' : '' }}
                            </span>
                            <span ng-if="accountUsers.usr && accountUsers.withSpace == false && innerWidth < 1135">
                                {{ accountUsers.usr | limitTo : 7 }}{{ accountUsers.usr.length > 7 ? '...' : '' }}
                            </span>
                            <span ng-if="accountUsers.usr && accountUsers.withSpace == undefined && innerWidth < 1135">
                                {{ accountUsers.usr.split(' ')[0] | limitTo : 12 }}{{ accountUsers.usr.split(' ')[0].length > 12 ? '...' : '' }}
                                {{ accountUsers.usr.split(' ')[1] | limitTo : 11 }}{{ accountUsers.usr.split(' ')[1].length > 11 ? '...' : '' }}
                            </span>
                            <span ng-if="accountUsers.usr && accountUsers.withSpace == true" class="word-break">
                                {{ accountUsers.usr }}
                            </span>
                            <span ng-if="!accountUsers.usr">-</span>
                        </td>
                        <td>
                            <span ng-if="accountUsers.role">{{ accountUsers.role }}</span>
                            <span ng-if="!accountUsers.role">-</span>
                        </td>
                        <td title="{{ accountUsers.login }}">
                            <span ng-if="accountUsers.login && innerWidth >= 1300">
                                {{ accountUsers.login | limitTo : 30 }}{{ accountUsers.login.length > 30 ? '...' : '' }}
                            </span>
                            <span ng-if="accountUsers.login && innerWidth >= 1200 && innerWidth < 1300">
                                {{ accountUsers.login | limitTo : 27 }}{{ accountUsers.login.length > 27 ? '...' : '' }}
                            </span>
                            <span ng-if="accountUsers.login && innerWidth > 1135 && innerWidth < 1200">
                                {{ accountUsers.login | limitTo : 24 }}{{ accountUsers.login.length > 24 ? '...' : '' }}
                            </span>
                            <span ng-if="accountUsers.login && innerWidth <= 1135">
                                {{ accountUsers.login | limitTo : 19 }}{{ accountUsers.login.length > 19 ? '...' : '' }}
                            </span>
                            <span ng-if="!accountUsers.login">-</span>
                        </td>
                        <td>{{ accountUsers.status }}</td>
                        <td>{{ accountUsers.started | date : 'dd.MM.yy' }}</td>
                        <td>
                            <span ng-if="accountUsers.disable">{{ accountUsers.disable | date : 'dd.MM.yy' }}</span>
                            <span ng-if="!accountUsers.disable">-</span>
                        </td>
                        <td>{{ accountUsers.lastAuth | date : 'dd.MM.yy' }}</td>
                        <td title="{{ accountUsers.userId }}">{{ accountUsers.userId }}</td>
                        <td>
                            <a ng-click="referralBankModal(accountUsers.userId, accountUsers.usr);" class="referral-bank-btn">
                                Show me
                                <span class="money-img"></span>
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>
    </div>
</div>
