<div id="candidates-source-stat" class="candidates-source-stat">
    <div style="width: 100%; display: flex; align-items: center; position: relative; margin-top: 20px; margin-bottom: 20px">
        <div style="padding-top: 10px">
            <candidate-source-stat-page-intro-component page-title="'Number of candidate sources'"></candidate-source-stat-page-intro-component>
        </div>
        <div style="display: flex; align-items: center; justify-content: center; width: 100%">
            <h3 class="main-page-title" style="margin: 0">
                <span>{{ 'Number of candidate sources' | translate }}</span>
            </h3>
        </div>
    </div>

    <section class="stat__settings">
        <div class="controllers">
            <div class="wrapper">
                <div class="controller time-range-controller margin-right">
                    <div class="date-range">
                        <span class="title">{{ 'Time range' | translate }}</span>

                        <div class="range-dropdown" style="max-width: 210px; min-width: 210px; width: 100%">
                            <custom-select-new
                                class="custom-drop-down"
                                method="vm.selectDateRange"
                                model="vm.model.settings.timeRange.selectRange"
                                options="vm.model.settings.timeRange.dateRange"
                            ></custom-select-new>
                        </div>
                        <div class="date-pickers">
                            <span class="range-name" style="position: relative; left: 13px; top: 2px" translate="from_4"></span>
                            <div class="picker" ng-class="{ disabled: vm.model.settings.timeRange.disabledTimeRange }">
                                <mdp-date-picker
                                    ng-click="vm.activateTimeRange()"
                                    class="startDate"
                                    mdp-format="DD/MM/YYYY"
                                    mdp-open-on-click
                                    mdp-placeholder="{{ vm.model.settings.timeRange.startDate | date : 'dd/MM/yyyy' }}"
                                    name="startDate"
                                    mdp-max-date="vm.newDateStat"
                                    mdp-min-date="'2014-01-01'"
                                    mdp-disabled="vm.model.settings.timeRange.disabledTimeRange"
                                    ng-model="vm.model.settings.timeRange.startDate"
                                ></mdp-date-picker>
                            </div>
                            <span class="range-name" style="position: relative; left: 13px; top: 2px" translate="to-2"></span>
                            <div class="picker" ng-class="{ disabled: vm.model.settings.timeRange.disabledTimeRange }">
                                <mdp-date-picker
                                    ng-click="vm.activateTimeRange()"
                                    class="endDate"
                                    mdp-format="DD/MM/YYYY"
                                    mdp-open-on-click
                                    mdp-placeholder="{{ vm.model.settings.timeRange.endDate | date : 'dd/MM/yyyy' }}"
                                    name="endDate"
                                    mdp-max-date="vm.newDateStat"
                                    mdp-min-date="'2014-01-01'"
                                    mdp-disabled="vm.model.settings.timeRange.disabledTimeRange"
                                    ng-model="vm.model.settings.timeRange.endDate"
                                ></mdp-date-picker>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="controller time-range-controller margin-right margin-top">
                    <div class="users">
                        <span class="title" ng-class="{ enabled: vm.model.settings.users.isEnabled }" translate="Show users"></span>
                        <div class="switcher" ng-class="{ on: vm.model.settings.users.isEnabled }">
                            <custom-switcher
                                switcher-id="'constructor-report'"
                                off-on="true"
                                checked="vm.model.settings.users.isEnabled"
                                method="vm.toggleSelectedUsers"
                            ></custom-switcher>
                        </div>
                        <div class="drop-down">
                            <custom-select-with-checkboxes
                                id="users-filter"
                                ng-if="vm.model.settings.users.users && $root.useAmericanNameStyle"
                                disabled="!vm.model.settings.users.isEnabled"
                                display-mult-selection="true"
                                model="vm.model.settings.users.selected"
                                options="vm.model.settings.users.users"
                                translate-all="'Select all-v3'"
                                path="'fullNameEn'"
                                placeholder="'Select users'"
                                show-search="'true'"
                                track-id="'userId'"
                            ></custom-select-with-checkboxes>
                            <custom-select-with-checkboxes
                                id="users-filter"
                                ng-if="vm.model.settings.users.users && !$root.useAmericanNameStyle"
                                disabled="!vm.model.settings.users.isEnabled"
                                display-mult-selection="true"
                                model="vm.model.settings.users.selected"
                                options="vm.model.settings.users.users"
                                translate-all="'Select all-v3'"
                                path="'fullName'"
                                placeholder="'Select users'"
                                show-search="'true'"
                                track-id="'userId'"
                            ></custom-select-with-checkboxes>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section
        ng-show="
            !($root['me']['recrutRole'] == 'recruter' && ($root.me.personParams.enableDownloadToExcel == 'N' || !$root.me.personParams.enableDownloadToExcel))
        "
        class="stat__actions"
    >
        <div class="wrapper">
            <div ng-show="vm.model.sourceStat.total" class="action report-info">
                {{ 'For the all period added' | translate }}
                {{ 'history_info.candidates_1' | translate }}:&nbsp
                <span style="font-weight: 500">{{ vm.model.sourceStat.total }}</span>
            </div>
            <div class="action btn-wrap">
                <button
                    ng-click="vm.getCandidatesSourceStatistic()"
                    class="btn btn-default"
                    translate="{{ vm.model.sourceStat ? 'Update report' : 'Generate report' }}"
                ></button>
            </div>
            <div ng-show="vm.model.sourceStat.total" class="action report-excel">
                <a id="excel-download" class="hidden" download></a>
                <a
                    ng-click="vm.downloadExcel()"
                    ng-hide="
                        ($root['me']['recrutRole'] == 'recruter' &&
                            ($root.me.personParams.enableDownloadToExcel == 'N' || !$root.me.personParams.enableDownloadToExcel)) ||
                        ($root['me']['recrutRole'] == 'admin' && $root.me.personParams.enableDownloadToExcel == 'N')
                    "
                >
                    <img alt="" src="/images/sprite/excel-history.svg" />
                    {{ 'Export to Excel_2' | translate }}
                </a>
            </div>
        </div>
    </section>
    <section
        ng-show="
            $root['me']['recrutRole'] == 'recruter' && ($root.me.personParams.enableDownloadToExcel == 'N' || !$root.me.personParams.enableDownloadToExcel)
        "
        class="stat__actions"
    >
        <div class="wrapper" style="position: relative">
            <div ng-show="vm.model.sourceStat.total" class="action report-info" style="position: absolute; width: 400px">
                {{ 'For the all period added' | translate }}
                {{ 'history_info.candidates_1' | translate }}:&nbsp
                <span style="font-weight: 500">{{ vm.model.sourceStat.total }}</span>
            </div>
            <div class="action btn-wrap">
                <button
                    ng-click="vm.getCandidatesSourceStatistic()"
                    class="btn btn-default"
                    translate="{{ vm.model.sourceStat ? 'Update report' : 'Generate report' }}"
                ></button>
            </div>
        </div>
    </section>
    <section class="stat__table" ng-class="{ inline: vm.model.sourceStat.orgStatistic.length }">
        <table ng-if="vm.model.sourceStat.orgStatistic.length" class="source-table" style="height: fit-content">
            <thead>
                <tr>
                    <th class="ff-medium" translate="Source"></th>
                    <th class="ff-medium" translate="Added"></th>
                    <th class="ff-medium" translate="% from total"></th>
                </tr>
            </thead>
            <tbody>
                <tr style="height: 17%" ng-repeat="source in vm.model.sourceStat.orgStatistic track by $index">
                    <td ng-if="source.source !== 'other'" class="align-left" style="padding: 33px 15px">
                        {{ source.source | translate }}
                    </td>
                    <td
                        ng-if="source.source === 'other'"
                        class="align-left"
                        ng-class="source.source === 'other' ? 'source-other' : ''"
                        style="padding: 33px 15px"
                    >
                        {{ source.source | translate }}
                        <i
                            class="info-icon"
                            aria-hidden="true"
                            backdrop-id="'candidates-source-stat'"
                            tabindex="0"
                            target-id="'other-source-info'"
                            toggle-element-visibility
                        ></i>
                        <div id="other-source-info" class="other-source-info hidden" custom-scrollbar>
                            <span translate="{{ vm.model.sourceStat.otherGroupsDetail }}"></span>
                        </div>
                    </td>
                    <td style="padding: 33px 15px">{{ source.count }}</td>
                    <td style="padding: 33px 15px">{{ source.percent }}%</td>
                </tr>
                <tr style="height: 17%">
                    <td class="ff-medium align-left" style="padding: 33px 15px" translate="total"></td>
                    <td class="ff-medium" style="padding: 33px 15px">{{ vm.model.sourceStat.total }}</td>
                    <td class="ff-medium" style="padding: 33px 15px">100%</td>
                </tr>
            </tbody>
        </table>

        <div ng-if="vm.model.sourceStat.userStatistics.length && vm.model.sourceStat.isLoaded" class="wrapper-source-by-user-table">
            <table class="source-by-user-table">
                <thead>
                    <tr>
                        <th class="ff-medium" rowspan="2" translate="user"></th>
                        <th class="main-header ff-medium" colspan="3" ng-repeat="source in vm.model.sourceStat.groups track by $index">
                            <span ng-if="source === 'other'" class="source" ng-class="source === 'other' ? 'source-other' : ''">
                                {{ source | translate }}
                                <i
                                    class="info-icon"
                                    aria-hidden="true"
                                    backdrop-id="'candidates-source-stat'"
                                    tabindex="0"
                                    target-id="'other-source-info-by-users'"
                                    toggle-element-visibility
                                ></i>
                                <div id="other-source-info-by-users" class="other-source-info hidden" custom-scrollbar>
                                    <span translate="{{ vm.model.sourceStat.otherGroupsDetail }}"></span>
                                </div>
                            </span>
                            <span ng-if="source !== 'other'" class="source">
                                {{ source | translate }}
                            </span>
                            <div class="source-origin">
                                <div class="type" translate="Added"></div>
                                <div class="type" translate="% from total of acc"></div>
                                <div class="type" translate="% from user’s"></div>
                            </div>
                        </th>
                        <th class="main-header ff-medium" colspan="3">
                            <span class="source" translate="total"></span>
                            <div class="source-origin">
                                <div class="type" translate="Added"></div>
                                <div class="type" translate="% from total of acc"></div>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="user in vm.model.sourceStat.userStatistics track by $index">
                        <td class="align-left">{{ user.creatorName }}</td>
                        <td colspan="3" ng-repeat="sourceType in vm.model.sourceStat.groups track by $index">
                            <div class="user-stats">
                                <div class="stat">{{ user.statisticGroupBySource[sourceType].count || '-' }}</div>
                                <div ng-if="user.statisticGroupBySource[sourceType].percentOfAccount" class="stat">
                                    {{ user.statisticGroupBySource[sourceType].percentOfAccount | limitTo : 4 }}%
                                </div>
                                <div ng-if="!user.statisticGroupBySource[sourceType].percentOfAccount" class="stat">-</div>
                                <div ng-if="user.statisticGroupBySource[sourceType].percentOfOwn" class="stat">
                                    {{ user.statisticGroupBySource[sourceType].percentOfOwn | limitTo : 4 }}%
                                </div>
                                <div ng-if="!user.statisticGroupBySource[sourceType].percentOfOwn" class="stat">-</div>
                            </div>
                        </td>
                        <td>
                            <div class="user-stats">
                                <div class="stat">{{ vm.model.sourceStat.userStatistics[$index].count }}</div>
                                <div class="stat">{{ vm.model.sourceStat.userStatistics[$index].percent | limitTo : 4 }}%</div>
                            </div>
                        </td>
                    </tr>
                    <tr ng-if="vm.model.sourceStat.userStatistics.length > 1" class="border-sides">
                        <td class="ff-medium" translate="Total by source"></td>
                        <td colspan="3" ng-repeat="sourceType in vm.model.sourceStat.groups track by $index">
                            <div class="user-stats">
                                <div class="ff-medium stat">{{ vm.getSourceInfoStats(sourceType) }}</div>
                                <div class="ff-medium stat">{{ vm.getSourceInfoStats(sourceType, 'relative') }}</div>
                                <div class="ff-medium stat"></div>
                            </div>
                        </td>
                        <td colspan="2">
                            <div class="user-stats">
                                <div class="ff-medium stat">{{ vm.model.sourceStat.total }}</div>
                                <div class="ff-medium stat">{{ vm.model.sourceStat.percentOfAccount }}%</div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div ng-if="vm.model.sourceStat.userStatistics.length">
            <hr class="sticky left" />
            <hr class="sticky" />
        </div>
    </section>

    <section class="stat__graph" ng-class="{ inline: vm.model.sourceStat.orgStatistic.length }">
        <div ng-if="vm.model.sourceStat.userStatistics.length" class="graph step-graph">
            <h3
                ng-if="vm.model.sourceStat.userStatistics.length === 1"
                class="graph-title"
                translate="The number of candidates added from different sources by user"
            ></h3>
            <h3
                ng-if="vm.model.sourceStat.userStatistics.length > 1"
                class="graph-title"
                translate="The number of candidates added from different sources by users"
            ></h3>
            <div class="canvas-wrapper">
                <canvas id="step-graph-block-wrapper"></canvas>
                <canvas id="step-graph-diagram"></canvas>
            </div>
            <div class="step-diagram-sources">
                <div class="source" ng-repeat="source in vm.model.sourceStat.groups track by $index">
                    <i class="circle-icon" ng-style="{ background: vm.model.stepDiagramColors[$index] }"></i>
                    <span>{{ source | translate }}</span>
                </div>
            </div>
        </div>
        <div
            id="circle-graph-default"
            ng-if="vm.model.sourceStat.userStatistics.length || vm.model.sourceStat.orgStatistic.length"
            class="graph"
            ng-class="{ 'full-width': vm.model.sourceStat.orgStatistic.length }"
        ></div>
        <div id="circle-graph-users" ng-if="vm.model.sourceStat.userStatistics.length > 1" class="graph"></div>
    </section>
</div>
