<div class="candidates-source-stat stat-efficiency">
    <div
        class="candidates-source-stat-title"
        style="width: 100%; display: flex; align-items: center; position: relative; margin-top: 20px; margin-bottom: 20px"
    >
        <div style="padding-top: 10px">
            <candidate-source-stat-page-intro-component page-title="'Sources efficiency'"></candidate-source-stat-page-intro-component>
        </div>
        <div style="display: flex; align-items: center; justify-content: center; width: 100%">
            <h3 class="main-page-title" style="margin: 0">
                <span>{{ 'Sources efficiency' | translate }}</span>
            </h3>
        </div>
    </div>

    <section class="stat__settings">
        <div class="controllers">
            <div class="wrapper">
                <div class="controller time-range-controller margin-right">
                    <div class="date-range">
                        <span class="title">{{ 'Time range' | translate }}</span>

                        <div class="range-dropdown" style="max-width: 210px; min-width: 210px; width: 100%">
                            <custom-select-new
                                class="custom-drop-down"
                                model="vm.model.settings.timeRange.selectRange"
                                options="vm.model.settings.timeRange.dateRange"
                                method="vm.selectDateRange"
                            ></custom-select-new>
                        </div>
                        <div class="date-pickers">
                            <span class="range-name" style="position: relative; left: 13px; top: 2px" translate="from_4"></span>
                            <div class="picker" ng-class="{ disabled: vm.model.settings.timeRange.disabledTimeRange }">
                                <mdp-date-picker
                                    ng-click="vm.activateTimeRange()"
                                    class="startDate"
                                    ng-model="vm.model.settings.timeRange.startDate"
                                    mdp-open-on-click
                                    name="startDate"
                                    mdp-max-date="vm.newDateSourceEfficiency"
                                    mdp-min-date="'2014-01-01'"
                                    mdp-disabled="vm.model.settings.timeRange.disabledTimeRange"
                                    mdp-placeholder="{{ vm.model.settings.timeRange.startDate | date : 'dd/MM/yyyy' }}"
                                    mdp-format="DD/MM/YYYY"
                                ></mdp-date-picker>
                            </div>
                            <span class="range-name" style="position: relative; left: 13px; top: 2px" translate="to-2"></span>
                            <div class="picker" ng-class="{ disabled: vm.model.settings.timeRange.disabledTimeRange }">
                                <mdp-date-picker
                                    ng-click="vm.activateTimeRange()"
                                    class="endDate"
                                    ng-model="vm.model.settings.timeRange.endDate"
                                    mdp-open-on-click
                                    name="endDate"
                                    mdp-max-date="vm.newDateSourceEfficiency"
                                    mdp-min-date="'2014-01-01'"
                                    mdp-disabled="vm.model.settings.timeRange.disabledTimeRange"
                                    mdp-placeholder="{{ vm.model.settings.timeRange.endDate | date : 'dd/MM/yyyy' }}"
                                    mdp-format="DD/MM/YYYY"
                                ></mdp-date-picker>
                            </div>
                        </div>
                    </div>

                    <div class="users vacancies">
                        <span class="title" ng-class="{ enabled: vm.model.settings.withVacancies }" translate="Show vacancies"></span>

                        <div class="switcher" ng-class="{ on: vm.model.settings.withVacancies }">
                            <custom-switcher
                                switcher-id="'constructor-report'"
                                off-on="true"
                                checked="vm.model.settings.withVacancies"
                                method="vm.toggleVacanciesSelect"
                            ></custom-switcher>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="stat__actions">
        <div
            ng-show="
                !(
                    $root['me']['recrutRole'] == 'recruter' &&
                    ($root.me.personParams.enableDownloadToExcel == 'N' || !$root.me.personParams.enableDownloadToExcel)
                )
            "
            class="wrapper"
        >
            <div ng-if="vm.model.sourceStat.totals.approvedCandidates" class="action report-info">
                <span class="total-found-info">
                    <span>{{ 'Total hires for the period' | translate }}:&nbsp;</span>
                    <span style="font-weight: 500">{{ vm.model.sourceStat.totals.approvedCandidates }}</span>
                    <i
                        class="info-icon"
                        tooltip-placement="bottom"
                        tooltip-class="tooltip-outer center-xs"
                        uib-tooltip-html="$root.tooltips.source_eff_stat__found_total"
                        aria-hidden="true"
                    ></i>
                </span>
            </div>
            <div class="action btn-wrap" ng-style="vm.getStyleWithExcel(vm.model.sourceStat, vm.model)">
                <button
                    ng-click="vm.getCandidatesSourceStatistic()"
                    class="btn_default btn_success"
                    translate="{{ vm.model.sourceStat && vm.model.sourceStat.length ? 'Update report' : 'Generate report' }}"
                ></button>
            </div>
            <div
                ng-if="
                    (($root['me']['recrutRole'] == 'recruter' && $root.me.personParams.enableDownloadToExcel == 'Y') ||
                        ($root['me']['recrutRole'] == 'admin' &&
                            ($root.me.personParams.enableDownloadToExcel == 'Y' || !$root.me.personParams.enableDownloadToExcel))) &&
                    vm.model.sourceStat &&
                    vm.model.sourceStat.length
                "
                class="action report-excel"
            >
                <a id="performance-download" class="hidden"></a>
                <a ng-show="vm.model.sourceStat && vm.model.sourceStat.length" ng-click="vm.downloadExcel()">
                    <img src="/images/sprite/excel-history.svg" alt="" />
                    <span translate="Export to Excel"></span>
                </a>
            </div>
        </div>
        <div
            ng-show="
                $root['me']['recrutRole'] == 'recruter' && ($root.me.personParams.enableDownloadToExcel == 'N' || !$root.me.personParams.enableDownloadToExcel)
            "
            class="wrapper"
            style="position: relative"
        >
            <div ng-if="vm.model.sourceStat.totals.approvedCandidates" class="action report-info">
                <span class="total-found-info">
                    <span>{{ 'Total hires for the period' | translate }}:&nbsp;</span>
                    <span style="font-weight: 500">{{ vm.model.sourceStat.totals.approvedCandidates }}</span>
                    <i
                        class="info-icon"
                        tooltip-placement="bottom"
                        tooltip-class="tooltip-outer center-xs"
                        uib-tooltip-html="$root.tooltips.source_eff_stat__found_total"
                        aria-hidden="true"
                    ></i>
                </span>
            </div>
            <div class="action btn-wrap" ng-style="vm.getStyle(vm.model.sourceStat)">
                <button
                    ng-click="vm.getCandidatesSourceStatistic()"
                    class="btn_default btn_success"
                    translate="{{ vm.model.sourceStat && vm.model.sourceStat.length ? 'Update report' : 'Generate report' }}"
                ></button>
            </div>
        </div>
    </section>

    <section class="stat__content">
        <section class="stat__table" ng-class="{ 'zoom-out': vm.browserZoom < 100 && vm.browserZoom > 94, 'zoom-out-out': vm.browserZoom <= 94 }">
            <table ng-if="vm.model.sourceStat && vm.model.sourceStat.withVacancies" class="source-table">
                <thead>
                    <tr>
                        <th translate="Source list"></th>
                        <th translate="Added"></th>
                        <th translate="Hiring"></th>
                        <th translate="Vacancies"></th>
                        <th translate="Source efficiency"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="source in vm.model.sourceStat track by $index">
                        <td class="align-left">{{ source.source | translate }}</td>
                        <td ng-bind="source.candidatesBySource"></td>
                        <td ng-bind="source.approvedCandidates"></td>
                        <td class="vacancies-wrap-cell">
                            <table class="vacancies-table">
                                <tbody>
                                    <tr ng-repeat="vacancy in source.vacancies track by $index">
                                        <!-- prettier-ignore -->
                                        <td class="vacancies-column">
                                            <a
                                                class="ff-medium"
                                                target="_blank"
                                                ui-sref="vacancy({id: vacancy.localId})"
                                                ng-bind="vacancy.position |limitToEllipse:250"
                                            ></a>
                                            <span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'">({{vacancy.clientId.name}})</span>
                                            <br />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td>{{ source.efficiency }}%</td>
                    </tr>
                    <tr>
                        <td class="align-left" translate="total"></td>
                        <td class="ff-medium" ng-bind="vm.model.sourceStat.totals.candidatesBySource"></td>
                        <td class="ff-medium" ng-bind="vm.model.sourceStat.totals.approvedCandidates"></td>
                        <td class="ff-medium"></td>
                        <td class="ff-medium">{{ vm.model.sourceStat.totals.efficiency }}%</td>
                    </tr>
                </tbody>
            </table>

            <table ng-if="vm.model.sourceStat && !vm.model.sourceStat.withVacancies" class="source-table">
                <thead>
                    <tr>
                        <th translate="Source list"></th>
                        <th translate="Added"></th>
                        <th translate="Hiring"></th>
                        <th translate="Source efficiency"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="source in vm.model.sourceStat">
                        <td class="align-left">{{ source.source | translate }}</td>
                        <td ng-bind="source.candidatesBySource"></td>
                        <td ng-bind="source.approvedCandidates"></td>
                        <td>{{ source.efficiency }}%</td>
                    </tr>
                    <tr>
                        <td class="align-left" translate="total"></td>
                        <td class="ff-medium" ng-bind="vm.model.sourceStat.totals.candidatesBySource"></td>
                        <td class="ff-medium" ng-bind="vm.model.sourceStat.totals.approvedCandidates"></td>
                        <td class="ff-medium">{{ vm.model.sourceStat.totals.efficiency }}%</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <section ng-show="vm.currentScreenSize !== 'small' && vm.model.sourceStat.totals.approvedCandidates" class="stat__graph">
            <div id="circle" class="graph"></div>
        </section>
    </section>
    <a id="downloadReport" download></a>
</div>
