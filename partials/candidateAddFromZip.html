<div class="block-zip-archive">
    <div class="breadcrumbs">
        <ol class="breadcrumb">
            <li>
                <a href="#/candidates">{{ 'Candidates' | translate }}</a>
            </li>

            <li class="active">
                {{ 'Get candidates from zip' | translate }}
            </li>
        </ol>
    </div>
    <div class="row candidate-zip-archive">
        <div class="col-lg-12 fullName-position">
            <h3 class="main-page-title">{{ 'Bulk resume upload in ZIP-archive' | translate }}</h3>
            <span class="grey-color">{{ 'All resumes will be upload to this account on your behalf' | translate }}</span>
        </div>
        <div class="addZipArchive">
            <form class="howAddZipResume mainFormZip">
                <h5>1. {{ 'Select the format in the archive:' | translate }}</h5>
                <div class="pull-left">
                    <div ng-click="$root.disabledBtn || selectZipType('zipMono')" class="custom-radio-container">
                        <radio-component name="zipRadio" checked="zipType === 'zipMono'"></radio-component>
                        <label class="custom-radiobutton__text">
                            {{ 'ZIP-archive resume files in doc, docx, pdf, odt. All resumes in one folder' | translate }}
                            <i
                                ng-click="$event.stopPropagation(); $event.preventDefault();"
                                class="hint-info-icon__grey"
                                title=""
                                tooltip-placement="bottom-left"
                                tooltip-class="tooltip-info-black"
                                uib-tooltip-html="$root.tooltips.helpWindowZip1"
                                aria-hidden="true"
                            ></i>
                        </label>
                    </div>
                </div>
                <div class="pull-left">
                    <div ng-click="$root.disabledBtn || selectZipType('zipFolders')" class="custom-radio-container">
                        <radio-component name="zipRadio" checked="zipType === 'zipFolders'"></radio-component>
                        <label class="custom-radiobutton__text">
                            {{ 'ZIP-archive resume files in doc, docx, pdf, odt. Resumes grouped into folders by job title or technology' | translate }}
                            <i
                                ng-click="$event.stopPropagation(); $event.preventDefault();"
                                class="hint-info-icon__grey"
                                title=""
                                tooltip-placement="bottom-left"
                                tooltip-class="tooltip-info-black"
                                uib-tooltip-html="$root.tooltips.helpWindowZip2"
                                aria-hidden="true"
                            ></i>
                        </label>
                    </div>
                </div>
                <div ng-show="$root.currentLang == 'ru' || $root.currentLang == 'ua'" class="pull-left">
                    <div ng-click="$root.disabledBtn || selectZipType('zipEstaff')" class="custom-radio-container">
                        <radio-component name="zipRadio" checked="zipType === 'zipEstaff'"></radio-component>
                        <label class="custom-radiobutton__text">
                            {{ 'ZIP-archive candidates from Stff. This is XML files with names like candidate-0x0A1234E567C890A0.xml' | translate }}
                            <i
                                ng-click="$event.stopPropagation(); $event.preventDefault();"
                                class="hint-info-icon__grey"
                                title=""
                                tooltip-placement="bottom-left"
                                tooltip-class="tooltip-info-black"
                                uib-tooltip-html="$root.tooltips.helpWindowZip3"
                                aria-hidden="true"
                            ></i>
                            <a ng-show="$root.currentLang == 'ru'" class="stff" href="estaff2cleverstaff.html" target="_blank">Экспорт из E-Staff</a>
                            <a ng-show="$root.currentLang == 'ua'" class="stff" href="estaff2cleverstaffUa.html" target="_blank">Експорт з E-Staff</a>
                        </label>
                    </div>
                </div>
            </form>
            <div class="howAddZipResume pull-left">
                <h5>
                    2.
                    {{ 'Select the candidate country in the archive:' | translate }}
                </h5>
                <span class="additional-text grey-color">{{ 'If they are not from the same country, you can choose 2 or 3 countries' | translate }}</span>
                <select-multi-async
                    translate-func="$root.translate"
                    show-select-all="false"
                    placeholder="'Enter a location'"
                    selected-limit="3"
                    selected-values="regionzip"
                    disabled="$root.disabledBtn"
                    fetch-options="(fetchCountries)"
                    on-change="(onSelectCountry)"
                    path-to-key="'value'"
                    input-search-threshold="inputSearchThreshold"
                ></select-multi-async>
            </div>
            <form class="howAddZipResume pull-left minorFormZip">
                <h5>3. {{ 'The archive has only resumes?' | translate }}</h5>
                <div class="pull-left">
                    <div ng-click="$root.disabledBtn || selectZipTypeFiles(true)" class="custom-radio-container">
                        <radio-component name="zipRadio" checked="zipTypeFiles === true"></radio-component>
                        <label class="custom-radiobutton__text">
                            {{ 'Only resumes files in the archive, no extra files' | translate }}
                        </label>
                    </div>
                </div>
                <div class="pull-left">
                    <div ng-click="$root.disabledBtn || selectZipTypeFiles(false)" class="custom-radio-container">
                        <radio-component name="zipRadio" checked="zipTypeFiles === false"></radio-component>
                        <label class="custom-radiobutton__text">
                            {{ 'The archive has not only a resumes, need to ignore the extra' | translate }}
                        </label>
                    </div>
                </div>
            </form>
            <div
                ng-show="
                    $root.me.recrutRole === 'admin' ||
                    $root.me.recrutRole === 'recruter' ||
                    $root.me.recrutRole === 'freelancer' ||
                    $root.me.recrutRole === 'researcher'
                "
                class="downloadZipArchive"
            >
                <input
                    id="zip"
                    class="hidden"
                    ng-disabled="(!zipType || zipTypeFiles === undefined) || regionzip.length == 0 || $root.disabledBtn"
                    accept=".zip"
                    type="file"
                    oi-file="optionsForZIP"
                />
                <div>
                    <span ng-if="$root.hideTariff">{{ 'no more than 200 MB' | translate }}</span>
                    <span ng-if="!$root.hideTariff">
                        {{ 'Up to 200 MB per 1 download | Up to 800 MB overall' | translate }}
                    </span>
                    <button-component
                        current-lang="$root.currentLang"
                        on-click="(checkValidZip)"
                        disabled="$root.disabledBtn"
                        text="'Choose and download the ZIP-archive' | translate"
                    ></button-component>
                </div>
            </div>
            <div ng-show="uploading.length" class="progress-wrapper">
                <div class="titleForProgress">
                    <span>
                        {{ 'File is loading. Please stay on this page, so as not to interrupt the upload' | translate }}
                    </span>
                </div>
                <div class="progress">
                    <div
                        class="progress-bar"
                        ng-style="{ width: uploading[0].item.progress + '%' }"
                        role="progressbar"
                        aria-valuenow="{{ uploading[0].item.progress }}"
                        aria-valuemin="0"
                        aria-valuemax="100"
                    >
                        <span class="sr-only"></span>
                    </div>
                </div>
                {{ uploading[0].item.loaded }} bytes {{ uploading[0].item.size }} bytes
            </div>
        </div>
        <div ng-show="zipUploads.length > 0" class="col-lg-12 status-info">
            <table class="table">
                <thead>
                    <tr>
                        <th>
                            <span>{{ 'File name' | translate }}</span>
                        </th>
                        <th>
                            <span>{{ 'Loaded' | translate }}</span>
                        </th>
                        <th>
                            <span>{{ 'name' | translate }}</span>
                        </th>
                        <th>
                            <span>{{ 'Size' | translate }}</span>
                        </th>
                        <th>
                            <span>{{ 'Status' | translate }}</span>
                        </th>
                        <th>
                            <span class="align-center">{{ 'resume file found' | translate }}</span>
                        </th>
                        <th>
                            <span class="align-center">{{ 'added new candidates' | translate }}</span>
                        </th>
                        <th>
                            <span class="align-center">{{ 'Updated candidates2' | translate }}</span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="zipUpload in zipUploads| orderBy:'-dc'">
                        <td>
                            <div class="ellipsis" title="{{ zipUpload.fileName }}">{{ zipUpload.fileName }}</div>
                        </td>
                        <td>
                            <date-format-base
                                translate-func="$root.translate"
                                with-today="true"
                                current-lang="$root.currentLang"
                                date="zipUpload.dc"
                                full-month="false"
                            ></date-format-base>
                        </td>
                        <td class="responsible">
                            <a href="#/users/{{ zipUpload.userId }}">
                                <span ng-if="$root.useAmericanNameStyle">{{ zipUpload.user.fullNameEn }}</span>
                                <span ng-if="!$root.useAmericanNameStyle">{{ zipUpload.user.fullName }}</span>
                            </a>
                        </td>
                        <td>
                            <span>{{ zipUpload.sizeMb }} Mb</span>
                        </td>
                        <td>
                            <span class="status" ng-class="zipUpload.status">{{ zipUpload.status }}</span>
                        </td>
                        <td>
                            <span class="align-center" ng-class="{ notFinished: zipUpload.status != 'finished' }">
                                {{ zipUpload.files }}
                            </span>
                        </td>
                        <td>
                            <div
                                ng-class="{ notFinished: zipUpload.status != 'finished' }"
                                style="display: flex; justify-content: center; align-items: center; gap: 8px"
                            >
                                <span class="align-center">
                                    {{ zipUpload.newResume }}
                                </span>
                                <i
                                    ng-if="zipUpload.excelId"
                                    ng-click="downloadArchiveNotParseCandidates(zipUpload.excelId)"
                                    class="fa fa-file-excel-o"
                                    style="color: #00b549; cursor: pointer"
                                    aria-hidden="true"
                                    title="{{ 'Download a list of files that have not been added to the database' | translate }}"
                                ></i>
                            </div>
                        </td>
                        <td>
                            <span ng-class="{ notFinished: zipUpload.status != 'finished' }">
                                <span class="align-center">
                                    {{ zipUpload.duplicateCandidates }}
                                </span>
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
