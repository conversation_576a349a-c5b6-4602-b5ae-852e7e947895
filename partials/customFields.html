<section class="block-custom-fields">
    <div ng-show="customFields.isEmpty()">
        <div class="intro">
            <h3 class="main-page-title" translate="Custom fields"></h3>
            <span class="intro__descr" translate="Use the custom fields creation function to overboost your performance"></span>
        </div>

        <div class="features-info">
            <h5
                class="features__title"
                translate="The possibility to create your own custom fields in different sections of CleverStaff is your opportunity to :"
            ></h5>

            <div class="features-wrap">
                <div class="feature">
                    <div class="feature__img">
                        <img src="/images/sprite/custom-fields/feature-1.png" alt="" />
                    </div>
                    <div class="feature__descr">
                        <span translate="Tune the system in more precise way to match with your workflows"></span>
                    </div>
                </div>
                <div class="feature">
                    <div class="feature__img">
                        <img src="/images/sprite/custom-fields/feature-2.png" alt="" />
                    </div>
                    <div class="feature__descr">
                        <span translate="Choose from 4 types of fields you can create"></span>
                    </div>
                </div>
                <div class="feature">
                    <div class="feature__img">
                        <img src="/images/sprite/custom-fields/feature-3.png" alt="" />
                    </div>
                    <div class="feature__descr">
                        <span translate="Display the custom fields information in the analytical reports"></span>
                    </div>
                </div>
                <div class="feature">
                    <div class="feature__img">
                        <img src="/images/sprite/custom-fields/feature-4.png" alt="" />
                    </div>
                    <div class="feature__descr">
                        <span translate="Perform the search within these fields"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="features-btn">
            <a ng-click="addNewFieldModal()" class="btn_default btn_success" type="button">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    version="1.1"
                    fill="white"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                >
                    <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
                </svg>
                {{ 'Add new field' | translate }}
            </a>
        </div>
    </div>

    <div ng-show="!customFields.isEmpty()" class="custom-fields">
        <div class="intro inline">
            <div class="into__wrap">
                <!--                <div class="main-page-title" translate="Custom fields"></div>-->
                <p class="intro__descr">
                    <span translate="Create custom fields, which will be displayed in vacancies, candidates and clients description."></span>
                    <br class="hidden-xs" />
                    <span translate="You can remove or edit them at any time"></span>
                </p>
            </div>

            <div class="btn-wrap hidden_laptop">
                <a ng-click="addNewFieldModal()" class="btn_default btn_success" type="button">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        version="1.1"
                        fill="white"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                    >
                        <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
                    </svg>
                    {{ 'Add new field' | translate }}
                </a>
            </div>
        </div>

        <div class="fields-types">
            <div class="fields-type">
                <div class="fields-type__title c-green">
                    <img src="/images/sprite/vacancies.svg" alt="" />
                    <h6 class="form-title-main" translate="Vacancies"></h6>
                </div>
                <div ng-show="customFields.isEmptyByType('vacancy')" class="fields-type__fields" custom-fields-scroll-bar>
                    <div class="fields__header">
                        <div class="fields__header__name">
                            <span translate="Field name"></span>
                        </div>
                        <div class="fields__header__type">
                            <span translate="Field type"></span>
                        </div>
                    </div>
                    <div class="field" ng-class="{ 'tooltip-top': $last }" ng-repeat="field in customFields['vacancy']">
                        <div class="field__title">
                            <span>{{ field.title }}</span>
                        </div>
                        <div class="field__actions">
                            <div class="field__type">
                                {{ field.type | customFieldType | translate }}
                            </div>
                            <div class="btn-wrap hidden_laptop">
                                <div
                                    ng-click="editCustomFieldModal(field)"
                                    class="tags-main-item-icon pencilHover"
                                    style="margin-right: 7px; top: 2px; position: relative"
                                    tooltip-placement="{{ $last ? 'top' : 'bottom' }}"
                                    tooltip-class="tooltip-dark"
                                    uib-tooltip="{{ 'Edit-field' | translate }}"
                                >
                                    <svg width="13" height="12" viewBox="0 0 13 12" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M7.37333 4.01333L7.98667 4.62667L1.94667 10.6667H1.33333V10.0533L7.37333 4.01333ZM9.77333 0C9.60667 0 9.43333 0.0666666 9.30667 0.193333L8.08667 1.41333L10.5867 3.91333L11.8067 2.69333C12.0667 2.43333 12.0667 2.01333 11.8067 1.75333L10.2467 0.193333C10.1133 0.06 9.94667 0 9.77333 0ZM7.37333 2.12667L0 9.5V12H2.5L9.87333 4.62667L7.37333 2.12667Z"
                                        />
                                    </svg>
                                </div>
                                <img
                                    ng-click="removeCustomField(field)"
                                    class="rotate last"
                                    src="/images/sprite/close-icon.svg"
                                    tooltip-placement="{{ $last ? 'top' : 'bottom' }}"
                                    tooltip-class="tooltip-dark"
                                    uib-tooltip="{{ 'Delete' | translate }}"
                                />
                            </div>
                        </div>
                        <div class="mandatory_star" ng-show="field.mandatory" title="{{ 'RequiredCustomFields_hint.vacancy' | translate }}""> *
                    </div>
                </div>
            </div>
        </div>

        <div class="fields-type">
            <div class="fields-type__title c-orange">
                <img src="/images/sprite/custom-fields/candidates.svg" alt="" />
                <h6 class="form-title-main" translate="Candidates"></h6>
            </div>
            <div ng-show="customFields.isEmptyByType('candidate')" class="fields-type__fields" custom-fields-scroll-bar>
                <div class="fields__header">
                    <div class="fields__header__name">
                        <span translate="Field name"></span>
                    </div>
                    <div class="fields__header__type">
                        <span translate="Field type"></span>
                    </div>
                </div>
                <div class="field" ng-class="{ 'tooltip-top': $last }" ng-repeat="field in customFields['candidate']">
                    <div class="field__title">
                        <span>{{ field.title }}</span>
                    </div>
                    <div class="field__actions">
                        <span class="field__type">{{ field.type | customFieldType | translate }}</span>
                        <div class="btn-wrap hidden_laptop">
                            <div
                                ng-click="editCustomFieldModal(field)"
                                class="tags-main-item-icon pencilHover"
                                style="margin-right: 7px; top: 2px; position: relative"
                                tooltip-placement="{{ $last ? 'top' : 'bottom' }}"
                                tooltip-class="tooltip-dark"
                                uib-tooltip="{{ 'Edit-field' | translate }}"
                            >
                                <svg width="13" height="12" viewBox="0 0 13 12" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M7.37333 4.01333L7.98667 4.62667L1.94667 10.6667H1.33333V10.0533L7.37333 4.01333ZM9.77333 0C9.60667 0 9.43333 0.0666666 9.30667 0.193333L8.08667 1.41333L10.5867 3.91333L11.8067 2.69333C12.0667 2.43333 12.0667 2.01333 11.8067 1.75333L10.2467 0.193333C10.1133 0.06 9.94667 0 9.77333 0ZM7.37333 2.12667L0 9.5V12H2.5L9.87333 4.62667L7.37333 2.12667Z"
                                    />
                                </svg>
                            </div>
                            <img
                                ng-click="removeCustomField(field)"
                                class="rotate last"
                                src="/images/sprite/close-icon.svg"
                                tooltip-placement="{{ $last ? 'top' : 'bottom' }}"
                                tooltip-class="tooltip-dark"
                                uib-tooltip="{{ 'Delete' | translate }}"
                            />
                        </div>
                    </div>
                    <div ng-show="field.mandatory" class="mandatory_star" title="{{ 'RequiredCustomFields_hint.client' | translate }}">*</div>
                </div>
            </div>
        </div>

        <div class="fields-type">
            <div class="fields-type__title c-blue">
                <img src="/images/sprite/customers.svg" alt="" />
                <h6 class="form-title-main" translate="Clients"></h6>
            </div>
            <div ng-show="customFields.isEmptyByType('client')" class="fields-type__fields" custom-fields-scroll-bar>
                <div class="fields__header">
                    <div class="fields__header__name">
                        <span translate="Field name"></span>
                    </div>
                    <div class="fields__header__type">
                        <span translate="Field type"></span>
                    </div>
                </div>
                <div class="field" ng-class="{ 'tooltip-top': $last }" ng-repeat="field in customFields['client']">
                    <div class="field__title">
                        <span>{{ field.title }}</span>
                    </div>

                    <div class="field__actions">
                        <span class="field__type">{{ field.type | customFieldType | translate }}</span>
                        <div class="btn-wrap hidden_laptop">
                            <div
                                ng-click="editCustomFieldModal(field)"
                                class="tags-main-item-icon pencilHover"
                                style="margin-right: 7px; top: 2px; position: relative"
                                tooltip-placement="{{ $last ? 'top' : 'bottom' }}"
                                tooltip-class="tooltip-dark"
                                uib-tooltip="{{ 'Edit-field' | translate }}"
                            >
                                <svg width="13" height="12" viewBox="0 0 13 12" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M7.37333 4.01333L7.98667 4.62667L1.94667 10.6667H1.33333V10.0533L7.37333 4.01333ZM9.77333 0C9.60667 0 9.43333 0.0666666 9.30667 0.193333L8.08667 1.41333L10.5867 3.91333L11.8067 2.69333C12.0667 2.43333 12.0667 2.01333 11.8067 1.75333L10.2467 0.193333C10.1133 0.06 9.94667 0 9.77333 0ZM7.37333 2.12667L0 9.5V12H2.5L9.87333 4.62667L7.37333 2.12667Z"
                                    />
                                </svg>
                            </div>
                            <img
                                ng-click="removeCustomField(field)"
                                class="rotate last"
                                src="/images/sprite/close-icon.svg"
                                tooltip-placement="{{ $last ? 'top' : 'bottom' }}"
                                tooltip-class="tooltip-dark"
                                uib-tooltip="{{ 'Delete' | translate }}"
                            />
                        </div>
                    </div>
                    <div ng-show="field.mandatory" class="mandatory_star" title="{{ 'RequiredCustomFields_hint.client' | translate }}">*</div>
                </div>
            </div>
        </div>
    </div>

    <!--    <div class="button-wrap-sm">-->
    <!--        <a ng-click="addNewFieldModal()" class="btn_default btn_success" type="button">-->
    <!--            <svg-->
    <!--                xmlns="http://www.w3.org/2000/svg"-->
    <!--                xmlns:xlink="http://www.w3.org/1999/xlink"-->
    <!--                version="1.1"-->
    <!--                fill="white"-->
    <!--                width="24"-->
    <!--                height="24"-->
    <!--                viewBox="0 0 24 24"-->
    <!--            >-->
    <!--                <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />-->
    <!--            </svg>-->
    <!--            {{ 'Add new field' | translate }}-->
    <!--        </a>-->
    <!--    </div>-->
</section>
