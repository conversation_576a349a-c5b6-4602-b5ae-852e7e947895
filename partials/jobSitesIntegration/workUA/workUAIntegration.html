<div class="work-ua-integration integration-wrapper">
    <div ng-if="vm.integrated" class="integratedBlock">
        <div class="integratedBlock__header">
            <div class="integratedBlock-titles-wrapper">
                <div class="integratedBlock__title">Work.ua</div>
                <!--prettier-ignore-->
                <div ng-if="vm.isIntegrated" class="integratedBlock__subTitle">
                    ({{ 'published' | translate }}
                    <a ng-if="vm.vacancyPublisher.userId !== $root.me.userId" target="_blank" href="#users/{{ vm.vacancyPublisher.userId }}">
                        {{ vm.vacancyPublisher.fullName }}
                    </a><span ng-if="vm.vacancyPublisher.userId === $root.me.userId">{{ 'by me' | translate }}</span>)
                </div>
            </div>
            <div
                ng-if="vm.isIntegrated"
                ng-click="vm.integrateWorkUA(vm, true)"
                class="integratedBlock__editBtn"
                ng-class="{ 'forbid-click': !vm.vacancyStatistic.editable }"
            >
                <img src="images/redesign/svg-icons/pencil.svg" alt="" />
            </div>
        </div>
        <div class="integratedBlock__nav">
            <div ng-if="!vm.isIntegrated" class="integratedBlock__nav__publish">
                <button-component
                    ng-click="vm.integrateWorkUA(vm, false)"
                    ng-disabled="vm.isIntegrated"
                    type="'secondary'"
                    text="'Publish' | translate"
                ></button-component>
            </div>

            <div ng-if="!vm.isIntegrated" class="integratedBlock__nav__connect">
                <button-component ng-click="vm.connectVacancyHandler(vm)" type="'secondary'" text="'connect' | translate"></button-component>
                <i
                    class="hint-info-icon__grey"
                    style="display: block"
                    tooltip-placement="top-right"
                    tooltip-class="tooltip-info-black"
                    uib-tooltip="{{
                        'You can link a vacancy in CleverStaff and an already published vacancy at work.ua/rabota.ua in order to get responses and statistics from it'
                            | translate : { value: 'work.ua' }
                    }}"
                    aria-hidden="true"
                ></i>
            </div>

            <div ng-if="vm.isIntegrated" class="integratedBlock__buttons__disconnect">
                <button-component ng-click="vm.disconnectVacancy(vm)" type="'danger'" text="'disconnect' | translate"></button-component>
                <i
                    class="hint-info-icon__grey"
                    style="display: block"
                    tooltip-placement="top-right"
                    tooltip-class="tooltip-info-black"
                    uib-tooltip="{{ 'You can break the integration with the published vacancy' | translate : { value: 'work.ua' } }}"
                    aria-hidden="true"
                ></i>
            </div>

            <button-component
                ng-click="vm.removePublish(vm)"
                type="'secondary'"
                text="'Archive' | translate"
                disabled="!vm.isIntegrated || !vm.vacancyStatistic.editable"
            ></button-component>
        </div>

        <div ng-if="vm.isIntegrated" class="integratedBlock__links">
            <div class="integratedBlock__links-link">
                <img src="images/redesign/svg-icons/link.svg" alt="" />
                <a class="post-link" ng-href="{{ vm.vacancyStatistic.link }}" target="_blank">
                    {{ 'Post link (for the candidate)' | translate }}
                </a>
            </div>
            <div class="integratedBlock__links-link">
                <img src="images/redesign/svg-icons/link.svg" alt="" />
                <a class="post-link" ng-href="{{ vm.vacancyStatistic.personalVacancyLink }}" target="_blank">
                    {{ 'Post link (for the recruiter)' | translate }}
                </a>
            </div>
        </div>
        <div ng-if="vm.isIntegrated" class="integratedBlock__info">
            <div class="integratedBlock__endPublication">
                <span class="integratedBlock__endPublication_title" translate="End date of publication"></span>
                <span
                    class="integratedBlock__endPublication_date"
                    dynamic-translate-date-format-short
                    date="vm.vacancyStatistic.dateExpire"
                    item="vm.vacancyStatistic"
                    ng-bind="vm.vacancyStatistic.parsedDate"
                ></span>
            </div>
            <div class="integratedBlock__advice">
                <div class="integratedBlock__advice-title">
                    <span>{{ 'Continue posting' | translate }}</span>
                    <i
                        class="hint-info-icon__grey"
                        style="display: block"
                        tooltip-placement="top-right"
                        tooltip-class="tooltip-info-black"
                        uib-tooltip="{{ 'You can extend the publication in the publication settings on the' | translate }}"
                        aria-hidden="true"
                    ></i>
                </div>
                <a class="post-link" target="_blank" href="https://www.work.ua/">work.ua</a>
            </div>
        </div>
        <div ng-if="vm.isIntegrated && vm.vacancyStatistic.status" class="statistic">
            <div ng-click="vm.workStatistickSwitcher = !vm.workStatistickSwitcher" class="vacancyStatisticSwitcher">
                <span class="statistic-title" translate="Statistic"></span>
                <img ng-if="!vm.workStatistickSwitcher" alt="" src="images/redesign/svg-icons/chevron-down.svg" />
                <img ng-if="vm.workStatistickSwitcher" alt="" src="images/redesign/svg-icons/chevron-up.svg" />
            </div>
            <div ng-if="vm.workStatistickSwitcher" class="vacancy-statistic-wrapper">
                <div class="table-wrapper">
                    <table class="table">
                        <tbody>
                            <tr class="table-row">
                                <td class="table-head__data" translate="date"></td>
                                <td class="table-head__data" translate="Feedback_integration"></td>
                            </tr>
                            <tr class="table-row">
                                <td class="table-data" translate="total"></td>
                                <td class="table-data">{{ vm.vacancyStatistic.applyStatistic.total }}</td>
                            </tr>
                            <tr
                                class="table-row tbody-row"
                                ng-repeat="(applyStatisticValueDate, applyStatisticValue) in vm.vacancyStatistic.applyStatistic.byDate"
                            >
                                <td class="table-data">{{ applyStatisticValueDate | dateFormat2 }}</td>
                                <td class="table-data">{{ applyStatisticValue }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div ng-if="!vm.integrated" class="notIntegratedBlock">
        <div class="notIntegratedBlock__title">Work.ua</div>
        <!--prettier-ignore-->
        <div ng-if="vm.isIntegrated" class="integratedBlock__subTitle">
            ({{ 'published' | translate }}
            <a ng-if="vm.vacancyPublisher.userId !== $root.me.userId" target="_blank" href="#users/{{ vm.vacancyPublisher.userId }}">
                {{ vm.vacancyPublisher.fullName }}
            </a><span ng-if="vm.vacancyPublisher.userId === $root.me.userId">{{ 'by me' | translate }}</span>)
        </div>
        <div
            class="notIntegratedBlock__description"
            translate="To post a vacancy on _portal connect or link a vacancy in CleverStaff with an already published one the integration in the section:"
            translate-values="{'portal': 'Work.ua'}"
        ></div>
        <a ng-if="vm.isIntegrated" class="notIntegratedBlock__link" ui-sref="integration-page" target="_blank" translate="Integration Setup"></a>
        <div ng-if="!vm.isIntegrated && ($root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter')" class="connect-wrapper">
            <button-component
                ng-click="vm.connectVacancyHandler(vm)"
                disabled="vm.isIntegrated"
                type="'secondary'"
                text="'connect' | translate"
            ></button-component>
            <i
                class="hint-info-icon__grey"
                style="display: block"
                tooltip-placement="top-right"
                tooltip-class="tooltip-info-black"
                uib-tooltip="{{
                    'You can link a vacancy in CleverStaff and an already published vacancy at work.ua/rabota.ua in order to get responses and statistics from it'
                        | translate : { value: 'work.ua' }
                }}"
                aria-hidden="true"
            ></i>
        </div>
        <div ng-if="vm.isIntegrated" class="integratedBlock__links">
            <div class="integratedBlock__links-link">
                <img src="images/redesign/svg-icons/link.svg" alt="" />
                <a class="post-link" ng-href="{{ vm.vacancyStatistic.link }}" target="_blank">
                    {{ 'Post link (for the candidate)' | translate }}
                </a>
            </div>
        </div>
        <div ng-if="vm.isIntegrated && vm.vacancyStatistic.status" class="statistic">
            <div ng-click="vm.rabotaStatistickSwitcher = !vm.rabotaStatistickSwitcher" class="vacancyStatisticSwitcher">
                <span class="statistic-title" translate="Statistic"></span>
                <i ng-if="!vm.rabotaStatistickSwitcher" class="fa fa-angle-down icon-style"></i>
                <i ng-if="vm.rabotaStatistickSwitcher" class="fa fa-angle-up icon-style"></i>
            </div>
            <div ng-if="vm.rabotaStatistickSwitcher" class="vacancy-statistic-wrapper">
                <div class="table-wrapper">
                    <table class="table">
                        <tbody>
                            <tr class="table-row">
                                <td class="table-head__data" translate="date"></td>
                                <td class="table-head__data" translate="Feedback_integration"></td>
                            </tr>
                            <tr class="table-row">
                                <td class="table-data" translate="total"></td>
                                <td class="table-data">{{ vm.vacancyStatistic.applyStatistic.total }}</td>
                            </tr>
                            <tr
                                class="table-row tbody-row"
                                ng-repeat="(applyStatisticValueDate, applyStatisticValue) in vm.vacancyStatistic.applyStatistic.byDate"
                            >
                                <td class="table-data">{{ applyStatisticValueDate | dateFormat2 }}</td>
                                <td class="table-data">{{ applyStatisticValue }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
