<div id="candidate-add-page" class="candidate-add controller-view">
    <div class="header">
        <div class="candidate-add__title-block">
            <h2 ng-if="type === 'add'" class="main-title">
                {{ 'New candidate' | translate }}
            </h2>
            <h2 ng-if="type === 'edit'" class="main-title">
                {{ 'Edit candidate profile in seconds' | translate }}
            </h2>

            <span ng-show="me.orgParams.tarif === 'free'" class="migration-help-tablet">
                {{ 'Need help with the database migration from other ATS/recruitment software?' | translate }}
                <a href="mailto:<EMAIL>?subject={{ 'Help with data migration' | translate }}">
                    {{ 'Let us help!' | translate }}
                </a>
            </span>
            <span ng-show="me.orgParams.tarif === 'free'" class="migration-help-lg">
                {{ 'Need help with the database migration from other ATS/recruitment software?' | translate }}
                <a href="mailto:<EMAIL>?subject={{ 'Help with data migration' | translate }}">
                    {{ 'Let us help!' | translate }}
                </a>
            </span>
        </div>

        <button-component
            ng-click="saveCandidate()"
            class="header__save-button"
            text="type == 'add' ? ( 'add_candidate' | translate ) : ( 'save' | translate )"
        ></button-component>
    </div>

    <div
        id="duplicates"
        ng-if="duplicatesByNameAndContacts.length > 0 && showDuplicates"
        class="found-duplicate"
        ng-style="{ top: duplicatesTop, left: duplicatesLeft }"
    >
        <img ng-click="hideDuplicates()" class="base-icon found-duplicate__close-icon" src="images/redesign/svg-icons/close.svg" alt="" />
        <div>{{ 'Maybe, you have similar candidates in your database' | translate }}:</div>
        <div ng-repeat="duplicate in duplicatesByNameAndContacts">
            <label>
                <a class="link-green" ng-href="!#/candidates/{{ duplicate.localId }}" target="_blank">
                    {{ duplicate.fullName }}
                </a>
            </label>
            <br />
        </div>
    </div>

    <div ng-if="duplicatesByNameAndContacts.length > 0" class="error-message duplicate-alert pull-right">
        <span>{{ 'Maybe, you have similar candidates in your database' | translate }}:</span>
        <!--prettier-ignore-->
        <a class="link-green" ng-repeat="duplicate in duplicatesByNameAndContacts" ng-href="!#/candidates/{{ duplicate.localId }}" target="_blank">
            {{ duplicate.fullName }}<span ng-if="!$last">, </span>
        </a>
    </div>

    <section class="main-container">
        <section class="left-section">
            <div ng-show="$root.$state.current.data.pageName == 'Candidate add' && !$root.file && addedFromResume" class="base-block vertical-items">
                <h2 class="main-title">
                    {{ 'Add a candidate in seconds' | translate }}
                </h2>

                <div class="quick-add vertical-items">
                    <div class="quick-add__item">
                        <input id="resume" class="hidden-input" type="file" oi-file="optionsForResumeFile" accept=".doc, .docx, .pdf, .rtf" />
                        <img class="base-icon" src="images/redesign/svg-icons/upload.svg" alt="" />
                        <label ng-click="fastCandResumeClick()" for="resume">
                            {{ 'Upload CV' | translate }}
                        </label>
                    </div>
                    <div class="quick-add__item">
                        <img class="base-icon" src="images/redesign/svg-icons/text.svg" alt="" />
                        <label ng-click="fastCandTextClick()">
                            {{ 'Insert Text' | translate }}
                        </label>
                    </div>
                    <div class="quick-add__item">
                        <img class="base-icon" src="images/redesign/svg-icons/redirect.svg" alt="" />
                        <label ng-click="fastCandSiteClick()">
                            {{ 'Insert Link' | translate }}
                        </label>
                    </div>
                </div>

                <div ng-if="fastCandText && $root.$state.current.data.pageName == 'Candidate add'" class="quick-add-text">
                    <img ng-click="fastCandTextClick()" class="flex-block-close" src="images/redesign/svg-icons/close.svg" alt="" />

                    <span class="form-text">
                        {{ 'Insert resume text into the textarea below and click the Process button' | translate }}
                    </span>
                    <textarea id="ckeditorFastCand" ckeditor ng-model="$root.fastCandResumeText"></textarea>

                    <div class="buttons-group">
                        <button-component
                            ng-click="fastCandTextClick()"
                            class-name="'full-width'"
                            type="'secondary'"
                            text="'cancel' | translate"
                        ></button-component>
                        <button-component ng-click="fromText()" class-name="'full-width'" text="'Process' | translate"></button-component>
                    </div>
                </div>

                <div ng-show="fastCandSite && $root.$state.current.data.pageName == 'Candidate add'" class="flex-item fast-cand-site-item">
                    <div class="flex-item">
                        <input-component
                            placeholder="'Insert a link to resume online'"
                            value="fastCandResumeLinkSite"
                            on-change="(onChangeResumeLink)"
                        ></input-component>
                    </div>
                    <div class="flex-item flex-item-last flex-justify-center">
                        <button-component ng-click="fromLinkSite(fastCandResumeLinkSite)" text="'Process' | translate"></button-component>
                    </div>

                    <!--TODO: get back after a while-->
                    <div
                        ng-show="
                            (fastCandResumeLinkSite.indexOf('work.ua') > -1 ||
                                fastCandResumeLinkSite.indexOf('hh') > -1 ||
                                fastCandResumeLinkSite.indexOf('linkedin') > -1) &&
                            !$root.extensionHas &&
                            showResumeFromLinkSiteErrorFlag
                        "
                        class="flex-item"
                    >
                        <span>
                            {{ 'Candidates from' | translate }}
                            <span ng-show="fastCandResumeLinkSite.indexOf('work.ua') > -1">work.ua</span>
                            <span ng-show="fastCandResumeLinkSite.indexOf('hh') > -1">hh</span>
                            <span ng-show="fastCandResumeLinkSite.indexOf('linkedin') > -1">Linkedin</span>
                            {{ 'better to add by using CleverStaff extension for' | translate }}
                            <span ng-show="resumeBrowser == 'Chrome'">Chrome.</span>
                            <span ng-show="resumeBrowser == 'Firefox'">Firefox.</span>
                            <span ng-show="resumeBrowser != 'Firefox' && resumeBrowser != 'Chrome'">Firefox {{ 'or' | translate }} Chrome.</span>
                            <a ng-href="{{ getPluginDownloadingLink() }}" target="_blank">
                                {{ 'Install it now' | translate }}
                            </a>
                        </span>
                    </div>
                    <div
                        ng-show="
                            (fastCandResumeLinkSite.indexOf('work.ua') > -1 ||
                                fastCandResumeLinkSite.indexOf('hh') > -1 ||
                                fastCandResumeLinkSite.indexOf('linkedin') > -1) &&
                            $root.extensionHas &&
                            showResumeFromLinkSiteErrorFlag
                        "
                        class="flex-item"
                    >
                        {{ 'Just follow the link' | translate }}
                        <a target="_blank" href="{{ fastCandResumeLinkSite }}">{{ fastCandResumeLinkSite }}</a>
                        {{ "and click 'Save to CleverStaff'" | translate }}
                    </div>
                </div>
            </div>

            <div class="base-block vertical-items">
                <div class="flex-item add-photo-item">
                    <div class="personal-photo-wrapper flex-justify-center position-relative">
                        <photo-cropper-component photo-id="candidate.photo" page-name="'candidate'"></photo-cropper-component>
                    </div>
                </div>

                <div id="nameBlock" class="vertical-items">
                    <div class="field-block" ng-class="{ 'personal-info__last-name_american-style': $root.useAmericanNameStyle }">
                        <p class="field-block__title">
                            {{ 'last_name' | translate }}
                            <span class="form-title-star">*</span>
                        </p>
                        <input-component
                            placeholder="'last_name'"
                            value="candidate.lastName"
                            on-change="(onChangeLastName)"
                            on-blur="(onBlurLastName)"
                            is-error="(errorFields.lastName && candidate.lastName.length !== 50) ||
                                    (errorFields.lastName && candidate.lastName.length === 50) ||
                                    (candidate.lastName.length > 0 && dublicetesTypeName)"
                        ></input-component>
                        <span ng-if="errorFields.lastName && candidate.lastName.length !== 50" class="error-text">
                            {{ 'Enter candidate last name' | translate }}
                        </span>
                        <span ng-if="errorFields.lastName && candidate.lastName.length === 50" class="error-text">
                            {{ 'text_should_be_no_longer_than_50_characters' | translate }}
                        </span>
                    </div>

                    <div class="field-block">
                        <p class="field-block__title">
                            {{ 'first_name' | translate }}
                            <span class="form-title-star">*</span>
                        </p>
                        <input-component
                            placeholder="'first_name'"
                            value="candidate.firstName"
                            on-change="(onChangeFirstName)"
                            on-blur="(onBlurFirstName)"
                            is-error="(errorFields.firstName && candidate.firstName.length !== 50) ||
                                    (errorFields.firstName && candidate.firstName.length === 50) ||
                                    (candidate.firstName.length > 0 && dublicetesTypeName)"
                        ></input-component>
                        <span ng-if="errorFields.firstName && candidate.firstName.length !== 50" class="error-text">
                            {{ 'Enter candidate name' | translate }}
                        </span>
                        <span ng-if="errorFields.firstName && candidate.firstName.length === 50" class="error-text">
                            {{ 'text_should_be_no_longer_than_50_characters' | translate }}
                        </span>
                    </div>

                    <div class="field-block">
                        <p class="field-block__title">{{ 'middle_name' | translate }}</p>
                        <input-component
                            placeholder="'middle_name'"
                            value="candidate.middleName"
                            on-change="(onChangeMiddleName)"
                            on-blur="(onBlurMiddleName)"
                            is-error="errorFields.middleName && candidate.middleName.length === 50"
                        ></input-component>
                        <span ng-if="errorFields.middleName && candidate.middleName.length === 50" class="error-text">
                            {{ 'text_should_be_no_longer_than_50_characters' | translate }}
                        </span>
                    </div>
                </div>

                <div class="field-block">
                    <p class="field-block__title">{{ 'date_of_birth' | translate }}</p>
                    <div class="date-birthday-picker">
                        <mdp-date-birthday-picker
                            ng-click="fixDatePicker()"
                            class="date-field-picker"
                            ng-model="datepickerOfBirth"
                            mdp-open-on-click
                            mdp-max-date="maxDate"
                            mdp-placeholder="{{ 'date_of_birth' | translate }}"
                            mdp-format="DD/MM/YYYY"
                        >
                            <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                            <img
                                ng-if="datepickerOfBirth && datepickerOfBirth !== 'Выбрать дату' && datepickerOfBirth !== 'Choose date'"
                                ng-click="resetBirthDate()"
                                class="date-field-picker__clear-icon"
                                alt=""
                                src="/images/redesign/svg-icons/close.svg"
                            />
                        </mdp-date-birthday-picker>
                    </div>
                </div>

                <div class="sex-toggle-block">
                    <div ng-click="onChangeSex('male')" class="sex-toggle-block__item">
                        <radio-component value="role.value" checked="candidate.sex === 'male'"></radio-component>
                        {{ 'male' | translate }}
                    </div>
                    <div ng-click="onChangeSex('female')" class="sex-toggle-block__item">
                        <radio-component value="role.value" checked="candidate.sex === 'female'"></radio-component>
                        {{ 'female' | translate }}
                    </div>
                </div>

                <div tooltip-class="tooltip-info-black" uib-tooltip-html="$root.tooltips.Fill_in_these_fields_to_fill_the_progress_bar">
                    <semicircle-progress-bar percent="progressPct"></semicircle-progress-bar>
                </div>
            </div>

            <div class="base-block vertical-items">
                <h2 class="main-title">
                    {{ 'Contacts' | translate }}
                </h2>

                <div id="contactsBlock" class="vertical-items">
                    <!--Mobile Phone-->
                    <div class="contacts__repeat-items">
                        <label class="contacts__item-title">{{ 'phone' | translate }}</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="mphone in contacts.mphone track by $index">
                            <label class="display-none" for="{{ 'mphone-' + index }}"></label>
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        id="{{ 'mphone-' + index }}"
                                        class="contacts__input"
                                        ng-class="{
                                            error: (duplicatesByNameAndContacts.length > 0 && dublicetesTypeMphone) || phoneError[$index] || mphone.isError
                                        }"
                                        placeholder="{{ 'phone' | translate }}"
                                        ng-model="mphone.value"
                                        oninput="this.value = this.value.replace(/[^+0-9]/g, '');"
                                        type="text"
                                        ng-change="mphone.value = validationOfContactField(mphone,'phone')"
                                        ng-blur="progressUpdate();checkDuplicatesByNameAndContacts();mphone.isError = false"
                                        ng-focus="removePhoneError($index)"
                                    />
                                    <div
                                        ng-if="mphone.value"
                                        ng-click="mphone.value = ''; mphone.isError = false; progressUpdate()"
                                        class="contacts__clear-input"
                                    ></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, mphone, $index)"
                                        class="contacts__priority-icon"
                                        ng-attr-title="{{
                                            (mphone.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!mphone.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="mphone.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.mphone.length > 1"
                                    ng-click="removeInputField('mphone', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove phone number' | translate }}"
                                    src="images/redesign/svg-icons/close.svg"
                                ></div>
                            </div>

                            <span ng-if="mphone.isError" class="error-text">{{ 'too much characters in phone' | translate }}</span>
                        </div>
                    </div>

                    <!--Email-->
                    <div class="contacts__repeat-items">
                        <label class="contacts__item-title">E-mail</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="email in contacts.email track by $index">
                            <label class="display-none" for="{{ 'email-' + index }}"></label>
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        id="{{ 'email-' + index }}"
                                        class="contacts__input"
                                        ng-class="{
                                            'input-error': (duplicatesByNameAndContacts.length > 0 && dublicetesTypeEmail) || email.isError
                                        }"
                                        placeholder="E-mail"
                                        name="email"
                                        ng-model="email.value"
                                        ng-change=" email.value = validationOfContactField(email,'email')"
                                        ng-blur="validationOfEmailField(email);progressUpdate();checkDuplicatesByNameAndContacts();"
                                    />
                                    <div
                                        ng-if="email.value"
                                        ng-click="email.value = ''; validationOfEmailField(email); progressUpdate()"
                                        class="contacts__clear-input"
                                    ></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, email, $index)"
                                        class="contacts__priority-icon"
                                        ng-attr-title="{{
                                            (email.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!email.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="email.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.email.length > 1"
                                    ng-click="removeInputField('email', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove' | translate }} Email"
                                ></div>
                            </div>

                            <span ng-if="email.isError" class="error-text">{{ 'Enter correct email' | translate }}</span>
                        </div>
                    </div>

                    <!--Linkedin-->
                    <div class="contacts__repeat-items">
                        <label class="contacts__item-title">Linkedin</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="linkedin in contacts.linkedin track by $index">
                            <label class="display-none" for="{{ 'linkedin-' + index }}"></label>
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        id="{{ 'linkedin-' + index }}"
                                        class="contacts__input"
                                        ng-class="{
                                            'input-error':
                                                (duplicatesByNameAndContacts.length > 0 && dublicetesTypeLinkedin) || !linkedinError || linkedin.isError
                                        }"
                                        placeholder="Linkedin"
                                        ng-model="linkedin.value"
                                        ng-change="linkedin.value = validationOfContactField(linkedin,'linkedin')"
                                        ng-model-options="{debounce: 100}"
                                        type="text"
                                        ng-blur="progressUpdate();checkDuplicatesByNameAndContacts();"
                                    />
                                    <div ng-if="linkedin.value" ng-click="linkedin.value = ''; progressUpdate()" class="contacts__clear-input"></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, linkedin, $index)"
                                        class="contacts__priority-icon"
                                        ng-attr-title="{{
                                            (linkedin.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!linkedin.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="linkedin.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.linkedin.length > 1"
                                    ng-click="removeInputField('linkedin', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove phone number' | translate }}"
                                ></div>
                            </div>

                            <span ng-if="linkedin.isError" class="error-text">{{ 'too much characters in contact_2048' | translate }}</span>
                        </div>
                    </div>

                    <!--Djinni-->
                    <div ng-show="contacts.djinni.length > 0" class="contacts__repeat-items" ng-class="{ 'last-order': contacts.djinni.isLast }">
                        <label class="contacts__item-title">Djinni</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="djinni in contacts.djinni track by $index">
                            <label class="display-none" for="{{ 'djinni-' + index }}"></label>
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        id="{{ 'djinni-' + index }}"
                                        class="contacts__input"
                                        ng-class="{ 'input-error': djinni.isError }"
                                        placeholder="Djinni"
                                        ng-model="djinni.value"
                                        ng-change="djinni.value = validationOfContactField(djinni,'djinni')"
                                        ng-model-options="{debounce: 100}"
                                        type="text"
                                        ng-blur="progressUpdate();"
                                    />
                                    <div ng-if="djinni.value" ng-click="djinni.value = ''; progressUpdate()" class="contacts__clear-input"></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, djinni, $index)"
                                        class="contacts__priority-icon"
                                        ng-attr-title="{{
                                            (djinni.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!djinni.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="djinni.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.djinni.length > 1"
                                    ng-click="removeInputField('djinni', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove phone number' | translate }}"
                                ></div>
                            </div>

                            <span ng-if="djinni.isError" class="error-text">{{ 'too much characters in contact_2048' | translate }}</span>
                        </div>
                    </div>

                    <!--Facebook-->
                    <div ng-show="contacts.facebook.length > 0" class="contacts__repeat-items" ng-class="{ 'last-order': contacts.facebook.isLast }">
                        <label class="contacts__item-title">Facebook</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="facebook in contacts.facebook track by $index">
                            <label class="display-none" for="{{ 'facebook-' + index }}"></label>
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        id="{{ 'facebook-' + index }}"
                                        class="contacts__input"
                                        ng-class="{ 'input-error': facebook.isError }"
                                        placeholder="Facebook"
                                        ng-model="facebook.value"
                                        ng-change="facebook.value = validationOfContactField(facebook,'facebook')"
                                        ng-model-options="{debounce: 100}"
                                        type="text"
                                        ng-blur="progressUpdate();"
                                    />
                                    <div ng-if="facebook.value" ng-click="facebook.value = ''; progressUpdate()" class="contacts__clear-input"></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, facebook, $index)"
                                        class="contacts__priority-icon"
                                        ng-attr-title="{{
                                            (facebook.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!facebook.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="facebook.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.facebook.length > 1"
                                    ng-click="removeInputField('facebook', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove phone number' | translate }}"
                                ></div>
                            </div>

                            <span ng-if="facebook.isError" class="error-text">{{ 'too much characters in contact_2048' | translate }}</span>
                        </div>
                    </div>

                    <!--Telegram-->
                    <div ng-if="contacts.telegram.length > 0" class="contacts__repeat-items" ng-class="{ 'last-order': contacts.telegram.isLast }">
                        <label class="contacts__item-title">Telegram</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="telegram in contacts.telegram track by $index">
                            <label class="display-none" for="{{ 'telegram-' + index }}"></label>
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        id="{{ 'telegram-' + index }}"
                                        class="contacts__input"
                                        ng-class="{ 'input-error': telegram.isError }"
                                        placeholder="{{ 'Telegram' | translate }}"
                                        ng-model="telegram.value"
                                        ng-change="telegram.value = validationOfContactField(telegram,'telegram')"
                                        ng-model-options="{debounce: 100}"
                                        type="text"
                                        ng-blur="progressUpdate();telegram.isError = false"
                                    />
                                    <div
                                        ng-if="telegram.value"
                                        ng-click="telegram.value = ''; telegram.isError = false; progressUpdate()"
                                        class="contacts__clear-input"
                                    ></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, telegram, $index)"
                                        class="contacts__priority-icon"
                                        ng-attr-title="{{
                                            (telegram.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!telegram.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="telegram.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.telegram.length > 1"
                                    ng-click="removeInputField('telegram', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove' | translate }} Telegram"
                                ></div>
                            </div>

                            <span ng-if="telegram.isError" class="error-text">{{ 'too much characters in contact_2048' | translate }}</span>
                        </div>
                    </div>

                    <!--WhatsApp-->
                    <div ng-if="contacts.whatsApp.length > 0" class="contacts__repeat-items" ng-class="{ 'last-order': contacts.whatsApp.isLast }">
                        <label class="contacts__item-title">{{ 'WhatsApp' | translate }}</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="whatsapp in contacts.whatsApp track by $index">
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        class="contacts__input"
                                        ng-class="{ 'input-error': whatsapp.isError }"
                                        placeholder="{{ 'whatsApp' | translate }}"
                                        ng-change="whatsapp.value = validationOfContactField(whatsapp,'whatsApp')"
                                        oninput="this.value = this.value.replace(/[^+0-9]/g, '');"
                                        ng-model="whatsapp.value"
                                        ng-model-options="{debounce: 100}"
                                        type="text"
                                        ng-blur="progressUpdate();whatsapp.isError=false"
                                    />
                                    <div
                                        ng-if="whatsapp.value"
                                        ng-click="whatsapp.value = ''; whatsapp.isError = false; progressUpdate()"
                                        class="contacts__clear-input"
                                    ></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, whatsapp, $index)"
                                        class="contacts__priority-icon"
                                        ng-attr-title="{{
                                            (whatsapp.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!whatsapp.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="whatsapp.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.whatsApp.length > 1"
                                    ng-click="removeInputField('whatsApp', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove' | translate }} WhatsApp"
                                ></div>
                            </div>

                            <span ng-if="whatsapp.isError" class="error-text">{{ 'too much characters in contact' | translate }}</span>
                        </div>
                    </div>

                    <!--Viber-->
                    <div ng-if="contacts.viber.length > 0" class="contacts__repeat-items" ng-class="{ 'last-order': contacts.viber.isLast }">
                        <label class="contacts__item-title">{{ 'Viber' | translate }}</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="viber in contacts.viber track by $index">
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        class="contacts__input"
                                        ng-class="{ 'input-error': viber.isError }"
                                        placeholder="{{ 'viber' | translate }}"
                                        ng-change="viber.value = validationOfContactField(viber,'viber')"
                                        oninput="this.value = this.value.replace(/[^+0-9]/g, '');"
                                        ng-model="viber.value"
                                        ng-model-options="{debounce: 100}"
                                        type="text"
                                        ng-blur="progressUpdate();viber.isError=false"
                                    />
                                    <div
                                        ng-if="viber.value"
                                        ng-click="viber.value = ''; viber.isError = false; progressUpdate()"
                                        class="contacts__clear-input"
                                    ></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, viber, $index)"
                                        class="contacts__priority-icon"
                                        ng-attr-title="{{
                                            (viber.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!viber.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="viber.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.viber.length > 1"
                                    ng-click="removeInputField('viber', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove' | translate }} Viber"
                                ></div>
                            </div>

                            <span ng-if="viber.isError" class="error-text">{{ 'too much characters in contact' | translate }}</span>
                        </div>
                    </div>

                    <!--Skype-->
                    <div ng-if="contacts.skype.length > 0" class="contacts__repeat-items" ng-class="{ 'last-order': contacts.skype.isLast }">
                        <label class="contacts__item-title">Skype</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="skype in contacts.skype track by $index">
                            <label class="display-none" for="{{ 'skype-' + index }}"></label>
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        id="{{ 'skype-' + index }}"
                                        class="contacts__input"
                                        ng-class="{ 'input-error': skype.isError }"
                                        placeholder="Skype"
                                        ng-model="skype.value"
                                        ng-change="skype.value = validationOfContactField(skype,'skype')"
                                        ng-model-options="{debounce: 100}"
                                        type="text"
                                        ng-blur="progressUpdate(); skype.isError = false"
                                    />
                                    <div
                                        ng-if="skype.value"
                                        ng-click="skype.value = ''; skype.isError = false; progressUpdate()"
                                        class="contacts__clear-input"
                                    ></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, skype, $index)"
                                        class="contacts__priority-icon"
                                        ng-attr-title="{{
                                            (skype.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!skype.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="skype.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.skype.length > 1"
                                    ng-click="removeInputField('skype', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove' | translate }} Skype"
                                ></div>
                            </div>

                            <span ng-if="skype.isError" class="error-text">{{ 'too much characters in contact' | translate }}</span>
                        </div>
                    </div>

                    <!--Git-->
                    <div ng-if="contacts.github.length > 0" class="contacts__repeat-items" ng-class="{ 'last-order': contacts.github.isLast }">
                        <label class="contacts__item-title">Git</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="github in contacts.github track by $index">
                            <label class="display-none" for="{{ 'github-' + index }}"></label>
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        id="{{ 'github-' + index }}"
                                        class="contacts__input"
                                        ng-class="{ 'input-error': github.isError }"
                                        placeholder="Git"
                                        ng-model="github.value"
                                        ng-change="github.value = validationOfContactField(github,'github')"
                                        ng-model-options="{debounce: 100}"
                                        type="text"
                                        ng-blur="progressUpdate();"
                                    />
                                    <div ng-if="github.value" ng-click="github.value = ''; progressUpdate()" class="contacts__clear-input"></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, github, $index)"
                                        class="contacts__priority-icon"
                                        ng-attr-title="{{
                                            (github.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!github.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="github.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.github.length > 1"
                                    ng-click="removeInputField('github', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove' | translate }} Git"
                                ></div>
                            </div>

                            <span ng-if="github.isError" class="error-text">{{ 'too much characters in contact_2048' | translate }}</span>
                        </div>
                    </div>

                    <!--Behance-->
                    <div ng-if="contacts.behance.length > 0" class="contacts__repeat-items" ng-class="{ 'last-order': contacts.behance.isLast }">
                        <label class="contacts__item-title">{{ 'Behance' | translate }}</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="behance in contacts.behance track by $index">
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        class="contacts__input"
                                        ng-class="{ 'input-error': behance.isError }"
                                        placeholder="{{ 'behance' | translate }}"
                                        ng-change="behance.value = validationOfContactField(behance,'behance')"
                                        ng-model="behance.value"
                                        ng-model-options="{debounce: 100}"
                                        type="text"
                                        ng-blur="progressUpdate();"
                                    />
                                    <div ng-if="behance.value" ng-click="behance.value = ''; progressUpdate()" class="contacts__clear-input"></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, behance, $index)"
                                        class="contacts__priority-icon"
                                        ng-class="{ 'default-star': !behance.default, 'gold-star': behance.default }"
                                        ng-attr-title="{{
                                            (behance.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!behance.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="behance.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.behance.length > 1"
                                    ng-click="removeInputField('behance', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove' | translate }} Git"
                                ></div>
                            </div>

                            <span ng-if="behance.isError" class="error-text">{{ 'too much characters in contact_2048' | translate }}</span>
                        </div>
                    </div>

                    <!--Homepage-->
                    <div ng-if="contacts.homepage.length > 0" class="contacts__repeat-items" ng-class="{ 'last-order': contacts.homepage.isLast }">
                        <label class="contacts__item-title">{{ 'home_page' | translate }}</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="homepage in contacts.homepage track by $index">
                            <label class="display-none" for="{{ 'homepage-' + index }}"></label>
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        id="{{ 'homepage-' + index }}"
                                        class="contacts__input"
                                        ng-class="{ 'input-error': homepage.isError }"
                                        placeholder="{{ 'home_page' | translate }}"
                                        ng-change="homepage.value = validationOfContactField(homepage, 'homepage')"
                                        ng-model="homepage.value"
                                        ng-model-options="{debounce: 100}"
                                        type="text"
                                        ng-blur="progressUpdate();"
                                    />
                                    <div ng-if="homepage.value" ng-click="homepage.value = ''; progressUpdate()" class="contacts__clear-input"></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, homepage, $index)"
                                        class="contacts__priority-icon"
                                        ng-attr-title="{{
                                            (homepage.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!homepage.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="homepage.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.homepage.length > 1"
                                    ng-click="removeInputField('homepage', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove' | translate }} Home Page"
                                ></div>
                            </div>

                            <span ng-if="homepage.isError" class="error-text">{{ 'too much characters in contact_2048' | translate }}</span>
                        </div>
                    </div>

                    <!--Other-->
                    <div ng-if="contacts.other.length > 0" class="contacts__repeat-items" ng-class="{ 'last-order': contacts.other.isLast }">
                        <label class="contacts__item-title">{{ 'Other' | translate }}</label>

                        <div class="contacts-item" ng-class="{ 'contacts-item-last': $last }" ng-repeat="other in contacts.other track by $index">
                            <div class="contacts__input-row">
                                <div class="contacts__input-wrapper">
                                    <input
                                        class="contacts__input"
                                        ng-class="{ 'input-error': other.isError }"
                                        placeholder="{{ 'other' | translate }}"
                                        ng-change="other.value = validationOfContactField(other,'other')"
                                        ng-model="other.value"
                                        ng-model-options="{debounce: 100}"
                                        type="text"
                                        ng-blur="progressUpdate();"
                                    />
                                    <div ng-if="other.value" ng-click="other.value = ''; progressUpdate()" class="contacts__clear-input"></div>
                                    <div
                                        ng-click="selectFavoriteContacts(this, $event, other, $index)"
                                        class="contacts__priority-icon"
                                        ng-attr-title="{{
                                            (other.default ? 'Unmark as preferable contact method' : 'Mark as preferrable contact method') | translate
                                        }}"
                                    >
                                        <img ng-if="!other.default" class="base-icon" src="images/redesign/svg-icons/star-outline.svg" alt="" />
                                        <img
                                            ng-if="other.default"
                                            class="base-icon base-icon_color-green"
                                            src="images/redesign/svg-icons/star-filled.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>

                                <div
                                    ng-show="contacts.other.length > 1"
                                    ng-click="removeInputField('other', contacts, $index)"
                                    class="remove-icon"
                                    title="{{ 'Remove' | translate }} {{ 'Other' | translate }}"
                                ></div>
                            </div>

                            <span ng-if="other.isError" class="error-text">{{ 'too much characters in contact_2048' | translate }}</span>
                        </div>
                    </div>
                </div>

                <div class="field-block">
                    <p class="field-block__title">{{ 'add_contact' | translate }}</p>
                    <select-single-left-icon
                        placeholder="'Select contact type'"
                        options="contactTypes"
                        translate-options="true"
                        on-change="(addContact)"
                    ></select-single-left-icon>
                </div>
            </div>

            <!--Origin-->
            <div class="base-block vertical-items">
                <div class="field-block">
                    <p class="field-block__title">{{ 'source_is' | translate }}</p>
                    <select-single-virtualized
                        placeholder="'source_is'"
                        is-searchable="true"
                        options="allOriginsData"
                        translate-options="true"
                        selected-value="originModel"
                        on-change="(onChangeOrigin)"
                    ></select-single-virtualized>
                </div>

                <div class="field-block">
                    <p class="field-block__title">{{ 'status' | translate }}</p>
                    <select-single
                        placeholder="'status'"
                        options="status"
                        path-to-label="'name'"
                        translate-options="true"
                        selected-value="candidate.status"
                        on-change="(onChangeStatus)"
                        is-clearable="false"
                        is-error="candidateForm.status.$invalid && !candidateForm.status.$pristine"
                    ></select-single>
                </div>

                <div class="field-block">
                    <p class="field-block__title">
                        {{ 'add tags' | translate }}
                        <span ng-if="$root.me.recrutRole === 'admin'" ng-click="goToTags()" class="add-tags-link" class="link-style">
                            {{ 'Tags management' | translate }}
                        </span>
                        <i
                            ng-if="$root.me.recrutRole !== 'admin'"
                            class="hint-info-icon__grey"
                            tooltip-placement="right"
                            tooltip-class="tooltip-info-black"
                            uib-tooltip="{{ 'tag_hint_for_second_roles' | translate }}"
                            aria-hidden="true"
                        ></i>
                    </p>

                    <select-multi-virtualized
                        placeholder="'add tags'"
                        options="candidateGroupsOptions"
                        path-to-key="'candidateGroupId'"
                        selected-values="candidate.groups"
                        on-change="(onChangeTags)"
                    ></select-multi-virtualized>
                </div>
            </div>

            <div class="base-block attachments">
                <h2 class="main-title">{{ 'Attachments' | translate }}</h2>

                <input id="file" class="hidden-input" type="file" oi-file="options" />
                <input id="fileForEditFromResume" class="hidden-input" type="file" oi-file="optionsForEditFromResume" multiple="multiple" />

                <div ng-if="candidateProperties.files.length > 0 || fileForSave.length > 0 || (linksForSave.length != undefined && linksForSave.length != 0)">
                    <div class="attached-list">
                        <div
                            ng-if="type === 'add'"
                            class="item flex-row flex-align-center flex-space-between flex-item-block file-item"
                            ng-repeat="file in candidateProperties.files | orderBy:'-url'"
                        >
                            <div>
                                <img ng-if="!file.url" class="base-icon" src="images/redesign/svg-icons/attach.svg" alt="" />
                                <img ng-if="file.url" class="base-icon" src="images/redesign/svg-icons/attach.svg" alt="" />

                                <a
                                    ng-show="!file.url && !showEditFileName"
                                    class="file-name"
                                    ng-href="{{ serverAddress }}/getapp/{{ file.fileId }}/{{ file.fileName }}"
                                    title="{{ file.fileName }}"
                                >
                                    {{ file.fileName | fileNameCut : 0 : 20 }}
                                </a>
                                <a
                                    ng-show="file.url && !file.showEditFileName"
                                    class="file-name"
                                    href="{{ file.url }}"
                                    target="_blank"
                                    title="{{ file.fileName }}"
                                >
                                    {{ file.fileName | limitTo : 20 }}
                                    <span ng-show="file.fileName.length > 20">...</span>
                                </a>
                            </div>
                            <div ng-click="removeFile(file.attId)" class="remove-icon attached-item__remove"></div>
                        </div>

                        <div ng-if="type === 'add'" class="attached-item" ng-repeat="file in fileForSave">
                            <img class="base-icon" src="images/redesign/svg-icons/attach.svg" alt="" />
                            <a
                                class="attached-item__name"
                                ng-href="{{ serverAddress }}/getapp/{{ file.attId }}/{{ file.fileName }}"
                                title="{{ file.fileName }}"
                            >
                                <filename-ellipsis file-name="file.fileName"></filename-ellipsis>
                            </a>
                            <div ng-click="removeFile(file.attId)" class="remove-icon attached-item__remove"></div>
                        </div>

                        <!--Edit-->
                        <div ng-if="type === 'edit'" class="attached-item" ng-repeat="file in candidateProperties.files | orderBy:'-url'">
                            <img ng-if="!file.url" class="base-icon" src="images/redesign/svg-icons/attach.svg" alt="" />
                            <img ng-if="file.url" class="base-icon" src="images/redesign/svg-icons/link.svg" alt="" />

                            <a
                                ng-show="!file.url && !showEditFileName"
                                class="attached-item__name"
                                ng-href="{{ serverAddress }}/getapp/{{ file.fileId }}/{{ file.fileName }}"
                                title="{{ file.fileName }}"
                            >
                                <filename-ellipsis file-name="file.fileName"></filename-ellipsis>
                            </a>
                            <a ng-show="file.url && !file.showEditFileName" class="file-name" href="{{ file.url }}" target="_blank" title="{{ file.fileName }}">
                                {{ file.fileName | limitTo : 20 }}
                                <span ng-show="file.fileName.length > 20">...</span>
                            </a>

                            <div ng-click="removeFile(file.fileId)" class="remove-icon attached-item__remove"></div>
                        </div>

                        <div ng-if="type === 'edit'" class="attached-item" ng-repeat="file in fileForSave">
                            <img class="base-icon" src="images/redesign/svg-icons/attach.svg" alt="" />
                            <a
                                class="attached-item__name"
                                ng-href="{{ serverAddress }}/getapp/{{ file.attId }}/{{ file.fileName }}"
                                title="{{ file.fileName }}"
                            >
                                <filename-ellipsis file-name="file.fileName"></filename-ellipsis>
                            </a>
                            <div ng-click="removeFile(file.fileId)" class="remove-icon attached-item__remove"></div>
                        </div>
                    </div>
                </div>

                <div ng-if="linksForSave.length != undefined && linksForSave.length != 0" class="attached-list">
                    <div ng-show="link.url" class="attached-item" ng-repeat="link in linksForSave">
                        <img ng-show="link.url" class="base-icon" src="images/redesign/svg-icons/link.svg" alt="" />
                        <a class="attached-item__name" target="_blank" href="{{ link.url }}" title="{{ link.fileName }}">
                            {{ link.fileName }}
                        </a>
                        <div ng-click="removeLink(link.fileName)" class="remove-icon attached-item__remove"></div>
                    </div>
                </div>

                <div uib-dropdown is-open="status.isopen">
                    <a class="attachments-title" href="" uib-dropdown-toggle>
                        <img class="base-icon attachments-title__icon" src="images/redesign/svg-icons/attach.svg" alt="" />
                        {{ 'Attach file and link' | translate }}
                    </a>
                    <div class="attachments-menu" uib-dropdown-menu open-class="attachments-menu-opened" role="menu" aria-labelledby="single-button">
                        <label class="attachments-menu__item" for="file">
                            <img class="base-icon" src="images/redesign/svg-icons/attach.svg" alt="" />
                            {{ 'Attach file' | translate }}
                        </label>
                        <label ng-click="showAddLinkFunc()" class="attachments-menu__item">
                            <img class="base-icon" src="images/redesign/svg-icons/link.svg" alt="" />
                            {{ 'Attach link' | translate }}
                        </label>
                    </div>
                </div>

                <div ng-show="showAddLink" class="add-link-form">
                    <div class="field-block">
                        <p class="field-block__title">
                            {{ 'Title' | translate }}
                            <span class="form-title-star">*</span>
                        </p>
                        <input-component
                            placeholder="'Title'"
                            value="addLinkToCandidate.name"
                            on-change="(onChangeLinkName)"
                            is-error="addLinkErrorShow && addLinkToCandidate.name.length == 0"
                        ></input-component>
                    </div>

                    <div class="field-block">
                        <p class="field-block__title">
                            URL
                            <span class="form-title-star">*</span>
                        </p>
                        <input-component
                            placeholder="'URL'"
                            value="addLinkToCandidate.url"
                            on-change="(onChangeLinkUrl)"
                            is-error="(addLinkErrorShow && addLinkToCandidate.url.length == 0) || addLinkForm.url.$error.url"
                        ></input-component>
                    </div>

                    <div class="buttons-group">
                        <button-component
                            ng-click="closeAddLinkFunc()"
                            class-name="'full-width'"
                            type="'secondary'"
                            text="'cancel' | translate"
                        ></button-component>
                        <button-component ng-click="addLinkInCandidateStart()" class-name="'full-width'" text="'add' | translate"></button-component>
                    </div>
                </div>
            </div>
        </section>

        <section class="right-section">
            <div class="base-block fields-column">
                <div class="field-block">
                    <p class="field-block__title">
                        {{ 'desired position' | translate }}
                        <span class="form-title-star">*</span>
                    </p>

                    <select-single-async-creatable
                        placeholder="'position' | translate"
                        fetch-options="(getPositionsOptions)"
                        on-change="(onChangePosition)"
                        selected-value="positionModel"
                        is-error="errorFields.position"
                        initial-input-value="candidate.position"
                        input-search-threshold="inputSearchThreshold"
                        no-options-message="'Enter the desired position of the candidate' | translate"
                    ></select-single-async-creatable>

                    <span ng-if="errorFields.position && candidate.position.length === 200" class="error-text">
                        {{ 'text_should_be_no_longer_than_200_characters' | translate }}
                    </span>
                    <span ng-if="errorFields.position && candidate.position.length !== 200" class="error-text">
                        {{ 'enter_position_name' | translate }}
                    </span>
                </div>

                <div class="field-block">
                    <p class="field-block__title">{{ 'desired_salary' | translate }}</p>

                    <div class="salary-group">
                        <input-component
                            class="salary-group__input"
                            placeholder="'desired_salary' | translate"
                            value="candidate.salary"
                            on-change="(onChangeSalary)"
                            type="'number'"
                            is-error="errorFields.salary"
                        ></input-component>

                        <select-single
                            class="salary-group__currency"
                            options="currency"
                            selected-value="candidate.currency"
                            on-change="(onChangeCurrency)"
                            is-clearable="false"
                        ></select-single>
                    </div>
                </div>

                <div class="field-block">
                    <p class="field-block__title">{{ 'industry' | translate }}</p>
                    <select-single-virtualized
                        placeholder="'industry'"
                        options="industries"
                        translate-options="true"
                        is-searchable="true"
                        filter-match-from="'start'"
                        path-to-label="'value'"
                        selected-value="industryModel"
                        on-change="(onChangeIndustry)"
                    ></select-single-virtualized>
                </div>

                <div class="field-block">
                    <p class="field-block__title">{{ 'employment_type' | translate }}</p>
                    <select-multi-virtualized
                        placeholder="'employment_type'"
                        select-all-text="'Select all-v2' | translate"
                        options="employmentTypesOptions"
                        translate-options="true"
                        on-change="(onChangeEmploymentType)"
                        selected-values="employmentTypeModel"
                        is-error="errorFields.employmentType"
                        path-to-key="'value'"
                        is-searchable="false"
                    ></select-multi-virtualized>
                </div>

                <div class="field-block">
                    <p class="field-block__title">{{ 'Current company' | translate }}</p>
                    <input-component
                        placeholder="'Current company'"
                        value="candidate.currentWorkPlace"
                        on-change="(onChangeCurrentWorkingPlace)"
                        on-blue="(onBlurCurrentWorkingPlace)"
                        is-error="errorFields.currentWorkPlace"
                    ></input-component>

                    <span ng-if="errorFields.currentWorkPlace && candidate.currentWorkPlace.length === 100" class="error-text">
                        {{ 'text_should_be_no_longer_than_100_characters' | translate }}
                    </span>
                </div>

                <div class="field-block">
                    <p class="field-block__title">{{ 'Current position' | translate }}</p>
                    <input-component
                        placeholder="'Current position'"
                        value="candidate.currentPosition"
                        on-change="(onChangeCurrentPosition)"
                        on-blue="(onBlurCurrentPosition)"
                        is-error="errorFields.currentPosition"
                    ></input-component>

                    <span ng-if="errorFields.currentPosition && candidate.currentPosition.length === 100" class="error-text">
                        {{ 'text_should_be_no_longer_than_100_characters' | translate }}
                    </span>
                </div>

                <div class="field-block">
                    <p class="field-block__title">{{ 'Level' | translate }}</p>
                    <select-single
                        placeholder="'ChooseTheLevel'"
                        options="allRoles"
                        translate-options="true"
                        on-change="(onChangeRoleLevel)"
                        selected-value="roleLevelModel"
                    ></select-single>
                </div>
            </div>

            <div class="base-block vertical-items">
                <h2 class="main-title">{{ 'Location' | translate }}</h2>

                <div class="region-fields-group">
                    <div ng-if="!showGoogleLocationPicker" class="field-block">
                        <p class="field-block__title">{{ 'Country' | translate }}</p>
                        <select-single-virtualized
                            placeholder="'Choose the country'"
                            options="countriesOptions"
                            selected-value="candidate.country"
                            is-searchable="true"
                            on-change="(onChangeCountry)"
                        ></select-single-virtualized>
                    </div>

                    <div ng-show="candidate.country.value && !showGoogleLocationPicker" class="field-block">
                        <p class="field-block__title">{{ 'City' | translate }}</p>
                        <location-select-single-google-btn
                            placeholder="'Choose the city'"
                            options="citiesOptions"
                            selected-value="candidate.city"
                            is-searchable="true"
                            on-change="(onChangeCity)"
                            no-options-element="noOptionsElement"
                            translate-func="$root.translate"
                            filter-match-from="'start'"
                        ></location-select-single-google-btn>
                    </div>

                    <div ng-if="showGoogleLocationPicker" class="field-block">
                        <p class="field-block__title">{{ 'Location' | translate }}</p>
                        <select-single-async
                            id="googleLocation"
                            translate-func="$root.translate"
                            placeholder="'Enter a location'"
                            selected-value="googleLocationModel"
                            fetch-options="(fetchGooglePlaces)"
                            on-change="(onChangeGoogleLocation)"
                            path-to-key="'value'"
                            input-search-threshold="inputSearchThreshold"
                            initial-input-value="initialGooglePlacesInput"
                        ></select-single-async>
                    </div>
                </div>

                <div ng-click="readyRelocateHandler()" class="ready-relocate-toggle">
                    <checkbox-component is-checked="candidate.readyRelocate"></checkbox-component>
                    <span>{{ 'ready_to_relocate' | translate }}</span>
                </div>

                <div ng-if="regionsToRelocate.length" class="relocates-list">
                    <div class="relocate-item" ng-repeat="region in regionsToRelocate">
                        <!-- prettier-ignore -->
                        <span ng-if='!region.fromGoogle'>
                            {{ region.country.label }}<span ng-if='region.city.label'>, {{region.city.value === 'Any city'
                            ? region.city.label
                            : $root.getItemByLang('city', region.city.value, $root.currentLang) }}</span>
                        </span>
                        <span ng-if="region.fromGoogle && region.city">{{ region.country }}, {{ region.city }}</span>
                        <span ng-if="region.fromGoogle && !region.city">{{ region.fullName }}</span>

                        <div ng-click="deleteRegionToRelocate($index)" class="remove-icon relocate-item__remove"></div>
                    </div>
                </div>

                <div ng-if="candidate.readyRelocate">
                    <div class="region-fields-group">
                        <div ng-if="!showGoogleLocationPickerRelocate" class="field-block">
                            <p class="field-block__title">{{ 'Country' | translate }}</p>
                            <select-single-virtualized
                                placeholder="'Choose the country'"
                                options="countriesForRelocateOptions"
                                selected-value="countryForRelocate"
                                is-searchable="true"
                                on-change="(onChangeCountryForRelocate)"
                            ></select-single-virtualized>
                        </div>

                        <div
                            ng-show="countryForRelocate.value && countryForRelocate.countryEn !== 'Any' && !showGoogleLocationPickerRelocate"
                            class="field-block"
                        >
                            <p class="field-block__title">{{ 'City' | translate }}</p>
                            <location-select-single-google-btn
                                placeholder="'Choose the city'"
                                options="citiesOptionsForRelocate"
                                selected-value="cityForRelocate"
                                is-searchable="true"
                                on-change="(onChangeCityForRelocate)"
                                no-options-element="noOptionsElement"
                                translate-func="$root.translate"
                                filter-match-from="'start'"
                            ></location-select-single-google-btn>
                        </div>

                        <div ng-if="showGoogleLocationPickerRelocate" class="field-block">
                            <p class="field-block__title">{{ 'Location' | translate }}</p>
                            <select-single-async
                                id="googleLocationRelocate"
                                translate-func="$root.translate"
                                placeholder="'Enter a location'"
                                selected-value="googleRelocateLocationModel"
                                fetch-options="(fetchGooglePlaces)"
                                on-change="(onChangeRelocateGoogleLocation)"
                                path-to-key="'value'"
                                input-search-threshold="inputGoogleSearchThreshold"
                                initial-input-value="initialRelocateGooglePlacesInput"
                            ></select-single-async>
                        </div>
                    </div>
                </div>
            </div>

            <div id="skills-block" class="base-block fields-group">
                <h2 class="main-title">{{ 'Skills' | translate }}</h2>

                <div class="skills-block" ng-repeat="skillField in skillsModel track by $index">
                    <div class="skills-block__dropdowns-wrapper">
                        <div class="field-block">
                            <p ng-if="$index === 0" class="field-block__title">{{ 'Skill' | translate }}</p>
                            <select-single-virtualized-creatable
                                current-lang="$root.currentLang"
                                placeholder="'Select or create skill'"
                                options="skillsOptions"
                                selected-value="skillField.skill"
                                is-searchable="true"
                                is-grouped="true"
                                on-change="(onChangeSkill)"
                                additional-arg="$index"
                                no-options-message="'You have no skills. You can add them.'"
                            ></select-single-virtualized-creatable>
                        </div>

                        <div class="field-block">
                            <div ng-if="$index === 0" class="field-block__title">{{ 'Skill experience' | translate }}</div>
                            <select-single
                                current-lang="$root.currentLang"
                                placeholder="'Select role'"
                                translate-options="true"
                                is-clearable="false"
                                default-value="skillExperienceOptions[0]"
                                selected-value="skillField.experience"
                                options="skillExperienceOptions"
                                on-change="(onChangeSkillExperience)"
                                additional-arg="$index"
                            ></select-single>
                        </div>
                    </div>

                    <img
                        ng-click="removeSkillField($index)"
                        class="skills-block__remove-icon"
                        ng-style="$index === 0 && { 'margin-top': '24px' }"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>

                <button-with-icon
                    class-name="'full-width'"
                    on-click="(addNewSkillField)"
                    type="'secondary'"
                    text="'add_new_skill' | translate"
                    icon-name="'plus'"
                ></button-with-icon>

                <h2 ng-if="importedSkills.length" class="main-title">{{ 'Integrated skills' | translate }}</h2>

                <div ng-if="importedSkills.length" class="relocates-list">
                    <div class="relocate-item" ng-repeat="importedSkill in importedSkills track by $index">
                        {{ importedSkill.skillName }}
                        <div ng-click="deleteImportedSkill($index)" class="remove-icon relocate-item__remove"></div>
                    </div>
                </div>
            </div>

            <div id="languages-block" class="base-block fields-group">
                <h2 class="main-title">{{ 'languages' | translate }}</h2>

                <div class="skills-block" ng-repeat="languageField in languagesModel track by $index">
                    <div class="skills-block__dropdowns-wrapper">
                        <div class="field-block">
                            <p ng-if="$index === 0" class="field-block__title">{{ 'Language' | translate }}</p>
                            <select-single-virtualized
                                placeholder="'Select language'"
                                is-searchable="true"
                                options="languagesOptions"
                                selected-value="languageField.name"
                                on-change="(onChangeLanguage)"
                                additional-arg="$index"
                            ></select-single-virtualized>
                        </div>
                        <div class="field-block">
                            <p ng-if="$index === 0" class="field-block__title">{{ 'level_lang' | translate }}</p>
                            <select-single
                                placeholder="'selectLevel'"
                                translate-options="true"
                                is-clearable="false"
                                selected-value="languageField.level"
                                options="languageLevelOptions"
                                on-change="(onChangeLanguageLevel)"
                                additional-arg="$index"
                            ></select-single>
                        </div>
                    </div>

                    <img
                        ng-click="removeLanguageField($index)"
                        class="skills-block__remove-icon"
                        ng-style="$index === 0 && { 'margin-top': '24px' }"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>

                <button-with-icon
                    class-name="'full-width'"
                    on-click="(addNewLanguageField)"
                    type="'secondary'"
                    text="'add_new_lang' | translate"
                    icon-name="'plus'"
                ></button-with-icon>
            </div>

            <div class="base-block fields-column">
                <div class="vertical-items">
                    <h2 class="main-title">{{ 'Experience' | translate }}</h2>

                    <div>
                        <div class="field-block">
                            <p class="field-block__title">{{ 'total experience' | translate }}</p>
                            <select-single
                                placeholder="'Select experience'"
                                options="experience"
                                translate-options="true"
                                selected-value="candidate.expirence"
                                on-change="(onChangeExperience)"
                            ></select-single>
                        </div>
                    </div>

                    <candidate-job-experience-block
                        ng-if="fullDataCandidate"
                        candidate="fullDataCandidate"
                        redact-mode="true"
                        work-error-fields="workErrorFields"
                    ></candidate-job-experience-block>
                </div>

                <div class="vertical-items">
                    <h2 class="main-title">{{ 'education' | translate }}</h2>

                    <div>
                        <div class="field-block">
                            <p class="field-block__title">{{ 'Education level' | translate }}</p>
                            <select-single
                                placeholder="'Select education'"
                                options="educationLevels"
                                translate-options="true"
                                selected-value="candidate.education"
                                on-change="(onChangeEducation)"
                            ></select-single>

                            <span
                                ng-if="
                                    candidateForm.education.$invalid && !candidateForm.education.$pristine && candidateForm.education.$error.maxlength == true
                                "
                                class="error-text"
                            >
                                {{ 'text_should_be_no_longer_than_100_characters' | translate }}
                            </span>
                        </div>
                    </div>

                    <candidate-education-block
                        ng-if="fullDataCandidate"
                        candidate="fullDataCandidate"
                        redact-mode="true"
                        education-error-fields="educationErrorFields"
                    ></candidate-education-block>
                </div>
            </div>

            <div id="custom-fields-block" class="base-block vertical-items">
                <h2 class="main-title">{{ 'Custom fields' | translate }}</h2>

                <div
                    ng-if="customField.state == 'A' && $root.$state.current.data.pageName == 'Candidate add'"
                    class="field-block"
                    ng-repeat="customField in allObjCustomField track by customField.fieldId"
                    ng-switch="customField.type"
                >
                    <p class="field-block__title">
                        {{ customField.title }}
                        <span ng-if="customField.mandatory" class="form-title-star">*</span>
                    </p>

                    <div ng-switch-when="string">
                        <textarea
                            ckeditor
                            cf="true"
                            data-id="{{ customField.fieldId }}"
                            [id]="{{ customField.fieldId }}"
                            placeholder="{{ 'Character Restriction' | translate }}"
                            ng-model="fieldValues.value[$index]"
                            ng-change="addCustomFieldParams(fieldValues.value[$index], customField.fieldId, customField.fieldValue.fieldValueId, fieldValues, $index);onValidateMandatoryField(customField.fieldId, customField.type, customField, fieldValues.value[$index]);"
                            ng-blur="addCustomFieldParams(fieldValues.value[$index], customField.fieldId)"
                        ></textarea>
                        <span ng-if="fieldValues.descrError" class="error-text">{{ 'text_should_be_no_longer_than_3000_characters' | translate }}</span>
                        <div ng-repeat="stringError in validateTinyText track by $index">
                            <span ng-if="stringError.state && stringError.fieldId === customField.fieldId" class="error-text">
                                {{ 'Maximum number of characters exceeded by' | translate }}
                                {{ stringError.diffChars }}.
                            </span>
                        </div>
                    </div>

                    <select-single-virtualized
                        ng-if="customField.type == 'select'"
                        is-searchable="customField.params.length > 10"
                        placeholder="'Select option'"
                        options="customField.params"
                        path-to-label="'value'"
                        on-change="(onChangeSelectCustomField)"
                        selected-value="customField.value"
                        additional-arg="$index"
                    ></select-single-virtualized>

                    <div class="date-picker" ng-switch-when="date">
                        <mdp-date-picker
                            class="date-field-picker"
                            ng-model="datepickerOfCustomEdit"
                            mdp-open-on-click
                            name="{{ customField.fieldId }}"
                            ng-change="checkDateTime(customField, datepickerOfCustomEdit, 'datepickerOfCustomEdit');onValidateMandatoryField(customField.fieldId, customField.type,customField, datepickerOfCustomEdit);"
                            mdp-placeholder="{{ 'Choose date' | translate }}"
                            mdp-format="DD/MM/YYYY"
                        >
                            <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                        </mdp-date-picker>
                        <img
                            ng-show="customField.value"
                            ng-click="deleteDate(customField, datepickerOfCustomEdit = '');onValidateMandatoryField(customField.fieldId, customField.type,customField, datepickerOfCustomEdit);"
                            class="base-icon date-picker__clear-icon"
                            aria-hidden="true"
                            src="images/redesign/svg-icons/close.svg"
                            alt=""
                        />
                    </div>

                    <div class="date-picker" ng-switch-when="datetime">
                        <mdp-date-time-picker
                            class="date-field-picker"
                            ng-model="datepickerOfCustomEditTime"
                            mdp-open-on-click
                            name="{{ customField.fieldId }}"
                            mdp-auto-switch="true"
                            ng-change="checkDateTime(customField, datepickerOfCustomEditTime, 'datepickerOfCustomEditTime');onValidateMandatoryField(customField.fieldId, customField.type, customField, datepickerOfCustomEditTime);"
                            mdp-placeholder="{{ 'Choose date and time' | translate }}"
                            mdp-format="DD/MM/YYYY HH:mm"
                        >
                            <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                        </mdp-date-time-picker>
                        <img
                            ng-show="customField.value"
                            ng-click="deleteDate(customField, datepickerOfCustomEditTime = '');onValidateMandatoryField(customField.fieldId, customField.type, customField, datepickerOfCustomEditTime);"
                            class="base-icon date-picker__clear-icon"
                            src="images/redesign/svg-icons/close.svg"
                            aria-hidden="true"
                        />
                    </div>

                    <span ng-if="customField.isValidMandatory" class="error-text">{{ 'Empty mandatory field' | translate }}</span>
                </div>

                <!--Edit Custom Fields-->
                <div
                    ng-if="customField.state == 'A' && $root.$state.current.data.pageName == 'Candidate edit'"
                    class="field-block"
                    ng-repeat="customField in candidate.customFields track by customField.fieldId"
                    ng-switch="customField.type"
                >
                    <p class="field-block__title">
                        {{ customField.title }}
                        <span ng-if="customField.mandatory" class="form-title-star">*</span>
                    </p>

                    <div ng-switch-when="string">
                        <textarea
                            ckeditor
                            cf="true"
                            data-id="{{ customField.fieldId }}"
                            [id]="{{ customField.fieldId }}"
                            placeholder="{{ 'Character Restriction' | translate }}"
                            ng-model="customField.value"
                            ng-change="onChangeCustomFieldText(customField, customField.value)"
                            ng-blur="onValidateCustomField(customField)"
                        ></textarea>
                        <span ng-if="fieldValues.descrError" class="error-text">{{ 'text_should_be_no_longer_than_3000_characters' | translate }}</span>
                        <div ng-repeat="stringError in validateTinyText track by $index">
                            <span ng-if="stringError.state && stringError.fieldId === customField.fieldId" class="error-text">
                                {{ 'Maximum number of characters exceeded by' | translate }}
                                {{ stringError.diffChars }}.
                            </span>
                        </div>
                    </div>

                    <select-single-virtualized
                        ng-if="customField.type == 'select'"
                        is-searchable="customField.params.length > 10"
                        placeholder="'Select option'"
                        options="customField.params"
                        path-to-label="'value'"
                        on-change="(onChangeSelectCustomField)"
                        selected-value="customField.value"
                        additional-arg="customField"
                    ></select-single-virtualized>

                    <div class="date-picker" ng-switch-when="date">
                        <mdp-date-picker
                            class="date-field-picker"
                            ng-model="customField.value"
                            mdp-open-on-click
                            name="{{ customField.fieldId }}"
                            ng-change="onChangeCustomFieldDate(customField, customField.value)"
                            mdp-placeholder="{{ 'Choose date' | translate }}"
                            mdp-format="DD/MM/YYYY"
                        >
                            <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                        </mdp-date-picker>
                        <img
                            ng-show="customField.value"
                            ng-click="onChangeCustomFieldDate(customField, '')"
                            class="base-icon date-picker__clear-icon"
                            aria-hidden="true"
                            src="images/redesign/svg-icons/close.svg"
                            alt=""
                        />
                    </div>

                    <div class="date-picker" ng-switch-when="datetime">
                        <mdp-date-time-picker
                            class="date-field-picker"
                            ng-model="customField.value"
                            mdp-open-on-click
                            name="{{ customField.fieldId }}"
                            ng-change="onChangeCustomFieldDate(customField, customField.value)"
                            mdp-auto-switch="true"
                            mdp-placeholder="{{ 'Choose date and time' | translate }}"
                            mdp-format="DD/MM/YYYY HH:mm"
                        >
                            <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                        </mdp-date-time-picker>
                        <img
                            ng-show="customField.value"
                            ng-click="onChangeCustomFieldDate(customField, '')"
                            class="base-icon date-picker__clear-icon"
                            src="images/redesign/svg-icons/close.svg"
                            aria-hidden="true"
                        />
                    </div>

                    <span ng-if="customField.invalidMandatory" class="error-text">{{ 'Empty mandatory field' | translate }}</span>
                </div>

                <div ng-show="$root.me.recrutRole === 'admin'">
                    <a
                        ng-if="allObjCustomField.length > 0 || candidate.customFields.length > 0"
                        class="additional-action"
                        href="!#/company/custom-fields"
                        target="_blank"
                    >
                        <img class="additional-action__icon" src="/images/redesign/svg-icons/plus.svg" alt="" />
                        <span>{{ 'Add/Edit custom field' | translate }}</span>
                    </a>
                    <a
                        ng-if="allObjCustomField.length == 0 || candidate.customFields.length == 0"
                        class="additional-action"
                        href="!#/company/custom-fields"
                        target="_blank"
                    >
                        <img class="additional-action__icon" src="/images/redesign/svg-icons/plus.svg" alt="" />
                        <span>{{ 'Add custom fields' | translate }}</span>
                    </a>
                </div>
            </div>

            <div ng-show="itsEditCandidate && candidate.recalls.length" class="base-block vertical-items">
                <h2 class="main-title">{{ 'Recalls-from-public-page' | translate }}</h2>

                <div ng-show="recall.fields.custom.length" class="vertical-items recall" ng-repeat="recall in candidate.recalls track by $index">
                    <div ng-click="recall.showRecall = !recall.showRecall; scrollableTextarea(recall.showRecall)" class="recall__header">
                        <span class="recall__title">
                            {{ recall.vacancy.vacancyId.position }}
                            <span>
                                ({{
                                    recall.recallTemplate.templateName === 'Default'
                                        ? ('Default-recall-template' | translate)
                                        : recall.recallTemplate.templateName
                                }})
                            </span>
                        </span>
                        <div class="recall__header-icons">
                            <img
                                class="base-icon recall__arrow"
                                ng-class="{ recall__arrow_rotated: recall.showRecall }"
                                src="images/redesign/svg-icons/chevron-down.svg"
                                alt=""
                            />
                            <div
                                ng-click="deleteRecallModal(recall, $index); $event.stopPropagation();"
                                class="clear-icon"
                                title="{{ 'delete-recall-ico' | translate }}"
                            ></div>
                        </div>
                    </div>

                    <div ng-show="recall.showRecall" class="vertical-items">
                        <div class="field-block" ng-repeat="custom in recall.fields.custom" ng-switch="custom.type">
                            <p class="field-block__title">{{ custom.name }}</p>

                            <div class="recall__item" ng-switch-when="text">
                                <textarea
                                    class="recall__textarea"
                                    placeholder="{{ 'enter-text' | translate }}"
                                    name="custom.name"
                                    ng-model="custom.value"
                                    required="custom.mandatory"
                                ></textarea>
                                <div ng-click="deleteField(custom)" class="clear-icon recall__clear-icon" title="{{ 'Remove-recall-field' | translate }}"></div>
                            </div>

                            <div ng-switch-when="textFile">
                                <div class="recall__item">
                                    <textarea
                                        class="recall__textarea"
                                        placeholder="{{ 'enter-text' | translate }}"
                                        name="custom.name"
                                        ng-model="custom.value"
                                        required="custom.mandatory"
                                    ></textarea>
                                    <div
                                        ng-click="deleteField(custom)"
                                        class="clear-icon recall__clear-icon"
                                        title="{{ 'Remove-recall-field' | translate }}"
                                    ></div>
                                </div>

                                <div class="files">
                                    <div class="files__list" ng-repeat="file in custom.files">
                                        <filename-ellipsis title="{{ file.name }}" file-name="file.name"></filename-ellipsis>
                                    </div>
                                </div>
                            </div>

                            <div class="recall__item" ng-switch-when="select">
                                <select-single
                                    class="recall__select"
                                    placeholder="'Select option'"
                                    options="custom.options"
                                    on-change="(onChangeRecallSelect)"
                                    selected-value="{label: custom.value, value: custom.value}"
                                    additional-arg="custom"
                                ></select-single>

                                <div ng-click="deleteField(custom)" class="clear-icon recall__clear-icon" title="{{ 'Remove-recall-field' | translate }}"></div>
                            </div>

                            <div ng-switch-when="selectFile">
                                <div class="recall__item">
                                    <select-single
                                        class="recall__select"
                                        placeholder="'Select option'"
                                        options="custom.options"
                                        on-change="(onChangeRecallSelect)"
                                        selected-value="{label: custom.value, value: custom.value}"
                                        additional-arg="custom"
                                    ></select-single>

                                    <div
                                        ng-click="deleteField(custom)"
                                        class="clear-icon recall__clear-icon"
                                        title="{{ 'Remove-recall-field' | translate }}"
                                    ></div>
                                </div>

                                <div class="files">
                                    <div class="files__list" ng-repeat="file in custom.files">
                                        <filename-ellipsis title="{{ file.name }}" file-name="file.name"></filename-ellipsis>
                                    </div>
                                </div>
                            </div>

                            <div class="recall__item" ng-switch-when="date">
                                <div class="date-picker">
                                    <mdp-date-picker
                                        class="date-field-picker"
                                        ng-model="custom.value"
                                        mdp-open-on-click
                                        name="custom.name"
                                        mdp-placeholder="{{ 'Choose date' | translate }}"
                                        mdp-format="DD/MM/YYYY"
                                    >
                                        <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                                    </mdp-date-picker>
                                    <img
                                        ng-show="custom.value"
                                        ng-click="custom.value = ''"
                                        class="base-icon date-picker__clear-icon"
                                        aria-hidden="true"
                                        src="images/redesign/svg-icons/close.svg"
                                        alt=""
                                    />
                                </div>

                                <div ng-click="deleteField(custom)" class="clear-icon recall__clear-icon" title="{{ 'Remove-recall-field' | translate }}"></div>
                            </div>

                            <div class="recall__item" ng-switch-when="datetime">
                                <div class="date-picker">
                                    <mdp-date-time-picker
                                        class="date-field-picker"
                                        ng-model="custom.value"
                                        mdp-open-on-click
                                        name="custom.name"
                                        mdp-auto-switch="true"
                                        mdp-placeholder="{{ 'Choose date and time' | translate }}"
                                        mdp-format="DD/MM/YYYY HH:mm"
                                    >
                                        <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                                    </mdp-date-time-picker>
                                    <img
                                        ng-show="custom.value"
                                        ng-click="custom.value = ''"
                                        class="base-icon date-picker__clear-icon"
                                        src="images/redesign/svg-icons/close.svg"
                                        aria-hidden="true"
                                    />
                                </div>

                                <div ng-click="deleteField(custom)" class="clear-icon recall__clear-icon" title="{{ 'Remove-recall-field' | translate }}"></div>
                            </div>

                            <div class="inputs-wrapper__customs__items-item file" ng-switch-when="file">
                                <div ng-if="custom.fileLimit === 1" class="solo-file">
                                    <span>
                                        <span class="value" title="{{ custom.files[0].name }}">{{ custom.files[0].name | fileNameCut : 0 : 40 }}</span>
                                    </span>
                                </div>
                                <div ng-show="custom.files.length && custom.fileLimit > 1" class="files">
                                    <div class="files__list" ng-repeat="file in custom.files">
                                        <filename-ellipsis title="{{ file.name }}" file-name="file.name"></filename-ellipsis>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="base-block vertical-items">
                <h2 class="main-title">{{ 'description' | translate }}</h2>

                <textarea
                    id="ckeditor"
                    ckeditor
                    option="ckEditorDescriptionOptions"
                    ng-model="$root.descriptionFromResume"
                    ng-model-options="{debounce: 250}"
                    ng-change="progressUpdate()"
                ></textarea>
            </div>
        </section>
    </section>

    <div class="footer">
        <button-component
            ng-if="type == 'edit' && candidate != undefined && candidate.status != 'archived'"
            ng-click="deleteCandidate()"
            type="'danger'"
            text="'Remove the candidate' | translate"
        ></button-component>

        <button-component ng-click="saveCandidate()" text="type == 'add' ? ( 'add_candidate' | translate ) : ( 'save' | translate )"></button-component>
    </div>
</div>
