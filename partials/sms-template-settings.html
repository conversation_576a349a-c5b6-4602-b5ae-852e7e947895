<div class="sms-template-settings-block">
    <div class="wrapper">
        <div
            ng-show="($root.me.recrutRole === 'recruter' && $root.editEmailTemplatesValue === 'Y') || $root.me.recrutRole === 'admin'"
            class="template-for-edit"
        >
            <div class="main-row">
                <div class="editor-block central-block" ng-class="vm.notValidFields" style="border-radius: 5px; position: relative">
                    <div class="form-title">{{ 'The name of the template' | translate }}</div>
                    <span class="form-title-star">*</span>
                    <div style="position: relative">
                        <input
                            class="form-control name margin-top-null"
                            style="margin-bottom: 11px; font-size: 14px"
                            type="text"
                            placeholder="{{ 'The name of the template' | translate }}"
                            ng-model="vm.templateModel.name"
                            ng-change="vm.templateOnChange()"
                            ng-model-options="{debounce: 250}"
                        />
                        <span class="name-error-message" translate="Please fill the name field in"></span>
                    </div>

                    <div class="text-error-border" style="position: relative">
                        <textarea
                            id="{{ vm.editorId }}"
                            ng-if="vm.templateLoaded"
                            ng-readonly="($root.me.recrutRole === 'recruter' && $root.editEmailTemplatesValue === 'N' && $root.selectTemplateName == 'General templates' && !$root.isEmptyTemplate) || ($root.me.recrutRole !== 'admin' && $root.me.recrutRole !== 'recruter' && $root.selectTemplateName == 'General templates' && !$root.isEmptyTemplate)"
                            ckeditor
                            options="vm.ckEditorOptions"
                            ng-model="vm.templateModel.text"
                        ></textarea>
                        <span class="text-error-message" style="position: absolute; padding-top: 0" translate="Please fill the email text field in"></span>
                    </div>
                </div>
            </div>
            <div ng-show="vm.templateLoaded && vm.isTimeDateOperator" class="operator-info">{{ 'alphasms3' | translate }}</div>
        </div>
        <div class="template-for-preview">
            <h4
                ng-show="
                    ($root.me.recrutRole == 'admin' || ($root.me.recrutRole == 'recruter' && $root.editEmailTemplatesValue == 'Y')) &&
                    $root.selectTemplateName === 'General templates'
                "
            >
                <span translate="Preview"></span>
            </h4>
            <h4 ng-show="$root.selectTemplateName === 'My templates'"><span translate="Preview"></span></h4>
            <div class="main-row">
                <div class="preview-block central-block" style="border-radius: 5px">
                    <div class="preview-subject" ng-bind="vm.templateRendered.title"></div>
                    <div class="preview-wrapper">
                        <div ng-if="vm.templateLoaded" class="preview-text" ng-bind-html="vm.templateRendered.text | trust"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div ng-show="($root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter') && vm.templateChanged === true" class="buttons-row buttons">
        <button ng-show="!$root.isEmptyTemplate" ng-click="vm.discardChanges()" class="btn_default btn_empty btn-for-margin" translate="cancel"></button>
        <button
            ng-show="$root.isEmptyTemplate"
            ng-click="$root.discardTemplateNewChanges()"
            class="btn_default btn_empty btn-for-margin"
            translate="cancel"
        ></button>
        <button ng-click="vm.saveChangesCustomTemplates()" class="btn_default btn_success" translate="save"></button>
    </div>
</div>
