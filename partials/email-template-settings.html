<div class="email-template-settings-block">
    <div class="wrapper">
        <div ng-if="$root.selectTemplateName == 'My templates'" class="template-for-edit">
            <div class="main-row">
                <div class="editor-block central-block" ng-class="vm.notValidFields" style="border-radius: 5px; position: relative">
                    <div class="form-title">{{ 'The name of the template' | translate }}</div>
                    <span class="form-title-star">*</span>
                    <div style="position: relative">
                        <input
                            ng-if="$root.selectTemplateName == 'General templates' && !$root.isEmptyTemplate"
                            class="form-control name margin-top-null"
                            style="margin-bottom: 11px; font-size: 14px"
                            ng-disabled="$root.templateType || ($root.me.recrutRole === 'recruter' && $root.editEmailTemplatesValue === 'N' && $root.selectTemplateName == 'General templates') || ($root.me.recrutRole !== 'admin' && $root.me.recrutRole !== 'recruter' && $root.selectTemplateName == 'General templates')"
                            type="text"
                            title="{{ 'You cant change the name for this template' | translate }}"
                            placeholder="{{ 'The name of the template' | translate }}"
                            ng-model="vm.templateModel.name"
                            ng-change="vm.templateOnChange()"
                            ng-model-options="{debounce: 250}"
                        />
                        <input
                            ng-if="$root.selectTemplateName == 'My templates' || ($root.selectTemplateName == 'General templates' && $root.isEmptyTemplate)"
                            class="form-control name margin-top-null"
                            style="margin-bottom: 11px; font-size: 14px"
                            type="text"
                            placeholder="{{ 'The name of the template' | translate }}"
                            ng-model="vm.templateModel.name"
                            ng-change="vm.templateOnChange()"
                            ng-model-options="{debounce: 250}"
                        />
                        <span class="name-error-message" style="position: absolute; bottom: -33px" translate="Please fill the name field in"></span>
                    </div>
                    <div class="form-title" ng-style="vm.notValidName ? { 'padding-top': '10px' } : { 'padding-top': '0px' }" translate="Email subject"></div>
                    <span class="form-title-star">*</span>
                    <div style="position: relative" ng-style="vm.notValidTitle ? { 'padding-bottom': '10px' } : { 'padding-bottom': '0px' }">
                        <input
                            class="form-control subject margin-top-null"
                            style="font-size: 14px"
                            ng-disabled="($root.me.recrutRole === 'recruter' && $root.editEmailTemplatesValue === 'N' && $root.selectTemplateName == 'General templates' && !$root.isEmptyTemplate) || ($root.me.recrutRole !== 'admin' && $root.me.recrutRole !== 'recruter' && $root.selectTemplateName == 'General templates' && !$root.isEmptyTemplate)"
                            type="text"
                            placeholder="{{ 'Email subject' | translate }}"
                            ng-model="vm.templateModel.title"
                            ng-change="vm.templateOnChange()"
                            ng-model-options="{debounce: 250}"
                        />
                        <span class="title-error-message" style="position: absolute; bottom: -7px" translate="Please fill the subject field in"></span>
                    </div>
                    <div class="text-error-border" style="position: relative">
                        <textarea
                            id="{{ vm.editorId }}"
                            ng-if="vm.templateLoaded"
                            ng-readonly="($root.me.recrutRole === 'recruter' && $root.editEmailTemplatesValue === 'N' && $root.selectTemplateName == 'General templates' && !$root.isEmptyTemplate) || ($root.me.recrutRole !== 'admin' && $root.me.recrutRole !== 'recruter' && $root.selectTemplateName == 'General templates' && !$root.isEmptyTemplate)"
                            ckeditor
                            options="vm.ckEditorOptions"
                            ng-model="vm.templateModel.text"
                        ></textarea>
                        <span class="text-error-message" style="position: absolute; padding-top: 0" translate="Please fill the email text field in"></span>
                    </div>
                    <!--                    <div-->
                    <!--                        ng-if='($root.selectTemplateName === "General templates" && !$root.isEmptyTemplate) && ($root.me.recrutRole == "admin" || ($root.me.recrutRole == "recruter" && $root.editEmailTemplatesValue == "Y"))'-->
                    <!--                        class='editor-bottom-buttons'-->
                    <!--                        style='justify-content: flex-end;position:absolute;right: 0'>-->
                    <!--                        <span style='color: #337AB7' class='reset-button'-->
                    <!--                              ng-click='vm.resetToDefault();$event.preventDefault()'-->
                    <!--                              translate='Reset email template to default'></span>-->
                    <!--                    </div>-->
                </div>
            </div>
            <!--            <div class='editor-bottom-buttons' ng-style="(vm.notValidText)? {'top':'15px'} : null"-->
            <!--                 style='position:relative;'>-->
            <!--                <template-file-attach io-file='optionsForTemplate' files='$root.fileForSave'-->
            <!--                                      max-files-count='vm.filesMaxCount' remove-file='vm.removeFile(fileId)'-->
            <!--                                      style='position:absolute;top: 10px;'></template-file-attach>-->
            <!--            </div>-->
        </div>
        <div
            ng-if="
                ($root.me.recrutRole == 'admin' || ($root.me.recrutRole == 'recruter' && $root.editEmailTemplatesValue == 'Y')) &&
                $root.selectTemplateName == 'General templates'
            "
            class="template-for-edit"
        >
            <div class="main-row">
                <div class="editor-block central-block" ng-class="vm.notValidFields" style="border-radius: 5px; position: relative">
                    <div class="form-title">{{ 'The name of the template' | translate }}</div>
                    <span class="form-title-star">*</span>
                    <div style="position: relative">
                        <input
                            ng-if="$root.selectTemplateName == 'General templates' && !$root.isEmptyTemplate"
                            class="form-control name margin-top-null"
                            style="margin-bottom: 11px; font-size: 14px"
                            ng-disabled="$root.templateType || ($root.me.recrutRole === 'recruter' && $root.editEmailTemplatesValue === 'N' && $root.selectTemplateName == 'General templates') || ($root.me.recrutRole !== 'admin' && $root.me.recrutRole !== 'recruter' && $root.selectTemplateName == 'General templates')"
                            type="text"
                            title="{{ 'You cant change the name for this template' | translate }}"
                            placeholder="{{ 'The name of the template' | translate }}"
                            ng-model="vm.templateModel.name"
                            ng-change="vm.templateOnChange()"
                            ng-model-options="{debounce: 250}"
                        />
                        <input
                            ng-if="$root.selectTemplateName == 'My templates' || ($root.selectTemplateName == 'General templates' && $root.isEmptyTemplate)"
                            class="form-control name margin-top-null"
                            style="margin-bottom: 11px; font-size: 14px"
                            type="text"
                            placeholder="{{ 'The name of the template' | translate }}"
                            ng-model="vm.templateModel.name"
                            ng-change="vm.templateOnChange()"
                            ng-model-options="{debounce: 250}"
                        />
                        <span class="name-error-message" style="position: absolute; bottom: -33px" translate="Please fill the name field in"></span>
                    </div>
                    <div class="form-title" ng-style="vm.notValidName ? { 'padding-top': '10px' } : { 'padding-top': '0px' }" translate="Email subject"></div>
                    <span class="form-title-star">*</span>
                    <div style="position: relative" ng-style="vm.notValidTitle ? { 'padding-bottom': '10px' } : { 'padding-bottom': '0px' }">
                        <input
                            class="form-control subject margin-top-null"
                            style="font-size: 14px"
                            ng-disabled="($root.me.recrutRole === 'recruter' && $root.editEmailTemplatesValue === 'N' && $root.selectTemplateName == 'General templates' && !$root.isEmptyTemplate) || ($root.me.recrutRole !== 'admin' && $root.me.recrutRole !== 'recruter' && $root.selectTemplateName == 'General templates' && !$root.isEmptyTemplate)"
                            type="text"
                            placeholder="{{ 'Email subject' | translate }}"
                            ng-model="vm.templateModel.title"
                            ng-change="vm.templateOnChange()"
                            ng-model-options="{debounce: 250}"
                        />
                        <span class="title-error-message" style="position: absolute; bottom: -7px" translate="Please fill the subject field in"></span>
                    </div>
                    <div class="text-error-border" style="position: relative">
                        <textarea
                            id="{{ vm.editorId }}"
                            ng-if="vm.templateLoaded"
                            ng-readonly="($root.me.recrutRole === 'recruter' && $root.editEmailTemplatesValue === 'N' && $root.selectTemplateName == 'General templates' && !$root.isEmptyTemplate) || ($root.me.recrutRole !== 'admin' && $root.me.recrutRole !== 'recruter' && $root.selectTemplateName == 'General templates' && !$root.isEmptyTemplate)"
                            ckeditor
                            options="vm.ckEditorOptions"
                            ng-model="vm.templateModel.text"
                        ></textarea>
                        <span class="text-error-message" style="position: absolute; padding-top: 0" translate="Please fill the email text field in"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="template-for-preview">
            <h4
                ng-show="
                    ($root.me.recrutRole == 'admin' || ($root.me.recrutRole == 'recruter' && $root.editEmailTemplatesValue == 'Y')) &&
                    $root.selectTemplateName === 'General templates'
                "
            >
                <span translate="Preview"></span>
            </h4>
            <h4 ng-show="$root.selectTemplateName === 'My templates'"><span translate="Preview"></span></h4>
            <div class="main-row">
                <div class="preview-block central-block" style="border-radius: 5px">
                    <div class="preview-subject" ng-bind="vm.templateRendered.title"></div>
                    <div class="preview-wrapper">
                        <div ng-if="vm.templateLoaded" class="preview-text" ng-bind-html="vm.templateRendered.text | trust"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="buttons-wrapper" style="position: relative; align-items: flex-start" ng-style="vm.notValidText ? { top: '10px' } : null">
        <div class="editor-bottom-buttons" style="position: relative">
            <template-file-attach
                style="position: relative"
                io-file="optionsForTemplate"
                files="$root.fileForSave"
                max-files-count="vm.filesMaxCount"
                remove-file="vm.removeFile(fileId)"
            ></template-file-attach>
        </div>
        <div
            ng-if="
                vm.templateModel.type &&
                $root.selectTemplateName === 'General templates' &&
                !$root.isEmptyTemplate &&
                ($root.me.recrutRole == 'admin' || ($root.me.recrutRole == 'recruter' && $root.editEmailTemplatesValue == 'Y'))
            "
            class="editor-bottom-buttons"
        >
            <span
                ng-click="vm.resetToDefault();$event.preventDefault()"
                class="reset-button"
                style="color: #337ab7"
                translate="Reset email template to default"
            ></span>
        </div>
    </div>
    <div
        ng-show="
            ($root.me.recrutRole == 'admin' || ($root.me.recrutRole == 'recruter' && $root.editEmailTemplatesValue == 'Y')) &&
            vm.templateChanged === true &&
            $root.selectTemplateName === 'General templates'
        "
        class="buttons-row buttons"
    >
        <button ng-show="!$root.isEmptyTemplate" ng-click="vm.discardChanges()" class="btn_default btn_empty btn-for-margin" translate="cancel"></button>
        <button
            ng-show="$root.isEmptyTemplate"
            ng-click="$root.discardTemplateNewChanges()"
            class="btn_default btn_empty btn-for-margin"
            translate="cancel"
        ></button>
        <button
            ng-click="$root.selectTemplateName === 'My templates' ? vm.saveChangesCustomTemplates() : vm.saveChangesGeneralTemplates()"
            class="btn_default btn_success"
            translate="save"
        ></button>
    </div>
    <div ng-show="vm.templateChanged === true && $root.selectTemplateName === 'My templates'" class="buttons-row buttons">
        <button ng-show="!$root.isEmptyTemplate" ng-click="vm.discardChanges()" class="btn_default btn_empty btn-for-margin" translate="cancel"></button>
        <button
            ng-show="$root.isEmptyTemplate"
            ng-click="$root.discardTemplateNewChanges()"
            class="btn_default btn_empty btn-for-margin"
            translate="cancel"
        ></button>
        <button
            ng-click="$root.selectTemplateName === 'My templates' ? vm.saveChangesCustomTemplates() : vm.saveChangesGeneralTemplates()"
            class="btn_default btn_success"
            translate="save"
        ></button>
    </div>
</div>
