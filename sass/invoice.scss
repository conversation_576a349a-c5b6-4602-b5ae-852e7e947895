.invoice-generation {
    display: flex;
    flex-direction: column;
    padding: 30px 0 40px;

    background-color: $theme-navbar-background;

    align-items: center;
    justify-content: flex-start;
    .container {
        section {
            position: relative;

            display: flex;
            flex-direction: row;

            align-items: center;

            &.section__block {
                padding: 40px 10px 30px;

                border: 1px solid #ccc;
                border-radius: 5px;
                background-color: #fff;

                .title {
                    font-size: 28px;

                    margin: 0 0 19px 0;

                    color: #999;
                }
            }
            &.invoice__intro {
                flex-direction: column;
                margin-bottom: 18px;

                justify-content: center;

                .btn-transparent {
                    @include btn-transparent();

                    font-size: 14px;

                    position: relative;

                    display: inline-block;
                    margin-bottom: 10px;
                    padding: 5px 17px 5px 34px;

                    text-align: center;

                    background-color: transparent;

                    align-self: flex-start;
                    &:before {
                        position: absolute;
                        top: calc(50% - 6px);
                        left: 17px;

                        width: 12px;
                        height: 12px;

                        content: '';

                        background-image: url('/images/reports-img/long-arrow.png');
                    }
                    &:hover {
                        color: #fff;
                        border-color: #00b549;
                        background-color: #00b549;
                        &:before {
                            background-image: url('/images/sprite/long-arrow-white.png');
                        }
                    }
                }
                h2 {
                    font-size: 24px;

                    margin: 0;
                }
            }

            &.invoice__order-info {
                flex-direction: column;
                margin-bottom: 10px;

                justify-content: center;
                align-items: center;
                table {
                    width: 100%;
                }
                tr {
                    th {
                        span {
                            display: block;
                            padding: 10px 10px 10px 0;

                            color: #666;
                            border-top: 1px solid #ccc;
                            border-bottom: 1px solid #ccc;
                            background-color: #e2f1dd;
                        }
                        &:first-child {
                            span {
                                border-left: 1px solid #ccc;
                                border-radius: 5px 0 0 5px;
                            }
                        }
                        &:last-child {
                            span {
                                border-right: 1px solid #ccc;
                                border-radius: 0 5px 5px 0;
                            }
                        }
                    }
                    td {
                        padding-top: 10px;
                        .wrapper {
                            position: relative;

                            display: inline-block;
                            custom-select-new {
                                display: inline-block;
                            }
                        }
                        &.limit-width {
                            width: 15%;
                            max-width: 200px;
                        }
                        span.bold {
                            font-weight: 500;

                            width: 100px;
                            min-width: 80px;
                            margin-left: auto;
                        }
                    }
                    td,
                    th {
                        position: relative;

                        text-align: center;
                        &:first-child {
                            text-align: left;
                            span {
                                display: block;
                                padding-left: 10px;
                            }
                        }
                        &:last-child {
                            text-align: right;
                            span {
                                display: block;
                                padding-right: 10px;
                            }
                        }
                    }
                }
                select {
                    position: relative;

                    width: 90px;
                    height: 30px;
                    padding-left: 11px;

                    color: #666;
                    border: 1px solid #ccc;
                    border-radius: 5px;
                }
            }

            &.invoice__customer-information {
                flex-direction: column;
                margin-bottom: 30px;

                align-items: center;
                justify-content: center;

                .section__tooltip {
                    position: absolute;
                    top: 28px;
                    left: 22px;

                    color: #666;
                }
                form {
                    display: flex;
                    flex-direction: column;

                    align-items: center;
                    justify-content: center;
                    .form-group {
                        display: flex;
                        flex-direction: row;

                        align-items: center;

                        label {
                            font-weight: normal;

                            display: inline-block;
                            min-width: 175px;
                            margin: 0 10px 0 0;

                            text-align: right;

                            color: #000;
                        }
                        select,
                        input {
                            display: inline-block;
                            min-width: 470px;
                            height: 30px;
                            padding-left: 10px;

                            color: #666;
                            border: 1px solid #dbdbdb;
                            border-radius: 5px;
                            &.error {
                                border-color: #ff382d;
                                &:focus {
                                    outline: none;
                                }
                            }
                        }
                        custom-select-new {
                            min-width: 470px;
                            height: 30px;
                            .select-label.custom-new,
                            .select-ops.custom-new {
                                width: 100%;
                            }
                        }
                        select {
                            background: transparent;
                        }
                        input::placeholder {
                            color: #999;
                        }
                        &.error {
                            .error_message {
                                color: #f30909;
                            }
                        }
                    }
                }
            }
            &.invoice__generate {
                justify-content: center;
            }
        }
    }
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
}
