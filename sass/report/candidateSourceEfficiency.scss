.stat-efficiency.candidates-source-stat {
    candidate-source-stat-page-intro-component {
        section {
            margin: 0px !important;
        }
    }

    section {
        &.stat__settings .controllers .wrapper .controller {
            .title {
                white-space: nowrap;
                font-weight: 500;
                font-size: 14px;
                display: flex;
                color: #202021 !important;
                .info-icon {
                    position: relative;
                    top: -7px;
                }
            }
            .vacancies {
                .title {
                    margin-right: 10px;
                }
                .enabled {
                    color: rgb(102, 102, 102);
                }
            }
            .switcher {
                padding: 4px 10px;
                border-radius: 5px;
                label {
                    font-weight: normal;
                }
                .label-1 {
                    margin-right: 10px;
                }
                .label-2 {
                    margin-left: 10px;
                }
                &.on {
                    .label-1 {
                    }
                    .label-2 {
                    }
                }
            }
            .date-range .date-pickers {
                .picker {
                    margin-left: 0px;
                }
            }
        }
        &.stat__actions .wrapper .action.report-info span {
            font-size: 14px;
            .bold {
            }
            &.total-found-info {
                display: flex;
                i {
                    margin-left: 10px;
                }
            }
        }
        &.stat__content {
            justify-content: center;
            flex-direction: column;
            .stat__graph {
                width: 100%;
                margin-left: 0;
                margin-top: 20px;
                .graph {
                    width: 100%;
                }
            }
            .stat__table {
                width: 100%;
                table.source-table {
                    color: #000;
                    border-radius: 5px;
                    .vacancies-column {
                        a {
                            color: #000;
                        }
                    }
                    tbody tr {
                        &:last-child {
                            border-radius: 0 0 5px 5px;
                            td {
                                border-bottom: none;
                            }
                            td:first-child {
                                border-bottom-left-radius: 5px;
                            }
                            td:last-child {
                                border-bottom-right-radius: 5px;
                            }
                        }
                        td {
                            min-width: 195px;
                            border-bottom: 1px solid #000;
                            &.ff-medium,
                            &:first-child {
                            }
                            &.vacancies-wrap-cell {
                                padding: 0;
                            }
                            table.vacancies-table {
                                tbody tr {
                                    td {
                                        border-bottom: 1px solid #cccccc;

                                        a {
                                            text-decoration: underline;
                                        }
                                        .ff-medium {
                                        }
                                        &:first-child {
                                        }
                                    }
                                }
                            }
                        }
                        td.align-left {
                            text-align: left;
                            padding-left: 15px;
                        }
                    }
                    thead tr th {
                        width: auto;

                        &:first-child {
                            border-top-left-radius: 5px;
                        }
                        &:last-child {
                            border-top-right-radius: 5px;
                        }
                    }
                }
                &.zoom-out-out {
                    border: 1px solid black;
                    border-radius: 5px;
                }
                &.zoom-out {
                    table.source-table {
                        & > thead tr th {
                            border-top: 1px solid #000;
                            &:first-child {
                                border-left: 1px solid #000;
                            }
                            &:last-child {
                                border-right: 1px solid #000;
                            }
                        }
                        & > tbody tr {
                            td {
                                &:first-child {
                                    border-left: 1px solid #000;
                                }
                                &:last-child {
                                    border-right: 1px solid #000;
                                }
                            }
                            &:last-child {
                                td {
                                    border-bottom: 1px solid #000;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 1316px) {
    .candidates-source-stat {
        flex-direction: column;
    }
    .candidates-source-stat section.stat__settings .controllers .wrapper {
        .controller {
            flex-direction: column;
            align-items: flex-start;
            height: auto;
            .users {
                margin-top: 20px;
                margin-left: 0;
            }
        }
    }
    .candidates-source-stat section.stat__graph.inline {
        margin-top: 10px;
        margin-left: 0px;
        width: calc(51% - 10px);
    }
}
