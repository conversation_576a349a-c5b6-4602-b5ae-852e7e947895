.mce-tinymce.mce-container.mce-panel {
    border: 1px solid #e3e3e3;
    .mce-edit-area.mce-container.mce-panel.mce-stack-layout-item.mce-last {
        margin-left: -1px;
        padding-right: 1px;
    }
}

.mce-tinymce.mce-container.mce-panel .mce-edit-area.mce-container.mce-panel.mce-stack-layout-item.mce-last {
    border-width: 1px !important;
    border-left: 1px solid lightgrey;
}

#custom_mceu_addName_1 {
    padding-left: 20px;

    background: #e2f1dd url(/images/sprite/icons/tinyMCeAddNameButton.svg) no-repeat;
    background-position: 4px 3px;
    background-size: 17px 16px;
}
#custom_mceu_tmplLang_2 {
    #custom_mceu_1-open {
        padding-right: 2px;
        padding-left: 8px;
    }
    .mce-txt {
        span {
            vertical-align: baseline;
        }
    }
}
#custom_mceu_1-dropdown-body {
    top: 26px;
    left: 391px;

    display: block;

    &.mce-menu {
        width: 43px !important;
        min-width: 43px;
    }
    .mce-menu-item {
        padding-left: 6px;
    }
    @media (max-width: 500px) {
        top: 52px;
        left: 2px;
    }
}
