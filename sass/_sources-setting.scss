.sources-setting-container {
    padding: 24px 38px 0 24px;
    .padding-right-zero {
        padding-right: 0;
    }
    .padding-left {
        padding-left: 24px;
        margin-bottom: 40px;
    }
    @media (max-width: $screen-lg) {
        .padding-left {
            padding-left: 14px;
        }
    }
    .title {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    h2 {
        text-align: left;
        font-size: 18px;
        color: #000;
        margin: 0 0 22px 0;
        display: inline-block;
        position: relative;
        .info-icon {
            position: absolute;
            display: inline-block;
            bottom: 11px;
            right: -15px;
        }
    }
    .source-name {
        padding: 14px 0 12px 15.58px;
        border-radius: 5px;
        color: #666;
    }
    .sources-list {
        padding: 24px;
        border-radius: 10px;
    }
    .sources-list-default {
        background-color: #e5e5e5;
        margin-right: 24px;
        .source-name {
            background-color: #ffffff;
        }
        @media (max-width: $screen-lg) {
            margin-right: 14px;
            margin-bottom: 15px;
        }
    }
    .sources-list-custom {
        background-color: #ffffff;
        .source-name-container {
            &:hover {
                color: #333;
                .source-name {
                    background-color: #dbecfb;
                }
                .edit-icon,
                .remove-icon {
                    display: inline-block;
                    cursor: pointer;
                }
            }
        }
        .source-name {
            background-color: #ebf4fe;
        }
    }
    .source-name-text {
        width: fit-content;
        margin: 0;
    }
    .source-names-container {
        display: flex;
        flex-wrap: wrap;
        &.empty-container {
            margin-top: -4px;
        }
        .source-name-container {
            display: flex;
            flex: 0 1 50%;
            margin-bottom: 8px;
            position: relative;
            &:nth-child(odd) {
                .edit-icon {
                    right: 47px;
                }
                .remove-icon {
                    right: 24px;
                }
                .source-name {
                    margin-right: 8px;
                }
            }
            .source-name {
                width: 100%;
            }
            .edit-icon,
            .remove-icon {
                position: absolute;
                display: none;
                width: 12px;
                height: 12px;
                right: 37px;
                top: 38%;
            }
            .remove-icon {
                position: absolute;
                display: none;
                width: 12px;
                height: 12px;
                right: 15px;
                top: 38%;
                background: url(/images/sprite/close-icon.svg);
            }
        }
    }
    .source-button-container {
        margin-top: 24px;
        position: relative;
        display: flex;
        justify-content: center;
        .source-add-button {
            .button-capture {
                padding-left: 15px;
            }
            .plus-icon {
                display: inline-block;
                position: absolute;
                left: 18px;
                top: 33%;
                width: 12px;
                height: 12px;
                @include plus-icon();
            }
        }
    }
}

@media (max-width: 575px) {
    .sources-setting-container {
        padding: 20px 15px !important;
        .source-names-container {
            flex-direction: column;
            .source-name {
                margin-right: 0 !important;
            }
        }
        .source-name {
            padding: 14px 12px;
        }
    }
    .sources-tooltip {
        width: 122%;
    }
    .sources-custom-tooltip {
        width: 100%;
    }
}
