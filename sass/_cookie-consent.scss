.cookie-wrapper {
    font-size: 16px;

    position: fixed;
    z-index: 99999;
    bottom: 0;

    width: 100%;
    margin: 0 auto;
    padding: 20px 2%;

    text-align: center;

    color: #ccc;
    border: 1px solid rgb(51, 196, 109);
    background-color: rgba(0, 0, 0, 0.8);
    .inner-text {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex-wrap: wrap;
        justify-content: center;
    }
    button {
        font-weight: 500;
        line-height: 21px;
        display: block;
        margin: 20px auto 30px;
        padding: 3px 25px;

        transition: background-color 0.3s ease-out, color 0.3s ease-out;

        color: white;
        border: 1px solid white;
        border-radius: 5px;
        outline: none;
        background-color: transparent;
        &:hover {
            color: #333;
            background-color: white;
        }
    }
    .close-icon {
        position: absolute;
        top: 5px;
        right: 4px;
        cursor: pointer;
        filter: invert(85%) sepia(9%) saturate(11%) hue-rotate(6deg) brightness(96%) contrast(91%);
    }
    a {
        font-weight: 500;

        margin-left: 5px;

        cursor: pointer;

        color: white;
    }
    .second-sentence,
    .first-sentence {
        white-space: nowrap;
    }
    .second-sentence,
    .first-sentence {
        white-space: nowrap;
        span {
            display: inline-block;

            text-align: left;
        }
    }
}
@media (max-width: 1300px) {
    .cookie-wrapper .first-sentence,
    .cookie-wrapper .second-sentence {
        display: block;

        white-space: normal;
    }
}
@media (max-width: 1200px) {
    .cookie-wrapper .inner-text {
        justify-content: flex-start;
    }
}
