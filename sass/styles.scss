//
//
// CleverStaff Theme
// --------------------------------------------------

// Reset styles
:focus {
    outline: none;
}

// Core Framework
// -------------------------
//@import 'font-awesome/_variables';
//@import 'font-awesome/font-awesome';
//@import 'mCustomScrollbar/jquery.mCustomScrollbar.min.scss';
//@import 'bootstrap-date-picker/datepicker';
//@import 'pnotify/pnotify.custom.min';
//@import 'pnotify/jquery-ui-1.9.2.custom';
//@import 'select2/select2';
//@import 'cropperjs/cropper';
//@import 'angular-circular-timepicker/angular-circular-timepicker';
//@import 'angular-material/angular-material';
//@import 'mdPickers/mdPickers';

// Theme styles
// -------------------------
@import 'variables';
@import 'svg-bg-images';
@import 'mixins';
@import 'common-styles';
@import 'header';
@import 'vacancies';
@import 'vacancy';
@import 'vacancy-information';
@import 'vacancy-report';
@import 'reports';
@import 'clients';
@import 'client';
@import 'candidate';
@import 'company';
@import 'company-history';
@import 'email-templates';
@import 'company-statistics';
@import 'employees';
@import 'notifications';
@import 'employee';
@import 'import-zip-archive';
@import 'vacancy-task';
@import 'users';
@import 'user';
@import 'employee-add-from-candidate';
@import 'modal/vacancy-add-candidate';
@import 'modal/tag-page-modals';
@import 'modal/vacancy-candidate-change-status';
@import 'modal/email-notifications';
@import 'modal/candidate-excel-history';
@import 'modal/candidate-remove';
@import 'modal/delete-candidates';
@import 'modal/tag-remove';
@import 'vacancy-settings';
@import 'company-settings';
@import 'integration-with-email';
@import 'modal/add-coment';
@import 'modal/delete-candidate-from-vacancy';
@import 'modal/change-vacancy-status';
@import 'modal/adding-responsible';
@import 'modal/delete-responsible';
@import 'modal/notHaveIntegrationWith-FB';
@import 'modal/haveIntegrationWith-FB';
@import 'modal/send-vacancy-by-email';
@import 'modal/send-reject-by-email';
@import 'modal/candidate-show-file';
@import 'modal/cloud-admin-new-payment';
@import 'modal/create-letter-personal-mailing';
@import 'modal/create-sms';
@import 'modal/news-fb';
@import 'modal/repeat-gmail-integration-dissabling-modal';
@import 'modal/news-feed-modal';
@import 'vacancy-new-add';
@import 'googleCalendar';
@import 'dashboard';
@import 'pay';
@import 'signup-new';
@import 'vacancy-reports';
@import 'vacancy-pipeline';
@import 'recall';
@import 'my-tasks-kanban/my-tasks-kanban';
@import 'my-tasks-kanban/kanban-avatar';
@import 'departmentCatalog';
@import 'contact-add';
@import 'client-add';
@import 'excel-history';
@import 'contacts';
@import 'employee-add';
@import 'pipeline';
@import 'modal/add-interview-from-recall';
@import 'modal/add-photo';
@import 'feedback-page';
@import 'feedback-page-thanks';
@import 'feedback-page-new-design';
@import 'feedback-page-new-design-thanks';
@import 'notice-menu';
@import 'modal/facebook-modal';
@import 'modal/facebook-modal-en';
@import 'modal/google-modal';
@import 'modal/google-modal-en';
@import 'modal/quest-modal';
@import 'modal/modal-error';
@import 'finishreg';
@import 'notification';
@import 'modal/candidate-change-status-in-vacancy';
@import 'modal/delete-comment-candidate';
@import 'modal/blockAccountHmNotPaid';
@import 'modal/no-access-modal';
@import 'invoice';
@import 'invoice-confirm-corporate';
@import 'photoCropper/photoCropper.component';
@import 'faq';
@import 'modal/employee-change-status-in-employee';
@import 'modal/email-chat';
@import 'modal/users-invite';
@import 'modal/invite-hiring-manager';
@import 'modal/_invite-new-user';
@import 'modal/news';
@import 'hr-module-info';
@import 'modal/candidate-add-tags-multiple';
@import 'modal/send-email';
@import 'news';
@import 'modal/pipeline-descr';
@import 'modal/change-email-test-candidate';
@import 'modal/block-invite-user';
@import 'modal/calendar-access';
@import 'modal/request-report-modal';
@import 'modal/invoice-confirm';
@import 'modal/delete-account';
@import 'pay-corporate';
@import 'modal/candidate-remove-from-system';
@import 'modal/custom-fields';
@import '_cloud-admin';
@import 'customFields';
@import '_fonts';
@import 'new-buttons';
@import 'tinyMCE-custom';
@import 'account-deletion';
@import 'custom-select-new';
@import 'mailing';
@import 'modal/mailing-modals';
@import 'modal/remove-employee';
@import 'modal/open-promo-logo';
@import 'modal/limited-access-candidate';
@import 'modal/on-success-payment';
@import 'custom-reports';
@import 'my-reports';
@import 'constructor-reports-edit';
@import 'constructor-reports';
@import 'pagination';
@import 'settings';
@import 'cookie-consent';
@import 'modal/_close-vacancies-modal';
@import 'emailIntegrationEdit';
@import 'search';
@import 'main-loader';
@import 'new-year-loader';
@import 'modal/facebook-modal-before-share';
@import 'modal/copy-vacancy-to-vacancy';
@import 'cloud-admin-account-information';
@import 'modal/referral-bank-modal';
@import '_template-settings';
@import '_vacancy-templates';
@import '_mailto';
@import 'modal/email-integration-modals';
@import 'modal/vacancySendAutoTest';
@import 'modal/removeTestEnabledInVacancy';
@import 'vacancy-test';
@import 'modal/vacancy-vcv';
@import 'modal/vcv-candidate-link';
@import 'search-clients';
@import 'search-vacancies';
@import 'custom-select-with-checkboxes';
@import 'tabs.scss';
@import 'modal/free-paid-users';
@import 'tabs';
@import 'modal/referral-discount';
@import 'modal/referral-bonus';
@import '_referral-program';
@import 'modal/send-referral';
@import 'modal/_referral-reg-info';
@import 'modal/_referral-withdraw';
@import 'userPerformanceReport';
@import 'region-select';
@import 'resetPassword';
@import 'payment';
@import 'tags.component';
@import 'codeInput.component';
@import 'selectForSkillsComponent';
@import 'selectWithTwoInputsComponent';
@import 'dropdowns_and_blocks/dropdownsAndBlocksCommon';
@import 'dropdowns_and_blocks/desiredSalaryBlock';
@import 'candidateEducationAnaJobExperienceBlocks';
@import 'forms/forms';
@import 'semicircleProgressBarComponent';
@import 'lib/ngImgCrop/ng-img-crop';
@import 'scrollbar';
@import 'modal/congratulations';
@import 'modal/two-factor-authorization/first-stage-two-factor-authorization';
@import 'modal/two-factor-authorization/confirm-stage-two-factor-authorization';
@import 'modal/two-factor-authorization/final-stage-two-factor-authorization';
@import 'integrationLostNotification.component';
@import 'modal/vacancy-application/editVacancyApplicationModal';
@import 'candidate-preview';
@import 'gdpr';
@import 'modal/_notification-pop-up';
@import 'modal/_presentation-pop-up';
@import 'custom-popover';

// components
@import 'responsiblePerson.component';

// gamification
@import 'gamification/gamification.component';
@import 'gamification/like.component';
@import 'gamification/extended-like.component';
@import 'modal/selectedEmojiModal';

// Job search sites Integration Component
@import 'jobSitesIntegration/jobSitesIntegration';
@import 'modal/jobSitesIntegration/removePublish';
@import 'modal/jobSitesIntegration/connectVacancy';
@import 'modal/jobSitesIntegration/youHaveNotIntegration.modal';
@import 'modal/jobSitesIntegration/approveChanges';
// HH
@import 'jobSitesIntegration/hh/hhVacancyPosting';
@import 'modal/jobSitesIntegration/hh/publishVacancyModal';
// WorkUA
@import 'modal/jobSitesIntegration/workUA/auth-work-modal';
@import 'modal/jobSitesIntegration/workUA/publishVacancyWorkUaModal';

@import 'integration-page/integrationPage.component';
@import 'integration-page/hhIntegration.component';
@import 'integration-page/bambooHrIntegration.component';
@import 'integration-page/alphasmsIntegration.component';
// WorkUA
@import 'modal/jobSitesIntegration/workUA/auth-work-modal';
@import 'modal/jobSitesIntegration/workUA/publishVacancyWorkUaModal';

//RabotaUA
@import 'modal/jobSitesIntegration/rabotaUA/auth-rabota-modal';
@import 'modal/jobSitesIntegration/rabotaUA/publishVacancyRabotaUaModal';
@import 'modal/jobSitesIntegration/rabotaUA/approveChangesForRabota';

// Score Cards
@import 'scoreCards/scoreCardsMain.component';
@import 'modal/scoreCards/createScoreCard.modal';
@import 'modal/scoreCards/scoreCardPreview.modal';
@import 'modal/scoreCards/scoreCardDelete.modal';
@import 'modal/scoreCards/chooseTypeOfEditionScoreCard.modal';
@import 'modal/scoreCards/acceptToChangeScoreCard.modal';

// Candidate
@import 'candidate/candidateEvaluate';
@import 'modal/candidate/candidateEvaluate.modal';

@import 'custom-select-with-checkboxes';
@import 'report/candidatesSource';
@import 'report/candidatesSourceStat';
@import 'report/candidateSourceEfficiency';
@import '_sources-setting.scss';
@import 'modal/modify-source';
@import 'vacancy-suggestions';
@import 'gamification/news-feed.component';
@import 'modal/vacancy-suggestion-check-fields';
@import 'education-jobExpirience-component';
@import 'achievements/common';
@import 'achievements/collections';
@import 'achievements/results';
@import 'achievements/awards';
@import 'actions';
@import '_switcher-wrapper';
//@import "sprite";

@import 'common/btn__back';
@import 'common/_buttons';
@import 'common/pageIntro';
@import 'common/dateRange';
@import 'common/radio';
@import 'common/checkboxes';
@import 'common/copyToClipboard';
//@import "sprite";

// ** Forms ** //
@import 'forms/forms';

@import 'directives';

//Redesign files
//Moved to webpack build (entry.js)
//@import 'redesign/index';
