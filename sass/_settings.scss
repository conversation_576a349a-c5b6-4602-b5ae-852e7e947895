@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.container-setting {
    #intro-section {
        margin: 16px 0;

        & h1 {
            margin: 0;
            font-size: 18px;
            color: $main-black;
        }
    }

    .block-settings {
        float: none;
        width: 620px;
        padding: 40px;
        margin: 0 auto;
        background-color: $main-white;
        border: 1px solid $border-grey;
        border-radius: 12px;

        &__title {
            margin-bottom: 20px;
            color: $semi-black;
        }

        &__wrapper {
            padding: 0 0 0 40px;

            @media (max-width: $screen-xs) {
                padding: 0 0 0 16px;
            }
        }

        .error-select {
            .custom-select {
                border: 1px solid red;
                border-radius: 5px;
            }
        }

        .fa {
            display: none;
            margin: 0;
            font-size: 27px;
            line-height: 18px;
            color: $main-green;
            vertical-align: bottom;
            background-color: $main-white;
        }

        button {
            position: relative;
            width: 23px;
            height: 23px;
            padding: 0;
            background-color: rgba(32, 31, 31, 0.2);
            border: none;
            outline: none;
        }

        .scopeAccount {
            @include flex(row, space-between, center);
            padding: 0;
            margin-bottom: 16px;

            .input-radio-wrapper {
                position: relative;
                display: flex;
                gap: 8px;

                .checkmark {
                    top: 2px;
                    width: 16px;
                    height: 16px;
                    padding: 0;
                }
            }

            label {
                display: flex;
                padding: 0;
                margin: 0;
                font-weight: 400;
                color: $main-black;

                &.single-company {
                    width: unset;
                }

                @media (max-width: $screen-xs) {
                    &.size-label {
                        min-width: auto;
                        max-width: 84px !important;
                    }

                    &.scopeCheckmarkOrg,
                    &.scopeCheckmarkMe,
                    &.scopeCheckmarkRegion {
                        &:after {
                            top: 16px !important;
                        }

                        &:before {
                            top: 12px !important;
                        }
                    }
                }

                .single-account {
                    display: inline-block;
                    padding-left: 58px;
                    font-weight: normal;

                    &.ru-width {
                        padding-left: 49px;
                    }
                }
            }

            &:last-child {
                margin: 0;
            }
        }

        .select-wrap-region {
            padding: 0;

            @media (max-width: 768px) {
                width: 50% !important;
            }
        }

        .checkmark {
            display: block !important;
        }

        @media (max-width: 768px) {
            width: 90%;
            padding: 15px;

            & .scopeAccount {
                padding: 0 !important;
            }
        }
    }

    .header {
        position: relative;
        margin: 0;
        text-align: center;

        @media (max-width: 768px) {
            h2 {
                display: block;
                padding-left: 40px;
                font-size: 25px;
            }
        }

        h2 {
            display: inline-block;
            margin: 0;
            font-size: 24px;
            font-weight: normal;
            color: $main-black;
        }
    }
}

@media screen and (max-width: $screen-sm) {
    .container-setting .header {
        width: 90%;
        margin: 25px auto;
    }
}

@media (max-width: 440px) {
    .scope-visibility {
        width: 150px;
    }
}

@media (max-width: 765px) {
    .container-setting {
        .col-lg-12 {
            padding: 0 !important;
        }

        .header {
            width: 100%;
            margin: 0;
        }
    }
}

@media (max-width: 380px) {
    .container-setting .header h2 {
        width: 300px;
    }
}

@media (max-width: 320px) {
    .scope-visibility {
        width: 100px;
    }
}
