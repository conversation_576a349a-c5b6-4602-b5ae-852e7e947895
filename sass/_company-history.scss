.block-company-history {
    .row {
        padding: 0 15px;
        & > .img-section {
            padding: 15px;

            background-color: $theme-white;
            img {
                width: 100%;
            }
            & > div {
                & > a {
                    @include bold();

                    display: block;
                    margin-top: 15px;

                    color: $theme-dark;
                    border: 1px solid $theme-light-green-hover;
                    background-color: $theme-white;
                    .size {
                        font-size: 11px;
                        font-weight: 300;

                        color: $theme-grey;
                    }
                }
            }
            .company-label {
                font-size: 11px;
                font-weight: 300;

                margin: 15px 0 0 12px;

                color: $theme-grey;
            }
            & > input {
                @include input-styling();

                margin-top: -10px;
            }
            #cropper-wrap {
                padding: 10px;

                border-radius: 5px;
                .cropper-container {
                    margin-left: -16px;
                }
                #close {
                    @include button-styling-cancel();

                    font-weight: normal;

                    padding: 3px 9px;
                }
                #cropp {
                    @include button-green-styling();

                    font-weight: normal;

                    padding: 3px 9px;
                }
                #img-wrapper {
                    margin-bottom: 20px;
                }
            }
            #wrapperForPng {
                padding: 10px;

                border-radius: 5px;
                img {
                    width: 180px;
                    height: 180px;
                    margin-bottom: 15px;
                }
                #cancel {
                    @include button-styling-cancel();

                    font-weight: normal;

                    padding: 3px 4px;
                }
                #download {
                    @include button-green-styling();

                    font-weight: normal;

                    margin-left: 12px;
                    padding: 3px 4px;
                }
            }
        }

        .status {
            @include history-label-styling();
        }

        .stage {
            @include stages-labels-styling();
        }

        & > .blockHistory {
            padding-right: 0;
            padding-left: 0;
            .history-outer {
                margin-top: 15px;
                padding: 0;
                .btn {
                    @include button-styling();
                }
                .accept {
                    color: $theme-white;
                    background-color: $theme-dark-green;
                    &:hover {
                        background-color: $theme-light-green-hover;
                    }
                }
                .cancel {
                    color: #585859;
                    background-color: #e3e3e3;
                    &:hover {
                        background-color: $theme-light-grey-hover;
                    }
                }
                .editMessage {
                    @include link-styling();
                }
                .comment-container span a {
                    display: inline !important;
                }
                .text-in-block a {
                    display: inline !important;
                }
                .event {
                    margin-left: 0;
                    &:hover {
                        background: rgba(0, 181, 73, 0.02) !important;
                        border-left: 2px solid #00b549 !important;
                        transition: all 0.3s ease-in !important;
                    }

                    a {
                        @include link-styling();
                    }
                    > div {
                        padding: 8px 0;

                        border-bottom: 1px solid $theme-border-color-base;
                    }
                    .col-lg-10 {
                        padding-left: 0;
                        color: #202021;
                        .removeMessage {
                            margin-left: 10px;
                        }
                        .blueLink {
                            color: $theme-link-color;
                            cursor: pointer;
                        }
                        .red {
                            color: $red;
                        }
                        .textField {
                            .field {
                                margin-top: 10px;
                                textarea {
                                    width: 405px;
                                    height: 155px;

                                    resize: none;
                                }
                            }
                            .btn {
                                @include button-styling();
                            }
                            .accept {
                                color: $theme-white;
                                background-color: $theme-dark-green;
                                &:hover {
                                    background-color: $theme-light-green-hover;
                                }
                            }
                            .cancel {
                                color: #585859;
                                background-color: #e3e3e3;
                                &:hover {
                                    background-color: $theme-light-grey-hover;
                                }
                            }
                            &:last-child {
                                margin-left: 10px;
                            }
                        }
                        a.underline {
                            text-decoration: underline;

                            color: #000;
                            &:hover {
                                color: #000;
                            }
                        }
                    }
                    .col-lg-2 {
                        padding-left: 8px;
                    }
                    .margin-top {
                        .manyCandidates {
                            & > .boldTxt {
                                @include bold();
                                .blueLink {
                                    color: $main-green;
                                    cursor: pointer;
                                }
                            }
                            .blueLink {
                                color: $main-green;
                                cursor: pointer;
                            }
                        }
                        .menuCandidate {
                            display: block;
                        }
                    }
                    .margin-top-1 {
                        margin-top: -17px;
                    }
                    .margin-top-status {
                        .status {
                            margin-top: -1px;
                        }
                    }
                    .candidate_message {
                        .boldTxt {
                            @include bold();
                            .blueLink {
                                color: $theme-link-color;
                                cursor: pointer;
                            }
                        }
                        .blueLink {
                            color: $theme-link-color;
                            cursor: pointer;
                        }
                        .menuCandidate {
                            .comma {
                                color: #000;
                            }
                        }
                    }
                    .time {
                        @include bold();
                    }
                    .coma {
                        position: relative;
                        right: 2px;
                    }
                    .task {
                        @include bold();

                        color: #ffc100;
                    }
                    .removed {
                        color: #999;
                        background-color: #e2e2e2;
                    }
                    .interview_add {
                        cursor: auto;
                        .boldTxt {
                            cursor: pointer;
                            @include bold();
                            .blueLink {
                                color: $theme-link-color;
                                cursor: pointer;
                            }
                        }
                        .blueLink {
                            color: $theme-link-color;
                            cursor: pointer;
                        }
                    }
                    .status {
                        @include history-label-styling();
                    }
                    &:hover {
                        cursor: pointer;

                        background-color: $theme-light-green;
                    }
                    &:not(.last-action) {
                        border: 0;
                    }
                    a {
                        &:not(.btn) {
                            @include link-styling();

                            display: inline-block;
                        }
                    }
                }
                .border {
                    width: 100%;
                    cursor: default;
                    border: none;
                    word-break: break-word;
                    border-left: 2px solid transparent;
                    .border {
                        border: none;
                    }
                }
                .last-actions-header {
                    margin-left: 0;
                    padding: 8px;
                    .title-header {
                        font-size: 18px;
                        font-weight: 600;

                        padding: 8px 15px;
                    }
                    .border {
                        border: none;
                    }
                    a:not(.show-comments) {
                        @include button-styling();

                        margin-left: 15px;
                        padding: 3px 12px;

                        border: none;
                        background-color: #b4eba4;
                    }
                    .show-comments {
                        color: #202021;
                    }
                }
                .more-history {
                    @include button-styling();

                    display: block;
                    width: 100%;
                }
                span.danger {
                    color: $red;
                }
            }
        }
    }
    .pagination-block {
        .pagination-elements {
            .reset-button {
                width: 27%;
                margin-left: 45px;
            }
        }
    }
}

.green-link {
    color: $main-green !important;

    &:hover {
        color: $yellow !important;
    }
}

@media (max-width: 1200px) {
    .block-company-history .row > .img-section img {
        max-width: 300px;
    }
    .block-company-history .row > .img-section #cropper-wrap {
        max-width: 300px;
        margin: auto;
    }
    .block-company-history .row > .img-section #cropper-wrap .cropper-container {
        margin-left: 0;
    }
    .block-company-history .row > .img-section #wrapperForPng {
        max-width: 200px;
        margin: auto;
    }
}
.block-company-history .row > .blockHistory .history-outer {
    .pagination-block {
        display: none;
    }

    .company-history-pagination {
        border-radius: 5px;
    }

    table thead,
    table tbody tr {
        display: table;
        width: 100%;
        table-layout: fixed;
    }
    table {
        tr {
            &.table-space {
                height: 8px;
                box-shadow: 0px -1px 4px rgba(0, 0, 0, 0.12);
            }
        }
    }
}

@media (max-width: 575px) {
    .block-company-history .row > .blockHistory .history-outer .event .col-lg-2 {
        padding-left: 0 !important;
    }
    .block-company-history .row > .blockHistory .history-outer .header-of-table {
        margin-left: 10px;
    }
    .block-company-history .row > .blockHistory .history-outer .event .col-lg-10 {
        word-break: break-word !important;
    }
}
