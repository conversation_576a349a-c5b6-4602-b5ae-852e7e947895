@import 'js/common/styles/global-variables.module';
@import 'variables';

.collections {
    margin: var(--main-gap);

    &-wrapper {
        display: flex;
        width: 100%;
    }

    &-right-block {
        min-height: 470px;
        overflow: hidden;

        border-radius: 60px;
        box-shadow: $gamification-blue-box-shadow;

        &-wrapper {
            width: 75%;
        }

        &-item {
            display: flex;
            align-items: center;
            min-height: 94px;
            padding: 0 30px 10px 30px;
            background-color: #dcfafa;

            &.red {
                background: linear-gradient(to right, #e8dee1 0%, #eafcfc 26%, #f7fefe 100%);
            }

            &.green {
                background: linear-gradient(to right, #bae8e7 0%, #dcfafa 26%, #f0fdfd 100%);
            }

            &.blue {
                padding: 0 30px 10px 30px;
                background: linear-gradient(to right, #c1deea 0%, #e2fbfb 26%, #f2fdfd 100%);
                border-radius: 0;
            }

            &.yellow {
                background: linear-gradient(to right, #f4fbe9 0%, #f6fefd 36%, #f6fefe 100%);
            }

            &.purple {
                background: linear-gradient(to right, #dfe3f4 0%, #eafcfc 26%, #f3fdfe 100%);
            }

            &-crystals {
                display: flex;
                align-items: baseline;
                justify-content: space-around;
                width: 100%;
                height: 130px;
                margin: 0 10px 0 30px;

                &-item {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    opacity: 0;

                    img {
                        pointer-events: none;
                    }

                    &-picture-1 {
                        width: 50px;
                        max-height: 55px;
                    }

                    &-picture-2 {
                        width: 60px;
                        max-height: 65px;
                        pointer-events: none;
                    }

                    &-picture-3 {
                        width: 70px;
                        max-height: 75px;
                    }

                    &-picture-4 {
                        width: 110px;
                        max-height: 115px;
                    }

                    &-picture-5 {
                        width: 130px;
                        max-height: 135px;
                    }

                    &-count {
                        font-weight: 500;
                        color: #fd5806;
                    }

                    &-blur {
                        height: 20px;
                        font-size: 20px;
                        font-weight: 500;
                        color: #fd5806;
                    }

                    &-picture-1-crystal {
                        height: 35px;
                    }

                    &-picture-2-crystal {
                        position: relative;
                        bottom: 8px;
                        height: 43px;
                    }

                    &-picture-3-crystal {
                        position: relative;
                        bottom: 22px;
                        height: 59px;

                        &[data='/images/sprite/achievements/crystals/green/green3.svg'] {
                            bottom: 14px !important;
                            height: 50px !important;
                        }

                        &[data='/images/sprite/achievements/crystals/purple/purple3.svg'] {
                            & span {
                                position: relative;
                                right: 10px;
                                bottom: 2px;
                            }
                        }

                        &[data='/images/sprite/achievements/crystals/purple/purple2.svg'] {
                            position: relative;
                            bottom: 12px;
                            height: 48px;
                        }
                    }

                    &-picture-4-crystal {
                        position: relative;
                        bottom: 25px;
                        height: 70px;

                        & span {
                            position: relative;
                            right: 7px;
                            bottom: 9px;
                        }

                        &[data='/images/sprite/achievements/crystals/purple/purple4.svg'] {
                            right: 22px;
                            bottom: 30px;

                            & span {
                                bottom: 5px;
                            }
                        }
                    }

                    &-picture-5-crystal {
                        position: relative;
                        bottom: 24px;
                        height: 70px;

                        & object {
                            position: relative;
                            bottom: 7px;
                        }

                        & span {
                            position: relative;
                            right: 8px;
                            bottom: 9px;
                        }
                    }
                }
            }

            &-name {
                width: 100px;
                margin-top: 2%;
                font-size: 18px;
                font-weight: 500;

                &.red {
                    color: #d1181f;
                }

                &.green {
                    color: #387d2b;
                }

                &.blue {
                    color: #22419c;
                    background: none;
                }

                &.yellow {
                    color: #eeb053;
                }

                &.purple {
                    color: #79309b;
                }
            }
        }
    }

    &-left-block {
        margin-right: 30px;
        overflow: hidden;
        border-radius: 60px;
        box-shadow: $gamification-blue-box-shadow;

        &-wrapper {
            width: 25%;
        }

        &-main {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 30px;
            background-color: #dcfafa;

            &-counter {
                display: flex;
                align-items: center;
                margin-top: 20px;
                margin-bottom: 20px;

                &-wrapper-popup {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    margin-top: 20px;
                }

                &-image {
                    width: 70px;
                    height: 70px;
                    pointer-events: none;
                }
            }
        }

        &-footer {
            display: flex;
            flex-direction: column;
            padding: 20px;
            background-color: #ffffff;

            &-exchange {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 20px;
            }

            &-title {
                color: #8bc5c5;
            }
        }
    }

    .blink {
        font-weight: 500;
        animation: blink 2s linear infinite;
    }

    @keyframes blink {
        0% {
            opacity: 0;
        }

        25% {
            opacity: 0.5;
        }

        100% {
            opacity: 1;
        }
    }
}

.purpleInDistribute {
    bottom: 13px;
    height: 49px;
}

.crystals-in-distribute-popup {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 30px;

    &-item {
        display: flex;
        flex-basis: 5em;
        flex-direction: column;
        align-items: center;
    }
}

.collections-left-block-main-counter-wrapper-popup {
    .modal-content {
        min-height: 400px;
    }
}

.collections-right-block-item-crystals-item-count.emeralds {
    margin-top: 5px;
    margin-left: 5px;
}

.greenFirstCrystal {
    position: relative;
    top: -1px;
    left: 5px;
    width: 45px;
}

.greenSecondCrystal {
    position: relative;
    top: 1px;
    left: 6px;
    width: 60px;
}

.greenThirdCrystal {
    position: relative;
    top: 2px;
    left: 9px;
    width: 65px;
}

.greenFourthCrystal {
    position: relative;
    top: 8px;
    left: 17px;
    width: 120px;
}

.greenFifthCrystal {
    position: relative;
    top: 9px;
    left: 20px;
    width: 130px;
}

.collections-right-block-item-crystals-item-count.sapphires {
    margin-top: 15px;
}

.blueFirstCrystal {
    position: relative;
    top: 9px;
    left: 5px;
    width: 50px;
}

.blueSecondCrystal {
    position: relative;
    top: 10px;
    left: 6px;
    width: 60px;
}

.blueThirdCrystal {
    position: relative;
    top: 10px;
    left: -5px;
    width: 70px;
}

.blueFourthCrystal {
    position: relative;
    top: 12px;
    left: 12px;
    width: 90px !important;
}

.blueFifthCrystal {
    position: relative;
    top: 11px;
    left: 17px;
    width: 135px !important;
}

.purpleFirstCrystal {
    position: relative;
    top: -11px;
    left: 4px;
    width: 50px;
}

.purpleSecondCrystal {
    position: relative;
    top: -8px;
    left: 6px;
    width: 60px;
}

.purpleThirdCrystal {
    position: relative;
    top: -2px;
    left: 16px;
    width: 75px;

    div {
        position: relative;
        right: 9px;
    }
}

.purpleFourthCrystal {
    position: relative;
    top: -3px;
    left: 15px;
    width: 100px;
}

.purpleFifthCrystal {
    position: relative;
    top: 0;
    left: 29px;
    width: 130px;
}

.redFirstCrystal {
    position: relative;
    top: -4px;
    left: 4px;
    width: 50px;
}

.redSecondCrystal {
    position: relative;
    top: -3px;
    left: 12px;
    width: 60px;
}

.redThirdCrystal {
    position: relative;
    top: -3px;
    left: 4px;
    width: 70px;
}

.redFourthCrystal {
    position: relative;
    top: 3px;
    left: 9px;
    width: 110px;
}

.redFifthCrystal {
    position: relative;
    top: 3px;
    left: 14px;
    width: 130px;
}

.yellowFirstCrystal {
    position: relative;
    top: -4px;
    left: 4px;
    width: 50px;
}

.yellowSecondCrystal {
    position: relative;
    top: -3px;
    left: 8px;
    width: 60px;
}

.yellowThirdCrystal {
    position: relative;
    top: 3px;
    left: 10px;
    width: 70px;
}

.yellowFourthCrystal {
    position: relative;
    top: 8px;
    left: 17px;
    width: 120px;
}

.yellowFifthCrystal {
    position: relative;
    top: 4px;
    left: 15px;
    width: 130px;
}

.yellowFourthMl {
    margin-left: 10px;
}

.yellowThirdMl {
    margin-left: 20px;
}

.yellowSecondMl {
    margin-left: 8px;
}

.blueThirdMr {
    margin-right: 14px;
}

.crystals-height {
    height: 55px;
}

.increase-crystal {
    position: relative;
    top: -50px;
    left: -100px;
}

.increase-crystal.greens {
    position: relative;
    top: -59px;
}

.increase-crystal.greens.greenThirdArrow {
    left: -90px;
}

.increase-crystal.greens.greenFourthArrow {
    margin-right: 30px;
}

.increase-crystal.greens.greenFifthArrow {
    margin-right: 40px;
}

.increase-crystal.blues {
    position: relative;
    top: -60px !important;
}

.increase-crystal.blues.blueThirdArrow {
    left: -101px;
}

.increase-crystal.blues.blueFourthArrow {
    margin-right: 30px;
}

.increase-crystal.blues.blueFifthArrow {
    margin-right: 40px;
}

.increase-crystal.purples {
    position: relative;
    top: -63px;
}

.increase-crystal.purples.purpleFourthArrow {
    margin-right: 30px;
}

.increase-crystal.purples.purpleFifthArrow {
    margin-right: 40px;
}

.increase-crystal.reds {
    position: relative;
    top: -58px;
}

.increase-crystal.reds.redFourthArrow {
    margin-right: 30px;
}

.increase-crystal.reds.redFifthArrow {
    margin-right: 40px;
}

.increase-crystal.yellows {
    position: relative;
    top: -51px;
}

.increase-crystal.yellows.yellowSecondArrow {
    left: -90px;
}

.increase-crystal.yellows.yellowThirdArrow {
    left: -84px;
}

.increase-crystal.yellows.yellowFourthArrow {
    margin-right: 20px;
}

.increase-crystal.yellows.yellowFifthArrow {
    margin-right: 35px;
}

.blueFirstCount {
    margin-right: 2px;
}

.collections-right-block-item-crystals.purple {
    height: 155px;
}

.collections-right-block-item-crystals-item.blues-wrapper:nth-child(2) {
    position: relative;
    right: 7px;
}

.collections-right-block-item-crystals-item.blues-wrapper:nth-child(3) {
    position: relative;
    right: 5px;
}

.collections-right-block-item-crystals-item.blues-wrapper:nth-child(4) {
    position: relative;
    right: 2px;
}

.collections-right-block-item-crystals-item.blues-wrapper:nth-child(5) {
    position: relative;
    left: 7px;
}

.collections-right-block-item-crystals-item.purple-wrapper:nth-child(2) {
    position: relative;
    right: 4px;
}

.collections-right-block-item-crystals-item.purple-wrapper:nth-child(3) {
    position: relative;
    right: 9px;
}

.collections-right-block-item-crystals-item.purple-wrapper:nth-child(4) {
    position: relative;
    right: 5px;
}

.collections-right-block-item-crystals-item.purple-wrapper:nth-child(5) {
    position: relative;
    left: 3px;
}

.collections-right-block-item-crystals-item.red-wrapper:nth-child(2) {
    position: relative;
    right: 2px;
}

.collections-right-block-item-crystals-item.red-wrapper:nth-child(3) {
    position: relative;
    right: 5px;
}

.collections-right-block-item-crystals-item.red-wrapper:nth-child(4) {
    position: relative;
    right: 2px;
}

.collections-right-block-item-crystals-item.red-wrapper:nth-child(5) {
    position: relative;
    left: 3px;
}

.collections-right-block-item-crystals-item.yellow-wrapper:nth-child(2) {
    position: relative;
    right: 3px;
}

.collections-right-block-item-crystals-item.yellow-wrapper:nth-child(3) {
    position: relative;
    right: 9px;
}

.collections-right-block-item-crystals-item.yellow-wrapper:nth-child(4) {
    position: relative;
    right: 4px;
}

.collections-right-block-item-crystals-item.yellow-wrapper:nth-child(5) {
    position: relative;
    left: 2px;
}

.collections-right-block-item-crystals-item-blur.greens {
    position: relative;
    top: 0;
    height: 25px !important;
}

.collections-right-block-item-crystals-item-blur.blues {
    position: relative;
    top: 9px;
    height: 35px !important;
}

.collections-right-block-item-crystals-item-blur.reds {
    position: relative;
    top: -4px;
}

.collections-right-block-item-crystals-item-blur.yellows {
    position: relative;
    top: -4px;
}

.collections-right-block-item-crystals-item-blur.purples {
    position: relative;
    top: -10px;
}

.ifCrystallZero {
    filter: grayscale(0) !important;
}

.ifCrystallFilter {
    filter: grayscale(1);
}

.newsfeed-gamification {
    display: none;
}

@media (max-width: 1800px) {
    .increase-crystal {
        left: -95px;
    }
}

@media (max-width: 1750px) {
    .collections-right-block-item-crystals {
        justify-content: space-between;
    }
}

@media (max-width: 1400px) {
    .increase-crystal {
        left: -82px !important;
    }
}

.hideGamification {
    @media screen and (max-width: $laptop-width) {
        display: none;
    }
}

img[class*='Crystal'] {
    pointer-events: none;
}
