.gamification-popup {
    .modal-dialog {
        .modal-content {
            border-radius: 80px;
            width: 700px;
            padding: 50px;
            padding-bottom: 20px;
            background: $gamification-blue-gradient !important;
        }
    }
    &-small {
        .modal-dialog {
            .modal-content {
                margin: 0 auto;
                width: 500px;
                .instruction-popup-wrapper {
                    height: 250px;
                    & .new-pop-up-body {
                        width: 100%;
                        color: #3d9f9f;
                        display: flex;
                        justify-content: space-between;
                        & .left-block {
                            width: 37%;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            flex-wrap: wrap;
                            & p {
                                text-align: center;
                            }
                            & h4 {
                                font-weight: 600;
                            }
                            & span {
                                width: 100%;
                                display: inline-block;
                                text-align: center;
                            }

                            img {
                                pointer-events: none;
                            }
                        }
                        & .right-block {
                            width: 65%;
                            margin-left: 60px;
                            & h4 {
                                font-weight: 600;
                            }
                        }
                    }
                    .ua-margin {
                        margin-top: 24px !important;
                    }
                    .pl-margin {
                        margin-top: 0 !important;
                    }
                }
                .gamification-popup-dots {
                    width: 400px;
                }
            }
        }
    }
    &-image {
        height: 60px;
        width: 60px;
        &-stars {
            height: 25px;
            width: 25px;
            margin-right: 15px;
        }
        &-wrapper {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 120px;
            border-radius: 100%;
            background: $gamification-orange-gradient;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    &-close {
        position: absolute;
        top: -15px;
        right: -15px;
        height: 30px;
        width: 30px;
        background-color: #dbfafa;
        border-radius: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
.close-pop-up {
    .modal-dialog {
        .modal-content {
            width: 750px !important;
        }
    }
}
.gamification-popup-for-congratulation {
    .modal-dialog {
        .modal-content {
            border-radius: 80px;
            padding: 50px 40px 50px 50px;
            width: 650px;
            background: $gamification-blue-gradient !important;
        }
    }
    &-small {
        .modal-dialog {
            .modal-content {
                width: 500px;
                .instruction-popup-wrapper {
                    height: 250px;
                }
                .gamification-popup-dots {
                    width: 400px;
                }
            }
        }
    }
    &-image {
        max-height: 60px;
        &-stars {
            height: 25px;
            width: 25px;
            margin-right: 15px;
        }
        &-wrapper {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            border-radius: 100%;
            background: $gamification-orange-gradient;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    &-close {
        position: absolute;
        top: -15px;
        right: -15px;
        height: 30px;
        width: 30px;
        background-color: #dbfafa;
        border-radius: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
.gamification-popup-for-success {
    .modal-dialog {
        .modal-content {
            border-radius: 80px;
            width: 670px;
            padding: 50px;
            padding-bottom: 30px;
            background: $gamification-blue-gradient !important;
        }
    }
    &-small {
        .modal-dialog {
            .modal-content {
                width: 500px;
                .instruction-popup-wrapper {
                    height: 250px;
                }
                .gamification-popup-dots {
                    width: 400px;
                }
            }
        }
    }
    &-image {
        max-height: 55px;
        &-stars {
            height: 35px;
            width: 35px;
            margin-right: 10px;
            margin-left: 10px;
        }
        &-wrapper {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            border-radius: 100%;
            background: $gamification-orange-gradient;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    &-close {
        position: absolute;
        top: -15px;
        right: -15px;
        height: 30px;
        width: 30px;
        background-color: #dbfafa;
        border-radius: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
.gamification-popup-for-level {
    .modal-dialog {
        max-width: 400px;
        .modal-content {
            border-radius: 80px;
            width: 400px;
            padding: 50px;
            padding-bottom: 30px;
            background: $gamification-blue-gradient;
        }
    }
    &-small {
        .modal-dialog {
            .modal-content {
                width: 500px;
                .instruction-popup-wrapper {
                    height: 250px;
                }
                .gamification-popup-dots {
                    width: 400px;
                }
            }
        }
    }
    &-image {
        max-height: 60px;
        &-stars {
            height: 25px;
            width: 25px;
            margin-right: 15px;
        }
        &-wrapper {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            border-radius: 100%;
            background: $gamification-orange-gradient;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    &-close {
        position: absolute;
        top: -15px;
        right: -15px;
        height: 30px;
        width: 30px;
        background-color: #dbfafa;
        border-radius: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
.instruction-popup-wrapper-for-level {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    height: 200px;
}
.instruction-popup-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    .one-person {
        text-align: center;
        padding: 20px;
        font-size: 25px;
        word-break: break-word;
        color: #3d9f9f;
    }
    .many-persons {
        text-align: center;
        font-size: 20px;
        word-break: break-word;
        color: #3d9f9f;
    }
    .lvl-up-wrapper {
        text-align: center;
        font-size: 18px;
        word-break: break-word;
        color: #3d9f9f;
        img {
            width: 100px;
            margin-top: 20px;
        }
    }
    .for-wrapper span:not(:last-child):after {
        content: ', ';
    }
    .for-wrapper-en:after {
        content: ' 😊';
    }
    .for-wrapper {
        .person-title {
            color: #3d9f9f;
        }
        .person-title:hover {
            color: #fdd14a;
        }
    }
    .center-if {
        justify-content: center;
        margin-bottom: 50px;
    }
    .first-place {
        text-align: center;
        font-size: 16px;
        word-break: break-word;
        color: #3d9f9f;
        //padding: 10px;
    }
    .second-place {
        text-align: center;
        font-size: 17px;
        word-break: break-word;
        padding: 35px;
        color: #3d9f9f;
    }

    .stones-container {
        display: flex;
        gap: 25px;
    }

    &-stone {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        flex-direction: column;
    }
    &-vacancy {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        padding: 0 50px 0;
        margin-top: -15px;
        &-close-img {
            width: 20px;
            margin-top: 10px;
        }
        &-equal {
            color: #fd5701;
            font-size: 35px;
            margin-bottom: 5px;
        }
        &-counter {
            margin-top: 0 !important;
            padding-top: 30px !important;
            padding-bottom: 30px !important;
            border-radius: 60px !important;
        }
    }
    &-text {
        margin: 30px 50px 30px;
        text-align: center;
        &-level {
            margin-left: -25px;
            margin-bottom: 15px;
            margin-right: 0;
            &-img {
                width: 150px;
            }
            &-wrapper {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: flex-start;
            }
        }
        &-stones {
            margin: 30px 20px 50px;
        }
    }
    semicircle-progress-bar {
        section {
            #container {
                width: 150px;
            }
            svg {
                height: 22px;
                border-radius: 25px;
                width: 100%;
                fill: none;
                stroke: url(#gradient);
                stroke-width: 8;
            }
        }
    }
    .gradient-block-in-popup {
        background: $gamification-orange-gradient-awards;
        border-radius: 25px;
        padding: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 150px;
    }
}
.gamification-popup-dots {
    position: absolute;
    bottom: -30px;
    width: 600px;
    display: flex;
    justify-content: center;
    &-item {
        height: 15px;
        width: 15px;
        background: orange;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 30px;
        &-fill {
            height: 5px;
            width: 5px;
            background: white;
            border-radius: 50%;
            z-index: 1;
        }
        &.active {
            background: white;
            z-index: 10;
            box-shadow: 0px 0px 5px #fff;
        }
        &.done {
            background: white;
            z-index: 10;
        }
    }
}
.state-quest-block {
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 200px;
    right: 50px;
    width: 400px;
    &-header {
        padding: 20px 60px;
        display: flex;
        flex-direction: column;
        align-items: center;
        background: $gamification-orange-gradient;
        border-radius: 50px;
        &-text {
            color: #ffffff;
            margin-bottom: 10px;
        }
        &-stage {
            color: #ffffff;
            font-weight: 500;
        }
    }
    &-status {
        display: flex;
        flex-direction: column;
        border-radius: 50px;
        overflow: hidden;
        margin-top: 20px;
        &-items {
            display: flex;
            padding: 20px;
            padding-left: 50px;
            align-items: flex-start;
            flex-direction: column;
            background: $gamification-blue-gradient;
            &-circle {
                min-width: 15px;
                height: 15px;
                margin-right: 20px;
                background: transparent;
                border: 1px solid #fd5c06;
                border-radius: 50%;
                &.filled {
                    background: $gamification-orange-gradient;
                }
            }
        }
        &-award {
            display: flex;
            padding: 15px;
            justify-content: center;
            background: $gamification-orange-gradient-awards;
        }
    }
    &-footer {
        background: $gamification-blue-gradient;
        margin-top: 20px;
        border-radius: 50px;
        display: flex;
        justify-content: center;
        padding: 20px;
    }
}
.state-quest-block.stage4 {
    top: 250px;
}
.state-quest-block.stage5 {
    top: 250px;
}
.state-quest-block.onVacancies {
    top: 300px;
}
.state-quest-block.stage6 {
    top: 130px;
}
.state-quest-block.stage7 {
    top: 130px;
}
.onboarding_lang_option {
    color: #3d9f9f;
    font-weight: 500;
    pointer-events: auto;
    cursor: pointer;
}
.onboarding_lang_option:not(:last-child) {
    margin-right: 20px;
}
.onboarding_currentLang {
    color: #fd5a06;
    font-weight: 500;
}

.onboarding-zindex {
    z-index: 6 !important;
}
.backdrop-zindex {
    z-index: 5 !important;
}
.organizer-welcome-popup {
    .modal-content {
        max-height: 635px !important;
    }
}

.gamification-popup-in-vacancy-stages {
    position: absolute;
    bottom: 0;
    left: 500px;
    width: 550px;
    height: 200px;
    border-radius: 60px;
    padding-left: 30px;
    padding-right: 30px;
    background: $gamification-blue-gradient;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.onboarding-lang {
    display: flex;
    z-index: 1050;
    margin-left: 50px;
    align-items: center;
}

.welcome-pop-up {
    .modal-dialog {
        width: 830px;
    }
}
@media (max-height: 720px) {
    .welcome-pop-up {
        .modal-dialog {
            max-height: 75% !important;
        }
        .modal-content {
            width: 770px !important;
            padding: 30px;
            top: -33px !important;
        }
    }
}
@media (max-height: 860px) {
    .gamification-popup-in-vacancy-stages.onboarding {
        bottom: 80px;
    }
    .state-quest-block.stage6 {
        position: absolute;
        top: 80px;
        right: 20px;
    }
    .state-quest-block.stage7 {
        position: absolute;
        top: 65px;
        right: 20px;
    }
}
@media only screen and (min-width: 1900px) {
    .gamification-popup-in-vacancy-stages {
        left: 700px;
    }
}
@media only screen and (max-width: 1280px) {
    .gamification-popup-in-vacancy-stages {
        left: 400px;
    }
}
@media only screen and (max-width: 1200px) {
    .gamification-popup-in-vacancy-stages {
        left: 300px;
    }
}
@media only screen and (max-width: 905px) {
    .gamification-popup-in-vacancy-stages {
        left: 150px;
    }
}
.onboarding-change-state-modal-wrapper {
    .modal-dialog {
    }
}
.instruction-popup-wrapper.center-if {
    justify-content: center;
    margin-bottom: 50px;
}
.gamification-footer-with-one-btn {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}
.gamification-big-orange-title {
    margin-left: 12px;
    background: $gamification-orange-gradient;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    font-weight: 500;
    font-size: 55px;
    letter-spacing: 4px;
    line-height: 1;
}
.gamification-big-orange-title-for-congratulation {
    background: $gamification-orange-gradient;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    font-weight: 500;
    font-size: 60px;
    letter-spacing: 4px;
    line-height: 1;
}

.gamification-pop-up-list-ul {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    justify-content: flex-start;
    margin-left: 27px;
    margin-bottom: 0;
    margin-top: 0;
    list-style: none;
    & li::before {
        content: '• ';
        color: #3d9f9f;
    }
}

.linkToResults {
    color: #fd5c06;
}
.linkToResults:hover {
    color: #fdd14a;
}

@media only screen and (max-width: 900px) {
    .gamification-big-orange-title {
        margin: 0;
        background: $gamification-orange-gradient;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        font-weight: 500;
        font-size: 55px;
        letter-spacing: 4px;
        line-height: 1;
    }
}
.orange-title-small {
    font-size: 35px;
    margin-right: 0;
    letter-spacing: 0;
}
.secondary-orange-title-gamification {
    color: $gamification-orange-title-color !important;
    font-size: 14px;
}
.ml-75 {
    margin-left: 75px;
}
.headline-orange-gamification {
    color: $gamification-orange-title-color;
    font-size: 16px;
    font-weight: 500;
}
.gamification-blue-title {
    color: $gamification-blue-title-color;
    font-weight: 500;
    font-size: 16px;
    &-secondary {
        color: $gamification-blue-title-color;
    }
    &-secondary-for-congratulation {
        color: $gamification-blue-title-color;
        font-size: 20px;
    }
    &-secondary-for-level {
        color: $gamification-blue-title-color;
        font-size: 30px;
    }
    &.popup-header {
        font-size: 20px;
        margin-top: 30px;
    }
    &.popup-header-for-congratulation {
        font-size: 30px;
        margin-top: 30px;
    }
    &.popup-header-for-level {
        font-size: 30px;
        margin-top: 60px;
    }
    &.popup-header-for-success {
        font-size: 30px;
    }
}
.orange-check-mark-img {
    width: 50px;
}
.margin-top-10 {
    margin-top: 10px;
}
.margin-top-30 {
    margin-top: 30px;
}
.margin-bottom-30 {
    margin-bottom: 30px;
}
.font-weight-normal {
    font-weight: normal;
}
.fd-c {
    flex-direction: column;
    display: flex;
}
.ai-fs {
    display: flex;
    align-items: flex-start;
}
.fw-b {
    font-weight: 500;
}
.mb-15 {
    margin-bottom: 15px;
}
.width100 {
    width: 100%;
}
.flex-column-align-center {
    display: flex;
    flex-direction: column;
    align-items: center;
}
.active-onboarding-action {
    border: 2px solid #fd5a06;
    border-radius: 5px !important;
    margin: 3px;
    animation: pulse 1.5s infinite;
    -webkit-animation: pulsing 1.9s infinite;
}
.active-onboarding-action-second-nav {
    border: 2px solid #fd5a06;
    border-radius: 5px !important;
    animation: pulse 1.5s infinite;
    -webkit-animation: pulsing 1.9s infinite;
}
.active-onboarding-action-stages {
    border: 2px solid #fd5a06;
    border-radius: 5px !important;
    -webkit-animation: pulsing 1.9s infinite;
}
.active-onboarding-action-industry {
    border: 2px solid rgba(253, 90, 6, 1);
    border-radius: 5px !important;
    padding: 5px 5px 0;
    animation: pulse 1.5s infinite;
    -webkit-animation: pulsing 1.9s infinite;
}
.active-onboarding-action-btn {
    border: 2px solid #fd5a06;
    border-radius: 5px;
    padding: 5px;
    animation: pulse 1.5s infinite;
    -webkit-animation: pulsing 1.9s infinite;
}
.active-onboarding-action-btn-for-close {
    border: 2px solid #fd5a06;
    padding: 5px;
    animation: pulse 1.5s infinite;
    -webkit-animation: pulsing 1.9s infinite;
}
@keyframes pulsing {
    0% {
        border: 2px solid rgba(253, 90, 6, 0.3);
    }
    15% {
        border: 2px solid rgba(253, 90, 6, 0.8);
    }
    50% {
        border: 2px solid rgba(253, 90, 6, 1);
        box-shadow: 0 0 0 6px rgba(255, 99, 71, 0.3);
    }
    100% {
        border: 2px solid rgba(253, 90, 6, 0.2);
    }
}
.skipOnboardingBlock {
    position: fixed;
    background: #b4b4b5;
    color: white;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    transform: translate(-50%, -50%);
    bottom: 10px;
    left: 50%;
    width: max-content;
    border-radius: 5px;
    padding: 10px 20px;
    opacity: 0.7;
    &-text {
        text-decoration: underline;
        margin-right: 5px;
    }
}
.onboarding-completer {
    height: 32px;
    border-radius: 5px;
    border: 1px solid #ccc;
    padding-left: 5px;
    display: flex;
    padding-left: 5px;
    align-items: center;
    position: relative;
    color: #999;
    &-carret {
        position: absolute;
        right: 0;
        width: 30px;
        height: 100%;
        background: #f1f1f1;
        border-bottom-right-radius: 5px;
        border-top-right-radius: 5px;
        border-left: 1px solid #ccc;
        display: flex;
        justify-content: center;
        align-items: center;
        &-img {
            width: 12px;
            height: 15px;
        }
    }
}
.onboarding-input {
    height: 32px;
    border-radius: 5px;
    border: 1px solid #ccc;
    padding: 5px;
}
.onboarding-completer-red {
    color: red !important;
}
.onboarding-completer-grey {
    color: #555555 !important;
}
.gamification-notification {
    display: flex;
    flex-direction: column;
    border-radius: 50px;
    overflow: hidden;
    opacity: 0;
    max-width: 250px;
    margin-top: 30px;
    &-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        position: fixed;
        right: 20px;
        top: 90px;
        z-index: 9999999;
    }
    &:first-child {
        margin-top: 0;
    }
    &-top {
        padding: 10px 20px;
        background: $gamification-blue-gradient;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    &-bottom {
        background: $gamification-orange-gradient-awards;
        padding: 10px;
        display: flex;
        justify-content: center;
    }
}
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(169, 60, 55, 0.2);
    }

    70% {
        box-shadow: 0 0 0 5px rgba(169, 60, 55, 0.2);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(169, 60, 55, 0.2);
    }
}
.pointer-events-none {
    pointer-events: none;
}
@media (max-width: 1200px) {
    .vacancy-page-info-category.onboarding {
        .secondlyInVacancy {
            margin-left: 7px;
        }
    }
}
@media (max-width: 1000px) {
    .skipOnboardingBlock {
        text-align: center;
        width: auto;
    }
}
@media (max-width: 1466px) {
    .vacancy-page-info-category.onboarding {
        width: 40%;
    }
}

.gamification-new-popup {
    & .modal-content {
        width: 835px !important;
        height: 600px !important;
        padding-top: 35px !important;
        top: 15px;
    }
    p {
        font-size: 17px;
        letter-spacing: 0.7px;
    }
}
.race_results {
    .modal-content {
        padding-right: 10px !important;
    }
}

.how-achievements-work {
    width: fit-content;
    margin: var(--main-gap);
    margin-left: auto;
}
