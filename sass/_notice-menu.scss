@import 'js/common/styles/global-variables.module';
@import 'variables';
@import 'mixins';

.navbar {
    .nav {
        &.information {
            .label {
                position: absolute;
                top: 0;
                right: 0;
            }

            .notices {
                position: absolute;
                top: 50px;
                right: 0;
                z-index: 100000;

                display: none;
                width: 500px;
                max-width: 85vw;
                font-size: $main-font-size;

                cursor: default;
                background-color: $main-white;
                border: 1px solid $dark-grey;
                border-radius: 12px;
                box-shadow: $border-box-shadow;

                &__header {
                    padding: 12px;
                    font-weight: 500;
                    color: $main-black;
                    background-color: $main-white;
                    border-bottom: 1px solid $border-grey;
                    border-radius: 12px 12px 0 0;
                }

                &__footer {
                    display: flex;
                    gap: 10px;
                    justify-content: center;
                    width: 100%;
                    padding: 12px;
                    border-top: 1px solid $border-grey;

                    button-component {
                        width: 50%;

                        button {
                            width: 100%;
                            font-size: $secondary-font-size;
                        }
                    }
                }

                &__body {
                    min-height: 45px;
                    max-height: 280px;
                    padding: 12px 5px 12px 12px;
                    margin-right: 4px;
                    overflow: auto;

                    &__items {
                        display: flex;
                        padding: 12px;
                        margin-top: 8px;
                        text-align: left;
                        border: 1px solid $border-grey;
                        border-radius: 8px;

                        &:first-child {
                            margin-top: 0;
                        }

                        &.read {
                            background-color: $background-grey;
                        }

                        .notice-info {
                            display: flex;
                            flex-direction: row;
                            justify-content: space-between;
                            width: 100%;

                            .notice {
                                font-size: $secondary-font-size;
                                line-height: 18px;
                                color: $main-black;
                                letter-spacing: 0.28px;
                                word-break: break-word;

                                .notice-wrapper {
                                    display: flex;
                                    gap: 4px;
                                    align-items: center;
                                }

                                .notice-btn {
                                    @include green-link();
                                    padding: 0;
                                    margin: 0;
                                    background-color: transparent;
                                    border: none;
                                }

                                .candidate-notice-link {
                                    @include link-styling();
                                    display: inline-flex;
                                }

                                .candidate-notice-name {
                                    display: inline-flex;
                                }

                                .candidates-other-link {
                                    @include green-link();
                                }
                            }

                            .date {
                                display: inline-block;
                                min-width: 90px;
                                margin-top: 4px;
                                font-size: $small-font-size;
                                line-height: 16px;
                                color: $semi-black;
                                letter-spacing: 0.24px;
                            }

                            &__right-block {
                                display: flex;
                                align-items: center;
                            }
                        }
                    }

                    .no-messages {
                        padding: 24px 0;
                        font-size: $main-font-size;
                        color: $semi-black;
                        text-align: center;
                    }

                    .pdcs-notice {
                        a {
                            display: inline-block;
                        }
                    }

                    a {
                        display: inline-block;
                        font-weight: normal;
                    }

                    .candidate a {
                        display: inline;
                    }

                    a.candidate {
                        display: inline-block;
                    }

                    .red {
                        color: $theme-red;
                    }

                    &::-webkit-scrollbar {
                        width: 4px;
                        height: 4px;
                    }
                }

                .readed {
                    background: $main-white;
                    border: 1px solid $border-grey;
                }

                .uppercase {
                    text-transform: uppercase;
                }

                .lowercase {
                    text-transform: lowercase;
                }

                a {
                    @include green-link();
                    padding-top: 0;
                    padding-bottom: 0;
                    font-size: $secondary-font-size;
                }

                .responsible,
                .taskChangeStatus {
                    a {
                        display: inline-block;
                    }
                }

                .fa-check-circle-o {
                    font-size: 18px;

                    &.green {
                        color: $theme-dark-green;
                    }
                }

                .view-letter-button {
                    width: 136px;
                    height: 24px;
                    padding: 0;
                    border-radius: 100px;
                }
            }
        }
    }
}
