.block-custom-reports {
    h4 {
        color: #202021 !important;
        font-size: 14px;
        font-weight: 500 !important;
        margin: 0;
        padding: 0;

        background: none !important;
    }

    .topic-title {
        display: inline-block;
        font-weight: 700 !important;
        word-break: break-word;
        font-size: 14px !important;
        color: #202021 !important;
    }

    .active {
        display: block !important;
        visibility: visible !important;
        max-height: 200px !important;
        padding-top: 16px !important;
        padding-bottom: 16px !important;
    }
    main {
        padding: 0 15px;
    }

    section {
        margin-right: 0;
        margin-bottom: 16px;
        margin-left: 0;
    }
    h3 {
        display: flex;
        margin: 16px 0;
        .icon {
            font-size: 20px;

            margin-left: 5px;
            padding: 4px;

            color: #45aaec;
        }
        .fa {
            &-pencil:hover {
                transform: scale(1.5);
            }
            &-times:hover {
                transform: scale(1.5);
            }
        }
        button {
            margin-left: auto;
        }
    }
    .time-interval-wrapper {
        width: 100% !important;
        background: #ffffff;
        box-shadow: 2px 4px 4px rgba(0, 0, 0, 0.1);
        border-radius: 5px;
        padding: 14px !important;

        h4 {
            margin-right: 15px !important;
        }
        .active {
            padding: 0px !important;
        }
    }
    .wrap-datepicker {
        margin-left: 20px;

        flex-grow: 1;
        & .left-date,
        .right-date {
            & button {
                position: relative;
                left: 8px;
                padding-left: 0;
                padding-right: 0;
            }
        }
        & mdp-date-picker {
            md-input-container {
                max-width: 100px;
                width: 100px;
            }
            label {
                color: #202021 !important;
            }
            input {
                background: white !important;
                border: 1px solid #cccccc !important;
            }
        }
    }
    .downloadReport {
        font-weight: 600;

        margin-right: 17px;
        margin-left: auto;
        padding: 3px 12px;

        color: #fff;
        border: none;
        border-radius: 5px;
        background-color: #00b549;
        &:hover {
            color: #202021;
            background-color: #a3e8a7;
        }
    }
    .table-outer {
        margin-top: 30px;
        padding: 0;

        border: 1px solid $theme-light-grey-hover;
        &-scroll {
            overflow-y: auto;
            max-height: 500px;
        }
        .report-first {
            @include table-styling();

            width: 100%;
            margin-bottom: 0;

            table-layout: fixed;

            background-color: $theme-light-green;
            th {
                @include bold();

                vertical-align: top;

                color: $theme-dark;
                background-color: $theme-white;
            }
            :nth-child(1) {
                width: 4%;
            }
            :nth-child(2) {
                width: 11%;
            }
            :nth-child(3) {
                width: 9%;
            }
            :nth-child(4) {
                width: 9%;
            }
            :nth-child(5) {
                width: 10%;
            }
            :nth-child(6) {
                width: 10%;
                padding-left: 0;
            }
            :nth-child(7) {
                width: 9%;
                padding-left: 0;
            }
            :nth-child(8) {
                width: 8%;
                padding-left: 6px;
            }
            :nth-child(9) {
                width: 12%;
                padding-left: 5px;
            }
            :nth-child(10) {
                width: 10%;
            }
            tr {
                background-color: $theme-white;
                &:first-child {
                    background-color: $theme-light-green;
                }
                td {
                    vertical-align: bottom;
                    a {
                        font-weight: 600;
                    }
                    .small-font {
                        font-size: 11px;

                        color: $theme-light-grey;
                    }
                }
            }
        }
        .fixed-header {
            position: fixed;
            top: 0;

            width: 97.7%;
        }
        .tableWithoutBorderTr {
            margin-top: 0;
            margin-bottom: 0;

            background-color: $theme-subtitle-background;
            thead {
                border-bottom-style: hidden;
                tr {
                    th {
                        font-weight: 500;

                        padding: 0;

                        color: $theme-light-grey;
                        div {
                            display: inline;

                            color: #202021;
                        }
                        &:first-child {
                            color: #202021;
                        }
                        a {
                            @include link-styling();
                        }
                    }
                    .number-second-table {
                        width: 4%;
                    }
                    .number-second-table2 {
                        width: 43%;
                    }
                    .number-second-table3 {
                        width: 33%;
                    }
                    .number-second-table4 {
                        width: 26%;
                    }
                    .number-second-table5 {
                        width: 14%;
                    }
                    .number-second-table6 {
                        width: 10%;
                    }
                    .number-second-table7 {
                        width: 10%;
                    }
                    .number-second-table8 {
                        width: 13%;
                    }
                    .number-second-table9 {
                        width: 10%;
                    }
                    .number-second-table10 {
                        width: 10%;
                        span {
                            margin-left: 50px;

                            color: black;
                        }
                    }
                }
            }
            tbody {
                tr {
                    td {
                        padding: 0;

                        border-top: none;
                        a {
                            @include link-styling();
                        }
                        & > span {
                            font-weight: 500;

                            padding: 0;

                            color: #b8b8b8;
                        }
                    }
                }
            }
        }
        .custom-all-table {
            background-color: $theme-white;
            .candidateTableLast {
                td {
                }
                :first-child {
                    width: 4%;
                }
                :nth-child(2) {
                    width: 11%;
                    a {
                        @include link-styling();
                    }
                }
                :nth-child(3) {
                    width: 9%;
                }
                :nth-child(4) {
                    width: 9%;
                }
                :nth-child(5) {
                    width: 15%;
                }
                :nth-child(6) {
                    width: 10%;
                    padding-left: 0;
                }
                :nth-child(7) {
                    width: 9%;
                    padding-left: 0;
                }
                :nth-child(8) {
                    width: 8%;
                    padding-left: 10px;
                }
                :nth-child(9) {
                    width: 12%;
                    padding-left: 7px;
                }
                :nth-child(10) {
                    width: 8%;
                }
                .comment {
                    font-size: 12px;

                    word-wrap: break-word;
                }
                .language {
                    .inlineLang {
                        display: block;
                        width: 100%;
                    }
                }
            }
        }
    }

    .sortReport {
        margin-top: 20px;
        padding-left: 10px;
        select {
            margin-left: 15px;

            border-radius: 5px;
        }
    }
    .form {
        width: auto;
        padding: 8px 8px;
        padding-bottom: 0;

        border-radius: 5px;
        background: #fff;
        .fields {
            @include flex(row, space-between, center);

            position: relative;

            width: 100%;
            &-title {
                @include flex(row, space-between, center);
                .select-content {
                    position: absolute;
                    z-index: 999;
                    top: 110%;
                    left: 0;

                    visibility: hidden;
                    overflow: hidden;
                    width: 100%;
                    max-height: 0;
                    margin: 0;
                    padding: 0 8px;

                    transition: all 0.5s ease-in;

                    border-radius: 5px;
                    background-color: #fff;
                    box-shadow: 0 0 4px 4px rgba(0, 0, 0, 0.1);
                }
                &-select {
                    input {
                        @include input-styling();
                        @include border-radius();

                        border: 1px solid #e3e3e3;
                        outline: none;
                    }
                    li {
                        margin: 0;
                        padding: 0;
                        padding-bottom: 3px;

                        list-style: none;

                        cursor: pointer;
                        &:hover {
                            color: #f0ad4e;
                        }
                    }
                }
            }
            .field {
                input {
                    @include input-styling();
                    @include border-radius();

                    width: 100px;
                    //padding-left: 10px;

                    cursor: pointer;
                    &:focus {
                        outline: none;
                    }
                }
                span {
                    font-weight: 600;

                    margin-right: 5px;
                }
                label {
                    font-weight: normal;
                }
            }
            i {
                font-size: 12px;

                margin-left: 6px;

                color: #45aaec;
            }
            .tooltip-icon {
                top: 9px;
            }
            .info-content {
                position: absolute;
                z-index: 10;
                top: 115%;
                left: 25%;
                right: 0;

                display: none;
                width: 480px;
                padding: 10px;

                border-radius: 5px;
                box-shadow: none;
                color: #666;
                border: 1px solid #579ded;
                background: #ddecfc;
            }
        }
    }
    .button-outer {
        display: flex;
    }
    .dateRange .date-pickers .picker i.fa-calendar {
        top: calc(50% - 6px);
        right: 12px;
    }
}

.custom-report-title {
    font-size: 18px;
    font-weight: 600;
}
