.btn-new {
    color: #fff;
    min-width: 160px;
    padding: 0 20px;

    font-size: 14px;
    height: 32px;
    letter-spacing: 0.4px;
    border-radius: 25px;
    &.small {
        padding: 0 28px;
    }
    &.cancel {
        background-color: #999999;
        border: 1px solid #999999;
        margin-right: 30px;

        &:hover {
            background-color: #666666;
            box-shadow: inset 0 6px 10px 1px rgba(57, 57, 57, 0.75);
        }
    }
    &.accent {
        background-color: #00b549;
        border: 1px solid #00b549;

        &:hover {
            background-color: #019b3f;
            box-shadow: inset 0 6px 10px 1px rgba(0, 111, 29, 0.75);
        }
    }

    /*&:focus {
		border: 1px solid #00b549;
		outline: none;
	}*/
    &.apply-via {
        font-size: 14px;

        padding: 10px 20px;
        .fa-linkedin {
            font-size: 13px;

            padding: 1px 3px 2px 2px;

            color: #fff;
            border: 1px solid #fff;
            background-color: #3590c2;
        }
        &.linkedin {
            border: 1px solid #0274b3;
            background-color: #0274b3;
            &:hover {
                color: #0274b3;
                border: 1px solid #0274b3;
                background-color: #fff;
            }
        }
    }
}

// Custom input [type=radio]
.custom[type='radio'] {
    &:checked,
    &:not(:checked) {
        position: absolute;
        left: -9999px;
    }
    &:checked + label,
    &:not(:checked) + label {
        line-height: 20px;

        position: relative;

        display: inline-block;
        padding-left: 28px;

        cursor: pointer;

        color: #666;
    }
    &:checked + label:before,
    &:not(:checked) + label:before {
        position: absolute;
        top: 1px;
        left: 0;

        width: 18px;
        height: 18px;

        content: '';

        border: 1px solid #ddd;
        border-radius: 100%;
        background: #fff;
    }
    &:checked + label:after {
        position: absolute;
        top: 5px;
        left: 4px;

        width: 10px;
        height: 10px;

        content: '';
        -webkit-transition: all 0.2s ease;
        transition: all 0.2s ease;

        border-radius: 100%;
        background: #00b549;
    }
    &:not(:checked) + label:after {
        position: absolute;
        top: 5px;
        left: 4px;

        width: 10px;
        height: 10px;

        content: '';
        -webkit-transition: all 0.2s ease;
        transition: all 0.2s ease;
        -webkit-transform: scale(0);
        transform: scale(0);

        opacity: 0;
        border-radius: 100%;
        background: #00b549;
    }
    &:checked + label:after {
        -webkit-transform: scale(1);
        transform: scale(1);

        opacity: 1;
    }
}

// custom input [type=checkbox]
.custom[type='checkbox'] {
    position: absolute;

    opacity: 0; // hide it

    & + label {
        position: relative;

        padding: 0;

        cursor: pointer;
    }

    & + label:before {
        display: inline-block;
        width: 20px;
        height: 20px;
        margin-right: 10px;

        content: '';
        transition: all ease 0.2s;
        vertical-align: middle;

        border-radius: 50%;
        background: #e3f2dd;
    }

    &:hover + label:before {
        background: lightgrey;
    }

    &:focus + label:before {
        box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.12);
    }

    &:checked + label:before {
        border-radius: 50%;
        background: #00b549;
    }

    &:disabled + label {
        cursor: auto;

        color: #b8b8b8;
    }

    &:disabled + label:before {
        background: #ddd;
        box-shadow: none;
    }

    &:checked + label:after {
        position: absolute;
        top: 10px;
        left: 5px;

        width: 2px;
        height: 2px;

        content: '';
        transform: rotate(45deg);

        background: white;
        box-shadow: 2px 0 0 white, 4px 0 0 white, 4px -2px 0 white, 4px -4px 0 white, 4px -6px 0 white, 4px -8px 0 white;
    }
    &.default {
        & + label {
            color: #666;
        }
        & + label:before {
            display: inline-block;
            width: 15px;
            min-width: 15px;
            height: 15px;
            min-height: 15px;
            margin-right: 0;

            content: '';
            transition: none;
            vertical-align: middle;

            border: 1px solid #999;
            border-radius: 0;
            background: #fff;
        }

        &:checked + label {
            color: #333;
        }

        &:checked + label:before {
            color: #666;
            border-radius: 0;
            background: #fff;
        }

        &:checked + label:after {
            position: absolute;
            top: calc(50% - 1.41px);
            left: 2.3px;

            width: 2px;
            height: 2px;

            content: '';
            transform: rotate(45deg);

            background: #000;
            box-shadow: 2px 0 0 #000, 4px 0 0 #000, 4px -2px 0 #000, 4px -4px 0 #000, 4px -6px 0 #000, 4px -8px 0 #000;
        }
    }
}
