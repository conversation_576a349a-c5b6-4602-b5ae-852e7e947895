@import 'js/common/styles/global-variables.module';

.desired-salary-block {
    &__content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .form-control {
            &:active {
                border: 1px solid #cccccc;
                outline: none;
                box-shadow: none;
            }

            &:focus {
                border: 1px solid #cccccc;
                outline: none;
                box-shadow: none;
            }

            &.error {
                border: 1px solid $red;
            }
        }
    }

    &__main-divider {
        width: 30px;
        height: 1px;
        margin-right: 10px;
        margin-left: 10px;
        background-color: #c0c0c0;
    }

    &__currency {
        width: 25%;
    }

    &__hide {
        position: relative;
        display: flex;
        margin-top: 0;
    }

    .error-message-block {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 15px;
        padding-top: 2px;
        padding-bottom: 3px;

        .error-message {
            line-height: 10px;
            color: $red;
            transition: all 0.3s ease-in;
        }

        .hide {
            display: none;
        }

        .attach-block {
            width: 45%;
        }
    }
}
