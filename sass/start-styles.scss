// Core Framework
// -------------------------
//@import 'bootstrap/bootstrap';
//@import 'font-awesome/_variables';
//@import 'font-awesome/font-awesome';

//@import 'mCustomScrollbar/jquery.mCustomScrollbar.min.scss';
//@import 'pnotify/pnotify.custom.min';
//@import 'pnotify/jquery-ui-1.9.2.custom';
//@import 'select2/select2';
//@import 'bootstrap-date-picker/datepicker';
//@import 'angular-circular-timepicker/angular-circular-timepicker';
//@import 'angular-material/angular-material';
//@import 'mdPickers/mdPickers';
//@import 'lib/ngImgCrop/ng-img-crop';

// Theme styles
// -------------------------
@import 'variables';
@import 'svg-bg-images';
@import 'mixins';
@import '_fonts';
@import 'round-switcher';
@import 'common-styles';

@import 'new-buttons';
@import 'custom-select-new';
@import '_main-loader';
@import 'new-year-loader';
@import 'header';
@import 'signup-new';

@import 'start-styles/invitereg';
@import 'start-styles/vacancy-default-template';
@import 'start-styles/public-candidate';
@import 'start-styles/public-company-for-vacancies';
@import 'start-styles/public-test-for-candidate';
@import 'start-styles/_pdconsent-public-page';
@import 'start-styles/public-styles';
@import 'start-styles/restore-password';
@import 'scrollbar';
// ** Modal window ** //
@import 'modal/public-vacancy-form';
@import 'modal/vacancy-added-response';

// ** Custom templates ** //
@import 'publicPages/company-default-template';
@import 'start-styles/custom-templates/mev/company';
@import 'start-styles/custom-templates/mev/vacancy';
@import 'start-styles/custom-templates/mev/recallForm';

// ** Components ** //
@import 'publicPages/components/our-vacancies.component';
@import 'publicPages/components/publicPhotoCropper.component';
@import 'publicPages/components/scrollup.component';
@import 'publicPages/components/companyHeader.component';

// ** Forms ** //
@import 'forms/forms';

// ** Buttons ** //
@import 'common/buttons';
