.block-integration-with-email {
    color: #333;
    .info-icon {
        @include infoIcon();
    }

    .close-btn {
        position: absolute;
        top: 16px;
        right: 16px;
        z-index: 10;
        cursor: pointer;
        transition: 0.3s all;
        @include control-icon-size;
        @include icon-mask('/images/redesign/svg-icons/close.svg', $semi-black);

        &:hover {
            background-color: $main-black;
        }
    }
    //-------------------------------------------------------
    .no-emails-block {
        .header {
            letter-spacing: 0.6px;
            .header-row-backBtn {
                margin: 25px 0;
            }
            h1 {
                font-size: 24px;
                text-align: center;
                line-height: 33px;
                margin: 0;
            }
            h2 {
                font-size: 16px;

                margin-bottom: 40px;
            }
        }

        .main {
            padding: 0 15px;

            letter-spacing: 0.6px;
            .row {
                margin-bottom: 10px;
                display: flex;
                justify-content: center;
            }
            .main-row {
                display: flex;
                &.top-row {
                    .main-tile-left {
                        border-top-left-radius: 10px;
                    }
                    .main-tile-right {
                        border-top-right-radius: 10px;
                    }
                }
            }
            .main-title {
                font-size: 18px;
                line-height: 18px;
                margin-bottom: 20px;
            }
            .main-tile {
                font-size: 14px;

                display: flex;
                max-width: 648px;
                padding: 20px;

                background-color: white;

                flex-grow: 1;
                .tile-text {
                    display: flex;
                    flex-direction: column;

                    justify-content: center;
                    p {
                        margin-bottom: 0;
                    }
                    .tile-text-title {
                    }
                }
                .tile-img {
                    display: inline-block;
                    margin-right: 20px;
                    svg {
                        .st0 {
                            fill: $theme-green;
                        }
                        .st1 {
                            fill: $theme-orange;
                        }
                    }
                }
            }
            .main-tile-left {
                margin-right: 10px;

                border-top-right-radius: 10px;
                border-bottom-right-radius: 10px;
            }
            .main-tile-right {
                border-top-left-radius: 10px;
                border-bottom-left-radius: 10px;
            }
            .main-tile-central {
                border-radius: 10px;
            }
            .main-footer {
                padding: 20px 0 10px;
                .btn-add-mailbox {
                    @include button-animated($theme-btn-green, white, $theme-btn-green-hover, white);
                }
            }
        }

        @media screen and (max-width: 768px) {
            .main {
                .main-row {
                    flex-direction: column;
                    justify-content: center;
                }
                .main-tile {
                    width: 100%;
                    max-width: 100%;

                    border-radius: 10px;
                }
                .main-tile-left {
                    margin-bottom: 10px;
                }
            }
        }
    }
    //-------------------------------------------------------
    .integrated-block {
        .header-row-backBtn {
            margin-top: 23px;
            text-align: center;
            .title {
                font-size: 16px;
                line-height: 24px;
                letter-spacing: 0.004em;
                color: #333333;
                padding: 20px;
                background-color: white;
                max-width: 1072px;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                margin: 0 auto 8px;
                .text {
                    font-size: 24px !important;
                    color: #000000;
                }
                .integration-title {
                    font-size: 16px;
                    line-height: 24px;
                    letter-spacing: 0.004em;
                    color: #333333;
                }
            }
        }
        .central-block {
            .header {
                padding: 15px 0;

                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                background-color: white;
                .title {
                    display: flex;

                    justify-content: center;
                    .img {
                        margin-right: 5px;
                        svg {
                            .st0 {
                                fill: $theme-green;
                            }
                            .st1 {
                                fill: $theme-orange;
                            }
                        }
                    }
                    .text {
                        font-size: 26px;
                    }
                }
            }
            .main-tails-row {
                display: flex;
                width: 100%;
                max-width: 1075px;
                margin: 0 auto;
                height: 321px;
                .mbList-tile {
                    height: 480px;
                    width: 45%;
                    margin-right: 8px;
                    padding: 20px;
                    border-bottom-left-radius: 10px;
                    background-color: white;
                    .email-column {
                        width: 59%;
                        float: left;
                        margin-left: 2px;
                    }
                    .switch-column {
                        width: calc(20% - 20px);
                        float: left;
                        text-align: center;
                        display: flex;
                        align-items: center;
                        margin-left: 19px;
                        justify-content: center;
                        label {
                            margin: 0 auto;
                            padding-left: 0;
                        }
                        .checkbox-block-label {
                            width: 15px;
                            height: 19px;
                        }
                        &__disabled {
                            background-color: #f8f8f8;
                        }
                    }
                    .mbList-header {
                        display: flex;
                        position: relative;
                        .mbList-title {
                            position: relative;
                            display: flex;
                            align-items: flex-end;
                            justify-content: center;
                            &.switch-column {
                                width: 20%;
                                margin: 0;
                                padding-left: 11px;
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                            }
                            .title-column {
                                font-size: 12px !important;
                                line-height: 1;
                                letter-spacing: 0.004em;
                                color: #999999;
                                display: block;
                                width: auto;
                                &-top {
                                    position: relative;
                                    display: inline;
                                }
                            }
                            .info-icon {
                                top: -11px;
                                right: -13px;
                                z-index: 1;
                            }
                        }
                        .new-label {
                            top: -9px;
                            right: -18px;
                            color: #4995eb;
                            white-space: nowrap;
                        }
                    }
                    .mbList-block {
                        margin-top: 11px;
                        border-radius: 5px;
                        .mblist-block-list {
                            max-height: 200px;
                            .switcher-main-wrapper input.switch-toggle-round + label {
                                height: 19px;
                            }
                            .switcher-main-wrapper input.switch-toggle-round + label:after {
                                width: 18px;
                            }
                            .mCustomScrollBox,
                            .mCSB_container {
                                position: static !important;
                            }
                            .mbList-mailbox__container {
                                display: flex;
                                margin-bottom: 15px;
                                .mbList-mailbox__email {
                                    position: relative;
                                    display: flex;

                                    padding: 5px 10px;

                                    letter-spacing: 0.6px;
                                    border-radius: 5px;

                                    justify-content: space-between;
                                    align-items: center;
                                    .email {
                                        max-width: 245px;
                                        overflow: hidden;
                                        text-overflow: ellipsis;
                                    }
                                    &.editable {
                                        .bucket {
                                            background-image: url('/images/sprite/icons/remove-email-icon-green.svg');
                                        }
                                        .gm-no-int {
                                            margin-left: 3px;
                                        }
                                    }
                                    .bucket,
                                    .star,
                                    .gm-no-int {
                                        display: none;
                                        width: 16px;
                                        height: 16px;
                                        font-size: 16px;
                                        background-size: contain;
                                    }
                                    .icons {
                                        position: absolute;
                                        right: 5px;
                                        height: 16px;
                                        align-items: center;
                                        display: inline-flex;
                                        span {
                                            justify-content: center;
                                            align-items: center;
                                            margin-left: 5px;
                                        }
                                        &.default {
                                            .star {
                                                display: inline-flex;

                                                background: url('/images/sprite/icons/favorit-star-active.svg')
                                                    no-repeat;
                                            }
                                        }
                                        &.gm-no-integration {
                                            .gm-no-int {
                                                display: inline-flex;
                                                color: red;
                                            }
                                        }
                                    }
                                    .bucket {
                                        margin-left: 4px;
                                        display: inline-flex;
                                        background: url('/images/sprite/icons/remove-email-icon.svg') no-repeat;
                                    }
                                    &:hover {
                                        cursor: pointer;
                                    }
                                }
                                &.one-mb {
                                    .mbList-mailbox {
                                        .icons.default .star,
                                        .icons .star {
                                            display: none;
                                        }
                                        &.editable {
                                            .icons .star {
                                                display: none;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .mb-settings-tile {
                    width: calc(55% - 10px);
                    min-width: 569px;
                    height: 480px;
                    background-color: white;
                    border-bottom-right-radius: 10px;
                }
                @media (max-width: 1000px) {
                    flex-direction: column;
                    .mbList-tile {
                        width: 100%;
                        margin-bottom: 10px;
                    }
                    .mb-settings-tile {
                        width: 100%;
                    }
                }
            }
            .mailing-info-block {
                display: flex;
                width: 100%;
                max-width: 1075px;
                border-radius: 10px;
                margin: 30px auto 25px;
                background-color: white;
                padding: 20px;
                mailing-info-component {
                    width: 100%;
                }
            }
        }
    }
    //-------------------------------------------------------
    .new-integration {
        position: fixed;
        z-index: 1049;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;

        display: flex;

        align-items: center;
        justify-content: center;
        .new-integration-block {
            z-index: 1050;

            width: 1200px;

            border-radius: 10px;
            background-color: white;
            .header {
                position: relative;

                height: 60px;

                border-bottom: 1px solid #ccc;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                background-color: $theme-subtitle-background;
                .header-title {
                    font-size: 24px;

                    margin: 0;
                    padding: 17px;

                    text-align: center;
                    letter-spacing: 0.6px;
                }
                .header-close {
                    position: absolute;
                    top: 15px;
                    right: 15px;

                    width: 14px;
                    height: 14px;

                    cursor: pointer;
                }
            }
            .status_bar {
                display: flex;
                padding: 20px 10px;

                color: #333;

                align-items: center;
                .line {
                    height: 0;

                    border: 1px solid #acacac;
                }
                .line-small {
                    width: 2%;
                    margin: 0 10px;
                }
                .line-central {
                    margin: 0 10px;
                    &:nth-of-type(2) {
                        flex-basis: 157px;
                    }
                    &:nth-of-type(4) {
                        flex-basis: 215px;
                    }
                }
                &.ru-width {
                    .line-central {
                        &:nth-of-type(2) {
                            flex-basis: 83px;
                        }
                        &:nth-of-type(4) {
                            flex-basis: 118px;
                        }
                    }
                }
                .steps {
                    display: flex;
                    width: 93%;

                    align-items: center;
                    .step {
                        display: flex;

                        align-items: center;
                        &.active {
                            color: $theme-green;
                            .step_number-round {
                                color: white;
                                border-color: $theme-green;
                                background-color: $theme-green;
                            }
                        }
                        .step_number-round {
                            font-size: 18px;

                            position: relative;

                            display: inline-block;
                            width: 30px;
                            height: 30px;
                            margin-right: 8px;

                            color: #acacac;
                            border: 2px solid #acacac;
                            border-radius: 50%;
                            .number {
                                position: absolute;
                                top: 1px;
                                left: 8px;
                            }
                        }
                    }
                }
            }

            .integration-main-block {
                margin: 0 10px;

                .step-header {
                    font-size: 18px;
                    padding: 10px 0;
                    text-align: center;
                    letter-spacing: 0.6px;
                }
                .step-view {
                    .first-step {
                        display: flex;
                        height: 410px;
                        border-top: 1px solid #ccc;
                        border-bottom: 1px solid #ccc;
                        flex-direction: column;
                        justify-content: space-around;
                        .mailbox-types {
                            display: flex;
                            width: 29%;
                            height: 70%;
                            margin: 0 auto;
                            justify-content: space-between;
                            flex-wrap: wrap;
                            :last-child {
                                margin: 0 auto;
                            }
                            .mail-type-button {
                                width: 141px;
                                font-size: 0;
                                margin-bottom: 20px;
                                height: 45px;
                                display: flex;
                                align-items: center;
                                background: white;
                                color: #444;
                                border-radius: 5px;
                                border: thin solid #888;
                                box-shadow: 1px 1px 1px grey;
                                white-space: nowrap;
                                padding: 15px 16px;
                                &:hover {
                                    cursor: pointer;
                                }
                                &.selected {
                                    background-color: #efefef;
                                }
                                .mail-type-button__icon {
                                    width: 18px;
                                    height: 18px;
                                }
                                .mail-type-button__text {
                                    display: inline-block;
                                    vertical-align: bottom;
                                    padding-left: 24px;
                                    padding-right: 24px;
                                    font-size: 14px;
                                    line-height: 16px;
                                    font-family: 'Roboto-Medium';
                                }
                            }
                        }
                    }
                    .second-step {
                        border-top: 1px solid #ccc;
                        border-bottom: 1px solid #ccc;
                        height: 410px;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-around;
                        .settings-block {
                            display: flex;
                            justify-content: center;
                            .full-height {
                                height: 100%;
                            }
                            .block-left {
                                width: calc(50% - 10px);
                                margin-right: 20px;
                                margin-bottom: 20px;
                                &-gmail {
                                    width: 80%;
                                }
                                label {
                                    margin-bottom: 0;
                                }
                                .block-left-inputs {
                                    margin-bottom: 20px;
                                    padding: 20px;

                                    border: 1px solid #ccc;
                                    border-radius: 5px;
                                    .email-row,
                                    .password-row {
                                        display: flex;

                                        justify-content: flex-end;
                                        align-items: center;
                                        label {
                                            text-align: right;
                                            letter-spacing: 0.6px;
                                        }
                                        input {
                                            width: 65%;
                                            margin-left: 10px;
                                        }
                                    }
                                    .email-row {
                                        position: relative;

                                        margin-bottom: 20px;
                                        .email {
                                            width: 63%;
                                            margin-left: 20px;
                                        }
                                        &.valid,
                                        &.not-valid,
                                        &.integrated {
                                            &:after {
                                                position: absolute;
                                                top: 12px;
                                                right: 8px;

                                                display: inline-block;
                                                width: 11px;
                                                height: 11px;

                                                content: '';

                                                background-size: cover;
                                            }
                                        }
                                        .incorrect-email,
                                        .integrated {
                                            color: red;
                                            display: none;
                                            position: absolute;
                                            bottom: -19px;
                                            left: 37.3%;
                                        }
                                        &.integrated {
                                            .integrated {
                                                display: block;
                                            }
                                        }
                                        &.not-valid {
                                            .ng-not-empty + .incorrect-email {
                                                display: block;
                                            }
                                        }
                                        &.not-valid,
                                        &.integrated {
                                            input {
                                                color: red;
                                                border-color: red;
                                                background-color: #fee2e2;
                                            }
                                            &:after {
                                                background: url('../../images/sprite/icons/cross-red.svg') no-repeat;
                                            }
                                        }
                                        &.valid {
                                            .incorrect-email,
                                            .integrated {
                                                display: none;
                                            }
                                            input {
                                                color: $theme-green;
                                                border-color: $theme-green;
                                                background-color: #eaffed;
                                            }
                                            &:after {
                                                width: 14px;
                                                height: 14px;

                                                background: url('../../images/sprite/icons/checkmark-green.svg')
                                                    no-repeat;
                                            }
                                        }
                                    }
                                }
                                .config-info {
                                    @include info-block();
                                }
                            }
                            .block-left__gmail-reintegration {
                                height: 350px;
                                box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.12);
                                padding: 20px;
                                .gmail-reintegration-container {
                                    display: flex;
                                    height: 100%;
                                    flex-direction: column;
                                    justify-content: space-between;
                                    .gmail-reintegration__content {
                                        padding-bottom: 13px;
                                        &-item {
                                            width: 47%;
                                            display: flex;
                                            flex-direction: column;
                                            align-items: center;
                                            justify-content: space-between;
                                        }
                                        .gmail-reintegration__text {
                                            font-size: 14px;
                                            color: #666666;
                                            padding-right: 50px;
                                            word-wrap: break-word;
                                            &-bold {
                                                font-weight: 600;
                                            }
                                        }
                                        &-with-img {
                                            display: flex;
                                            height: 100%;
                                            justify-content: space-between;
                                            .gmail-reintegration__img {
                                                background-repeat: no-repeat;
                                                background-size: 100%;
                                                display: flex;
                                                justify-content: space-between;
                                                align-content: center;
                                                cursor: pointer;
                                                height: 100%;
                                                width: 100% !important;
                                                border-radius: 5px;
                                                &-one {
                                                    background-image: url('/images/sprite/integration/gmail-reintegration-step-2.png');
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-reintegration-step-2.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-reintegration-step-2.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-reintegration-step-2.png');
                                                    }
                                                }
                                                &-two {
                                                    background-image: url('/images/sprite/integration/gmail-reintegration-step-3.png');
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-reintegration-step-3.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-reintegration-step-3.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-reintegration-step-3.png');
                                                    }
                                                }
                                                &-three {
                                                    background-image: url('/images/sprite/integration/gmail-reintegration-step-4.png');
                                                    height: 100%;
                                                    border: 1px solid #cccccc;
                                                    border-radius: 5px;
                                                    width: 100% !important;
                                                    margin-bottom: 15px;
                                                    transition: all 0.3s;
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-reintegration-step-4.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-reintegration-step-4.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-reintegration-step-4.png');
                                                    }
                                                }
                                                &-fourth {
                                                    background-image: url('/images/sprite/integration/gmail-reintegration-step-4_1.png');
                                                    height: 100%;
                                                    border: 1px solid #cccccc;
                                                    border-radius: 5px;
                                                    width: 100% !important;
                                                    margin-bottom: 15px;
                                                    transition: all 0.3s;
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-reintegration-step-4_1.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-reintegration-step-4_1.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-reintegration-step-4_1.png');
                                                    }
                                                }
                                                &-five {
                                                    background-image: url('/images/sprite/integration/gmail-reintegration-step-5.png');
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-reintegration-step-5.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-reintegration-step-5.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-reintegration-step-5.png');
                                                    }
                                                }
                                                &-six {
                                                    background-image: url('/images/sprite/integration/gmail-reintegration-step-6.png');
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-reintegration-step-6.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-reintegration-step-6.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-reintegration-step-6.png');
                                                    }
                                                }
                                                &-seven {
                                                    background-image: url('/images/sprite/integration/gmail-reintegration-step-7.png');
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-reintegration-step-7.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-reintegration-step-7.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-reintegration-step-7.png');
                                                    }
                                                }
                                                &-eight {
                                                    background-image: url('/images/sprite/integration/gmail-reintegration-step-8.png');
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-reintegration-step-8.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-reintegration-step-8.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-reintegration-step-8.png');
                                                    }
                                                }
                                                &-active {
                                                    border: 1px solid #0eb953;
                                                    box-shadow: 0px 1px 10px rgba(59, 89, 152, 0.18);
                                                }
                                                &-icon {
                                                    cursor: pointer;
                                                    height: 30px;
                                                }

                                                &-corp-three {
                                                    background-image: url('/images/sprite/integration/gmail-corp-integration-step-3.png');
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-corp-integration-step-3.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-corp-integration-step-3.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-corp-integration-step-3.png');
                                                    }
                                                }
                                                &-corp-four {
                                                    background-image: url('/images/sprite/integration/gmail-corp-integration-step-4.png');
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-corp-integration-step-4.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-corp-integration-step-4.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-corp-integration-step-4.png');
                                                    }
                                                }
                                                &-corp-five {
                                                    background-image: url('/images/sprite/integration/gmail-corp-integration-step-5.png');
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-corp-integration-step-5.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-corp-integration-step-5.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-corp-integration-step-5.png');
                                                    }
                                                }
                                                &-corp-six {
                                                    background-image: url('/images/sprite/integration/gmail-corp-integration-step-6.png');
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-corp-integration-step-6.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-corp-integration-step-6.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-corp-integration-step-6.png');
                                                    }
                                                }
                                                &-corp-seven {
                                                    background-image: url('/images/sprite/integration/gmail-corp-integration-step-7.png');
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-corp-integration-step-7.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-corp-integration-step-7.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-corp-integration-step-7.png');
                                                    }
                                                }
                                                &-corp-eight {
                                                    background-image: url('/images/sprite/integration/gmail-corp-integration-step-8.png');
                                                    &-ru {
                                                        background-image: url('/images/sprite/integration/ru/gmail-corp-integration-step-8.png');
                                                    }
                                                    &-ua {
                                                        background-image: url('/images/sprite/integration/ua/gmail-corp-integration-step-8.png');
                                                    }
                                                    &-pl {
                                                        background-image: url('/images/sprite/integration/pl/gmail-corp-integration-step-8.png');
                                                    }
                                                }
                                            }
                                            p,
                                            .gmail-reintegration__img {
                                                width: 50%;
                                            }
                                            &-one {
                                                p {
                                                    width: 40%;
                                                }
                                                .gmail-reintegration__img {
                                                    width: 60%;
                                                }
                                            }
                                        }
                                    }
                                    .gmail-reintegration__buttons {
                                        display: flex;
                                        justify-content: space-between;
                                        &-one {
                                            justify-content: flex-end;
                                        }
                                        .btn {
                                            max-width: 154px;
                                        }
                                    }

                                    .gmail-types-items {
                                        margin: 0 auto;
                                        .gmail-types-item {
                                            padding: 20px;
                                            border: 2px dashed #cccccc;
                                            border-radius: 20px;
                                            margin-bottom: 20px;
                                            transition: all 0.3s;
                                            &.isSelected {
                                                border-color: #47ab43;
                                            }

                                            &.custom-radio-container {
                                                position: relative;
                                                .custom-radio {
                                                    top: unset;
                                                }
                                            }

                                            label {
                                                padding-left: 10px;
                                                cursor: pointer;
                                                position: relative;
                                                top: 2px;
                                            }
                                        }
                                    }
                                    .instruction-block {
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        &__btn {
                                            margin-left: 30px;
                                        }
                                    }
                                }
                            }
                            .block-right {
                                width: calc(50% - 10px);
                                margin-bottom: 20px;
                                padding: 20px;

                                border: 1px solid #ccc;
                                border-radius: 5px;
                                label {
                                    font-weight: normal;

                                    position: relative;

                                    margin-bottom: 0;
                                    &.bold {
                                    }
                                }
                                .settings-row {
                                    display: flex;

                                    input {
                                        margin: 10px 20px 20px 0;
                                    }
                                    & > .imap,
                                    & > .smtp {
                                        position: relative;
                                        .inp-title {
                                            letter-spacing: 0.6px;
                                            .info-icon {
                                                top: -5px;
                                                right: -15px;
                                            }
                                        }
                                    }
                                    & > .imap {
                                        width: 59%;
                                        input {
                                            width: 70%;
                                        }
                                    }
                                    & > .smtp {
                                        width: 41%;
                                        input {
                                            width: 100%;
                                        }
                                    }
                                    .switch {
                                        display: flex;
                                        height: 19px;

                                        align-items: center;
                                    }
                                }
                                .host-row {
                                    .imap {
                                        .info-icon {
                                            right: 28.5%;
                                        }
                                    }
                                    .smtp {
                                        .info-icon {
                                            right: -2%;
                                        }
                                    }
                                    .info-icon {
                                        top: 25px;
                                    }
                                }
                                .port-row {
                                    .info-icon {
                                        top: 25px;
                                    }
                                    input[type='number']::-webkit-inner-spin-button,
                                    input[type='number']::-webkit-outer-spin-button {
                                        -webkit-appearance: none;
                                        -moz-appearance: none;
                                        appearance: none;
                                        margin: 0;
                                    }
                                    .imap {
                                        input {
                                            width: 59%;
                                        }
                                        .info-icon {
                                            right: 39.3%;
                                        }
                                    }
                                    .smtp {
                                        input {
                                            width: 84%;
                                        }
                                        .info-icon {
                                            right: 14%;
                                        }
                                    }
                                }
                                .security-row {
                                    .info-icon {
                                        top: -6px;
                                        right: -16px;
                                    }
                                }
                                input.cmn-toggle-round + label {
                                    width: 30px !important;
                                    height: 12px !important;
                                    margin: 0 10px;
                                    &:before {
                                        background-color: #afafaf;
                                    }
                                    &:after {
                                        width: 18px;
                                        height: 16px;
                                        top: -3px;
                                        left: -3px;
                                    }
                                }
                                input.cmn-toggle-round:checked + label {
                                    &:before {
                                        background: #00b549;
                                    }
                                    &:after {
                                        margin-left: 15px;
                                    }
                                }
                                &.exchange {
                                    .settings-row {
                                        position: relative;

                                        flex-wrap: wrap;
                                        .info-icon {
                                            top: 20px;
                                            right: -1%;
                                        }
                                        input {
                                            margin: 5px 0 20px 0;
                                        }
                                        &.domain-row {
                                            input {
                                                margin-bottom: 15px;
                                            }
                                        }
                                        &.version-row {
                                            custom-select-new {
                                                border-radius: 5px;
                                                &.not-valid {
                                                    border: 1px solid red;
                                                }
                                            }
                                        }
                                    }
                                    custom-select-new-simple {
                                        width: 50%;
                                    }
                                    .select-label.custom-new {
                                        width: 100%;
                                    }
                                    .select-ops.custom-new {
                                        max-height: 100px;
                                        width: 100%;
                                    }
                                    .inp-title {
                                    }
                                    .version-row {
                                        &.not-selected {
                                            .select-label.custom-new .select-label-text {
                                            }
                                        }
                                    }
                                }
                            }
                            .info-icon {
                                top: 6px;
                                right: 6px;
                            }
                            input {
                                &.not-valid {
                                    color: red;
                                    border-color: red;
                                    background-color: #fee2e2;
                                }
                            }
                        }
                    }
                    .third-step {
                        .status-main-block {
                            width: 50%;
                            margin: 0 auto;

                            letter-spacing: 0.6px;
                            .status-block-top {
                                margin-bottom: 20px;
                                padding: 20px;

                                border: 1px solid #ccc;
                                border-radius: 5px;
                                .status-field {
                                    display: flex;
                                    width: 70%;
                                    margin: 0 auto;
                                    padding: 8px;

                                    border: 1px solid #e42516;
                                    border-radius: 5px;

                                    justify-content: space-between;
                                    align-items: center;
                                    .status-text {
                                        line-height: 14px;
                                    }
                                    &.status-ok {
                                        border-color: $theme-green;
                                        .status-icon {
                                            border-color: $theme-green;
                                            &:after {
                                                width: 16px;
                                                height: 12px;
                                                background-image: url('../../images/sprite/icons/checkmark-green.svg');
                                            }
                                        }
                                    }
                                    .status-icon {
                                        position: relative;

                                        width: 30px;
                                        height: 20px;

                                        border-left: 1px solid #e42516;
                                        &:after {
                                            position: absolute;
                                            top: 4px;
                                            left: 12px;

                                            display: inline-block;
                                            width: 11px;
                                            height: 13px;

                                            content: '';

                                            background: url('../../images/sprite/icons/cross-red.svg') no-repeat;
                                            background-size: cover;
                                        }
                                    }
                                    &.imap_pop3-status {
                                        margin-bottom: 20px;
                                    }
                                }
                            }
                            .status-block-info {
                                padding: 20px;

                                border-left: 3px solid $theme-green;
                                background-color: #e3f2dd;
                            }
                        }
                    }
                    .step-buttons {
                        margin: 20px 0;
                        text-align: center;
                        .btn-grey {
                            @include button-animated($theme-btn-back, #333, $theme-btn-back-hover, white);
                            border: 1px solid #333;
                            width: 210px;
                        }
                        .btn-green {
                            @include button-animated($theme-btn-green, white, $theme-btn-green-hover, white);
                            margin-left: 35px;
                            width: 210px;
                        }
                    }
                }
            }
        }
        .new-integration-backdrop {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;

            opacity: 0.5;
            background-color: black;
        }
        &.edit-settings-popup {
            .header {
                padding: 0;
            }
            .new-integration-block .integration-main-block {
                .step-view {
                    .second-step {
                        padding-top: 20px;

                        border-top: none;
                    }
                }
            }
        }
    }
    //-------------------------------------------------------
    .mCS-dark .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
        width: 4px;

        background-color: #666 !important;
    }
    .mCSB_inside > .mCSB_container {
        padding: 0;
    }
}

.integration-input {
    box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.12);
    border-radius: 5px;
    input.cmn-toggle-round + label {
        width: 30px !important;
        height: 12px !important;
        margin: 0 10px;
        &:before {
            background-color: #afafaf;
        }
        &:after {
            width: 18px;
            height: 16px;
            top: -3px;
            left: -3px;
        }
    }
    input.cmn-toggle-round:checked + label {
        &:before {
            background: #00b549;
        }
        &:after {
            margin-left: 15px;
        }
    }
}

.enlarge-image-modal-wrapper {
    .modal-dialog {
        width: auto;
        .modal-content {
            margin: 0 auto;
            width: 85%;
            .enlarge-image {
                position: relative;
                &__close-icon {
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    background-color: rgba(81, 80, 80, 0.5215686274509804);
                    padding: 7px;
                    border-radius: 5px;
                }
                &__img {
                    width: 100%;
                }
            }
        }
    }
}

.blurBackground {
    height: 100%;
}
.gmail-reintegration-container {
    .custom-radio + .checkmark:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 19px;
        height: 19px;
        border: 2px solid #a0aab8;
        border-radius: 50%;
        background: #fff;
    }
    .custom-radio + .checkmark:after {
        content: '';
        position: absolute;
        top: 4px;
        left: 4px;
        width: 11px;
        height: 11px;
        border-radius: 50%;
        background: #47ab43;
        opacity: 0;
        transition: 0.2s;
    }
}
.container-img-blur-block {
    transition: 0.5s ease;
    opacity: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #0e0e0e;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
}
.container-img:hover .gmail-reintegration__img {
    opacity: 0.3;
    cursor: pointer;
}

.container-img:hover .container-img-blur-block {
    opacity: 0.5;
}
.container-img {
    position: relative;
    width: 100%;
    height: 80%;
}

.container-img.instruction-video {
    width: 330px;
    display: block;
    margin: 0 auto;
    cursor: pointer;
    .container-img-blur-block img {
        width: 35px;
    }
}

@media (max-width: 430px) {
    .block-integration-with-email
        .new-integration
        .new-integration-block
        .integration-main-block
        .step-view
        .step-buttons {
        display: flex;
        flex-direction: column;
        align-items: center;
        button:first-child {
            margin-bottom: 10px;
        }
        button:last-child {
            margin-left: 0 !important;
        }
    }
}

@media (max-width: 575px) {
    .new-integration-block {
        .line-small {
            display: none;
        }
        .status_bar {
            justify-content: center;
        }
        .status_bar .steps {
            flex-direction: column;
            align-items: flex-start !important;
            width: max-content !important;
            .line-central {
                flex-basis: 15px !important;
                margin: 0 14px !important;
            }
        }
        .integration-main-block {
            .first-step {
                height: auto !important;
                .mailbox-types {
                    width: 100% !important;
                }
            }
            .second-step {
                height: 290px !important;
                .settings-block {
                    overflow: scroll;
                    flex-direction: column;
                    .block-left__gmail-reintegration {
                        margin-top: 130px !important;
                        .gmail-reintegration-container {
                            justify-content: flex-start !important;
                            .gmail-reintegration__content.gmail-reintegration__content-with-img {
                                flex-direction: column;
                            }
                            .instruction-block {
                                flex-direction: column;
                                .pre-gmail {
                                    width: 300px;
                                    height: 100px;
                                }
                                .instruction-block__btn {
                                    margin: 10px 0;
                                }
                            }
                            .gmail-reintegration__content-with-img {
                                justify-content: space-evenly !important;
                            }
                            //.gmail-reintegration__text.other-text {
                            //    margin-top: 180px !important;
                            //}
                            .gmail-reintegration__text {
                                width: 100% !important;
                                .container-img-blur-block {
                                    top: 20% !important;
                                }
                            }
                            .gmail-reintegration__content-with-img-one {
                                flex-direction: column;
                                .container-img-blur-block {
                                    top: 20% !important;
                                    height: 40% !important;
                                }
                            }
                        }
                    }
                    .block-left {
                        width: 100% !important;
                        margin-right: 0 !important;
                        .other,
                        .exchange,
                        .outlook {
                            margin-top: 315px;
                            margin-right: 10px;
                        }
                        .gmail {
                            margin-top: 230px !important;
                            margin-right: 10px;
                        }
                    }
                    .block-right {
                        width: 98% !important;
                    }
                }
            }
            .third-step {
                .status-main-block {
                    width: 100% !important;
                    overflow: scroll;
                    height: 180px;
                }
            }
        }
    }

    .block-integration-with-email
        .new-integration
        .new-integration-block
        .integration-main-block
        .step-view
        .second-step
        .settings-block
        .block-left
        .block-left-inputs
        .email-row
        input {
        font-size: 14px !important;
    }

    .block-integration-with-email .integrated-block .central-block .main-tails-row .mb-settings-tile {
        min-width: 350px;
    }
    .block-integration-with-email
        .integrated-block
        .central-block
        .main-tails-row
        .mbList-tile
        .mbList-block
        .mblist-block-list
        .mbList-mailbox__container
        .mbList-mailbox__email
        .gm-no-integration-mr {
        margin-right: 33px;
    }
    .emailIntegration-edit-block .main-block .block-buttons .cancel {
        min-width: 140px !important;
        width: 50px;
    }
    .emailIntegration-edit-block .main-block .block-buttons .accept {
        min-width: 140px !important;
        width: 50px;
    }
    .block-buttons.text-center {
        display: flex;
        justify-content: center;
    }
    .tooltip-outer.personal-mailing {
        width: 220px;
        left: -120px !important;
        top: 20px !important;
    }
    .block-integration-with-email .integrated-block .central-block .main-tails-row .mbList-tile .switch-column {
        margin-left: 15px;
    }
    .block-integration-with-email
        .integrated-block
        .central-block
        .main-tails-row
        .mbList-tile
        .mbList-header
        .mbList-title.switch-column {
        margin-left: 20px;
    }
}

.gmail-video-wrapper {
    margin: 0 !important;
    padding: 0 !important;
    .modal-body {
        padding: 0 !important;
        margin: 0 !important;
        .gmail-video-body {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }
}
