@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

#calendar-page {
    display: flex;
    justify-content: center;
    padding: var(--main-gap);

    p,
    ul,
    hr {
        padding: 0;
        margin: 0;
    }

    hr {
        border-color: $border-grey;
    }

    .integration-section {
        display: flex;
        flex-direction: column;
        gap: var(--main-gap);
        justify-content: center;
        max-width: 65%;

        @media (max-width: 1300px) {
            max-width: 90%;
        }
    }

    .integration-head {
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
        justify-content: center;

        &__header {
            font-size: $heading-font-size;
        }

        &__buttons {
            display: flex;
            gap: 8px;
            align-items: center;
        }
    }

    .integration-button {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: center;
        height: 42px;
        padding: 0 16px;
        background-color: $main-white;
        transition: all 0.2s linear;
        @include border-small;

        &:hover {
            border-color: $dark-grey;
        }

        &__icon {
            width: 20px;
            aspect-ratio: 1/1;

            &_size-outlook {
                width: 23px;
            }
        }
    }

    .integration-block {
        display: flex;
        flex-direction: column;
        gap: 16px;
        padding: 16px;
        color: $secondary-black;
        background-color: $main-white;
        @include border-large;

        &__title {
            font-size: $heading-small-font-size;
            font-weight: 500;
            color: $main-black;
        }
    }

    .advantages-list {
        list-style: none;

        li:before {
            padding: 4pt;
            font-size: 10pt;
            content: '•';
        }
    }

    .calendar-container {
        display: flex;
        gap: var(--main-gap);
        width: 100%;
    }

    .calendar,
    .calendar-settings {
        height: fit-content;
        padding: 16px;
        color: $secondary-black;
        background-color: $main-white;
        @include border-large;
    }

    .calendar {
        width: 75%;
    }

    .right-block {
        display: flex;
        flex-direction: column;
        gap: var(--main-gap);
        width: 25%;
    }

    .calendar-settings {
        display: flex;
        flex-direction: column;
        gap: 16px;

        &__buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        &__button {
            width: 100%;
        }
    }

    .switcher-wrapper {
        display: flex;
        gap: 8px;
        align-items: center;

        &_outlook {
            margin: 0 auto;
        }
    }

    .calendar-item {
        display: flex;
        gap: 8px;

        &_disabled-text {
            pointer-events: none;
            opacity: 0.5;
        }
    }

    .wrap-balance {
        text-wrap: balance;

        i {
            vertical-align: sub;
        }
    }

    .inline-hint-icon {
        display: inline;
        font-size: 20px;
        line-height: 0;
        color: transparent;
        white-space: nowrap;
        vertical-align: middle;
        cursor: pointer;
    }

    .outlook-block {
        &__buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
        }
    }
}
