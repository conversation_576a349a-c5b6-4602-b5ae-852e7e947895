@import 'js/common/styles/global-variables.module';

.block-vacancies {
    .vacancy-table-redesign {
        margin: 0 0 auto 0;
        overflow: auto;
        border-top: 1px solid $border-grey;

        .row {
            padding: 0;

            .table {
                margin: 0;

                font-family: $main-font-family;
                font-size: $main-font-size;

                thead {
                    position: sticky;
                    top: 0;
                    z-index: 2;
                    box-shadow: $main-box-shadow;

                    th {
                        text-align: left;
                        border-right: none;
                        border-bottom: 1px solid $border-grey;
                    }
                }

                tbody {
                    tr:not(:last-child) {
                        td {
                            border-bottom: 1px solid $border-grey;
                        }
                    }

                    td:last-child {
                        border-right: none;
                    }

                    .vacancy-name-link {
                        color: $main-black;
                        text-decoration: none;
                        transition: color 0.1s ease;

                        &:hover {
                            color: $main-green;
                        }
                    }

                    .responsibles {
                        display: flex;
                        gap: 8px;
                        align-items: center;
                    }

                    .rest {
                        color: $semi-black;
                    }

                    .status {
                        width: 185px;
                        padding: 0;

                        &_en-width {
                            width: 185px;
                        }

                        &_ua-width {
                            width: 167px;
                        }

                        &_pl-width {
                            width: 142px;
                        }

                        &_ru-width {
                            width: 172px;
                        }
                    }
                }

                .multiple-resp-popover {
                    padding: 0;
                    border: 1px solid $border-grey;
                    border-radius: 4px;
                    box-shadow: $main-box-shadow;

                    .responsible-person-component {
                        display: block;
                        width: 36px;
                    }

                    .popover-content {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 8px;
                        align-items: center;
                        min-width: 36px;
                        max-width: 300px;
                        max-height: 187px;
                        padding: 8px;
                        overflow: auto;
                    }

                    .arrow {
                        display: none;
                    }

                    &__item {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        text-align: center;

                        &_bold-name {
                            font-weight: 600;
                        }
                    }
                }

                .skills-list {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                    align-items: center;
                    width: fit-content;

                    &_item {
                        padding: 4px 8px;
                        background-color: $pale-grey;
                        border-radius: 4px;
                    }

                    &__rest-skills-button {
                        transition: background-color 100ms ease;

                        &:hover {
                            background-color: $border-grey;
                            outline: 1px solid $dark-grey;
                        }
                    }
                }
            }
        }
    }

    .client-link {
        max-width: 90px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        @include link-green;

        @media screen and (min-width: 1440px) {
            max-width: 140px;
        }
    }
}
