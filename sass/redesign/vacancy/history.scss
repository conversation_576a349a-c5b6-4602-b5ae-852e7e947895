@import 'js/common/styles/global-variables.module';
@import 'sass/mixins';

.block-vacancy,
.candidate-page-wrapper {
    .history-outer {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin: var(--main-gap);
        overflow: auto;
        background: $main-white;
        border: 1px solid $border-grey;
        border-radius: 12px;

        &__header {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            border-bottom: 1px solid $border-grey;

            &-title {
                font-size: $heading-small-font-size;
                font-weight: 500;
                line-height: 26px;
                color: $main-black;
                letter-spacing: 0.36px;
            }

            &-btn {
                display: flex;
                gap: 10px;
                align-items: center;

                select-single {
                    width: 300px;
                }

                img {
                    width: 42px;
                }
            }
        }

        &__body {
            display: flex;
            flex-direction: column;
            padding-bottom: 8px;
            overflow: auto;

            &-items {
                position: relative;

                &.attachedComment {
                    .action-wrapper {
                        padding: 8px 16px 8px 47px;
                        margin: 16px;
                        border: 1px solid $main-green;
                        border-radius: 8px;

                        .action-buttons-wrapper {
                            top: 25px;
                            right: 10px;
                        }

                        .pin-message {
                            position: absolute;
                            top: 50%;
                            left: 40px;
                            width: 18px;
                            transform: translate(-50%, -50%);

                            img {
                                filter: $filter-main-green;
                            }
                        }

                        .col-lg-10 {
                            padding-right: 90px;
                        }
                    }
                }

                .action-wrapper {
                    display: flex;
                    flex-direction: column;
                    padding: 8px 16px;
                    color: $secondary-black;

                    .user-removed {
                        color: $red;
                    }

                    .col-lg-10 {
                        width: 100%;
                        padding-right: 45px;
                        padding-left: 0;
                    }

                    &:hover {
                        background-color: $pale-grey;
                    }

                    .manyCandidates {
                        & > .boldTxt {
                            @include bold();

                            .blueLink {
                                color: $main-green;
                                cursor: pointer;
                            }
                        }

                        .blueLink {
                            color: $main-green;
                            cursor: pointer;
                        }

                        .menuCandidate {
                            display: block;
                        }
                    }

                    .interview_add {
                        cursor: auto;

                        .boldTxt {
                            cursor: pointer;
                            @include bold();

                            .blueLink {
                                color: $blue;
                                cursor: pointer;
                            }
                        }

                        .blueLink {
                            color: $blue;
                            cursor: pointer;
                        }
                    }

                    .time {
                        @include bold();
                    }

                    .task {
                        @include bold();
                        color: #ffc100;
                    }

                    .status {
                        @include history-label-styling();
                    }

                    .stage {
                        @include stages-labels-styling();
                    }

                    a {
                        font-weight: 400;
                        line-height: 20px;
                        color: $main-green;
                        letter-spacing: 0.32px;

                        &:hover {
                            color: $yellow;
                        }

                        &:not(.btn) {
                            display: inline-block;
                            font-weight: 400;
                            line-height: 20px;
                            color: $main-green;
                            letter-spacing: 0.32px;

                            &:hover {
                                color: $yellow;
                            }
                        }
                    }

                    div:not(.action-personal-wrapper, .clearfix) {
                        margin-bottom: 4px;
                    }
                }
            }

            &-divider {
                height: 5px;
                margin: 16px 16px 12px 16px;
                overflow: visible;
                font-size: $secondary-font-size;
                color: $semi-black;
                text-align: center;
                border: none;
                border-top: 1px solid $border-grey;

                &.last {
                    border-top: 1px solid transparent;
                }

                &:after {
                    position: relative;
                    top: -10px;
                    padding: 0 16px;
                    content: attr(data-content);
                    background: $main-white;
                }
            }
        }

        &__footer {
            border-top: 1px solid $border-grey;
        }
    }
}
