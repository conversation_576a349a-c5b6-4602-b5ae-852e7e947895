@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

#send-candidate-to-client-email {
    display: flex;
    flex-direction: column;
    gap: var(--main-gap);
    padding: var(--main-gap);

    p,
    label {
        margin: 0;
    }

    label {
        font-weight: normal;
    }

    .link {
        font-size: $main-font-size;
        color: $main-green;

        &:hover {
            color: $yellow;
        }
    }

    .secondary-font-size {
        font-size: $secondary-font-size;
    }

    .page-head {
        h3 {
            margin: 0;
            font-size: $heading-font-size;
        }

        &__details {
            margin: 8px 0 0 0;
            color: $semi-black;
        }
    }

    .page-content {
        display: grid;
        grid-template-columns: minmax(220px, 25%) 1fr;
        gap: var(--main-gap);

        @media screen and (max-width: $tablet-width) {
            grid-template-columns: 1fr;
        }

        &__left-side {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        &__right-side {
            display: flex;
            flex-direction: column;
            gap: 16px;
            height: fit-content;
            padding: 16px;
            background-color: $main-white;
            @include border-large;
        }
    }

    .stages-section {
        display: flex;
        flex-direction: column;
        height: fit-content;
        overflow: hidden;
        background-color: $main-white;
        @include border-large;

        &__header {
            display: flex;
            justify-content: space-between;
            padding: 10px 16px;
            font-size: $heading-font-size;
            font-weight: 500;
            border-bottom: 1px solid $border-grey;
        }

        &__stages-list {
            max-height: 320px;
            overflow-y: auto;

            &_declined-list {
                border-top: 1px solid $border-grey;
            }
        }

        &__count {
            margin-right: 0;
            margin-left: auto;
            color: $semi-black;
        }

        &__stage-item {
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: flex-start;
            padding: 10px 16px;
            transition: background-color 0.2s ease;

            &:hover {
                background-color: $pale-grey;
            }

            &_active {
                color: $main-green;
                background-color: $light-green;

                .stages-section__count {
                    color: $main-green;
                }
            }
        }

        &__stage-name {
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .candidates-section {
        display: flex;
        flex-direction: column;
        background-color: $main-white;
        @include border-large;

        &_error {
            border-color: $red;
        }

        &__header {
            display: flex;
            justify-content: space-between;
            padding: 16px;
            font-size: $heading-font-size;
            font-weight: 500;
            border-bottom: 1px solid $border-grey;
        }

        &__list {
            display: flex;
            flex-direction: column;
            gap: 16px;
            max-height: 310px;
            padding: 16px;
            overflow: auto;
        }

        &__item {
            display: flex;
            gap: 8px;
        }
    }

    .field-block {
        flex: 1 1 100%;

        &_hidden {
            opacity: 0;
        }

        &__title {
            margin-bottom: 8px;
            font-size: $secondary-font-size;
            line-height: normal;
            color: $secondary-black;
        }

        &__description-bottom {
            margin-top: 8px;
            font-size: $secondary-font-size;
            color: $secondary-black;
        }
    }

    .error-text {
        font-size: $secondary-font-size;
        color: $red;
    }

    .mail-buttons {
        display: flex;
        gap: 8px;
    }

    .attach-file {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: fit-content;
        margin-top: 16px;

        &__label-group {
            display: flex;
            gap: 8px;
            align-items: center;
            width: fit-content;
            font-size: $heading-small-font-size;
            cursor: pointer;
        }

        &__container {
            max-width: 200px;
        }

        &__item {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        &__item-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .attach-icon {
        @include control-icon-size;
        filter: $filter-semi-black;
    }

    .delete-icon {
        flex-shrink: 0;
        @include control-icon-size;

        &__close {
            @include icon-mask('/images/redesign/svg-icons/close.svg', $semi-black);

            &:hover {
                background-color: $secondary-black;
            }
        }
    }
}
