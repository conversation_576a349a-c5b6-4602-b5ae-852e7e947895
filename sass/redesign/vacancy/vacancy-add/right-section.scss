@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.salary-block {
    display: flex;
    gap: 8px;

    &__input-group {
        display: flex;
        gap: 5px;
        align-items: center;
        height: 42px;
        padding: 0 12px;
        background-color: $main-white;
        @include border-small;

        &_error {
            border-color: $red;
        }
    }

    &__label {
        color: $semi-black;
        cursor: default;
    }

    &__input {
        width: 100%;
        padding: 0;
        border: none;
    }

    &__vertical-divider {
        height: 50%;
        margin: 0 5px;
        border-left: 1px solid $border-grey;
    }

    &__currency-select {
        min-width: 95px;
    }

    &__hide-salary {
        display: flex;
        gap: 8px;
        cursor: pointer;
    }

    &__hide-salary-text,
    &__hide-salary-end {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }
}
