@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.vacancies-search-component-wrapper {
    z-index: 3;
}

.vacancies-search-component {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    justify-content: flex-end;
    padding: 16px;
    font-family: $main-font-family;
    font-size: $main-font-size;
    color: $main-black;
    background-color: $main-white;
    border-bottom: 1px solid $border-grey;

    .search-button {
        gap: 4px;
        padding: 0 8px;
        white-space: nowrap;
    }

    &_left-side {
        display: flex;
        flex: 1 1 400px;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
        max-width: 650px;
        min-height: 42px;
        padding: 8px;
        border: 1px solid $border-grey;
        border-radius: 8px;

        .switcher-wrapper {
            display: flex;
            flex-shrink: 0;
            gap: 8px;
        }

        .switcher-hint {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .vacancies-switcher-label {
            margin: 0;
            font-weight: normal;
            line-height: normal;
        }

        .search-type-toggle {
            display: flex;
            align-items: center;
        }

        .vacancies-search-vertical-divider {
            min-height: 24px;
            border-left: 1px solid $border-grey;
        }

        .vacancies-search-input-wrapper {
            display: flex;
            flex: 1 1 160px;
            align-items: center;

            &:hover {
                .clear-input-icon {
                    pointer-events: initial;
                    opacity: 1;
                }
            }
        }

        .vacancies-search-input {
            width: 100%;
            margin-left: 4px;
            border: none;
        }

        .search-icon {
            @include control-icon-size;
            cursor: pointer;

            &__search {
                display: block;
                transition: all 0.3s ease-in;
                @include icon-mask('/images/redesign/svg-icons/search.svg', $semi-black);

                &:hover {
                    background-color: $yellow;
                }
            }
        }

        .clear-input-icon-wrapper {
            @include control-icon-size;
            display: flex;
            align-items: center;
            margin-right: 5px;
        }

        .clear-input-icon {
            @include control-icon-size;
            pointer-events: none;
            filter: $filter-semi-black;
            opacity: 0;

            &:hover {
                filter: $filter-main-black;
            }
        }
    }

    &_advanced-search {
        flex: 1 1 auto;
        min-height: auto;
    }

    .vacancies-search-total-found {
        margin-right: auto;
        white-space: nowrap;
    }

    &_right-side {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
    }
}

.hint-info-icon__grey {
    display: inline-block;
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    background: url('/images/redesign/svg-icons/info.svg') no-repeat;
    filter: invert(92%) sepia(5%) saturate(16%) hue-rotate(316deg) brightness(90%) contrast(84%);
}
