@import 'js/common/styles/global-variables.module';

.candidates-advanced-search {
    position: absolute;
    inset: 0;
    z-index: 20;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 80%;
    height: 75vh;
    margin: auto;
    overflow: hidden;
    font-family: $main-font-family;
    font-size: $main-font-size;
    background-color: $main-white;
    border-radius: 12px;

    @media screen and (max-width: $laptop-width) {
        width: 98%;
        height: 98%;
    }

    #filters-section {
        border-bottom: none;
    }

    .advanced-search-head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border-bottom: 1px solid $border-grey;

        .search-title {
            margin: 0;
            font-size: $heading-font-size;
            font-weight: 500;
            line-height: normal;
        }

        .adv-search-close-icon {
            width: 20px;
            filter: $filter-semi-black;
        }
    }

    .advanced-search-body {
        display: flex;
        flex-direction: column;
        padding: 16px;
        overflow: auto;

        @import 'sass/redesign/advanced-search/body';
    }

    .advanced-search-footer {
        display: flex;
        gap: 16px;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border-top: 1px solid $border-grey;

        @media screen and (max-width: $laptop-width) {
            flex-direction: column;
        }

        #filters-section {
            padding: 0;
        }

        &_control {
            display: flex;
            flex-shrink: 0;
            gap: 12px;
            align-items: center;
            margin-right: 0;
            margin-left: auto;
        }

        .remove-all-button {
            display: flex;
            gap: 8px;
            align-items: center;
            padding: 4px 8px;
            line-height: 1em;
            color: $main-black;
            background-color: $pale-grey;
            border-radius: 4px;

            .remove-all-icon {
                width: 16px;
                filter: invert(8%) sepia(8%) saturate(1063%) hue-rotate(175deg) brightness(96%) contrast(91%);
            }
        }
    }

    .remove-filter {
        display: flex;
        align-items: center;

        &__icon {
            width: 16px;
            height: 16px;
        }
    }
}
