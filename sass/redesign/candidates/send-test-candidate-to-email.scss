@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.sent-test-email {
    padding: 0 var(--main-gap);
    margin-top: var(--main-gap);
    margin-right: auto;
    margin-left: auto;

    .main-page-title {
        display: flex;
        align-items: center;
        align-self: center;
        height: 42px;
        padding: 0;
        margin: 0;
        font-size: $heading-font-size;
        font-weight: 500;
        line-height: 1.25;
    }

    .send-test-from-vacancy-title {
        height: initial;
        font-size: $main-font-size;
        font-weight: normal;
        color: $semi-black;

        a {
            @include link-green;
        }
    }

    .candidates-list {
        display: flex;
        flex-direction: column;
        gap: 8px;

        &__item {
            display: flex;
            gap: 8px;
        }

        &__item-checkbox {
            height: 20px;
        }

        &__danger-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            filter: $filter-main-red;
        }
    }

    .candidate-item-link {
        @include link-green;
    }

    .header-title {
        margin-bottom: var(--main-gap);
        text-align: center;
    }

    .email-block {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .buttons-row {
        display: flex;
        gap: 16px;
        align-items: center;
    }

    .align-start {
        align-self: flex-start;
        text-align: start;
    }

    .align-end {
        text-align: end;
    }

    .block-for-flex {
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    .email-wrapper {
        display: flex;
        gap: 50px;
        align-items: center;
        justify-content: flex-start;
    }

    .select-candidate {
        display: flex;
        gap: var(--main-gap);

        .select-candidate-wrapper {
            min-width: 500px;
            height: fit-content;
            padding: 0;
            background-color: $main-white;
            border: 1px solid $border-grey;
            border-radius: 12px;

            .title-block {
                padding: 16px;
            }

            .input-title {
                display: block;
                margin-bottom: 8px;
                font-size: $secondary-font-size;
                font-weight: 400;
                line-height: 1.33;
                color: $secondary-black;
            }

            .autoSelect {
                margin-bottom: 16px;

                .select2-search-choice {
                    position: relative;

                    height: 100%;
                    padding: 4px 32px 4px 8px;
                    margin: 0 4px 0 0;
                    font-size: $main-font-size;
                    line-height: 1.5;

                    background-color: $pale-grey;
                    border: none;
                    border-radius: 4px;

                    .select2-search-choice-close {
                        display: none;
                    }

                    .select2-search-choice-remove {
                        position: absolute;
                        top: 50%;
                        right: 8px;
                        display: block;
                        width: 20px;
                        height: 20px;
                        background-image: url('/images/redesign/svg-icons/close.svg');
                        background-size: contain;
                        transform: translateY(-50%);
                    }
                }

                .select2-drop {
                    border: 1px solid $border-grey;
                }

                .select2-result-label {
                    padding: 4px;
                }

                &.hide-icon {
                    .fa.fa-pencil {
                        display: none;
                    }
                }
            }

            .email {
                padding: 16px;

                .item {
                    &.mail-checkbox {
                        font-size: $main-font-size;
                        line-height: 1.5;

                        .checkbox {
                            display: inline;
                            margin-right: 15px;
                        }

                        label {
                            font-weight: 400;

                            a {
                                color: $main-green;
                                transition: all 0.3s ease-in;

                                &:hover,
                                &:focus {
                                    color: $yellow;
                                }
                            }
                        }
                    }

                    input[type='radio'] {
                        margin: 0 10px 0 0;
                    }

                    input {
                        margin-right: 5px;
                    }
                }

                .alert-danger {
                    min-height: 42px;
                    margin-bottom: 0;

                    a {
                        color: $main-green;
                        transition: all 0.3s ease-in;

                        &:hover {
                            color: $yellow;
                        }
                    }
                }
            }

            .padding {
                padding: 0;
            }

            #mCSB_2 {
                border-radius: 5px;
            }

            .testCandidateBlock {
                padding: 16px;
                overflow: auto;

                &:last-child {
                    padding-bottom: 16px;
                    border-bottom-right-radius: 12px;
                    border-bottom-left-radius: 12px;
                }

                .candidateString {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 48px;
                    padding: 12px;
                    background-color: $background-grey;
                    border: 1px solid $border-grey;
                    border-radius: 8px;

                    &:not(:last-child) {
                        margin-bottom: 8px;
                    }

                    .wrapper {
                        display: flex;
                        gap: 8px;
                        align-items: center;
                        padding: 0 !important;
                    }

                    .icon-wrapper {
                        display: flex;
                        gap: 10px;
                        align-items: center;
                    }

                    .icon {
                        display: block;
                        margin: 0;
                        cursor: pointer;
                        transition: all 0.3s ease-in;
                        @include control-icon-size;

                        &:hover,
                        &:focus {
                            background-color: $secondary-black;
                        }

                        &__person {
                            @include icon-mask('/images/redesign/svg-icons/person.svg', $semi-black);
                        }

                        &__pencil {
                            @include icon-mask('/images/redesign/svg-icons/pencil.svg', $semi-black);
                        }

                        &__trash {
                            @include icon-mask('/images/redesign/svg-icons/trash.svg', $semi-black);
                        }

                        &__mail {
                            @include icon-mask('/images/redesign/svg-icons/mail.svg', $semi-black);
                        }
                    }

                    .padding {
                        padding: 0;
                    }

                    .padding-right {
                        padding: 0;
                    }

                    .grey {
                        width: 100%;
                        max-width: 200px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .iconsStyle {
                        padding-left: 0;

                        .text_change {
                            color: $main-green;
                            cursor: pointer;
                        }

                        .edit {
                            display: none;
                            margin-left: -14px;
                            color: $main-green;
                            cursor: pointer;
                        }

                        .delete {
                            display: none;

                            color: $main-green;

                            cursor: pointer;
                        }

                        .showEmails {
                            position: relative;
                            right: 202px;

                            padding: 2px 7px;

                            span {
                                width: 100%;
                                padding: 2px 7px;

                                background-color: beige;
                            }
                        }
                    }

                    &:hover {
                        .iconsStyle > .edit,
                        .delete {
                            display: inline-block;
                            margin-left: -40px;
                        }
                    }

                    .icons-block {
                        opacity: 0;
                    }

                    &:hover .icons-block {
                        opacity: 1;
                    }
                }
            }

            .testCandidateBlock2 {
                padding: 0 16px;
                background-color: $main-white;

                .mCustomScrollBox {
                    &:after {
                        display: block;
                        clear: both;
                        content: '';
                    }

                    .mCSB_container {
                        top: 0;

                        &:after {
                            display: block;
                            clear: both;
                            content: '';
                        }
                    }
                }
            }

            .mCSB_inside > .mCSB_container {
                margin: 0 !important;
            }

            .testCandidateFromVacancy {
                .checkboxes {
                    padding: 0 var(--main-gap) 16px var(--main-gap);
                }
            }

            .testCandidateFromVacancy2 {
                max-height: 240px;
                padding: 0;
            }

            .testCandidateFromCandidate {
                padding: 15px 0;
                background-color: $main-white;
                border-radius: 5px;
            }
        }

        .mrgTop2 {
            padding: 0;

            .obligatory {
                color: $red;
            }

            .background {
                padding: 16px;
                background-color: $main-white;
                border: 1px solid $border-grey;
                border-radius: 12px;

                .item:not(:first-child) {
                    margin-top: 15px;
                }

                .item {
                    .title {
                        display: inline-block;
                        margin-bottom: 8px;
                        font-size: $secondary-font-size;
                        font-weight: 400;
                        line-height: 1.33;
                        color: $secondary-black;
                    }

                    textarea {
                        width: 100%;
                    }

                    #testCandidateMCE_ifr {
                        height: 335px !important;
                    }

                    #testCandidateMCE2_ifr {
                        height: 200px !important;
                    }
                }
            }
        }
    }

    .attach-file {
        margin-top: 16px;

        &__item {
            display: flex;
            gap: 8px;
            align-items: center;
        }
    }

    .close-icon {
        @include icon-mask('/images/redesign/svg-icons/close.svg', $semi-black);
    }

    mail-to {
        margin: 0;

        &.no-text .mailto-elem:before {
            top: -8px;
            left: -38px;
        }
    }
}

@media (max-width: 575px) {
    .select-candidate {
        flex-direction: column;
    }

    .col-lg-3.padding-right.addChancelBtn.btnForCandidate.pull-right.ng-scope {
        display: flex;
        justify-content: center;
        width: 100%;
        padding-left: 0;
        margin-top: 10px;
    }

    .select2-container-multi .select2-choices .select2-search-field input {
        min-width: 375px;
    }

    .sendTestCandidateToEmail .blockTestCandidate .padding-right {
        display: flex;
        justify-content: center;
        width: 100%;
        padding-left: 0 !important;

        .text-right {
            margin: 10px 0 !important;
        }
    }

    .sendTestCandidateToEmail .addCandidate .mrgTop2 {
        padding-left: 15px;
    }

    .sendTestCandidateToEmail .addCandidate .mrgTop .testCandidateBlock .candidateString {
        flex-direction: column;
        margin-bottom: 15px;
    }

    .sendTestCandidateToEmail .addCandidate .mrgTop .testCandidateBlock .candidateString .padding {
        width: 100%;
    }

    .sendTestCandidateToEmail .addCandidate .mrgTop .testCandidateBlock .candidateString .grey {
        width: 75%;

        padding-left: 0;
        margin-top: 15px;
    }

    .sendTestCandidateToEmail .block-for-flex {
        height: 23px;
    }

    .block-for-flex {
        display: flex;
        align-items: flex-end;
    }

    .sendTestCandidateToEmail .addCandidate .mrgTop .testCandidateFromCandidate {
        padding: 0;
        padding-top: 15px !important;
    }

    .sendTestCandidateToEmail .addCandidate .mrgTop .testCandidateBlock .candidateString .iconsStyle .edit {
        display: block;
        margin: 5px;
    }

    .sendTestCandidateToEmail .addCandidate .mrgTop .testCandidateBlock .candidateString .iconsStyle .delete {
        display: block;
        margin: 5px;
    }

    .sendTestCandidateToEmail .addCandidate .mrgTop .testCandidateBlock .candidateString mail-to {
        top: 5px;
        left: 0 !important;
        margin: 0 0 10px 25px;
    }

    mail-to.no-text .mailto-elem:before {
        top: 0 !important;
    }

    .col-lg-2.col-md-1.col-sm-1.col-xs-1.padding-right.iconsStyle {
        display: flex;
        flex-direction: row;
    }
}

#mCSB_2 {
    border-radius: 5px !important;
}

.mCustomScrollBox {
    border-radius: 5px !important;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
}

.attach-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;

    .attach-button {
        display: inline-flex;
        gap: 8px;
        align-items: center;
        width: fit-content;
        @include link-green;
        transition: all 0.3s ease-in;
    }

    .attach-file-icon {
        @include icon-mask('/images/redesign/svg-icons/attach.svg', $semi-black);
    }

    .editFileMenuTitle {
        font-size: $main-font-size;
        font-weight: normal;
        line-height: 1.3;
        color: $main-green !important;
        transition: all 0.3s ease-in;
    }

    .attach-subtitle {
        font-size: $secondary-font-size;
        font-weight: normal;
        line-height: 1.3;
        color: $secondary-black;
    }
}
