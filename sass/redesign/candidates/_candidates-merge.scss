@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.candidates-merge {
    .duplicate-alert {
        width: 71%;
        margin-right: 30px;
        margin-left: 20px;
        padding: 5px 15px;
        label {
            margin-bottom: 0;
            margin-left: 15px;

            text-decoration: none;
            a {
                color: #337ab7;
                &:hover {
                    text-decoration: none;

                    color: #fdd14a;
                }
            }
        }
    }

    .base-icon {
        filter: $filter-semi-black;
        @include control-icon-size;

        &_color-green {
            filter: $filter-main-green;
        }
    }

    .remove-icon {
        @include icon-mask('/images/redesign/svg-icons/close.svg', $semi-black);
        margin-left: 12px;

        &:hover {
            background-color: $main-black;
        }
    }

    .attachments-menu {
        padding: 12px;
        background-color: $main-white;

        &__item {
            display: flex;
            font-weight: 400;
            gap: 8px;
            align-items: center;
            color: $secondary-black;
            cursor: pointer;
            margin: 0;

            &:not(:last-child) {
                margin-bottom: 12px;
            }
        }
    }

    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: var(--main-gap) var(--main-gap) 0;

        &--left {
            color: $semi-black;
            i {
                display: inline-block;
                margin-bottom: -5px;
            }
        }

        &--right {
            display: flex;
            gap: 12px;
        }
    }

    &__main {
        display: flex;
        gap: var(--main-gap);
        margin: var(--main-gap);

        @media (max-width: 1200px) {
            flex-direction: column;
        }

        &--left,
        &--right {
            @include border-large;
            height: max-content;
            background: $main-white;
        }

        &--left {
            width: 38%;
            max-width: 385px;
            padding: 0 16px;

            @media (max-width: 1200px) {
                width: 100%;
            }

            & > .candidate-add-foto {
                padding: 16px 0;
                display: flex;
                align-items: center;
                justify-content: center;

                .cropper-wrapper {
                    .photo-mask {
                        width: 124px;
                        height: 124px;
                    }
                    .image-add-new {
                        bottom: 0;
                        right: 0;
                        &__item {
                            width: 35px;
                            height: 35px;
                        }
                    }
                }

                & > div {
                    padding: 0 0 15px 0;
                    #photo-edit {
                        position: absolute;
                        bottom: 0;

                        width: 100%;
                        padding: 10px;

                        color: $theme-white;
                        background-color: rgba(0, 0, 0, 0.7);
                        .photo-hover {
                            &:hover {
                                cursor: pointer;

                                color: $theme-blue;
                            }
                        }
                    }
                    & > .photoWidth {
                        width: 100%;
                        height: auto;
                    }
                }
                & > div ~ div {
                    & > div {
                        padding-top: 70px;
                        padding-bottom: 70px;

                        cursor: pointer;
                        text-align: center;

                        background-position-x: -8px;
                        background-position-y: -5px;
                        background-size: 250px 150px;
                    }
                    a.add-image {
                        @include button-styling-cancel();
                    }
                }
            }
            & > h4 {
                @include bold();
                & > i {
                    margin-right: 10px;

                    color: $theme-dark-green;
                }
            }
            & > ul {
                line-height: 25px;

                padding: 0;

                list-style-type: none;
                & > li {
                    & > i {
                        color: $theme-dark-green;
                    }
                    & > label {
                        font-weight: normal;

                        display: inline;
                        margin-left: 10px;

                        cursor: pointer;

                        color: $theme-link-color;
                    }
                    & > a {
                        margin-left: 10px;

                        cursor: pointer;

                        color: $theme-link-color;
                    }
                }
            }
            & > .fastCandTextForTxt {
                position: absolute;
                z-index: 9;

                width: 225%;
                padding: 10px 15px;

                border: 1px solid $theme-light-grey;
                border-radius: 5px;
                background-color: $theme-white;
                box-shadow: 0 1px 10px 4px $theme-light-grey;
                i {
                    padding: 1px;
                    &:hover {
                        cursor: pointer;

                        border-radius: 4px;
                    }
                }
                .text-center {
                    margin-top: 10px;
                    a {
                        @include button-styling();
                    }
                    .cancel {
                        @include button-styling-cancel();

                        margin-right: 10px;
                    }
                }
            }
            & > .fastCandSiteForSite {
                margin-bottom: 10px;
                & > input {
                    @include input-styling();
                    @include border-radius();

                    width: 100%;
                    padding: 4px 8px 4px 4px;
                    &:focus {
                        outline: none;
                    }
                }
                .text-center {
                    margin-top: 10px;
                    a {
                        @include button-styling();
                    }
                }
            }
            & > .candidate-add-control {
                padding: 0;
                & > h5 {
                    font-weight: 500;
                }
                & > span {
                    font-size: 12px;

                    margin-left: 10px;

                    color: #ccc;
                }
                & > .attachments {
                    padding: 0 0 16px 0;
                    border-top: 1px solid #ccc;
                    & > .addedFileCandidate {
                        .attached-list {
                            display: flex;
                            flex-direction: column;
                            gap: 16px;
                            margin-bottom: 16px;
                        }

                        .attached-item {
                            display: flex;
                            gap: 8px;
                            align-items: center;
                            height: 44px;
                            padding: 8px 12px;
                            background-color: $pale-grey;
                            border-radius: 8px;

                            &:hover {
                                .attached-item__remove {
                                    pointer-events: initial;
                                    opacity: 1;
                                }
                            }

                            &__name {
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }

                            &__remove {
                                flex-shrink: 0;
                                margin-right: 0;
                                margin-left: auto;
                                pointer-events: none;
                                opacity: 0;
                            }
                        }

                        & > .menu {
                            & > .pull-left {
                                width: 100%;
                                & > .pull-right {
                                    cursor: pointer;
                                }
                            }
                        }
                    }
                    & > .item {
                        padding: 5px 10px;
                        & > .menu {
                            & > .item {
                                & > span {
                                }
                                & > i {
                                    cursor: pointer;
                                }
                            }
                        }
                    }
                    & > h5 {
                        font-weight: 500;
                    }
                    & > .toggle-dropdown {
                        img {
                            width: 18px;
                            height: 18px;
                            filter: $filter-semi-black;
                            margin-right: 8px;
                        }
                        span {
                            @include green-link();
                            font-weight: 400;
                        }
                    }
                    & > .addLinkFormForLink {
                        padding: 0;
                        & > div {
                            & > form {
                                padding: 16px 0;
                                p {
                                    margin-bottom: 5px;
                                    font-size: $secondary-font-size;
                                    color: $secondary-black;
                                }
                            }
                        }
                        & > div ~ div {
                            & > .text-center {
                                .btn {
                                    @include button-styling();
                                }
                                .accept {
                                    color: $theme-white;
                                    background-color: $theme-dark-green;
                                    &:hover {
                                        background-color: $theme-light-green-hover;
                                    }
                                }
                                .cancel {
                                    color: #585859;
                                    background-color: #e3e3e3;
                                    &:hover {
                                        background-color: $theme-light-grey-hover;
                                    }
                                }
                            }
                        }

                        .btn-wrapper {
                            display: flex;
                            justify-content: space-between;
                            gap: 8px;

                            & > * {
                                width: 100%;
                            }

                            .full-width {
                                width: 100%;
                            }
                        }
                    }
                }
                & > .profile-percent {
                    padding: 0;
                    & > div {
                        margin-top: 20px;
                        .progress-bar-success {
                            background-color: #a3e8a7 !important;
                        }
                    }
                }
                .merge-button {
                    white-space: normal;
                }
                .deleteCandidate {
                    display: inline-block;
                    width: 100%;
                    padding-bottom: 15px;
                    a {
                        @include button-styling-cancel();
                    }
                }
                .mergeCandidate {
                    display: inline-block;
                    width: 100%;
                    padding-bottom: 15px;
                    a {
                        @include button-styling-cancel();

                        margin-left: 10px;
                    }
                }
            }
        }
        &--right {
            width: 100%;

            .main-info {
                display: flex;
                flex-direction: column;
                padding: 16px;

                label {
                    font-size: $secondary-font-size;
                    color: $secondary-black;
                    font-weight: 400;
                }

                .btn-group {
                    display: flex;
                    gap: 16px;

                    button {
                        width: 50%;
                        text-align: left;
                        color: $semi-black;
                        background-color: $background-grey;
                        border: 1px solid $border-grey;
                        border-radius: 8px;
                        min-height: 3em;
                        padding: 0 16px;
                        white-space: normal;
                        word-break: break-word;

                        &.active {
                            transition: all 0.5s;
                            background-color: $light-green;
                        }
                    }

                    .btn-group-column {
                        width: 100%;
                        display: flex;
                        flex-direction: column;
                        gap: 16px;

                        button {
                            width: 100%;
                        }
                    }

                    .regions,
                    .langs {
                        width: 50%;
                        display: flex;
                        flex-direction: column;
                        gap: 16px;

                        button {
                            justify-content: center;
                            width: 100%;
                        }
                    }
                }

                .item {
                    margin-bottom: 15px;
                }

                .top-blocks {
                    display: flex;
                    gap: 16px;
                    .firstBlock,
                    .rightBlock {
                        width: 50%;
                    }
                }

                .custom-fields {
                    width: 100%;
                    display: flex;
                    gap: 16px;

                    .customFirstCandidate,
                    .customSecondCandidate {
                        width: 50%;
                        .btn-group button {
                            width: 100%;
                        }
                    }
                }

                .contactCandidate {
                    width: 100%;
                    display: flex;
                    flex-direction: row;
                    flex-wrap: wrap;
                    column-gap: 16px;

                    .item {
                        width: calc(50% - 8px);
                    }
                }

                .skillsDisplay {
                    width: calc(50% - 8px);

                    .btn-group {
                        margin-bottom: 15px;
                    }

                    .mergeBlock {
                        display: block;
                        height: 15px;
                        margin-bottom: 30px;
                        border: 2px solid $main-green;
                        border-top: none;

                        a {
                            display: block;
                            .mergeIcon {
                                display: inline-block;
                                width: 30px;
                                height: 30px;
                                margin-bottom: -15px;
                                cursor: pointer;
                                background: url(/images/redesign/svg-icons/merge-candidates.svg) $theme-white no-repeat;
                                background-size: cover;
                                box-shadow: 0 0 0 10px $theme-white;
                            }
                        }
                    }
                }

                .btn-wrapper {
                    display: flex;
                    justify-content: left;
                    margin-top: 16px;
                    gap: 12px;
                }
            }
        }
    }
}
