@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.add-and-edit-task {
    .full-width {
        width: 100%;
    }

    .modal-body {
        .item-row {
            align-items: flex-start;
        }
    }

    .item-title {
        gap: 4px;
        margin-bottom: 8px;
        line-height: normal;
    }

    .error-text {
        font-size: $secondary-font-size;
        color: $red;
    }

    .removed-user {
        font-size: $main-font-size;
        color: $red;
    }
}
