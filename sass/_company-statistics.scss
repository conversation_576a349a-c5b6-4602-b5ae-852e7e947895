.block-company-statistics {
    #show_more {
        display: none;
    }

    .row {
        @include disabled-margins-for-row();
    }

    .no-padding {
        padding-right: 0;
        padding-left: 0;
    }

    .time-interval-wrapper {
        height: 68px;
        background: #ffffff;
        box-shadow: 2px 4px 4px rgba(0, 0, 0, 0.1);
        border-radius: 5px;
        padding: 15px !important;
        margin-bottom: 16px;

        .active {
            padding: 0px !important;
        }
    }

    .return-on-report-page {
        margin: 36px 0;
        margin-bottom: 30px;
        padding: 0;

        & > a {
            @include return-on-report-page-btn();

            &:hover {
                .arrow {
                    background-position: -16px 0;
                }
            }
        }

        .arrow {
            position: absolute;
            top: 8px;
            left: 44px;
            width: 18px;
            height: 13px;
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAANCAYAAADISGwcAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMDY3IDc5LjE1Nzc0NywgMjAxNS8wMy8zMC0yMzo0MDo0MiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkJEMkE2MjA4NzA3MjExRTg4NTBCQkJFQUIzNDE0RjJDIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkJEMkE2MjA5NzA3MjExRTg4NTBCQkJFQUIzNDE0RjJDIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QkQyQTYyMDY3MDcyMTFFODg1MEJCQkVBQjM0MTRGMkMiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QkQyQTYyMDc3MDcyMTFFODg1MEJCQkVBQjM0MTRGMkMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6Aa14vAAAA9klEQVR42qSUPQoCMRBGk2UXtPMuuYeVjSiWgjbewhOIiqU/iGglVp4htYgXsPACoiB+AyOEJUpmDDyyZJk3k81mrHPOREYdbMAMDMyP4b3/9irJkUXWmmAHKqDPs3QkO8oFtMASFOAJOuAuTC5y5KXAORdFgQ2wVyQXOT5foAcWfyZXOSx+QgocB2s3cEpMesFP2OXkKgcV8ODzUg0UYDGpHTlfkbD6K+0sMf7Ms9pBBUz4ecTnVwNDcBRsRO3IAgFdlxeoggM3EiMsQuwI+8AqEBTcSKRFiB1ZRNDma0SCraITihyxVrzmO0zda6rohCLHW4ABAN9kT2Kf8NOYAAAAAElFTkSuQmCC);
            background-position: 1px 0;
            background-repeat: no-repeat;
        }
    }

    .img-section {
        padding: 15px;

        background-color: $theme-white;

        img {
            max-width: 100%;
        }

        a.add-image {
            @include bold();

            display: block;
            margin-top: 15px;

            color: $theme-dark;
            border: 1px solid $theme-dark-green;
            background-color: $theme-white;

            .size {
                font-size: 11px;
                font-weight: 300;

                color: $theme-grey;
            }
        }

        .company-label {
            font-size: 11px;
            font-weight: 300;

            margin: 15px 0 0 12px;

            color: $theme-grey;
        }

        .company-name {
            @include input-styling();

            margin-top: -10px;
        }

        .some-numbers {
            display: block;
            margin-top: 15px;

            .header {
                font-weight: 600;
            }

            .information {
                margin-top: 15px;

                .count {
                    color: $theme-dark-green;
                }
            }
        }

        #cropper-wrap {
            padding: 10px;

            border-radius: 5px;

            .cropper-container {
                margin-left: -16px;
            }

            #close {
                @include button-styling-cancel();

                font-weight: normal;

                padding: 3px 9px;
            }

            #cropp {
                @include button-green-styling();

                font-weight: normal;

                padding: 3px 9px;
            }

            #img-wrapper {
                margin-bottom: 20px;
            }
        }

        #wrapperForPng {
            padding: 10px;

            border-radius: 5px;

            img {
                width: 180px;
                height: 180px;
                margin-bottom: 15px;
            }

            #cancel {
                @include button-styling-cancel();

                font-weight: normal;

                padding: 3px 4px;
            }

            #download {
                @include button-green-styling();

                font-weight: normal;

                margin-left: 12px;
                padding: 3px 4px;
            }
        }
    }

    .statistics-outer {
        .header {
            @include disabled-margins-for-row;

            date-range-component {
                .alert {
                    margin-bottom: 0;
                    margin-top: 15px;
                }
            }

            h4 {
                display: inline-block;
                color: #333333;
                margin: 15px 0;
                margin-top: 0;
                font-size: 14px;
            }

            .no-sides-padding {
                padding-left: 0;
            }

            .centered {
                display: flex;
                align-items: center;
                height: 32px;

                h4 {
                    font-size: 18px;
                    margin: 0 10px 0 0;
                }
            }

            .dateRange {
                display: flex;
                flex-direction: row;
                margin-bottom: 20px;

                align-items: center;
                justify-content: flex-start;

                .fields-title-select {
                    position: relative;

                    span.arrow-toggle {
                        padding: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        position: absolute;
                        height: 32px;
                        width: 32px;
                        border: 1px solid #cccccc;
                        border-radius: 0 5px 5px 0;
                        right: 0;
                        top: 0;
                        background: rgba(204, 204, 204, 0.4);

                        i {
                            color: #999999;
                        }
                    }
                }

                .fields-title {
                    width: 100%;
                    @include flex(row, flex-end, center);

                    position: relative;

                    h4 {
                        color: #333333;
                        margin: 0 10px 0 0;
                    }

                    input {
                        text-align: left;
                        padding-right: 42px;
                        min-width: 225px;
                        height: 32px;
                        border: 1px solid #cccccc;
                        border-radius: 5px;
                        color: #333;
                    }

                    .select-content {
                        position: absolute;
                        z-index: 999;
                        top: 100%;
                        left: 0;
                        border: 1px solid #ccc;
                        border-top: none;
                        border-radius: 0 0 5px 5px;

                        visibility: hidden;
                        overflow: hidden;
                        width: 100%;
                        max-height: 0;
                        margin: 0;

                        transition: all 0.5s ease-in;

                        background-color: #fff;
                        list-style-type: none;
                        padding: 0;

                        li {
                            padding: 0 8px;
                            text-align: left;
                            color: #333333;

                            &:hover {
                                cursor: pointer;

                                color: #000;
                                background: rgba(0, 0, 0, 0.1);
                            }
                        }

                        &.active {
                            visibility: visible;
                            max-height: 200px;
                        }
                    }
                }

                .wrap-datepicker {
                    display: flex;
                    width: auto;
                    justify-content: flex-end;
                    align-items: center;

                    .field {
                        display: flex;
                        flex-direction: row;
                        position: relative;
                        width: 131px;

                        button {
                            width: 32px;
                        }

                        md-input-container {
                            max-width: 100px;
                            width: 100px;
                        }

                        input {
                            width: 100px;
                            height: 32px;
                            background-color: #fff !important;

                            color: #000;

                            &#startDate,
                            &#endDate {
                                width: 120px;
                                padding: 0 28px 0 10px;
                                text-align: center;

                                &.active {
                                    border: 1px solid #000;
                                }
                            }
                        }

                        label {
                            margin: 0 -15px;
                        }

                        i.fa-calendar {
                            position: absolute;
                            right: 10px;
                            top: calc(50% - 7px);
                            color: #00b549;
                        }

                        &.disabled {
                            input {
                                background-color: transparent;
                            }

                            i.fa-calendar {
                                color: #333333;
                            }
                        }
                    }

                    li {
                        margin: 0;
                        padding: 0;
                        padding-bottom: 3px;

                        list-style: none;

                        cursor: pointer;

                        &:hover {
                            color: #f0ad4e;
                        }
                    }
                    svg {
                        path {
                            //color: rgba(0, 0, 0, 0.54) !important;
                        }
                    }
                    button {
                        position: relative;
                        top: 1px !important;
                        //right: 4px !important;
                    }
                }

                input {
                    @include input-styling();
                    @include border-radius();

                    width: 100%;
                    padding: 0 10px;

                    border: 1px solid #e3e3e3;
                    outline: none;
                }
            }
            .select-label-text {
                background: white;
            }
            .list-ops {
                text-align: initial;
            }
            a {
                color: $theme-dark;

                i {
                    font-size: 17px;

                    margin-left: 10px;

                    color: $theme-dark-green;
                }
            }

            select {
                @include select-styling();

                border-bottom: none;
                background-color: $theme-light-green;

                &:hover {
                    cursor: pointer;

                    background-color: $theme-light-green-hover;
                }
            }
        }

        .heading-wrapper {
            //display: flex;
            //align-items: center;
            //justify-content: space-between;
            .time-interval-wrapper {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .report-excel {
                a {
                    text-decoration: underline;
                    height: auto;
                    font-size: 16px;

                    color: #333333;
                    cursor: pointer;
                }
                img {
                    display: inline-block;
                    width: 20px;
                    margin-right: 5px;
                }
            }
        }

        .statistics-wrapper {
            & div {
                display: none;
            }
            .responsible-wrapper {
                display: flex;
            }

            .total-row-wrapper {
                height: 64px;
                background: white;
                pointer-events: none;
                border-bottom: 1px solid #f0f0f0;
                & td {
                    border: none;
                    border-bottom: 3px solid #f0f0f0;
                    border-right: 1px solid #f0f0f0;
                    vertical-align: middle;
                    text-align: center;
                    padding: 10px;
                    & table {
                        .inner-table-row {
                            height: auto;
                            border-left: none;

                            &:hover {
                                cursor: pointer;
                                border-left: none;
                            }

                            .inner-table-data {
                                text-align: center;
                                padding: 0;
                                border-right: none;
                                border-bottom: none;
                                background-color: transparent;

                                i.fa.fa-sort {
                                    margin-left: 5px;
                                }

                                .margin {
                                    margin-bottom: 7px;
                                }
                            }
                        }
                    }
                }
            }

            table {
                background-color: $theme-white;

                thead {
                    tr {
                        th {
                            padding: 10px;
                            height: 37px;
                            font-weight: normal;
                            border: none;
                            background-color: #fafafa;
                            font-size: 14px;
                            line-height: 14px;
                            color: #999999;
                            border-right: 1px solid #f0f0f0;
                            vertical-align: middle;
                            overflow: hidden;

                            // inner table style
                            table {
                                .inner-table-row {
                                    height: auto;
                                    border-left: none;

                                    &:hover {
                                        cursor: pointer;
                                        border-left: none;
                                    }

                                    .inner-table-data {
                                        text-align: center;
                                        padding: 0;
                                        border-right: none;
                                        background-color: #fafafa;

                                        i.fa.fa-sort {
                                            margin-left: 5px;
                                        }

                                        .margin {
                                            margin-bottom: 7px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                tbody {
                    tr {
                        position: relative;
                        border-left: 2px solid transparent;
                        height: 64px;

                        &:hover {
                            cursor: pointer;
                            border-left: 2px solid #00b549;
                            background-color: rgba(0, 181, 73, 0.02);
                        }

                        &.table-space {
                            height: 8px;
                            box-shadow: 0px -1px 4px rgba(0, 0, 0, 0.12);
                        }

                        &.total-row-wrapper {
                            pointer-events: none;
                            border-bottom: 1px solid #f0f0f0;
                        }

                        td {
                            border: none;
                            border-right: 1px solid #f0f0f0;
                            vertical-align: middle;
                            text-align: center;
                            padding: 10px;

                            &.name {
                                text-align: left;
                                display: flex;
                                align-items: center;

                                .responsible-person-wrapper {
                                    display: flex;
                                    align-items: center;

                                    responsible-person {
                                        margin-right: 10px;
                                    }
                                }

                                a.person {
                                    text-decoration: none;
                                    color: #337ab7;
                                    font-weight: 600;

                                    &:hover {
                                        cursor: pointer;
                                        color: #fdd14a;
                                    }
                                }
                            }

                            .small-font {
                                font-size: 11px;
                                color: $theme-light-grey;
                            }

                            // vacancy data table
                            table {
                                .inner-table-row {
                                    height: auto;
                                    border-left: none;

                                    &:hover {
                                        cursor: pointer;
                                        border-left: none;
                                    }

                                    .inner-table-data {
                                        padding: 0;
                                        border-right: none;
                                        background-color: transparent;
                                        text-align: center;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 575px) {
    .block-company-statistics .container-fluid {
        padding: 0 5px !important;
    }
    .block-company-statistics .statistics-outer {
        .tooltip-outer {
            width: 280px !important;
            left: 50px !important;
        }
        .heading-wrapper {
            .time-interval-wrapper {
                flex-direction: column;
                height: auto;
                .text-right.links {
                    padding-right: 0 !important;
                    .dateRange {
                        flex-direction: column;
                        margin-bottom: 10px !important;
                        .fields-title {
                            flex-direction: column;
                            margin-left: 0 !important;
                            margin: 0 0 10px 0 !important;
                            h4 {
                                margin-bottom: 10px !important;
                            }
                        }
                    }
                }
            }
        }
        .statistics-wrapper {
            width: 100%;
            overflow: scroll;
            .table {
                margin-bottom: 0 !important;
            }
        }
    }
}

@media (max-width: 386px) {
    .block-company-statistics .statistics-outer .no-sides-padding {
        padding-left: 35px !important;
        .main-page-title {
            margin-top: 14px !important;
        }
    }
}
