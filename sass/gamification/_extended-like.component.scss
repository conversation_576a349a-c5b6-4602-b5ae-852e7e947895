extended-like-component {
    .like-wrapper {
        display: flex;
        align-items: flex-start;

        .like-button-container {
            position: relative;
            display: inline-flex;
        }

        .like-button {
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-top: 5px;

            &__img {
                width: 24px;
            }
        }

        .like-emoji {
            position: absolute;
            bottom: 50%;
            left: 20%;

            display: flex;
            flex-wrap: wrap;

            overflow: auto;
            width: 245px;
            height: 82px;

            margin: 10px;
            background: #ffffff;
            box-shadow: 0px 4px 4px 4px rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            padding: 5px;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease-in-out;
            pointer-events: none;

            &.showBlock {
                opacity: 1;
                transform: translateY(0);
                pointer-events: unset;
            }

            &__item {
                margin: 0 5px 5px;
                cursor: pointer;
                transition: transform 0.15s ease-in-out;
                width: 28px;

                &:hover {
                    transform: scale(1.2);
                }

                &:last-child {
                    margin-right: 0;
                }
            }
        }

        .like-reactions {
            display: flex;
            flex-wrap: wrap;
            margin-left: 5px;

            .like-reaction {
                position: relative;
                display: flex;
                border: 1px solid lighten($gamification-orange-title-color, 20%);
                border-radius: 20px;
                padding: 1px 9px;
                cursor: pointer;
                margin: 5px 0 0 5px;

                &:hover {
                    .like-reaction__users {
                        visibility: visible;
                        opacity: 1;
                    }
                }

                &.isReacted {
                    border: 1px solid $gamification-orange-title-color;
                    background-color: lighten($gamification-orange-title-color, 45%);
                }

                &__users {
                    display: flex;
                    flex-wrap: wrap;
                    position: absolute;
                    bottom: 24px;
                    padding: 10px;
                    background-color: #0e0e0ec7;
                    color: #ffffff;
                    overflow: hidden;
                    border-radius: 10px;
                    font-size: 12px;
                    transform: translate(-40%, 0%);
                    opacity: 0;
                    visibility: hidden;
                    transition: opacity 0.2s;
                    &-item {
                        margin: 2px 0;
                        width: max-content;
                    }
                }

                &__count {
                    color: $gamification-orange-title-color;
                }

                &__image {
                    margin-left: 4px;

                    img {
                        width: 16px;
                    }
                }
            }
        }
    }

    .mCS-dark .mCSB_scrollTools {
        width: 8px;
        border: 1px solid #fd5706;
        border-radius: 10px;
        z-index: 2;
    }

    .mCS-dark .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
        width: 4px;
        margin-left: 1px;
        margin-top: 1px;
        margin-bottom: 1px;
        background-color: #fd5706 !important;
    }
}
