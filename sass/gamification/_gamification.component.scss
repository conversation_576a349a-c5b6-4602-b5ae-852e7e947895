gamification-component {
    .gamification-container {
        padding: 20px 20px 40px;
        border-radius: 10px;
        margin-top: 10px;
        background-color: #ffffff;
        box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.08);
        position: relative;
    }

    .gamification-content {
        display: flex;
        justify-content: center;
        align-items: center;

        width: 100%;
        height: 400px;

        &.gold-status {
            background: url(/images/sprite/gamification/gold.svg) no-repeat center center / contain;
        }

        &.silver-status {
            background: url(/images/sprite/gamification/silver.svg) no-repeat center center / contain;
        }

        .message-block {
            width: 75%;
            text-align: center;

            &__text {
                font-size: 22px;
                font-weight: 600;

                .img-size {
                    width: 25px;
                }
            }
        }
    }

    .gamification-like-wrapper {
        position: absolute;
        left: 25px;
        bottom: 10px;
    }
}

@media (max-width: 1600px) {
    gamification-component {
        .gamification-content {
            height: 330px;
            .message-block {
                &__text {
                    font-size: 18px;
                }
            }
        }
    }
}

@media (max-width: 1200px) {
    gamification-component {
        .gamification-content {
            height: 435px;
            .message-block {
                width: 65%;
                &__text {
                    font-size: 22px;
                }
            }
        }
    }
}

@media (max-width: 710px) {
    gamification-component {
        .gamification-content {
            height: 340px;
            .message-block {
                width: 80%;
                &__text {
                    font-size: 19px;
                }
            }
        }
    }
}

@media (max-width: 580px) {
    gamification-component {
        .gamification-content {
            height: 260px;
            .message-block {
                width: 80%;
                &__text {
                    font-size: 16px;
                }
            }
        }
    }
}

@media (max-width: 375px) {
    gamification-component .gamification-content .message-block__text {
        font-size: 15px;
    }
    gamification-component .gamification-content .message-block {
        padding-top: 20px;
    }
}

@media (max-width: 360px) {
    gamification-component {
        .gamification-content {
            height: 175px;
            .message-block {
                width: 80%;
                &__text {
                    font-size: 12px;
                }
            }
        }
    }
}
