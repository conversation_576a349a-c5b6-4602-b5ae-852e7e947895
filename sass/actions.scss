@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.action-personal-wrapper {
    display: flex;
    width: 100%;
    font-size: $secondary-font-size;
    font-weight: 400;
    line-height: 18px;
    color: $semi-black;
    letter-spacing: 0.28px;

    .user {
        font-size: $secondary-font-size !important;
        font-weight: 400;
        line-height: 18px !important;
        color: $semi-black !important;
        letter-spacing: 0.28px !important;

        &:hover {
            color: $yellow !important;
            cursor: pointer;
        }
    }
}

.action-buttons-wrapper {
    position: absolute;
    top: 10px;
    right: 0;
    display: flex;
    flex-direction: row-reverse;
    gap: 8px;
    align-items: center;
    justify-content: flex-start;
    margin-right: 16px;

    .remove-action,
    .pin-action,
    .eye-action,
    .edit-action {
        @include control-icon-size;
        cursor: pointer;
        transition: 0.2s background-color;

        &:hover {
            background-color: $main-black;
        }
    }

    .remove-action {
        @include icon-mask('/images/redesign/svg-icons/close.svg', $semi-black);
    }

    .edit-action {
        @include icon-mask('/images/redesign/svg-icons/pencil.svg', $semi-black);
    }

    .pin-action {
        @include icon-mask('/images/redesign/svg-icons/pin.svg', $semi-black);
    }

    .eye-action {
        @include icon-mask('/images/redesign/svg-icons/eye.svg', $semi-black);
        cursor: default;
    }
}

.remove-user-in-history {
    color: $red !important;
}
