.add-coment {
    .checkbox-wrapper__disable {
        label {
            margin-top: 0px !important;
            margin-bottom: 5px !important;
        }

        .disabled-checkbox + label:hover:before {
            border-color: unset !important;
        }

        .disabled-checkbox:checked + label:before {
            border-color: #c4c4c4 !important;
            background: #c4c4c4 !important;
        }
    }

    #cke_ckEditorVacancyComment {
        .cke_top {
            display: none;
        }
    }

    .cke_editor_ckEditorEmployeeEdit {
        .cke_top {
            display: none;
        }
    }

    #cke_ckEditorEditCandidateComment {
        .cke_top {
            display: none;
        }
    }

    #cke_ckEditorCandidateMain {
        #cke_1_top {
            display: none;
        }
    }

    .modal-body {
        .item {
            &-radio {
                display: flex;
                flex-direction: row;
                align-items: center;
                gap: 8px;
                .checked {
                    font-weight: 600;
                }
            }
            &-header {
                font-size: $heading-small-font-size;
                font-weight: 500;
                text-align: center;
                margin: 0;
            }
            input-component input::-webkit-outer-spin-button,
            input::-webkit-inner-spin-button {
                -webkit-appearance: none;
                margin: 0;
            }

            .disabledChecker {
                pointer-events: none;
            }

            .disabledCheckbox {
                pointer-events: none;
                opacity: 0.6;
            }
            .sendVia {
                width: 50%;
            }
        }
        .item-actions {
            &-arrow {
                img {
                    width: 18px;
                    transition: 0.3s all;
                }
                .open-auto {
                    filter: $filter-main-green;
                }
                .close-auto {
                    filter: $filter-semi-black;
                    &:hover {
                        filter: $filter-main-black;
                    }
                }
            }

            &.disabled {
                opacity: 0.6;
                pointer-events: none;
            }
        }
    }

    &.edit-email-integration {
        input {
            margin-bottom: 5px;
        }

        select {
            border-radius: 5px;
        }
    }

    .send-letter {
        max-width: 640px;
        width: 100%;
        padding: 25px;
        background: #ffffff;
        box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.14), 0px 4px 8px rgba(0, 0, 0, 0.2);
        border-radius: 5px;

        .send-letter__title {
            color: #202021 !important;
            font-weight: 700 !important;
            font-size: 14px;
            display: inline-block;
            margin-bottom: 12px;
        }

        .set-time {
            max-width: 135px;
            min-width: 135px;
            width: 100%;
            height: 35px;
            background: #ffffff;

            border: 1px solid #cccccc;
            border-radius: 5px;
            padding: 4px 0px 4px 10px;

            ::placeholder {
                color: #c4c4c4;
            }
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
    }
}
