/*.adding-responsible {
    .modal-header {
        i {
            @include close-item();
        }
    }
    .modal-body {
        & > .taskText {
            & > .form-control {
                height: 200px;
                max-height: 380px;

                resize: vertical;
            }
        }
    }
    .modal-footer {
        .btn {
            @include button-styling();
        }
        .accept {
            color: $theme-white;
            background-color: $theme-dark-green;
            &:hover {
                background-color: $theme-light-green-hover;
            }
        }
        .cancel {
            color: #585859;
            background-color: #e3e3e3;
            &:hover {
                background-color: $theme-light-grey-hover;
            }
        }
    }
}

@media (max-width: 575px) {
    .adding-responsible {
        .modal-body {
            padding: 10px 0 20px 0 !important;
        }
        .modal-footer {
            padding-bottom: 15px;
        }
    }
}
*/
