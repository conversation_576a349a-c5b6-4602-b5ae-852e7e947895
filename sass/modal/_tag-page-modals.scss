.secondary-modal .modal-750px {
    max-width: 100% !important;
    width: 880px !important;
}
.secondary-modal .modal-dialog {
    width: 540px;
}
.add-tags-input {
    outline: none;
    border: 1px solid #cccccc !important;
    border-radius: 5px;
    background: #ffffff;
    width: 100%;
    height: 32px;
    padding: 10px 12px;
    color: #666666;
    font-weight: normal;
    &:focus {
        outline: none;
        border: 1px solid #cccccc;
        box-shadow: none;
    }
}
.tag-modal {
    .select2-container .select2-choice > .select2-chosen {
        float: left;
    }
    .select2-container {
        width: 95%;
    }
    .modal-header {
        .fa-times {
            position: absolute;
            right: 8px;
            top: 8px;
            &:hover {
                cursor: pointer;
            }
        }
    }
    .modal-body {
        &-error-message {
            display: none;
            width: 100%;
            text-align: center;
            margin-bottom: 5px;
            color: #b94a48;
            &.show {
                display: block;
            }
        }
    }
    .modal-footer {
        .button {
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            width: 100%;
        }
    }
    button[disabled] {
        background-color: #00b549 !important;
        opacity: 0.3;
    }
    input::placeholder {
        font-size: 12px;
        font-weight: normal;
    }
    .btn_thin {
        min-width: 110px;
    }
    .merged-body {
        height: 130px;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        flex-direction: column;
        position: relative;
    }
    .edit-body {
        height: 130px;
        position: relative;
    }
}
.short-descr-modal {
    .modal-dialog {
        width: 395px;
    }
}

@media (max-width: 575px) {
    .tag-modal {
        .modal-footer {
            button {
                max-width: 120px;
                min-width: 120px;
                width: 120px;
            }
            .text-center {
                width: 100%;
                justify-content: space-around;
            }
        }
    }
}
