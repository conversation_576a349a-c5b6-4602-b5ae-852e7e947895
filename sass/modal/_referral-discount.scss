.referral-discount-popup {
    .modal-dialog {
        width: 550px;
        .modal-content {
            border-radius: 10px;
            .referral-discount-content {
                .modal-header {
                    .close-icon {
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        display: inline-block;
                        width: 10px;
                        height: 10px;
                        background: url('../../images/sprite/close-icon.svg');
                        background-size: cover;
                        cursor: pointer;
                    }
                }
                .modal-body {
                    padding: 20px;
                    .content-block {
                        display: flex;
                        justify-content: space-between;
                        .image-block,
                        .text-block {
                            width: calc(50% - 10px);
                        }
                        .text-block {
                            p:last-child {
                                margin-bottom: 0;
                            }
                        }
                    }
                    @media (max-width: 520px) {
                        .content-block {
                            flex-direction: column;
                            .image-block,
                            .text-block {
                                width: 100%;
                            }
                            .image-block {
                                text-align: center;
                                margin-bottom: 15px;
                            }
                        }
                    }
                }
                .modal-footer {
                    .go-to-payment {
                        @include button-animated();
                    }
                }
            }
        }
    }
}
