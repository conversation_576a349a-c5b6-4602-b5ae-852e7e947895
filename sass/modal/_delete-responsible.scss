@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.delete-responsible {
    .modal-header {
        & > h4 {
            @include bold();
        }
        i {
            @include close-item();
        }
    }
    .modal-body {
        justify-content: center;
        align-items: center;
        padding: 24px 16px 16px;

        .modal-text {
            max-width: 575px;
            font-size: $heading-font-size;
            color: $main-black;
        }

        .fa-times {
            @include close-item();
        }
        & > .taskText {
            & > .form-control {
                height: 200px;
                max-height: 380px;

                resize: vertical;
            }
        }
        &.news-modal {
            overflow-x: hidden;
        }
    }
    .modal-footer {
        padding: 16px 16px 24px;

        .button {
            width: 100%;

            button {
                width: 100%;
            }
        }

        .btn_wrapper {
            display: flex;
            justify-content: center;

            a {
                margin-right: 15px;
            }
        }
    }
}
