@import 'js/common/styles/global-variables.module';

.referral-bonus-content {
    .modal-body {
        #message_ifr {
            height: 134px;
            max-height: 134px;
        }

        .form-control {
            padding: 6px 10px;
        }

        .mce-content-body {
            height: 115px;
        }

        label {
            font-weight: normal;
            line-height: 14px;
        }

        .sender-email-label {
            margin-top: 10px;
        }

        .subject-row {
            margin-bottom: 10px;
        }

        .email-row {
            margin-bottom: 10px;

            .sender-email {
                position: relative;
                display: block;

                cursor: pointer;

                .dropdown-wrapper {
                    position: absolute;
                    z-index: 10;

                    display: none;
                    width: 100%;
                    background-color: white;

                    border: 1px solid #ccc;

                    .integrated-sender-email {
                        display: block;
                        padding: 8px;

                        &:hover {
                            background-color: #eee;
                        }
                    }
                }

                .email-select-header {
                    position: relative;
                    height: 34px;
                    min-height: 34px;

                    padding: 5px 8px 8px;

                    color: #999;
                    border: 1px solid #ccc;
                    border-radius: 5px;

                    .chevron-block {
                        position: absolute;
                        top: 0;
                        right: 0;

                        width: 29px;
                        height: 32px;

                        color: #ccc;
                        background: #f1f1f1 url(/images/sprite/vector1.svg) no-repeat center;
                        border-left: 1px solid #ccc;
                        border-top-right-radius: 5px;
                        border-bottom-right-radius: 5px;

                        .fa-chevron-down {
                            transform: translate(50%, 50%);
                        }
                    }
                }

                .error-email-sender {
                    border: 1px solid $red;
                }
            }
        }

        .error-message {
            display: none;
            color: $red;

            &.empty {
                display: none;
            }
        }

        .error-message-emailSender {
            color: $red;
        }

        .message-row,
        .email-row,
        .subject-row {
            .error-message {
                display: none;
                color: $red;
            }

            &.not-valid {
                input {
                    border-color: $red;
                }

                .mce-tinymce.mce-container.mce-panel,
                .mce-edit-area.mce-container.mce-panel.mce-stack-layout-item.mce-last {
                    border: 1px solid $red;
                }

                .error-message {
                    display: block;

                    &.empty {
                        display: none;
                    }
                }
            }

            &.empty {
                input {
                    border-color: $red;
                }

                .error-message.empty {
                    display: block;
                }
            }
        }
    }

    .modal-footer {
        .go-to-payment {
            margin-left: 31px;
            @include button-animated();
        }

        .close-modal {
            @include button-animated($theme-btn-back, #333333, $theme-btn-back-hover, white);
            border: 1px solid #333333;
        }
    }
}
@media (max-width: $screen-xs) {
    .send-referral-popup .modal-dialog .modal-content .referral-bonus-content .modal-body {
        margin: 10px 10px 0px 10px;
    }

    .send-referral-popup .modal-dialog {
        &.with-keyboard {
            margin-top: 155px;
        }

        .modal-content .referral-bonus-content {
            .modal-footer {
                .go-to-payment {
                    margin: 15px 0 0;
                }
            }
        }
    }
}
