@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.edit-vacancy-application-modal {
    .edit-application-header {
        &__close {
            position: absolute;
            top: 15px;
            right: 15px;
        }

        &__title {
            text-align: center;
        }

        &__subTitle {
            margin: 20px;
            color: $secondary-black;
        }
    }

    .fields-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 44px;
        padding: 6px 16px;
        cursor: pointer;

        &__arrow {
            @include control-icon-size;
            filter: $filter-secondary-black;
            transition: all 0.3s ease-in-out;
            transform: rotate(180deg);

            &-rotated {
                transform: rotate(360deg);
            }
        }
    }

    .edit-application-content {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .system-fields {
            &__hint {
                margin: 19px 0;
            }
        }

        .custom-fields {
            &__table {
                .table {
                    margin-bottom: 0;
                }
            }
        }
    }

    .fields-list-title {
        font-weight: 500;
    }

    .edit-application-footer {
        padding-top: 36px;

        button:first-child {
            margin-right: 20px;
        }
    }

    .toggle-list {
        @include border-small;
        background-color: $background-grey;
    }

    .table {
        display: flex;
        flex-direction: column;
        max-height: 0;
        margin: 0;
        overflow: hidden;
        transition: all 0.15s ease-in-out;

        &-row {
            display: flex;
            border-bottom: 1px solid $border-grey;

            &:last-child {
                border-bottom: none;
            }
        }

        &-data {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20%;
            height: 44px;
            padding: 6px 16px;

            &:first-child {
                justify-content: flex-start;
                width: 60%;
                border-left: none;
            }
        }

        &.full-height {
            height: 100%;
            max-height: 22vh;
            margin: 0;
        }
    }

    .control-wrapper {
        display: flex;
        align-items: center;
    }

    .table-head {
        padding-right: 8px;
        color: $semi-black;
        background-color: $border-grey;
    }

    .table-body {
        overflow: auto;
    }

    .switch {
        .switch-label {
            padding: 0 0 6px 44px;
        }
    }

    .checkbox {
        display: none;

        + label {
            position: relative;
            display: block;
            margin: 0;
            cursor: pointer;

            &:before {
                position: absolute;
                top: 1px;
                left: 0;
                display: block;
                width: 16px;
                height: 16px;
                content: '';
                border: 1px solid #d6e0e7;
                border-radius: 3px;
                transition: all 295ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
            }

            &:after {
                position: absolute;
                top: 4px;
                left: 6px;
                display: block;
                width: 4px;
                height: 8px;
                content: '';
                opacity: 0;
                transition: opacity 295ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
                transform: rotate(45deg);
            }

            &:hover:before {
                border-color: #47ab43;
            }
        }

        &:checked + label {
            &:before {
                background: #47ab43;
                border-color: #47ab43;
            }

            &:after {
                opacity: 1;
            }
        }
    }
}

@media (max-width: 670px) {
    .edit-vacancy-application-modal .table-data:nth-child(2) {
        min-width: 115px;
    }
}

@media (max-width: 600px) {
    .publish-rabota-popup .modal-dialog .modal-content {
        margin: 10px;
    }

    .edit-vacancy-application-modal .table-data:first-child {
        min-width: 120px;
    }

    .edit-vacancy-application-modal .table-data:last-child {
        min-width: 100px;
        margin-right: 15px;
    }

    .edit-vacancy-application-modal .table-row {
        width: 350px;
    }
}
