.vacancy-application-modal {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    .modal-body {
        overflow: visible;
    }
    .secondary-modal-body {
        display: inline-block;
    }
    .error {
        border: 1px solid red !important;
        border-radius: 5px;
    }
    .form-control {
        border: 1px solid #cccccc;
        border-radius: 5px;
        padding: 10px 12px;
        height: 32px;
        &:focus {
            outline: none;
            box-shadow: none;
        }
    }
    &-header {
        &__title {
            text-align: center;
            font-weight: 500;
            font-size: 18px;
            color: #202021;
        }
        &__close {
            position: absolute;
            top: 15px;
            right: 15px;
        }
    }
    &-body {
        padding: 0;
        margin-top: 30px;
    }
    &-footer {
        padding-bottom: 30px;
        padding-top: 10px;
        background-color: white;
    }
    &-item {
        display: flex;
        flex-direction: column;
        .hide-add-lang-vacancy {
            opacity: 0 !important;
            pointer-events: none;
        }
        &-hidden {
            display: none;
        }
        &-radio {
            display: flex;
            &:first-child {
                margin-right: 50px;
            }
            label {
                margin-right: 6px;
                letter-spacing: 1px;
                cursor: pointer;
            }
        }
        &-error {
            margin-left: 12px;
            height: 20px;
            opacity: 0;
            &-show {
                opacity: 1;
            }
            &-block {
                color: red;
            }
            &-border {
                border: 1px solid red !important;
                border-radius: 5px;
            }
        }
    }
    .hoverIcon {
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
        .showIcon {
            margin-top: -31px;
            margin-right: 15px;

            cursor: pointer;

            opacity: 0;
            color: $theme-green;
            position: absolute;
            right: 15px;
            top: 60px;
        }
        .editDate {
            display: flex;
            padding: 0;
            width: 100%;
            i {
                color: $theme-green;
            }
            span {
                cursor: pointer;
            }
            .layout-row {
                width: 100%;
                .md-button.md-icon-button {
                    margin: 0;
                }
                md-input-container.md-icon-float {
                    width: 100%;
                }
            }
        }
        .onlyDate {
            width: 100%;
            .layout-row {
                width: 100%;
                .md-icon-float {
                    width: 100%;
                }
            }
        }
        .onlyDate + .showIcon {
            margin-top: 5px;
            align-items: center;
            position: absolute;
            right: 15px;
            top: 30px;
        }
        &:hover .showIcon {
            opacity: 1;
        }
    }
    .vacancy-page-custom-fields-item {
        width: 45%;
    }
    md-input-container.md-icon-float {
        width: 100% !important;
    }
    .show-error-message {
        color: red;
    }
    .fd-row {
        flex-direction: row !important;
    }
    .select2-container .select2-choice > .select2-chosen {
        font-size: 14px !important;
    }
    region-input.region-container .select2-container .select2-choice .select2-arrow {
        border-left: 1px solid #ccc !important;
        background-color: #f1f1f1 !important;
    }
    region-input.region-container .select2-container .select2-choice abbr {
        right: 40px !important;
    }
    region-input.region-container {
        justify-content: space-between;
    }
    region-input.region-container .select2-container {
        margin-right: 0 !important;
        width: 45%;
    }
    region-input.region-container {
        display: flex;
    }
    region-input.region-container .select2-container {
        margin-right: 25px;
        margin-top: 0 !important;
    }
    .second-divider {
        width: 2%;
    }
    .first-divider {
        width: 3%;
    }
    .csn-wrapper {
        width: 45%;
    }
    .width-for-regions {
        width: 100% !important;
    }
    #requiredSkills {
        width: 35% !important;
    }
    .select-skill {
        width: 23% !important;
    }
    .sides-margin {
        margin-left: 11px !important;
    }
    .skill-title {
        width: 26% !important;
    }
    .skills-switcher {
        label {
            margin-bottom: 0px;
        }
    }
}
.vacancy-application-popup {
    &.modal {
        align-items: flex-start;
    }
    .modal-dialog {
        max-width: 850px;
        width: 100%;
        .modal-content {
            border-radius: 5px;
            border: 0;
            .publish-vacancy-rabota {
                .modal-header {
                    font-size: 22px !important;
                    background-color: #daece1;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                    .title {
                        color: #fff;
                        font-size: 22px;
                    }
                    .close-btn {
                        margin: -2px -10px;
                        width: 14px;
                        height: 14px;
                        position: absolute;
                        content: '';
                        mask: url('../../../../images/sprite/closeIntegrationPopup.svg') no-repeat 50% 50%;
                        background: #ffffff none;
                        top: 17px;
                        right: 25px;
                        &:hover {
                            cursor: pointer;
                        }
                    }
                }
                .modal-body {
                    padding: 25px;
                    // fix for display dropdown correct in the end of the pop-up
                    max-height: inherit;
                    overflow: inherit;
                    .wrapper {
                        margin-top: 20px;
                    }
                    .row_wrapper {
                        display: flex;
                        justify-content: space-between;
                        flex-wrap: wrap;
                        margin-top: 15px;
                        .flex-row {
                            flex-direction: row !important;
                            justify-content: space-between;
                        }
                        .language-wrapper,
                        .language-level-wrapper {
                            width: 48%;
                        }
                        .salary {
                            width: 100% !important;
                            flex-direction: row !important;
                            div {
                                flex-direction: column;
                                display: flex;
                                &.sum {
                                    width: 15%;
                                    margin-right: 7px;
                                }
                                &.cur {
                                    display: flex;
                                    flex-direction: column;
                                    justify-content: flex-end;
                                    p {
                                        line-height: 21px;
                                        color: #bdbdbd;
                                        margin-bottom: 5px;
                                    }
                                }
                                &.descr {
                                    width: 80%;
                                    margin-left: 20px;
                                }
                            }
                        }
                        .line-checkbox {
                            flex-direction: row !important;
                            label {
                                margin-left: 0;
                            }
                            input {
                                width: 16px;
                                height: 16px;
                                display: block;
                                margin-right: 7px;
                                margin-top: 1px;
                                float: left;
                                position: relative;
                                cursor: pointer;
                                //&:checked:after {
                                //  background: #00B549;
                                //  content: '\2714';
                                //  border-radius: 2px;
                                //  color: #fff;
                                //}
                            }
                        }

                        .item {
                            display: inline-flex;
                            width: 48%;
                            flex-direction: column;
                            .error-text {
                                color: red;
                                opacity: 0;
                            }
                            .opacity1 {
                                opacity: 1;
                            }
                            .description-length {
                                font-size: 12px;
                                font-weight: 400;
                            }
                            label {
                                color: #333333;
                                .mandatory-star {
                                    color: #ef5350;
                                }
                                .info-icon {
                                    position: relative;
                                    top: -6px;
                                    left: 2px;
                                }
                                &.flexi {
                                    display: flex;
                                    flex-direction: row;
                                }
                            }
                            input[type='text'],
                            input[type='email'],
                            input[type='number'] {
                                border: 1px solid #999999;
                                border-radius: 5px;
                                color: #666666;
                                height: 32px;
                                padding: 12px;
                                &:focus,
                                &:active {
                                    outline: none;
                                }
                                &::placeholder {
                                    color: #cccccc;
                                }
                                &.error {
                                    border: 1px solid red;
                                }
                            }
                            .inputs {
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                input[type='text'] {
                                    border: 1px solid #999999;
                                    border-radius: 5px;
                                    color: #666666;
                                    height: 32px;
                                    padding: 12px;
                                    width: 25%;
                                    &:focus,
                                    &:active {
                                        outline: none;
                                    }
                                    &::placeholder {
                                        color: #cccccc;
                                    }
                                    &.error {
                                        border: 1px solid red;
                                    }
                                    &:last-child {
                                        width: 45%;
                                    }
                                }
                                input[type='number'] {
                                    width: 30%;
                                }
                            }

                            custom-select-new,
                            custom-select-autocompete,
                            custom-select-with-autocomplete,
                            custom-select-with-checkboxes {
                                &.error .select-label.custom-new .select-label-text {
                                    border: 1px solid red;
                                    border-right: none;
                                }
                                &.error .select-label.custom-new .select-caret {
                                    border: 1px solid red;
                                    border-left: none;
                                }
                                .select-label.custom-new {
                                    width: 100%;
                                }
                                .select-ops.custom-new.active {
                                    position: relative;
                                    width: 100%;
                                }
                                .select-label.custom-new .select-label-text {
                                    border-color: #999999;
                                    border-right: 0;

                                    color: #333333;
                                    &.placeholder {
                                        color: #cccccc;
                                    }
                                    input {
                                        color: #333333;
                                    }
                                }
                                .select-label.custom-new .select-caret {
                                    background-color: transparent;
                                    border-color: #999999;
                                    .error {
                                        border: 1px solid red;
                                    }
                                }
                                .select-label.custom-new.disabled {
                                    opacity: 0.5;
                                    background-color: transparent;
                                    cursor: not-allowed;
                                    user-select: none;
                                }
                            }
                            custom-select-new.salary_select {
                                width: 30%;
                            }
                            custom-select-new.error,
                            custom-select-with-checkboxes.error {
                                .select-label.custom-new .select-label-text {
                                    border-color: red;
                                }
                                .select-label.custom-new .select-caret {
                                    border-color: red;
                                }
                            }
                            #s2id_1_city_container {
                                margin-top: 20px;
                            }
                            .disabled-input,
                            .select2-container.disabled-input {
                                .select-label.custom-new .select-label-text,
                                .select-label.custom-new .select-caret {
                                    border-color: rgba(204, 204, 204, 0.5);
                                }
                                .select-label.custom-new .select-caret i {
                                    color: #cccccc;
                                }
                                .select2-choice {
                                    border-color: rgba(204, 204, 204, 0.5);
                                    background-color: transparent;
                                    > .select2-chosen {
                                        color: #cccccc;
                                    }
                                    .select2-arrow {
                                        b {
                                            mask: url('../../../../images/sprite/vector.svg') no-repeat 50% 50%;
                                            background: #cccccc none;
                                        }
                                    }
                                    //.select2-arrow {
                                    //  //background-color: transparent;
                                    //  border-left: 0;
                                    //  color: #cccccc;;
                                    //  background-color: #cccccc;;
                                    //}
                                }
                            }
                            .select2-container {
                                .select2-choice {
                                    border-color: #999;
                                    > .select2-chosen {
                                        color: #666666;
                                    }
                                    .select2-arrow {
                                        background-color: transparent;
                                        border-left: 0;
                                    }
                                }
                            }
                            .select2-container.error {
                                .select2-choice {
                                    border-color: red;
                                }
                            }
                        }
                        .wide-item {
                            width: 100%;
                            flex-direction: column;
                        }
                        .language-wrapper {
                            position: relative;
                        }
                        .language-wrapper .custom-select-new-wrapper {
                            &:after {
                                content: '\2014';
                                color: rgb(153, 153, 153);
                                position: absolute;
                                top: 8px;
                                right: -21px;
                            }
                        }
                        .custom-select-new-wrapper {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 10px;
                            position: relative;
                            &:hover .remove-fields {
                                opacity: 1;
                            }
                            .remove-fields {
                                opacity: 0;
                                transition: opacity 0.2s 0.1s;
                                cursor: pointer;
                                position: absolute;
                                top: 1px;
                                right: -14px;
                                &:after {
                                    opacity: 1;
                                    transition: all 0.3s 0.2s;
                                    content: '\2716';
                                    position: absolute;
                                    top: 7px;
                                    right: -3px;
                                    width: 12px;
                                    height: 20px;
                                }
                            }
                            &:last-of-type {
                                margin-bottom: 0;
                            }
                            .half-width {
                                width: 48%;
                            }
                        }

                        .additional-switcher {
                            display: flex;
                            flex-direction: row;
                            justify-content: space-between;
                            background-color: #f7f7f7;
                            height: 40px;
                            border-left: 2px solid #999;
                            align-items: center;
                            cursor: pointer;
                            padding: 15px;
                            i.fa {
                                color: #999999;
                            }
                            &:hover {
                                i.fa {
                                    color: darken(#999999, 5%);
                                }
                            }
                        }
                        .margin-top {
                            margin-top: 20px;
                        }
                        .full-width {
                            width: 100%;
                            .error {
                                border: 1px solid red;
                            }
                        }
                        .textarea-wrapper {
                            padding: 1px;
                        }
                        .flex {
                            display: flex;
                            align-items: center;
                        }
                        .specifyContacts {
                            flex-direction: row;
                            input[type='checkbox'] {
                                width: 16px;
                                height: 16px;
                                display: block;
                                margin-right: 7px;
                                margin-top: 1px;
                                float: left;
                                position: relative;
                                cursor: pointer;
                                &:after {
                                    content: '';
                                    vertical-align: middle;
                                    text-align: center;
                                    line-height: 16px;
                                    position: absolute;
                                    cursor: pointer;
                                    height: 15px;
                                    width: 15px;
                                    left: 0;
                                    top: 0;
                                    font-size: 12px;
                                }
                                &:checked:after {
                                    background: #00b549;
                                    content: '\2714';
                                    border-radius: 2px;
                                    color: #fff;
                                }
                            }
                        }
                        .bold {
                            font-weight: 500;
                        }
                    }
                    .mce-statusbar {
                        display: none;
                    }
                }
                .modal-footer {
                    display: flex;
                    justify-content: center;
                    button:first-child {
                        margin-right: 25px;
                    }
                }
            }
        }
    }
}

@media (max-width: 500px) {
    .vacancy-application-popup {
        .modal-dialog {
            margin: 0 !important;
        }
    }
    .vacancy-application-modal .modal-body {
        .exp-and-salary {
            flex-direction: column !important;
            .salary-wrapper {
                width: 100%;
            }
            .exp-wrapper {
                width: 100%;
            }
        }
    }
    .modal-footer {
        flex-wrap: wrap;
        justify-content: space-around;
    }
    .modal-footer .media-button {
        margin-bottom: 10px;
    }
    .modal-footer button:first-child {
        margin-right: 1px;
    }
}
@media (max-width: 370px) {
    .vacancy-application-modal .modal-footer .btn-default {
        min-width: 130px;
    }
    .modal-footer .btn_default {
        min-width: 120px;
    }
}
