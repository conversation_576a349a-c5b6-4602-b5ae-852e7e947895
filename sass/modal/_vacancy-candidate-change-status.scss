@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.candidate-add-in-vacancy {
    #cke_ckEditorAddVacancy,
    #cke_ckEditorCandidateAddVacancy,
    #cke_ckEditorTransferCandidate,
    #cke_ckEditorEditCandidateCommentChangeDate {
        .cke_inner .cke_top {
            display: none;
        }
    }

    .modal-body {
        gap: 16px;
        .item {
            display: flex;
            flex-direction: column;
            gap: 8px;
            &-title {
                font-size: $secondary-font-size;
                line-height: 18px;
                letter-spacing: 0.28px;
                color: $secondary-black;
                .send-from {
                    float: right;
                    margin-left: auto;
                }
                .create-template {
                    display: flex;
                    gap: 8px;
                    color: $main-green;
                    text-decoration: none;
                    cursor: pointer;
                    float: right;
                    margin-left: auto;
                    img {
                        width: 18px;
                        filter: $filter-semi-black;
                    }
                }
            }
            &-note {
                font-size: $secondary-font-size;
                color: $secondary-black;
            }

            .date-field-picker.empty {
                md-input-container .md-input {
                    border: 1px solid $red;
                }
            }
            .error-text {
                display: none;
            }
        }

        .email-notify {
            display: flex;
            flex-direction: column;
            padding: 16px;
            background-color: $pale-grey;
            border: 1px solid $border-grey;
            border-radius: 8px;
            &-arrow {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
                font-size: $main-font-size;
                font-weight: 600;
                letter-spacing: 0.32px;
                color: $main-black;
                img {
                    width: 20px;
                    filter: $filter-semi-black;
                    transition: 0.3s all;
                    &.arrowDown {
                        transform: rotate(180deg);
                    }
                }
            }

            &-wrapper {
                display: flex;
                flex-direction: column;
                gap: 16px;
                .alert-danger {
                    display: flex;
                    align-items: center;
                    div {
                        font-size: $main-font-size;
                        line-height: 20px;
                        letter-spacing: 0.32px;
                        span {
                            color: $red;
                        }
                        a {
                            color: $main-green;
                            transition: all 0.3s ease-in;

                            &:hover {
                                color: $yellow;
                            }
                        }
                    }
                }
            }
        }
        .google-meet {
            display: flex;
            flex-direction: column;
            position: relative;
            gap: 16px;
            padding: 9px;
            background-color: $pale-grey;
            border: 1px solid $border-grey;
            border-radius: 8px;
            &-checkbox {
                display: flex;
                flex-direction: column;
                gap: 8px;

                .disabled {
                    pointer-events: none;
                }

                checkbox-component {
                    width: 20px;
                    height: 20px;
                    margin-right: 8px;
                }
                span {
                    font-size: $main-font-size;
                    letter-spacing: 0.32px;
                    color: $main-black;
                    cursor: pointer;
                    &.disabled {
                        color: $semi-black;
                        pointer-events: none;
                    }
                }
            }
            &-wrapper {
                display: flex;
                flex-direction: column;
                gap: 8px;
                .item-title {
                    display: flex;
                    align-items: center;
                    i {
                        display: block;
                    }
                }
            }
            &-comment {
                textarea {
                    font-size: $main-font-size;
                    line-height: 20px;
                    letter-spacing: 0.32px;
                    padding: 12px;
                    color: $main-black;
                    &::placeholder {
                        color: $semi-black;
                    }
                }
            }
            .no-integration-overlay {
                position: absolute;
                z-index: 1;
                opacity: 0.4;
                height: 86%;
                width: 98%;
                right: 6px;
                top: 65px;
                background-color: $pale-grey;
            }

            .meet-error {
                z-index: 0;
                position: absolute;
                width: max-content;
                box-shadow: $main-box-shadow;
                top: 29%;
                background-color: $main-white;
                padding: 10px;
                border-radius: 10px;
                left: 14%;
            }
        }
        .google-meet-users {
            position: relative;
            margin-top: 15px;
            .repeat-meet-users {
                display: flex;
                flex-wrap: wrap;
                margin-top: 5px;
                .users-list {
                    height: 32px;
                    width: fit-content;
                    display: flex;
                    align-items: center;
                    background-color: #daece1;
                    border-radius: 5px;
                    padding: 10px;
                    color: #000000;
                    font-weight: 300;
                    font-size: 14px;
                    margin-right: 15px;
                    margin-top: 10px;
                    .delete-user {
                        -webkit-mask: url(/images/sprite/close-icon.svg) no-repeat 50% 50%;
                        background: #202021;
                        padding: 4px;
                        margin-left: 15px;
                    }
                }
            }
            .google-meet-dropdown {
                .select-ops.custom-new {
                    max-height: 200px;
                }
                width: 320px;
                input {
                    padding-left: 0;
                }
                .select-label.custom-new {
                    width: 320px;
                }
                .select-ops.custom-new.active {
                    width: 320px;
                }
            }
            label {
                text-decoration: none !important;
                color: #337ab7;
                margin: 15px 0 0 2px;
            }
            label:hover {
                color: #fdd14a;
            }
            .google-meet-error {
                position: absolute;
                width: max-content;
                box-shadow: 0px 0px 9px 6px darkgrey;
                top: 30px;
                background-color: whitesmoke;
                padding: 10px;
                border-radius: 10px;
                left: 15%;
            }
        }
    }
}

// вынести в отдельный файл (стили для директивы по файлам)
template-file-attach .file-attach {
    display: flex;
    flex-direction: column;
    gap: 8px;
    &-wrapper {
        display: flex;
        flex-direction: column;
        gap: 8px;
        &-items {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background-color: $border-grey;
            border-radius: 8px;
            a {
                cursor: pointer;
                font-size: $main-font-size;
                line-height: 20px;
                letter-spacing: 0.32px;
                color: $main-black;
            }
            img {
                width: 18px;
                filter: $filter-semi-black;
                transition: 0.3s all;
                &:hover {
                    filter: $filter-main-black;
                }
            }
        }
    }
    &-btn {
        display: flex;
        flex-direction: row;
        align-items: center;
        label {
            display: flex;
            align-items: center;
            margin-bottom: 0;
        }
        img {
            width: 18px;
            height: 18px;
            filter: $filter-semi-black;
            margin-right: 8px;
        }
        span {
            @include green-link();
            font-weight: 400;
        }
    }
    &-message {
        font-size: $secondary-font-size;
        color: $secondary-black;
        line-height: 16px;
    }
}
