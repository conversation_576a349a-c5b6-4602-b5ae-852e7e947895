@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.candidate-change-status-in-vacancy custom-select-email-templates .select-ops.custom-new.active {
    width: 100%;
    margin-left: 0px;
}

.candidate-change-status-in-vacancy .select-ops.custom-new.active {
    width: 91%;
    margin-left: 36px;
}

.candidate-change-status-in-vacancy {
    #cke_ckEditorOneCandidateInModal {
        .cke_top {
            display: none;
        }
    }

    /* .modal-header {
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;

        h4 {
            font-size: 22px !important;
            @include bold();
        }

        i {
            @include close-item();
        }
    } */

    .modal-body {
        gap: 16px;

        .google-meet {
            display: flex;
            flex-direction: column;
            gap: 16px;
            padding: 9px;
            background-color: $pale-grey;
            border: 1px solid $border-grey;
            border-radius: 8px;
            &-checkbox {
                display: flex;
                flex-direction: column;
                gap: 8px;

                .disabled {
                    pointer-events: none;
                }

                checkbox-component {
                    width: 20px;
                    height: 20px;
                    margin-right: 8px;
                }
                span {
                    font-size: $main-font-size;
                    letter-spacing: 0.32px;
                    color: $main-black;
                    cursor: pointer;
                    &.disabled {
                        color: $semi-black;
                        pointer-events: none;
                    }
                }
            }
            &-wrapper {
                display: flex;
                flex-direction: column;
                gap: 8px;
                .item-title {
                    display: flex;
                    align-items: center;
                    i {
                        display: block;
                    }
                }
            }
            &-comment {
                textarea {
                    font-size: $main-font-size;
                    line-height: 20px;
                    letter-spacing: 0.32px;
                    padding: 12px;
                    color: $main-black;
                    &::placeholder {
                        color: $semi-black;
                    }
                }
            }
        }

        .error-field {
            color: red;
            opacity: 0;
            margin-top: -5px;

            &.show-error-field {
                opacity: 1;
                transition: 0.3s all ease-in;
                margin-left: 12px;
            }
        }

        .tooltip-white-hint {
            top: 46px;
            bottom: inherit !important;
            min-width: 250px;
            word-break: break-word;
            padding: 10px 12px !important;
        }

        .meetComment {
            height: 150px;
            padding: 10px;
            width: 100%;
        }

        .meetComment::placeholder {
            opacity: 0.6;
        }

        .google-meet-users {
            margin-top: 15px;

            .repeat-meet-users {
                display: flex;
                flex-wrap: wrap;
                margin-top: 5px;

                .users-list {
                    height: 32px;
                    width: fit-content;
                    display: flex;
                    align-items: center;
                    background-color: #daece1;
                    border-radius: 5px;
                    padding: 10px;
                    color: #000000;
                    font-weight: 300;
                    font-size: 14px;
                    margin-right: 15px;
                    margin-top: 10px;

                    .delete-user {
                        -webkit-mask: url(/images/sprite/close-icon.svg) no-repeat 50% 50%;
                        background: #202021;
                        padding: 4px;
                        margin-left: 15px;
                    }
                }
            }

            .google-meet-dropdown {
                width: 320px;

                .select-label.custom-new {
                    width: 320px;
                }

                .select-ops.custom-new.active {
                    width: 320px;
                }
            }

            label {
                text-decoration: none !important;
                color: #337ab7;
                margin: 15px 0 0 2px;
            }

            label:hover {
                color: #fdd14a;
            }
        }

        .google-active {
            color: #cccccc;

            label:hover:before {
                border-color: #cccccc !important;
            }
        }

        .checkbox {
            display: none;

            + label {
                position: relative;
                display: block;
                padding-left: 24px;
                font-size: 14px;
                line-height: 1.5;
                cursor: pointer;
                font-weight: 400;

                &:before {
                    content: '';
                    display: block;
                    position: absolute;
                    top: 1px;
                    left: 0;
                    width: 16px;
                    height: 16px;
                    border: 1px solid #d6e0e7;
                    border-radius: 3px;
                    transition: all 295ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
                }

                &:after {
                    content: '';
                    display: block;
                    position: absolute;
                    top: 4px;
                    left: 6px;
                    width: 4px;
                    height: 8px;
                    border-bottom: 2px solid #ffffff;
                    border-right: 2px solid #ffffff;
                    -webkit-transform: rotate(45deg);
                    transform: rotate(45deg);
                    opacity: 0;
                    transition: opacity 295ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
                }

                &:hover:before {
                    border-color: #47ab43;
                }
            }

            &:checked + label {
                &:before {
                    border-color: #47ab43;
                    background: #47ab43;
                }

                &:after {
                    opacity: 1;
                }
            }
        }

        .item {
            &-title {
                font-size: $secondary-font-size;
                line-height: 18px;
                letter-spacing: 0.28px;
                color: $secondary-black;
                .send-from {
                    float: right;
                    margin-left: auto;
                }
            }

            &-note {
                font-size: $secondary-font-size;
                color: $secondary-black;
            }
            /*
            .standartTextarea {
                width: 100%;
                height: 150px;
                margin-top: 10px;

                resize: none;
            }

            select {
                @include select-styling();
            }

            input {
                @include input-styling();

                &:focus {
                    outline: none;
                }
            }

            file-attach {
                margin-top: 11px;
            }

            .attachFile {
                @include link-styling();
                margin-top: 10px;
                font-weight: 500;
            } */
        }

        .input-group {
            .candidatePicker {
                .md-icon-float {
                    width: 100%;
                }
            }
        }
    }

    .modal-footer {
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;

        .flex-position {
            display: flex;
            flex-direction: row;

            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            @include button-styling();

            &.margin-mobile {
                margin-left: 10px;
            }
        }

        .accept {
            color: $theme-white;
            background-color: $theme-dark-green;

            &:hover {
                background-color: $theme-light-green-hover;
            }
        }

        .cancel {
            color: #585859;
            background-color: #e3e3e3;

            &:hover {
                background-color: $theme-light-grey-hover;
            }
        }
    }
}

.infoMeet {
    top: -47px !important;
    left: 320px !important;
}

@media (max-width: 575px) {
    .candidate-add-in-vacancy {
        .modal-footer {
            div {
                margin-top: 5px;
                margin-left: 0 !important;
            }
        }

        .meet-label {
            margin-left: 13px;
        }
    }
}

@media (max-width: 475px) {
    .margin-mobile {
        margin-top: 10px;
    }
    .modal-footer {
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
    }
    .candidate-change-status-in-vacancy .modal-footer .flex-position {
        flex-direction: column;
    }
}
