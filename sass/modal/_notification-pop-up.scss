.notification-pop-up {
    .modal-dialog {
        width: auto;
        max-width: none;

        .modal-content {
            background: transparent;
            border: none !important;

            .modal-body {
                max-width: 740px;
                max-height: none;
                height: auto;
                padding: 0;
                margin: 0;
                border-radius: 12px;
                border: 1px solid rgba(253, 183, 78, 1);
                background: #fff9f4;

                .modal-header {
                    position: relative;
                    width: 100%;
                    min-height: 100px;
                    background-color: rgba(253, 183, 78, 1);
                    border-radius: 0;

                    .modal-title {
                        margin-top: 25px;
                        color: #fff;
                        font-style: normal;
                        font-weight: 700;
                        font-size: 22px;
                        line-height: 175%;
                    }

                    .close-icon {
                        opacity: 0;
                        filter: invert(99%) sepia(2%) saturate(148%) hue-rotate(243deg) brightness(115%) contrast(100%);
                    }
                }

                .modal-info {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;

                    .modal-img {
                        width: 100%;
                        height: 100%;
                        min-height: 120px;
                        object-fit: cover;
                    }

                    .modal-msg {
                        margin: 0;
                        padding: 30px 47px 40px 47px;

                        font-style: normal;
                        font-weight: 400;
                        color: $secondary-black;
                        font-size: 16px;
                        line-height: 130%;
                    }
                }

                .buttons-container {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-evenly;
                    margin-bottom: 40px;
                    .btn_default {
                        min-width: 65px;
                        width: 157px;
                    }
                    .btn-orange {
                        background-color: #ffc100;
                        &:hover {
                            background-color: #e6ae00;
                        }
                        &:active {
                            box-shadow: inset 0 6px 10px 1px rgba(179, 135, 0, 0.75);
                        }
                    }
                }

                .informed-btn {
                    margin: 0 auto 45px !important;
                }
            }

            .bird-img {
                pointer-events: none;
                position: absolute;
                width: 108px;
                left: calc(50% - 108px / 2);
                top: -50px;
                z-index: 1;
            }
        }

        &:hover {
            .modal-content .modal-body .modal-header .close-icon {
                opacity: 1;
            }
        }
    }
}

@media (max-width: 600px) {
    .notification-pop-up {
        .modal-dialog {
            .modal-content {
                margin-top: 100px;
                padding-bottom: 100px;

                .modal-body {
                    .modal-header {
                        .modal-title {
                            font-size: 16px;
                        }

                        .close-icon {
                            opacity: 1;
                        }
                    }

                    .modal-info {
                        .modal-msg {
                            font-size: 11px;
                            line-height: 160%;
                            padding: 20px 35px;
                        }
                    }

                    .buttons-container {
                        margin-bottom: 0;

                        button-component {
                            margin-bottom: 20px;
                        }
                        .btn_default {
                            min-width: 100px;
                            margin: 0 25px 20px !important;
                            font-size: 11px;
                            width: 148px;
                        }
                    }

                    .informed-btn {
                        margin: 0 auto 20px !important;
                    }
                }
            }
        }
    }
}
