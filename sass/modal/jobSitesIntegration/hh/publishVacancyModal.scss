.publish-hh-popup {
    &.modal {
        align-items: flex-start;
    }
    .modal-dialog {
        max-width: 704px;
        max-height: 850px;
        width: 100%;
        .modal-content {
            border-radius: 11px;
            border: 0;
            .publish-vacancy-hh {
                .modal-header {
                    .title {
                        color: #202021;
                        font-size: 24px;
                    }
                }
                .modal-body {
                    padding: 25px;
                    // fix for display dropdown correct in the end of the pop-up
                    max-height: inherit;
                    overflow: inherit;
                    .row_wrapper {
                        display: flex;
                        justify-content: space-between;
                        flex-wrap: wrap;
                        .item {
                            display: inline-flex;
                            width: 48%;
                            flex-direction: column;
                            label {
                                color: #333333;
                                .mandatory-star {
                                    color: #ef5350;
                                }
                                .info-icon {
                                    position: relative;
                                    top: -6px;
                                    left: 2px;
                                }
                            }
                            label + input[type='text'],
                            input[type='email'],
                            input[type='number'] {
                                border: 1px solid #cccccc;
                                border-radius: 5px;
                                color: #555555;
                                height: 34px;
                                padding: 12px;
                                &:focus,
                                &:active {
                                    outline: none;
                                }
                                &::placeholder {
                                    color: #cccccc;
                                }
                                &.error {
                                    border: 1px solid red;
                                }
                            }
                            .inputs {
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                input[type='text'] {
                                    border: 1px solid #cccccc;
                                    border-radius: 5px;
                                    color: #555555;
                                    height: 34px;
                                    padding: 12px;
                                    width: 25%;
                                    &:focus,
                                    &:active {
                                        outline: none;
                                    }
                                    &::placeholder {
                                        color: #cccccc;
                                    }
                                    &.error {
                                        border: 1px solid red;
                                    }
                                    &:last-child {
                                        width: 45%;
                                    }
                                }
                                input[type='number'] {
                                    width: 30%;
                                }
                            }

                            custom-select-with-autocomplete {
                                position: relative;
                                .select-label.custom-new {
                                    .select-label-text {
                                        input::placeholder {
                                            border-color: #cccccc;
                                            border-right: 0;

                                            color: #999;
                                        }
                                    }
                                }
                            }

                            custom-select-new,
                            custom-select-with-checkboxes,
                            custom-select-with-autocomplete {
                                .select-label.custom-new {
                                    width: 100%;
                                }
                                .select-ops.custom-new.active {
                                    width: 100%;
                                }
                                .select-label.custom-new.disabled {
                                    background-color: transparent;
                                    .select-label-text {
                                        color: #cccccc;
                                    }
                                }
                            }
                            custom-select-new.salary_select {
                                width: 30%;
                            }
                            custom-select-new.error,
                            custom-select-with-checkboxes.error,
                            custom-select-with-autocomplete.error {
                                .select-label.custom-new .select-label-text {
                                    border-color: red;
                                }
                                .select-label.custom-new .select-caret {
                                    border-color: red;
                                }
                            }
                            #s2id_1_city_container {
                                margin-top: 20px;
                            }
                            .disabled-input,
                            .select2-container.disabled-input {
                                .select-label.custom-new .select-label-text,
                                .select-label.custom-new .select-caret {
                                    border-color: rgba(204, 204, 204, 0.5);
                                }
                                .select-label.custom-new .select-caret i {
                                    color: #cccccc;
                                }
                                .select2-choice {
                                    border-color: rgba(204, 204, 204, 0.5);
                                    background-color: transparent;
                                    > .select2-chosen {
                                        color: #cccccc;
                                    }
                                    .select2-arrow {
                                        b {
                                            mask: url('../../../../images/sprite/vector.svg') no-repeat 50% 50%;
                                            background: #cccccc none;
                                        }
                                    }
                                }
                            }
                            .select2-container.error {
                                .select2-choice {
                                    border-color: red;
                                }
                            }
                        }
                        .additional-switcher {
                            display: flex;
                            flex-direction: row;
                            justify-content: space-between;
                            background-color: #f7f7f7;
                            height: 40px;
                            border-left: 2px solid #999;
                            align-items: center;
                            cursor: pointer;
                            padding: 15px;
                            i.fa {
                                color: #999999;
                            }
                            &:hover {
                                i.fa {
                                    color: darken(#999999, 5%);
                                }
                            }
                        }
                        .margin-top {
                            margin-top: 20px;
                        }
                        .full-width {
                            width: 100%;
                        }
                        .flex {
                            display: flex;
                            align-items: center;
                        }
                        .specifyContacts {
                            flex-direction: row;
                            input[type='checkbox'] {
                                width: 16px;
                                height: 16px;
                                display: block;
                                margin-right: 7px;
                                margin-top: 1px;
                                float: left;
                                position: relative;
                                cursor: pointer;
                                &:after {
                                    content: '';
                                    vertical-align: middle;
                                    text-align: center;
                                    line-height: 16px;
                                    position: absolute;
                                    cursor: pointer;
                                    height: 15px;
                                    width: 15px;
                                    left: 0;
                                    top: 0;
                                    font-size: 12px;
                                }
                                &:checked:after {
                                    background: #00b549;
                                    content: '\2714';
                                    border-radius: 2px;
                                    color: #fff;
                                }
                            }
                        }
                        .bold {
                            font-weight: 500;
                        }
                    }
                    .mce-statusbar {
                        display: none;
                    }
                }
                .modal-footer {
                    button:first-child {
                        margin-right: 25px;
                    }
                }
            }
        }
    }
}
