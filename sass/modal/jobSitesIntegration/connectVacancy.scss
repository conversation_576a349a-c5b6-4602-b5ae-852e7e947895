.connect-vacancy {
    .modal-body {
        gap: 16px;
        .item {
            display: flex;
            flex-direction: column;
            gap: 8px;

            &-title {
                font-size: $secondary-font-size;
                line-height: 18px;
                letter-spacing: 0.28px;
                color: $secondary-black;
            }
        }
        .form-control {
            transition: all 0.3s;

            &.error-input {
                border: 1px solid red;
            }
        }

        .text-error {
            color: $red;
        }
    }
}
