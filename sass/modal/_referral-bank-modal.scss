.ca-bank-payments-popup {
    .modal-content {
        border-radius: 10px;
    }
    .referral-bank-modal {
        border-radius: 5px;
        .modal-header {
            & > h4 {
                color: #666;
                @include bold();
                span {
                    color: #000;
                }
            }
        }
        .modal-body {
            padding: 25px;
            .user-data {
                border: 1px solid #3f3f3f;
                width: 100%;
                padding: 0;
                border-radius: 0;
                .user-table-history {
                    width: 100%;
                    thead {
                        display: block;
                        width: 100%;
                        tr {
                            display: table;
                            width: 100%;

                            table-layout: fixed;

                            background-color: #e3f2dd;
                            th {
                                padding: 5px;
                                position: relative;

                                text-align: center;
                                vertical-align: middle;
                                background-color: #ddd;
                                border-right: 1px solid #3f3f3f;
                                border-bottom: 1px solid #3f3f3f;

                                &:hover {
                                    cursor: default;
                                }
                                &:nth-child(1) {
                                    width: 33.3%;
                                }
                                &:nth-child(2) {
                                    width: 33.3%;
                                }
                                &:nth-child(3) {
                                    width: 33.3%;
                                    border-right: none;
                                }
                            }
                        }
                    }
                    &.with-scroll {
                        thead,
                        tbody {
                            tr {
                                th,
                                td {
                                    &:nth-child(1) {
                                        width: 33%;
                                    }
                                    &:nth-child(2) {
                                        width: 33%;
                                    }
                                    &:nth-child(3) {
                                        width: 36.6%;
                                        border-right: none;
                                    }
                                }
                                td {
                                    &:nth-child(3) {
                                        border-right: 1px solid #333;
                                    }
                                }
                            }
                        }
                    }
                    tbody {
                        display: block;
                        width: 100%;
                        max-height: 138px;
                        background: #fff;
                        overflow-x: auto;
                        tr {
                            display: table;
                            width: 100%;

                            table-layout: fixed;

                            background-color: #fff;
                            td {
                                text-align: center;
                                padding: 4px;
                                color: #000;
                                border-right: 1px solid #3f3f3f;
                                border-bottom: 1px solid #3f3f3f;
                                &:nth-child(1) {
                                    width: 33.3%;
                                }
                                &:nth-child(2) {
                                    width: 33.3%;
                                }
                                &:nth-child(3) {
                                    width: 33.3%;
                                    border-right: none;
                                }
                                .date {
                                    text-decoration: underline;
                                    display: inline-block;
                                    width: 63%;
                                    outline: none;
                                    text-align: right;
                                    margin-right: 10px;
                                    background: transparent;
                                    border: none;
                                    height: 16px;
                                    &.not-set {
                                        border-bottom: 1px solid #666;
                                    }
                                }
                                .cbx {
                                    display: inline-block;
                                    vertical-align: middle;
                                    width: 15px;
                                    height: 15px;
                                    border-radius: 2px;
                                    border: 1px solid #999;
                                    background-color: white;
                                    cursor: pointer;
                                    &.checked {
                                        background: white url('/images/sprite/check-mark.png') center no-repeat;
                                        background-size: 12px;
                                    }
                                }
                            }
                            &:nth-child(odd) {
                                background-color: #f1f0f0;
                            }
                            &:hover {
                                cursor: default;
                            }
                        }
                        &:last-child {
                            td {
                                border-bottom: none;
                            }
                        }
                    }
                }
            }
        }
        .modal-footer {
            .accept {
                margin-left: 27px;
                @include button-animated();
            }
            .btn-transparent {
                @include button-animated($theme-btn-back, #333333, $theme-btn-back-hover, white);
                border: 1px solid #333333;
            }
        }
    }
}
