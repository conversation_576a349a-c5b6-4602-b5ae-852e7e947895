.free-paid-users {
    letter-spacing: 0.2px;
    .modal-header {
        h3 {
            font-size: 18px;
            letter-spacing: 1.1px;
            margin: 0;
        }
        .header-close {
            position: absolute;
            top: 14px;
            right: 11px;
            width: 14px;
            height: 14px;
            cursor: pointer;
        }
    }
    .padding-en {
        padding: 20px 15px;
    }
    .modal-body {
        padding: 15px 20px;
        overflow: hidden;
        display: inline-table;
        width: 100%;
        p {
            text-indent: 7px;
            margin: 0;

            &:nth-child(5) {
                margin-top: 10px;
            }
        }
        img {
            margin-top: 15px;
        }
        .block_clear {
            display: flex;
            align-items: center;
            margin: 20px auto 0;
            justify-content: center;

            .itsClear {
                padding-left: 20px;
                width: 15px;
                height: 16px;

                background: url(../../images/sprite/sprite-checkbox.png) no-repeat;
                margin-bottom: 0;
                cursor: pointer;
                input {
                    display: none;
                }
            }
        }
    }
    .modal-footer {
        button {
            @include btn-primary-new();

            letter-spacing: 1px;
            min-width: 160px;
            &.cancel {
                @include btn-transparent();

                padding: 5px 29px;
                font-size: 14px;

                color: #666;
                border: 1px solid #999;
                background-color: transparent;
                font-weight: 500;
                min-width: 168px;
                &:hover {
                    color: #fff;
                    border-color: #666;
                    background-color: #666;
                }
                &:active {
                    box-shadow: inset 0 3px 4px 0 rgba(0, 0, 0, 0.45);
                }
            }
            &.accent {
                font-size: 14px;
                font-weight: 500;

                padding: 5px 15px;

                text-decoration: none;

                color: $theme-white;
                border-radius: 23px;
                background-color: $theme-green;
                margin-left: 15px;
                min-width: 168px;
                &:hover {
                    color: $theme-white;
                    background-color: $theme-green-hover;
                }
                &:active {
                    box-shadow: inset 0 3px 4px 0 rgba(0, 0, 0, 0.45);
                }
            }
        }
    }
}
.free-paid-users-modal {
    .modal-dialog {
        width: 630px;
        .modal-content {
            border-radius: 10px;
        }
    }
}
@media screen and (max-width: 991px) {
    .free-paid-users-modal {
        align-items: flex-start !important;
    }
}
@media screen and (max-width: 440px) {
    .free-paid-users .modal-footer {
        display: flex;
        justify-content: space-around;
        padding: 20px 0;
        button.cancel {
            min-width: 120px;
        }
    }
}
