.extra-width {
    .modal-dialog {
    }
    .modal-content {
        border-radius: 5px;
        width: 750px;
    }
}
.modal-border-radius {
    .modal-content {
        border-radius: 5px;
    }
}
.vacancy-vcv {
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    background-color: #fff;
    .modal-header {
        font-size: 22px !important;
        font-size: 22px !important;
        h4 {
            font-size: 22px;
            line-height: 19px;
        }
        i {
            position: absolute;
            top: 10px;
            right: 12px;
            &:hover {
                cursor: pointer;
            }
        }
    }
    .modal-body {
        .body__intro {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            .intro__title {
                display: block;

                font-size: 14px;
                color: #000000;
                margin-bottom: 20px;
                text-align: left;
                line-height: 11px;
            }
            .custom-drop-down {
                width: 270px;
                margin-bottom: 20px;
                .select-ops.custom-new,
                .select-label.custom-new {
                    width: 100%;
                    text-align: left;
                }
            }
        }
        .body__main {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            .wrapper {
                display: flex;
                align-items: center;
                flex-direction: column;
                justify-content: flex-start;
                border: 1px solid #ccc;
                padding: 20px;
                border-left: none;
                border-right: none;
                width: 100%;
                max-height: 380px;
                overflow-y: auto;
                .candidate {
                    min-height: 31px;
                    display: flex;
                    width: 100%;
                    align-items: center;
                    justify-content: flex-end;
                    margin-bottom: 20px;
                    .name {
                        display: inline-block;

                        color: #333333;
                        margin-right: 10px;
                        line-height: 11px;
                    }
                    .contacts {
                        display: inline-flex;
                        .contact {
                            position: relative;
                            padding: 9px;
                            border: 1px solid #00b549;

                            line-height: 11px;
                            font-size: 14px;
                            border-radius: 5px;
                            min-width: 220px;
                            text-align: left;
                            margin-right: 10px;
                            &:after {
                                content: url(/images/sprite/check-mark.png);
                                right: 7px;
                                top: calc(50% - 6px);
                                position: absolute;
                            }
                            &.error {
                                border: 1px solid #ff0000;
                                top: calc(50% - 5.5px);
                                &:after {
                                    content: url(/images/sprite/remove-danger.png);
                                }
                            }
                            &:last-child {
                                margin-right: 0;
                            }
                        }
                    }
                    &:last-child {
                        margin-bottom: 0;
                        margin-right: 0;
                    }
                }
            }
            .error-msg {
                display: block;
                text-align: left;
                margin-top: 20px;
                span {
                    color: #ff0000;
                    font-size: 14px;
                }
            }
        }
    }
    .modal-footer {
        .danger {
            @include button-animated(#f33, white, #c00);

            height: 32px;
        }
        .transparent {
            font-size: 14px;

            height: 32px;
            margin-right: 25px;
            padding: 0 30px;

            transition: 0.2s all ease;

            color: #333;
            border: 1px solid #333;
            border-radius: 25px;
            outline: none;
            background-color: #fff;
            min-width: 160px;
            &:hover {
                color: #fff;
                background-color: #666;
            }
        }
        .btn-primary {
            @include btn-primary-new();

            height: 32px;
            padding: 0 20px;
            min-width: 160px;
            &:hover {
                cursor: pointer;
                text-decoration: none;

                color: #fff;
                background-color: #019b3f;
            }
        }
    }
}

@media (max-width: 770px) {
    .send-email {
        max-height: 550px;
    }
    .send-email .modal-body {
        overflow: scroll;
        height: 350px;
        padding: 0 20px;
        margin: 0;
    }
    .vacancy-vcv .modal-body .body__main {
        display: flex;
    }
    .extra-width .modal-content {
        width: auto;
    }
    .vacancy-vcv .modal-body .body__intro {
        flex-direction: column;
    }
    .vacancy-vcv .modal-body .body__intro .form-title-main {
        font-size: 16px;
        margin-bottom: 10px;
    }

    .vacancy-vcv .modal-body .body__main .wrapper .candidate {
        flex-direction: column;
    }
    .vacancy-vcv .modal-body .body__main .wrapper .candidate .name {
        margin-bottom: 10px;
    }
}

@media (max-width: 576px) {
    .vacancy-vcv .modal-body .body__main .wrapper .candidate .contacts {
        flex-direction: column;
    }
    .vacancy-vcv .modal-body {
        padding: 20px;
    }
    .vacancy-vcv .modal-body .body__main .wrapper .candidate .contacts .contact {
        margin-right: 0;
        margin-bottom: 5px;
    }
    mail-to {
        float: none;
        margin-left: 20px;
    }
}

@media (max-width: 356px) {
    .vacancy-vcv .modal-body .body__intro .form-title-main {
        font-size: 14px;
    }
    .vacancy-vcv .modal-body .body__main .wrapper .candidate .contacts .contact {
        max-width: 200px;
    }
    .vacancy-vcv .modal-body .body__intro .custom-drop-down {
        max-width: 250px;
    }
    .vacancy-vcv .modal-body {
        margin: 10px;
    }
}
@media (max-width: 330px) {
    .vacancy-vcv .modal-body {
        margin: 5px;
    }
}
