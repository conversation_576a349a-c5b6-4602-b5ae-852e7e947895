.block-company-public-vacancy.block-public {
    position: relative;
    overflow-x: hidden;
    min-height: 100vh;
    background-color: #e3f2dd;
    .no-description-message {
        max-width: 410px;
        margin: 110px auto 180px;

        border: 10px solid #69d46d;
        border-radius: 25px;
        & .circles {
            padding: 11px 20px;

            text-align: right;

            border-bottom: 5px solid #69d46d;
            .circle {
                display: inline-block;
                width: 18px;
                height: 18px;
                margin-right: 7px;

                border-radius: 50%;
                background-color: #69d46d;
                &:last-child {
                    margin-right: 0;
                }
            }
        }
        & .message {
            font-size: 17px;

            padding: 20px 15px;
            & .vacancy-position {
                font-size: 18px;
                font-weight: 600;
            }
            & img {
                display: block;
                margin: 25px auto 0;
            }
        }
    }

    .cleverstaff {
        margin: 20px 0 20px;

        text-align: center;
        a {
            color: #202021;
            text-decoration: none;
            &:hover {
                color: #202021;
            }
        }
        span {
            font-size: 15px;
            line-height: 20px;

            display: inline-block;
            margin: 0;
            padding: 0;
        }
        img {
            display: inline-block;
            width: 20px;
            height: 20px;
        }
    }
}

.vacancy-default-template {
    .content {
        display: flex;
    }

    .group-wrapper {
        display: flex;
        align-items: center;
    }

    .back-button-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .back-button {
            color: #0096f5;
            cursor: pointer;
            display: flex;
            align-items: center;
            margin: 0 10px 20px;

            &__img {
                margin-right: 10px;
            }

            &__label {
                line-height: 14px;
            }
        }
    }

    .apply-now {
        display: flex;
        justify-content: center;
        margin-top: 24px;
    }

    .public-vacancy-name {
        color: #0096f5;
        font-weight: 500 !important;
        font-size: 16px;
        line-height: 16px;
    }

    .vacancy {
        position: relative;
        padding: 34px;
        margin-bottom: 20px;
        height: 100%;

        &__last-update {
            color: #828282;
            font-size: 14px;
            position: absolute;
            right: 0;
            top: -20px;
        }

        &__edit {
            position: absolute;
            top: 15px;
            right: 10px;
            cursor: pointer;
            &:hover svg path {
                fill: #0eb953;
            }
        }

        &__main-info {
            .group-wrapper {
                flex-wrap: wrap;
            }
        }

        &__hot-icon {
            img {
                margin-bottom: 6px;
                margin-right: 5px;
            }
        }

        &__name {
            margin-right: 15px;
        }

        &__industry {
            margin-right: 15px;
            height: 20px;
        }

        &__location {
            line-height: 14px;
            color: #828282;
        }
        &__employment-type {
            margin: 5px 0;
            display: flex;

            img {
                margin-right: 5px;
            }
        }

        &__salary {
            display: flex;
            color: #202021;

            img {
                margin-right: 5px;
            }
        }
        &__currency {
            margin-left: 5px;
            color: #828282;
        }
        &__requirements {
            margin-top: 20px;

            .requirements-title {
            }

            .requirements-wrapper {
                display: flex;
            }

            .requirements-items {
                margin-top: 5px;

                &:first-child {
                    margin-right: 20px;
                }
            }

            .requirements-item {
                padding-bottom: 5px;

                &:last-child {
                    padding-bottom: 0;
                }

                img {
                    margin-right: 5px;
                }
            }
        }

        &__description {
            margin-top: 20px;

            .description-text {
                word-break: break-word;
                margin-top: 5px;
            }
        }
    }

    .similar-vacancies {
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        width: 20%;
        max-width: 250px;

        &__title {
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 10px;
        }

        &__items {
        }

        &__item {
            padding: 15px 10px;
            text-decoration: none;
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;
        }

        &__hot-icon {
            margin-right: 5px;
        }

        &__name {
        }

        &__employmentType {
            margin-top: 5px;
            color: #000000;
            display: flex !important;

            img {
                margin-right: 5px;
            }
        }

        &__industry {
            min-width: 40px;
            color: #000000;
            margin-left: 10px;
        }

        &__location {
            margin-top: 5px;
            color: #828282;
        }
    }

    .no-vacancy-wrapper {
        .no-description-message {
            display: inline-block;
            max-width: none;
            margin: 0;
            padding: 4% 10.5% 10%;

            border: none;
            border-radius: 15px;
            background-color: rgba(255, 255, 255, 0.3);
            display: flex;
            justify-content: center;
            align-items: center;

            .message {
                background-color: #faded8;
                border-left: 2px solid #e75a3a;
                padding: 30px;
                font-size: 15px;
                display: flex;
                align-items: center;

                .vacancy-position {
                    font-weight: 600;
                }

                img {
                    margin-left: 6px;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .vacancy-default-template {
        .content {
            flex-direction: column;
        }

        .vacancy {
            &__requirements {
                .requirements-wrapper {
                    justify-content: space-between;
                }
            }
        }

        .similar-vacancies {
            margin-left: 0;
            width: 100%;
            max-width: 100%;

            &__title {
                text-align: center;
                margin: 20px 0;
            }

            &__items {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
            }

            &__item {
                max-width: 32%;
            }
        }
    }
}

@media (max-width: 535px) {
    .vacancy-default-template {
        .vacancy {
            &__edit {
                display: none;
            }

            &__requirements {
                .requirements-wrapper {
                    flex-direction: column;
                }
            }
        }

        .similar-vacancies {
            &__item {
                max-width: 49%;
            }
        }
    }
}

@media (max-width: 400px) {
    .vacancy-default-template {
        .vacancy {
            padding: 15px;
        }

        .similar-vacancies {
            &__item {
                max-width: 100%;
            }
        }

        .apply-now {
            margin: 15px 0 20px;
        }
    }
}
