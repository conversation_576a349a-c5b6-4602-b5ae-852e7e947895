.invite-reg-custom-dropdown {
    &.select2-drop {
        font-family: 'SF UI Display';
        min-height: 40px;
        font-size: 14px;
        line-height: 1.5;
        color: #a0aab8;
        background: #ffffff;
        overflow: hidden;
        outline: none;
        border-radius: 20px;
        padding: 0;
        border: 1px solid #47ab43;
        .select2-search {
            display: none;
        }
        .select2-results {
            padding: 0;
            margin: 0;
            border-radius: 20px;
        }
        .select2-results-dept-0 {
            padding: 7px 19px;
            background-color: #ffffff;
            color: #838287;
            border-top: 1px solid #eff2f7;
            transition: all 0.3s;
            &:first-of-type {
                border: none;
            }
            &:hover .flag-text {
                color: #47ab43 !important;
            }
        }
    }
}

.invite-reg-wrapper {
    & .viewport-height {
        height: 100vh;
    }

    .header--simplify {
        position: fixed;
        top: 41px;
        left: 30px;
        z-index: 11;
        padding: 0;
    }
    .container-title {
        text-align: center;
        font-size: 40px;
        line-height: 60px;
        font-family: 'SF UI Display Medium';
        margin-bottom: 10%;
    }
    .container-position {
        width: 45%;
        margin-top: 2%;
        margin-left: 55%;
        margin-bottom: 12px;
        &.alreadyAccountMargin {
            margin-top: 25%;
        }
    }
    .select2-container {
        &.select2-container-active {
            .select2-choices,
            .select2-choice {
                box-shadow: none !important;
            }
        }
        &.countryCustom {
            width: 40%;
            .select2-arrow {
                border: none;
                background-color: transparent;
                top: 3px;
                right: 3px;
            }
            .select2-choice {
                min-height: 40px;
                padding: 8px 0 0 20px;
                font-size: 14px;
                line-height: 1.5;
                color: #a0aab8;
                background: #eff2f7;
                border: 1px solid #d6e0e7;
                border-radius: 20px;
                transition: all 180ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
            }
            &.select2-dropdown-open {
                .select2-choice {
                    outline: none;
                    background-color: #ffffff;
                    border: 1px solid #47ab43;
                }
            }
        }
    }

    .password-status {
        display: flex;
        align-items: center;
        justify-content: center;
        .state-show {
            margin-bottom: 1px;
        }
        .state-show,
        .state-hide {
            cursor: pointer;
        }
    }

    .error-field {
        border: 1px solid #e75a3a !important;
        background: rgba(231, 90, 58, 0.05) !important;
        color: #a0aab8 !important;
    }
    .error-text {
        color: #e75a3a;
        opacity: 0;
        padding-left: 20px;
        &.phone-position {
            padding-left: calc(30% + 20px);
        }
        &.error-show {
            opacity: 1;
        }
    }

    .form {
        input {
            outline: none;
        }
        margin-bottom: 40px;

        fieldset {
            margin-bottom: 6px;
        }

        .form-role,
        .form-email {
            width: calc(50% - 5px);
            pointer-events: none;
            overflow: hidden;
            user-select: none;
            margin-bottom: 6px;
        }
        .form__label {
            display: block;
            margin-bottom: 2px;
            font-family: 'SF UI Display Medium';
            font-size: 14px;
            line-height: 1.5;
        }

        .form__indicator {
            font-weight: 700;
            color: #ff0000;
        }

        .form__error {
            padding-left: 20px;
            display: block;
            height: 0;
            opacity: 0;
            font-size: 14px;
        }

        .form__input {
            width: 100%;
            padding: 9px 20px 8px;
            font-size: 14px;
            line-height: 1.5;
            color: #a0aab8;
            background: #eff2f7;
            border: 1px solid #d6e0e7;
            border-radius: 20px;
            box-shadow: none;
            transition: background 180ms cubic-bezier(0.455, 0.03, 0.515, 0.955),
                border-color 180ms cubic-bezier(0.455, 0.03, 0.515, 0.955);

            &.tel--input {
                margin-left: 10px;
            }
            &.invalid {
                color: #e75a3a;
                border: 1px solid #e75a3a;
                background: #faded8;

                + .form__error {
                    opacity: 1;
                }
            }

            &:not(:placeholder-shown):not(.invalid) {
                color: #312e37;
                background: #ffffff;
            }

            &:focus {
                color: #312e37;
                background: #ffffff;
                border-color: #47ab43;
            }

            &.placeholder {
                color: #a0aab8;
                opacity: 1;
            }

            &[type='password'] {
                letter-spacing: 0.5px;
                color: #838287;

                &::placeholder {
                    letter-spacing: 0;
                }
            }
        }

        .form__select .choices {
            width: 100%;
        }

        .form__area {
            min-height: 120px;
        }

        .form__submit {
            padding-top: 10px;
            margin-bottom: 0;
            text-align: center;

            button {
                outline: none;
            }
            button[disabled] {
                background: #a0aab8;
                border: 1px solid #a0aab8;
                color: #312e37;
                &:hover {
                    box-shadow: none;
                    cursor: not-allowed;
                }
            }
        }

        .one-line-group {
            display: flex;
        }

        .form__description {
            display: block;
            margin-bottom: 12px;
            text-align: center;
            font-size: 14px;
            line-height: 1.5;
        }
        .display-flex {
            display: flex;
            justify-content: space-between;
        }
        .pass--input {
            width: calc(50% - 20px);
        }
        .form__group {
            small {
                display: block;
                margin-top: 6px;
                margin-bottom: 22px;
                font-family: 'SF UI Display Medium';
                font-size: 12px;
                line-height: 1.5;
                color: #838287;
                opacity: 0.6;
            }

            .form__error {
                color: #e75a3a;
            }
        }

        .form__group--plain {
            margin-bottom: 0;

            + .form__submit {
                padding-top: 32px;
            }
        }

        .form__next {
            padding-top: 22px;
            text-align: center;
            font-family: 'SF UI Display Medium';
            font-size: 16px;
            line-height: 21px;

            a {
                display: block;
                color: #2f80ed;
            }
        }
    }

    .banner {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 10;
        width: calc(((100vw - 970px) / 2) + (970px * (5 / 12)) - 30px);
        background: #312e37;
        border-radius: 0 0 400px 0;
        padding: 0 30px;
        height: 100%;
        color: #ffffff;
        overflow: hidden;
        display: flex;
        align-items: center;

        &:before {
            content: '';
            display: block;
            position: absolute;
            bottom: 0;
            left: 50%;
            z-index: -1;
            width: 1024px;
            height: 100%;
            margin-left: -512px;
            background: #312e37;
            border-radius: 600px 600px 0 0;
        }

        .banner__content {
            width: 100%;
            text-align: center;
            margin-bottom: 20%;
        }

        .banner__content--shift {
            padding-left: 30px;
        }

        .banner__title {
            margin-bottom: 32px;
            font-family: 'SF UI Display Medium';
            font-size: 34px;
            line-height: 60px;
        }

        .banner__text {
            position: relative;
            top: 80px;
            max-width: 374px;
            margin-left: auto;
            margin-right: auto;
            font-family: 'SF UI Display Medium';
            text-align: left;
            opacity: 0;

            + .banner__text {
                margin-top: 24px;
            }
        }

        .btn {
            opacity: 0;
        }
    }

    .decoration {
        width: 332px;
        filter: drop-shadow(4px 6px 10px rgba(40, 40, 40, 0.5));

        path {
            fill: #312e37;
        }
    }

    .decoration-wrapper {
        position: absolute;
        z-index: -1;
    }

    .decoration--first {
        display: block;
        top: 10%;
        right: 30%;
    }

    .decoration--second {
        top: 25%;
        left: -30%;
        transform: rotate(28.19deg);
    }

    .decoration--third {
        bottom: 0%;
        right: 0%;
        transform: rotate(60deg);
    }

    .decoration--fourth {
        bottom: -30%;
        left: -40%;
        transform: rotate(10.45deg);
    }

    .btn {
        outline: none;
        position: relative;
        display: inline-block;
        min-width: 224px;
        padding: 17px 10px;
        text-align: center;
        font-family: 'SF UI Display Medium';
        font-size: 14px;
        line-height: 1;
        cursor: pointer;
        transition: all 180ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
    }

    .btn--primary {
        color: #ffffff;
        background: #47ab43;
        border: 1px solid #47ab43;
        border-radius: 25px;
        transition: all 180ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
        &:hover {
            color: #ffffff;
            box-shadow: 0 0 0 4px #a3d5a1;
        }
    }

    .checkbox {
        display: none;

        + label {
            position: relative;
            display: block;
            padding-left: 24px;
            font-family: 'SF UI Display Medium';
            font-size: 14px;
            line-height: 1.5;
            color: #838287;
            cursor: pointer;

            a {
                color: #2f80ed;
            }

            &:before {
                content: '';
                display: block;
                position: absolute;
                top: 1px;
                left: 0;
                width: 16px;
                height: 16px;
                border: 1px solid #d6e0e7;
                border-radius: 3px;
                transition: all 295ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
            }

            &:after {
                content: '';
                display: block;
                position: absolute;
                top: 4px;
                left: 6px;
                width: 4px;
                height: 8px;
                border-bottom: 2px solid #ffffff;
                border-right: 2px solid #ffffff;
                -webkit-transform: rotate(45deg);
                transform: rotate(45deg);
                opacity: 0;
                transition: opacity 295ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
            }

            &:hover:before {
                border-color: #47ab43;
            }
        }

        &:checked + label {
            &:before {
                border-color: #47ab43;
                background: #47ab43;
            }

            &:after {
                opacity: 1;
            }
        }
    }
    .logo {
        display: inline-block;
        transform: scale(0.8);
        transform-origin: 0 50%;
    }

    .logo--lg {
        width: 200px;
    }

    .logo--md {
        width: 173px;
        overflow: hidden;

        img {
            max-width: inherit;
            width: 173px;
        }

        &.logo--shrink {
            width: 37px;
        }
    }

    .logo-banner {
        display: block;
    }
    .static-logo {
        display: none;
    }

    .logo--md {
        width: 173px;
        overflow: hidden;

        img {
            max-width: inherit;
            width: 173px;
        }

        &.logo--shrink {
            width: 37px;
        }
    }

    .text-box {
        position: relative;
        display: block;
        width: 100%;
        height: 30px;
        overflow: hidden;

        span {
            position: absolute;
            top: 40px;
            width: 100%;
            left: 0;
            white-space: nowrap;
        }
    }

    .text-box--sm {
        height: 27px;

        span {
            top: 35px;
        }
    }

    .text-box--semi {
        height: 32px;

        span {
            top: 60px;
        }
    }

    .text-box--md {
        height: 36px;

        span {
            top: 80px;
        }
    }

    .text-box--hg {
        height: 38px;

        span {
            top: 100px;
        }
    }
}

#errorForRus {
    display: none;
    a {
        color: #0000ee;
    }
}

@media (min-width: 1440px) {
    .invite-reg-wrapper {
        .header--simplify {
            left: 100px;
        }
    }
}

@media (min-width: 1200px) and (max-width: 1439px) {
    .invite-reg-wrapper {
        .container-title {
            font-size: 30px;
            line-height: 45px;
            margin-bottom: 5%;
        }
        .container-position {
            width: 50%;
            margin-left: 50%;
        }
        .banner {
            width: calc(((100vw - 1170px) / 2) + (1170px * (5 / 12)) - 30px);
        }
        .header--simplify {
            left: 55px;
        }
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .invite-reg-wrapper {
        .container-position {
            width: 50%;
            margin-top: 10%;
            margin-left: 50%;
            margin-bottom: 12px;
        }
        .banner {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 10;
            width: calc(((100vw - 970px) / 2) + (970px * (5 / 12)) - 30px);
            background: #312e37;
            border-radius: 0 0 400px 0;

            &:before {
                display: none;
            }
        }

        .banner--alt {
            left: auto;
            right: 0;
            border-radius: 0 0 0 400px;
        }

        .banner--cta .banner__content {
            padding-top: 0;
            padding-bottom: 0;
        }

        .banner--basic {
            display: flex;
        }

        .decoration-wrapper {
            display: block;
        }

        .decoration--first {
            top: -10%;
            right: -15%;
        }

        .header--simplify {
            position: fixed;
            top: 41px;
            left: 30px;
            z-index: 11;
            padding: 0;
        }

        .static-logo {
            display: none;
        }
        .logo-banner {
            display: block;
        }
    }
    .inviteHasExpired {
        font-size: 1.5rem !important;
        width: 37% !important;
    }
}

@media (min-width: 839px) and (max-width: 991px) {
    .inviteHasExpired {
        width: 35% !important;
        font-size: 1.4rem !important;
    }
}

@media (min-width: 776px) and (max-width: 991px) {
    .invite-reg-wrapper {
        .banner {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 10;
            width: 50% !important;
            background: #312e37;
            border-radius: 0 0 400px 0;

            &:before {
                display: none;
            }
        }
        .container-position {
            width: 65%;
            margin-left: 45%;
        }
        .banner--cta {
            display: flex;
        }
    }
    .inviteHasExpired {
        width: 35% !important;
        font-size: 1.4rem !important;
    }
}
@media (min-width: 540px) and (max-width: 775px) {
    .invite-reg-wrapper {
        min-width: 320px;
        .header--simplify {
            margin: 40px 0px 0px 40px;
        }
        .banner {
            width: 72% !important;
        }
        .banner--basic {
        }
        .logo-banner {
        }
        .static-logo {
            display: none;
            width: 200px;
            img {
                width: 100%;
            }
        }
        .container-title {
            font-size: 25px;
            line-height: 35px;
            margin-bottom: 5%;
        }
        .container-position {
            width: 90%;
            margin: 10% auto;
        }
    }
    .inviteHasExpired {
        width: 50% !important;
        font-size: 1.5rem !important;
    }
}

@media (min-width: 320px) and (max-width: 539px) {
    .invite-reg-wrapper {
        min-width: 320px;
        .header--simplify {
            display: none;
            margin: 40px 0px 0px 40px;
        }
        .banner {
            display: none;
            width: 72% !important;
        }
        .banner--basic {
        }
        .logo-banner {
        }
        .static-logo {
            display: none;
            width: 200px;
            img {
                width: 100%;
            }
        }
        .container-title {
            font-size: 25px;
            line-height: 35px;
            margin-bottom: 5%;
        }
        .container-position {
            width: 90%;
            margin: 10% auto;
            .form__group.display-flex {
                flex-direction: column;
                .form-role,
                .form-email {
                    width: 100%;
                }
            }
        }
    }
    .inviteHasExpired {
        width: 50% !important;
        font-size: 1.5rem !important;
    }
}
