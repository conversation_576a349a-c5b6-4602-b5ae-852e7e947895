body {
    padding-bottom: 0;
}

.block-company-public-vacancies {
    position: relative;

    overflow-x: hidden;
    min-height: 100vh;
    padding-top: 50px;

    background-color: #e3f2dd;
    & .vacancy-wrapper {
        display: block;
    }
    & .relative {
        position: relative;
        & .social-icons {
            position: fixed;
            z-index: 999;
            bottom: 20%;
            left: 0;
            & a {
                display: block;
                width: 33px;
                height: 33px;
                padding-top: 8px;

                transition: all 0.3s;
                & i {
                    font-size: 19px;

                    display: block;

                    color: #fff;
                }
                &:hover {
                    width: 42px;

                    text-decoration: none;

                    color: #fff;
                }
                &.gmail {
                    background-color: #db4538;
                }
                &.linked {
                    background-color: #0178b5;
                }
                &.twitter {
                    background-color: #139df1;
                }
                &.fb {
                    background-color: #3c5a98;
                }
            }
        }
        & .api-link {
            position: absolute;
            top: 0;
            right: 10px;
            a {
                text-decoration: underline;
            }
        }
    }
    & .company-info {
        display: flex;
        flex-direction: row;
        min-width: 245px;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px 40px;

        border-radius: 5px;
        background-color: #f1f9ee;
        & .logo {
            display: flex;
            flex-flow: column;
            max-width: 235px;
            height: 100%;
            margin-right: 4%;
            & img {
                width: 100%;
                height: auto;
            }
        }
        & .info {
            display: flex;
            flex-direction: column;
            justify-content: center;
            max-width: 50%;
            width: 100%;
            &.full-width {
                width: 100%;
                max-width: 100% !important;
            }
            & h2 {
                font-size: 24px;

                margin: 0;
                display: flex;
                word-break: break-word;
                justify-content: center;
                &.margin-bot {
                    margin-bottom: 14px;
                }
            }
            & .info--site {
                display: flex;
                flex-direction: row;
                margin-bottom: 8px;

                align-items: center;
                & img {
                    display: inline-block;
                    width: 21px;
                    margin-right: 10px;
                    &.fb {
                        width: 21px;
                    }
                }
                & a {
                    display: inline-block;
                    &.site {
                        max-width: 250px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        text-align: left;
                    }
                }
                &.text-center {
                    text-align: center;
                }
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
    & .vacancies-intro {
        display: block;
        margin: 50px auto 25px;

        text-align: center;
        & h2 {
            display: inline-block;
            margin: 0;
            padding: 13px 20px;
            text-align: center;
            border-radius: 5px;
            background-color: #f1f9ee;
            width: 62%;
        }
    }
    & .vacancies-filter {
        display: flex;
        align-items: center;
        max-width: 62%;
        margin: 0 auto 26px;
        justify-content: space-between;
        .filter-settings {
            display: inline-block;
        }
        .switch {
            display: inline-block;
            width: auto;
            text-align: center;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 5px 10px;
            label {
                margin: 0;
                padding: 0;

                vertical-align: middle;
                &:hover {
                    cursor: pointer;
                }
            }
            .cmn-toggle + label {
                display: inline-block;
                margin: 0 7px;
            }
        }
        .positions-wrap {
            position: relative;
            z-index: 999;

            display: inline-block;
            width: auto;
            min-width: 160px;

            outline: none;
            ul {
                position: absolute;
                z-index: 9999;
                width: 100%;
                overflow-x: hidden;
                overflow-y: auto;
                max-height: 200px;
                li {
                    overflow: hidden;
                    padding: 5px 10px;

                    text-align: left;
                    text-overflow: ellipsis;

                    border-bottom: 1px solid #ddd;
                    border-left: 1px solid #ddd;
                    cursor: pointer;
                    color: #666666;
                    &:hover {
                        color: #000000;
                    }
                    &.selected {
                        background-color: #caced1;
                    }
                }
            }
            input {
                overflow: hidden;
                width: 160px;
                text-overflow: ellipsis;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5.5px 20px 5.5px 10px;
                &:focus {
                    outline: none;
                }
                &::placeholder {
                    color: #000;
                }
                &.error {
                    color: #fc1c1a;
                    border: 1px solid #fc1c1a;
                    &::placeholder {
                        color: #fc1c1a;
                    }
                }
            }
            i.fa-times {
                font-size: 12px;

                position: absolute;
                top: 10px;
                right: 9px;

                transition: 0.2s all ease;

                color: #a6b3ba;
                &:hover {
                    cursor: pointer;

                    color: #00b549;
                }
            }
            &.no-scroll-margin {
                .mCSB_inside > .mCSB_container {
                    margin-right: 0;
                }
            }
        }
        .locations-wrap {
            position: relative;
            z-index: 999;

            display: inline-block;

            vertical-align: middle;
            .vacancies-location {
                position: relative;

                width: 100%;
                height: 32px;
                margin: 0 auto;

                outline: none;
                span {
                    color: #000;
                    line-height: 32px;

                    display: block;
                    overflow: hidden;
                    width: 160px;
                    height: 32px;
                    padding: 0 10px;

                    vertical-align: bottom;
                    text-overflow: ellipsis;

                    border: 1px solid #ccc;
                    border-radius: 5px;
                    background-color: #fff;
                    &:hover {
                        cursor: pointer;
                    }
                }
                ul {
                    position: absolute;
                    z-index: 999;
                    top: 100%;

                    width: 99%;
                    padding-left: 1px;
                    height: auto;
                    max-height: 180px;
                    border: 1px solid #ccc;
                    border-top: none;
                    border-radius: 5px;
                    .mCustomScrollBox {
                        border-radius: 5px;
                        .mCSB_container.mCS_y_hidden.mCS_no_scrollbar_y {
                            margin-right: 0;
                        }
                    }
                    li {
                        color: #666666;
                        cursor: pointer;
                        padding: 3px 16px;
                        border-bottom: 1px solid #ddd;
                        &.accent {
                            color: #7a7a7a;
                        }
                        &:hover {
                            color: #000;
                        }
                        &.selected {
                            background-color: #caced1;
                        }
                        &:last-child {
                            border-bottom: none;
                        }
                    }
                }
                &:hover {
                    & .fa-caret-down {
                        color: #00b549;
                    }
                }
                i.fa-times {
                    font-size: 12px;

                    position: absolute;
                    top: 11px;
                    right: 37px;

                    transition: 0.2s all ease;

                    color: #a6b3ba;
                    &:hover {
                        cursor: pointer;

                        color: #00b549;
                    }
                }
            }
            .down-arrow-wrapper {
                position: absolute;
                right: 1px;
                display: flex;
                align-items: center;
                justify-content: center;
                top: 1px;
                width: 30px;
                height: 30px;
                border-left: 1px solid #ccc;
                background-color: #f1f1f1;
                border-radius: 0 5px 5px 0;
                cursor: pointer;
                i.fa-chevron-down {
                    font-size: 14px;
                    transition: 0.2s all ease;

                    color: #a6b3ba;
                }
            }
        }
        .filter-button {
            display: inline-block;

            vertical-align: middle;
            .btn-primary-new {
                @include btn-primary-new();

                height: 32px;
                min-width: 160px;
                padding: 0 20px;
            }
        }
        .mCSB_inside > .mCSB_container {
            margin-right: 7px;
            padding: 0;
        }
        .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
            background-color: #666666 !important;
            width: 3px;
            margin: 0 3px 0 auto;
        }
        .mCSB_scrollTools .mCSB_draggerRail {
            margin: 0 3px 0 auto;
        }
        .mCSB_scrollTools .mCSB_draggerContainer {
            height: 90%;
            top: 6px;
        }
    }
    & .vacancies {
        position: relative;
        z-index: 990;

        max-width: 62%;
        margin: 0 auto 80px;
        padding: 30px 16px;

        border-radius: 5px;
        background-color: #f1f9ee;
        & .vacancy {
            display: block;
            margin: 0 auto 20px;
            padding: 20px;

            transition-timing-function: ease-out;
            transition-duration: 250ms;

            background: #ffffff;
            box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.12);
            border-radius: 5px;
            &:hover {
                transition-timing-function: ease-out;
                transition-duration: 250ms;
                transform: scale(1.05, 1.05);
                a span {
                    color: #000;
                }
            }
            & a {
                color: #000;
                &:hover {
                    color: #000;
                }
                & span {
                    display: block;

                    text-transform: uppercase;
                    &.vacancy__name {
                        font-style: normal;
                        font-weight: 600;
                        font-size: 18px;
                        line-height: 27px;
                        letter-spacing: 0.004em;
                        color: #333333;
                        margin-bottom: 21px;
                    }
                    &.vacancy__location {
                        font-style: normal;
                        font-weight: normal;
                        font-size: 14px;
                        line-height: 21px;
                        letter-spacing: 0.004em;
                        @include link-styling();
                    }
                    &.vacancy__salary {
                        display: inline-block;
                        & span {
                            display: inline-block;
                        }
                    }
                    &.vacancy__employment {
                        display: inline-block;
                    }
                }
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
        &.filter-error {
            font-size: 17px;

            text-align: center;
            text-transform: uppercase;

            color: #a94442;
            border: none;
        }
    }
    & .cleverstaff {
        margin-bottom: 20px;

        text-align: center;
        a {
            color: #202021;
            text-decoration: none;
            &:hover {
                color: #202021;
            }
        }
        span {
            font-size: 15px;
            line-height: 20px;

            display: inline-block;
            margin: 0;
            padding: 0;
        }
        img {
            display: inline-block;
            width: 20px;
            height: 20px;
        }
    }
    .background-animations {
        & img.position {
            position: fixed;

            opacity: 0.45;
            &.cloud-1 {
                top: 10%;
                left: 11%;
            }
            &.cloud-2 {
                position: absolute;
                top: 110px;
                left: 22%;
            }
            &.cloud-3 {
                top: 9%;
                right: 15%;
            }
            &.cloud-4 {
                position: absolute;
                top: 100px;
                right: 23%;
            }
            &.ball {
                top: 40%;
                left: 12%;
            }
            &.calendar {
                top: 34%;
                right: 7%;
            }
            &.calendar-2 {
                top: 52%;
                left: 5%;
            }
            &.spiral-line {
                top: 44%;
                right: 12%;
            }
            &.person-3 {
                top: 54.5%;
                right: 0;
            }
            &.plane {
                top: 60.5%;
                left: 3%;
            }
            &.person-1 {
                position: absolute;
                bottom: 0.8%;
                left: 10%;
            }
            &.person-2 {
                position: absolute;
                right: 9%;
                bottom: 1%;
            }
        }
        .cloud-1.ng-enter,
        .cloud-2.ng-enter,
        .calendar-2.ng-enter,
        .plane.ng-enter,
        .person-1.ng-enter,
        .ball.ng-enter {
            transition: 0.65s linear all;
            transform: translateX(-300%);

            opacity: 0;
        }
        .cloud-4.ng-enter,
        .cloud-3.ng-enter,
        .calendar.ng-enter,
        .spiral-line.ng-enter,
        .person-3.ng-enter,
        .person-2.ng-enter {
            transition: 0.65s linear all;
            transform: translateX(300%);

            opacity: 0;
        }
        .cloud-1.ng-enter.ng-enter-active,
        .ball.ng-enter.ng-enter-active,
        .calendar-2.ng-enter.ng-enter-active,
        .plane.ng-enter.ng-enter-active,
        .person-1.ng-enter.ng-enter-active,
        .cloud-2.ng-enter.ng-enter-active,
        .cloud-4.ng-enter.ng-enter-active,
        .cloud-3.ng-enter.ng-enter-active,
        .calendar.ng-enter.ng-enter-active,
        .spiral-line.ng-enter.ng-enter-active,
        .person-3.ng-enter.ng-enter-active,
        .person-2.ng-enter.ng-enter-active {
            transform: translateY(0);

            opacity: 1;
        }
    }
    .no-padding-mobile {
        padding: 0;
    }
}

span.custom-tooltip {
    position: absolute;
    bottom: 135%;
    left: 0;

    width: 180px;
    padding: 5px 10px;

    border-radius: 13px;
    &:after {
        position: absolute;
        bottom: 0;
        left: 50%;

        width: 0;
        height: 0;
        margin-bottom: -9px;
        margin-left: -9px;

        content: '';

        border: 9px solid transparent;
        border-bottom: 0;
    }
    &.error {
        color: #a94442;
        background-color: #f2dede;
        &:after {
            border-top-color: #f2dede;
        }
    }
}

@media (max-width: 1347px) {
    .block-company-public-vacancies .vacancies-filter .switch {
        width: auto;
        max-width: none;
        text-align: center;
    }
    .block-company-public-vacancies .vacancies-filter .positions-wrap {
        width: auto;
        margin-right: 0;

        text-align: center;
    }
    .block-company-public-vacancies .vacancies-filter .locations-wrap {
        width: auto;
        margin-right: 0;

        text-align: left;
        .vacancies-location {
            display: inline-block;
            width: auto;
            span {
                text-align: left;
            }
        }
    }
    .block-company-public-vacancies .vacancies-filter .filter-button {
        display: block;
        margin: 20px 0;

        text-align: center;
    }
}

@media screen and (max-width: $screen-lg) {
    .block-company-public-vacancies .vacancies-filter,
    .block-company-public-vacancies .vacancies-intro h2,
    .block-company-public-vacancies .vacancies {
        max-width: 90%;
        width: 90%;
    }
}

@media (max-width: 987px) {
    .block-company-public-vacancies .vacancies-filter,
    .block-company-public-vacancies .vacancies-intro h2,
    .block-company-public-vacancies .vacancies {
        max-width: 95%;
        width: 95%;
    }
}

@media (max-width: 768px) {
    .block-company-public-vacancies .vacancies-filter,
    .block-company-public-vacancies .vacancies-intro h2,
    .block-company-public-vacancies .vacancies {
        max-width: 100%;
        width: 100%;
    }

    .block-company-public-vacancies .vacancies-filter {
        flex-wrap: wrap;
        .filter-button {
            width: 100%;
            margin: 20px 0 0 0;
        }
        .locations-wrap {
            margin-left: 10px;
            min-width: calc(33% - 10px);
            .vacancies-location {
                width: 100%;
                span {
                    width: 100%;
                }
            }
        }
        .positions-wrap {
            margin-left: 10px;
            min-width: calc(33% - 10px);
            input {
                width: 100%;
            }
        }
    }

    .block-company-public-vacancies {
        .company-info {
            flex-wrap: wrap;
            .logo {
                margin: 0 auto 17px;
            }
            .info {
                max-width: 100% !important;
            }
        }
        .vacancies {
            .vacancy {
                a {
                    div {
                        align-self: flex-end;
                    }
                    span {
                        margin-bottom: 0;
                        padding-bottom: 0;
                        .vacancy__name {
                            font-style: normal;
                            font-weight: 500;
                            font-size: 18px;
                            line-height: 27px;
                            letter-spacing: 0.004em;
                            color: #333333;
                        }
                        .vacancy__location {
                            font-style: normal;
                            font-weight: normal;
                            font-size: 14px;
                            line-height: 21px;
                            letter-spacing: 0.004em;
                            color: #337ab7;
                        }
                    }
                    .emp-salary {
                        display: inline-block;
                        width: 100%;
                        padding: 6px 0 0 0;

                        text-align: center;
                        .vacancy__salary,
                        .vacancy__employment {
                            font-weight: normal;
                            font-size: 14px;
                            line-height: 21px;
                            text-align: right;
                            letter-spacing: 0.004em;
                            color: #333333;
                        }
                    }
                }
                .vacancy__name--wrap,
                .vacancy__salary--wrap,
                .vacancy__location--wrap {
                    text-align: center;
                }
            }
        }
    }
}

@media screen and (max-width: 660px) {
    .block-company-public-vacancies {
        padding-top: 0;
        .vacancies-filter {
            justify-content: space-around;
            .switch {
                width: 100%;
                border: none;
                margin-bottom: 20px;
                padding: 0;
                .xs-wrapper {
                    display: inline-flex;
                    align-items: center;
                    border: 1px solid #ccc;
                    padding: 5px 10px;
                    border-radius: 5px;
                }
            }
        }
    }
    .block-company-public-vacancies .company-info,
    .block-company-public-vacancies .vacancies-filter,
    .block-company-public-vacancies .vacancies-intro h2,
    .block-company-public-vacancies .vacancies {
        border-radius: 0;
        width: 100%;
        max-width: 100%;
    }
}

@media (max-width: 600px) {
    .block-company-public-vacancies .vacancies .vacancy a span.vacancy__name,
    .block-company-public-vacancies .vacancies .vacancy a span.vacancy__location {
    }
}

@media (max-width: 565px) {
    .block-company-public-vacancies {
        .vacancies {
            max-width: 85%;
            .vacancy {
                padding: 12px 0;
            }
        }
        .cs-logo {
            .social-icons {
                display: none;
            }
        }
    }
    .block-company-public-vacancies .background-animations img.position {
        position: absolute;
    }
}

@media (max-width: 490px) {
    .block-company-public-vacancies .company-info .logo {
        max-width: 60%;

        text-align: center;
    }
    .block-company-public-vacancies .company-info .info {
        max-width: 100%;
        margin-top: 10px;
    }
    .block-company-public-vacancies .company-info {
        min-width: 0;
    }
    .block-company-public-vacancies .vacancies .vacancy a span.vacancy__name,
    .block-company-public-vacancies .vacancies .vacancy a span.vacancy__location {
        width: 100%;
    }
}

//background-animation
@media (max-width: 1080px) {
    .block-company-public-vacancies .background-animations img.position.cloud-4,
    .block-company-public-vacancies .background-animations img.position.cloud-2 {
        display: none;
    }
    .block-company-public-vacancies .background-animations img.position.cloud-1 {
        left: 5%;
    }
    .block-company-public-vacancies .background-animations img.position.cloud-3 {
        top: 80px;
        right: 5%;
    }
}
@media (max-width: 987px) {
    .block-company-public-vacancies .background-animations img.position.plane {
        display: none;
    }
}

@media (max-width: 767px) {
    .block-company-public-vacancies .background-animations img.position.person-1 {
        left: 5%;
    }
}

@media (max-width: 660px) {
    .block-company-public-vacancies .background-animations img.position.person-1 {
        display: none;
    }
    .block-company-public-vacancies .background-animations img.position.cloud-1,
    .block-company-public-vacancies .background-animations img.position.cloud-3 {
        display: none;
    }
    .block-company-public-vacancies .background-animations img.position.spiral-line {
        display: none;
    }
    .block-company-public-vacancies .background-animations img.position.calendar {
        top: 260px;
    }
    .block-company-public-vacancies .background-animations img.position.person-2 {
        right: 1%;
    }
}
@media (max-width: 490px) {
    .block-company-public-vacancies .background-animations img.position.calendar {
        display: none;
    }
    .block-company-public-vacancies .background-animations img.position.ball {
        display: none;
    }
    .block-company-public-vacancies .background-animations img.position.person-3 {
        top: 475px;
    }
    .block-company-public-vacancies .background-animations img.position.calendar-2 {
        top: 450px;
    }
}

@media (min-width: 362px) and (max-width: 400px) {
    .block-company-public-vacancies .background-animations img.position.person-3 {
        top: 517px;
    }
    .block-company-public-vacancies .background-animations img.position.calendar-2 {
        top: 410px;
    }
}
// end of BI;

// vacancies animate

.vacancy.ng-enter,
.vacancy.ng-leave {
    position: relative;

    display: block;

    transition: 450ms cubic-bezier(0.25, 0.25, 0.75, 0.75) all;
}

.vacancy.ng-enter.ng-enter-active,
.vacancy.ng-leave {
    height: 30px;

    transform: rotateX(0deg) translatez(30px);

    opacity: 1;
}

.vacancy.ng-leave.ng-leave-active,
.vacancy.ng-enter {
    height: 0;

    transform: rotateX(-180deg) translatez(30px);

    opacity: 0;
}

// filter-settings animate
.positions-wrap,
.locations-wrap,
.filter-button {
    transition: 0.35s linear all;
    transform: translateX(0);

    opacity: 1;
}

.positions-wrap.ng-hide-remove,
.positions-wrap.ng-hide-add,
.positions-wrap.ng-hide,
.locations-wrap.ng-hide-remove,
.locations-wrap.ng-hide-add,
.locations-wrap.ng-hide,
.filter-button.ng-hide-remove,
.filter-button.ng-hide-add,
.filter-button.ng-hide {
    display: block;
}

.positions-wrap.ng-hide,
.filter-button.ng-hide,
.locations-wrap.ng-hide {
    transform: translateX(-50%);

    opacity: 0;
}
