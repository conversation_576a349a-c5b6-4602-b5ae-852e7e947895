candidate-evaluate-component {
    .candidate-evaluate-block {
        border-top: inherit !important;
        border-top-left-radius: 0 !important;
        border-top-right-radius: 0 !important;

        .score-card {
            display: flex;
            flex-direction: column;
            width: 100%;
            background-color: $background-grey;
            border: 1px solid $border-grey;
            border-radius: 8px;

            .averageResult {
                display: flex;
                gap: 8px;
                align-items: center;
                margin-left: 16px;

                img {
                    width: 18px;
                    height: 18px;
                }

                .full,
                .half {
                    filter: $filter-main-green;
                }

                .empty {
                    filter: $filter-dark-grey;
                }

                .score {
                    font-weight: 500;
                    color: $main-green;
                }

                .counter {
                    margin-left: 8px;
                    font-weight: 400;
                    color: $semi-black;

                    .no-voters {
                        min-width: 130px;
                    }
                }
            }

            .score-card-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 12px 18px;

                .form-title-main {
                    font-size: $main-font-size;
                    line-height: 20px;
                    word-break: break-word;

                    .client {
                        font-weight: 500;
                        color: $semi-black;
                    }

                    .position {
                        min-width: auto;
                        @include green-link();
                    }
                }

                .score-card-name {
                    display: flex;
                    align-items: center;
                    font-size: 16px;

                    .min-width {
                        min-width: 120px;
                    }

                    .evaluate {
                        margin-left: 8px;
                        font-weight: normal;
                        color: #999999;
                    }

                    .averageResult {
                        margin-left: 8px;
                    }
                }

                .export-scorecard {
                    img {
                        width: 28px;
                        height: 28px;
                        object-fit: contain;

                        &:hover {
                            transform: scale(1.1);
                        }
                    }
                }

                .save-to-excel-tooltip .tooltip-inner {
                    color: #333333;
                    background-color: white;
                    border-radius: 5px;
                    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2), 0px 0px 0px rgba(0, 0, 0, 0.12);
                }
            }

            .score-card-item-schemas {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin: 16px;
                border-radius: 5px;

                .schemas {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: space-between;
                    background-color: $main-white;
                    border: 1px solid $border-grey;
                    border-radius: 8px;

                    &__wrapper {
                        display: flex;
                        justify-content: space-between;
                        width: 100%;
                        padding: 12px;
                        border-bottom: 1px solid $border-grey;

                        &.bottomBorder {
                            border-bottom: none;
                        }
                    }

                    &__name {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        width: 100%;
                        font-weight: 500;

                        .name {
                            max-width: 65%;
                            color: $secondary-black;
                        }
                    }

                    &__buttons {
                        display: flex;
                        gap: 16px;
                        align-items: center;
                        justify-content: flex-end;

                        &__title {
                            width: max-content;
                            min-width: 120px;
                            color: $semi-black;
                        }

                        &__change {
                            position: relative;
                            display: flex;
                            flex-direction: row;
                            gap: 16px;
                            align-items: center;
                        }

                        img {
                            width: 18px;
                            height: 18px;
                            filter: $filter-semi-black;

                            &.rotate {
                                transform: rotate(180deg);
                            }
                        }

                        .tooltip-white-hint {
                            min-width: 180px;
                            max-width: 175px;
                            word-break: break-word;
                        }
                    }

                    .comment-block {
                        display: flex;
                        flex-direction: column;
                        gap: 8px;
                        width: 95%;
                        margin: 0 0 16px 12px;

                        &__item {
                            width: fit-content;
                            padding: 12px;
                            background: $main-white;
                            border: 1px solid $border-grey;
                            border-radius: 8px;

                            .user-info {
                                display: flex;
                                align-items: baseline;
                                justify-content: space-between;
                                padding-bottom: 5px;
                                font-size: $secondary-font-size;

                                .name {
                                    margin-right: 5px;
                                    overflow: hidden;
                                    text-overflow: ellipsis;

                                    a {
                                        @include link-styling();
                                        color: $main-black;
                                    }
                                }

                                .date {
                                    color: $semi-black;
                                }
                            }

                            .comment-info {
                                font-size: $secondary-font-size;

                                span {
                                    word-break: break-word;
                                    white-space: break-spaces;
                                }
                            }

                            &:last-child {
                                margin-bottom: 0;
                            }
                        }
                    }

                    .comment-block-main {
                        display: flex;
                        flex-direction: column;
                        gap: 8px;
                        justify-content: flex-start;
                        width: 65%;
                        margin: 12px;
                    }

                    &__info {
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        width: 100%;
                        overflow: hidden;

                        .first {
                            width: max-content;
                            max-width: 60%;

                            .rowScard {
                                align-items: flex-start;

                                .score-schemas {
                                    width: 1700%;
                                    padding: 12px;
                                    text-align: inherit;
                                    background-color: $pale-grey;
                                }

                                .score-name {
                                    padding: 12px;
                                    color: $semi-black;
                                }

                                .score-comment {
                                    display: flex;
                                    flex-direction: row;
                                    gap: 4px;
                                    margin-left: 8px;

                                    span {
                                        font-size: 14px;
                                        font-weight: 400;
                                        color: $semi-black;
                                    }

                                    img {
                                        filter: $filter-semi-black;
                                    }

                                    .selected {
                                        filter: $filter-main-green;
                                    }
                                }

                                .score-questions {
                                    width: 100%;
                                    padding: 0;
                                    font-weight: 400;

                                    .main-block {
                                        display: flex;
                                        flex-direction: row;
                                        width: auto;
                                        padding: 12px 0 12px 12px;

                                        .question {
                                            overflow: hidden;
                                            color: $main-black;
                                            text-overflow: ellipsis;
                                        }
                                    }
                                }

                                .score-results {
                                    display: flex;
                                    align-items: center;
                                    width: 1700%;
                                    padding: 12px;
                                    text-align: inherit;
                                    background-color: $light-green;

                                    .score-comment {
                                        span {
                                            color: $main-green;
                                        }

                                        img {
                                            filter: $filter-main-green;
                                        }
                                    }
                                }
                            }
                        }

                        .second {
                            display: inline-block;
                            width: min-content;
                            max-width: 45%;
                            overflow: auto;
                            text-align: -webkit-right;

                            &::-webkit-scrollbar {
                                height: 7px;
                            }

                            &::-webkit-scrollbar-thumb {
                                background-color: #d9d9d9;
                                border-radius: 5px;
                            }

                            &__wrapper {
                                display: flex;
                                justify-content: space-evenly;
                                width: max-content;
                            }

                            .score-questions {
                                width: 100%;
                                color: $main-black;
                                text-align: center;
                            }

                            .rowScard {
                                width: 100%;
                                min-width: 65px;
                                text-align: center;
                            }

                            .getMe {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 30px;
                                height: 30px;
                                margin-top: 2px;
                                font-size: 13px;
                                font-weight: 500;
                                background-color: #f4bf4f;
                                border-radius: 50%;

                                a {
                                    color: #312e37;
                                }
                            }

                            .custom {
                                .responsible-wrapper {
                                    width: 30px;
                                    height: 30px;
                                    font-size: 13px;
                                }
                            }
                        }

                        .score-name {
                            width: max-content;
                            padding: 12px;

                            &.average {
                                color: $semi-black;
                            }
                        }

                        .custom-response {
                            padding: 4px;

                            responsible-person {
                                .responsible-wrapper {
                                    width: 36px !important;
                                    height: 36px !important;
                                }

                                img {
                                    width: 100%;
                                }
                            }
                        }

                        .score-schemas {
                            width: 100%;
                            padding: 12px;
                            font-weight: 500;
                            text-align: center;

                            span {
                                display: flow-root;
                                max-width: 385px;
                                overflow: hidden;
                                color: $secondary-black;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }
                        }

                        .score-results {
                            width: 100%;
                            padding: 12px;
                            font-weight: 500;
                            color: $main-green;
                            text-align: center;
                        }

                        .score-questions {
                            padding: 12px 0 12px 0;
                            overflow: hidden;
                            font-size: 14px;
                            color: #202021;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }

                        .rowScard {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                        }
                    }
                }
            }

            .score-card-hr {
                margin: auto 18px;
                border-bottom: 1px solid $border-grey;
            }
        }
    }
}

.evaluate-tip .tooltip-inner {
    max-width: 200px !important;
}

@media (max-width: 875px) {
    candidate-evaluate-component .candidate-evaluate-block .score-card .score-card-item-schemas .schemas {
        &__wrapper {
            flex-direction: column;
            align-items: center;
        }

        &__name {
            align-items: flex-start;
            justify-content: space-between;
            width: 100%;
            margin-bottom: 10px;

            .name {
                max-width: 70%;
            }
        }

        &__buttons {
            .alreadyEvaluatedByMe {
                display: none;
            }

            button {
                margin-right: 15px;
            }
        }

        &__info {
            .first {
                .score-schemas {
                    span {
                        max-width: 300px;
                    }
                }
            }
        }
    }
}

@media (max-width: 576px) {
    candidate-evaluate-component .candidate-evaluate-block {
        padding: 20px 10px;

        .score-card .score-card-item {
            padding: 15px 15px;
        }

        .form-title-main {
            width: 100%;

            .position {
                min-width: fit-content !important;
                max-width: 60%;
                font-size: 16px;
                word-break: break-word;
            }

            .client {
                width: inherit !important;
                font-size: 14px;
            }

            .averageResult {
                width: 80px;
                font-size: 14px;

                .score {
                    font-size: 14px;
                }

                .full {
                    height: 16px;
                    margin-top: 3px;
                }

                .empty {
                    height: 16px;
                    margin-bottom: 2px;
                }

                .half {
                    height: 20px;
                    margin-top: 2px;
                }
            }
        }

        .score-card .score-card-item-schemas {
            padding: 0 20px 25px 20px;

            .schemas {
                margin-top: 25px;

                &__name {
                    font-size: 14px;

                    .averageResult.cardsScore {
                        span {
                            font-size: 14px;
                        }

                        .full {
                            height: 16px;
                        }

                        .empty {
                            height: 16px;
                        }
                    }
                }

                .schemas__info {
                    &::-webkit-scrollbar {
                        height: 7px;
                    }

                    &::-webkit-scrollbar-thumb {
                        background-color: #d9d9d9;
                        border-radius: 5px;
                    }
                    margin-top: 20px;
                    overflow: scroll;

                    .first {
                        max-width: 70%;

                        .score-name {
                            width: fit-content;
                        }

                        .score-schemas {
                            width: 195%;
                            background-color: #f2f2f2;
                        }

                        .score-results {
                            width: 195%;
                            background-color: #eaf7ed;
                        }

                        .score-comment {
                            img {
                                height: 15px;
                            }

                            span {
                                font-size: 14px;
                            }
                        }
                    }

                    .second {
                        overflow: unset;

                        .score-schemas {
                            background-color: #f2f2f2;
                        }

                        .score-results {
                            background-color: #eaf7ed;
                        }

                        .custom {
                            .responsible-wrapper {
                                width: 25px;
                                height: 25px;
                                font-size: 11px;
                            }
                        }
                    }

                    .score-name {
                        margin-bottom: 10px;
                        font-size: 14px;
                    }

                    .score-schemas {
                        font-size: 15px;
                    }

                    .score-questions {
                        font-size: 14px;

                        .main-block {
                            font-size: 14px;
                        }
                    }

                    .score-results {
                        font-size: 14px;
                    }
                }

                .comment-block__item {
                    .user-info {
                        font-size: 12px;

                        .name {
                            margin-top: 2px;
                        }
                    }

                    .comment-info {
                        font-size: 13px;
                    }
                }
            }
        }
    }
}

@media (max-width: 470px) {
    candidate-evaluate-component .candidate-evaluate-block .form-title-main {
        .position.long {
            min-width: 74% !important;
        }
    }
}

@media (max-width: 470px) {
    candidate-evaluate-component
        .candidate-evaluate-block
        .score-card
        .score-card-item-schemas
        .schemas
        .schemas__info
        .first
        .score-schemas {
        width: 150%;
    }

    candidate-evaluate-component
        .candidate-evaluate-block
        .score-card
        .score-card-item-schemas
        .schemas
        .schemas__info
        .first
        .score-results {
        width: 150%;
    }
}
