@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

integration-lost-notification-component {
    .integration-lost-notification-component-wrapper {
        width: 360px;
        position: fixed;
        top: 25px;
        right: 25px;
        z-index: 9999;
        transition: all 0.3s ease-in;

        .notification-item {
            display: flex;
            gap: 8px;
            background-color: $main-white;
            margin-bottom: 10px;
            padding: 16px;
            position: relative;
            border: 1px solid $red;
            border-radius: 12px;

            &__remove {
                position: absolute;
                top: 16px;
                right: 16px;
                display: inline-block;
                margin: 0;
                cursor: pointer;
                opacity: 0;
                transition: all 0.3s ease-in;
                @include icon-mask('/images/redesign/svg-icons/close.svg', $semi-black);
                @include control-icon-size;

                &:hover,
                &:focus {
                    background-color: $secondary-black;
                }
            }

            &__alert {
                display: inline-block;
                margin: 0;
                width: 20px !important;
                height: 20px !important;
                cursor: pointer;
                transition: all 0.3s ease-in;
                @include icon-mask('/images/redesign/svg-icons/error-notify.svg', $red);
            }

            &__content {
                font-size: $main-font-size;
                color: $secondary-black;
                margin-bottom: 10px;
                max-width: 260px;
            }

            &__buttons {
                display: flex;
                justify-content: flex-start;
            }
        }
        &:hover {
            .notification-item__remove {
                opacity: 1;
            }
        }
    }
}
