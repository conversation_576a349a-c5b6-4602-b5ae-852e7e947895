import type { StorybookConfig } from '@storybook/react-webpack5';

const config: StorybookConfig = {
    stories: ['../js/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
    staticDirs: [{ from: '../images/redesign', to: 'images/redesign' }],
    addons: [
        '@storybook/addon-webpack5-compiler-swc',
        '@storybook/addon-onboarding',
        '@storybook/addon-links',
        '@storybook/addon-essentials',
        '@chromatic-com/storybook',
        '@storybook/addon-interactions',
        {
            name: '@storybook/addon-styling-webpack',
            options: {
                rules: [
                    // Replaces existing CSS rules with given rule
                    {
                        test: /\.css$/,
                        use: ['style-loader', 'css-loader'],
                    },
                    // Replaces any existing Sass rules with given rules
                    {
                        test: /\.s[ac]ss$/i,
                        use: [
                            'style-loader',
                            'css-loader',
                            {
                                loader: 'sass-loader',
                                options: { implementation: require.resolve('sass') },
                            },
                        ],
                    },
                ],
            },
        },
    ],
    framework: {
        name: '@storybook/react-webpack5',
        options: {},
    },
    docs: {
        autodocs: 'tag',
    },
    webpackFinal: async (config) => {
        config.resolve.modules.push('../js', '../', '../images');
        config.resolve.extensions.push('.ts', '.tsx');

        // @ts-ignore
        const imageRule = config.module.rules.find((rule) => rule?.['test']?.test('.svg'));
        if (imageRule) {
            // @ts-ignore
            imageRule['exclude'] = /\.svg$/;
        }

        config.module.rules.push({
            test: /\.svg$/,
            use: ['@svgr/webpack'],
        });
        return config;
    },
};
export default config;
